import * as Cesium from 'cesium';

import image from './assets/chemical.jpg';
import { getCesiumMaterialCache, setCesiumMaterialCache } from './material-cache';

/**
 * 危化品
 */
export class ChemicalMaterialProperty implements Cesium.MaterialProperty {
  constructor() {}

  static readonly MaterialType = 'ChemicalMaterial';

  getType(_time?: Cesium.JulianDate) {
    return ChemicalMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: any) {
    result ??= {};

    return result;
  }

  equals(other?: ChemicalMaterialProperty) {
    return other instanceof ChemicalMaterialProperty;
  }
}

const WaterMaterial = getCesiumMaterialCache('Water');

setCesiumMaterialCache(ChemicalMaterialProperty.MaterialType, {
  ...WaterMaterial,
  fabric: {
    ...WaterMaterial.fabric,
    type: 'Chemical',
    uniforms: {
      ...WaterMaterial.fabric.uniforms,
      normalMap: image,
      baseWaterColor: Cesium.Color.fromCssColorString(`rgba(127,145,83,0.4)`),
      blendColor: Cesium.Color.fromCssColorString(`rgba(65,19,29,0.4)`),
      frequency: 100,
      animationSpeed: 0.01,
      amplitude: 1,
    },
  },
  translucent: () => true,
});
