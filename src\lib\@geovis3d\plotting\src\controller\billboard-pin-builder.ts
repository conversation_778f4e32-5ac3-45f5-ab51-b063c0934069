import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';
import { positionUpdate } from './utils/position-update';

/**
 * billboard-pin-builder标绘配置
 */
export default <PlottingControllerOptions>{
  type: 'billboard-pin-builder',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 1,
  location: { visible: true },
  altitude: { visible: true },
  scale: { visible: true },
  scaleCallback(entity, scale) {
    const prev = entity.billboard?.scale?.getValue(Cesium.JulianDate.now()) ?? 1;
    entity.billboard!.scale = new Cesium.ConstantProperty(prev * scale);
  },
  update(entity) {
    if (!entity.billboard) {
      entity.billboard = new Cesium.BillboardGraphics({});
    }
    positionUpdate(entity);
  },
};
