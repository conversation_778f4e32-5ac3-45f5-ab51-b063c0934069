import type * as Cesium from 'cesium';

/** 全球坐标 [Longitude, Latitude, Height?] */
export type WGS84 = number[];

/** 火星坐标 [Longitude, Latitude, Height?] */
export type GCJ02 = number[];

/** 百度坐标 [Longitude, Latitude, Height?] */
export type BD09 = number[];

/** 屏幕坐标 [x, y] */
export type Coordinate = Cesium.Cartesian2;

/** 度分秒坐标 [Longitude, Latitude] */
export type DMSCoordinate = [string, string];
