<!-- 监控视频 -->
<script lang="ts" setup>
import type { VideoTreeNode } from '@/lib/video-detect';

import type FlvJs from 'flv.js';
import { DEVideoDetect } from '@/lib/video-detect';

import { toPublicPath } from '@/utils/resolve-path';
import FullSreenArea from './full-sreen-area.vue';

defineOptions({ name: 'VideoMonitorModal' });

const videoDetect = new DEVideoDetect(`${import.meta.env.VITE_VISUAL_SYSTEM_PATH}/wpCamera/hikvisionWp/forward`);

const prop = {
  value: 'code',
  label: 'name',
  isLeaf: 'isLeaf',
};

async function loadNode(node, resolve) {
  const data = node.data?.ext ? node.data : null;
  const nodes = await videoDetect.getNodes(data);
  resolve(nodes);
}

  type Videos = Record<string, FlvJs.MediaDataSource & { name: string }>;

const videos = reactive<Videos>({});

const laoding = ref(false);
async function nodeClick(data: VideoTreeNode) {
  if (!data.isChannel || videos[data.code]) {
    return;
  }
  if (!Object.keys(videos).includes(data.code)) {
    laoding.value = true;
    const instance = videoDetect.getInstance(data!);
    try {
      const rtsp = await instance.realTimeRTSP();
      const options = [
        'param=-an',
        'param=-c:v',
        'param=copy',
        'param=-f',
        'param=flv',
        // 'param=-buffer_size',
        // 'param=102400',
        'ffmpeg=true',
      ].join('&&&');
      const ssl = window.location.protocol !== 'http:';
      const protocol = ssl ? 'wss://' : 'ws://';
      const url = `${import.meta.env.VITE_VISUAL_SYSTEM_LIVE_PATH}?url=${rtsp}&&&${options}`;
      videos[data.code] = {
        url,
        type: 'flv',
        name: data.name,
      };
    }
    catch (error) {
      console.error(error);
    }
    laoding.value = false;
  }
}

// 计算栅格 style
const style = computed(() => {
  const length = Object.keys(videos)?.length ?? 0;
  let style: any = null;
  let index = 2;
  while (!style) {
    if (length <= index * index) {
      style = {
        'grid-template-rows': Array.from({ length: index })
          .fill('')
          .map(() => `${100 / index}%`)
          .join(' '),
        'grid-template-columns': Array.from({ length: index })
          .fill('')
          .map(() => `${100 / index}%`)
          .join(' '),
      };
      break;
    }
    else {
      index++;
    }
  }
  return style;
});
</script>

<template>
  <FullSreenArea :class="$style['sreen-area']">
    <el-scrollbar class="video-list">
      <el-tree
        lazy
        :props="prop"
        :load="(node, resolve) => loadNode(node, resolve)"
        @node-click="(data) => nodeClick(data)"
      >
        <template #default="{ data }">
          <span
            class="pl-20px"
            :style="{
              color: !data.onLine ? '#aaa' : '#fff',
            }"
          >
            <el-icon v-if="!data.onLine" class="i-tabler:unlink" />
            <el-icon v-else-if="!data.isChannel" class="i-tabler:brand-stackshare" />
            <el-icon v-else class="i-tabler:device-computer-camera" />
            {{ data.name }}
          </span>
        </template>
      </el-tree>
    </el-scrollbar>
    <div class="video-wrap" :style="style">
      <div v-for="code in Object.keys(videos)" :key="code" class="video-box">
        <iframe
          w="100%"
          h="100%"
          :src="toPublicPath(`/video/flv.html?url=${videos[code]?.url} `)"
          frameborder="0"
        />
        <div class="close" @click="() => delete videos[code]">
          <el-icon class="i-material-symbols:close-rounded" text="24px" />
        </div>
        <span class="name">{{ videos[code].name }}</span>
      </div>
    </div>
  </FullSreenArea>
</template>

<style module>
  .sreen-area {
  display: flex;
  align-items: stretch;
  overflow: hidden;
}
</style>

<style scoped lang="scss">
  .video-detect-view {
  width: 100%;
  height: 600px;
}

.video-list {
  width: 400px;
  padding: 20px;
  background: rgb(#1a3583, 30%);

  .el-tree {
    --el-font-size-base: 18px;

    .el-tree-node__content {
      display: flex;
      align-items: center;

      > .el-icon {
        display: none;
      }
    }
  }

  :deep().el-collapse {
    --el-collapse-border-color: transparent;
    --el-collapse-header-height: auto;

    .el-collapse-item__header {
      margin-top: 8px;

      > .el-icon {
        display: none;
      }
    }
  }

  :deep().tree-item {
    display: flex;
    flex: 1;
    align-items: center;
    padding-right: 30px;
    cursor: pointer;

    > img {
      width: 16px;
      height: 16px;
      margin-right: 12px;
      filter: grayscale(100%) brightness(200%);
    }

    > span {
      flex: 1;
    }
  }
}

.video-wrap {
  display: grid;
  flex: 1;
  overflow: hidden;
  background: rgb(#292b2e, 30%);
}

.video-box {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
  background: rgb(1 4 10 / 60%);

  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    color: #fff;
    cursor: pointer;
    background: rgb(#000, 20%);
    border-radius: 15px;
  }

  .video {
    flex: 1;
    align-items: stretch;
    padding: 5px;
    overflow: hidden;
    object-fit: cover;
  }

  .name {
    position: absolute;
    top: 5px;
    left: 10px;
    height: 30px;
  }
}
</style>
