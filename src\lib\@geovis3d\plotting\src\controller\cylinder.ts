import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';

/**
 * cylinder标绘配置
 */
export default <PlottingControllerOptions>{
  type: 'cylinder',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
  location: { visible: true },
  altitude: { visible: true },
  control: { visible: true },
  update(entity) {
    if (!entity.cylinder) {
      entity.cylinder = new Cesium.CylinderGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    if (positions.length === 0) {
      return;
    }
    const controller = entity.plotting;
    if (positions.length === 1) {
      const position = entity.plotting.mousePosition;
      position && positions.push(position);
    }
    if (positions.length < 2) {
      return;
    }
    entity.position = new Cesium.ConstantPositionProperty(positions[0]);
    const radius = Cesium.Cartesian3.distance(positions[0], positions[1]);
    entity.cylinder!.bottomRadius = new Cesium.CallbackProperty(() => radius, false);
    if (controller.defining || entity.cylinder!.length === 0) {
      entity.cylinder!.length = new Cesium.ConstantProperty(radius * 2);
    }
  },
};
