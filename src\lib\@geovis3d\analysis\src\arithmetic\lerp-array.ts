import * as Cesium from 'cesium';

import { clampToHeightMostDetailedByTilesetOrTerrain } from './clamp-to-ground';

export interface LerpArrayOptions {
  /**
   * 起点
   */
  start: Cesium.Cartesian3;
  /**
   * 终点
   */
  end: Cesium.Cartesian3;

  /**
   * 内插值数量
   */
  count: number;

  scene?: Cesium.Scene;

  /**
   * 是否贴地
   */
  clampToGround?: boolean;

  /**
   * 贴地类型
   * @default Cesium.ClassificationType.BOTH
   */
  classificationType?: Cesium.ClassificationType;

  /**
   * 地形数据
   * @default scene.terrainProvider
   */
  terrainProvider?: Cesium.TerrainProvider;
}

/**
 * 在起点和终点间进行插值, 返回的数组包括起点和终点，数组长度为 count+1
 * @param start 起点
 * @param end 终点
 * @param count 内插值的数量
 */
export async function lerpArray(options: LerpArrayOptions): Promise<Cesium.Cartesian3[]> {
  const { start, end, count, scene, clampToGround, classificationType, terrainProvider } = options;
  const result: Cesium.Cartesian3[] = [];

  for (let i = 0; i < count; i++) {
    const position = Cesium.Cartesian3.lerp(start, end, 1 / count, new Cesium.Cartesian3());
    result.push(position);
  }
  result.push(end.clone());
  if (!clampToGround) {
    return result;
  }
  if (!scene) {
    throw new Error('scene is required on `clampToGround == true`.');
  }
  const detaileds = await clampToHeightMostDetailedByTilesetOrTerrain({
    scene,
    terrainProvider,
    positions: result,
    classificationType,
  });

  return detaileds;
}
