import type { Geovis3d } from '../main';

import type { PlottingEntity } from './plotting-entity';
import * as Cesium from 'cesium';

export class Plotting {
  constructor(geovis3d: Geovis3d) {
    this._activeChanged = new Cesium.Event();

    // 单击空白处取消激活态
    // 如果当前激活的entity已经不在定义态了(!defining)，则判断点击是否命中该entity或子entity,不命中则取消激活态
    geovis3d.screenEvent.on('LEFT_CLICK', ({ position }) => {
      const entity = this.active;
      const { defining, sampling } = entity?.plotting ?? {};
      if (!defining && !sampling) {
        const pick = geovis3d.viewer.scene.pick(position);
        // !!!要点击要粒子
        if (!pick) {
          this.active = undefined;
        }
        // const pickEntity = pick?.id;
        // if (![pickEntity, pickEntity?.parent].includes(entity)) {
        //   this.active = undefined;
        // }
      }
    });
  }

  /**
   * @internal
   */
  private _activeChanged: Cesium.Event<(value?: PlottingEntity, prev?: PlottingEntity) => void>;

  get activeChanged() {
    return this._activeChanged;
  }

  /**
   * @internal
   */
  private _active?: PlottingEntity;

  get active(): PlottingEntity | undefined {
    return this._active;
  }

  set active(entity: PlottingEntity | undefined) {
    if (entity && !entity.isMounted) {
      throw new Error('该entity尚未挂载(!entity.isMounted),无法激活');
    }
    const prev = this._active;
    if (prev !== entity) {
      this._active = entity;
      this.activeChanged.raiseEvent(entity, prev);
      prev?.plotting.definitionChanged.raiseEvent(prev?.plotting, 'active', false, true);
      entity?.plotting.definitionChanged.raiseEvent(entity?.plotting, 'active', true, false);
    }
  }
}
