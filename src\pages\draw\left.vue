<script lang="ts" setup>
import { Geovis3d } from '@/lib/@geovis3d/core';
import { PlottingPresetModule } from '@/lib/@geovis3d/vue-plotting-preset';
import { useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'Left' });
const publicPath = import.meta.env.VITE_PUBLIC_PATH;
const show = ref(true);
const viewer = useCzViewer();
watchEffect(async () => {
  if (viewer.value) {
    Geovis3d.register(viewer.value);
    // 取消双击事件 ，双击的话，会视角直接切到该实体，且无法拖拽
    viewer.value.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
    );
  }
});
</script>

<template>
  <layout-left-panel>
    <div class="plan-sty pointer-events-auto relative h-100vh pb-46px pt-64px">
      <div
        class="h-full flex flex-col of-hidden"
        :w="show ? '275px' : '0'"
        transition="all 300"
      >
        <PlottingPresetModule
          v-if="viewer"
          base-url="/"
          app-code="xkq"
          :asset-url="`${publicPath}staticFile`"
          :viewer="viewer"
        />
      </div>
      <el-button link class="toggle-btn" @click="show = !show">
        <el-icon
          class="i-material-symbols:chevron-left"
          text="20px! #FFF!"
          transition="all 300"
          :rotate="show ? '0' : '180'"
          inline-block
        />
      </el-button>
    </div>
  </layout-left-panel>
</template>

<style lang="scss" scoped>
.plan-sty {
  background: var(--el-bg-color) !important;
}

.toggle-btn {
  position: absolute;
  top: 521px;
  left: 100%;
  z-index: 99;
  width: 24px;
  height: 56px;
  background: var(--el-bg-color) !important;
  border-radius: 0 4px 4px 0 !important;
}
</style>
