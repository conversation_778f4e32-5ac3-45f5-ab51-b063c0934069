<!-- ColorBlendModeAttribute -->
<script lang="ts" setup>
import type { ColorBlendModeSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'ColorBlendModeAttribute' });

const props = defineProps<{
  modelValue?: ColorBlendModeSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: ColorBlendModeSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const options = [
  {
    label: '高亮',
    value: 'HIGHLIGHT',
  },
  {
    label: '混合',
    value: 'MIX',
  },
  {
    label: '替换',
    value: 'REPLACE',
  },
];
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="model" :options="options" :clearable="false" />
  </el-form-item>
</template>
