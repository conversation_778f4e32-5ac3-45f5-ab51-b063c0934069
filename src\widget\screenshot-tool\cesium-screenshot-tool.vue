<!-- 专题图 -->
<script lang="ts" setup>
import { usePromiseModal } from '@/hooks/use-promise-modal';
import { useScaleBar } from '@/hooks/useScaleBar';
import { useVh } from '@/utils/view-port';
import { dmsEncode, downloadSource, throttle } from '@x3d/all';
import { useCzEntityCollection, useCzEventListener, useCzViewer } from '@x3d/vue-hooks';
import html2canvas from 'html2canvas-pro';
import { jsPDF as JSPDF } from 'jspdf';

defineOptions({ name: 'Sreenshot' });

const { isActive, open, forceClose } = usePromiseModal<void>(() => { });

defineExpose({
  open,
});

const vh = useVh();

const width = computed(() => vh(500));

watchEffect((onCleanup) => {
  if (isActive.value) {
    const container: any = document.body.querySelector('#cesium-container');
    container && (container.style.zIndex = 999);
    container && (container.style.right = `${width.value}px`);

    onCleanup(() => {
      container && (container.style.zIndex = '');
      container && (container.style.right = '');
    });
  }
});

const viewer = useCzViewer();

const { width: scaleBarWidth, distanceText } = useScaleBar();

const data = ref({
  name: '',
  desc: '',
  grid: false,
  time: dayjs().format('YYYY-MM-DD'),
  legends: [] as ({ label?: string; color?: string; symbol: string }[]),
});

watchEffect(() => {
  if (isActive.value) {
    data.value = {
      name: '',
      desc: '',
      grid: false,
      time: dayjs().format('YYYY-MM-DD'),
      legends: [] as ({ label?: string; color?: string; symbol: string }[]),
    };
  }
});

const entityCollection = useCzEntityCollection();

function gridRender() {
  if (!isActive.value || !data.value.grid) {
    return;
  }
  entityCollection.removeScope();
  const rectangle = viewer.value.camera.computeViewRectangle()?.clone();

  if (rectangle) {
    const diff = (rectangle.north - rectangle.south) / 6;
    const longitudes: number[] = [];
    const latitudes: number[] = [];

    while (rectangle.west + longitudes.length * diff <= rectangle.east) {
      longitudes.push(rectangle.west + longitudes.length * diff);
    }

    while (rectangle.south + latitudes.length * diff <= rectangle.north) {
      latitudes.push(rectangle.south + latitudes.length * diff);
    }
    longitudes.forEach((longitude, i1) => {
      if (i1 === longitudes.length - 1) {
        return;
      }
      latitudes.forEach((latitude, i2) => {
        if (i2 === latitudes.length - 1) {
          return;
        }
        const position = Cesium.Cartesian3.fromRadians(longitude, latitude);
        entityCollection.add(new Cesium.Entity({
          position,
          polyline: {
            width: 1,
            positions: [
              Cesium.Cartesian3.fromRadians(longitude, latitude),
              Cesium.Cartesian3.fromRadians(longitudes[i1 + 1], latitude),
              Cesium.Cartesian3.fromRadians(longitudes[i1 + 1], latitudes[i2 + 1]),

            ],
          },
          label: {
            text: i1 === 1
              ? `${dmsEncode(Cesium.Math.toDegrees(latitude), 3)}${latitude > 0 ? 'N' : 'S'}`
              : i2 === latitudes.length - 2 ? `${dmsEncode(Cesium.Math.toDegrees(longitude), 3)}${longitude > 0 ? 'E' : 'W'}` : undefined,
            font: '12pt bold',
            pixelOffset: new Cesium.Cartesian2(70, -15),
            outlineColor: Cesium.Color.BLACK,
            outlineWidth: 2,
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          },
        }));
      });
    });
  }
}
watchEffect(() => {
  (!isActive.value || !data.value.grid) && entityCollection.removeScope();
  isActive.value && data.value.grid && gridRender();
});

useCzEventListener(() => viewer.value.scene.camera.changed, throttle(() => gridRender(), 100));
useCzEventListener(() => viewer.value.scene.camera.moveEnd, throttle(() => gridRender(), 100));

function addLegend() {
  data.value.legends.push({
    symbol: Cesium.createGuid(),
    label: '',
    color: '#ff0000',
  });
}
async function removeLegend(data: any) {
  await ElMessageBox.confirm('确定移除该图例？', '提示');
  const index = data.legend.findIndex((e: any) => e.symbol === data.symbol);
  if (index !== -1) {
    data.value.legends.splice(index, 1);
  }
}

async function download() {
  viewer.value.scene.render();
  const canvas = await html2canvas(document.querySelector(`#cesium-container`)!, {
    allowTaint: true,
  });
  const blob = await new Promise<Blob>((resolve) => {
    canvas.toBlob(blob => resolve(blob!));
  });
  downloadSource(blob, `${data.value.name || '未命名专题图'}.png`);
}
async function downloadPDF() {
  viewer.value.scene.render();
  const canvas = await html2canvas(document.querySelector(`#cesium-container`)!, {
    allowTaint: true,
  });
  const pdf = new JSPDF();

  const width = 297;
  pdf.internal.pageSize.width = 297;
  const imgWidth = canvas.width;
  const imgHeight = canvas.height;
  const scale = width / imgWidth;
  const scaledHeight = imgHeight * scale;
  pdf.internal.pageSize.width = 297;
  pdf.internal.pageSize.height = scaledHeight;

  pdf.addImage(canvas, 'JPEG', 0, 0, width, scaledHeight);
  pdf.save(`${data.value.name || '未命名专题图'}.pdf`);
}
</script>

<template>
  <teleport v-if="isActive" to="#cesium-container">
    <div position="absolute left-0 right-0 top-0" h="100px" bg="#fff" flex="~ justify-center items-center">
      <span text="40px #000" font="bold">{{ data.name || '未命名专题图' }}</span>
    </div>
    <div position="absolute left-0 bottom-0 top-0" w="50px" bg="#fff" />
    <div position="absolute bottom-0 right-0 top-0" w="50px" bg="#fff" />
    <div
      position="absolute left-0 right-0 bottom-0"
      h="50px"
      px="50px"
      bg="#fff"
      flex="~ items-center justify-between"
    >
      <span text="20px #000">制图日期: {{ $toDayjs(data.time).format('YYYY年MM月DD日') }}</span>
      <span v-if="data.desc" text="20px #000">{{ data.desc }}</span>
    </div>

    <div
      position="absolute right-80px bottom-80px"
      p="x-20px y-10px"
      bg="#fff"
      rd="6px"
      b="#000/10"
      flex="~ col"
      text="16px #000"
      lh="1.5em"
      font="bold"
      min-w="130px"
      min-h="50px"
    >
      <template v-if="data.legends.length">
        <span text="20px" pb="10px">图例</span>
        <div v-for="item in data.legends" :key="item.symbol" flex="~" p="b-5px">
          <div size="16px" mr="10px" flex="shrink-0" :style="{ background: item.color }" />
          <span flex="1" lh="16px">{{ item.label }}</span>
        </div>
      </template>
      <div flex="~ col items-center">
        <span>{{ distanceText }}</span>
        <div b="b-3px #000 x-3px" :style="{ width: `${scaleBarWidth}px` }" h="6px" />
      </div>
    </div>

    <div position="absolute right-50px top-30px">
      <img size="50px!" src="./assets/north-arrow.svg">
    </div>
  </teleport>

  <teleport v-if="isActive" to="body">
    <div
      position="fixed z-100 right-0 top-0"
      h="100vh"
      flex="~ col"
      :style="{ width: `${width}px` }"
      bg="[var(--el-bg-color)]"
    >
      <header-title1 content="专题图制作">
        <template #extra>
          <el-button type="primary" @click="forceClose()">
            返回
          </el-button>
        </template>
      </header-title1>
      <el-scrollbar>
        <el-form size="large" p="20px" :label-width="$vh(100)">
          <el-form-item label="专题图名">
            <el-input v-model="data.name" placeholder="未命名专题图" />
          </el-form-item>
          <el-form-item label="制图日期">
            <el-date-picker v-model="data.time" class="w-100%!" :clearable="false" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="data.desc" placeholder="请输入描述" />
          </el-form-item>
          <el-form-item label="经纬网格">
            <el-switch v-model="data.grid" />
          </el-form-item>
          <el-form-item label="图例">
            <div flex="1 ~ col">
              <div v-for="(item) in data.legends" :key="item.symbol" flex="1 ~" m="b-10px">
                <el-color-picker v-model="item.color" />
                <el-input v-model="item.label" placeholder="请输入图例名" flex="1" />
                <el-button text type="danger" circle @click="removeLegend(item)">
                  <el-icon class="i-custom:delete" text="20px!" />
                </el-button>
              </div>
              <el-button type="primary" flex="self-end" mr="40px!" @click="addLegend()">
                新增图例
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <el-button type="primary" m="x-40px! y-20px!" h="50px!" @click="download">
        下载图片
      </el-button>
      <el-button type="primary" m="x-40px! y-20px!" h="50px!" @click="downloadPDF">
        下载PDF
      </el-button>
    </div>
  </teleport>
</template>
