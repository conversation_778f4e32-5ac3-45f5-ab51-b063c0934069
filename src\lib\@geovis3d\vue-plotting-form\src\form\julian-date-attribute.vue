<!-- JulianDateAttribute -->
<script lang="ts" setup>
import type { JulianDateSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'JulianDateAttribute' });

const props = defineProps<{
  modelValue?: JulianDateSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: JulianDateSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-form-item :label="label">
    <el-date-picker v-model="model" />
  </el-form-item>
</template>
