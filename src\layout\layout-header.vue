<script lang="ts" setup>
import { useSettingStore } from '@/stores/setting';
import { useUserStore } from '@/stores/user';
import SettingDialog from '@/widget/setting/setting-dialog.vue';
import { URL_SETTING_LIST } from '@/widget/setting/url-setting-map';

defineOptions({ name: 'LayoutHeader' });

const userStore = useUserStore();
const settingStore = useSettingStore();
const appName = computed(() => {

  return settingStore.setting?.sysTitle || import.meta.env.VITE_APP_NAME
});
const router = useRouter();

// 获取logo URL
const logoUrl = computed(() => {
  const setting = settingStore.setting as any;
  return setting?.logoUrl ? `/3d-data-system/server${setting.logoUrl}` : '';
});

// 是否显示logo图片
const showLogo = computed(() => !!logoUrl.value);
/**
 * 导航目录
 */
const menuRoutes = computed(() => {
  const routes = router.options.routes.find(item => item.name === 'Layout')?.children ?? [];
  const menus = settingStore.activeMenus;

  // 定义优先显示的路由路径
  const priorityPaths = ['/home', '/servers'];

  // 将路由分为优先路由和其他路由
  const priorityRoutes = priorityPaths
    .map(path => routes.find(route => route.path === path))
    .filter(Boolean); // 过滤掉不存在的路由

  // 过滤其他路由（排除优先路由，应用角色和菜单检查）
  const otherRoutes = routes.filter((route) => {
    // 排除已添加的优先路由
    if (priorityPaths.includes(route.path)) {
      return false;
    }

    const meta = route.children![0].meta;
    if (!menus.includes(meta?.title ?? '')) {
      return false;
    }

    const limitRoles = meta?.roles ?? [];
    if (limitRoles.length) {
      return userStore.roleList.some(role => limitRoles.includes(role));
    }
    else {
      return true;
    }
  });

  // 合并优先路由和其他路由
  const filteredRoutes = [...priorityRoutes, ...otherRoutes];

  return filteredRoutes.sort((a, b) => (a.children?.[0]?.meta?.sort ?? 1) - (b.children?.[0]?.meta?.sort ?? 1));
});

const systemSettingVisiable = ref(false);

const urlPermissionList = computed(() => userStore.userInfo?.permissionList ?? []);

async function logout() {
  await ElMessageBox.confirm('确定退出系统？', '提示');
  userStore.logout();
}

function openTab(url: string) {
  window.open(url);
}
const buttons = computed(() => {
  const setting: any = settingStore.setting || {};
  return URL_SETTING_LIST.map((item) => {
    let src: string = setting[item.url];
    src = src?.replaceAll('{token}', userStore.authorization ?? '');
    src = src?.replaceAll('{base64token}', btoa(userStore.authorization ?? ''));

    return {
      ...item,
      text: setting[item.name],
      src,
      // 海南还需同步权限
      disabled: import.meta.env.VITE_ENV === 'HAINAN' ? !urlPermissionList.value.includes(item.value) : false,
    };
  });
});

const route = useRoute();
function systemLog() {
  router.push({ path: '/system-log' });
}
function goToPage(item: any) {
  if (item.path === '/servers') {
    window.open('http://localhost:3000/3d-data-system/#/servers');
  }
  else if (item.path === '/directory') {
    let src: string | undefined = settingStore.setting.dataProcessingUrl;
    src = src?.replaceAll('{token}', userStore.authorization ?? '');
    src = src?.replaceAll('{base64token}', btoa(userStore.authorization ?? ''));
    if (src) {
      window.open(src);
      router.back();
    }
  }
  else {
    router.push({ path: item.path });
  }
}
</script>

<template>
  <div class="layout-header">
    <div flex="~ items-center">
      <div class="mr-48px min-w-375px whitespace-nowrap pr-20px font-blod-24px" flex="~ items-center">
        <img v-if="showLogo" :src="logoUrl" class="ml-24px mr-8px" alt="Logo" style=" width: auto;height: 24px;">
        <el-icon v-else class="i-custom:logo ml-24px mr-8px" text="24px" />
        {{ appName }}
      </div>
      <div pointer-events-auto class="menu-container whitespace-nowrap">
        <template v-for="item in menuRoutes" :key="item.name">
          <el-button link class="m-0! px-24px! text-18px!" :class="{ active: route?.path === item.path }"
            @click="goToPage(item)">
            {{ item?.children?.[0]?.meta?.title }}
          </el-button>
        </template>
      </div>
    </div>

    <div flex="~ 1 justify-end">
      <div pointer-events="initial" flex="~ items-center" p="r-20px">
        <el-dropdown>
          <el-icon class="i-tabler:menu-2" text="24px" />
          <template #dropdown>
            <el-dropdown-menu>
              <template v-for="(item, index) in buttons" :key="index">
                <el-dropdown-item v-if="item.label && item.src && !item.disabled">
                  <el-button link un-text="18px!" @click="openTab(item.src)">
                    <el-icon class="i-custom:threedate" />
                    {{ item.text }}
                  </el-button>
                </el-dropdown-item>
              </template>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-divider direction="vertical" h="15px!" b="1px #FFF!" mx="16px!" />
        <el-popover placement="bottom" trigger="hover" popper-style="padding:8px" :width="$vh(120)" :show-arrow="false">
          <template #reference>
            <el-button link un-bg="transparent!">
              <div size="30px!" rd="50%" text="18px" bg="#fff/20" flex="~ items-center justify-center" m="r-10px">
                <el-icon class="i-custom:user" />
              </div>
              <span>{{ userStore.userName }}</span>
            </el-button>
          </template>
          <div flex="~ col" class="menu-btn">
            <el-button text un-hover="font-blod-14px!" m="0!">
              个人信息
            </el-button>
            <el-button v-if="userStore.roleList.includes('system')" text un-hover="font-blod-14px!" m="0!"
              @click="systemSettingVisiable = true">
              系统设置
            </el-button>
            <el-button text un-hover="font-blod-14px!" m="0!" @click="systemLog()">
              系统日志
            </el-button>
            <el-button text un-hover="font-blod-14px!" m="0!" @click="logout()">
              退出登录
            </el-button>
          </div>
        </el-popover>
      </div>
    </div>
    <SettingDialog v-model="systemSettingVisiable" />
  </div>
</template>

<style scoped lang="scss">
.layout-header {
  position: absolute;
  top: -2px;
  right: 0;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 64px;
  background: var(--el-bg-color); // #292b2e;

  .menu-container {
    .el-button {
      position: relative;
      height: 64px;
      background-color: transparent;

      &:hover,
      &.active {
        font-weight: 700;
        color: #4176ff;
        background-color: rgb(0 0 0 / 20%);
      }

      &::after {
        position: absolute;
        bottom: 10px;
        display: inline-block;
        width: calc(100% - 48px);
        height: 3px;
        margin: 0 24px;
        content: '';
        background: #4176ff;
        border-radius: 3px;
        opacity: 0;
        transition: all 0.5s;
      }

      &.active::after {
        opacity: 1;
      }
    }
  }

  /* stylelint-disable-next-line no-descending-specificity */
  .el-button {
    --el-fill-color-light: rgb(255 255 255 / 20%);

    padding: 8px 15px;
    background-color: rgb(255 255 255 / 20%);
  }
}
</style>
