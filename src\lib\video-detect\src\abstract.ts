export interface VideoTreeNode<T = Record<string, any>> {
  name: string;

  /** 可作为唯一ID */
  code: string;

  /** 是否为叶（没有下级了） */
  isLeaf: boolean;

  /** 是否在线 */
  onLine: boolean;

  /** 是否是通道 */
  isChannel: boolean;

  /** 拓展数据 */
  ext: T;
}

export abstract class VideoDetect<T = any> {
  abstract getCount(): Promise<number | undefined>;

  abstract getNodes(params: VideoTreeNode<T>): Promise<VideoTreeNode<T>[]>;

  abstract getInstance(params: VideoTreeNode<T>): ChannelInstance<T>;
}

export abstract class ChannelInstance<T = Record<string, any>> {
  abstract data: VideoTreeNode<T>;

  /** 获取实时流地址 */
  abstract realTimeRTSP(): Promise<string>;
}
