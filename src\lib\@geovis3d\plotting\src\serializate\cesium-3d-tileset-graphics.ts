import * as Cesium from 'cesium';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface Cesium3DTilesetGraphicsSerializateJSON {
  show?: boolean;
  uri?: string;
  maximumScreenSpaceError?: number;
}

export type Cesium3DTilesetGraphicsKey = keyof Cesium3DTilesetGraphicsSerializateJSON;

export class Cesium3DTilesetGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.Cesium3DTilesetGraphics,
    omit?: Cesium3DTilesetGraphicsKey[],
    time?: Cesium.JulianDate,
  ): Cesium3DTilesetGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }

    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      uri: getValue('uri'),
      maximumScreenSpaceError: getValue('maximumScreenSpaceError'),
    };
  }

  static fromJSON(
    json?: Cesium3DTilesetGraphicsSerializateJSON,
    omit?: Cesium3DTilesetGraphicsKey[],
  ): Cesium.Cesium3DTilesetGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.Cesium3DTilesetGraphics({
      show: getValue('show') ?? true,
      uri: getValue('uri'),
      maximumScreenSpaceError: getValue('maximumScreenSpaceError'),
    });
  }
}
