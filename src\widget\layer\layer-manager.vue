<!-- 图层管理 -->
<script lang="tsx" setup>
import type { ElTree, TreeInstance } from 'element-plus';
import type { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type.mjs';
import type { LayerInfo } from './utils/legacy';
import { layerInfoDeleteByIdIdUsingPost } from '@/genapi/cimapi';
import GroupEditDialog from './components/group-edit-dialog.vue';
import LayerEditDialog from './components/layer-edit-dialog.vue';
import LegendContainer from './components/legend-container.vue';
import PropertiesPopperLayer from './components/properties-popper-layer.vue';
import PropertiesPopperPickFeature from './components/properties-popper-pick-feature.vue';
import PropertiesPopperVector from './components/properties-popper-vector.vue';
import TilesetEditDialog from './components/tileset-edit-dialog.vue';
import WfsEditDialog from './components/wfs-edit-dialog.vue';
import { useLayerState } from './state/layer-state';

defineOptions({ name: 'LayerManager' });
const visible = ref(false);

const { rawTreeList, checkedIds, refreshTreeList, treeListLoading } = useLayerState();

const treeRef = shallowRef<TreeInstance>();
const keyword = ref('');

const showAddLayer = ref(false);
const showMoreMenu = ref(false);
const shwo3DTilePanel = ref(false);

watch(showAddLayer, b => b && (shwo3DTilePanel.value = !b));
watch(shwo3DTilePanel, b => b && (showAddLayer.value = !b));
watch(keyword, (val) => {
  treeRef.value!.filter(val);
});

// 删除图层
async function deleteItem(item: LayerInfo) {
  if (!item.id) {
    return;
  }
  await ElMessageBox.confirm(
    () => (
      <span>
        您确定要
        <span text="#FF6363">删除</span>
        该图层吗？
      </span>
    ),
    {
      title: '提示',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    },
  );
  await layerInfoDeleteByIdIdUsingPost({ path: { id: item.id } });
  await refreshTreeList();
}

function handleCheck() {
  const keys = treeRef.value?.getCheckedKeys() ?? [];
  checkedIds.value = toRaw(keys) as any;
}

async function dropComplete(
  dragNode: TreeNodeData,
  dropNode: TreeNodeData,
  type: string,
) {
  const { fid, sort, id, config } = dropNode.data;
  await cimApi.layerInfoSaveOrUpdateLayerInfoUsingPost({
    data: {
      ...dragNode.data,
      config: JSON.stringify(config),
      fid: fid === '0' ? id : fid,
      sort: type === 'before' ? Cesium.Math.clamp(sort - 1, 0, 100) : sort + 1,
    },
  });
  refreshTreeList();
}
function allowDrop(_drag: TreeNodeData, drop: TreeNodeData, type: string) {
  return !!(+drop.data.fid === 0 && type === 'inner') || !!(+drop.data.fid !== 0 && type !== 'inner');
}

function allowDrag(node: TreeNodeData) {
  return !!Object.keys(node?.data?.config ?? {}).length;
}

function filterNode(key: string, data: any) {
  if (!key) {
    return true;
  }
  return data?.layerName?.includes(key);
}

const currentEditTilesetId = ref<string>();

const layerEditDialogRef = templateRef('layerEditDialogRef');

const groupEditDialogRef = templateRef('groupEditDialogRef');

// 图层修改
async function modifyItem(data: LayerInfo) {
  if (data?.id && checkedIds.value.includes(data.id)) {
    ElMessageBox.alert('当前图层已被选中，请先取消选中再进行操作！', '提示', {
      confirmButtonText: '确定',
    });
    return;
  }
  await layerEditDialogRef.value?.open(data);
  refreshTreeList();
}

const wfsEditDialogRef = templateRef('wfsEditDialogRef');
</script>

<template>
  <el-popover
    :visible="visible"
    placement="top-start"
    trigger="click"
    width="auto"
    :show-arrow="false"
    :teleported="false"
    popper-class="pointer-events-auto p-0px!"
  >
    <template #reference>
      <slot @click.stop="visible = !visible" />
    </template>
    <div of="auto" resize="x" w="280px">
      <header-title1 class="w-auto! py-20px! pr-10px!" b-b="1px! solid #fff/10%!">
        图层
        <template #extra>
          <el-button
            link
            @click="groupEditDialogRef?.open({})"
          >
            <el-icon class="i-material-symbols:add-circle-outline color-#fff text-20px!" />
          </el-button>
          <el-button link @click.stop="visible = false">
            <el-icon class="i-material-symbols:close" text="20px! #fff!" />
          </el-button>
        </template>
      </header-title1>
      <el-input v-model="keyword" placeholder="请输入图层名称" h="40px!">
        <template #suffix>
          <el-icon class="i-material-symbols:search" text="20px! #fff/50%!" />
        </template>
      </el-input>
      <el-scrollbar v-loading="treeListLoading" class="h-700px!" wrap-class=" p-10px!">
        <el-tree
          ref="treeRef"
          node-key="id"
          :data="rawTreeList"
          :default-checked-keys="checkedIds"
          :expand-on-click-node="false"
          :allow-drag="allowDrag"
          :allow-drop="allowDrop"
          :filter-node-method="filterNode"
          draggable
          show-checkbox
          @check="handleCheck"
          @node-drop="dropComplete"
        >
          <template #default="{ data }">
            <div
              :class="data.fid === '0' ? 'font-blod-16px! text-#bbb!' : 'font-normal-sm'"
              of="hidden"
              flex="~ 1 items-center justify-between"
            >
              <el-text truncated flex="1">
                {{ data.layerName }}
              </el-text>
              <el-popover
                placement="bottom"
                trigger="hover"
                popper-class="p-8px! w-120px!"
                :show-arrow="false"
              >
                <template #reference>
                  <el-button
                    link
                    circle
                    class="item-menu h-20px!"
                    @click="showMoreMenu = true"
                  >
                    <el-icon class="i-custom:menu" text="20px! #fff!" />
                  </el-button>
                </template>
                <div v-if="data.fid === '0'" flex="~ col items-center" un-children="m-x-0!">
                  <el-button text @click="groupEditDialogRef?.open(data)">
                    重命名
                  </el-button>
                  <el-button text @click="modifyItem({ fid: data.id })">
                    添加图层
                  </el-button>
                  <el-button text @click="deleteItem(data)">
                    删除
                  </el-button>
                </div>
                <div v-else flex="~ col items-center" un-children="m-x-0!">
                  <el-button text @click="modifyItem(data)">
                    修改图层
                  </el-button>
                  <el-button
                    v-if="data.config.type === '倾斜摄影'"
                    text
                    @click="currentEditTilesetId = data.id"
                  >
                    编辑图层
                  </el-button>
                  <el-button text @click="deleteItem(data)">
                    删除
                  </el-button>
                </div>
              </el-popover>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </el-popover>

  <TilesetEditDialog v-model:tileset-id="currentEditTilesetId" />
  <LayerEditDialog ref="layerEditDialogRef" />
  <WfsEditDialog ref="wfsEditDialogRef" />
  <GroupEditDialog ref="groupEditDialogRef" />
  <PropertiesPopperVector />
  <PropertiesPopperLayer />
  <PropertiesPopperPickFeature />
  <LegendContainer />
</template>

<style lang="scss" scoped>
:deep() .el-tree {
  &-node {
    &__expand-icon {
      --el-tree-expand-icon-color: #fff;

      font-size: 16px !important;
    }

    &__content {
      --el-tree-node-content-height: 56px;

      position: relative;
      width: 100%;

      .el-checkbox {
        --el-checkbox-input-width: 14px;
        --el-checkbox-input-height: 14px;

        pointer-events: none;
      }

      .item-menu {
        display: none;
      }

      &:hover .item-menu {
        display: block;
      }
    }

    &__children {
      overflow: visible;

      .el-tree-node {
        &__content {
          --el-tree-node-content-height: 40px;

          .el-checkbox {
            pointer-events: all;
          }
        }
      }
    }
  }
}
</style>
