<!-- 图层新增、修改弹窗 -->
<script lang="ts" setup>
import type { LayerConfig } from '../assets/types';
import type { LayerInfo } from '../utils/legacy';
import { layerInfoSaveOrUpdateLayerInfoUsingPost } from '@/genapi/cimapi';
import { usePromiseModal } from '@/hooks/use-promise-modal';
import { detailParams } from '../assets/detailParam';
import LayerFormBbox from './layer-form-bbox.vue';
import LayerFormCamera from './layer-form-camera.vue';
import LayerFormEgis from './layer-form-egis.vue';
import LayerFormLegend from './layer-form-legend.vue';
import LayerFormParam from './layer-form-param.vue';
import LayerFormWfs from './layer-form-wfs.vue';

defineOptions({ name: 'LayerEditDialog' });

const data = ref<LayerInfo>({});

const config = ref<LayerConfig>({});

const { isActive, open, completed, forceClose } = usePromiseModal<void, LayerInfo>(
  (item) => {
    data.value = JSON.parse(JSON.stringify(item));
    config.value = data.value.config ?? {};
  },
);
defineExpose({
  open,
  forceClose,
  completed,
});

async function submit() {
  await layerInfoSaveOrUpdateLayerInfoUsingPost({
    data: {
      ...data.value,
      layerName: config.value.name,
      config: JSON.stringify(config.value),
    },
  });
  ElMessage.success('保存成功');
  completed();
}
</script>

<template>
  <drag-card
    v-if="isActive"
    :title="`${data.id ? '修改图层' : '添加图层'}`"
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    @close="forceClose()"
  >
    <el-scrollbar h="60vh!">
      <header-title2 content="基础设置" />
      <el-form :label-width="$vh(88)" p="x-20px">
        <el-form-item label="默认显示">
          <el-switch v-model="config.defaultVisible" />
        </el-form-item>
        <el-form-item label="节点名称">
          <el-input v-model="config.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="图层类型">
          <el-select-v2 v-model="config.type" :options="detailParams.map(item => ({ label: item.name, value: item.name }))" placeholder="请选择" />
        </el-form-item>
      </el-form>
      <header-title2 class="mb-16px" content="参数设置" />
      <LayerFormParam v-model="config.layer" :type="config.type" />
      <LayerFormEgis v-model="config.egis" />
      <template v-if="config.type !== '倾斜摄影' ">
        <LayerFormBbox v-model="config.rectangle" :url="config.layer?.url" :layer="config.layer?.layer" />
      </template>
      <LayerFormCamera v-model="config.camera" />
      <LayerFormWfs v-model="config.wfs" />
      <LayerFormLegend />
    </el-scrollbar>
    <template #footer>
      <el-button
        type="primary"
        class="plain-#4176FF! px-26px!"
      >
        预览
      </el-button>
      <div>
        <el-button
          px="26px!"
          class="bg-white! color-black!"
          @click="forceClose()"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          class="bg-#4176FF! px-26px! color-white!"
          @click="submit()"
        >
          确定
        </el-button>
      </div>
    </template>
  </drag-card>
</template>

<style scoped lang="scss">
.add-layer-panel {
  position: relative;
  width: 400px;
  pointer-events: initial;

  --el-component-size: 38px;
}

.el-form {
  :deep() .el-select {
    --el-fill-color-blank: #1c1d1e;
    --el-color-primary: #616162;
  }
}
</style>
