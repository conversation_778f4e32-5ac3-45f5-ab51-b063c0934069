import type { Middleware, Placement } from '@floating-ui/dom';
import { computePosition } from '@floating-ui/dom';

import * as Cesium from 'cesium';
import { createCzElement } from './create-cz-element';

/**
 * 任意坐标转换成二维画布坐标
 * @param scene
 * @param position
 */
function coordinateTransform(
  scene: Cesium.Scene,
  position?: Cesium.Cartesian2 | Cesium.Cartesian3 | Cesium.Cartographic,
): Cesium.Cartesian2 | undefined {
  if (position instanceof Cesium.Cartesian2) {
    return position;
  }
  else if (position instanceof Cesium.Cartesian3) {
    return scene.cartesianToCanvasCoordinates(position);
  }
  else if (position instanceof Cesium.Cartographic) {
    const cartesian = Cesium.Ellipsoid.WGS84.cartographicToCartesian(position);
    return scene.cartesianToCanvasCoordinates(cartesian);
  }
}

export interface CreateCzFollowerOptions {
  scene: Cesium.Scene;
  group?: string;
  guid?: string;
  className?: string | string[];
  style?: CSSStyleDeclaration;
}

/**
 * 用于更新元素的函数
 */
export interface UpdatePopperOptions {
  position?: Cesium.Cartesian2 | Cesium.Cartesian3 | Cesium.Cartographic;
  placement?: Placement;
  middleware?: Array<Middleware | null | undefined | false>;
}

export interface CreateCzFollowerRetrun {
  /**
   * 创建的div元素
   */
  element: HTMLDivElement;

  /**
   * 用于更新元素的函数
   */
  update: (option: UpdatePopperOptions) => void;

  /**
   * 销毁方法
   */
  destroy: () => void;
}

/**
 * 创建一个可更新坐标位置的元素
 * @param options
 */
export function createCzFollower(options: CreateCzFollowerOptions): CreateCzFollowerRetrun {
  const scene = options.scene;
  const { element, destroy: destroyElement } = createCzElement(options);
  element.style.position = 'absolute';

  let position = new Cesium.Cartesian2();
  let destroyed = false;

  const virtualEl = {
    getBoundingClientRect() {
      const { x = 0, y = 0 } = position;
      return {
        width: 0,
        height: 0,
        x,
        y,
        left: x,
        right: x,
        top: y,
        bottom: y,
      };
    },
  };

  const update = (options: UpdatePopperOptions) => {
    if (destroyed) {
      console.warn('popper is destroyed.');
      return;
    }
    position = coordinateTransform(scene, options.position) || new Cesium.Cartesian2();
    computePosition(virtualEl, element, {
      placement: options.placement,
      middleware: options.middleware,
    }).then(({ x, y }) => {
      element.style.left = `${x}px`;
      element.style.top = `${y}px`;
    });
  };

  const destroy = () => {
    if (!destroyed) {
      destroyElement();
      destroyed = true;
    }
  };
  return {
    element,
    update,
    destroy,
  };
}
