import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.DataSourceCollection} 拓展用法与 {@link Cesium.DataSourceCollection} 基本一致。
 *
 * `GcDataSourceCollection.event`鼠标事件监听
 */
export class GcDataSourceCollection extends Cesium.DataSourceCollection {
  constructor() {
    super();
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
