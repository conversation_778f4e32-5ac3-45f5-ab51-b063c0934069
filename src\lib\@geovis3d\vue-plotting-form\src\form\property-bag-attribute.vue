<!-- PropertyBagAttribute -->
<script lang="ts" setup>
import type { PropertyBagSerializateJSON } from '@/lib/@geovis3d/plotting';
import { useShallowBinding } from './hooks';

defineOptions({ name: 'PropertyBagAttribute' });

const props = defineProps<{
  modelValue?: PropertyBagSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: PropertyBagSerializateJSON): void;
}>();

const model = ref<PropertyBagSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div class="property-bag-attribute">
    PropertyBagAttribute
  </div>
</template>
