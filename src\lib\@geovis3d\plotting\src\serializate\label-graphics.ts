import type { Cartesian2SerializateJSON } from './cartesian2';

import type { Cartesian3SerializateJSON } from './cartesian3';
import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type {
  HeightReferenceSerializateJSON,
  HorizontalOriginSerializateJSON,
  LabelStyleSerializateJSON,
  VerticalOriginSerializateJSON,
} from './enum';
import type { NearFarScalarSerializateJSON } from './near-far-scalar';
import * as Cesium from 'cesium';
import { Cartesian2Serializate } from './cartesian2';

import { Cartesian3Serializate } from './cartesian3';
import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { NearFarScalarSerializate } from './near-far-scalar';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface LabelGraphicsSerializateJSON {
  show?: boolean;
  text?: string;
  font?: string;
  style?: LabelStyleSerializateJSON;
  scale?: number;
  showBackground?: boolean;
  backgroundColor?: ColorSerializateJSON;
  backgroundPadding?: Cartesian2SerializateJSON;
  pixelOffset?: Cartesian2SerializateJSON;
  eyeOffset?: Cartesian3SerializateJSON;
  horizontalOrigin?: HorizontalOriginSerializateJSON;
  verticalOrigin?: VerticalOriginSerializateJSON;
  heightReference?: HeightReferenceSerializateJSON;
  fillColor?: ColorSerializateJSON;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  translucencyByDistance?: NearFarScalarSerializateJSON;
  pixelOffsetScaleByDistance?: NearFarScalarSerializateJSON;
  scaleByDistance?: NearFarScalarSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  disableDepthTestDistance?: number;
}

export type LabelGraphicsKey = keyof LabelGraphicsSerializateJSON;

export class LabelGraphicsSerializate {
  private constructor() {}
  static toJSON(
    data?: Cesium.LabelGraphics,
    omit?: LabelGraphicsKey[],
    time?: Cesium.JulianDate,
  ): LabelGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      text: getValue('text'),
      font: getValue('font'),
      style: EnumSerializate.toJSON(Cesium.LabelStyle, getValue('style')),
      scale: getValue('scale'),
      showBackground: getValue('showBackground'),
      backgroundColor: ColorSerializate.toJSON(getValue('backgroundColor')),
      backgroundPadding: Cartesian2Serializate.toJSON(getValue('backgroundPadding')),
      pixelOffset: Cartesian2Serializate.toJSON(getValue('pixelOffset')),
      eyeOffset: Cartesian3Serializate.toJSON(getValue('eyeOffset')),
      horizontalOrigin:
        EnumSerializate.toJSON(Cesium.HorizontalOrigin, getValue('horizontalOrigin')) ?? 'CENTER',
      verticalOrigin:
        EnumSerializate.toJSON(Cesium.VerticalOrigin, getValue('verticalOrigin')) ?? 'BOTTOM',
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      fillColor: ColorSerializate.toJSON(getValue('fillColor')),
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      translucencyByDistance: NearFarScalarSerializate.toJSON(getValue('translucencyByDistance')),
      pixelOffsetScaleByDistance: NearFarScalarSerializate.toJSON(
        getValue('pixelOffsetScaleByDistance'),
      ),
      scaleByDistance: NearFarScalarSerializate.toJSON(getValue('scaleByDistance')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      disableDepthTestDistance: getValue('disableDepthTestDistance'),
    };
  }

  static fromJSON(
    json?: LabelGraphicsSerializateJSON,
    omit?: LabelGraphicsKey[],
  ): Cesium.LabelGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.LabelGraphics({
      show: getValue('show') ?? true,
      text: getValue('text'),
      font: getValue('font'),
      style: EnumSerializate.fromJSON(Cesium.LabelStyle, getValue('style')),
      scale: getValue('scale'),
      showBackground: getValue('showBackground'),
      backgroundColor: ColorSerializate.fromJSON(getValue('backgroundColor')),
      backgroundPadding: Cartesian2Serializate.fromJSON(getValue('backgroundPadding')),
      pixelOffset: Cartesian2Serializate.fromJSON(getValue('pixelOffset')),
      eyeOffset: Cartesian3Serializate.fromJSON(getValue('eyeOffset')),
      horizontalOrigin: EnumSerializate.fromJSON(
        Cesium.HorizontalOrigin,
        getValue('horizontalOrigin'),
      ),
      verticalOrigin: EnumSerializate.fromJSON(Cesium.VerticalOrigin, getValue('verticalOrigin')),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      fillColor: ColorSerializate.fromJSON(getValue('fillColor')),
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      translucencyByDistance: NearFarScalarSerializate.fromJSON(getValue('translucencyByDistance')),
      pixelOffsetScaleByDistance: NearFarScalarSerializate.fromJSON(
        getValue('pixelOffsetScaleByDistance'),
      ),
      scaleByDistance: NearFarScalarSerializate.fromJSON(getValue('scaleByDistance')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      disableDepthTestDistance: getValue('disableDepthTestDistance'),
    });
  }
}
