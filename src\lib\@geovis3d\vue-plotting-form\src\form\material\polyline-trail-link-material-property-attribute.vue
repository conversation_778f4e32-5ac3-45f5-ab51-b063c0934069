<!-- PolylineTrailLinkMaterialPropertyAttribute 属性编辑 -->
<script lang="ts" setup>
import type { PolylineTrailLinkMaterialPropertySerializateJSON } from '@/lib/@geovis3d/plotting';

import ColorAttribute from '../color-attribute.vue';
import { useShallowBinding } from '../hooks';
import NumberAttribute from '../number-attribute.vue';

defineOptions({ name: 'PolylineTrailLinkMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: PolylineTrailLinkMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: PolylineTrailLinkMaterialPropertySerializateJSON): void;
}>();

const model = ref<PolylineTrailLinkMaterialPropertySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="追踪线" /> -->
  <ColorAttribute v-model="model.color" label="颜色" />
  <NumberAttribute v-model="model.time" :min="1" :precision="0" label="持续时间" />
</template>
