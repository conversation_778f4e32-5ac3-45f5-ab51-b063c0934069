import * as Cesium from 'cesium';

import PolylineArrowLinkPNG from './assets/polyline-arrow-link.png';
import { setCesiumMaterialCache } from './material-cache';
import PolylineArrowLink from './shaders/polyline-arrow-link.glsl?raw';

export interface PolylineArrowLinkMaterialUniforms {
  color?: Cesium.Color;
  time?: number;
  image?: string;
}

const uniforms: PolylineArrowLinkMaterialUniforms = {
  color: Cesium.Color.RED,
  time: 0,
  image: PolylineArrowLinkPNG,
};

const PolylineArrowLinkMaterialType = 'PolylineArrowLinkMaterialType';

export class PolylineArrowLinkMaterial extends Cesium.Material {
  constructor(options?: PolylineArrowLinkMaterialUniforms) {
    super({
      fabric: {
        type: PolylineArrowLinkMaterialType,
        source: PolylineArrowLink,
        uniforms: {
          color: options?.color ?? uniforms.color,
          time: options?.time ?? uniforms.time,
          image: options?.image ?? uniforms.image,
        },
      },
    });
  }
}

/**
 * 动态箭头线
 */
export class PolylineArrowLinkMaterialProperty implements Cesium.MaterialProperty {
  constructor(options?: PolylineArrowLinkMaterialUniforms) {
    this._color = Cesium.defaultValue(options?.color, uniforms.color);
    // this._time = Cesium.defaultValue(options?.time, uniforms.time);
    this._time = performance.now();

    this._image = Cesium.defaultValue(options?.image, uniforms.image);
  }

  private _time: number;

  get time() {
    return this._time;
  }

  set time(value: number) {
    if (this._time !== value) {
      this._time = value;
    }
  }

  private _color: Cesium.Color;

  get color() {
    return this._color;
  }

  set color(value: Cesium.Color) {
    if (this._color !== value) {
      this._color = value;
    }
  }

  private _image: string;

  get image() {
    return this._image;
  }

  set image(value: string) {
    if (this._image !== value) {
      this._image = value;
    }
  }

  static get MaterialType() {
    return PolylineArrowLinkMaterialType;
  }

  getType(_time?: Cesium.JulianDate) {
    return PolylineArrowLinkMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: PolylineArrowLinkMaterialUniforms) {
    result ??= {};
    result.color = this.color;
    result.image = this.image;
    result.time = (performance.now() - this._time) / 1000;

    return result;
  }

  equals(other?: PolylineArrowLinkMaterialProperty) {
    return (
      this === other
      || (other instanceof PolylineArrowLinkMaterialProperty
        && this.color == other?.color
        && this.time == other?.time
        && this.image == other?.image)
    );
  }
}
setCesiumMaterialCache(PolylineArrowLinkMaterialType, {
  fabric: {
    type: PolylineArrowLinkMaterialType,
    uniforms,
    source: PolylineArrowLink,
  },
  translucent: () => true,
});
