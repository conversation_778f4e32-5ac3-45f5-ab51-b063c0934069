<!-- 安全承诺详情 -->
<script lang="ts" setup>
import type {
  WeiXianYuanXinXiDTO,
} from '@/genapi/production';

import {
  productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet,
  productionHazardousChemicalsGetSafetyCommitByCompanyCodeCompanyCodeUsingGet,
} from '@/genapi/production';
import { computedLoading } from '@/hooks/computed-loading';
import { ElDescriptionsItem as DescItem, ElDescriptions as Descs } from 'element-plus';
import { COMPANY_RISK_RANK_ENUM, COMPANY_RISK_RANK_ENUM_COLORS } from '../../enum';

defineOptions({ name: 'DetailSafePromise' });

const props = defineProps<{
  companyCode?: string;
}>();
const [data, loading] = computedLoading(async () => {
  if (!props.companyCode)
    return;

  const { data } = await productionHazardousChemicalsGetSafetyCommitByCompanyCodeCompanyCodeUsingGet(
    {
      path: {
        companyCode: props.companyCode!,
      },
    },
  );
  return data;
});

// 危险源列表
const [sources] = computedLoading(async () => {
  if (!props.companyCode)
    return;
  const { data } = await productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet({
    path: {
      companyCode: props.companyCode!,
    },
  });
  return data;
});

// 危险源按危险源等级进行分类 key:等级  value: WeiXianYuanXinXiDTO[]
const sourcesCats = computed(() => {
  const record: Record<string, WeiXianYuanXinXiDTO[]> = {};
  sources.value?.forEach((source) => {
    if (source.hazardRank) {
      record[source.hazardRank] ??= [];
      record[source.hazardRank].push(source);
    }
  });
  return record;
});
</script>

<template>
  <el-scrollbar v-loading="loading" class="detail-safe-promise" wrap-class="px-20px">
    <header-title2 class="pb-10px">
      <span class="mr-20px">企业状态</span>
      <span :style="{ color: COMPANY_RISK_RANK_ENUM_COLORS[data?.riskGrade ?? ''] }">
        {{ COMPANY_RISK_RANK_ENUM[data?.riskGrade ?? ''] || '未知' }}
      </span>
    </header-title2>

    <Descs :column="3" class="flex-1" border>
      <DescItem align="left" label="生产装置套数">
        {{ data?.unitsNumber || 0 }}套
      </DescItem>
      <DescItem align="left" label="运行套数">
        {{ data?.runNumber || 0 }}套
      </DescItem>
      <DescItem align="left" label="停产套数">
        {{ data?.parkNumber || 0 }}套
      </DescItem>
      <DescItem align="left" label="一级重大危险源">
        {{ sourcesCats['1']?.length ?? 0 }}处
      </DescItem>
      <DescItem align="left" label="二级重大危险源">
        {{ sourcesCats['2']?.length ?? 0 }}处
      </DescItem>
      <DescItem align="left" label="三级重大危险源">
        {{ sourcesCats['3']?.length ?? 0 }}处
      </DescItem>
      <DescItem align="left" label="四级重大危险源">
        {{ sourcesCats['4']?.length ?? 0 }}处
      </DescItem>
      <DescItem align="left" label="一般危险源">
        {{ sourcesCats['9']?.length ?? 0 }}处
      </DescItem>
      <!-- <DescItem align="left" label="开车装置数"> {{ data?.zrr }}处 </DescItem>
      <DescItem align="left" label="停车装置数"> {{ data?.zrr }}处 </DescItem>
      <DescItem align="left" label="试生产装置数"> {{ data?.zrr }}处 </DescItem>
      <DescItem align="left" label="重点监管危险工艺"> {{ data?.zrr }}处 </DescItem>
      <DescItem align="left" label="检维修套数"> {{ data?.zrr }}套 </DescItem> -->
      <DescItem align="left" label="特级动火作业">
        {{ data?.firesNumber || 0 }} 处
      </DescItem>
      <DescItem align="left" label="一级动火作业">
        {{ data?.fire1Number || 0 }}处
      </DescItem>
      <DescItem align="left" label="二级动火作业">
        {{ data?.fire2Number || 0 }} 处
      </DescItem>
      <DescItem align="left" label="进入受限空间作业">
        {{ data?.spaceworkNumber || 0 }}处
      </DescItem>
      <DescItem align="left" label="盲板抽堵作业">
        {{ data?.blindplateNumber || 0 }}处
      </DescItem>
      <DescItem align="left" label="高处作业">
        {{ data?.highworkNumber || 0 }}处
      </DescItem>
      <DescItem align="left" label="吊装作业">
        {{ data?.liftingworkNumber || 0 }}处
      </DescItem>
      <DescItem align="left" label="临时用电作业">
        {{ data?.electricityworkNumber || 0 }} 处
      </DescItem>
      <DescItem align="left" label="动土作业">
        {{ data?.soilworkNumber || 0 }}处
      </DescItem>
      <DescItem align="left" label="断路作业">
        {{ data?.roadworkNumber || 0 }}处
      </DescItem>
      <DescItem align="left" label="检维修作业">
        {{ data?.inspectionNumber || 0 }}处
      </DescItem>
      <DescItem align="left" label="是否有承包商作业">
        {{ Number(data?.contractor) ? '是' : '否' }}
      </DescItem>
      <DescItem align="left" label="是否处于试生产期">
        {{ Number(data?.trialProduction) ? '是' : '否' }}
      </DescItem>
      <DescItem align="left" label="是否处于开停车状态">
        {{ Number(data?.openParking) ? '是' : '否' }}
      </DescItem>
      <DescItem align="left" label="是否开展中（扩）试">
        {{ Number(data?.test) ? '是' : '否' }}
      </DescItem>
    </Descs>

    <header-title2 class="py-10px">
      <span class="mr-20px">企业承诺</span>
    </header-title2>
    <div class="promise-box">
      <span class="pb-40px">
        今天我公司进行安全风险研判，各项安全风险防控措施已经落实到位，我承诺所有安全生产装置处于安全运行状态，罐区、仓库等重大危险源安全风险得到有效管控。
      </span>
      <span class="text-right">主要负责人：{{ data?.commitment }}</span>
      <span class="text-right">{{ $toDayjs(data?.commitDate).format('YYYY-MM-DD') }}</span>
    </div>
  </el-scrollbar>
</template>

<style scoped lang="scss">
  .detail-safe-promise {
  width: 100%;
}

.el-descriptions {
  --el-descriptions-table-border: 0;
  --el-descriptions-item-bordered-label-background: rgba(#fff, 0%);

  background: rgb(#292b2e, 30%);
}

:deep() .el-descriptions__label {
  font-size: 16px;
  color: #b8e5ff !important;
  text-align: right !important;
}

:deep().el-descriptions__content {
  --el-text-color-primary: #fff;

  font-size: 16px;
}

.promise-box {
  display: flex;
  flex-direction: column;
  min-height: 100px;
  padding: 20px;
  background: rgb(#292b2e, 30%);
}
</style>
