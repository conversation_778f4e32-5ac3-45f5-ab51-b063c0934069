<!-- 右侧企业安全承诺 -->
<script lang="ts" setup>
import type { EChartsOption } from 'echarts';

import simpleCityName from '@/assets/simple-city-name.json';
import { BAR_SERIES_OPTIONS } from '@/config/echartsTheme/presetConfig';
import {
  productionHazardousChemicalsStatisticByCommitmentUsingGet,
  productionHazardousChemicalsStatisticByKeyUsingPost,
} from '@/genapi/production';

defineOptions({ name: 'RightPromiseStat' });

const type = ref('1');

// 安全承诺排名
const { state: data } = useAsyncState(async () => {
  const { data } = await productionHazardousChemicalsStatisticByCommitmentUsingGet({});
  return data;
}, undefined);

const { state: data2 } = useAsyncState(async () => {
  const { data } = await productionHazardousChemicalsStatisticByKeyUsingPost({
    data: {
      key: '6',
    },
  });
  return data;
}, undefined);

const option = computed(() => {
  let list = +type.value === 2 ? data.value : data2.value;
  list
      = list?.map((item) => {
      return {
        ...item,
        text: simpleCityName[item.text!],
      };
    }) ?? [];

  return {
    grid: {
      top: '0',
      bottom: '0',
      left: '0',
      containLabel: true,
    },
    tooltip: {},
    xAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10,
      },
    },
    yAxis: {
      type: 'category',
      data: list?.map(e => e.text) ?? [],
      axisLabel: {
        fontSize: 10,
      },
    },
    series: [
      {
        ...BAR_SERIES_OPTIONS[0],
        type: 'bar',
        data: list?.map(e => (!e.value ? 0 : +e.value * 100)) ?? [],
      },
    ],
  } as EChartsOption;
});
</script>

<template>
  <el-radio-group v-model="type">
    <el-radio value="1">
      区域风险排名
    </el-radio>
    <el-radio value="2">
      安全承诺排名
    </el-radio>
  </el-radio-group>
  <div h="200px">
    <VEcharts :option="option" autoresize />
  </div>
</template>
