import type { Cartesian2SerializateJSON } from './cartesian2';

import type { Cartesian3SerializateJSON } from './cartesian3';
import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type { CornerTypeSerializateJSON, ShadowModeSerializateJSON } from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { Cartesian2Serializate } from './cartesian2';

import { Cartesian3Serializate } from './cartesian3';
import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PolylineVolumeGraphicsSerializateJSON {
  show?: boolean;
  positions?: Cartesian3SerializateJSON[];
  shape?: Cartesian2SerializateJSON[];
  cornerType?: CornerTypeSerializateJSON;
  granularity?: number;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
}

export type PolylineVolumeGraphicsKey = keyof PolylineVolumeGraphicsSerializateJSON;

export class PolylineVolumeGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.PolylineVolumeGraphics,
    omit?: PolylineVolumeGraphicsKey[],
    time?: Cesium.JulianDate,
  ): PolylineVolumeGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);
    return {
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map((item: Cesium.Cartesian3) =>
        Cartesian3Serializate.toJSON(item),
      ),
      shape: getValue('shape')?.map((item: Cesium.Cartesian2) =>
        Cartesian2Serializate.toJSON(item),
      ),
      cornerType: EnumSerializate.toJSON(Cesium.CornerType, getValue('cornerType')),
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
    };
  }

  static fromJSON(
    json?: PolylineVolumeGraphicsSerializateJSON,
    omit?: PolylineVolumeGraphicsKey[],
  ): Cesium.PolylineVolumeGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.PolylineVolumeGraphics({
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map(item => Cartesian3Serializate.fromJSON(item)!),
      shape: getValue('shape')?.map(item => Cartesian2Serializate.fromJSON(item)!),
      cornerType: EnumSerializate.fromJSON(Cesium.CornerType, getValue('cornerType')),
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
    });
  }
}
