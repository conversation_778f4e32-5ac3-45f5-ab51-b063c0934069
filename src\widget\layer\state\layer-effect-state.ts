import type { ImageryLayer } from 'cesium';
import type { LayerType } from '../utils/create-layer';
import type { LayerInfo } from '../utils/legacy';
import { isPromise, promiseTimeout } from '@x3d/all';
import { useCzImageryLayerCollection, useCzPrimitiveCollection, useCzViewer } from '@x3d/vue-hooks';
import { EllipsoidTerrainProvider } from 'cesium';
import { createLayer } from '../utils/create-layer';
import { useLayerState } from './layer-state';

interface LayerRecord {
  id: string;
  type: LayerType;
  layer: any;
  data: LayerInfo;
  remove: () => void;
}

export const useLayerEffectState = createGlobalState(() => {
  const viewer = useCzViewer();
  const { checkedIds, getTreeItemById } = useLayerState();

  viewer.value.imageryLayers.removeAll();

  // 图层列表及其对应的配置信息
  const layerInfoMap = new WeakMap<ImageryLayer, LayerInfo>();

  // id 图层映射
  const layerRecords: Record<string, LayerRecord> = {};

  const getLayerRecordById = (id: string) => layerRecords[id];

  const primitiveCollection = useCzPrimitiveCollection();
  const imageryLayerCollection = useCzImageryLayerCollection();

  watchArray(checkedIds, (_value, _oldValue, added, removed) => {
    // remove
    removed.forEach((id) => {
      layerRecords[id]?.remove?.();
      delete layerRecords[id];
    });

    // add
    added.forEach(async (id) => {
      const data = getTreeItemById(id);
      if (!data) {
        return;
      }
      const { type, value } = await createLayer(data) ?? {};
      if (!type) {
        return;
      }

      const layer = isPromise(value) ? await value : value;
      layerInfoMap.set(layer, data);
      switch (type) {
        case 'ImageryLayer':
          imageryLayerCollection.add(layer);
          layerRecords[id] = {
            id,
            type,
            layer,
            data,
            remove: () => imageryLayerCollection.remove(layer, true),
          };
          break;

        case 'Cesium3DTileset':
          await primitiveCollection.add(layer);
          layerRecords[id] = {
            id,
            type,
            layer,
            data,
            remove: () => primitiveCollection.remove(layer),
          };

          // if (layer.properties && layer.properties.Height) {
          //   layer.style = new Cesium.Cesium3DTileStyle({
          //     color: {
          //       conditions: [
          //         ['${Height} >= 20', 'color(\'green\')'],
          //         ['${Height} >= 6', 'color(\'yellow\')'],
          //         ['true', 'color(\'red\')'],
          //       ],
          //     },
          //     show: '${Height} > 0',
          //     meta: {
          //       description: '"Building id ${id} has height ${Height}."',
          //     },
          //   });
          // }
          // const selected = {
          //   feature: undefined,
          //   originalColor: new Cesium.Color(),
          // };

          // const clickHandler = viewer.value.screenSpaceEventHandler.getInputAction(
          //   Cesium.ScreenSpaceEventType.LEFT_CLICK,
          // );

          // if (Cesium.PostProcessStageLibrary.isSilhouetteSupported(viewer.value.scene)) {
          //   const silhouetteBlue
          //     = Cesium.PostProcessStageLibrary.createEdgeDetectionStage();
          //   silhouetteBlue.uniforms.color = Cesium.Color.BLUE;
          //   silhouetteBlue.uniforms.length = 0.01;
          //   silhouetteBlue.selected = [];

          //   const silhouetteGreen
          //     = Cesium.PostProcessStageLibrary.createEdgeDetectionStage();
          //   silhouetteGreen.uniforms.color = Cesium.Color.LIME;
          //   silhouetteGreen.uniforms.length = 0.01;
          //   silhouetteGreen.selected = [];

          //   viewer.value.scene.postProcessStages.add(
          //     Cesium.PostProcessStageLibrary.createSilhouetteStage([
          //       silhouetteBlue,
          //       silhouetteGreen,
          //     ]),
          //   );

          //   viewer.value.screenSpaceEventHandler.setInputAction((movement) => {
          //     silhouetteBlue.selected = [];
          //     const pickedFeature = viewer.value.scene.pick(movement.endPosition);
          //     if (!Cesium.defined(pickedFeature)) {
          //       return;
          //     }

          //     if (pickedFeature !== selected.feature) {
          //       silhouetteBlue.selected = [pickedFeature];
          //     }
          //   }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

          //   viewer.value.screenSpaceEventHandler.setInputAction((movement) => {
          //     silhouetteGreen.selected = [];

          //     const pickedFeature = viewer.value.scene.pick(movement.position);
          //     if (!Cesium.defined(pickedFeature)) {
          //       clickHandler(movement);
          //       return;
          //     }
          //     if (silhouetteGreen.selected[0] === pickedFeature) {
          //       return;
          //     }
          //     const highlightedFeature = silhouetteBlue.selected[0];
          //     if (pickedFeature === highlightedFeature) {
          //       silhouetteBlue.selected = [];
          //     }
          //     silhouetteGreen.selected = [pickedFeature];
          //   }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
          // }
          // else {
          //   const highlighted = {
          //     feature: undefined,
          //     originalColor: new Cesium.Color(),
          //   };

          //   viewer.value.screenSpaceEventHandler.setInputAction((movement) => {
          //     if (Cesium.defined(highlighted.feature)) {
          //       highlighted.feature.color = highlighted.originalColor;
          //       highlighted.feature = undefined;
          //     }
          //     const pickedFeature = viewer.value.scene.pick(movement.endPosition);
          //     updateNameOverlay(pickedFeature, movement.endPosition);

          //     if (!Cesium.defined(pickedFeature)) {
          //       return;
          //     }
          //     if (pickedFeature !== selected.feature) {
          //       highlighted.feature = pickedFeature;
          //       Cesium.Color.clone(pickedFeature.color, highlighted.originalColor);
          //       pickedFeature.color = Cesium.Color.YELLOW;
          //     }
          //   }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
          //   viewer.value.screenSpaceEventHandler.setInputAction((movement) => {
          //     if (Cesium.defined(selected.feature)) {
          //       selected.feature.color = selected.originalColor;
          //       selected.feature = undefined;
          //     }
          //     const pickedFeature = viewer.value.scene.pick(movement.position);
          //     if (!Cesium.defined(pickedFeature)) {
          //       clickHandler(movement);
          //       return;
          //     }
          //     if (selected.feature === pickedFeature) {
          //       return;
          //     }
          //     selected.feature = pickedFeature;
          //     if (pickedFeature === highlighted.feature) {
          //       Cesium.Color.clone(highlighted.originalColor, selected.originalColor);
          //       highlighted.feature = undefined;
          //     }
          //     else {
          //       Cesium.Color.clone(pickedFeature.color, selected.originalColor);
          //     }
          //     pickedFeature.color = Cesium.Color.LIME;
          //   }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
          // }

          break;
        case 'TerrainProvider':
          await promiseTimeout(1000);
          viewer.value.terrainProvider = layer;
          layerRecords[id] = {
            id,
            type,
            layer,
            data,
            remove: () => viewer.value.terrainProvider = new EllipsoidTerrainProvider(),
          };
          break;
        case 'GeoJson':
          viewer.value.dataSources.add(layer);
          layerRecords[id] = {
            id,
            type,
            layer,
            data,
            remove: () => viewer.value.dataSources.remove(layer, true),
          };
      }

      // 首次加载不跳转
      if (_oldValue?.length) {
        const bbox = data.config?.rectangle?.map(item => item);
        const camera = data.config?.camera ?? {};

        // 相机位置
        if (camera.longitude && camera.latitude && camera.height) {
          viewer.value.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(camera.longitude, camera.latitude, camera.height),
            orientation: {
              heading: Cesium.Math.toRadians(camera.heading ?? 0),
              pitch: Cesium.Math.toRadians(camera.pitch ?? 0),
              roll: Cesium.Math.toRadians(camera.roll ?? 0),
            },
          });
        }
        else if (bbox?.some(e => !!e) && bbox.length === 4) {
          viewer.value.camera.flyTo({
            destination: Cesium.Rectangle.fromDegrees(...bbox),
          });
        }
        else if (type === 'Cesium3DTileset') {
          viewer.value.flyTo(layer);
        }
      }
    });
  });

  return {
    getLayerRecordById,
    layerInfoMap,
  };
});
