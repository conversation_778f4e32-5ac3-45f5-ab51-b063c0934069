import type * as Cesium from 'cesium';

import type { BD09, Coordinate, GCJ02, WGS84 } from './types';
import gcoord from 'gcoord';
import { wgs84ToCartesian, wgs84ToCartographic, wgs84ToCoordinate } from './wgs84';

/**
 * GCJ02 转 Cartographic
 * @param position GCJ02
 * @returns Cartographic
 */
export function gcj02ToCartographic(position: GCJ02): Cesium.Cartographic {
  const wgs84 = gcj02ToWgs84(position);
  return wgs84ToCartographic(wgs84);
}

/**
 * GCJ02 转 Cartesian3
 * @param position GCJ02
 * @returns Cartesian3
 */
export function gcj02ToCartesian(position: GCJ02): Cesium.Cartesian3 {
  const wgs84 = gcj02ToWgs84(position);
  return wgs84ToCartesian(wgs84);
}

/**
 * GCJ02 转 Coordinate
 * @param position GCJ02
 * @returns Coordinate
 */
export function gcj02ToCoordinate(position: GCJ02, scene: Cesium.Scene): Coordinate {
  const wgs84 = gcj02ToWgs84(position);
  return wgs84ToCoordinate(wgs84, scene);
}

/**
 * GCJ02 转 WGS84
 * @param position GCJ02
 * @returns WGS84
 */
export function gcj02ToWgs84(position: GCJ02): WGS84 {
  const [x, y, height] = position;
  const [longitude, latitude] = gcoord.transform([x, y], gcoord.GCJ02, gcoord.WGS84);
  return [longitude, latitude, height];
}

/**
 * GCJ02 转 BD09
 * @param position GCJ02
 * @returns BD09
 */
export function gcj02ToBd09(position: GCJ02): BD09 {
  const [x, y, height] = position;
  const [longitude, latitude] = gcoord.transform([x, y], gcoord.GCJ02, gcoord.BD09);
  return [longitude, latitude, height];
}
