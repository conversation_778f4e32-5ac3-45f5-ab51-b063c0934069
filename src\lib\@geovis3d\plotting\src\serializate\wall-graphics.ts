import type { Cartesian3SerializateJSON } from './cartesian3';

import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type { ShadowModeSerializateJSON } from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { Cartesian3Serializate } from './cartesian3';

import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface WallGraphicsSerializateJSON {
  show?: boolean;
  positions?: Cartesian3SerializateJSON[];
  minimumHeights?: number[];
  maximumHeights?: number[];
  granularity?: number;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
}

export type WallGraphicsKey = keyof WallGraphicsSerializateJSON;

export class WallGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.WallGraphics,
    omit?: WallGraphicsKey[],
    time?: Cesium.JulianDate,
  ): WallGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map((item: Cesium.Cartesian3) =>
        Cartesian3Serializate.toJSON(item),
      ),
      minimumHeights: getValue('minimumHeights'),
      maximumHeights: getValue('maximumHeights'),
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
    };
  }

  static fromJSON(
    json?: WallGraphicsSerializateJSON,
    omit?: WallGraphicsKey[],
  ): Cesium.WallGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.WallGraphics({
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map(item => Cartesian3Serializate.fromJSON(item)!),
      minimumHeights: getValue('minimumHeights'),
      maximumHeights: getValue('maximumHeights'),
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
    });
  }
}
