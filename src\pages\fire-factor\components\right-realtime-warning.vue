<!-- 站点实时预警信息 -->
<script lang="ts" setup>
import { FIRE_FACTOR_BASE_URL, senseAlarmCtrGetAlarmLevelCnt, senseAlarmCtrGetAlarmList } from '../api';

defineOptions({ name: 'RightRealtimeWarning' });
const keyword = ref('');

const { state, execute, isLoading } = useAsyncState(
  async () => {
    const { data } = await senseAlarmCtrGetAlarmList({
      iskType: '',
      alarmLevel: '',
      areaIsn: '00000000.00000021.',
      areaRank: '2',
    });

    const result = (data ?? []) as any[];
    return result.sort((a, b) => +b.danger - +a.danger);
  },
  [],
  {
    immediate: true,
  },
);

watchThrottled([keyword], () => execute(), {
  throttle: 2000,
});

const risklevels = [
  {
    code: '1',
    label: '一级火险',
  },
  {
    code: '2',
    label: '二级火险',
  },
  {
    code: '3',
    label: '三级火险',
  },
  {
    code: '4',
    label: '四级火险',
  },
  {
    code: '5',
    label: '五级火险',
  },
];

const countStats = computedAsync(async () => {
  const { data } = await senseAlarmCtrGetAlarmLevelCnt({
    riskType: '',
    areaIsn: '00000000.00000021.',
    areaRank: '2',
  });
  return data;
});
</script>

<template>
  <header-title1>站点实时告警</header-title1>
  <el-divider m="y-10px!" />

  <div class="mx-10px" flex="~ justify-around" text="16px">
    <div flex="~ col items-center 1" b-r="1px #fff/10">
      <span text="18px">五级火险</span>
      <span>站点：{{ countStats?.filter((item:any) => +item.danger === 5 && item.riskType === 'station').length }}</span>
      <span>区域：{{ countStats?.filter((item:any) => +item.danger === 5 && item.riskType === 'area').length }}</span>
    </div>
    <div flex="~ col items-center 1" b-r="1px #fff/10">
      <span text="18px">四级火险</span>
      <span>站点：{{ countStats?.filter((item:any) => +item.danger === 4 && item.riskType === 'station').length }}</span>
      <span>区域：{{ countStats?.filter((item:any) => +item.danger === 4 && item.riskType === 'area').length }}</span>
    </div>
    <div flex="~ col items-center 1">
      <span text="18px">三级火险</span>
      <span>站点：{{ countStats?.filter((item:any) => +item.danger === 3 && item.riskType === 'station').length }}</span>
      <span>区域：{{ countStats?.filter((item:any) => +item.danger === 3 && item.riskType === 'area').length }}</span>
    </div>
  </div>
  <el-divider m="y-10px!" />
  <el-scrollbar v-loading="isLoading" wrap-class="p-10px" h="700px!">
    <div
      v-for="item in state"
      :key="item.id"
      flex="~ items-start"
      b="1px"
      b-color="[var(--el-border-color)]"
      p="10px"
      rd="6px"
      m="b-10px"

      cursor="pointer"
    >
      <div flex="~ col shrink-0 items-center" w="120px">
        <img :src="`${FIRE_FACTOR_BASE_URL}/ffmw/evecom/big-screen/style/images/fire/alarm_${item.danger}.png`" alt="">
        <span text="14px #ddd" p="10px">{{ item.riskType === 'area' ? '区域风险' : '站点风险' }}</span>
      </div>
      <div flex="~ col 1" text="14px" m="l-10px" of="hidden">
        <el-text v-if="item.riskType === 'area'" text="start! 16px!" font="bold" truncated self="start!">
          {{ `${item.areaFullName.replaceAll('/', ``) + risklevels.find(e => +e.code === +item.danger)?.label}预警` }}
        </el-text>
        <el-text v-else text="start! 16px!" font="bold" truncated self="start!">
          {{ `${item.name + risklevels.find(e => +e.code === +item.danger)?.label}预警` }}
        </el-text>
        <el-divider m="y-5px!" />
        <span v-if="item.riskType === 'area'">
          预计{{ $toDayjs(item.updatetime).format('MM月DD日HH时') }}至{{ $toDayjs(item.updatetime).add(1, 'd').format('MM月DD日HH时') }}
          {{ item.name }}森林火险等级将达到{{ risklevels.find(e => +e.code === +item.danger)?.label }}预警，请注意做好预防。</span>
        <span v-else>{{ $toDayjs(item.updatetime).format('MM月DD日HH时') }}{{ item.name }}森林火险等级将达到{{ risklevels.find(e => +e.code === +item.danger)?.label }}预警，请注意做好预防。</span>
      </div>
    </div>
  </el-scrollbar>
</template>
