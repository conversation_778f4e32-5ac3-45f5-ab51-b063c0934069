<!-- card1 -->
<script lang="ts" setup>
import type { VNodeChild } from 'vue';
import type { BasicOrComponentOpt } from '../basic-or-component/basic-or-component.vue';

export interface BasicCardProps {
  showClose?: boolean;
  showMenu?: boolean;
  title?: BasicOrComponentOpt;
  extra?: BasicOrComponentOpt;
  content?: BasicOrComponentOpt;
  footer?: BasicOrComponentOpt;
}

export interface BasicCardSlots {
  title?: VNodeChild;
  extra?: VNodeChild;
  default?: VNodeChild;
  footer?: VNodeChild;
}

export interface BasicCardEmits {
  (event: 'close',): void;
  (event: 'clickMenu',): void;
}

defineOptions({ name: 'BasicCard' });

const props = withDefaults(defineProps<BasicCardProps>(), {
  showClose: true,
});

const emit = defineEmits<BasicCardEmits>();
const slots = defineSlots<BasicCardSlots>();
</script>

<template>
  <div class="basic-card mb-5px" flex="~ col">
    <header-title1 b-b="1px! solid #fff/10%" flex="shrink-0">
      <basic-or-component :is="slots.title ?? props?.title" />
      <template #extra>
        <basic-or-component :is="slots.extra ?? props?.extra" />
        <el-button v-if="showMenu" link class="h-25px! p-0!" @click="$emit('clickMenu')">
          <el-icon class="i-material-symbols:more-vert" text="20px!" color="#fff" />
        </el-button>
        <el-button v-if="showClose" link class="h-25px! p-0!" @click="emit('close')">
          <el-icon class="i-material-symbols:close" text="20px! #fff!" />
        </el-button>
      </template>
    </header-title1>
    <el-scrollbar flex="1">
      <basic-or-component :is="slots?.default ?? props?.content" />
    </el-scrollbar>
    <div
      v-if="slots.footer ?? props?.footer"
      class="footer-container"
      p="24px"
      flex="~ justify-between"
    >
      <basic-or-component :is="slots?.footer ?? props?.footer" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.basic-card {
  box-sizing: border-box;
  background: var(--el-bg-color);
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 8px;
  opacity: 1;
}

.footer-container {
  border-top: 1px solid rgb(255 255 255 / 10%);
  box-shadow: 0 -6px 16px 0 rgb(0 0 0 / 40%);
}
</style>
