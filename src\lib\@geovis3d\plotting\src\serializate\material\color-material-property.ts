import type { MaterialPropertySerializateController } from './material-property';

import * as Cesium from 'cesium';

import { ColorSerializate } from '../color';

export interface ColorMaterialPropertySerializateJSON {
  color?: string;
}

export default <
  MaterialPropertySerializateController<
    'ColorMaterialProperty',
    Cesium.ColorMaterialProperty,
    ColorMaterialPropertySerializateJSON
  >
>{
  type: 'ColorMaterialProperty',
  hit: (property) => {
    return property instanceof Cesium.ColorMaterialProperty;
  },
  toJSON(property, time) {
    time ??= Cesium.JulianDate.now();
    const color = property?.getValue(time)?.color;
    return {
      type: 'ColorMaterialProperty',
      params: {
        color: ColorSerializate.toJSON(color),
      },
    };
  },
  fromJSON(json) {
    const color = json?.params?.color;
    return new Cesium.ColorMaterialProperty(ColorSerializate.fromJSON(color));
  },
};
