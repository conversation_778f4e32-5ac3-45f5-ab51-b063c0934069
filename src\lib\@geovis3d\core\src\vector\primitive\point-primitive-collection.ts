import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.PointPrimitiveCollection} 构造函数参数
 */
export type PointPrimitiveCollectionConstructorOptions = ConstructorParameters<
  typeof Cesium.PointPrimitiveCollection
>[0];

/**
 * {@link Cesium.PointPrimitiveCollection} 拓展用法与 {@link Cesium.PointPrimitiveCollection} 基本一致。
 *
 * `GcPointPrimitiveCollection.event`鼠标事件监听
 */
export class GcPointPrimitiveCollection extends Cesium.PointPrimitiveCollection {
  constructor(options?: PointPrimitiveCollectionConstructorOptions) {
    super(options);

    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
