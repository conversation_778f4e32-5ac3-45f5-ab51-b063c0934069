import { getGeovisAuthorization, getGeovisAuthorizationStr } from '@/service/geovisearth';

import * as Cesium from 'cesium';

const TDT_TOKEN = '703844650e1c2ea9ff6811e2036c959f';

export async function createImageryLayerOptions(): Promise<ImageryLayerOptions[]> {
  await getGeovisAuthorization();
  return [
    {
      label: '基础影像',
      cover: new URL('./cover/基础影像.png', import.meta.url).href,
      layer: new Cesium.ImageryLayer(
        new Cesium.UrlTemplateImageryProvider({
          url: `${import.meta.env.VITE_GEOVISEARTH_URL}/base/v1/img/{z}/{x}/{y}?format=webp&${getGeovisAuthorizationStr()}`,
          maximumLevel: 18,
        }),

        {},
      ),
    },
    {
      label: '矢量图',
      cover: new URL('./cover/矢量图.png', import.meta.url).href,
      layer: new Cesium.ImageryLayer(
        new Cesium.UrlTemplateImageryProvider({
          url: `${import.meta.env.VITE_GEOVISEARTH_URL}/base/v1/vec/{z}/{x}/{y}?format=webp&tmsIds=w&${getGeovisAuthorizationStr()}`,
          maximumLevel: 18,
        }),
        {},
      ),
    },
  ];
}

export async function createLabelLayerOptions(): Promise<ImageryLayerOptions[]> {
  await getGeovisAuthorization();
  return [
    {
      label: '标注',
      cover: new URL('./cover/标注.png', import.meta.url).href,
      layer: new Cesium.ImageryLayer(
        new Cesium.UrlTemplateImageryProvider({
          url: `${import.meta.env.VITE_GEOVISEARTH_URL}/base/v1/cia/{z}/{x}/{y}?format=webp&tmsIds=w&${getGeovisAuthorizationStr()}`,
          maximumLevel: 18,
        }),
        {},
      ),
    },
  ];
}
const data = ['202401', '202402', '202403', '202404', '202405', '202406', '202407', '202408', '202409', '202410', '202411', '202412'];

export function createHistoryLayerOptions(): ImageryLayerOptions[] {
  return data.map(item => ({
    label: `${item}`,
    cover: new URL('./cover/标注.png', import.meta.url).href,
    layer: new Cesium.ImageryLayer(
      new Cesium.WebMapTileServiceImageryProvider({
        url: `${import.meta.env.VITE_ICENTER_URL}/tilecache/service/wmts`,
        layer: `青岛市城阳区_${item}_影像`,
        style: 'default',
        format: 'image/PNG',
        tileMatrixSetID: 'EPSG:4326',
        tilingScheme: new Cesium.GeographicTilingScheme(),
        rectangle: new Cesium.Rectangle(
          2.101360130873131,
          0.6331263695419231,
          2.1017607137756276,
          0.6333083335758707,
        ),
      }),
      {},
    ),
  }));
}
