import type { components } from '@/genapi/cimapi';
import { encrypt } from '@/config/signature';
import { apiLogin, apiLogout, apiSsoLogin } from '@/genapi/auth';
import { systemSystemUserGetCurrUserInfoUsingGet } from '@/genapi/cimapi';
import qs from 'qs';
import VueCookies from 'vue-cookies';

export const useUserStore = defineStore('user', () => {
  const router = useRouter();

  const authorization = useLocalStorage('cim-authorization', '');
  const ssoInfo = useLocalStorage('sso-info', {
    client_id: '6bfa3a13bc2b4778b9047b87b0414c81',
    client_secret: 'f5c92935b4184c339260f88028d40f66',
  });

  // 当前是否已登录
  const isLogined = computed(() => {
    return !!authorization.value;
  });

  /**
   * 账号密码登录
   */
  const login = async (username: string, password: string) => {
    const { data, code } = await apiLogin({
      username: encrypt(username),
      password: encrypt(password),
    });
    authorization.value = data?.accessToken ?? '';
    return { data, code };
  };

  /**
   * sso登录
   */
  const ssoLogin = async (username: string) => {
    const { data, code } = await apiSsoLogin(
      { userName: username },
    );
    if (code) {
      authorization.value = data?.accessToken ?? '';
      const url = new URL(location.href);
      // 删除 URL 中的 code 和 state 参数
      url.searchParams.delete('code');
      url.searchParams.delete('state');
      window.location.href = new URL(`${url.origin}${import.meta.env.VITE_PUBLIC_PATH}`).toString();
    }
  };

  const logouting = ref(false);
  const logout = async () => {
    if (logouting.value) {
      return;
    }
    logouting.value = true;
    if (authorization.value) {
      try {
        await apiLogout();
      }
      catch (error) {
        console.error(error);
      }
    }

    if (import.meta.env.VITE_ENV !== 'HUBEI') {
      // 存在token是从中台跳转过来的 因此需要回到中台
      const access_token = VueCookies.get('access_token');
      if (access_token) {
        const ssoUrl = import.meta.env.VITE_SSO_URL; // SSO服务地址，从环境变量中获取
        const redirect_uri = import.meta.env.VITE_SSO_REDIRECT_URL;
        const allocation = {
          client_id: ssoInfo.value.client_id,
          response_type: 'code',
          access_token,
          redirect_uri,
        };
        VueCookies.remove('access_token');
        VueCookies.remove('refresh_token');
        authorization.value = '';
        location.replace(`${ssoUrl}/logout?${qs.stringify(allocation)}`);
        return;
      }
    }

    authorization.value = '';
    router.replace({ name: '/login/' });
    logouting.value = false;
  };

  const userInfo = shallowRef<components['schemas']['LoginUserInfoDTO']>();
  const userName = computed(() => {
    return userInfo.value?.systemUser?.userName;
  });

  const getUserInfo = async () => {
    const { code = -1, data = {} } = await systemSystemUserGetCurrUserInfoUsingGet({});
    if ([200].includes(code)) { // 预留
      // return;
    }
    userInfo.value = data;
  };

  watchImmediate(authorization, (authorization) => {
    if (authorization) {
      getUserInfo();
    }
  }, {
    flush: 'post',
  });

  watch(authorization, (authorization) => {
    if (!authorization) {
      userInfo.value = {};
      logout();
    }
  }, {
    flush: 'post',
  });

  const roleList = computed(() => {
    const roles = [...userInfo.value?.roleList ?? []];
    if (userInfo.value && !roles.length) {
      roles.push('system');
    }
    return roles;
  });

  return {
    authorization,
    ssoInfo,
    isLogined,
    login,
    logout,
    ssoLogin,
    userInfo,
    userName,
    roleList,
  };
});
