<!-- EllipsoidGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { EllipsoidGraphicsKey, EllipsoidGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { EllipsoidGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian3Attribute from './cartesian3-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'EllipsoidGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: EllipsoidGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.EllipsoidGraphics, EllipsoidGraphicsSerializateJSON>({
  graphic: () => props.entity?.ellipsoid,
  omit: props.omit,
  toJSON: (graphics, omit) => EllipsoidGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => EllipsoidGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="ellipsoid"
    graphics-field="show"
    label="可见"
  />
  <Cartesian3Attribute
    v-if="!hide?.includes('radii')"
    v-model="model.radii"
    graphics="ellipsoid"
    graphics-field="radii"
    label="radii"
  />
  <Cartesian3Attribute
    v-if="!hide?.includes('innerRadii')"
    v-model="model.innerRadii"
    graphics="ellipsoid"
    graphics-field="innerRadii"
    label="innerRadii"
  />
  <NumberAttribute
    v-if="!hide?.includes('minimumClock')"
    v-model="model.minimumClock"
    graphics="ellipsoid"
    graphics-field="minimumClock"
    label="最小时钟角度"
  />
  <NumberAttribute
    v-if="!hide?.includes('maximumClock')"
    v-model="model.maximumClock"
    graphics="ellipsoid"
    graphics-field="maximumClock"
    label="最大时钟角度"
  />
  <NumberAttribute
    v-if="!hide?.includes('minimumCone')"
    v-model="model.minimumCone"
    graphics="ellipsoid"
    graphics-field="minimumCone"
    label="最小圆锥角"
  />
  <NumberAttribute
    v-if="!hide?.includes('maximumCone')"
    v-model="model.maximumCone"
    graphics="ellipsoid"
    graphics-field="maximumCone"
    label="最大圆锥角"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="ellipsoid"
    graphics-field="heightReference"
    label="高度参照"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="ellipsoid"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="ellipsoid"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="ellipsoid"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="ellipsoid"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="ellipsoid"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('stackPartitions')"
    v-model="model.stackPartitions"
    graphics="ellipsoid"
    graphics-field="stackPartitions"
    label="垂向面数量"
  />
  <NumberAttribute
    v-if="!hide?.includes('slicePartitions')"
    v-model="model.slicePartitions"
    graphics="ellipsoid"
    graphics-field="slicePartitions"
    label="径向切片数量"
  />
  <NumberAttribute
    v-if="!hide?.includes('subdivisions')"
    v-model="model.subdivisions"
    graphics="ellipsoid"
    graphics-field="subdivisions"
    label="轮廓环采样数"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="ellipsoid"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="ellipsoid"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
