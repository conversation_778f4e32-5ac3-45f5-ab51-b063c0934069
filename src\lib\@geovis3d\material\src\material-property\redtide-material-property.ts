import * as Cesium from 'cesium';

import image from './assets/redtide.jpg';
import { getCesiumMaterialCache, setCesiumMaterialCache } from './material-cache';

/**
 * 赤潮
 */
export class RedtideMaterialProperty implements Cesium.MaterialProperty {
  constructor() {}

  static readonly MaterialType = 'RedtideMaterial';

  getType(_time?: Cesium.JulianDate) {
    return RedtideMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: any) {
    result ??= {};

    return result;
  }

  equals(other?: RedtideMaterialProperty) {
    return other instanceof RedtideMaterialProperty;
  }
}

const WaterMaterial = getCesiumMaterialCache('Water');

setCesiumMaterialCache(RedtideMaterialProperty.MaterialType, {
  ...WaterMaterial,
  fabric: {
    ...WaterMaterial.fabric,
    type: 'Redtide',
    uniforms: {
      ...WaterMaterial.fabric.uniforms,
      baseWaterColor: Cesium.Color.fromCssColorString(`rgba(161,57,61,0.8)`),
      blendColor: Cesium.Color.fromCssColorString(`rgba(65,19,29,0.8)`),
      normalMap: image,
      frequency: 500,
      animationSpeed: 0.01,
      amplitude: 1,
    },
  },
  translucent: () => true,
});
