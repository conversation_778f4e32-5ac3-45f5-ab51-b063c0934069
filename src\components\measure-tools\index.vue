<!-- 量测工具面板 -->
<script lang="ts" setup>
defineOptions({ name: 'Measure' });

const showMeasure = ref(true);
const activeName = ref('distance');
const measureDistanceRef = templateRef('measureDistanceRef');
const measureDistanceClampRef = templateRef('measureDistanceClampRef');
const measureAreaRef = templateRef('measureAreaRef');
const measureHeightRef = templateRef('measureHeightRef');
const measureAngleRef = templateRef('measureAngleRef');

function handleClick() {
  showMeasure.value = true;
  handleClear();
}
function handleClear() {
  measureDistanceClampRef.value?.initEntity();
  measureDistanceRef.value?.initEntity();
  measureAreaRef.value?.initEntity();
  measureHeightRef.value?.initEntity();
  measureAngleRef.value?.initEntity();
}
onUnmounted(() => {
  handleClear();
});
</script>

<template>
  <teleport to="#layout-content-right">
    <div
      class="measure-container"
      flex="~ items-center"
      m="b-0!"
      rd="8px"
      p="x-10px"
      w="400px!"
    >
      <el-tabs v-model="activeName" flex="1" @tab-click="handleClick">
        <el-tab-pane name="distance">
          <template #label>
            <el-icon class="i-custom:distance-simple" text="24px!" title="距离量测" />
          </template>
        </el-tab-pane>
        <el-tab-pane name="distanceClamp">
          <template #label>
            <el-icon class="i-custom:distance-simple" text="24px!" title="贴地距离" />
          </template>
        </el-tab-pane>
        <el-tab-pane name="area">
          <template #label>
            <el-icon class="i-custom:area-simple" text="24px!" title="面积量测" />
          </template>
        </el-tab-pane>
        <el-tab-pane name="height">
          <template #label>
            <el-icon class="i-custom:height-simple" text="24px!" title="高度量测" />
          </template>
        </el-tab-pane>
        <el-tab-pane name="angle">
          <template #label>
            <el-icon class="i-custom:angle-simple" text="24px!" title="角度量测" />
          </template>
        </el-tab-pane>
      </el-tabs>
      <el-divider direction="vertical" b-b="#7E8082" mx="8px!" />
      <el-button class="b-0! rd-4px! bg-#FF6363/10%!" un-hover="bg-#FF6363/25%!" @click="handleClear">
        <span text="#FF6363" flex="~ items-center">
          <el-icon class="i-custom:clear-simple" mr="4px" />清除
        </span>
      </el-button>
    </div>
    <div w="400px!" mt="20px">
      <measure-distance v-if="activeName === 'distance'" ref="measureDistanceRef" v-model="showMeasure" />
      <measure-distance-clamp v-if="activeName === 'distanceClamp'" ref="measureDistanceClampRef" v-model="showMeasure" />
      <measure-area v-if="activeName === 'area'" ref="measureAreaRef" v-model="showMeasure" />
      <measure-height v-if="activeName === 'height'" ref="measureHeightRef" v-model="showMeasure" />
      <measure-angle v-if="activeName === 'angle'" ref="measureAngleRef" v-model="showMeasure" />
    </div>
  </teleport>
</template>

<style lang="scss" scoped>
.measure-container {
  background: var(--el-bg-color);

  :deep().el-tabs__header {
    height: 52px;
    margin-bottom: 0;
  }

  :deep() .el-tabs {
    --el-border-color-light: rgb(0 0 0 / 0%);
    --el-color-primary: #4176ff;

    padding: 0;

    &__nav-wrap,
    &__nav-scroll {
      overflow: visible;
    }

    &__active-bar {
      z-index: -1;
      height: 36px !important;
      padding: 20px !important;
      margin-bottom: -4px !important;
      margin-left: -8px;
      border-radius: 4px;
    }
  }
}
</style>
