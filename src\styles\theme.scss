:root {
  --global-bg-color: #292b2e; /* 默认背景色 */
}

body {
  --el-color-primary: #5474fc;
  --el-bg-color: var(--global-bg-color); // #292b2e;
  --el-bg-color-overlay: var(--el-bg-color);
  --el-border-color: rgb(255 255 255 / 10%);
  --el-border-color-hover: rgb(255 255 255 / 30%);
  --el-input-focus-border-color: rgb(255 255 255 / 30%);
  --el-text-color-placeholder: #7e8082;
  --el-text-color-regular: #fff;
  --el-border-radius-base: 2px;
  --el-border: 1px var(--el-border-color) solid;
  --el-border-color-darker: rgb(255 255 255 / 10%);
  --el-border-color-light: rgb(255 255 255 / 30%);
  --el-fill-color-light: rgb(28 29 30 / 40%);
  --el-fill-color-lighter: rgb(28 29 30 / 80%);
  --el-border-color-extra-light: rgb(34 65 90);
  --el-fill-color: #097290cc;
  --el-color-info: #e2f4ff;

  .el-tabs {
    padding-top: 16px;

    --el-color-primary: #0dc6ff;
    --el-font-size-base: 16px;
    --el-border-color-light: #225c72;
    --el-tabs-header-height: 31px;

    .el-tabs__header {
      margin-bottom: 16px;
    }

    .el-tabs__item.is-active {
      --el-color-primary: #fff;
      --el-text-color-primary: #c7e8fc;
    }

    .el-tabs__active-bar {
      height: 4px;
      border-radius: 2px;
    }
  }

  .el-dialog {
    --el-dialog-margin-top: 0;

    top: 50%;
    border-radius: 20px;
    transform: translateY(-50%);

    --el-dialog-padding-primary: 0px;
    --el-dialog-bg-color: var(--global-bg-color); // #292b2e;
    --el-dialog-title-font-size: 22px;
    --el-message-close-size: 20px;

    &__header {
      --el-text-color-primary: #f3faff;

      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 32px;
      font-weight: 500;
      text-shadow: 0 2px 4px rgb(0 0 0 / 50%);
      border-bottom: 1px solid rgb(255 255 255 / 10%);
    }

    &__headerbtn {
      position: initial;
      width: initial;
      height: 20px;
      font-size: 22px;

      .el-dialog__close {
        --el-color-primary: rgb(255 255 255 / 50%);

        color: #fff;
      }
    }

    &__body {
      padding: 32px;
    }

    &__footer {
      padding: 25px 32px;
      border-top: 1px solid rgb(255 255 255 / 10%);
    }
  }

  .el-message-box {
    --el-messagebox-title-color: #f3faff;
    --el-messagebox-padding-primary: 0;
    --el-messagebox-bg-color: var(--global-bg-color); // #292b2e;
    --el-messagebox-font-size: 22px;
    --el-messagebox-content-font-size: 16px;
    --el-messagebox-border-radius: 20px;

    padding: 0;
    background-color: var(--el-messagebox-bg-color);

    &__header {
      display: flex;
      align-items: center;
      height: 54px;
      padding: 0 30px;
      font-weight: 500;
      text-shadow: 0 2px 4px rgb(0 0 0 / 50%);
      background: var(--el-messagebox-bg-color);
      border-bottom: 1px solid rgb(255 255 255 / 10%);
    }

    &__headerbtn {
      width: 54px;
      height: 54px;
      font-size: 22px;

      &__close {
        color: #fff;
      }
    }

    &__content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 80px;
      margin: 20px;
    }

    &__btns {
      margin: 20px;
    }
  }

  // .el-pagination {
  //   --el-pagination-button-bg-color: #072833;
  //   --el-disabled-bg-color: #07283381;

  //   .el-pager li,
  //   button {
  //     border: var(--el-color-primary) 1px solid;
  //   }
  // }

  // .el-table {
  //   font-size: 16px;

  //   --el-table-tr-bg-color: #042b384d;
  //   --el-table-header-bg-color: rgb(7 71 91 / 70%);
  //   --el-table-row-hover-bg-color: rgb(7 71 91 / 70%);
  //   --el-table-header-text-color: #b8e5ff;
  //   --el-table-border-color: rgb(22 73 84 / 70%);
  // }

  .el-table {
    font-size: 14px;
    color: #e8eaedb3;

    thead {
      tr {
        height: 44.85px !important;
      }
    }

    tbody {
      tr {
        height: 58px !important;

        .el-button {
          --el-color-primary: #1890ff;
          --el-button-hover-link-text-color: #1890ff; // --el-button-text-color: var(--el-color-primary);
        }
      }
    }

    --el-table-tr-bg-color: #17181a;
    --el-table-header-bg-color: #2f3238cc;
    --el-table-row-hover-bg-color: #2f3238;
    --el-table-header-text-color: #ffffff80;
    --el-table-border-color: transparent;
    --el-fill-color-lighter: #2f323880;
  }

  .el-pagination {
    --el-pagination-button-bg-color: #072833;
    --el-disabled-bg-color: #07283381;

    .el-pager li,
    button {
      border: var(--el-color-primary) 1px solid;
    }

    .el-select {
      width: 100px !important;
    }

    .btn-prev,
    .number,
    .btn-next,
    .more {
      border: none !important;
    }

    .is-active {
      border: 1px solid #4584ff !important;
    }
  }

  .el-input {
    --el-input-bg-color: #1c1d1e;
    --el-input-focus-border-color: rgb(255 255 255 / 30%);
  }

  .el-button {
    --el-primary-color: #4176ff;
    --el-button-hover-text-color: #fff;
    --el-button-hover-bg-color: rgb(255 255 255 / 4%);
    --el-button-border-color: rgb(255 255 255 / 10%);

    height: 36px;

    &--primary {
      --el-button-bg-color: var(--el-primary-color);
    }
  }

  .el-tree {
    --el-font-size-base: 15px;
    --el-tree-node-content-height: 36px;
    --el-tree-node-hover-bg-color: rgb(66 153 205 / 10%);

    .el-tree-node__content:hover {
      background: linear-gradient(270deg, rgb(28 29 30 / 40%) 0%, rgb(28 29 30 / 80%) 100%);
    }
  }

  .el-descriptions {
    --el-descriptions-table-border: 1px solid #ffffff60;

    // --el-descriptions-item-bordered-label-background: rgb(43 78 107 / 40%);

    // .el-descriptions__table {
    // table-layout: fixed;
    // }

    &__cell {
      font-size: var(--el-font-size-base);
    }

    .el-descriptions__cell.el-descriptions__label.is-bordered-label {
      text-align: center;
    }

    .el-descriptions--large .el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell {
      padding: 8px;
      text-align: 14px;
    }
  }

  .el-upload--picture-card {
    --el-fill-color-lighter: transparent;
    --el-upload-picture-card-size: 76px;
  }

  .el-upload-list--picture-card {
    --el-upload-list-picture-card-size: 76px;
  }

  .el-timeline {
    --el-timeline-node-color: #1bc3f2;

    .el-timeline-item__tail {
      border-color: var(--el-border-color-light);
    }

    .el-timeline-item__timestamp {
      font-size: 16px;
      color: #e2f4ff;
    }
  }

  .el-select-dropdown__item,
  .el-cascader-panel,
  .el-picker-panel {
    --el-color-primary: #00eaff;
  }

  .el-popover {
    &.el-popper {
      min-width: 120px;
    }
  }

  .el-checkbox {
    --el-checkbox-checked-bg-color: #4176ff;
    --el-checkbox-checked-input-border-color: #4176ff;
    --el-border: 1px var(--el-border-color-hover) solid;
  }

  .el-menu {
    --el-menu-item-font-size: 18px;
    --el-menu-border-color: transparent;
    --el-menu-active-color: #fff;
    --el-menu-hover-bg-color: #1c1d1e;

    .el-sub-menu {
      .el-sub-menu__title {
        padding: 0 14px;
        font-family: MiSans-Semibold, sans-serif;
        background-color: #1c1d1e;
        border-radius: 4px;
      }
    }

    .el-menu-item {
      --el-menu-hover-bg-color: rgb(137 169 255 / 20%);
    }
  }

  #distanceLegendDiv {
    position: relative;

    .distance-legend {
      position: initial;
      display: flex;
      justify-content: center;

      .distance-legend-label {
        display: inline-block;
        font-size: 14px;
      }

      .distance-legend-scale-bar {
        top: auto !important;
        bottom: 4px !important;
        left: auto !important;
      }
    }
  }

  .compass {
    position: static !important;
  }
}
