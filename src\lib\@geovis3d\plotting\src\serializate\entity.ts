import type { BillboardGraphicsSerializateJSON } from './billboard-graphics';

import type { BoxGraphicsSerializateJSON } from './box-graphics';
import type { Cesium3DTilesetGraphicsSerializateJSON } from './cesium-3d-tileset-graphics';
import type { CorridorGraphicsSerializateJSON } from './corridor-graphics';
import type { CylinderGraphicsSerializateJSON } from './cylinder-graphics';
import type { EllipseGraphicsSerializateJSON } from './ellipse-graphics';
import type { EllipsoidGraphicsSerializateJSON } from './ellipsoid-graphics';
import type { LabelGraphicsSerializateJSON } from './label-graphics';
import type { ModelGraphicsSerializateJSON } from './model-graphics';
import type { PathGraphicsSerializateJSON } from './path-graphics';
import type { PlaneGraphicsSerializateJSON } from './plane-graphics';
import type { PointGraphicsSerializateJSON } from './point-graphics';
import type { PolygonGraphicsSerializateJSON } from './polygon-graphics';
import type { PolylineGraphicsSerializateJSON } from './polyline-graphics';
import type { PolylineVolumeGraphicsSerializateJSON } from './polyline-volume-graphics';
import type { PropertyBagSerializateJSON } from './property-bag';
import type { QuaternionSerializateJSON } from './quaternion';
import type { RectangleGraphicsSerializateJSON } from './rectangle-graphics';
import type { TimeIntervalCollectionSerializateJSON } from './time-interval-collection';
import type { WallGraphicsSerializateJSON } from './wall-graphics';
import * as Cesium from 'cesium';
import { BillboardGraphicsSerializate } from './billboard-graphics';

import { BoxGraphicsSerializate } from './box-graphics';
import { Cesium3DTilesetGraphicsSerializate } from './cesium-3d-tileset-graphics';
import { CorridorGraphicsSerializate } from './corridor-graphics';
import { CylinderGraphicsSerializate } from './cylinder-graphics';
import { EllipseGraphicsSerializate } from './ellipse-graphics';
import { EllipsoidGraphicsSerializate } from './ellipsoid-graphics';
import { LabelGraphicsSerializate } from './label-graphics';
import { ModelGraphicsSerializate } from './model-graphics';
import { PathGraphicsSerializate } from './path-graphics';
import { PlaneGraphicsSerializate } from './plane-graphics';
import { PointGraphicsSerializate } from './point-graphics';
import { PolygonGraphicsSerializate } from './polygon-graphics';
import { PolylineGraphicsSerializate } from './polyline-graphics';
import { PolylineVolumeGraphicsSerializate } from './polyline-volume-graphics';
import { PropertyBagSerializate } from './property-bag';
import { QuaternionSerializate } from './quaternion';
import { RectangleGraphicsSerializate } from './rectangle-graphics';
import { TimeIntervalCollectionSerializate } from './time-interval-collection';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';
import { WallGraphicsSerializate } from './wall-graphics';

export interface EntitySerializateJSON {
  id?: string;
  name?: string;
  availability?: TimeIntervalCollectionSerializateJSON;
  show?: boolean;
  description?: string;
  position?: Cesium.PositionProperty | Cesium.Cartesian3;
  orientation?: QuaternionSerializateJSON;
  // viewFrom?: PropertySerializateJSON;
  // parent?: EntitySerializateJSON;
  billboard?: BillboardGraphicsSerializateJSON;
  box?: BoxGraphicsSerializateJSON;
  corridor?: CorridorGraphicsSerializateJSON;
  cylinder?: CylinderGraphicsSerializateJSON;
  ellipse?: EllipseGraphicsSerializateJSON;
  ellipsoid?: EllipsoidGraphicsSerializateJSON;
  label?: LabelGraphicsSerializateJSON;
  model?: ModelGraphicsSerializateJSON;
  tileset?: Cesium3DTilesetGraphicsSerializateJSON;
  path?: PathGraphicsSerializateJSON;
  plane?: PlaneGraphicsSerializateJSON;
  point?: PointGraphicsSerializateJSON;
  polygon?: PolygonGraphicsSerializateJSON;
  polyline?: PolylineGraphicsSerializateJSON;
  properties?: PropertyBagSerializateJSON;
  polylineVolume?: PolylineVolumeGraphicsSerializateJSON;
  rectangle?: RectangleGraphicsSerializateJSON;
  wall?: WallGraphicsSerializateJSON;
}

export type EntityKey = keyof EntitySerializateJSON;

export class EntitySerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.Entity,
    omit?: EntityKey[],
    time?: Cesium.JulianDate,
  ): EntitySerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      id: getValue('id'),
      name: getValue('name'),
      availability: TimeIntervalCollectionSerializate.toJSON(getValue('availability')),
      show: getValue('show') ?? true,
      description: getValue('description'),
      // position: Cesium.PositionProperty | Cesium.Cartesian3,
      orientation: QuaternionSerializate.toJSON(getValue('orientation')),
      // viewFrom: PropertySerializate.toJSON(getValue('viewFrom')),
      // parent: EntitySerializate.toJSON(getValue('parent')),
      billboard: BillboardGraphicsSerializate.toJSON(getValue('billboard')),
      box: BoxGraphicsSerializate.toJSON(getValue('box')),
      corridor: CorridorGraphicsSerializate.toJSON(getValue('corridor')),
      cylinder: CylinderGraphicsSerializate.toJSON(getValue('cylinder')),
      ellipse: EllipseGraphicsSerializate.toJSON(getValue('ellipse')),
      ellipsoid: EllipsoidGraphicsSerializate.toJSON(getValue('ellipsoid')),
      label: LabelGraphicsSerializate.toJSON(getValue('label')),
      model: ModelGraphicsSerializate.toJSON(getValue('model')),
      tileset: Cesium3DTilesetGraphicsSerializate.toJSON(getValue('tileset')),
      path: PathGraphicsSerializate.toJSON(getValue('path')),
      plane: PlaneGraphicsSerializate.toJSON(getValue('plane')),
      point: PointGraphicsSerializate.toJSON(getValue('point')),
      polygon: PolygonGraphicsSerializate.toJSON(getValue('polygon')),
      polyline: PolylineGraphicsSerializate.toJSON(getValue('polyline')),
      properties: PropertyBagSerializate.toJSON(getValue('properties')),
      polylineVolume: PolylineVolumeGraphicsSerializate.toJSON(getValue('polylineVolume')),
      rectangle: RectangleGraphicsSerializate.toJSON(getValue('rectangle')),
      wall: WallGraphicsSerializate.toJSON(getValue('wall')),
    };
  }

  static fromJSON(
    json?: EntitySerializateJSON,
    omit?: EntityKey[],
  ): Cesium.Entity.ConstructorOptions | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);
    return {
      id: getValue('id'),
      name: getValue('name'),
      availability: TimeIntervalCollectionSerializate.fromJSON(getValue('availability')),
      show: getValue('show') ?? true,
      description: getValue('description'),
      // position: Cesium.PositionProperty | Cesium.Cartesian3,
      orientation: QuaternionSerializate.fromJSON(getValue('orientation')) as any,
      // viewFrom: PropertySerializate.fromJSON(getValue('viewFrom')),
      // parent: EntitySerializate.fromJSON(getValue('parent')),
      billboard: BillboardGraphicsSerializate.fromJSON(getValue('billboard')),
      box: BoxGraphicsSerializate.fromJSON(getValue('box')),
      corridor: CorridorGraphicsSerializate.fromJSON(getValue('corridor')),
      cylinder: CylinderGraphicsSerializate.fromJSON(getValue('cylinder')),
      ellipse: EllipseGraphicsSerializate.fromJSON(getValue('ellipse')),
      ellipsoid: EllipsoidGraphicsSerializate.fromJSON(getValue('ellipsoid')),
      label: LabelGraphicsSerializate.fromJSON(getValue('label')),
      model: ModelGraphicsSerializate.fromJSON(getValue('model')),
      tileset: Cesium3DTilesetGraphicsSerializate.fromJSON(getValue('tileset')),
      path: PathGraphicsSerializate.fromJSON(getValue('path')),
      plane: PlaneGraphicsSerializate.fromJSON(getValue('plane')),
      point: PointGraphicsSerializate.fromJSON(getValue('point')),
      polygon: PolygonGraphicsSerializate.fromJSON(getValue('polygon')),
      polyline: PolylineGraphicsSerializate.fromJSON(getValue('polyline')),
      properties: PropertyBagSerializate.fromJSON(getValue('properties')),
      polylineVolume: PolylineVolumeGraphicsSerializate.fromJSON(getValue('polylineVolume')),
      rectangle: RectangleGraphicsSerializate.fromJSON(getValue('rectangle')),
      wall: WallGraphicsSerializate.fromJSON(getValue('wall')),
    };
  }
}
