<!-- TimeIntervalAttribute -->
<script lang="ts" setup>
import type { TimeIntervalSerializateJSON } from '@/lib/@geovis3d/plotting';
import { useShallowBinding } from './hooks';

defineOptions({ name: 'TimeIntervalAttribute' });

const props = defineProps<{
  modelValue?: TimeIntervalSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: TimeIntervalSerializateJSON): void;
}>();

const model = ref<TimeIntervalSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  intervals: TimeIntervalSerializateJSON[];
</template>
