<!-- 企业详情弹窗 -->
<script lang="ts" setup>
import { productionHazardousChemicalsCompanyDetailCompanyCodeUsingPost } from '@/genapi/production';
import { computedLoading } from '@/hooks/computed-loading';

import { useCompanyActiveInject } from '../hooks';
import DetailBasicInfo from './detail/detail-basic-info.vue';
import DetailRealTimeMonitor from './detail/detail-real-time-monitor.vue';
import DetailSafePromise from './detail/detail-safe-promise.vue';
import DetailVideo from './detail/detail-video.vue';

defineOptions({ name: 'CompanyDetailDialog' });

const companyActive = useCompanyActiveInject();

const companyCode = computed(() => {
  return companyActive.detailCompanyCode;
});
const [data, loading] = computedLoading(async () => {
  if (!companyCode.value)
    return;
  const { data } = await productionHazardousChemicalsCompanyDetailCompanyCodeUsingPost({
    path: {
      companyCode: companyCode.value!,
    },
  });
  return data;
});
const current = ref('基本信息');
</script>

<template>
  <DragCard
    v-if="companyCode"
    class="!w-1260px"
    :title="data?.companyName"
    show-close
    @close="companyActive.detailCompanyCode = undefined"
  >
    <el-tabs v-model="current" p="x-20px">
      <el-tab-pane name="基本信息">
        <template #label>
          <div class="tab-btn">
            <el-icon class="i-tabler:file-text" />
            <span>基本信息</span>
          </div>
        </template>
      </el-tab-pane>

      <el-tab-pane name="实时监测">
        <template #label>
          <div class="tab-btn">
            <el-icon class="i-tabler:graph-filled" />
            <span>实时监测</span>
          </div>
        </template>
      </el-tab-pane>

      <el-tab-pane name="安全承诺">
        <template #label>
          <div class="tab-btn">
            <el-icon class="i-material-symbols:health-and-safety" />
            <span>安全承诺</span>
          </div>
        </template>
      </el-tab-pane>

      <!-- <el-tab-pane name="视频监控">
        <template #label>
          <div class="tab-btn">
            <el-icon class="i-tabler:device-computer-camera" />
            <span>视频监控</span>
          </div>
        </template>
      </el-tab-pane> -->
    </el-tabs>
    <div h="550px">
      <DetailBasicInfo v-if="current === '基本信息' && data" :data="data" />
      <DetailRealTimeMonitor v-if="current === '实时监测' && companyCode" :company-code="companyCode" />
      <DetailSafePromise v-if="current === '安全承诺' && companyCode" :company-code="companyCode" />
      <DetailVideo v-if="current === '视频监控' && companyCode" :company-name="companyCode" />
    </div>
  </DragCard>
</template>

<style scoped lang="scss">
.tab-btn {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;

  .el-icon {
    padding-right: 10px;
  }
}
</style>
