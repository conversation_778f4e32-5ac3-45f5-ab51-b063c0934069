import { layerInfoGetLayerInfoUsingGet } from '@/genapi/cimapi';
import { useUserStore } from '@/stores/user';
import { legacy } from '../utils/legacy';

/**
 * 图层管理全局变量
 */
export const useLayerState = createGlobalState(() => {
  const userStore = useUserStore();

  const { state: rawTreeList, isLoading: treeListLoading, execute: refreshTreeList } = useAsyncState(
    async () => {
      if (userStore.isLogined && userStore.userInfo?.userId) {
        const { data = [] } = await layerInfoGetLayerInfoUsingGet({});
        return legacy(data);
      }
      else {
        return [];
      }
    },
    [],
    {
      immediate: false,
      resetOnExecute: false,
    },
  );

  watchImmediate(() => userStore.userInfo?.userId, () => refreshTreeList());

  const treeItemList = computed(() => {
    function flattenChildren(items: any[]): any[] {
      return items.reduce((acc, item) => {
        acc.push(item);
        if (item.children && item.children.length > 0) {
          acc.push(...flattenChildren(item.children));
        }
        return acc;
      }, []);
    }
    return flattenChildren(rawTreeList.value);
  });

  const getTreeItemById = (id: string) => treeItemList.value.find(item => item.id === id);

  // 选中图层id列表
  const checkedIds = ref<string[]>([]);

  const checkedItems = computed(() => treeItemList.value.filter(item => checkedIds.value.includes(item.id!)));

  // 图层刷新，剔除选中列表中已经删除的图层
  watch(treeItemList, () => {
    const defualtIds = treeItemList.value.filter(item => item.config?.defaultVisible).map(item => item.id!);
    checkedIds.value = [...new Set(defualtIds)];
  });

  return {
    /**
     * 图层树数据列表
     */
    rawTreeList,
    /**
     * 图层树数据加载状态
     */
    treeListLoading,
    /**
     * 刷新图层树数据列表
     */
    refreshTreeList,
    /**
     * 选中图层id列表
     */
    checkedIds,
    /**
     * 图层数据列表
     */
    treeItemList,
    /**
     * 选中图层列表
     */
    checkedItems,
    /**
     * 根据id获取图层数据
     */
    getTreeItemById,
  };
});
