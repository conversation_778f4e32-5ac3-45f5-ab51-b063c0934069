<!-- 安全承诺列表 -->
<script lang="ts" setup>
import type {
  ProductionHazardousChemicalsCommitmentListPageUsingPost,
} from '@/genapi/production';

import {
  productionHazardousChemicalsCommitmentListPageUsingPost,
} from '@/genapi/production';
import { usePage } from '@/hooks/use-page';
import dayjs from 'dayjs';
import { COMPANY_RISK_RANK_ENUM, COMPANY_RISK_RANK_ENUM_OPTION } from '../enum';

import DetailSafePromise from './detail/detail-safe-promise.vue';
import FullSreenArea from './full-sreen-area.vue';

defineOptions({ name: 'SafePromiseListModal' });

const query = reactive<NonNullable<ProductionHazardousChemicalsCommitmentListPageUsingPost.Body['query']>>({
  commitBeginTime: dayjs().format('YYYY-MM-DD 00:00:00'),
  commitEndTime: dayjs().format('YYYY-MM-DD 23:59:59'),
});

const datetime = computed<any>({
  get: () => [query.commitBeginTime, query.commitEndTime],
  set(value) {
    query.commitBeginTime = value?.[0];
    query.commitEndTime = value?.[1];
  },
});

const { records, execute, pageSize, currentPage, isLoading, total, startNo } = usePage({
  initPageSize: 30,
  async fetch({ currentPage, pageSize }) {
    const { data } = await productionHazardousChemicalsCommitmentListPageUsingPost({
      data: {
        size: pageSize,
        current: currentPage,
        query,
      },
    });
    return {
      records: data?.records ?? [],
      total: data?.total ?? 0,
    };
  },
});

watch(
  () => query,
  () => execute({ currentPage: 1 }),
  {
    flush: 'post',
    immediate: true,
    deep: true,
  },
);

const companyCode = ref<string>();
const detailVisible = ref(false);

watchEffect(() => {
  if (companyCode.value) {
    detailVisible.value = true;
  }
});
watchEffect(() => {
  if (!detailVisible.value) {
    companyCode.value = undefined;
  }
});
</script>

<template>
  <FullSreenArea :class="$style['sreen-area']">
    <el-form inline>
      <el-form-item label="企业名称" class="w-300px">
        <el-input v-model="query.companyName" clearable />
      </el-form-item>
      <el-form-item label="风险提示">
        <el-select-v2
          v-model="query.riskGrade"
          :options="COMPANY_RISK_RANK_ENUM_OPTION"
          clearable
        />
      </el-form-item>
      <el-form-item label="时间区间">
        <el-date-picker
          v-model="datetime"
          type="datetimerange"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="datetime.map((e) => new Date(e))"
          :disabled-date="(v) => $toDayjs(v).isAfter($toDayjs())"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :clearable="false"
        />
      </el-form-item>
    </el-form>
    <div class="table-wrapper">
      <el-table v-loading="isLoading" :data="records" stripe height="100%">
        <el-table-column
          type="index"
          :index="(index) => startNo + index"
          label="序号"
          align="center"
        />
        <!-- <el-table-column prop="riskGrade" lable="风险等级" /> -->
        <el-table-column prop="regionName" label="行政区划" align="center" />
        <el-table-column prop="companyName" label="企业名称" align="center" />
        <el-table-column prop="commitDate" label="承诺时间" align="center" />
        <el-table-column v-slot="{ row }" label="风险提示" align="center">
          {{ COMPANY_RISK_RANK_ENUM[row.riskGrade] }}
        </el-table-column>
        <el-table-column v-slot="{ row }" label="查看报告" align="center">
          <el-button @click="companyCode = row.companyCode">
            查看报告
          </el-button>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex justify-end pt-20px">
      <el-pagination v-model:page-size="pageSize" v-model:current-page="currentPage" :total="total" />
    </div>
  </FullSreenArea>
  <el-dialog v-model="detailVisible" append-to-body destroy-on-close>
    <DetailSafePromise :company-code="companyCode" />
  </el-dialog>
</template>

<style module>
  .sreen-area {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
}
</style>

<style scoped lang="scss">
  .table-wrapper {
  display: flex;
  flex: 1;
  overflow: hidden;
}
</style>
