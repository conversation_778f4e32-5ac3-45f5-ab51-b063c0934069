import type { Router } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { promiseTimeout } from '@x3d/all';
import axios from 'axios';
import qs from 'qs';
import VueCookies from 'vue-cookies';

/**
 * 单点登录路由拦截
 * @param router Vue Router 实例
 */
export function setupSSOToken(router: Router) {
  const ssoUrl = import.meta.env.VITE_SSO_URL; // SSO服务地址，从环境变量中获取
  const redirect_uri = import.meta.env.VITE_SSO_REDIRECT_URL;
  router.beforeEach(async (to, from, next) => {
    const url = new URL(location.href);
    const urlParams = new URLSearchParams(url.search);
    const code = urlParams.get('code'); // 从URL中获取code参数
    const state = urlParams.get('state'); // 从URL中获取code参数
    if (url.toString().includes('sso') || (code && state)) {
      const access_token = VueCookies.get('access_token');
      const userStore = useUserStore(); // 获取用户存储实例
      try {
        // 存在token 二次登录
        if (access_token) {
          await fetchUserInfo(userStore, ssoUrl, access_token);
        }
        else {
          // 存在code和state 且不存在token  就是sso授权回调
          if (code && state) {
            await handleSSOCode(code, userStore, ssoUrl, redirect_uri);
          }
          else {
            // 什么都没有 是首次登录 需要去sso授权
            redirectToSSOAuthorization(userStore, ssoUrl, redirect_uri);
          }
        }
      }
      catch (error: any) {
        console.error('Error during SSO token exchange or user info fetch:', error);
        // if ([401].includes(error.status)) {
        //   updateToken(userStore, ssoUrl);
        // }
        redirectToSSOAuthorization(userStore, ssoUrl, redirect_uri);
        // 清除 URL 中的 code 参数
        // const url = new URL(location.href);
        // url.searchParams.delete('code');
        // url.searchParams.delete('state');
        // url.searchParams.delete('usmaCookie');
        // history.replaceState({}, document.title, url.toString());
        // next('/login'); // 在发生错误时跳转到登录页
      }
    }
    else {
      next();
    }
  });

  async function handleSSOCode(code: string, userStore: any, ssoUrl: string, redirect_uri: string) {
    const form = new FormData();
    form.append('grant_type', 'authorization_code');
    form.append('client_id', userStore.ssoInfo.client_id);
    form.append('client_secret', userStore.ssoInfo.client_secret);
    form.append('redirect_uri', redirect_uri);
    form.append('code', code);
    /** 必须保留  要确保第三方服务器验证完成 所以等待200ms */
    await promiseTimeout(200);
    const tokenResponse = await axios.post(`${ssoUrl}/token`, form, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    VueCookies.set('access_token', tokenResponse.data.access_token, '0');
    VueCookies.set('refresh_token', tokenResponse.data.refresh_token, '0');
    return await fetchUserInfo(userStore, ssoUrl, tokenResponse.data.access_token);
  }

  async function fetchUserInfo(userStore: any, ssoUrl: string, access_token: string) {
    const userInfoResponse = await axios.get(`${ssoUrl}/getUserInfo?${qs.stringify({ client_id: userStore.ssoInfo.client_id })}`, {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    });

    const name = userInfoResponse.data.userDetail.name;
    if (name) {
      // 清除 URL 中的 code state参数
      const url = new URL(location.href);
      history.replaceState({}, document.title, url.host);
      await userStore.ssoLogin(name); // 接口会返回用户token 会跳转到home
    }
  }

  function redirectToSSOAuthorization(userStore: any, ssoUrl: string, redirect_uri: string) {
    const allocation = {
      client_id: userStore.ssoInfo.client_id,
      response_type: 'code',
      scope: 'all',
      state: Date.now(),
      redirect_uri,
    };
    location.replace(`${ssoUrl}/authorize?${qs.stringify(allocation)}`);
  }
}
