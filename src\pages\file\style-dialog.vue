<!-- 样式组件 -->
<script lang="ts" setup>
import type { DataSource } from 'cesium';

export interface StyleDialogProps {
  dataSource?: DataSource;
}

export interface StyleDialogEmits {
  (event: 'update:dataSource', data?: unknown): void;
}

defineOptions({ name: 'StyleDialog' });
const props = defineProps<StyleDialogProps>();
const emit = defineEmits<StyleDialogEmits>();

const data = ref({
  lineColor: Cesium.Color.TRANSPARENT?.toCssColorString(),
  lineWidth: 1,
  bgColor: Cesium.Color.TRANSPARENT?.toCssColorString(),
  outlineColor: Cesium.Color.TRANSPARENT?.toCssColorString(),
});

watchEffect(() => {
  if (props.dataSource) {
    const polyline = props.dataSource.entities.values.find(item => item.polyline)?.polyline;
    data.value.lineColor = (polyline?.material as any)?.color?.getValue()?.toCssColorString();
    data.value.lineWidth = polyline?.width?.getValue() ?? 1;
    const polygon = props.dataSource.entities.values.find(item => item.polygon)?.polygon;
    data.value.bgColor = (polygon?.material as any)?.color?.getValue()?.toCssColorString();
    data.value.outlineColor = polygon?.outlineColor?.getValue()?.toCssColorString();
  }
});

watchDeep(() => data.value, (data) => {
  props.dataSource?.entities.values.forEach((item) => {
    if (item.polyline) {
      item.polyline.material = new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString(data.lineColor));
      item.polyline.width = new Cesium.ConstantProperty(data.lineWidth);
    }
    if (item.polygon) {
      item.polygon.material = new Cesium.ColorMaterialProperty(Cesium.Color.fromCssColorString(data.bgColor));
      item.polygon.outlineColor = new Cesium.ConstantProperty(Cesium.Color.fromCssColorString(data.outlineColor));
    }
  });
}, { flush: 'post' });
</script>

<template>
  <drag-card
    v-if="dataSource"
    title="样式编辑"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    w="300px!"
    @close="emit('update:dataSource', undefined)"
  >
    <el-form p="10px" :label-width="$vh(80)">
      <el-form-item label="边框颜色">
        <el-color-picker v-model="data.outlineColor" show-alpha />
      </el-form-item>
      <el-form-item label="背景色">
        <el-color-picker v-model="data.bgColor" show-alpha />
      </el-form-item>
      <el-form-item label="线段颜色">
        <el-color-picker v-model="data.lineColor" show-alpha />
      </el-form-item>
      <el-form-item label="线段宽度">
        <el-input-number v-model="data.lineWidth" />
      </el-form-item>
    </el-form>
  </drag-card>
</template>
