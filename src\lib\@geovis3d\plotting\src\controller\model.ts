import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';
import { positionUpdate } from './utils/position-update';

/**
 * model 标绘配置
 */
export default <PlottingControllerOptions>{
  type: 'model',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 1,
  location: { visible: true },
  altitude: { visible: true },
  scale: { visible: true },
  rotationX: { visible: true },
  rotationY: { visible: true },
  rotationZ: { visible: true },
  scaleCallback(entity, scale) {
    const prev = entity.model!.scale?.getValue(Cesium.JulianDate.now()) ?? 1;
    entity.model!.scale = new Cesium.ConstantProperty(prev * scale);
  },
  update(entity) {
    if (!entity.model) {
      entity.model = new Cesium.ModelGraphics({});
    }
    positionUpdate(entity);
  },
};
