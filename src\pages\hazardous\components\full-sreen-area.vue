<!-- 全屏展示区域 -->
<script lang="ts" setup>
defineOptions({ name: 'FullSreenArea', inheritAttrs: false });
const isMounted = useMounted();
</script>

<template>
  <teleport v-if="isMounted" to="body">
    <div class="full-sreen-area" v-bind="$attrs">
      <slot />
    </div>
  </teleport>
</template>

<style scoped lang="scss">
  .full-sreen-area {
  position: absolute;
  inset: 0;
  z-index: 20;
  padding: 80px 30px 40px 120px;
  pointer-events: initial;
  background: rgb(#292b2e, 80%);
}
</style>
