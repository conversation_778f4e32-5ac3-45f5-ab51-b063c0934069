import type { MaybePromiseFn } from '@/lib/@geovis3d/shared';
import type { JulianDate } from 'cesium';
import { isPromise, throttle } from '@/lib/@geovis3d/shared';
import { CallbackProperty } from 'cesium';

/**
 * CallbackDelayProperty构造参数
 */
export interface CallbackDelayPropertyConstructorOptions {
  /**
   * 不跟随时间变化（只执行一次callback,后续都返回同一值）
   * @default true
   */
  isConstant?: boolean;

  /**
   * 节流延迟，单位毫秒
   * @default 0
   */
  ms?: number;

  /**
   * 是否执行最后一次触发
   * @default false
   */
  trailing?: boolean;

  /**
   * 是否执行第一次触发
   * @default true
   */
  leading?: boolean;
}

export type CallbackDelayPropertyFn<T> = MaybePromiseFn<[time: JulianDate], T>;

/**
 * 功能与 CallbackProperty 几乎一致，但可以传入Promise，且具有节流功能
 */
export class CallbackDelayProperty<T> extends CallbackProperty {
  constructor(
    callback: (time: JulianDate) => Promise<T> | T,
    initalValue: T,
    options: CallbackDelayPropertyConstructorOptions = {},
  ) {
    const { ms = 0, trailing = false, leading = true, isConstant = true } = options;

    let runed = false;
    const fn = throttle(
      async (time: JulianDate) => {
        if (isConstant && runed == true) {
          return this._promiseCache;
        }
        runed = true;
        const res = callback(time);
        this._promiseCache = isPromise(res) ? await res : res;
      },
      ms,
      trailing,
      leading,
    );

    super((time) => {
      fn(time);
      return this._promiseCache;
    }, options.isConstant ?? true);
    this._promiseCache = initalValue;
  }
}
