import { MaterialPropertySerializate } from './material-property';

export * from './chemical-material-property';
export * from './circle-wave-material-property';
export * from './color-material-property';
export * from './greentide-material-property';
export * from './image-gif-material-property';
export * from './image-material-property';
export * from './index';
export * from './material-property';
export * from './oil-material-property';
export * from './polyline-arrow-link-material-property';
export * from './polyline-attack-link-material-property';
export * from './polyline-pulse-link-material-property';
export * from './polyline-trail-link-material-property';
export * from './redtide-material-property';

// 自动载入
const glob = import.meta.glob('./*-material-property.ts', {
  eager: true,
  import: 'default',
});
Object.values(glob).forEach((serializate: any) => {
  MaterialPropertySerializate.addSerializate(serializate);
});
