<script lang="ts" setup>
import { usePage } from '@/hooks/use-page';
// 引入mockData作为模拟数据
import mockData from '../mockData.json';
// import { levelevaluationControllerGetPage, levelevaluationControllerGetTj } from '../api';
import LeftReportDialog from './left-report-dialog.vue';

defineOptions({ name: 'Left' });
const dateTimes = ref([

]);

const keyword = ref('');

const currentType = ref<string>();

const stats = [
  {
    name: '逐站点火险报告',
    type: '5',
  },
  {
    name: '行政区火险报告',
    type: '4',
  },
  {
    name: '精细化火险报告',
    type: '3',
  },
  {
    name: '可燃物干燥度指数报告',
    type: '2',
  },
];

// 根据新的mockData结构，获取统计数据
const statValues = computed(() => {
  // 从mockData中提取每种类型的记录数量
  return mockData.map(item => ({
    alarmLevel: item.type.toString(), // 确保类型匹配
    cnt: item.data.records.length,
  }));
});

// 根据当前选择的tab类型获取相应的数据集
const { records, execute, pageSize, currentPage, isLoading, total } = usePage({
  immediate: true,
  initPageSize: 50,
  async fetch() {
    // 获取当前选中类型对应的数据
    const typeData = currentType.value
      ? mockData.find(item => item.type.toString() === currentType.value)?.data
      : mockData[0]?.data;

    return {
      records: typeData?.records || [],
      total: typeData?.records?.length || 0,
    };
  },
});

watch(currentType, () => {
  // 仅监听类型变化，切换tab时刷新数据
  execute({ currentPage: 1 });
});

const leftReportDialogRef = templateRef('leftReportDialogRef');
</script>

<template>
  <div
    bg="[var(--el-bg-color)]"
    h="100%"
    of="y-hidden"
    p="t-60px b-40px"
    flex="~ col"
    w="400px!"
  >
    <header-title1>
      火险评估报告
    </header-title1>
    <div flex="~ justify-around">
      <div
        v-for="(item, index) in stats"
        :key="index"
        flex="~ col items-center"
        text="16px"
        cursor="pointer"
        :color="currentType === item.type ? '[var(--el-color-primary)]' : ''"
        @click="currentType = currentType === item.type ? undefined : item.type"
      >
        <span class="text-center">{{ item.name.slice(0, item.name.length - 4) }}</span>
        <span class="text-center">{{ item.name.slice(item.name.length - 4, item.name.length) }}</span>
        <el-text text="18px!" font="bold" type="primary">
          {{ statValues?.find((stat:any) => stat?.alarmLevel === item.type)?.cnt ?? '--' }}
        </el-text>
      </div>
    </div>
    <div flex="~ col" gap="y-10px" p="y-10px x-10px">
      <el-date-picker v-model="dateTimes" type="daterange" class="w-100%!" />
      <el-input v-model="keyword" placeholder="请输入关键字">
        <template #prefix>
          <el-icon class="i-tabler:search" />
        </template>
      </el-input>
    </div>
    <el-scrollbar v-loading="isLoading" wrap-class="px-10px">
      <div
        v-for="item in records"
        :key="item.id"
        b="1px"
        b-color="[var(--el-border-color)]"
        rd="6px"
        m="b-10px"
        p="10px"
        text="14px"
      >
        <el-text truncated text="18px!" font="bold">
          {{ item.name }}
        </el-text>
        <div flex="~ col">
          <span>报告类型：{{ stats.find(stat => stat.type === item.type)?.name }}</span>
          <span>评估范围：{{ item.areaname }}</span>
          <el-divider m="y-6px!" />
          <div flex="~ justify-between">
            <span>{{ item.reporttime }}</span>
            <el-button link type="primary" @click="leftReportDialogRef?.open(item)">
              报告预览>>
            </el-button>
          </div>
        </div>
      </div>
      <el-empty v-if="!isLoading && !records.length" />
    </el-scrollbar>
    <div class="flex justify-center pt-20px">
      <el-pagination v-model:page-size="pageSize" v-model:current-page="currentPage" :total="total" />
    </div>
    <LeftReportDialog ref="leftReportDialogRef" />
  </div>
</template>
