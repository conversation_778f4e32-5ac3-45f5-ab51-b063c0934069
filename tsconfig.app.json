{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "lib": ["ES2021", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["vite/client"]}, "include": ["shims.d.ts", "src/**/*", "scr/**/*.{vue,js,jsx,ts,tsx}"], "exclude": ["**/dist/**", "**/@geovis3d/**", "**/node_modules/**"]}