import { WebService, webServiceRequest } from '../service';

/**
 * 关键字搜索
 */
export function placeText(params) {
  return webServiceRequest({
    url: '/v3/place/text',
    params: {
      key: WebService.instance.key,
      ...params,
    },
  });
}

/**
 * 周边搜索
 */
export function placeAround(params) {
  return webServiceRequest({
    url: '/v3/place/around',
    params: {
      key: WebService.instance.key,
      ...params,
    },
  });
}

/**
 * 多边形搜索

 */
export function placePolygon(params) {
  return webServiceRequest({
    url: '/v3/place/polygon',
    params: {
      key: WebService.instance.key,
      ...params,
    },
  });
}

/**
 * ID查询
 */
export function placeDetail(params) {
  return webServiceRequest({
    url: '/v3place/detail',
    params: {
      key: WebService.instance.key,
      ...params,
    },
  });
}

/**
 * 驾车路径规划
 */
export function directionDriving(params) {
  return webServiceRequest({
    url: '/v3/direction/driving',
    params: {
      key: WebService.instance.key,
      ...params,
    },
  });
}

/**
 * 骑行路径规划
 */
export function directionBicycling(params) {
  return webServiceRequest({
    url: '/v4/direction/bicycling',
    params: {
      key: WebService.instance.key,
      ...params,
    },
  });
}
