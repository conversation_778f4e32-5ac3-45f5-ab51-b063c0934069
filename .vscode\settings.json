{

  "editor.quickSuggestions": {
    "strings": true
  },

  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "always",
    "source.fixAll.stylelint": "always"
  },

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off" },
    { "rule": "format/*", "severity": "off" },
    { "rule": "*-indent", "severity": "off" },
    { "rule": "*-spacing", "severity": "off" },
    { "rule": "*-spaces", "severity": "off" },
    { "rule": "*-order", "severity": "off" },
    { "rule": "*-dangle", "severity": "off" },
    { "rule": "*-newline", "severity": "off" },
    { "rule": "*quotes", "severity": "off" },
    { "rule": "*semi", "severity": "off" }
  ],

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml",
    "xml",
    "gql",
    "graphql",
    "astro",
    "css",
    "less",
    "scss",
    "pcss",
    "postcss"
  ],
  "stylelint.validate": ["vue", "css", "less", "postcss", "scss"],
  "typescript.preferences.autoImportFileExcludePatterns": ["vue-router/auto$"],
  "npm.packageManager": "pnpm",
  "iconify.inplace": false,
  "[svg]": {
    "editor.defaultFormatter": "jock.svg"
  },
  "svg.preview.background": "editor",
  "commentTranslate.multiLineMerge": true,
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "vue.server.hybridMode": true
}
