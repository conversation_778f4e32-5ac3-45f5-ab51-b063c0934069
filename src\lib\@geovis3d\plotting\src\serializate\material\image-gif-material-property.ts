import type { MaterialPropertySerializateController } from './material-property';
import { ImageGifMaterialProperty } from '@/lib/@geovis3d/material';

import * as Cesium from 'cesium';

export interface ImageGifMaterialPropertySerializateJSON {
  gif?: string;
}

export default <
  MaterialPropertySerializateController<
    'ImageGifMaterialProperty',
    ImageGifMaterialProperty,
    ImageGifMaterialPropertySerializateJSON
  >
>{
  type: 'ImageGifMaterialProperty',
  hit: (property) => {
    return property instanceof ImageGifMaterialProperty;
  },
  toJSON(property, time) {
    time ??= Cesium.JulianDate.now();
    const { gif } = property?.getValue(time) ?? {};
    return {
      type: 'ImageGifMaterialProperty',
      params: {
        gif,
        transparent: true,
      },
    };
  },
  fromJSON(json) {
    const { gif } = json?.params ?? {};
    return new ImageGifMaterialProperty({
      gif: gif!,
      transparent: true,
    });
  },
};
