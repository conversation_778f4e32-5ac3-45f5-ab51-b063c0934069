<svg width="40" height="41" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <filter x="-22.4%" y="-16%" width="144.9%" height="144.9%" filterUnits="objectBoundingBox" id="prefix__a">
            <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" in="shadowBlurOuter1" />
        </filter>
        <filter x="-38.9%" y="-27.8%" width="177.8%" height="177.8%" filterUnits="objectBoundingBox" id="prefix__c">
            <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 0.00372360947 0 0 0 0 0.0580977667 0 0 0 0 0.0786062047 0 0 0 0.5 0"
                in="shadowBlurOuter1" />
        </filter>
        <path id="prefix__b" d="M2.4 2.4h31.2v31.2H2.4z" />
        <path
            d="M26.948 17.735a.69.69 0 00-.15-.225l-1.92-2.206c-.27-.27-.866-.141-1.136.13a.692.692 0 000 .978l.49.63h-5.186V12.26l.542.325c.135.135.35.155.526.155.178 0 .51-.172.645-.307.27-.27.18-1.035-.09-1.306L18.49 9.202a.69.69 0 00-.754-.15.692.692 0 00-.226.15l-2.076 1.925c-.271.27-.339.932-.068 1.202.27.27.735.514 1.184.256l.496-.325v4.782h-5.277l.49-.63c.27-.27.27-.838 0-1.108-.271-.27-.874-.27-1.145 0L9.203 17.51a.694.694 0 000 .98l2.076 2.076a.69.69 0 00.98 0 .692.692 0 000-.979l-.49-.433h5.277v4.97l-.633-.383c-.271-.27-.833-.27-1.104 0-.27.27-.27.885 0 1.155l2.201 1.9a.69.69 0 00.755.15.692.692 0 00.225-.15l2.077-1.9c.27-.27.463-.884.192-1.155-.27-.27-.9-.27-1.171 0l-.542.384v-4.97h5.186l-.49.432a.692.692 0 00.49 1.182c.177 0 .598-.068.733-.203l1.833-2.076a.692.692 0 00.15-.755z"
            id="prefix__d" transform="translate(5 7)" />
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="prefix__e">
            <stop stop-color="#FFF" offset="0%" />
            <stop stop-color="#CCE7FF" offset="100%" />
        </linearGradient>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <g transform="translate(2 1)">
            <use fill="#000" filter="url(#prefix__a)" xlink:href="#prefix__b" />
            <use fill="#1465C9" xlink:href="#prefix__b" />
        </g>
        <g transform="translate(-3 -6)" fill-rule="nonzero">
            <use fill="#000" filter="url(#prefix__c)" xlink:href="#prefix__d" />
            <use fill="url(#prefix__e)" xlink:href="#prefix__d" />
        </g>
        <path stroke="#0085FF" stroke-width="2" d="M3 35v2M36 36h2M36 2h2M3 1v2" />
    </g>
</svg>