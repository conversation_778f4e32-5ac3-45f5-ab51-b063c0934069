export interface FontSerializateJSON {
  size?: number;
  family?: string;
  bold?: boolean;
  italic?: boolean;
}

export class FontSerializate {
  private constructor() {}
  static toJSON(data?: string): FontSerializateJSON | undefined {
    const [italic, bold, size, family] = data?.split('  ') ?? [];
    return {
      size: +size?.replace('px', '') || 12,
      family: family || 'SimHei',
      bold: bold === 'bold',
      italic: italic === 'italic',
    };
  }

  static fromJSON(json?: FontSerializateJSON): string | undefined {
    if (json) {
      const { size, family, bold, italic } = json;
      let res = `${italic ? 'italic' : 'normal'}  ${bold ? 'bold' : 'normal'}  ${
        Number(size) || 12
      }px`;
      if (family) {
        res += `  ${family || 'SimHei'}`;
      }
      return res;
    }
  }
}
