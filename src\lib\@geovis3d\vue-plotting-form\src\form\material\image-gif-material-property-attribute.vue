<!-- ImageGifMaterialPropertyAttribute 属性编辑 -->
<script lang="ts" setup>
import type { ImageGifMaterialPropertySerializateJSON } from '../@geovis3d/plotting';
import { useShallowBinding } from '../hooks';

import StringAttribute from '../string-attribute.vue';

defineOptions({ name: 'ImageGifMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: ImageGifMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: ImageGifMaterialPropertySerializateJSON): void;
}>();

const model = ref<ImageGifMaterialPropertySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="GIF" /> -->
  <StringAttribute v-model="model.gif" label="GIF地址" />
</template>
