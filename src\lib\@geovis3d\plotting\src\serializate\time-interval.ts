import type { JulianDateSerializateJSON } from './julian-date';

import * as Cesium from 'cesium';
import { JulianDateSerializate } from './julian-date';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface TimeIntervalSerializateJSON {
  start?: JulianDateSerializateJSON;
  stop?: JulianDateSerializateJSON;
  isStartIncluded?: boolean;
  isStopIncluded?: boolean;
  data?: any;
}

export type TimeIntervalKey = keyof TimeIntervalSerializateJSON;

export class TimeIntervalSerializate {
  private constructor() {}
  static toJSON(
    data?: Cesium.TimeInterval,
    time?: Cesium.JulianDate,
  ): TimeIntervalSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      start: JulianDateSerializate.toJSON(getValue('start')),
      stop: JulianDateSerializate.toJSON(getValue('stop')),
      isStartIncluded: getValue('isStartIncluded'),
      isStopIncluded: getValue('isStopIncluded'),
      data: getValue('data'),
    };
  }

  static fromJSON(json?: TimeIntervalSerializateJSON): Cesium.TimeInterval | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.TimeInterval({
      start: JulianDateSerializate.fromJSON(getValue('start')),
      stop: JulianDateSerializate.fromJSON(getValue('stop')),
      isStartIncluded: getValue('isStartIncluded'),
      isStopIncluded: getValue('isStopIncluded'),
      data: getValue('data'),
    });
  }
}
