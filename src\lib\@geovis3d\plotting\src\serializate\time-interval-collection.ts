import type { TimeIntervalSerializateJSON } from './time-interval';

import * as Cesium from 'cesium';

import { TimeIntervalSerializate } from './time-interval';

export interface TimeIntervalCollectionSerializateJSON {
  intervals: TimeIntervalSerializateJSON[];
}

export type TimeIntervalCollectionKey = keyof TimeIntervalCollectionSerializateJSON;

export class TimeIntervalCollectionSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.TimeIntervalCollection,
  ): TimeIntervalCollectionSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }

    return {
      intervals: Array.from({ length: data.length })
        .fill('')
        .map(item => TimeIntervalSerializate.toJSON(item)!)!,
    };
  }

  static fromJSON(
    json?: TimeIntervalCollectionSerializateJSON,
  ): Cesium.TimeIntervalCollection | undefined {
    if (!json) {
      return undefined;
    }
    return new Cesium.TimeIntervalCollection(
      json.intervals.map(item => TimeIntervalSerializate.fromJSON(item)!),
    );
  }
}
