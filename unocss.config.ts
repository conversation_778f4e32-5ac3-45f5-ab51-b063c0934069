import { fileURLToPath } from 'node:url';

import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetUno,
  transformerAttributifyJsx,
} from 'unocss';

import { generateIconCollection } from './internals/svg-icon';

const customIconPath = fileURLToPath(new URL('./icons', import.meta.url));

export default defineConfig({
  transformers: [transformerAttributifyJsx()],
  presets: [
    presetUno({}),
    presetAttributify({
      prefix: 'un-',
    }),
    presetIcons({
      collections: {
        custom: generateIconCollection(customIconPath, { multiColor: true, varPrefix: 'custom' }),
      },
    }),
  ],
  rules: [
    ['text-semibold', { 'font-family': 'MiSans-Semibold' }],
  ],
  shortcuts: [
    {
      'font-normal-md': 'font-normal text-18px',
      'font-normal-sm': 'font-normal text-16px',
      'font-normal-sx': 'font-normal text-14px',
    },
    [/^font-blod-(.*)$/, ([, c]) => `text-semibold font-size-${c}`],
    [/^plain-(.*)$/, ([, c]) => `b-color-${c}! bg-${c}/15%! color-${c}! hover-bg-${c} hover-color-#fff`],
  ],
});
