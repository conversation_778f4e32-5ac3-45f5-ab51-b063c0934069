<!-- 弹窗 -->
<script lang="ts" setup>
import type { Cartesian3 } from 'cesium';
import type { VNodeChild } from 'vue';

import type { BasicOrComponentOpt } from '../basic-or-component/basic-or-component.vue';
import { cartesianToCanvascoord, cartesianToCartographic, cartographicToCartesian } from '@x3d/all';
import { useCzEventListener, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'LocatedPopper', inheritAttrs: false });

const props = withDefaults(
  defineProps<LocatedPopper1Props>(),
  {
    clampToGround: true,
    to: '.cesium-widget',
  },
);

const slots = defineSlots<LocatedPopper1Slots>();

export interface LocatedPopper1Props {
  position?: Cartesian3;
  to?: any;
  /** 是否贴地 */
  clampToGround?: boolean;
  content?: BasicOrComponentOpt;
}
export interface LocatedPopper1Emits {
  (event: 'close',): void;
}

export interface LocatedPopper1Slots {
  default?: VNodeChild;
}

const viewer = useCzViewer();
const popperRef = shallowRef<HTMLElement>();

function computer() {
  if (!popperRef.value) {
    return;
  }
  const style = popperRef.value!.style!;
  // 不传入点位  则将窗口居中
  if (!props.position) {
    style.top = `${(viewer.value?.canvas.offsetHeight / 2).toFixed(2)}px`;
    style.left = `${(viewer.value?.canvas.offsetWidth / 2).toFixed(2)}px`;
    return;
  }

  let position = props.position.clone();

  if (props.clampToGround) {
    // 如果贴地 则每次渲染都要去获取高度
    // 高度保留两位小数防窗口抖动
    const cartographic = cartesianToCartographic(position);
    const height = viewer.value?.scene.globe.getHeight(cartographic) || 0;

    if (height > 0) {
      cartographic.height = +height.toFixed(2);
    }
    position = cartographicToCartesian(cartographic);
  }

  const { x, y } = cartesianToCanvascoord(props.position, viewer.value.scene) ?? {};
  if (x && y) {
    style.top = `${y.toFixed(2)}px`;
    style.left = `${x.toFixed(2)}px`;
  }
}
useCzEventListener(
  () => viewer.value.scene.postRender,
  () => computer(),
);
</script>

<template>
  <teleport :to="to">
    <div
      ref="popperRef"
      v-bind="$attrs"
      class="located-popper"
      position="absolute"
    >
      <basic-or-component :is="slots.default ?? props.content" />
    </div>
  </teleport>
</template>
