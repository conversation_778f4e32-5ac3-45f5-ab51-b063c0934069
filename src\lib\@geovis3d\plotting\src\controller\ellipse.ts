import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';

/**
 * ellipse标绘配置 圆形扩散波
 */
export default <PlottingControllerOptions>{
  type: 'ellipse',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
  location: { visible: true },
  // altitude: { visible: true },
  control: { visible: true },
  update(entity) {
    if (!entity.ellipse) {
      entity.ellipse = new Cesium.EllipseGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    if (positions.length === 0) {
      return;
    }
    if (positions.length === 1) {
      const position = entity.plotting.mousePosition;
      position && positions.push(position);
    }
    if (positions.length < 2) {
      return;
    }
    entity.position = new Cesium.ConstantPositionProperty(positions[0]);
    const radius = Cesium.Cartesian3.distance(positions[0], positions[1]);
    entity.ellipse!.semiMinorAxis = new Cesium.CallbackProperty(() => radius || 1, false);
    entity.ellipse!.semiMajorAxis = entity.ellipse!.semiMinorAxis;
  },
};
