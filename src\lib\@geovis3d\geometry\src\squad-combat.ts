import type { Coord, Feature, LineString } from '@turf/turf';

import { lineString } from '@turf/turf';
import { getArrowBodyPoints, getArrowHeadPoints } from './attack-arrow';
import { getBaseLength, getBatchCoords, getQBSplinePoints, getThirdPoint, HALF_PI } from './common';

export interface SquadCombatOptions {
  headHeightFactor?: number;
  headWidthFactor?: number;
  neckHeightFactor?: number;
  neckWidthFactor?: number;
  headTailFactor?: number;
  tailWidthFactor?: number;
}
/**
 * 分队战斗行动
 */
export function squadCombat(points: Coord[], options?: SquadCombatOptions): Feature<LineString> {
  if (points.length < 2) {
    throw new Error('points.length must >=2');
  }
  const headHeightFactor = options?.headHeightFactor ?? 0.18;
  const headWidthFactor = options?.headWidthFactor ?? 0.3;
  const neckHeightFactor = options?.neckHeightFactor ?? 0.85;
  const neckWidthFactor = options?.neckWidthFactor ?? 0.15;
  const tailWidthFactor = options?.tailWidthFactor ?? 0.1;
  const headTailFactor = 0.8;
  const _options = {
    headHeightFactor,
    headWidthFactor,
    neckHeightFactor,
    neckWidthFactor,
    tailWidthFactor,
    headTailFactor,
  };
  function getTailPoints(points: Coord[]) {
    const allLen = getBaseLength(points);
    const tailWidth = allLen * tailWidthFactor;
    const tailLeft = getThirdPoint(points[1], points[0], HALF_PI, tailWidth, false);
    const tailRight = getThirdPoint(points[1], points[0], HALF_PI, tailWidth, true);
    return [tailLeft, tailRight];
  }

  const tailCoords = getTailPoints(points);
  const headCoords = getArrowHeadPoints(points, tailCoords[0], tailCoords[1], _options);
  const neckLeft = headCoords[0];
  const neckRight = headCoords[4];
  const bodyCoords = getArrowBodyPoints(points, neckLeft, neckRight, tailWidthFactor);
  const count = bodyCoords.length;
  let leftCoords = [tailCoords[0]].concat(bodyCoords.slice(0, count / 2));
  leftCoords.push(neckLeft);
  let rightCoords = [tailCoords[1]].concat(bodyCoords.slice(count / 2, count));
  rightCoords.push(neckRight);
  leftCoords = getQBSplinePoints(leftCoords);
  rightCoords = getQBSplinePoints(rightCoords);
  const coords = [...leftCoords, ...headCoords, ...rightCoords.reverse()];
  return lineString(getBatchCoords(coords));
}
