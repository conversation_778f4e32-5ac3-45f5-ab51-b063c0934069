<!-- ModelGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { ModelGraphicsKey, ModelGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { ModelGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian2Attribute from './cartesian2-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import ColorBlendModeAttribute from './color-blend-mode-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';
import StringAttribute from './string-attribute.vue';

defineOptions({ name: 'ModelGraphicsGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: ModelGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.ModelGraphics, ModelGraphicsSerializateJSON>({
  graphic: () => props.entity?.model,
  omit: props.omit,
  toJSON: (graphics, omit) => ModelGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => ModelGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="model"
    graphics-field="show"
    label="可见"
  />
  <StringAttribute
    v-if="!hide?.includes('uri')"
    v-model="model.uri"
    graphics="model"
    graphics-field="uri"
    label="资源路径"
  />
  <NumberAttribute
    v-if="!hide?.includes('scale')"
    v-model="model.scale"
    graphics="model"
    graphics-field="scale"
    :precision="2"
    label="缩放"
  />
  <NumberAttribute
    v-if="!hide?.includes('minimumPixelSize')"
    v-model="model.minimumPixelSize"
    graphics="model"
    graphics-field="minimumPixelSize"
    label="最小像素"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('maximumScale')"
    v-model="model.maximumScale"
    graphics="model"
    graphics-field="maximumScale"
    label="最小缩放"
    :precision="2"
  />
  <BooleanAttribute
    v-if="!hide?.includes('incrementallyLoadTextures')"
    v-model="model.incrementallyLoadTextures"
    graphics="model"
    graphics-field="incrementallyLoadTextures"
    label="纹理保持"
  />
  <BooleanAttribute
    v-if="!hide?.includes('runAnimations')"
    v-model="model.runAnimations"
    graphics="model"
    graphics-field="runAnimations"
    label="动画"
  />
  <BooleanAttribute
    v-if="!hide?.includes('clampAnimations')"
    v-model="model.clampAnimations"
    graphics="model"
    graphics-field="clampAnimations"
    label="姿势保持"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="model"
    graphics-field="shadows"
    label="阴影"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="model"
    graphics-field="heightReference"
    label="高度参照"
  />
  <ColorAttribute
    v-if="!hide?.includes('silhouetteColor')"
    v-model="model.silhouetteColor"
    graphics="model"
    graphics-field="silhouetteColor"
    label="剪影颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('silhouetteSize')"
    v-model="model.silhouetteSize"
    graphics="model"
    graphics-field="silhouetteSize"
    label="剪影大小"
  />
  <ColorAttribute
    v-if="!hide?.includes('color')"
    v-model="model.color"
    graphics="model"
    graphics-field="color"
    label="颜色"
  />
  <ColorBlendModeAttribute
    v-if="!hide?.includes('colorBlendMode')"
    v-model="model.colorBlendMode"
    graphics="model"
    graphics-field="colorBlendMode"
    label="颜色混合方式"
  />
  <NumberAttribute
    v-if="!hide?.includes('colorBlendAmount')"
    v-model="model.colorBlendAmount"
    graphics="model"
    graphics-field="colorBlendAmount"
    label="颜色混合强度"
  />
  <Cartesian2Attribute
    v-if="!hide?.includes('imageBasedLightingFactor')"
    v-model="model.imageBasedLightingFactor"
    graphics="model"
    graphics-field="imageBasedLightingFactor"
    label="反射比值"
  />
  <ColorAttribute
    v-if="!hide?.includes('lightColor')"
    v-model="model.lightColor"
    graphics="model"
    graphics-field="lightColor"
    label="灯光颜色"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="model"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
