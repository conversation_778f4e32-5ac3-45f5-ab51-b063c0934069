import type { BD09, Coordinate, GCJ02, WGS84 } from './types';
import * as Cesium from 'cesium';

import gcoord from 'gcoord';

import { gcj02ToBd09 } from './gcj02';

/**
 * WGS84 转 Cartographic
 * @param position WGS84
 * @returns Cartographic
 */
export function wgs84ToCartographic(position: WGS84): Cesium.Cartographic {
  return new Cesium.Cartographic(
    Cesium.Math.toRadians(position[0]),
    Cesium.Math.toRadians(position[1]),
    position[2],
  );
}

/**
 * WGS84 转 Cartesian3
 * @param position WGS84
 * @returns Cartesian3
 */
export function wgs84ToCartesian(position: WGS84): Cesium.Cartesian3 {
  return Cesium.Cartesian3.fromDegrees(position[0], position[1], position[2]);
}

/**
 * WGS84 转 Coordinate
 * @param position WGS84
 * @returns Coordinate
 */
export function wgs84ToCoordinate(position: WGS84, scene: Cesium.Scene): Coordinate {
  const cartographic = wgs84ToCartographic(position);
  return scene.cartesianToCanvasCoordinates(
    Cesium.Ellipsoid.WGS84.cartographicToCartesian(cartographic),
  );
}

/**
 * WGS84 转 GCJ02
 * @param position WGS84
 * @returns GCJ02
 */
export function wgs84ToGcj02(position: WGS84): GCJ02 {
  const [x, y, height] = position;
  const [longitude, latitude] = gcoord.transform([x, y], gcoord.WGS84, gcoord.GCJ02);
  return [longitude, latitude, height];
}

/**
 * WGS84 转 BD09
 * @param position WGS84
 * @returns BD09
 */
export function wgs84ToBd09(position: WGS84): BD09 {
  const gcj02 = wgs84ToGcj02(position);
  return gcj02ToBd09(gcj02);
}
