import type { Cartesian2SerializateJSON } from './cartesian2';
import type { ColorSerializateJSON } from './color';

import { ParticleGraphics } from '@/lib/@geovis3d/core';
import * as Cesium from 'cesium';
import { Cartesian2Serializate } from './cartesian2';

import { ColorSerializate } from './color';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface ParticleGraphicsSerializateJSON {
  show?: boolean;
  // emitter?: Cesium.Property | Cesium.ParticleEmitter;
  // modelMatrix?: Cesium.Property | Cesium.Matrix4;
  // emitterModelMatrix?: Cesium.Property | Cesium.Matrix4;
  emissionRate?: number;
  // bursts?: Cesium.Property | Cesium.ParticleBurst[];
  loop?: boolean;
  scale?: number;
  startScale?: number;
  endScale?: number;
  color?: ColorSerializateJSON;
  startColor?: ColorSerializateJSON;
  endColor?: ColorSerializateJSON;
  image?: string;
  imageSize?: Cartesian2SerializateJSON;
  minimumImageSize?: Cartesian2SerializateJSON;
  maximumImageSize?: Cartesian2SerializateJSON;
  sizeInMeters?: boolean;
  speed?: number;
  minimumSpeed?: number;
  maximumSpeed?: number;
  lifetime?: number;
  particleLife?: number;
  minimumParticleLife?: number;
  maximumParticleLife?: number;
  mass?: number;
  minimumMass?: number;
  maximumMass?: number;
}

export type ParticleGraphicsKey = keyof ParticleGraphicsSerializateJSON;

export class ParticleGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: ParticleGraphics,
    omit?: ParticleGraphicsKey[],
    time?: Cesium.JulianDate,
  ): ParticleGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show'),
      // emitter?: Cesium.Property | Cesium.ParticleEmitter;
      // modelMatrix?: Cesium.Property | Cesium.Matrix4;
      // emitterModelMatrix?: Cesium.Property | Cesium.Matrix4;
      emissionRate: getValue('emissionRate'),
      // bursts?: Cesium.Property | Cesium.ParticleBurst[];
      loop: getValue('loop'),
      scale: getValue('scale'),
      startScale: getValue('startScale'),
      endScale: getValue('endScale'),
      color: ColorSerializate.toJSON(getValue('color')),
      startColor: ColorSerializate.toJSON(getValue('startColor')),
      endColor: ColorSerializate.toJSON(getValue('endColor')),
      image: getValue('image'),
      imageSize: Cartesian2Serializate.toJSON(getValue('imageSize')),
      minimumImageSize: Cartesian2Serializate.toJSON(getValue('minimumImageSize')),
      maximumImageSize: Cartesian2Serializate.toJSON(getValue('maximumImageSize')),
      sizeInMeters: getValue('sizeInMeters'),
      speed: getValue('speed'),
      minimumSpeed: getValue('minimumSpeed'),
      maximumSpeed: getValue('maximumSpeed'),
      lifetime: getValue('lifetime'),
      particleLife: getValue('particleLife'),
      minimumParticleLife: getValue('minimumParticleLife'),
      maximumParticleLife: getValue('maximumParticleLife'),
      mass: getValue('mass'),
      minimumMass: getValue('minimumMass'),
      maximumMass: getValue('maximumMass'),
    };
  }

  static fromJSON(
    json?: ParticleGraphicsSerializateJSON,
    omit?: ParticleGraphicsKey[],
  ): ParticleGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new ParticleGraphics({
      show: getValue('show'),
      // emitter?: Cesium.Property | Cesium.ParticleEmitter;
      // modelMatrix?: Cesium.Property | Cesium.Matrix4;
      // emitterModelMatrix?: Cesium.Property | Cesium.Matrix4;
      emissionRate: getValue('emissionRate'),
      // bursts?: Cesium.Property | Cesium.ParticleBurst[];
      loop: getValue('loop'),
      scale: getValue('scale'),
      startScale: getValue('startScale'),
      endScale: getValue('endScale'),
      color: ColorSerializate.fromJSON(getValue('color')),
      startColor: ColorSerializate.fromJSON(getValue('startColor')),
      endColor: ColorSerializate.fromJSON(getValue('endColor')),
      image: getValue('image'),
      imageSize: Cartesian2Serializate.fromJSON(getValue('imageSize')),
      minimumImageSize: Cartesian2Serializate.fromJSON(getValue('minimumImageSize')),
      maximumImageSize: Cartesian2Serializate.fromJSON(getValue('maximumImageSize')),
      sizeInMeters: getValue('sizeInMeters'),
      speed: getValue('speed'),
      minimumSpeed: getValue('minimumSpeed'),
      maximumSpeed: getValue('maximumSpeed'),
      lifetime: getValue('lifetime'),
      particleLife: getValue('particleLife'),
      minimumParticleLife: getValue('minimumParticleLife'),
      maximumParticleLife: getValue('maximumParticleLife'),
      mass: getValue('mass'),
      minimumMass: getValue('minimumMass'),
      maximumMass: getValue('maximumMass'),
    });
  }
}
