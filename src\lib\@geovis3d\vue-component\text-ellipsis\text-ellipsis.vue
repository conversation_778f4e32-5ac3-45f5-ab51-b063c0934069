<!--
文本省略组件
如果父级为flex，可能会使组件失效，可以尝试将父级设置overflow-x: hidden;
 -->
<script lang="ts" setup>
import type { ElTooltipProps } from 'element-plus';

import { computed } from 'vue-demi';
import { ResolveVnode } from '../resolve-vnode/resolve-vnode';

defineOptions({ name: 'TextEllipsis', inheritAttrs: false });

const props = withDefaults(
  defineProps<{
    /** 是否展开 */
    expand?: boolean;

    /** 至多显示几行 */
    lineClamp?: number;

    /** 是否显示tooltip */
    tooltip?: boolean;

    /** tooltip配置项  */
    tooltipOptions?: ElTooltipProps;
  }>(),
  {
    expand: false,
    lineClamp: 1,
    tooltip: false,
  },
);

const line = computed(() => {
  return !props.expand ? String(props.lineClamp) : undefined;
});
</script>

<template>
  <ElTooltip v-if="tooltip && !expand" v-bind="tooltipOptions">
    <span
      v-bind="$attrs"
      class="text-ellipsis"
      :class="{
        'text-ellipsis--line-clamp': !props.expand && props.lineClamp > 1,
      }"
    >
      <slot />
    </span>
    <template #content>
      <slot>
        <ResolveVnode :render="tooltipOptions?.content" />
      </slot>
    </template>
  </ElTooltip>
  <span
    v-else
    v-bind="$attrs"
    class="text-ellipsis"
    :class="{
      'text-ellipsis--line-clamp': !props.expand && props.lineClamp > 1,
      'expand': props.expand,
    }"
  >
    <slot />
  </span>
</template>

<style lang="scss">
.text-ellipsis {
  overflow: hidden;

  &.text-ellipsis--line-clamp {
    display: -webkit-box;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: v-bind('line');
  }

  &:not(.text-ellipsis--line-clamp) {
    &:not(.expand) {
      display: inline-block;
      max-width: 100%;
      text-overflow: ellipsis;
      vertical-align: bottom;
      white-space: nowrap;
    }
  }
}
</style>
