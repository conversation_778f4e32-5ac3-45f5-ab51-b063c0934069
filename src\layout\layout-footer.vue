<script lang="ts" setup>
import type { WGS84Coord } from '@x3d/all';
import type { Cartographic } from 'cesium';
import { useNavigation } from '@/hooks/use-navigation';
import { canvasCoordToCartesian, cartesianToWgs84 } from '@x3d/all';
import {
  useCzEventListener,
  useCzScreenSpaceAction,
  useCzThrottleFn,
  useCzViewer,
} from '@x3d/vue-hooks';
import { Math as CMath, defined, ScreenSpaceEventType } from 'cesium';

defineOptions({ name: 'LayoutFooter' });
const viewer = useCzViewer();
const navigation = useNavigation();
const distanceLegendRef = ref<HTMLDivElement>();
const cameraPosition = ref<Cartographic>(viewer.value.camera.positionCartographic);
const mousePosition = ref<WGS84Coord | undefined>();
const tileLevel = ref<number>(getTileLevel());
const fps = ref<number>();
const progress = ref<number>();

const source = computed(() => {
  const { longitude, latitude, height } = cameraPosition.value;
  return JSON.stringify({
    经度: `${CMath.toDegrees(longitude).toFixed(2)}°`,
    纬度: `${CMath.toDegrees(latitude).toFixed(2)}°`,
    高度: `${(height / 1000).toFixed(2)}km`,
    偏航角: `${CMath.toDegrees(viewer.value.camera.heading).toFixed(1)}°`,
    俯仰角: `${CMath.toDegrees(viewer.value.camera.pitch).toFixed(2)}°`,
    滚动角: `${CMath.toDegrees(viewer.value.camera.roll).toFixed(2)}°`,
  });
});
const { copy, copied, isSupported } = useClipboard({ source });
function getTileLevel(): number {
  const tiles = new Set<number>();
  const tilesToRender = viewer.value.scene.globe._surface?._tilesToRender;
  if (defined(tilesToRender)) {
    for (let i = 0; i < tilesToRender.length; i++) {
      tiles.add(tilesToRender[i].level);
    }
  }
  return [...tiles]?.[0] || 0;
}
let lastFpsSampleTime = 0;
let ticks = 0;
useCzEventListener(
  () => viewer.value.scene.postUpdate,
  () => {
    ticks++;
    const now = Date.now();
    const fpsElapsedTime = now - lastFpsSampleTime;
    if (fpsElapsedTime > 1000) {
      fps.value = ((ticks * 1000) / fpsElapsedTime) | 0;
      lastFpsSampleTime = now;
      ticks = 0;
      tileLevel.value = getTileLevel();
    }
  },
);
const getMouse = useCzThrottleFn(
  (endPosition) => {
    const car3 = canvasCoordToCartesian(endPosition, viewer.value.scene, 'globePick');
    mousePosition.value = car3 ? cartesianToWgs84(car3) : undefined;
    cameraPosition.value = viewer.value.camera.positionCartographic;
  },
  { ms: 500 },
);

useCzScreenSpaceAction(ScreenSpaceEventType.MOUSE_MOVE, ({ endPosition }) => {
  getMouse(endPosition);
});
onMounted(() => {
  distanceLegendRef.value && navigation.append('distanceLegend', distanceLegendRef.value);

  const helper = new Cesium.EventHelper();
  let max = 0;
  helper.add(viewer.value.scene.globe.tileLoadProgressEvent, (e) => {
    max = Math.max(max, e);
    progress.value = Math.round((100 * (max - e)) / max);
    if (e === 0) {
      progress.value = 100;
    }
  });
});
</script>

<template>
  <div class="layout-footer">
    <el-progress
      type="circle"
      :percentage="progress"
      :width="$vh(18)"
      :stroke-width="2"
      :status="progress === 100 ? 'success' : ''"
    />
    <div whitespace-nowrap flex="1">
      <el-button link>
        数据
      </el-button>
      <el-divider direction="vertical" />
      <el-button link>
        中心点
      </el-button>
      <span pr="12px"> {{ CMath.toDegrees(cameraPosition.longitude).toFixed(2) }}° </span>
      <el-divider direction="vertical" />
      <span px="12px"> {{ CMath.toDegrees(cameraPosition.latitude).toFixed(2) }}° </span>
      <el-divider direction="vertical" />
      <span px="12px"> {{ (cameraPosition.height / 1000).toFixed(2) }}km </span>
      <el-divider direction="vertical" />
      <span px="12px">
        偏航角: {{ CMath.toDegrees(viewer.camera.heading).toFixed(1) }}°
      </span>
      <el-divider direction="vertical" />
      <span px="12px">
        俯仰角: {{ CMath.toDegrees(viewer.camera.pitch).toFixed(1) }}°
      </span>
      <el-divider direction="vertical" />
      <span px="12px">
        滚转角:{{ CMath.toDegrees(viewer.camera.roll).toFixed(1) }}°
      </span>
      <el-button
        v-if="isSupported"
        class="h-24px! b-0! rd-4px! bg-#fff/10%! px-8px! hover:bg-#fff/30%!"
        @click="copy()"
      >
        <el-icon class="i-custom:copy" />
        {{ copied ? "已复制" : "复制结果" }}
      </el-button>
    </div>
    <div flex="~  items-center justify-start">
      <div ref="distanceLegendRef" />
      <div flex="~ justify-center" w="80px">
        层级 <span> {{ tileLevel }} </span>
      </div>
      <el-divider direction="vertical" />
      <div flex="~ justify-center" w="120px">
        经度
        <span> {{ (mousePosition?.[0] || 0).toFixed(2) }}° </span>
      </div>
      <el-divider direction="vertical" />
      <div flex="~ justify-center" w="110px">
        纬度
        <span> {{ (mousePosition?.[1] || 0).toFixed(2) }}° </span>
      </div>
      <el-divider direction="vertical" />
      <div flex="~ justify-center" w="80px">
        FPS <span> {{ fps }} </span>
      </div>
      <el-divider direction="vertical" />
      <div flex="~ justify-center" w="150px">
        相机 <span> {{ (cameraPosition.height / 1000).toFixed(2) }}km </span>
      </div>
      <el-divider direction="vertical" />
      <div flex="~ justify-center" w="140px">
        海拔 <span> {{ (mousePosition?.[2] || 0).toFixed(2) }} </span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.layout-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  font-size: 14px;
  line-height: 32px;
  pointer-events: all;
  background: var(--el-bg-color); // #292b2e;
  :deep() .el-progress {
    &__text {
      width: 100%;
      min-width: initial;
      font-size: 12px !important;
      font-weight: 700;
    }
  }

  .el-button {
    --el-color-primary: #fff;
    --el-font-size-base: 16px;
    --el-fill-color-light: rgb(255 255 255 / 20%);

    padding: 0 12px;
  }
}

.el-divider {
  margin: 0 2px !important;
  border-color: #7e8082 !important;
}
</style>
