import type { Units } from '@turf/turf';

import type { FeatureCollection, GeoJsonProperties, GeometryCollection, Point } from 'geojson';
import * as Cesium from 'cesium';
import ContourDataSourceWorker from './contour-data-source.worker?worker';

type Longitude = number;
type Latitude = number;
type Value = number;

export type ContourData = [Longitude, Latitude, Value][];

export interface ContourDataSourceLoadOptions {
  /**
   * turf.interpolate参数
   */
  interpolate: {
    points: FeatureCollection<Point, GeoJsonProperties>;
    cellSize: number;
    options?: {
      property?: string;
      units?: Units;
      weight?: number;
    };
  };
  /**
   * turf.isobands 参数
   */
  isobands: {
    /**
     * 色阶图数值，与`breaksProperties`数值一一对应，必须与`breaks`的数值个数一致
     */
    breaks: number[];
    options?: {

      /**
       * 指定数据源中的哪个属性作为z值
       * @defalutValue 'value'
       */
      zProperty?: string;

      /**
       * @example
       * ```
       * {"fill-opacity": 0.8}
       * ```
       */
      commonProperties?: GeoJsonProperties;

      /**
       * 色阶图颜色表，与`breaks`数值一一对应，必须与`breaks`的数值个数一致
       * @example
       * ```
       * [
       *  {fill: "#e3e3ff"},
       *  {fill: "#c6c6ff"},
       *  {fill: "#a9aaff"},
       *  {fill: "#8e8eff"},
       *  {fill: "#7171ff"},
       *  {fill: "#5554ff"},
       *  {fill: "#3939ff"},
       *  {fill: "#1b1cff"}
       *  ]
       * ```
       */
      breaksProperties?: GeoJsonProperties[];
    };
  };
  /**
   * 边界裁剪
   */
  surface: GeometryCollection;
  /**
   * turf.simplify 边界简化,加快计算速度
   *
   */
  simplify?: {
    tolerance?: number;
    highQuality?: boolean;
    mutate?: boolean;
  };
}
/**
 * 等高面图
 */
export class ContourDataSource extends Cesium.GeoJsonDataSource {
  constructor(name: string) {
    super(name);
  }

  static async load(options: ContourDataSourceLoadOptions) {
    return new Promise<Cesium.GeoJsonDataSource>((resolve, reject) => {
      const worker = new ContourDataSourceWorker();
      worker.postMessage(options);
      worker.addEventListener('error', reject, { once: true });
      worker.addEventListener('message', async ({ data }) => {
        const dataSource = await new Cesium.GeoJsonDataSource(this.name).load(
          data,
          {
            clampToGround: true,
            stroke: Cesium.Color.TRANSPARENT,
          },
        );
        resolve(dataSource);
      }, { once: true });
    });
  }

  override load(options: ContourDataSourceLoadOptions) {
    return new Promise<Cesium.GeoJsonDataSource>((resolve) => {
      const worker = new ContourDataSourceWorker();
      worker.postMessage(options);
      worker.addEventListener('message', async ({ data }) => {
        const dataSource = await super.load(
          data,
          {
            stroke: Cesium.Color.TRANSPARENT,
          },
        );
        resolve(dataSource);
      }, { once: true });
    });
  }
}
