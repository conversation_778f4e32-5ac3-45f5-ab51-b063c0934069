import type { Coord, Feature, LineString, Point } from '@turf/turf';

import { getCoord, lineString, midpoint, point } from '@turf/turf';
import {
  getAngleOfThreePoints,
  getBaseLength,
  getBatchCoords,
  getQBSplinePoints,
  getThirdPoint,
  HALF_PI,
  isClockWise,
  mathDistance,
  wholeDistance,
} from './common';

export interface AttackArrowOptions {
  headHeightFactor: number;
  headWidthFactor: number;
  neckHeightFactor: number;
  neckWidthFactor: number;
  headTailFactor: number;
}

/**
 * 平尾攻击箭头 至少需要三个点
 */
export function attackArrow(points: Coord[], options?: AttackArrowOptions): Feature<LineString> {
  if (points.length < 3) {
    throw new Error('points.length must >=3');
  }

  const headHeightFactor = options?.headHeightFactor ?? 0.18;
  const headWidthFactor = options?.headWidthFactor ?? 0.3;
  const neckHeightFactor = options?.neckHeightFactor ?? 0.85;
  const neckWidthFactor = options?.neckWidthFactor ?? 0.15;
  const headTailFactor = options?.headTailFactor ?? 0.8;

  options = {
    headHeightFactor,
    headWidthFactor,
    neckHeightFactor,
    neckWidthFactor,
    headTailFactor,
  };

  const clockWise = isClockWise(points[0], points[1], points[2]);
  const tailLeft = getCoord(clockWise ? points[1] : points[0]);
  const tailRight = getCoord(clockWise ? points[0] : points[1]);

  const midTail = midpoint(tailLeft, tailRight).geometry.coordinates;
  const boneCoords = [midTail, ...getBatchCoords(points.slice(2))];
  const headCoords = getArrowHeadPoints(boneCoords, tailLeft, tailRight, options).map(
    e => e.geometry.coordinates,
  );
  const neckLeft = headCoords[0];
  const neckRight = headCoords[4];

  const tailWidthFactor = mathDistance(tailLeft, tailRight) / getBaseLength(boneCoords);
  const bodyCoords = getArrowBodyPoints(boneCoords, neckLeft, neckRight, tailWidthFactor).map(
    e => e.geometry.coordinates,
  );
  const count = bodyCoords.length;
  let leftCoords = [tailLeft].concat(bodyCoords.slice(0, count / 2));
  leftCoords.push(neckLeft);
  let rightCoords = [tailRight].concat(bodyCoords.slice(count / 2, count));
  rightCoords.push(neckRight);
  leftCoords = getQBSplinePoints(leftCoords).map(e => e.geometry.coordinates);
  rightCoords = getQBSplinePoints(rightCoords).map(e => e.geometry.coordinates);

  const coords = [...leftCoords, ...headCoords, ...rightCoords.reverse()];
  return lineString(coords);
}

/**
 * 插值头部点
 * @param points

 */
export function getArrowHeadPoints(
  points: Coord[],
  tailLeft: Coord,
  tailRight: Coord,
  options: AttackArrowOptions,
): Feature<Point>[] {
  let len = getBaseLength(points);
  let headHeight = len * options.headHeightFactor;
  const headPoint = points.at(-1);
  len = mathDistance(headPoint, points.at(-2));
  const tailWidth = mathDistance(tailLeft, tailRight);
  if (headHeight > tailWidth * options.headTailFactor) {
    headHeight = tailWidth * options.headTailFactor;
  }
  const headWidth = headHeight * options.headWidthFactor;
  const neckWidth = headHeight * options.neckWidthFactor;
  headHeight = headHeight > len ? len : headHeight;
  const neckHeight = headHeight * options.neckHeightFactor;
  const headEndPoint = getThirdPoint(points.at(-2), headPoint, 0, headHeight, true);
  const neckEndPoint = getThirdPoint(points.at(-2), headPoint, 0, neckHeight, true);
  const headLeft = getThirdPoint(headPoint, headEndPoint, HALF_PI, headWidth, false);
  const headRight = getThirdPoint(headPoint, headEndPoint, HALF_PI, headWidth, true);
  const neckLeft = getThirdPoint(headPoint, neckEndPoint, HALF_PI, neckWidth, false);
  const neckRight = getThirdPoint(headPoint, neckEndPoint, HALF_PI, neckWidth, true);
  return [neckLeft, headLeft, point(getCoord(headPoint)), headRight, neckRight];
}

/**
 * 插值面部分数据
 * @param points
 * @param neckLeft
 * @param neckRight
 * @param tailWidthFactor

 */
export function getArrowBodyPoints(
  points: Coord[],
  neckLeft: Coord,
  neckRight: Coord,
  tailWidthFactor: number,
): Feature<Point>[] {
  const allLen = wholeDistance(points);
  const len = getBaseLength(points);
  const tailWidth = len * tailWidthFactor;
  const neckWidth = mathDistance(neckLeft, neckRight);
  const widthDif = (tailWidth - neckWidth) / 2;
  let tempLen = 0;
  const leftPoints: Feature<Point>[] = [];
  const rightPoints: Feature<Point>[] = [];

  for (let i = 1; i < points.length - 1; i++) {
    const angle = getAngleOfThreePoints(points[i - 1], points[i], points[i + 1]) / 2;
    tempLen += mathDistance(points[i - 1], points[i]);
    const w = (tailWidth / 2 - (tempLen / allLen) * widthDif) / Math.sin(angle);
    const left = getThirdPoint(points[i - 1], points[i], Math.PI - angle, w, true);
    const right = getThirdPoint(points[i - 1], points[i], angle, w, false);
    leftPoints.push(left);
    rightPoints.push(right);
  }
  return [...leftPoints, ...rightPoints];
}
