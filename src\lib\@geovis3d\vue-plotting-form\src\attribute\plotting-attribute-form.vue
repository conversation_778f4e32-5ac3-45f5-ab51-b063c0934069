<!-- 标绘entity的属性表单 -->
<script lang="ts" setup>
import type { PlottingDataSource } from '@/lib/@geovis3d/core';

import EntityAttribute from '../form/entity-attribute.vue';

import { usePlottingDataSourceHelper } from './use-plotting-data-source-helper';

export interface PlottingAttributeFormProps {
  dataSource: PlottingDataSource;
  hide?: Record<string, string[]>;
}

defineOptions({ name: 'PlottingAttributeForm' });

const props = defineProps<PlottingAttributeFormProps>();

const { active } = usePlottingDataSourceHelper(() => props.dataSource);
</script>

<template>
  <el-scrollbar v-if="active" :key="active.id" class="plotting-attribute-form">
    <el-form px-20px>
      <EntityAttribute :entity="active" :hide="hide" />
    </el-form>
  </el-scrollbar>
</template>

<style scoped lang="scss">
.plotting-attribute-form {
  width: 100%;
}
</style>
