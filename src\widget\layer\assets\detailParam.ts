export const detailParams = [
  {
    name: 'WMS',
    params: [
      {
        label: 'URL地址',
        name: 'URL地址',
        type: 'textarea',
        prop: 'url',
        column: 24,
        placeholder: '请输入URL地址',
        msg: '例：http://************:8021/tilecache/service/wms',
      },
      {
        label: '图层名称',
        name: '图层名称',
        type: 'input',
        prop: 'layer',
        column: 24,

        placeholder: '请输入图层名称',
        msg: '例：黄河-PNG-4326--002',
      },
      {
        label: '格网集',
        name: '格网集',
        type: 'select',
        prop: 'tileMatrixSetId',
        column: 24,
        propList: [
          'EPSG:4326',
          'EPSG:3857',
          'EPSG:4490',
          '自动格网集',
        ],
        value: 'EPSG:4326',
        placeholder: '请选择格网集',
      },
      {
        label: '瓦片格式',
        name: '瓦片格式',
        type: 'select',
        prop: 'format',
        column: 24,
        propList: [
          'image/jpeg',
          'image/png',
        ],
        value: 'image/png',
        placeholder: '请选择瓦片格式',
      },
      {
        label: '瓦片大小',
        name: '瓦片大小',
        type: 'select',
        prop: 'titleSize',
        column: 24,
        propList: [
          256,
          512,
        ],
        placeholder: '请选择瓦片大小',
      },
      {
        label: 'EGIS用户名',
        name: 'EGIS用户名',
        type: 'input',
        prop: 'egisUsername',
        column: 24,
        placeholder: '请输入EGIS用户名',
      },
      {
        label: 'EGIS密码',
        name: 'EGIS密码',
        type: 'input',
        prop: 'egisPassword',
        column: 24,
        placeholder: '请输入EGIS密码',
      },
      {
        label: '图层范围',
        prop: 'rectangle',
        column: 24,
      },

      {
        label: '图层描述',
        name: '图层描述',
        type: 'textarea',
        prop: 'description',
        column: 24,
      },
    ],
  },
  {
    name: 'WMTS',
    params: [
      {
        label: 'URL地址',
        name: 'URL地址',
        type: 'textarea',
        prop: 'url',
        column: 24,
        placeholder: '请输入URL地址',
        msg: '例：http://************:8021/tilecache/service/wmts',
      },
      {
        label: '图层名称',
        name: '图层名称',
        type: 'input',
        prop: 'layer',
        column: 24,
        placeholder: '请输入图层名称',
        msg: '例：Htxd8888-PNG-4326',
      },
      {
        label: '格网集',
        name: '格网集',
        type: 'select',
        prop: 'tileMatrixSetId',
        column: 24,
        propList: [
          'EPSG:4326',
          'EPSG:3857',
          'EPSG:4490',
          'default028mm',
          '自动格网集',
        ],
        placeholder: '请选择格网集',
      },
      {
        label: '瓦片格式',
        name: '瓦片格式',
        type: 'select',
        prop: 'format',
        column: 24,
        propList: [
          'image/jpeg',
          'image/png',
        ],
        placeholder: '请选择瓦片格式',
      },
      {
        label: '最小层级',
        name: '最小层级',
        type: 'num',
        prop: 'minimumLevel',
        column: 24,
        placeholder: '请输入最小层级',
      },
      {
        label: '最大层级',
        name: '最大层级',
        type: 'num',
        prop: 'maximumLevel',
        column: 24,
        placeholder: '请输入最大层级',
      },
      {
        label: '样式',
        name: '样式',
        type: 'input',
        prop: 'style',
        column: 24,
        placeholder: '请输入样式',
      },
      {
        label: 'EGIS用户名',
        name: 'EGIS用户名',
        type: 'input',
        prop: 'egisUsername',
        column: 24,
        placeholder: '请输入EGIS用户名',
      },
      {
        label: 'EGIS密码',
        name: 'EGIS密码',
        type: 'input',
        prop: 'egisPassword',
        column: 24,
        placeholder: '请输入EGIS密码',
      },
      {
        label: '图层范围',
        type: 'input',
        prop: 'rectangle',
        column: 24,
        placeholder: '请输入图层范围',
        msg: '例：[116.73,36.54,117.38,37.18]',
      },

    ],
  },
  {
    name: 'TMS',
    params: [
      {
        label: 'URL地址',
        name: 'URL地址',
        type: 'textarea',
        prop: 'url',
        column: 24,
        placeholder: '请输入URL地址',
        msg: '例：http://************:8021/tilecache/service/tms',
      },
      {
        label: '格网集',
        name: '格网集',
        type: 'select',
        prop: 'tileMatrixSetId',
        column: 24,
        propList: [
          'EPSG:4326',
          'EPSG:3857',
          'EPSG:4490',
          '自动格网集',

        ],
        placeholder: '请选择格网集',
      },
      {
        label: '瓦片格式',
        name: '瓦片格式',
        type: 'select',
        prop: 'format',
        column: 24,
        propList: [
          'image/jpeg',
          'image/png',
        ],
        placeholder: '请选择瓦片格式',
      },
      {
        label: 'EGIS用户名',
        name: 'EGIS用户名',
        type: 'input',
        prop: 'egisUsername',
        column: 24,
        placeholder: '请输入EGIS用户名',
      },
      {
        label: 'EGIS密码',
        name: 'EGIS密码',
        type: 'input',
        prop: 'egisPassword',
        column: 24,
        placeholder: '请输入EGIS密码',
      },
      {
        label: '图层范围',
        prop: 'rectangle',
        column: 24,
      },

    ],
  },
  {
    name: 'URLtemp',
    params: [
      {
        label: 'URL地址',
        name: 'URL地址',
        type: 'textarea',
        prop: 'url',
        column: 24,
        value: 'http://************:8021/tilecache/service/wmts',
        placeholder: '请输入URL地址',
        msg: '例：http://************:8021/tilecache/service/wmts',
      },
      {
        label: '格网集',
        name: '格网集',
        type: 'select',
        prop: 'tileMatrixSetId',
        column: 24,
        propList: [
          'EPSG:4326',
          'EPSG:3857',
          'EPSG:4490',
          '自动格网集',

        ],
        value: 'EPSG:4326',
        placeholder: '请选择格网集',
      },
      {
        label: '最大层级',
        name: '最大层级',
        type: 'num',
        prop: 'maximumLevel',
        column: 24,
        placeholder: '请输入最大层级',
        min: 0,
        max: 21,
      },
      {
        label: 'EGIS用户名',
        name: 'EGIS用户名',
        type: 'input',
        prop: 'egisUsername',
        column: 24,

        placeholder: '请输入EGIS用户名',
      },
      {
        label: 'EGIS密码',
        name: 'EGIS密码',
        type: 'input',
        prop: 'egisPassword',
        column: 24,

        placeholder: '请输入EGIS密码',
      },
      {
        label: '图层范围',
        type: 'input',
        prop: 'rectangle',
        column: 24,

        placeholder: '请输入图层范围',
        msg: '例：[116.73,36.54,117.38,37.18]',
      },

    ],
  },
  {
    name: 'MVT',
    params: [
      {
        label: 'URL地址',
        name: 'URL地址',
        type: 'textarea',
        prop: 'url',
        column: 24,
        value: 'http://************:8021/tilecache/service/wmts',
        placeholder: '请输入URL地址',
        msg: '例：http://************:8021/tilecache/service/wmts',
      },
      {
        label: '图层名称',
        name: '图层名称',
        type: 'input',
        prop: 'layer',
        column: 24,
        placeholder: '请输入图层名称',
        msg: '例：Htxd8888-PNG-4326',
      },
      {
        label: '格网集',
        name: '格网集',
        type: 'select',
        prop: 'tileMatrixSetId',
        column: 24,
        propList: [
          'EPSG:4326',
          'EPSG:3857',
          'EPSG:4490',
          '自动格网集',
        ],
        value: 'EPSG:3857',
        placeholder: '请选择格网集',
      },
      {
        label: '最大层级',
        name: '最大层级',
        type: 'num',
        prop: 'maximumLevel',
        column: 24,
        placeholder: '请输入最大层级',
        min: 0,
        max: 21,
        value: 20,
      },
      {
        label: '图层范围',
        type: 'input',
        prop: 'rectangle',
        column: 24,

        placeholder: '请输入图层范围',
        msg: '例：[116.73,36.54,117.38,37.18]',
      },

    ],
  },

  {
    name: 'cesiumdem',
    params: [
      {
        label: 'URL地址',
        name: 'URL地址',
        type: 'textarea',
        prop: 'url',
        column: 24,

        placeholder: '请输入URL地址',
        msg: '例：http://************:8320/tilecache/service/terrain/HongKongdsmcg-TERRAIN-4326',
      },
      {
        label: '图层范围',
        type: 'input',
        prop: 'rectangle',
        column: 24,

        placeholder: '请输入图层范围',
        msg: '例：[116.72,36.53,117.24,36.94]',
      },

    ],
  },

  {
    name: '倾斜摄影',
    params: [
      {
        label: 'URL地址',
        name: 'URL地址',
        type: 'textarea',
        prop: 'url',
        column: 24,

        placeholder: '请输入URL地址',
        msg: '例：http://************:8021/tiltphoto/郄马镇三维模型-B3DM/tileset.json',
      },
      {
        label: 'EGIS用户名',
        name: 'EGIS用户名',
        type: 'input',
        prop: 'egisUsername',
        column: 24,

        placeholder: '请输入EGIS用户名',
      },
      {
        label: 'EGIS密码',
        name: 'EGIS密码',
        type: 'input',
        prop: 'egisPassword',
        column: 24,

        placeholder: '请输入EGIS密码',
      },
      {
        label: '图层范围',
        type: 'input',
        prop: 'rectangle',
        column: 24,

        placeholder: '请输入图层范围',
        msg: '例：[116.72,36.53,117.24,36.94]',
      },

    ],
  },
];
