<!-- 折叠面板+树图 -->
<script lang="ts" setup>
import type { ElCollapse, ElCollapseItem, ElTree } from 'element-plus';

defineOptions({ name: 'CollapseTree' });

const props = defineProps<{
  /** ElCollapse 属性 */
  collapse?: InstanceType<typeof ElCollapse>['$props'];
  /** ElTree 属性 */
  tree?: InstanceType<typeof ElTree>['$props'];
  data?: (Record<string, any> & {
    /** ElCollapseItem 属性 */
    item?: InstanceType<typeof ElCollapseItem>['$props'];
    /** ElTree data 属性 */
    tree?: any[];
  })[];
}>();

const treeRef = shallowRef<InstanceType<typeof ElTree>>();
const collapseRef = shallowRef<InstanceType<typeof ElCollapse>>();
defineExpose({
  treeRef,
});

watchEffect(() => {
  if (props.tree) {
    treeRef.value?.forEach(item => item?.filter(''));
  }
});
</script>

<template>
  <el-collapse v-bind="props.collapse" ref="collapseRef" class="collapseTree">
    <el-collapse-item v-for="child in data" :key="child?.item?.name" v-bind="child.item">
      <template v-if="$slots.title" #title>
        <slot name="title" v-bind="child" />
      </template>
      <el-tree v-bind="props.tree" ref="treeRef" :data="child.tree">
        <template v-if="$slots.node" #default="attrs">
          <slot name="node" v-bind="attrs" />
        </template>
      </el-tree>
    </el-collapse-item>
  </el-collapse>
</template>

<style lang="scss" scoped>
  .el-tree {
  --el-font-size-base: 15px;

  :deep(.el-tree-node__content) {
    display: flex;
    align-items: center;
    height: 36px;
  }
}
</style>
