<script lang="ts" setup>
import { usePromiseModal } from '@/hooks/use-promise-modal';

import krpanoJSURL from './assets/tour.min.js?url';

defineOptions({ name: 'PanoramaDialog' });

const scriptTag = useScriptTag(krpanoJSURL);
const scriptTagLoaded = computedAsync(async () => {
  const res = await scriptTag.load();
  return !!res;
});

const xmlURL = shallowRef<string>();

const { isActive, open, completed, forceClose } = usePromiseModal<void>(
  (url: string) => {
    xmlURL.value = url;
  },
);

defineExpose({
  open,
  forceClose,
  completed,
});

const panoramaRef = templateRef('panoramaRef');

watchEffect(() => {
  if (scriptTagLoaded.value && panoramaRef.value && xmlURL.value) {
    const data = embedpano({
      xml: xmlURL.value,
      target: 'panoramaRef', // embed the krpano viewer into the 'pano' div element
      consolelog: true, // log the krpano messages also to the Browser console
      // bgcolor : "transparent",					// optionally: use a transparent background (for seeing the webpage behind 3D-models or partial panos)
      html5: 'only',
      passQueryParameters: true,
      onready(...krpano: any) {

        // enable the debugmode and handle all JS errors in the Browsers console
        // add a grid pano (with custom colors)
      },
    });
  }
});
</script>

<template>
  <template v-if="isActive">
    <div id="panoramaRef" ref="panoramaRef" h="100%" position="fixed inset-0 z-999" />
    <el-button un-size="40px!" position="fixed top-20px right-20px z-999" round color="#000" @click="forceClose()">
      <el-icon class="i-material-symbols:close" text="20px! #fff!" />
    </el-button>
  </template>
</template>
