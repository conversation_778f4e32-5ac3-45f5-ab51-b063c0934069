import type { Coord, Feature, LineString } from '@turf/turf';

import { getCoord, lineString } from '@turf/turf';
import { getThirdPoint, mathDistance } from './common';

interface StraightArrowOptions {
  maxArrowLength: number;
  arrowLengthScale: number;
}

/**
 * 细直箭头
 * @param points
 * @param options
 */
export function simpleStraightArrow(
  points: Coord[],
  options?: StraightArrowOptions,
): Feature<LineString> {
  if (points.length < 2) {
    throw new Error('points.length must >=2');
  }
  const coord1 = points[0];
  const coord2 = points[1];

  const arrowLengthScale = options?.arrowLengthScale ?? 3000000;
  const maxArrowLength = options?.maxArrowLength ?? 5;
  const distance = mathDistance(coord1, coord2);
  let len = distance / arrowLengthScale;
  len = len > maxArrowLength ? maxArrowLength : len;
  const leftPoint = getThirdPoint(coord1, coord2, Math.PI / 6, len, false);
  const rightPoint = getThirdPoint(coord1, coord2, Math.PI / 6, len, true);
  return lineString([coord1, coord2, leftPoint, coord2, rightPoint].map(e => getCoord(e)));
}
