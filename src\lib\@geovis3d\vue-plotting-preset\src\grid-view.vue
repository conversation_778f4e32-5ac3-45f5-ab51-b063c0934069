<!-- GridView -->
<script lang="ts" setup>
import type { PlottingTreeModel } from './tree';
import { PlottingEntity } from '@/lib/@geovis3d/core';
import { TextEllipsis } from '@/lib/@geovis3d/vue-component';

import { computed } from 'vue';

import { usePlotInjectState } from './state';

defineOptions({ name: 'GridView' });

const props = defineProps<{
  data?: PlottingTreeModel[];
}>();

const { dataSource } = usePlotInjectState()!;

const list = computed(() => {
  const isDirectory = props.data?.some(e => e.isDirectory);
  return isDirectory
    ? props.data ?? []
    : [
        {
          uuid: Cesium.createGuid(),
          children: props.data,
        } as PlottingTreeModel,
      ];
});

// 新增标绘
function plot(item: PlottingTreeModel, index = 0) {
  const entity = new PlottingEntity({
    ...item.constructors?.[index].options,
    name: item.label,
  });
  entity && dataSource.value?.entities.add(entity);
}
</script>

<template>
  <template v-for="item in list" :key="item.uuid">
    <el-divider v-if="item?.isDirectory" border-style="inset">
      {{ item.label }}
    </el-divider>
    <div class="grid-view">
      <div
        v-for="child in item.children ?? [item]"
        :key="child.label"
        class="grid-view-item"
        :title="child.label"
        @click="plot(child)"
      >
        <el-image
          class="banner"
          :src="child.cover"
          lazy
          fit="fill"
        />
        <TextEllipsis class="label">
          {{ child.label }}
        </TextEllipsis>
        <div
          v-if="child.constructors?.length && child.constructors!.length > 1"
          class="hover-buttons"
        >
          <div
            v-for="(entityFn, index) in child.constructors"
            :key="index"
            class="hover-button"
            @click.stop="plot(child, index)"
          >
            {{ entityFn.label }}
          </div>
        </div>
      </div>
    </div>
  </template>
</template>

<style scoped lang="scss">
.grid-view {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  margin: 0 -5px;

  .grid-view-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: calc(33.333% - 10px);
    height: 66px;
    margin: 3px;
    overflow: hidden;
    cursor: pointer;
    background: rgb(188 214 255 / 15%);
    border: 1px solid rgb(63 65 67 / 50%);
    border-radius: 4px;
    transition: background 0.3s;

    .hover-buttons {
      position: absolute;
      top: 0;
      left: 0;
      display: none;
      flex-direction: column;
      align-items: stretch;
      justify-content: space-around;
      width: 100%;
      height: 100%;
      color: #fff;
      background: rgb(93 155 255 / 50%);

      .hover-button {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 50%;

        &:hover {
          background: rgb(93 155 255 / 100%);
        }
      }
    }

    &:hover {
      background: rgb(93 155 255 / 50%);

      .hover-buttons {
        display: flex;
      }
    }
  }

  .banner {
    width: 22px;
    height: 22px;
    margin-bottom: 10px;
    background-size: 100% 100%;
  }

  .label {
    align-self: stretch;
    line-height: 1;
    color: #fff;
    text-align: center;
  }
}

.el-divider {
  --el-border-color: rgb(255 255 255 / 10%);
  --el-bg-color: transparent;
  --el-text-color-primary: #aad5ef;

  width: calc(100% - 8px);
  margin: 10px 4px;

  :deep(.el-divider__text) {
    font-size: 12px;
    color: rgb(255 255 255 / 80%);
  }
}
</style>
