<script lang="ts" setup>
defineOptions({ name: 'LayoutPanelButton' });
defineProps<LayoutPanelButtonProps>();

export interface LayoutPanelButtonProps {
  active?: boolean;
}
</script>

<template>
  <div
    class="layout-panel-button"
    :class="{
      'layout-panel-button--active': active,
    }"
  >
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.layout-panel-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 134px;
  height: 48px;
  padding-bottom: 5px;
  padding-left: 10px;
  margin-bottom: 15px;
  font-size: 19px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px;
  color: #f4f7fd;
  text-align: center;
  text-shadow: 0 2px 4px #000;
  cursor: pointer;
  background-image: url('./assets/layout-panel-button.svg');
  background-size: 100% 100%;
  transition: background-image 0.3s;

  &.layout-panel-button--active,
  &:hover {
    background-image: url('./assets/layout-panel-button--active.svg');
  }
}
</style>
