import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.PrimitiveCollection} 构造函数参数
 */
export type PrimitiveCollectionConstructorOptions = ConstructorParameters<
  typeof Cesium.PrimitiveCollection
>[0];

/**
 * {@link Cesium.PrimitiveCollection} 拓展用法与 {@link Cesium.PrimitiveCollection} 基本一致。
 *
 * `GcPrimitiveCollection.event`鼠标事件监听
 */
export class GcPrimitiveCollection extends Cesium.PrimitiveCollection {
  constructor(options?: PrimitiveCollectionConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
