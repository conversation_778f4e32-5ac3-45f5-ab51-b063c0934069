<!-- 倾斜压平 -->
<script lang="ts" setup>
import { cartesianToWgs84, CzPlotEntity } from '@x3d/all';
import { useCzEntities, useCzViewer } from '@x3d/vue-hooks';
import { CallbackProperty, PolygonGraphics, PolygonHierarchy } from 'cesium';

import FlattenPolygonCollection from './assets/FlattenPolygonCollection';

defineOptions({ name: 'TileFlat' });
const emits = defineEmits<{
  (event: 'close'): void;
}>();
const id = Cesium.createGuid();
const plotEntity = shallowRef<CzPlotEntity>();
const points = ref<Cesium.Cartesian3[]>();
const viewer = useCzViewer();
const flattenCollection = ref<FlattenPolygonCollection>();
function getTileset() {
  clear();
  const tileset = viewer.value?.scene.primitives._primitives.find(
    e => e instanceof Cesium.Cesium3DTileset,
  );
  flattenCollection.value = new FlattenPolygonCollection(tileset, { flatHeight: -30 });
}
function drawPloygon() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      manualTerminate: (entity) => {
        const bool = entity.record.positions.getLength() > 1;
        points.value = entity.record.positions.getPositions();
        getTileset();
        flattenCollection.value?.addRegion({ positions: points.value, id });
        return bool;
      },
      effect(entity) {
        entity.polygon ??= new PolygonGraphics({
          material: Cesium.Color.YELLOW.withAlpha(0.4),
          outlineColor: Cesium.Color.RED,
          outlineWidth: 2,
        });
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);
        if (coords.length >= 3) {
          const hierarchy = new PolygonHierarchy(positions);
          entity.polygon.hierarchy = new CallbackProperty(() => hierarchy, false);
        }
      },
    },
  });
}
function clear() {
  plotEntity.value = undefined;
  flattenCollection.value?.removeRegionById(id);
}
useCzEntities(() => [plotEntity.value]);
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="倾斜压平"
    class="w-400px"

    @close="emits('close')"
  >
    <el-form>
      <el-form-item label="倾斜模型">
        <el-select v-model="viewer" placeholder="请选择倾斜模型">
          <el-option v-for="item in models" :key="item.id" label="" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button class="plain-#5474FC" @click="drawPloygon">
        绘制压平面
      </el-button>
      <el-button class="plain-#FF6363" @click="clear()">
        清空
      </el-button>
    </template>
  </drag-card>
</template>
