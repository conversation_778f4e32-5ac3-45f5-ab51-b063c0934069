import type { MaterialPropertySerializateController } from './material-property';
import { PolylinePulseLinkMaterialProperty } from '@/lib/@geovis3d/material';

import * as Cesium from 'cesium';

import { ColorSerializate } from '../color';

export interface PolylinePulseLinkMaterialPropertySerializateJSON {
  color?: string;
  time?: number;
}

/**
 * PolylinePulseLinkMaterialProperty序列化加入缓存
 * @internal
 */
export default <
  MaterialPropertySerializateController<
    'PolylinePulseLinkMaterialProperty',
    PolylinePulseLinkMaterialProperty,
    PolylinePulseLinkMaterialPropertySerializateJSON
  >
>{
  type: 'PolylinePulseLinkMaterialProperty',
  hit: (property) => {
    return property instanceof PolylinePulseLinkMaterialProperty;
  },
  toJSON(property, time) {
    time ??= Cesium.JulianDate.now();
    const data = property?.getValue(time) ?? {};
    return {
      type: 'PolylinePulseLinkMaterialProperty',
      params: {
        color: ColorSerializate.toJSON(data.color),
        time: data.time,
      },
    };
  },
  fromJSON(json) {
    const { color, time } = json?.params ?? {};
    return new PolylinePulseLinkMaterialProperty({
      color: ColorSerializate.fromJSON(color),
      time,
    });
  },
};
