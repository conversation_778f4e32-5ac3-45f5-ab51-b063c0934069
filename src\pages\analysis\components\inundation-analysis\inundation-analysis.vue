<!-- 淹没分析 -->
<script lang="ts" setup>
import type { Pausable } from '@vueuse/shared';
import type { CzEntity } from '@x3d/all';

import { useTerrainCheck } from '@/hooks/useTerrainCheck';
import { cartesianToWgs84, CzPlotEntity, CzPlotScheme, PRESET_PLOT_SCHEMES_OPTIONS } from '@x3d/all';
import { useCzEntity, useCzViewer } from '@x3d/vue-hooks';
import { Color } from 'cesium';

defineOptions({ name: 'InundationAnalysis' });
PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});
let intervalFn: Pausable;
const viewer = useCzViewer();

const depthTestAgainstTerrain = viewer.value.scene.globe.depthTestAgainstTerrain;
viewer.value.scene.globe.depthTestAgainstTerrain = true; // 深度检测关闭

onUnmounted(() => {
  intervalFn?.pause();
  viewer.value.scene.globe.depthTestAgainstTerrain = depthTestAgainstTerrain;
});

// 淹没分析参数
const form = reactive({
  time: dayjs().format('YYYY-MM-DD HH:mm:00'), // 时间
  currentTime: dayjs().format('YYYY-MM-DD HH:mm:00'), // 时间
  maxHeight: 50, // 最高水位
  startHeight: 0,
  speed: 1, // 淹没速度 单位 m/h
  magnification: 600, // 倍率   秒
  currentHeight: 0,
});

const interval = useIntervalFn(() => {
  if (form.currentHeight === 0) {
    form.currentHeight = form.startHeight;
  }
  const { magnification, startHeight, maxHeight, speed } = form;
  form.currentTime = dayjs(form.currentTime).add(speed * magnification / 10, 's').format('YYYY-MM-DD HH:mm:00');

  form.currentHeight = Cesium.Math.clamp(
    form.currentHeight + (speed / 3600) * magnification / 10,
    startHeight,
    startHeight + maxHeight,
  );
  if (form.currentHeight >= startHeight + maxHeight) {
    interval?.pause();
  }
}, 100, {
  immediate: false,
});

const plotEntity = shallowRef<CzEntity>();

useCzEntity(plotEntity);
function plot() {
  clear();
  plotEntity.value = new CzPlotEntity({
    scheme: {
      type: 'Polygon',
      altitude: { visible: false },
      location: { visible: false },
    },
    polygon: {
      material: Cesium.Color.fromCssColorString('#5474FC').withAlpha(0.3),
      outlineColor: Cesium.Color.fromCssColorString('#5474FC'),
      outlineWidth: 1,
      outline: true,
    },
  });
}

const startHeightEntity = shallowRef<CzEntity>();

useCzEntity(startHeightEntity);

function setStartHeight() {
  startHeightEntity.value = new CzPlotEntity({
    scheme: {
      type: 'Point',
      altitude: { visible: false },
      location: { visible: false },
    },
    point: {
      pixelSize: 10,
      color: Color.AQUA,
      outlineWidth: 2,
      outlineColor: Color.WHITE.withAlpha(0.4),
    },
    label: {
      text: '水位高度起点',
      pixelOffset: new Cesium.Cartesian2(0, -25),
      font: '16px sans-serif',
    },
  });
}

function trigger() {
  const currentTime = viewer.value.clock.currentTime;
  const positions = plotEntity.value?.polygon?.hierarchy?.getValue(currentTime)?.positions;
  const position = startHeightEntity.value?.position?.getValue(currentTime);

  if (!positions?.length) {
    ElMessage.error('请绘制淹没区域');
    return;
  }
  if (!position) {
    ElMessage.error('请绘制当前水位高度点');
    return;
  }

  form.startHeight = cartesianToWgs84(position)[2]!;
  const entity = new Cesium.Entity({
    name: '多边形',
    polygon: {
      hierarchy: new Cesium.PolygonHierarchy(positions),
      material: Cesium.Color.fromCssColorString('#5474FC').withAlpha(0.6),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      extrudedHeight: new Cesium.CallbackProperty(() => {
        return +form.currentHeight - form.startHeight;
      }, false),
    },
  }) as any;
  plotEntity.value = entity;
  startHeightEntity.value = undefined;
  form.currentTime = form.time;
  form.currentHeight = 0;
  interval.resume();
}

function clear() {
  plotEntity.value = undefined;
  startHeightEntity.value = undefined;
  interval?.pause();
}

const terrainCheck = useTerrainCheck();
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="淹没分析"
    class="w-400px"
  >
    <el-text v-if="!terrainCheck" p="x-24px!" type="danger">
      未检测到存在地形，请打开图层勾选地形
    </el-text>
    <el-form mt="24px" mx="25px" label-position="left">
      <el-form-item label="模拟日期" :label-width="$vh(130)">
        <el-date-picker
          v-model="form.time"
          value-format="YYYY-MM-DD HH:mm:00"
          type="datetime"
          placeholder="选择日期"
        />
      </el-form-item>
      <el-form-item label="水位最大高度(米)" :label-width="$vh(130)">
        <el-input-number
          v-model="form.maxHeight"
          range
          :min="1"
          :step="1"
        />
      </el-form-item>
      <el-form-item label="淹没速度(米/小时)" :label-width="$vh(130)">
        <el-input-number
          v-model="form.speed"
          type="number"
          :min="0"
          w="120px"
        />
      </el-form-item>
      <el-form-item label="动画倍率" :label-width="$vh(130)">
        <el-slider
          v-model="form.magnification"
          mb="20px"
          :min="600"
          :max="7200"
          :step="600"
          :marks="{ 600: '10min/s', 3600: '1h/s', 7200: '2h/s' }"
          :format-tooltip="(val) => `${(val / 3600).toFixed(1)}h/s`"
        />
      </el-form-item>

      <el-divider>绘制要素</el-divider>
      <div p="b-20px">
        <el-button
          type="primary"
          px="26px!"
          class="bg-#4176FF! color-white!"
          @click="plot()"
        >
          绘制淹没面
        </el-button>
        <el-button
          type="primary"
          px="26px!"
          class="bg-#4176FF! color-white!"
          @click="setStartHeight()"
        >
          水位高度起点
        </el-button>
      </div>
      <el-divider>结果</el-divider>
      <el-form-item label="实时时间" :label-width="$vh(130)">
        {{ form.currentTime }}
      </el-form-item>
      <el-form-item label="实时水位(m)" :label-width="$vh(130)">
        {{ (form.currentHeight - form.startHeight).toFixed(1) }}
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button
        type="primary"
        px="26px!"
        class="bg-#4176FF! color-white!"
        @click="trigger"
      >
        模拟
      </el-button>
      <el-button class="plain-#FF6363 px-26px!" @click="clear">
        清除
      </el-button>
    </template>
  </drag-card>
</template>
