<!-- 坡度坡向分析 -->
<script lang="ts" setup>
import type { Entity, Rectangle } from 'cesium';
import { useTerrainCheck } from '@/hooks/useTerrainCheck';
import { CzPlotEntity } from '@x3d/all';

import { useCzEntities, useCzViewer } from '@x3d/vue-hooks';
import { CallbackProperty } from 'cesium';
import { slopeAnalysis } from './assets/SlopeAnalysis';

defineOptions({ name: 'SlopeAnalysis' });
// 深度检测关闭
const emits = defineEmits(['close']);
const viewer = useCzViewer();

watchEffect((onCleanup) => {
  const depthTestAgainstTerrain = viewer.value.scene.globe.depthTestAgainstTerrain;
  viewer.value.scene.globe.depthTestAgainstTerrain = true;
  onCleanup(() => {
    try {
      viewer.value.scene.globe.depthTestAgainstTerrain = depthTestAgainstTerrain;
    }
    catch (error) {
      console.error(error);
    }
  });
});

const points = ref<Rectangle>();
const plotEntity = shallowRef<CzPlotEntity>();
function start() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      forceTerminate: (entity) => {
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 2 ? positions : [];
        points.value = undefined;
        nextTick(() => {
          points.value = Cesium.Rectangle.fromCartesianArray(cache);
        });
        return entity.record.positions.getLength() > 1;
      },
      effect(entity, onCleanup) {
        entity.rectangle ??= new Cesium.RectangleGraphics({
          material: Cesium.Color.YELLOW.withAlpha(0.05),
        });
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 2 ? positions : [];
        const [startPoint, endPoint] = cache;
        if (startPoint && endPoint) {
          const rect = Cesium.Rectangle.fromCartesianArray(cache);
          entity.rectangle.coordinates = new CallbackProperty(() => rect, false);
          onCleanup(() => {
            entity.rectangle = undefined;
            points.value = undefined;
          });
        }
      },
    },
  });
}
const polylineEntities = shallowRef<Entity[]>();
const colorCounts = shallowRef<Record<string, number>>();

watch(points, async (points) => {
  if (points) {
    const res = await slopeAnalysis(viewer.value, points);
    polylineEntities.value = res.entities;
    colorCounts.value = res.colorCounts;
  }
});

useCzEntities(() => [plotEntity.value]);
useCzEntities(() => polylineEntities.value);
function clear() {
  polylineEntities.value = [];
  points.value = undefined;
  plotEntity.value = undefined;
}

const terrainCheck = useTerrainCheck();

const colors: string[] = [
  'yellow',
  'cyan',
  'limegreen',
  'red',
  'royalblue',
  'purple',
  'pink',
  'orange',
  'white',
];
const count = computed(() => {
  return Object.values(colorCounts.value ?? {}).reduce((a, b) => a + b, 0) || 0;
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="坡度坡向分析"
    class="w-400px"
    @close="emits('close')"
  >
    <el-text v-if="!terrainCheck" p="x-24px!" type="danger">
      未检测到存在地形，请打开图层勾选地形
    </el-text>
    <div grid="~ cols-3" gap="10px" p="20px">
      <div v-for="(color, index) in colors" :key="color" flex="~ items-center">
        <div :style="{ background: color }" w="40px" h="40px" m="r-10px" />
        <div flex="~ col">
          <span text="14px">{{ index * 10 }}-{{ (index + 1) * 10 }}° </span>
          <span text="12px">{{ ((colorCounts?.[color] || 0) / count * 100)?.toFixed(1) }}%</span>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" @click="start()">
        绘制分析区域
      </el-button>
      <el-button class="plain-#FF6363" @click="clear()">
        清除
      </el-button>
    </template>
  </drag-card>
</template>

<style lang="scss" scope>
.surface-excavation {
  display: inline-block;
}
</style>
