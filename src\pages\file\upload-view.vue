<!-- 上传组件 -->
<script lang="ts" setup>
import { fileSettingAddMultipleFilesUsingPost } from '@/genapi/cimapi';
import { useAsync } from '@/hooks/use-async';
import { UploadFilled } from '@element-plus/icons-vue';
import * as XLSX from 'xlsx';

export interface UploadViewEmits {
  (event: 'uploaded'): void;
  (event: 'sheetPreview', sheetJson: any): void;
  (event: 'xmlPreview', xmlContent: string): void;
}

defineOptions({ name: 'UploadView' });

const emit = defineEmits<UploadViewEmits>();

const elUploadRef = templateRef('elUploadRef');

const formats = ['json', 'geojson', 'kml', 'glb', 'gltf', 'shp', 'dbf', 'shx', 'xlsx', 'csv', 'xml'];

function isValidFormat(fileName: string) {
  return formats.includes(fileName.split('.').pop()!.toLowerCase());
}

const progressEvent = ref<any>();

const { execute: uploadFile, isLoading: uploadFileLoading } = useAsync(async (list: any) => {
  const formData = new FormData();
  list.forEach((file: any) => {
    formData.append('files', file.raw);
  });
  const res = await fileSettingAddMultipleFilesUsingPost({
    data: formData as any,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress(_progressEvent: any) {
      progressEvent.value = _progressEvent;
    },
  });
  ElMessage.success(res.message);
  emit('uploaded');
});

function onUploadChange(file: any, fileList: any) {
  if (file?.raw) {
    const name = file.name as string;
    const fileExtension = name.split('.').pop()?.toLowerCase();

    // 处理Excel/CSV文件
    if (fileExtension === 'xlsx' || fileExtension === 'csv') {
      const reader = new FileReader();
      reader.onload = function (event) {
        const buffer = new Uint8Array(event!.target!.result as any);
        const workbook = XLSX.read(buffer, {
          type: 'array',
        });
        const wsname = workbook.SheetNames[0];
        emit('sheetPreview', XLSX.utils.sheet_to_json(workbook.Sheets[wsname]));
      };
      reader.readAsArrayBuffer(file.raw);
      return;
    }

    // 处理XML文件
    if (fileExtension === 'xml') {
      const reader = new FileReader();
      reader.onload = function (event) {
        const xmlContent = event!.target!.result as string;
        emit('xmlPreview', xmlContent);
      };
      reader.readAsText(file.raw);
      return;
    }
  }

  const allFilesValid = fileList.every((item: any) => isValidFormat(item.name));
  if (allFilesValid) {
    uploadFile(0, fileList);
  }
  else {
    ElMessage.error(`仅支持上传${formats.join(', ')}格式的文件`);
  }
  elUploadRef.value?.clearFiles();
}
</script>

<template>
  <el-upload
    ref="elUploadRef"
    class="upload-btn"
    :auto-upload="false"
    :multiple="true"
    :on-change="onUploadChange"
    :show-file-list="false"
  >
    <div class="upload-content">
      <el-icon :size="30" color="#fff">
        <UploadFilled />
      </el-icon>
      <el-text un-text="18px" font="600">
        <span v-if="!uploadFileLoading">导入文件</span>
        <span v-else>上传中: {{ Math.round(progressEvent.loaded / 1024) }}Kb/{{ Math.round(progressEvent.total / 1024) }}Kb
          {{ Math.round(progressEvent.loaded / progressEvent.total * 100) }}%</span>
      </el-text>
      <el-text class="el-upload__tip">
        支持格式: {{ formats.join(', ') }}
      </el-text>
      <el-tooltip content="配套文件格式仅支持:dbf, shx" placement="bottom">
        <el-text class="el-upload__tip">
          若上传shp文件,配套文件一并上传
        </el-text>
      </el-tooltip>
    </div>
  </el-upload>
</template>

<style lang="scss" scoped>
.upload-btn {
  margin: 24px 34px;
  background-color: #4176ff;
  border-radius: 6px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  width: 29.3518vh;
  height: 11.8333vh;
  padding: 5px;
}
</style>
