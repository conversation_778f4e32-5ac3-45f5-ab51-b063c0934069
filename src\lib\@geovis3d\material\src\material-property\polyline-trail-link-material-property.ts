import * as Cesium from 'cesium';

import PolylineTrailLinkPNG from './assets/polyline-trail-link.png';
import { setCesiumMaterialCache } from './material-cache';
import PolylineTrailLink from './shaders/polyline-trail-link.glsl?raw';

export interface PolylineTrailLinkMaterialUniforms {
  color?: Cesium.Color;
  time?: number;
  image?: string;
}

const uniforms: PolylineTrailLinkMaterialUniforms = {
  color: Cesium.Color.RED,
  time: 0,
  image: PolylineTrailLinkPNG,
};

const PolylineTrailLinkMaterialType = 'PolylineTrailLinkMaterialType';

export class PolylineTrailLinkMaterial extends Cesium.Material {
  constructor(options?: PolylineTrailLinkMaterialUniforms) {
    super({
      fabric: {
        type: PolylineTrailLinkMaterialType,
        source: PolylineTrailLink,
        uniforms: {
          color: options?.color ?? uniforms.color,
          time: options?.time ?? uniforms.time,
          image: options?.image ?? uniforms.image,
        },
      },
    });
  }
}

/**
 * 追踪线
 */
export class PolylineTrailLinkMaterialProperty implements Cesium.MaterialProperty {
  constructor(options?: PolylineTrailLinkMaterialUniforms) {
    this._color = Cesium.defaultValue(options?.color, uniforms.color);
    // this._time = Cesium.defaultValue(options?.time, uniforms.time);
    this._time = performance.now();

    this._image = Cesium.defaultValue(options?.image, uniforms.image);
  }

  private _time: number;

  get time() {
    return this._time;
  }

  set time(value: number) {
    if (this._time !== value) {
      this._time = value;
    }
  }

  private _color: Cesium.Color;

  get color() {
    return this._color;
  }

  set color(value: Cesium.Color) {
    if (this._color !== value) {
      this._color = value;
    }
  }

  private _image: string;

  get image() {
    return this._image;
  }

  set image(value: string) {
    if (this._image !== value) {
      this._image = value;
    }
  }

  static get MaterialType() {
    return PolylineTrailLinkMaterialType;
  }

  getType(_time?: Cesium.JulianDate) {
    return PolylineTrailLinkMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: PolylineTrailLinkMaterialUniforms) {
    result ??= {};
    result.color = this.color;
    result.image = this.image;
    result.time = (performance.now() - this._time) / 1000;

    return result;
  }

  equals(other?: PolylineTrailLinkMaterialProperty) {
    return (
      this === other
      || (other instanceof PolylineTrailLinkMaterialProperty
        && this.color == other?.color
        && this.time == other?.time
        && this.image == other?.image)
    );
  }
}
setCesiumMaterialCache(PolylineTrailLinkMaterialType, {
  fabric: {
    type: PolylineTrailLinkMaterialType,
    uniforms,
    source: PolylineTrailLink,
  },
  translucent: () => true,
});
