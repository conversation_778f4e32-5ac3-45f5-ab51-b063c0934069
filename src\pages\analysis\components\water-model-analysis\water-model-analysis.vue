<!-- 水面融合 -->
<script lang="ts" setup>
import { toPublicPath, toStaticFilePath } from '@/utils/resolve-path';
import { cartesianToCartographic, cartographicToCartesian, CzPlotScheme, PRESET_PLOT_SCHEMES_OPTIONS, WaterRippleMaterialProperty } from '@x3d/all';
import { useCzDataSource, useCzPrimitive, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'WaterModelAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});

const viewer = useCzViewer();

useCzDataSource(async () => {
  const dataSource = await Cesium.GeoJsonDataSource.load(toPublicPath('/water.json'));
  dataSource.entities.values.forEach((item) => {
    if (item.polygon) {
      item.polygon.outline = new Cesium.ConstantProperty(false);
      item.polygon.classificationType = new Cesium.ConstantProperty(Cesium.ClassificationType.TERRAIN);
      item.polygon.height = new Cesium.ConstantProperty(0);
      item.polygon.heightReference = new Cesium.ConstantProperty(Cesium.ClassificationType.TERRAIN);
      item.polygon.material = new WaterRippleMaterialProperty({
        baseWaterColor: Cesium.Color.fromCssColorString('#006ab4').withAlpha(0.7),
        frequency: 8000,
        animationSpeed: 0.02,
        amplitude: 5.0,
        specularIntensity: 0.8,
        blendColor: Cesium.Color.fromCssColorString('#006ab4'),
      });
    }
  });
  return dataSource;
});

const { primitive } = useCzPrimitive(
  () => Cesium.Cesium3DTileset.fromUrl(toStaticFilePath('/3d-tileset/max-fsdzm/tileset.json')),

);

watchEffect(() => {
  primitive.value && viewer.value.flyTo(primitive.value);
});

const originPosition = computed(() => primitive.value?.boundingSphere?.center.clone());
const originModelMatrix = computed(() => primitive.value?.modelMatrix?.clone());

watch([primitive], ([primitive]) => {
  if (!primitive) {
    return;
  }

  const cartographic = cartesianToCartographic(originPosition.value!.clone());
  cartographic.height = 9;
  const position = cartographicToCartesian(cartographic);
  // 平移
  const diff = Cesium.Cartesian3.subtract(position, originPosition.value!, new Cesium.Cartesian3());
  const matrix = Cesium.Matrix4.fromTranslation(diff, new Cesium.Matrix4());
  primitive.modelMatrix = Cesium.Matrix4.multiply(originModelMatrix.value!, matrix, new Cesium.Matrix4());
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="精模与水体"
    class="w-400px"
    @close="emits('close')"
  >
    <!-- <el-divider>绘制要素</el-divider>
    <div m="x-20px y-10px">
      <el-button type="primary" @click="plotStart()">
        绘制起点
      </el-button>
      <el-button type="primary" @click="plotEnd()">
        绘制终点
      </el-button>
    </div>
    <el-divider>绘制障碍</el-divider> -->
    <div m="x-20px y-10px">
      <!-- <el-button type="primary" @click="plotObstacles('Point')">
        绘制障碍点
      </el-button>
      <el-button type="primary" @click="plotObstacles('Polyline')">
        绘制障碍线
      </el-button> -->
      <!-- <el-button type="primary" @click="plotObstacles()">
        绘制障碍面
      </el-button> -->
    </div>
  </drag-card>
</template>
