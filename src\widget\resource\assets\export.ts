export const ICON_POINT_MAP: any = import.meta.glob('./point/*.png', { eager: true });
export const ICON_POINT_PLAIN_MAP: any = import.meta.glob('./pointPlain/*.png', { eager: true });

export function getIconPointPNG(name: string) {
  const key = Object.keys(ICON_POINT_MAP).find(e => new RegExp(`\/${name}\.`).test(e));
  return key ? ICON_POINT_MAP[key]?.default : undefined;
}
export function getIconPointPlainPNG(name: string) {
  const key = Object.keys(ICON_POINT_PLAIN_MAP).find(e => new RegExp(`\/${name}\.`).test(e));
  return key ? ICON_POINT_PLAIN_MAP[key]?.default : undefined;
}
