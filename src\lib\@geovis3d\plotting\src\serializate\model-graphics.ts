import type { Cartesian2SerializateJSON } from './cartesian2';

import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type {
  ColorBlendModeSerializateJSON,
  HeightReferenceSerializateJSON,
  ShadowModeSerializateJSON,
} from './enum';
import * as Cesium from 'cesium';
import { Cartesian2Serializate } from './cartesian2';

import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface ModelGraphicsSerializateJSON {
  show?: boolean;
  uri?: string;
  scale?: number;
  minimumPixelSize?: number;
  maximumScale?: number;
  incrementallyLoadTextures?: boolean;
  runAnimations?: boolean;
  clampAnimations?: boolean;
  shadows?: ShadowModeSerializateJSON;
  heightReference?: HeightReferenceSerializateJSON;
  silhouetteColor?: ColorSerializateJSON;
  silhouetteSize?: number;
  color?: ColorSerializateJSON;
  colorBlendMode?: ColorBlendModeSerializateJSON;
  colorBlendAmount?: number;
  imageBasedLightingFactor?: Cartesian2SerializateJSON;
  lightColor?: ColorSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  // nodeTransformations?: | Cesium.PropertyBag  | {  [key: string]: Cesium.TranslationRotationScale;   };
  // articulations?: | Cesium.PropertyBag  | {  [key: string]: number;  };
  // clippingPlanes?: ClippingPlaneCollectionSerializateJSON;
  // customShader?: CustomShaderSerializateJSON;
}

export type ModelGraphicsKey = keyof ModelGraphicsSerializateJSON;

export class ModelGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.ModelGraphics,
    omit?: ModelGraphicsKey[],
    time?: Cesium.JulianDate,
  ): ModelGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      uri: getValue('uri'),
      scale: getValue('scale'),
      minimumPixelSize: getValue('minimumPixelSize'),
      maximumScale: getValue('maximumScale'),
      incrementallyLoadTextures: getValue('incrementallyLoadTextures'),
      runAnimations: getValue('runAnimations'),
      clampAnimations: getValue('clampAnimations'),
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      silhouetteColor: ColorSerializate.toJSON(getValue('silhouetteColor')),
      silhouetteSize: getValue('silhouetteSize'),
      color: ColorSerializate.toJSON(getValue('color')),
      colorBlendMode: EnumSerializate.toJSON(Cesium.ColorBlendMode, getValue('colorBlendMode')),
      colorBlendAmount: getValue('colorBlendAmount'),
      imageBasedLightingFactor: Cartesian2Serializate.toJSON(getValue('imageBasedLightingFactor')),
      lightColor: ColorSerializate.toJSON(getValue('lightColor')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      // nodeTransformations: | Cesium.PropertyBag  | {  [key: string]: Cesium.TranslationRotationScale,   };
      // articulations: | Cesium.PropertyBag  | {  [key: string]: number,  };
      // clippingPlanes:  ClippingPlaneCollectionSerializate.toJSON(getValue('clippingPlanes')),
      // customShader:  CustomShaderSerializate.toJSON(getValue('customShader')),
    };
  }

  static fromJSON(
    json?: ModelGraphicsSerializateJSON,
    omit?: ModelGraphicsKey[],
  ): Cesium.ModelGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.ModelGraphics({
      show: getValue('show') ?? true,
      uri: getValue('uri'),
      scale: getValue('scale'),
      minimumPixelSize: getValue('minimumPixelSize'),
      maximumScale: getValue('maximumScale'),
      incrementallyLoadTextures: getValue('incrementallyLoadTextures'),
      runAnimations: getValue('runAnimations'),
      clampAnimations: getValue('clampAnimations'),
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      silhouetteColor: ColorSerializate.fromJSON(getValue('silhouetteColor')),
      silhouetteSize: getValue('silhouetteSize'),
      color: ColorSerializate.fromJSON(getValue('color')),
      colorBlendMode: EnumSerializate.fromJSON(Cesium.ColorBlendMode, getValue('colorBlendMode')),
      colorBlendAmount: getValue('colorBlendAmount'),
      imageBasedLightingFactor: Cartesian2Serializate.fromJSON(
        getValue('imageBasedLightingFactor'),
      ),
      lightColor: ColorSerializate.fromJSON(getValue('lightColor')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      // nodeTransformations: | Cesium.PropertyBag  | {  [key: string]: Cesium.TranslationRotationScale,   };
      // articulations: | Cesium.PropertyBag  | {  [key: string]: number,  };
      // clippingPlanes:  ClippingPlaneCollectionSerializate.fromJSON(getValue('clippingPlanes')),
      // customShader:  CustomShaderSerializate.fromJSON(getValue('customShader')),
    });
  }
}
