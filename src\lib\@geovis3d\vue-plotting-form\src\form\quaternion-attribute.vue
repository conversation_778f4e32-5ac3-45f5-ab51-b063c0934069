<!-- QuaternionAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';
import { useCzEventComputed } from '@x3d/vue-hooks';

import * as Cesium from 'cesium';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'QuaternionAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
}>();

const position = useCzEventComputed(
  () => props.entity?.definitionChanged,
  () => props.entity?.position,
);

const orientation = useCzEventComputed(
  () => props.entity?.definitionChanged,
  () => props.entity?.orientation,
);

interface Quaternion {
  heading: number;
  pitch: number;
  roll: number;
}

const model = ref<Quaternion>({
  heading: 0,
  pitch: 0,
  roll: 0,
});

watch(orientation, (orientation) => {
  const cartesian3 = position.value?.getValue(Cesium.JulianDate.now());
  const time = Cesium.JulianDate.now();
  const quaternion
    = orientation?.getValue(time)
      ?? Cesium.Transforms.headingPitchRollQuaternion(
        cartesian3!,
        new Cesium.HeadingPitchRoll(
          Cesium.Math.toRadians(0),
          Cesium.Math.toRadians(0),
          Cesium.Math.toRadians(0),
        ),
      );
  const mtx3 = Cesium.Matrix3.fromQuaternion(quaternion);
  const mtx4 = Cesium.Matrix4.fromRotationTranslation(mtx3, cartesian3);
  const hpr = Cesium.Transforms.fixedFrameToHeadingPitchRoll(mtx4);
  const heading = +Cesium.Math.toDegrees(hpr.heading).toFixed(0);
  const pitch = +Cesium.Math.toDegrees(hpr.pitch).toFixed(0);
  const roll = +Cesium.Math.toDegrees(hpr.roll).toFixed(0);
  if (model.value.heading !== heading || model.value.pitch !== pitch || model.value.roll !== roll) {
    model.value.heading = heading;
    model.value.pitch = pitch;
    model.value.roll = roll;
  }
});

watch(
  model,
  (model) => {
    const { heading, pitch, roll } = model;
    const cartesian3 = position.value?.getValue(Cesium.JulianDate.now());
    const orientation = props.entity?.orientation as any;
    orientation?.setValue?.(
      Cesium.Transforms.headingPitchRollQuaternion(
        cartesian3!,
        new Cesium.HeadingPitchRoll(
          Cesium.Math.toRadians(heading),
          Cesium.Math.toRadians(pitch),
          Cesium.Math.toRadians(roll),
        ),
      ),
    );
  },
  { deep: true },
);
</script>

<template>
  <el-form-item label="旋转" />
  <NumberAttribute
    v-model="model.heading"
    label="heading"
    :min="-180"
    :max="180"
    :precision="0"
  />
  <NumberAttribute
    v-model="model.pitch"
    label="pitch"
    :min="-180"
    :max="180"
    :precision="0"
  />
  <NumberAttribute
    v-model="model.roll"
    label="roll"
    :min="-180"
    :max="180"
    :precision="0"
  />
</template>
