import type { MaterialPropertySerializateController } from './material-property';

import { RedtideMaterialProperty } from '@/lib/@geovis3d/material';

export interface RedtideMaterialPropertySerializateJSON {
  // color?: string;
}

export default <
  MaterialPropertySerializateController<
    'RedtideMaterialProperty',
    RedtideMaterialProperty,
    RedtideMaterialPropertySerializateJSON
  >
>{
  type: 'RedtideMaterialProperty',
  hit: (property) => {
    return property instanceof RedtideMaterialProperty;
  },
  toJSON(_property, _time) {
    // time ??= Cesium.JulianDate.now();
    // const color = property?.getValue(time)?.color;
    return {
      type: 'RedtideMaterialProperty',
      params: {
        // color: ColorSerializate.toJSON(color),
      },
    };
  },
  fromJSON(_json) {
    // const color = json?.params?.color;
    // return new RedtideMaterialProperty(ColorSerializate.fromJSON(color));
    return new RedtideMaterialProperty();
  },
};
