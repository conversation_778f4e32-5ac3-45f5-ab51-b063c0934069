<!-- 角度测量 -->
<script lang="ts" setup>
import { CzPlotEntity } from '@x3d/all';
import { useCzEntities } from '@x3d/vue-hooks';
import { Math as CMath } from 'cesium';

defineOptions({ name: 'MeasureAngle' });
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    isActive?: boolean;
  }>(),
  { modelValue: true },
);
const show = useVModel(props, 'modelValue');
const unit = ref('度');
const result = ref(0);

const plotEntity = shallowRef<CzPlotEntity>();
function initEntity() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      forceTerminate: (entity) => {
        return entity.record.positions.getLength() > 1;
      },
      control: { visible: true },
      effect(entity) {
        entity.polyline ??= new Cesium.PolylineGraphics({
          material: Cesium.Color.fromCssColorString('#FCC650'),
          width: 2,
        });
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 2 ? positions : [];
        entity.polyline.positions = new Cesium.CallbackProperty(() => cache, false);
        entity.position = new Cesium.ConstantPositionProperty(
          record.positions.getCenter()!,
        );
        const [startPoint, endPoint] = positions;
        if (startPoint && endPoint) {
          result.value = getHeading(startPoint, endPoint);
          entity.label = new Cesium.LabelGraphics({
            text: `${result.value.toFixed(2)}°`,
            font: '16pt Source Han Sans CN',
            pixelOffset: new Cesium.Cartesian2(0, -20),
          });
        }
      },
    },
  });
}
function getHeading(pointA: Cesium.Cartesian3, pointB: Cesium.Cartesian3): number {
  // 建立以点A为原点，X轴为east,Y轴为north,Z轴朝上的坐标系
  const transform = Cesium.Transforms.eastNorthUpToFixedFrame(pointA);
  // 向量AB
  const positionvector = Cesium.Cartesian3.subtract(
    pointB,
    pointA,
    new Cesium.Cartesian3(),
  );
  // 因transform是将A为原点的eastNorthUp坐标系中的点转换到世界坐标系的矩阵
  // AB为世界坐标中的向量
  // 因此将AB向量转换为A原点坐标系中的向量，需乘以transform的逆矩阵。
  const vector = Cesium.Matrix4.multiplyByPointAsVector(
    Cesium.Matrix4.inverse(transform, new Cesium.Matrix4()),
    positionvector,
    new Cesium.Cartesian3(),
  );
  // 归一化
  const direction = Cesium.Cartesian3.normalize(vector, new Cesium.Cartesian3());
  // heading
  const heading = Math.atan2(direction.y, direction.x) - Cesium.Math.PI_OVER_TWO;
  return Cesium.Math.toDegrees(Cesium.Math.TWO_PI - Cesium.Math.zeroToTwoPi(heading));
}
useCzEntities(() => [plotEntity.value]);

initEntity();
defineExpose({ initEntity });
</script>

<template>
  <basic-card
    v-show="show"
    show-close
    title="量测设置"
    @close="show = false"
  >
    <div class="px-24px py-16px">
      方向角量测
      <el-select v-model="unit" mt="8px">
        <el-option label="度" value="度" />
        <el-option label="弧度" value="弧度" />
      </el-select>
      <div text="#FCC650 14px" mt="12px" lh="24px">
        提示：左键在球上选点并拖动量测，鼠标放开结束
      </div>
    </div>
    <template #footer>
      <div flex="~ justify-between" class="my--8px w-full font-blod-18px">
        方向角：
        <span> {{ (unit === '度' ? result : CMath.toRadians(result)).toFixed(2) }}{{ unit }} </span>
      </div>
    </template>
  </basic-card>
</template>

<style lang="scss" scoped>
.el-select {
  :deep() &__wrapper {
    min-height: 44px !important;
  }
}
</style>
