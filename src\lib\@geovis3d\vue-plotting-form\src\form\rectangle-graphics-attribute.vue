<!-- RectangleGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { RectangleGraphicsKey, RectangleGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { RectangleGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import ClassificationTypeAttribute from './classification-type-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'RectangleGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: RectangleGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.RectangleGraphics, RectangleGraphicsSerializateJSON>({
  graphic: () => props.entity?.rectangle,
  omit: props.omit || ['coordinates'],
  toJSON: (graphics, omit) => RectangleGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => RectangleGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="rectangle"
    graphics-field="show"
    label="可见"
  />
  <!-- <RectangleAttribute v-if="!hide?.includes('coordinates')"
    v-model="model.coordinates"
graphics="rectangle"
    graphics-field="coordinates" label="coordinates" /> -->
  <NumberAttribute
    v-if="!hide?.includes('height')"
    v-model="model.height"
    graphics="rectangle"
    graphics-field="height"
    label="高"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="rectangle"
    graphics-field="heightReference"
    label="高度参照"
  />
  <NumberAttribute
    v-if="!hide?.includes('extrudedHeight')"
    v-model="model.extrudedHeight"
    graphics="rectangle"
    graphics-field="extrudedHeight"
    label="拉伸高度"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('extrudedHeightReference')"
    v-model="model.extrudedHeightReference"
    graphics="rectangle"
    graphics-field="extrudedHeightReference"
    label="拉伸参照"
  />
  <NumberAttribute
    v-if="!hide?.includes('rotation')"
    v-model="model.rotation"
    graphics="rectangle"
    graphics-field="rotation"
    label="旋转"
  />
  <NumberAttribute
    v-if="!hide?.includes('stRotation')"
    v-model="model.stRotation"
    graphics="rectangle"
    graphics-field="stRotation"
    label="纹理旋转"
  />
  <NumberAttribute
    v-if="!hide?.includes('granularity')"
    v-model="model.granularity"
    graphics="rectangle"
    graphics-field="granularity"
    label="渲染粒度"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="rectangle"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="rectangle"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="rectangle"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="rectangle"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="rectangle"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="rectangle"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="rectangle"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <ClassificationTypeAttribute
    v-if="!hide?.includes('classificationType')"
    v-model="model.classificationType"
    graphics="rectangle"
    graphics-field="classificationType"
    label="贴地类型"
  />
  <NumberAttribute
    v-if="!hide?.includes('zIndex')"
    v-model="model.zIndex"
    graphics="rectangle"
    graphics-field="zIndex"
    label="层级"
    :precision="0"
  />
</template>
