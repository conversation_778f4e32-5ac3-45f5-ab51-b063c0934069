import type { JulianDate } from 'cesium';
import type { CreateCzFollowerRetrun } from '../../popper/create-cz-follower';
import type { GcEntity } from './entity';

import { effectHelper } from '@/lib/@geovis3d/shared';

import { arrow, flip } from '@floating-ui/dom';

import { createCzFollower } from '../../popper/create-cz-follower';
import { createEntityEffect } from './entity';

export function createEntityTooltip(entity: GcEntity) {
  createEntityEffect(entity, (onCleanup) => {
    const tooltip = entity.tooltip;
    if (!tooltip) {
      return;
    }

    let follower: CreateCzFollowerRetrun | undefined;
    let cleanup: VoidFunction | undefined;
    const close = () => {
      cleanup?.();
      follower?.destroy();
      follower = undefined;
    };

    let draging = false;
    const stopDragListener = entity.event.on('DRAG', ({ draging: active }) => {
      draging = active;
    });

    const stopHoverListener = entity.event.on('HOVER', (options) => {
      const { hover, context } = options;
      if (hover && !draging) {
        if (!follower) {
          follower = createCzFollower({
            scene: entity.scene!,
            group: 'entity-tooltip-group',
            guid: entity.id,
          });
          follower.element.style.background = 'rgba(0,0,0,0.3)';
          follower.element.style.padding = '0.2rem';
          follower.element.style.fontSize = '0.6rem';
          follower.element.style.pointerEvents = 'none';
          follower.element.style.color = '#fff';
          tooltip({
            entity,
            element: follower.element,
            onCleanup: fn => (cleanup = fn),
            close,
          });
        }
        follower.update({
          position: context.endPosition,
          placement: 'top',
          middleware: [flip(), arrow({ element: follower.element })],
        });
      }
      else {
        close();
      }
    });

    onCleanup(() => {
      stopDragListener();
      stopHoverListener();
      close();
    });
  });
}

export function createEntityPopover(entity: GcEntity) {
  createEntityEffect(entity, (onCleanup) => {
    const popover = entity.popover;
    if (!popover) {
      return;
    }

    let follower: CreateCzFollowerRetrun | undefined;

    let cleanup: VoidFunction | undefined;

    const update = (time: JulianDate) => {
      follower?.update({
        position: entity.position?.getValue(time),
        placement: 'top',
        middleware: [flip(), arrow({ element: follower.element })],
      });
    };
    const [updateExecute, updateDestroy] = effectHelper((onCleanup) => {
      onCleanup(entity.scene!.postUpdate.addEventListener((_, time) => update(time)));
    });

    const close = () => {
      cleanup?.();
      follower?.destroy();
      updateDestroy();
      follower = undefined;
    };

    const render = () => {
      if (!follower) {
        follower = createCzFollower({
          scene: entity.scene!,
          group: 'entity-popover-group',
          guid: entity.id,
        });
        follower.element.style.background = 'rgba(0,0,0,0.3)';
        follower.element.style.padding = '0.2rem';
        follower.element.style.fontSize = '0.6rem';
        follower.element.style.pointerEvents = 'none';
        follower.element.style.color = '#fff';
        popover({
          entity,
          element: follower.element,
          onCleanup: fn => (cleanup = fn),
          close,
        });
        updateExecute();
      }
    };

    const [execute, destroy] = effectHelper((onCleanup) => {
      onCleanup(entity.event.on('LEFT_CLICK', render));
    });

    execute();

    onCleanup(() => {
      destroy();
      stop?.();
      close();
    });
  });
}

export function createEntityPrompt(entity: GcEntity) {
  createEntityEffect(entity, (onCleanup) => {
    const prompt = entity.prompt;
    if (!prompt) {
      return;
    }

    let follower: CreateCzFollowerRetrun | undefined;
    let cleanup: VoidFunction | undefined;
    const close = () => {
      cleanup?.();
      follower?.destroy();
      follower = undefined;
    };

    const stop = entity.event.on('DRAG', (options) => {
      const { draging, context } = options;
      if (draging) {
        if (!follower) {
          follower = createCzFollower({
            scene: entity.scene!,
            group: 'entity-prompt-group',
            guid: entity.id,
          });
          follower.element.style.background = 'rgba(0,0,0,0.3)';
          follower.element.style.padding = '0.2rem';
          follower.element.style.fontSize = '0.6rem';
          follower.element.style.pointerEvents = 'none';
          follower.element.style.color = '#fff';
          prompt({
            entity,
            element: follower.element,
            onCleanup: fn => (cleanup = fn),
            close,
          });
        }
        follower.update({
          position: context.endPosition,
          placement: 'top',
          middleware: [flip(), arrow({ element: follower.element })],
        });
      }
      else {
        close();
      }
    });

    onCleanup(() => {
      stop();
      close();
    });
  });
}
