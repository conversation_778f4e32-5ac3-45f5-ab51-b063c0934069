/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by @xiankq/openapi-typescript-expand
// Power by openapi-typescript

import {cimRequest} from "./request";

//打包报错 随便加上占位
export function chunkUploadUploadChunkUsingPost(options:ChunkUploadUploadChunkUsingPost.Options):Promise<ChunkUploadUploadChunkUsingPost.Result> {
  return cimRequest({
    url:'/chunkUpload/uploadChunk',
    method:'post',
    ...options,
  });
}
//打包报错 随便加上占位
export function chunkUploadMergeUsingPost(options:ChunkUploadUploadChunkUsingPost.Options):Promise<ChunkUploadUploadChunkUsingPost.Result> {
  return cimRequest({
    url:'/chunkUpload/uploadChunk',
    method:'post',
    ...options,
  });
}
/**
 * @tag change-data-controller
 * @summary 删除转换任务
 * @url /changeData/deleteById/{id}
 * @method get
 * @description 删除转换任务
 */

export module ChangeDataDeleteByIdIdUsingGet {
  export type Operation = paths['/changeData/deleteById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag change-data-controller
 * @summary 删除转换任务
 * @url /changeData/deleteById/{id}
 * @method get
 * @description 删除转换任务
 */

export function changeDataDeleteByIdIdUsingGet(options:ChangeDataDeleteByIdIdUsingGet.Options):Promise<ChangeDataDeleteByIdIdUsingGet.Result> {
  return cimRequest({
    url:'/changeData/deleteById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag change-data-controller
 * @summary 文件地址列表
 * @url /changeData/fileList
 * @method get
 * @description 文件地址列表
 */

export module ChangeDataFileListUsingGet {
  export type Operation = paths['/changeData/fileList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag change-data-controller
 * @summary 文件地址列表
 * @url /changeData/fileList
 * @method get
 * @description 文件地址列表
 */

export function changeDataFileListUsingGet(options:ChangeDataFileListUsingGet.Options):Promise<ChangeDataFileListUsingGet.Result> {
  return cimRequest({
    url:'/changeData/fileList',
    method:'get',
    ...options,
  });
}

/**
 * @tag change-data-controller
 * @summary 根据id获取转换任务
 * @url /changeData/getById/{id}
 * @method get
 * @description 根据id获取转换任务
 */

export module ChangeDataGetByIdIdUsingGet {
  export type Operation = paths['/changeData/getById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag change-data-controller
 * @summary 根据id获取转换任务
 * @url /changeData/getById/{id}
 * @method get
 * @description 根据id获取转换任务
 */

export function changeDataGetByIdIdUsingGet(options:ChangeDataGetByIdIdUsingGet.Options):Promise<ChangeDataGetByIdIdUsingGet.Result> {
  return cimRequest({
    url:'/changeData/getById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag change-data-controller
 * @summary 获取转换任务列表
 * @url /changeData/listPage
 * @method post
 * @description 获取转换任务列表
 */

export module ChangeDataListPageUsingPost {
  export type Operation = paths['/changeData/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag change-data-controller
 * @summary 获取转换任务列表
 * @url /changeData/listPage
 * @method post
 * @description 获取转换任务列表
 */

export function changeDataListPageUsingPost(options:ChangeDataListPageUsingPost.Options):Promise<ChangeDataListPageUsingPost.Result> {
  return cimRequest({
    url:'/changeData/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag change-data-controller
 * @summary getProgress
 * @url /changeData/progress
 * @method get
 * @description
 */

export module ChangeDataProgressUsingGet {
  export type Operation = paths['/changeData/progress']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag change-data-controller
 * @summary getProgress
 * @url /changeData/progress
 * @method get
 * @description
 */

export function changeDataProgressUsingGet(options:ChangeDataProgressUsingGet.Options):Promise<ChangeDataProgressUsingGet.Result> {
  return cimRequest({
    url:'/changeData/progress',
    method:'get',
    ...options,
  });
}

/**
 * @tag change-data-controller
 * @summary 新增/更新转换任务
 * @url /changeData/saveOrUpdate
 * @method post
 * @description 新增/更新转换任务
 */

export module ChangeDataSaveOrUpdateUsingPost {
  export type Operation = paths['/changeData/saveOrUpdate']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag change-data-controller
 * @summary 新增/更新转换任务
 * @url /changeData/saveOrUpdate
 * @method post
 * @description 新增/更新转换任务
 */

export function changeDataSaveOrUpdateUsingPost(options:ChangeDataSaveOrUpdateUsingPost.Options):Promise<ChangeDataSaveOrUpdateUsingPost.Result> {
  return cimRequest({
    url:'/changeData/saveOrUpdate',
    method:'post',
    ...options,
  });
}

/**
 * @tag change-data-controller
 * @summary 文件名更新/删/改
 * @url /changeData/updateFileList
 * @method post
 * @description 文件名更新/删/改
 */

export module ChangeDataUpdateFileListUsingPost {
  export type Operation = paths['/changeData/updateFileList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag change-data-controller
 * @summary 文件名更新/删/改
 * @url /changeData/updateFileList
 * @method post
 * @description 文件名更新/删/改
 */

export function changeDataUpdateFileListUsingPost(options:ChangeDataUpdateFileListUsingPost.Options):Promise<ChangeDataUpdateFileListUsingPost.Result> {
  return cimRequest({
    url:'/changeData/updateFileList',
    method:'post',
    ...options,
  });
}

/**
 * @tag change-data-controller
 * @summary 上传转换数据
 * @url /changeData/uploadChangeFile
 * @method post
 * @description 上传转换数据
 */

export module ChangeDataUploadChangeFileUsingPost {
  export type Operation = paths['/changeData/uploadChangeFile']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Header = Operation['parameters']['header'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    header?: Header;
    params: Query;
  };
}

/**
 * @tag change-data-controller
 * @summary 上传转换数据
 * @url /changeData/uploadChangeFile
 * @method post
 * @description 上传转换数据
 */

export function changeDataUploadChangeFileUsingPost(options:ChangeDataUploadChangeFileUsingPost.Options):Promise<ChangeDataUploadChangeFileUsingPost.Result> {
  return cimRequest({
    url:'/changeData/uploadChangeFile',
    method:'post',
    ...options,
  });
}

/**
 * @tag file-convert-controller
 * @summary downloadFile
 * @url /files/download/{fileDir}/{fileName}
 * @method get
 * @description
 */

export module FilesDownloadFileDirFileNameUsingGet {
  export type Operation = paths['/files/download/{fileDir}/{fileName}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag file-convert-controller
 * @summary downloadFile
 * @url /files/download/{fileDir}/{fileName}
 * @method get
 * @description
 */

export function filesDownloadFileDirFileNameUsingGet(options:FilesDownloadFileDirFileNameUsingGet.Options):Promise<FilesDownloadFileDirFileNameUsingGet.Result> {
  return cimRequest({
    url:'/files/download/{fileDir}/{fileName}',
    method:'get',
    ...options,
  });
}

/**
 * @tag file-convert-controller
 * @summary uploadMultipleFiles
 * @url /files/uploadMultipleFiles
 * @method post
 * @description
 */

export module FilesUploadMultipleFilesUsingPost {
  export type Operation = paths['/files/uploadMultipleFiles']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag file-convert-controller
 * @summary uploadMultipleFiles
 * @url /files/uploadMultipleFiles
 * @method post
 * @description
 */

export function filesUploadMultipleFilesUsingPost(options:FilesUploadMultipleFilesUsingPost.Options):Promise<FilesUploadMultipleFilesUsingPost.Result> {
  return cimRequest({
    url:'/files/uploadMultipleFiles',
    method:'post',
    ...options,
  });
}

/**
 * @tag file-convert-controller
 * @summary uploadMultipleFiles
 * @url /files/uploadZipFile
 * @method post
 * @description
 */

export module FilesUploadZipFileUsingPost {
  export type Operation = paths['/files/uploadZipFile']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag file-convert-controller
 * @summary uploadMultipleFiles
 * @url /files/uploadZipFile
 * @method post
 * @description
 */

export function filesUploadZipFileUsingPost(options:FilesUploadZipFileUsingPost.Options):Promise<FilesUploadZipFileUsingPost.Result> {
  return cimRequest({
    url:'/files/uploadZipFile',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary 上传文件
 * @url /fileSetting/addMultipleFiles
 * @method post
 * @description 上传文件
 */

export module FileSettingAddMultipleFilesUsingPost {
  export type Operation = paths['/fileSetting/addMultipleFiles']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary 上传文件
 * @url /fileSetting/addMultipleFiles
 * @method post
 * @description 上传文件
 */

export function fileSettingAddMultipleFilesUsingPost(options:FileSettingAddMultipleFilesUsingPost.Options):Promise<FileSettingAddMultipleFilesUsingPost.Result> {
  return cimRequest({
    url:'/fileSetting/addMultipleFiles',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary 根据id删除文件
 * @url /fileSetting/delete/{id}
 * @method delete
 * @description 根据id删除文件
 */

export module FileSettingDeleteIdUsingDelete {
  export type Operation = paths['/fileSetting/delete/{id}']['delete'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary 根据id删除文件
 * @url /fileSetting/delete/{id}
 * @method delete
 * @description 根据id删除文件
 */

export function fileSettingDeleteIdUsingDelete(options:FileSettingDeleteIdUsingDelete.Options):Promise<FileSettingDeleteIdUsingDelete.Result> {
  return cimRequest({
    url:'/fileSetting/delete/{id}',
    method:'delete',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary downloadFile
 * @url /fileSetting/download/{fileDir}/{fileName}
 * @method get
 * @description
 */

export module FileSettingDownloadFileDirFileNameUsingGet {
  export type Operation = paths['/fileSetting/download/{fileDir}/{fileName}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary downloadFile
 * @url /fileSetting/download/{fileDir}/{fileName}
 * @method get
 * @description
 */

export function fileSettingDownloadFileDirFileNameUsingGet(options:FileSettingDownloadFileDirFileNameUsingGet.Options):Promise<FileSettingDownloadFileDirFileNameUsingGet.Result> {
  return cimRequest({
    url:'/fileSetting/download/{fileDir}/{fileName}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary 下载图片文件
 * @url /fileSetting/downloadPhoto/{fileDir}/{fileName}
 * @method get
 * @description 下载图片文件
 */

export module FileSettingDownloadPhotoFileDirFileNameUsingGet {
  export type Operation = paths['/fileSetting/downloadPhoto/{fileDir}/{fileName}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['image/png'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary 下载图片文件
 * @url /fileSetting/downloadPhoto/{fileDir}/{fileName}
 * @method get
 * @description 下载图片文件
 */

export function fileSettingDownloadPhotoFileDirFileNameUsingGet(options:FileSettingDownloadPhotoFileDirFileNameUsingGet.Options):Promise<FileSettingDownloadPhotoFileDirFileNameUsingGet.Result> {
  return cimRequest({
    url:'/fileSetting/downloadPhoto/{fileDir}/{fileName}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary 根据id查询文件
 * @url /fileSetting/getById/{id}
 * @method get
 * @description 根据id查询文件
 */

export module FileSettingGetByIdIdUsingGet {
  export type Operation = paths['/fileSetting/getById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary 根据id查询文件
 * @url /fileSetting/getById/{id}
 * @method get
 * @description 根据id查询文件
 */

export function fileSettingGetByIdIdUsingGet(options:FileSettingGetByIdIdUsingGet.Options):Promise<FileSettingGetByIdIdUsingGet.Result> {
  return cimRequest({
    url:'/fileSetting/getById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary 查询文件列表
 * @url /fileSetting/list
 * @method post
 * @description 查询文件列表
 */

export module FileSettingListUsingPost {
  export type Operation = paths['/fileSetting/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary 查询文件列表
 * @url /fileSetting/list
 * @method post
 * @description 查询文件列表
 */

export function fileSettingListUsingPost(options:FileSettingListUsingPost.Options):Promise<FileSettingListUsingPost.Result> {
  return cimRequest({
    url:'/fileSetting/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary 更新文件
 * @url /fileSetting/update
 * @method put
 * @description 更新文件
 */

export module FileSettingUpdateUsingPut {
  export type Operation = paths['/fileSetting/update']['put'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary 更新文件
 * @url /fileSetting/update
 * @method put
 * @description 更新文件
 */

export function fileSettingUpdateUsingPut(options:FileSettingUpdateUsingPut.Options):Promise<FileSettingUpdateUsingPut.Result> {
  return cimRequest({
    url:'/fileSetting/update',
    method:'put',
    ...options,
  });
}

/**
 * @tag 业务模块-文件系统
 * @summary 上传图片文件
 * @url /fileSetting/uploadFile
 * @method post
 * @description 上传图片文件
 */

export module FileSettingUploadFileUsingPost {
  export type Operation = paths['/fileSetting/uploadFile']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-文件系统
 * @summary 上传图片文件
 * @url /fileSetting/uploadFile
 * @method post
 * @description 上传图片文件
 */

export function fileSettingUploadFileUsingPost(options:FileSettingUploadFileUsingPost.Options):Promise<FileSettingUploadFileUsingPost.Result> {
  return cimRequest({
    url:'/fileSetting/uploadFile',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-图层接口
 * @summary 删除
 * @url /layerInfo/deleteById/{id}
 * @method post
 * @description 删除
 */

export module LayerInfoDeleteByIdIdUsingPost {
  export type Operation = paths['/layerInfo/deleteById/{id}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-图层接口
 * @summary 删除
 * @url /layerInfo/deleteById/{id}
 * @method post
 * @description 删除
 */

export function layerInfoDeleteByIdIdUsingPost(options:LayerInfoDeleteByIdIdUsingPost.Options):Promise<LayerInfoDeleteByIdIdUsingPost.Result> {
  return cimRequest({
    url:'/layerInfo/deleteById/{id}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-图层接口
 * @summary 获取图层树
 * @url /layerInfo/getLayerInfo
 * @method get
 * @description 获取图层树
 */

export module LayerInfoGetLayerInfoUsingGet {
  export type Operation = paths['/layerInfo/getLayerInfo']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 业务模块-图层接口
 * @summary 获取图层树
 * @url /layerInfo/getLayerInfo
 * @method get
 * @description 获取图层树
 */

export function layerInfoGetLayerInfoUsingGet(options:LayerInfoGetLayerInfoUsingGet.Options):Promise<LayerInfoGetLayerInfoUsingGet.Result> {
  return cimRequest({
    url:'/layerInfo/getLayerInfo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-图层接口
 * @summary 获取图层类型配置
 * @url /layerInfo/getLayerTypeInfo
 * @method get
 * @description 获取图层类型配置
 */

export module LayerInfoGetLayerTypeInfoUsingGet {
  export type Operation = paths['/layerInfo/getLayerTypeInfo']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 业务模块-图层接口
 * @summary 获取图层类型配置
 * @url /layerInfo/getLayerTypeInfo
 * @method get
 * @description 获取图层类型配置
 */

export function layerInfoGetLayerTypeInfoUsingGet(options:LayerInfoGetLayerTypeInfoUsingGet.Options):Promise<LayerInfoGetLayerTypeInfoUsingGet.Result> {
  return cimRequest({
    url:'/layerInfo/getLayerTypeInfo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-图层接口
 * @summary 获取用户系统设置信息
 * @url /layerInfo/getSystemSettingsByUserCode
 * @method get
 * @description 获取用户系统设置信息
 */

export module LayerInfoGetSystemSettingsByUserCodeUsingGet {
  export type Operation = paths['/layerInfo/getSystemSettingsByUserCode']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 业务模块-图层接口
 * @summary 获取用户系统设置信息
 * @url /layerInfo/getSystemSettingsByUserCode
 * @method get
 * @description 获取用户系统设置信息
 */

export function layerInfoGetSystemSettingsByUserCodeUsingGet(options:LayerInfoGetSystemSettingsByUserCodeUsingGet.Options):Promise<LayerInfoGetSystemSettingsByUserCodeUsingGet.Result> {
  return cimRequest({
    url:'/layerInfo/getSystemSettingsByUserCode',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-图层接口
 * @summary 新增/更新图层
 * @url /layerInfo/saveOrUpdateLayerInfo
 * @method post
 * @description 新增/更新图层
 */

export module LayerInfoSaveOrUpdateLayerInfoUsingPost {
  export type Operation = paths['/layerInfo/saveOrUpdateLayerInfo']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-图层接口
 * @summary 新增/更新图层
 * @url /layerInfo/saveOrUpdateLayerInfo
 * @method post
 * @description 新增/更新图层
 */

export function layerInfoSaveOrUpdateLayerInfoUsingPost(options:LayerInfoSaveOrUpdateLayerInfoUsingPost.Options):Promise<LayerInfoSaveOrUpdateLayerInfoUsingPost.Result> {
  return cimRequest({
    url:'/layerInfo/saveOrUpdateLayerInfo',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-图层接口
 * @summary 新增/更新图层类型
 * @url /layerInfo/saveOrUpdateLayerType
 * @method post
 * @description 新增/更新图层类型
 */

export module LayerInfoSaveOrUpdateLayerTypeUsingPost {
  export type Operation = paths['/layerInfo/saveOrUpdateLayerType']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-图层接口
 * @summary 新增/更新图层类型
 * @url /layerInfo/saveOrUpdateLayerType
 * @method post
 * @description 新增/更新图层类型
 */

export function layerInfoSaveOrUpdateLayerTypeUsingPost(options:LayerInfoSaveOrUpdateLayerTypeUsingPost.Options):Promise<LayerInfoSaveOrUpdateLayerTypeUsingPost.Result> {
  return cimRequest({
    url:'/layerInfo/saveOrUpdateLayerType',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-图层接口
 * @summary 新增/更新系统设置
 * @url /layerInfo/saveOrUpdateSystemSettings
 * @method post
 * @description 新增/更新系统设置
 */

export module LayerInfoSaveOrUpdateSystemSettingsUsingPost {
  export type Operation = paths['/layerInfo/saveOrUpdateSystemSettings']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-图层接口
 * @summary 新增/更新系统设置
 * @url /layerInfo/saveOrUpdateSystemSettings
 * @method post
 * @description 新增/更新系统设置
 */

export function layerInfoSaveOrUpdateSystemSettingsUsingPost(options:LayerInfoSaveOrUpdateSystemSettingsUsingPost.Options):Promise<LayerInfoSaveOrUpdateSystemSettingsUsingPost.Result> {
  return cimRequest({
    url:'/layerInfo/saveOrUpdateSystemSettings',
    method:'post',
    ...options,
  });
}

/**
 * @tag 审计模块-接口
 * @summary 分页查询操作日志
 * @url /opLog/listPage
 * @method post
 * @description 分页查询操作日志
 */

export module OpLogListPageUsingPost {
  export type Operation = paths['/opLog/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 审计模块-接口
 * @summary 分页查询操作日志
 * @url /opLog/listPage
 * @method post
 * @description 分页查询操作日志
 */

export function opLogListPageUsingPost(options:OpLogListPageUsingPost.Options):Promise<OpLogListPageUsingPost.Result> {
  return cimRequest({
    url:'/opLog/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 注册接口
 * @summary register
 * @url /registerUser/config
 * @method get
 * @description
 */

export module RegisterUserConfigUsingGet {
  export type Operation = paths['/registerUser/config']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 注册接口
 * @summary register
 * @url /registerUser/config
 * @method get
 * @description
 */

export function registerUserConfigUsingGet(options:RegisterUserConfigUsingGet.Options):Promise<RegisterUserConfigUsingGet.Result> {
  return cimRequest({
    url:'/registerUser/config',
    method:'get',
    ...options,
  });
}

/**
 * @tag 注册接口
 * @summary register
 * @url /registerUser/user
 * @method get
 * @description
 */

export module RegisterUserUserUsingGet {
  export type Operation = paths['/registerUser/user']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 注册接口
 * @summary register
 * @url /registerUser/user
 * @method get
 * @description
 */

export function registerUserUserUsingGet(options:RegisterUserUserUsingGet.Options):Promise<RegisterUserUserUsingGet.Result> {
  return cimRequest({
    url:'/registerUser/user',
    method:'get',
    ...options,
  });
}

/**
 * @tag route-controller
 * @summary callExternalApi
 * @url /route/api
 * @method get
 * @description
 */

export module RouteApiUsingGet {
  export type Operation = paths['/route/api']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['image/png'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag route-controller
 * @summary callExternalApi
 * @url /route/api
 * @method get
 * @description
 */

export function routeApiUsingGet(options:RouteApiUsingGet.Options):Promise<RouteApiUsingGet.Result> {
  return cimRequest({
    url:'/route/api',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-标绘接口
 * @summary 删除标绘信息
 * @url /sceneInfo/deleteById/{id}
 * @method get
 * @description 删除
 */

export module SceneInfoDeleteByIdIdUsingGet {
  export type Operation = paths['/sceneInfo/deleteById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-标绘接口
 * @summary 删除标绘信息
 * @url /sceneInfo/deleteById/{id}
 * @method get
 * @description 删除
 */

export function sceneInfoDeleteByIdIdUsingGet(options:SceneInfoDeleteByIdIdUsingGet.Options):Promise<SceneInfoDeleteByIdIdUsingGet.Result> {
  return cimRequest({
    url:'/sceneInfo/deleteById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-标绘接口
 * @summary 导出标绘
 * @url /sceneInfo/export/json/{id}
 * @method get
 * @description 导出标绘信息
 */

export module SceneInfoExportJsonIdUsingGet {
  export type Operation = paths['/sceneInfo/export/json/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-标绘接口
 * @summary 导出标绘
 * @url /sceneInfo/export/json/{id}
 * @method get
 * @description 导出标绘信息
 */

export function sceneInfoExportJsonIdUsingGet(options:SceneInfoExportJsonIdUsingGet.Options):Promise<SceneInfoExportJsonIdUsingGet.Result> {
  return cimRequest({
    url:'/sceneInfo/export/json/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-标绘接口
 * @summary 根据id获取标绘
 * @url /sceneInfo/getSceneInfo/{id}
 * @method get
 * @description 获取标绘类型配置
 */

export module SceneInfoGetSceneInfoIdUsingGet {
  export type Operation = paths['/sceneInfo/getSceneInfo/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-标绘接口
 * @summary 根据id获取标绘
 * @url /sceneInfo/getSceneInfo/{id}
 * @method get
 * @description 获取标绘类型配置
 */

export function sceneInfoGetSceneInfoIdUsingGet(options:SceneInfoGetSceneInfoIdUsingGet.Options):Promise<SceneInfoGetSceneInfoIdUsingGet.Result> {
  return cimRequest({
    url:'/sceneInfo/getSceneInfo/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-标绘接口
 * @summary 导入标绘
 * @url /sceneInfo/import/json
 * @method post
 * @description 导入标绘
 */

export module SceneInfoImportJsonUsingPost {
  export type Operation = paths['/sceneInfo/import/json']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-标绘接口
 * @summary 导入标绘
 * @url /sceneInfo/import/json
 * @method post
 * @description 导入标绘
 */

export function sceneInfoImportJsonUsingPost(options:SceneInfoImportJsonUsingPost.Options):Promise<SceneInfoImportJsonUsingPost.Result> {
  return cimRequest({
    url:'/sceneInfo/import/json',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-标绘接口
 * @summary 分页查询标绘信息
 * @url /sceneInfo/listPage
 * @method post
 * @description 分页查询标绘信息
 */

export module SceneInfoListPageUsingPost {
  export type Operation = paths['/sceneInfo/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-标绘接口
 * @summary 分页查询标绘信息
 * @url /sceneInfo/listPage
 * @method post
 * @description 分页查询标绘信息
 */

export function sceneInfoListPageUsingPost(options:SceneInfoListPageUsingPost.Options):Promise<SceneInfoListPageUsingPost.Result> {
  return cimRequest({
    url:'/sceneInfo/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-标绘接口
 * @summary 新增/更新标绘
 * @url /sceneInfo/saveOrUpdateSceneInfo
 * @method post
 * @description 新增/更新标绘
 */

export module SceneInfoSaveOrUpdateSceneInfoUsingPost {
  export type Operation = paths['/sceneInfo/saveOrUpdateSceneInfo']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-标绘接口
 * @summary 新增/更新标绘
 * @url /sceneInfo/saveOrUpdateSceneInfo
 * @method post
 * @description 新增/更新标绘
 */

export function sceneInfoSaveOrUpdateSceneInfoUsingPost(options:SceneInfoSaveOrUpdateSceneInfoUsingPost.Options):Promise<SceneInfoSaveOrUpdateSceneInfoUsingPost.Result> {
  return cimRequest({
    url:'/sceneInfo/saveOrUpdateSceneInfo',
    method:'post',
    ...options,
  });
}

/**
 * @tag sichuan-sso-controller
 * @summary loginAuth
 * @url /api/sso/loginAuth
 * @method get
 * @description
 */

export module ApiSsoLoginAuthUsingGet {
  export type Operation = paths['/api/sso/loginAuth']['get'];
  export type Result = any;
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag sichuan-sso-controller
 * @summary loginAuth
 * @url /api/sso/loginAuth
 * @method get
 * @description
 */

export function apiSsoLoginAuthUsingGet(options:ApiSsoLoginAuthUsingGet.Options):Promise<ApiSsoLoginAuthUsingGet.Result> {
  return cimRequest({
    url:'/api/sso/loginAuth',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 查询所有机构数据
 * @url /system/systemDept/list
 * @method post
 * @description 查询所有机构数据
 */

export module SystemSystemDeptListUsingPost {
  export type Operation = paths['/system/systemDept/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 查询所有机构数据
 * @url /system/systemDept/list
 * @method post
 * @description 查询所有机构数据
 */

export function systemSystemDeptListUsingPost(options:SystemSystemDeptListUsingPost.Options):Promise<SystemSystemDeptListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDept/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 批量删除机构数据
 * @url /system/systemDept/removeByIdList
 * @method post
 * @description 批量删除机构数据
 */

export module SystemSystemDeptRemoveByIdListUsingPost {
  export type Operation = paths['/system/systemDept/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 批量删除机构数据
 * @url /system/systemDept/removeByIdList
 * @method post
 * @description 批量删除机构数据
 */

export function systemSystemDeptRemoveByIdListUsingPost(options:SystemSystemDeptRemoveByIdListUsingPost.Options):Promise<SystemSystemDeptRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDept/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 保存机构信息
 * @url /system/systemDept/save
 * @method post
 * @description 保存机构信息
 */

export module SystemSystemDeptSaveUsingPost {
  export type Operation = paths['/system/systemDept/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 保存机构信息
 * @url /system/systemDept/save
 * @method post
 * @description 保存机构信息
 */

export function systemSystemDeptSaveUsingPost(options:SystemSystemDeptSaveUsingPost.Options):Promise<SystemSystemDeptSaveUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDept/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 修改机构信息
 * @url /system/systemDept/update
 * @method post
 * @description 修改资源信息
 */

export module SystemSystemDeptUpdateUsingPost {
  export type Operation = paths['/system/systemDept/update']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-机构信息相关接口
 * @summary 修改机构信息
 * @url /system/systemDept/update
 * @method post
 * @description 修改资源信息
 */

export function systemSystemDeptUpdateUsingPost(options:SystemSystemDeptUpdateUsingPost.Options):Promise<SystemSystemDeptUpdateUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDept/update',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 查询所有字典数据
 * @url /system/systemDict/list
 * @method post
 * @description 查询所有字典数据
 */

export module SystemSystemDictListUsingPost {
  export type Operation = paths['/system/systemDict/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 查询所有字典数据
 * @url /system/systemDict/list
 * @method post
 * @description 查询所有字典数据
 */

export function systemSystemDictListUsingPost(options:SystemSystemDictListUsingPost.Options):Promise<SystemSystemDictListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDict/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 查询字典分页数据
 * @url /system/systemDict/listPage
 * @method post
 * @description 查询字典分页数据
 */

export module SystemSystemDictListPageUsingPost {
  export type Operation = paths['/system/systemDict/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 查询字典分页数据
 * @url /system/systemDict/listPage
 * @method post
 * @description 查询字典分页数据
 */

export function systemSystemDictListPageUsingPost(options:SystemSystemDictListPageUsingPost.Options):Promise<SystemSystemDictListPageUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDict/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 批量删除字典数据
 * @url /system/systemDict/removeByIdList
 * @method post
 * @description 批量删除字典数据
 */

export module SystemSystemDictRemoveByIdListUsingPost {
  export type Operation = paths['/system/systemDict/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 批量删除字典数据
 * @url /system/systemDict/removeByIdList
 * @method post
 * @description 批量删除字典数据
 */

export function systemSystemDictRemoveByIdListUsingPost(options:SystemSystemDictRemoveByIdListUsingPost.Options):Promise<SystemSystemDictRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDict/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 保存字典信息
 * @url /system/systemDict/save
 * @method post
 * @description 保存字典信息
 */

export module SystemSystemDictSaveUsingPost {
  export type Operation = paths['/system/systemDict/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 保存字典信息
 * @url /system/systemDict/save
 * @method post
 * @description 保存字典信息
 */

export function systemSystemDictSaveUsingPost(options:SystemSystemDictSaveUsingPost.Options):Promise<SystemSystemDictSaveUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDict/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 修改字典信息
 * @url /system/systemDict/update
 * @method post
 * @description 修改字典信息
 */

export module SystemSystemDictUpdateUsingPost {
  export type Operation = paths['/system/systemDict/update']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典信息相关接口
 * @summary 修改字典信息
 * @url /system/systemDict/update
 * @method post
 * @description 修改字典信息
 */

export function systemSystemDictUpdateUsingPost(options:SystemSystemDictUpdateUsingPost.Options):Promise<SystemSystemDictUpdateUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDict/update',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 查询所有字典数据
 * @url /system/systemDictData/list
 * @method post
 * @description 查询所有字典数据
 */

export module SystemSystemDictDataListUsingPost {
  export type Operation = paths['/system/systemDictData/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 查询所有字典数据
 * @url /system/systemDictData/list
 * @method post
 * @description 查询所有字典数据
 */

export function systemSystemDictDataListUsingPost(options:SystemSystemDictDataListUsingPost.Options):Promise<SystemSystemDictDataListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDictData/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 批量删除字典数据
 * @url /system/systemDictData/removeByIdList
 * @method post
 * @description 批量删除字典数据
 */

export module SystemSystemDictDataRemoveByIdListUsingPost {
  export type Operation = paths['/system/systemDictData/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 批量删除字典数据
 * @url /system/systemDictData/removeByIdList
 * @method post
 * @description 批量删除字典数据
 */

export function systemSystemDictDataRemoveByIdListUsingPost(options:SystemSystemDictDataRemoveByIdListUsingPost.Options):Promise<SystemSystemDictDataRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDictData/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 保存字典数据信息
 * @url /system/systemDictData/save
 * @method post
 * @description 保存字典数据信息
 */

export module SystemSystemDictDataSaveUsingPost {
  export type Operation = paths['/system/systemDictData/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 保存字典数据信息
 * @url /system/systemDictData/save
 * @method post
 * @description 保存字典数据信息
 */

export function systemSystemDictDataSaveUsingPost(options:SystemSystemDictDataSaveUsingPost.Options):Promise<SystemSystemDictDataSaveUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDictData/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 修改字典数据信息
 * @url /system/systemDictData/update
 * @method post
 * @description 修改字典数据信息
 */

export module SystemSystemDictDataUpdateUsingPost {
  export type Operation = paths['/system/systemDictData/update']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-字典数据相关接口
 * @summary 修改字典数据信息
 * @url /system/systemDictData/update
 * @method post
 * @description 修改字典数据信息
 */

export function systemSystemDictDataUpdateUsingPost(options:SystemSystemDictDataUpdateUsingPost.Options):Promise<SystemSystemDictDataUpdateUsingPost.Result> {
  return cimRequest({
    url:'/system/systemDictData/update',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询附件下载
 * @url /system/systemFile/download/{id}
 * @method get
 * @description 查询附件下载
 */

export module SystemSystemFileDownloadIdUsingGet {
  export type Operation = paths['/system/systemFile/download/{id}']['get'];
  export type Result = any;
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询附件下载
 * @url /system/systemFile/download/{id}
 * @method get
 * @description 查询附件下载
 */

export function systemSystemFileDownloadIdUsingGet(options:SystemSystemFileDownloadIdUsingGet.Options):Promise<SystemSystemFileDownloadIdUsingGet.Result> {
  return cimRequest({
    url:'/system/systemFile/download/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询多个附件下载,多个文件会自动打包成ZIP
 * @url /system/systemFile/downloadFiles
 * @method get
 * @description 查询多个附件下载,多个文件会自动打包成ZIP
 */

export module SystemSystemFileDownloadFilesUsingGet {
  export type Operation = paths['/system/systemFile/downloadFiles']['get'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询多个附件下载,多个文件会自动打包成ZIP
 * @url /system/systemFile/downloadFiles
 * @method get
 * @description 查询多个附件下载,多个文件会自动打包成ZIP
 */

export function systemSystemFileDownloadFilesUsingGet(options:SystemSystemFileDownloadFilesUsingGet.Options):Promise<SystemSystemFileDownloadFilesUsingGet.Result> {
  return cimRequest({
    url:'/system/systemFile/downloadFiles',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 下载项目本地模板附件
 * @url /system/systemFile/downloadLocalFile
 * @method get
 * @description 通过文件名称下载项目本地模板附件
 */

export module SystemSystemFileDownloadLocalFileUsingGet {
  export type Operation = paths['/system/systemFile/downloadLocalFile']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 下载项目本地模板附件
 * @url /system/systemFile/downloadLocalFile
 * @method get
 * @description 通过文件名称下载项目本地模板附件
 */

export function systemSystemFileDownloadLocalFileUsingGet(options:SystemSystemFileDownloadLocalFileUsingGet.Options):Promise<SystemSystemFileDownloadLocalFileUsingGet.Result> {
  return cimRequest({
    url:'/system/systemFile/downloadLocalFile',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询所有附件数据
 * @url /system/systemFile/getList
 * @method post
 * @description 查询所有附件数据
 */

export module SystemSystemFileGetListUsingPost {
  export type Operation = paths['/system/systemFile/getList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询所有附件数据
 * @url /system/systemFile/getList
 * @method post
 * @description 查询所有附件数据
 */

export function systemSystemFileGetListUsingPost(options:SystemSystemFileGetListUsingPost.Options):Promise<SystemSystemFileGetListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemFile/getList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询附件列表
 * @url /system/systemFile/list
 * @method post
 * @description 查询附件列表
 */

export module SystemSystemFileListUsingPost {
  export type Operation = paths['/system/systemFile/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询附件列表
 * @url /system/systemFile/list
 * @method post
 * @description 查询附件列表
 */

export function systemSystemFileListUsingPost(options:SystemSystemFileListUsingPost.Options):Promise<SystemSystemFileListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemFile/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询附件分页数据
 * @url /system/systemFile/listPage
 * @method post
 * @description 查询附件分页数据
 */

export module SystemSystemFileListPageUsingPost {
  export type Operation = paths['/system/systemFile/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 查询附件分页数据
 * @url /system/systemFile/listPage
 * @method post
 * @description 查询附件分页数据
 */

export function systemSystemFileListPageUsingPost(options:SystemSystemFileListPageUsingPost.Options):Promise<SystemSystemFileListPageUsingPost.Result> {
  return cimRequest({
    url:'/system/systemFile/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 批量删除附件数据
 * @url /system/systemFile/removeByIdList
 * @method post
 * @description 批量删除附件数据
 */

export module SystemSystemFileRemoveByIdListUsingPost {
  export type Operation = paths['/system/systemFile/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 批量删除附件数据
 * @url /system/systemFile/removeByIdList
 * @method post
 * @description 批量删除附件数据
 */

export function systemSystemFileRemoveByIdListUsingPost(options:SystemSystemFileRemoveByIdListUsingPost.Options):Promise<SystemSystemFileRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemFile/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 附件上传
 * @url /system/systemFile/upload
 * @method post
 * @description 附件上传
 */

export module SystemSystemFileUploadUsingPost {
  export type Operation = paths['/system/systemFile/upload']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-附件信息相关接口
 * @summary 附件上传
 * @url /system/systemFile/upload
 * @method post
 * @description 附件上传
 */

export function systemSystemFileUploadUsingPost(options:SystemSystemFileUploadUsingPost.Options):Promise<SystemSystemFileUploadUsingPost.Result> {
  return cimRequest({
    url:'/system/systemFile/upload',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-日志表相关接口
 * @summary 分页查询信息
 * @url /system/systemLog/listPage
 * @method post
 * @description 分页查询信息
 */

export module SystemSystemLogListPageUsingPost {
  export type Operation = paths['/system/systemLog/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-日志表相关接口
 * @summary 分页查询信息
 * @url /system/systemLog/listPage
 * @method post
 * @description 分页查询信息
 */

export function systemSystemLogListPageUsingPost(options:SystemSystemLogListPageUsingPost.Options):Promise<SystemSystemLogListPageUsingPost.Result> {
  return cimRequest({
    url:'/system/systemLog/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-日志表相关接口
 * @summary 保存日志信息
 * @url /system/systemLog/save
 * @method post
 * @description 保存日志信息
 */

export module SystemSystemLogSaveUsingPost {
  export type Operation = paths['/system/systemLog/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-日志表相关接口
 * @summary 保存日志信息
 * @url /system/systemLog/save
 * @method post
 * @description 保存日志信息
 */

export function systemSystemLogSaveUsingPost(options:SystemSystemLogSaveUsingPost.Options):Promise<SystemSystemLogSaveUsingPost.Result> {
  return cimRequest({
    url:'/system/systemLog/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-日志表相关接口
 * @summary 根据日志ID查询信息
 * @url /system/systemLogExtend/getByLogId/{logId}
 * @method get
 * @description 根据日志ID查询信息
 */

export module SystemSystemLogExtendGetByLogIdLogIdUsingGet {
  export type Operation = paths['/system/systemLogExtend/getByLogId/{logId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 系统管理模块-日志表相关接口
 * @summary 根据日志ID查询信息
 * @url /system/systemLogExtend/getByLogId/{logId}
 * @method get
 * @description 根据日志ID查询信息
 */

export function systemSystemLogExtendGetByLogIdLogIdUsingGet(options:SystemSystemLogExtendGetByLogIdLogIdUsingGet.Options):Promise<SystemSystemLogExtendGetByLogIdLogIdUsingGet.Result> {
  return cimRequest({
    url:'/system/systemLogExtend/getByLogId/{logId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 获取全部模块
 * @url /system/systemModule/getAll
 * @method get
 * @description 获取全部模块
 */

export module SystemSystemModuleGetAllUsingGet {
  export type Operation = paths['/system/systemModule/getAll']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 获取全部模块
 * @url /system/systemModule/getAll
 * @method get
 * @description 获取全部模块
 */

export function systemSystemModuleGetAllUsingGet(options:SystemSystemModuleGetAllUsingGet.Options):Promise<SystemSystemModuleGetAllUsingGet.Result> {
  return cimRequest({
    url:'/system/systemModule/getAll',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 根据模块id获取资源
 * @url /system/systemModule/getResources
 * @method get
 * @description 根据模块id获取资源
 */

export module SystemSystemModuleGetResourcesUsingGet {
  export type Operation = paths['/system/systemModule/getResources']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 根据模块id获取资源
 * @url /system/systemModule/getResources
 * @method get
 * @description 根据模块id获取资源
 */

export function systemSystemModuleGetResourcesUsingGet(options:SystemSystemModuleGetResourcesUsingGet.Options):Promise<SystemSystemModuleGetResourcesUsingGet.Result> {
  return cimRequest({
    url:'/system/systemModule/getResources',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 分页查询
 * @url /system/systemModule/listPage
 * @method post
 * @description 分页查询
 */

export module SystemSystemModuleListPageUsingPost {
  export type Operation = paths['/system/systemModule/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 分页查询
 * @url /system/systemModule/listPage
 * @method post
 * @description 分页查询
 */

export function systemSystemModuleListPageUsingPost(options:SystemSystemModuleListPageUsingPost.Options):Promise<SystemSystemModuleListPageUsingPost.Result> {
  return cimRequest({
    url:'/system/systemModule/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 设置目录
 * @url /system/systemModule/listResourcesByModuleId/{id}
 * @method get
 * @description 设置目录
 */

export module SystemSystemModuleListResourcesByModuleIdIdUsingGet {
  export type Operation = paths['/system/systemModule/listResourcesByModuleId/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 设置目录
 * @url /system/systemModule/listResourcesByModuleId/{id}
 * @method get
 * @description 设置目录
 */

export function systemSystemModuleListResourcesByModuleIdIdUsingGet(options:SystemSystemModuleListResourcesByModuleIdIdUsingGet.Options):Promise<SystemSystemModuleListResourcesByModuleIdIdUsingGet.Result> {
  return cimRequest({
    url:'/system/systemModule/listResourcesByModuleId/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 删除
 * @url /system/systemModule/removeByIdList
 * @method post
 * @description 删除
 */

export module SystemSystemModuleRemoveByIdListUsingPost {
  export type Operation = paths['/system/systemModule/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 删除
 * @url /system/systemModule/removeByIdList
 * @method post
 * @description 删除
 */

export function systemSystemModuleRemoveByIdListUsingPost(options:SystemSystemModuleRemoveByIdListUsingPost.Options):Promise<SystemSystemModuleRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemModule/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 保存资源信息
 * @url /system/systemModule/save
 * @method post
 * @description 保存资源信息
 */

export module SystemSystemModuleSaveUsingPost {
  export type Operation = paths['/system/systemModule/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 保存资源信息
 * @url /system/systemModule/save
 * @method post
 * @description 保存资源信息
 */

export function systemSystemModuleSaveUsingPost(options:SystemSystemModuleSaveUsingPost.Options):Promise<SystemSystemModuleSaveUsingPost.Result> {
  return cimRequest({
    url:'/system/systemModule/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 设置目录
 * @url /system/systemModule/settingResources
 * @method post
 * @description 设置目录
 */

export module SystemSystemModuleSettingResourcesUsingPost {
  export type Operation = paths['/system/systemModule/settingResources']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 设置目录
 * @url /system/systemModule/settingResources
 * @method post
 * @description 设置目录
 */

export function systemSystemModuleSettingResourcesUsingPost(options:SystemSystemModuleSettingResourcesUsingPost.Options):Promise<SystemSystemModuleSettingResourcesUsingPost.Result> {
  return cimRequest({
    url:'/system/systemModule/settingResources',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 修改资源信息
 * @url /system/systemModule/update
 * @method post
 * @description 修改资源信息
 */

export module SystemSystemModuleUpdateUsingPost {
  export type Operation = paths['/system/systemModule/update']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统模块
 * @summary 修改资源信息
 * @url /system/systemModule/update
 * @method post
 * @description 修改资源信息
 */

export function systemSystemModuleUpdateUsingPost(options:SystemSystemModuleUpdateUsingPost.Options):Promise<SystemSystemModuleUpdateUsingPost.Result> {
  return cimRequest({
    url:'/system/systemModule/update',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 查询所有资源数据
 * @url /system/systemResources/list
 * @method post
 * @description 查询所有资源数据
 */

export module SystemSystemResourcesListUsingPost {
  export type Operation = paths['/system/systemResources/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 查询所有资源数据
 * @url /system/systemResources/list
 * @method post
 * @description 查询所有资源数据
 */

export function systemSystemResourcesListUsingPost(options:SystemSystemResourcesListUsingPost.Options):Promise<SystemSystemResourcesListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemResources/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 批量删除资源数据
 * @url /system/systemResources/removeByIdList
 * @method post
 * @description 批量删除资源数据
 */

export module SystemSystemResourcesRemoveByIdListUsingPost {
  export type Operation = paths['/system/systemResources/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 批量删除资源数据
 * @url /system/systemResources/removeByIdList
 * @method post
 * @description 批量删除资源数据
 */

export function systemSystemResourcesRemoveByIdListUsingPost(options:SystemSystemResourcesRemoveByIdListUsingPost.Options):Promise<SystemSystemResourcesRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemResources/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 保存资源信息
 * @url /system/systemResources/save
 * @method post
 * @description 保存资源信息
 */

export module SystemSystemResourcesSaveUsingPost {
  export type Operation = paths['/system/systemResources/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 保存资源信息
 * @url /system/systemResources/save
 * @method post
 * @description 保存资源信息
 */

export function systemSystemResourcesSaveUsingPost(options:SystemSystemResourcesSaveUsingPost.Options):Promise<SystemSystemResourcesSaveUsingPost.Result> {
  return cimRequest({
    url:'/system/systemResources/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 修改资源信息
 * @url /system/systemResources/update
 * @method post
 * @description 修改资源信息
 */

export module SystemSystemResourcesUpdateUsingPost {
  export type Operation = paths['/system/systemResources/update']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-系统资源相关接口
 * @summary 修改资源信息
 * @url /system/systemResources/update
 * @method post
 * @description 修改资源信息
 */

export function systemSystemResourcesUpdateUsingPost(options:SystemSystemResourcesUpdateUsingPost.Options):Promise<SystemSystemResourcesUpdateUsingPost.Result> {
  return cimRequest({
    url:'/system/systemResources/update',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-角色相关接口
 * @summary 角色绑定权限
 * @url /system/systemRole/assignResource
 * @method post
 * @description 角色绑定权限
 */

export module SystemSystemRoleAssignResourceUsingPost {
  export type Operation = paths['/system/systemRole/assignResource']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-角色相关接口
 * @summary 角色绑定权限
 * @url /system/systemRole/assignResource
 * @method post
 * @description 角色绑定权限
 */

export function systemSystemRoleAssignResourceUsingPost(options:SystemSystemRoleAssignResourceUsingPost.Options):Promise<SystemSystemRoleAssignResourceUsingPost.Result> {
  return cimRequest({
    url:'/system/systemRole/assignResource',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 分配角色
 * @url /system/systemUser/assignRole
 * @method post
 * @description 分配角色
 */

export module SystemSystemUserAssignRoleUsingPost {
  export type Operation = paths['/system/systemUser/assignRole']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 分配角色
 * @url /system/systemUser/assignRole
 * @method post
 * @description 分配角色
 */

export function systemSystemUserAssignRoleUsingPost(options:SystemSystemUserAssignRoleUsingPost.Options):Promise<SystemSystemUserAssignRoleUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/assignRole',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 修改当前用户密码
 * @url /system/systemUser/changePassword
 * @method post
 * @description 修改当前用户密码
 */

export module SystemSystemUserChangePasswordUsingPost {
  export type Operation = paths['/system/systemUser/changePassword']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 修改当前用户密码
 * @url /system/systemUser/changePassword
 * @method post
 * @description 修改当前用户密码
 */

export function systemSystemUserChangePasswordUsingPost(options:SystemSystemUserChangePasswordUsingPost.Options):Promise<SystemSystemUserChangePasswordUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/changePassword',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 获取当前登录用户信息
 * @url /system/systemUser/getCurrUserInfo
 * @method get
 * @description 获取当前登录用户信息
 */

export module SystemSystemUserGetCurrUserInfoUsingGet {
  export type Operation = paths['/system/systemUser/getCurrUserInfo']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 获取当前登录用户信息
 * @url /system/systemUser/getCurrUserInfo
 * @method get
 * @description 获取当前登录用户信息
 */

export function systemSystemUserGetCurrUserInfoUsingGet(options:SystemSystemUserGetCurrUserInfoUsingGet.Options):Promise<SystemSystemUserGetCurrUserInfoUsingGet.Result> {
  return cimRequest({
    url:'/system/systemUser/getCurrUserInfo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 列表查询用户信息
 * @url /system/systemUser/getList
 * @method post
 * @description 列表查询用户信息
 */

export module SystemSystemUserGetListUsingPost {
  export type Operation = paths['/system/systemUser/getList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 列表查询用户信息
 * @url /system/systemUser/getList
 * @method post
 * @description 列表查询用户信息
 */

export function systemSystemUserGetListUsingPost(options:SystemSystemUserGetListUsingPost.Options):Promise<SystemSystemUserGetListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/getList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 用户角色数量统计
 * @url /system/systemUser/getUserRoleNumStatistic
 * @method get
 * @description 用户角色数量统计
 */

export module SystemSystemUserGetUserRoleNumStatisticUsingGet {
  export type Operation = paths['/system/systemUser/getUserRoleNumStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 用户角色数量统计
 * @url /system/systemUser/getUserRoleNumStatistic
 * @method get
 * @description 用户角色数量统计
 */

export function systemSystemUserGetUserRoleNumStatisticUsingGet(options:SystemSystemUserGetUserRoleNumStatisticUsingGet.Options):Promise<SystemSystemUserGetUserRoleNumStatisticUsingGet.Result> {
  return cimRequest({
    url:'/system/systemUser/getUserRoleNumStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 最后更新时间
 * @url /system/systemUser/lastUpdateTime
 * @method post
 * @description 最后更新时间
 */

export module SystemSystemUserLastUpdateTimeUsingPost {
  export type Operation = paths['/system/systemUser/lastUpdateTime']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 最后更新时间
 * @url /system/systemUser/lastUpdateTime
 * @method post
 * @description 最后更新时间
 */

export function systemSystemUserLastUpdateTimeUsingPost(options:SystemSystemUserLastUpdateTimeUsingPost.Options):Promise<SystemSystemUserLastUpdateTimeUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/lastUpdateTime',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 分页查询用户信息
 * @url /system/systemUser/listPage
 * @method post
 * @description 分页查询用户信息
 */

export module SystemSystemUserListPageUsingPost {
  export type Operation = paths['/system/systemUser/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 分页查询用户信息
 * @url /system/systemUser/listPage
 * @method post
 * @description 分页查询用户信息
 */

export function systemSystemUserListPageUsingPost(options:SystemSystemUserListPageUsingPost.Options):Promise<SystemSystemUserListPageUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 批量删除数据
 * @url /system/systemUser/removeByIdList
 * @method post
 * @description 批量删除数据
 */

export module SystemSystemUserRemoveByIdListUsingPost {
  export type Operation = paths['/system/systemUser/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 批量删除数据
 * @url /system/systemUser/removeByIdList
 * @method post
 * @description 批量删除数据
 */

export function systemSystemUserRemoveByIdListUsingPost(options:SystemSystemUserRemoveByIdListUsingPost.Options):Promise<SystemSystemUserRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 批量删除数据（预案系统调用）
 * @url /system/systemUser/removeUserFromSpd
 * @method get
 * @description 批量删除数据（预案系统调用）
 */

export module SystemSystemUserRemoveUserFromSpdUsingGet {
  export type Operation = paths['/system/systemUser/removeUserFromSpd']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 批量删除数据（预案系统调用）
 * @url /system/systemUser/removeUserFromSpd
 * @method get
 * @description 批量删除数据（预案系统调用）
 */

export function systemSystemUserRemoveUserFromSpdUsingGet(options:SystemSystemUserRemoveUserFromSpdUsingGet.Options):Promise<SystemSystemUserRemoveUserFromSpdUsingGet.Result> {
  return cimRequest({
    url:'/system/systemUser/removeUserFromSpd',
    method:'get',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 保存用户信息
 * @url /system/systemUser/save
 * @method post
 * @description 保存用户信息
 */

export module SystemSystemUserSaveUsingPost {
  export type Operation = paths['/system/systemUser/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 保存用户信息
 * @url /system/systemUser/save
 * @method post
 * @description 保存用户信息
 */

export function systemSystemUserSaveUsingPost(options:SystemSystemUserSaveUsingPost.Options):Promise<SystemSystemUserSaveUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 保存用户信息（预案系统调用）
 * @url /system/systemUser/saveUserFromSpd
 * @method post
 * @description 保存用户信息（预案系统调用）
 */

export module SystemSystemUserSaveUserFromSpdUsingPost {
  export type Operation = paths['/system/systemUser/saveUserFromSpd']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 保存用户信息（预案系统调用）
 * @url /system/systemUser/saveUserFromSpd
 * @method post
 * @description 保存用户信息（预案系统调用）
 */

export function systemSystemUserSaveUserFromSpdUsingPost(options:SystemSystemUserSaveUserFromSpdUsingPost.Options):Promise<SystemSystemUserSaveUserFromSpdUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/saveUserFromSpd',
    method:'post',
    ...options,
  });
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 更新用户信息（预案系统调用）
 * @url /system/systemUser/updateUserFromSpd
 * @method post
 * @description 更新用户信息（预案系统调用）
 */

export module SystemSystemUserUpdateUserFromSpdUsingPost {
  export type Operation = paths['/system/systemUser/updateUserFromSpd']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 系统管理模块-用户信息相关接口
 * @summary 更新用户信息（预案系统调用）
 * @url /system/systemUser/updateUserFromSpd
 * @method post
 * @description 更新用户信息（预案系统调用）
 */

export function systemSystemUserUpdateUserFromSpdUsingPost(options:SystemSystemUserUpdateUserFromSpdUsingPost.Options):Promise<SystemSystemUserUpdateUserFromSpdUsingPost.Result> {
  return cimRequest({
    url:'/system/systemUser/updateUserFromSpd',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 删除
 * @url /bs/tbMessage/deleteById/{id}
 * @method post
 * @description 删除
 */

export module BsTbMessageDeleteByIdIdUsingPost {
  export type Operation = paths['/bs/tbMessage/deleteById/{id}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 删除
 * @url /bs/tbMessage/deleteById/{id}
 * @method post
 * @description 删除
 */

export function bsTbMessageDeleteByIdIdUsingPost(options:BsTbMessageDeleteByIdIdUsingPost.Options):Promise<BsTbMessageDeleteByIdIdUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbMessage/deleteById/{id}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 最后更新时间
 * @url /bs/tbMessage/lastUpdateTime
 * @method post
 * @description 最后更新时间
 */

export module BsTbMessageLastUpdateTimeUsingPost {
  export type Operation = paths['/bs/tbMessage/lastUpdateTime']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 最后更新时间
 * @url /bs/tbMessage/lastUpdateTime
 * @method post
 * @description 最后更新时间
 */

export function bsTbMessageLastUpdateTimeUsingPost(options:BsTbMessageLastUpdateTimeUsingPost.Options):Promise<BsTbMessageLastUpdateTimeUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbMessage/lastUpdateTime',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 分页查询信息
 * @url /bs/tbMessage/listPage
 * @method post
 * @description 分页查询信息
 */

export module BsTbMessageListPageUsingPost {
  export type Operation = paths['/bs/tbMessage/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 分页查询信息
 * @url /bs/tbMessage/listPage
 * @method post
 * @description 分页查询信息
 */

export function bsTbMessageListPageUsingPost(options:BsTbMessageListPageUsingPost.Options):Promise<BsTbMessageListPageUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbMessage/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 未读个数查询
 * @url /bs/tbMessage/notReadNum
 * @method post
 * @description 未读个数查询
 */

export module BsTbMessageNotReadNumUsingPost {
  export type Operation = paths['/bs/tbMessage/notReadNum']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 未读个数查询
 * @url /bs/tbMessage/notReadNum
 * @method post
 * @description 未读个数查询
 */

export function bsTbMessageNotReadNumUsingPost(options:BsTbMessageNotReadNumUsingPost.Options):Promise<BsTbMessageNotReadNumUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbMessage/notReadNum',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 全部已读
 * @url /bs/tbMessage/readAll
 * @method post
 * @description 全部已读
 */

export module BsTbMessageReadAllUsingPost {
  export type Operation = paths['/bs/tbMessage/readAll']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 全部已读
 * @url /bs/tbMessage/readAll
 * @method post
 * @description 全部已读
 */

export function bsTbMessageReadAllUsingPost(options:BsTbMessageReadAllUsingPost.Options):Promise<BsTbMessageReadAllUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbMessage/readAll',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 消息读取
 * @url /bs/tbMessage/readById/{id}
 * @method get
 * @description 消息读取
 */

export module BsTbMessageReadByIdIdUsingGet {
  export type Operation = paths['/bs/tbMessage/readById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 业务模块-消息通知接口
 * @summary 消息读取
 * @url /bs/tbMessage/readById/{id}
 * @method get
 * @description 消息读取
 */

export function bsTbMessageReadByIdIdUsingGet(options:BsTbMessageReadByIdIdUsingGet.Options):Promise<BsTbMessageReadByIdIdUsingGet.Result> {
  return cimRequest({
    url:'/bs/tbMessage/readById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 查询多个附件下载,多个文件会自动打包成ZIP
 * @url /bs/tbPlanFilings/downloadFiles
 * @method get
 * @description 查询多个附件下载,多个文件会自动打包成ZIP
 */

export module BsTbPlanFilingsDownloadFilesUsingGet {
  export type Operation = paths['/bs/tbPlanFilings/downloadFiles']['get'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 查询多个附件下载,多个文件会自动打包成ZIP
 * @url /bs/tbPlanFilings/downloadFiles
 * @method get
 * @description 查询多个附件下载,多个文件会自动打包成ZIP
 */

export function bsTbPlanFilingsDownloadFilesUsingGet(options:BsTbPlanFilingsDownloadFilesUsingGet.Options):Promise<BsTbPlanFilingsDownloadFilesUsingGet.Result> {
  return cimRequest({
    url:'/bs/tbPlanFilings/downloadFiles',
    method:'get',
    ...options,
  });
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 最后更新时间
 * @url /bs/tbPlanFilings/lastUpdateTime
 * @method post
 * @description 最后更新时间
 */

export module BsTbPlanFilingsLastUpdateTimeUsingPost {
  export type Operation = paths['/bs/tbPlanFilings/lastUpdateTime']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 最后更新时间
 * @url /bs/tbPlanFilings/lastUpdateTime
 * @method post
 * @description 最后更新时间
 */

export function bsTbPlanFilingsLastUpdateTimeUsingPost(options:BsTbPlanFilingsLastUpdateTimeUsingPost.Options):Promise<BsTbPlanFilingsLastUpdateTimeUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbPlanFilings/lastUpdateTime',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 分页查询信息
 * @url /bs/tbPlanFilings/listPage
 * @method post
 * @description 分页查询信息
 */

export module BsTbPlanFilingsListPageUsingPost {
  export type Operation = paths['/bs/tbPlanFilings/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 分页查询信息
 * @url /bs/tbPlanFilings/listPage
 * @method post
 * @description 分页查询信息
 */

export function bsTbPlanFilingsListPageUsingPost(options:BsTbPlanFilingsListPageUsingPost.Options):Promise<BsTbPlanFilingsListPageUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbPlanFilings/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 批量删除数据
 * @url /bs/tbPlanFilings/removeByIdList
 * @method post
 * @description 批量删除数据
 */

export module BsTbPlanFilingsRemoveByIdListUsingPost {
  export type Operation = paths['/bs/tbPlanFilings/removeByIdList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 批量删除数据
 * @url /bs/tbPlanFilings/removeByIdList
 * @method post
 * @description 批量删除数据
 */

export function bsTbPlanFilingsRemoveByIdListUsingPost(options:BsTbPlanFilingsRemoveByIdListUsingPost.Options):Promise<BsTbPlanFilingsRemoveByIdListUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbPlanFilings/removeByIdList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 新增
 * @url /bs/tbPlanFilings/save
 * @method post
 * @description 新增
 */

export module BsTbPlanFilingsSaveUsingPost {
  export type Operation = paths['/bs/tbPlanFilings/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 新增
 * @url /bs/tbPlanFilings/save
 * @method post
 * @description 新增
 */

export function bsTbPlanFilingsSaveUsingPost(options:BsTbPlanFilingsSaveUsingPost.Options):Promise<BsTbPlanFilingsSaveUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbPlanFilings/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 按照预案类型统计
 * @url /bs/tbPlanFilings/statisticByPlanType
 * @method post
 * @description 按照预案类型统计
 */

export module BsTbPlanFilingsStatisticByPlanTypeUsingPost {
  export type Operation = paths['/bs/tbPlanFilings/statisticByPlanType']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 按照预案类型统计
 * @url /bs/tbPlanFilings/statisticByPlanType
 * @method post
 * @description 按照预案类型统计
 */

export function bsTbPlanFilingsStatisticByPlanTypeUsingPost(options:BsTbPlanFilingsStatisticByPlanTypeUsingPost.Options):Promise<BsTbPlanFilingsStatisticByPlanTypeUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbPlanFilings/statisticByPlanType',
    method:'post',
    ...options,
  });
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 更新
 * @url /bs/tbPlanFilings/update
 * @method post
 * @description 更新
 */

export module BsTbPlanFilingsUpdateUsingPost {
  export type Operation = paths['/bs/tbPlanFilings/update']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 业务模块-预案备案接口
 * @summary 更新
 * @url /bs/tbPlanFilings/update
 * @method post
 * @description 更新
 */

export function bsTbPlanFilingsUpdateUsingPost(options:BsTbPlanFilingsUpdateUsingPost.Options):Promise<BsTbPlanFilingsUpdateUsingPost.Result> {
  return cimRequest({
    url:'/bs/tbPlanFilings/update',
    method:'post',
    ...options,
  });
}

/**
 * @tag test-controller
 * @summary test
 * @url /test/run
 * @method get
 * @description
 */

export module TestRunUsingGet {
  export type Operation = paths['/test/run']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag test-controller
 * @summary test
 * @url /test/run
 * @method get
 * @description
 */

export function testRunUsingGet(options:TestRunUsingGet.Options):Promise<TestRunUsingGet.Result> {
  return cimRequest({
    url:'/test/run',
    method:'get',
    ...options,
  });
}

/**
 * @tag 日志接口
 * @summary 获取访问日志
 * @url /visit/log/getLog
 * @method get
 * @description
 */

export module VisitLogGetLogUsingGet {
  export type Operation = paths['/visit/log/getLog']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 日志接口
 * @summary 获取访问日志
 * @url /visit/log/getLog
 * @method get
 * @description
 */

export function visitLogGetLogUsingGet(options:VisitLogGetLogUsingGet.Options):Promise<VisitLogGetLogUsingGet.Result> {
  return cimRequest({
    url:'/visit/log/getLog',
    method:'get',
    ...options,
  });
}

/**
 * @tag xizang-sso-controller
 * @summary loginAuth
 * @url /api/sso/xizang/loginAuth
 * @method get
 * @description
 */

export module ApiSsoXizangLoginAuthUsingGet {
  export type Operation = paths['/api/sso/xizang/loginAuth']['get'];
  export type Result = any;
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag xizang-sso-controller
 * @summary loginAuth
 * @url /api/sso/xizang/loginAuth
 * @method get
 * @description
 */

export function apiSsoXizangLoginAuthUsingGet(options:ApiSsoXizangLoginAuthUsingGet.Options):Promise<ApiSsoXizangLoginAuthUsingGet.Result> {
  return cimRequest({
    url:'/api/sso/xizang/loginAuth',
    method:'get',
    ...options,
  });
}
export interface paths {
    "/api/sso/loginAuth": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** loginAuth */
        get: operations["loginAuthUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/sso/xizang/loginAuth": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** loginAuth */
        get: operations["loginAuthUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbMessage/deleteById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 删除
         * @description 删除
         */
        post: operations["deleteByIdUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbMessage/lastUpdateTime": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 最后更新时间
         * @description 最后更新时间
         */
        post: operations["lastUpdateTimeUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbMessage/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询信息
         * @description 分页查询信息
         */
        post: operations["listPageUsingPOST_7"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbMessage/notReadNum": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 未读个数查询
         * @description 未读个数查询
         */
        post: operations["notReadNumUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbMessage/readAll": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 全部已读
         * @description 全部已读
         */
        post: operations["readAllUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbMessage/readById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 消息读取
         * @description 消息读取
         */
        get: operations["readByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbPlanFilings/downloadFiles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 查询多个附件下载,多个文件会自动打包成ZIP
         * @description 查询多个附件下载,多个文件会自动打包成ZIP
         */
        get: operations["downloadFilesUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbPlanFilings/lastUpdateTime": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 最后更新时间
         * @description 最后更新时间
         */
        post: operations["lastUpdateTimeUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbPlanFilings/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询信息
         * @description 分页查询信息
         */
        post: operations["listPageUsingPOST_8"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbPlanFilings/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量删除数据
         * @description 批量删除数据
         */
        post: operations["removeByIdListUsingPOST_7"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbPlanFilings/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 新增
         * @description 新增
         */
        post: operations["savePlanFilingsUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbPlanFilings/statisticByPlanType": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 按照预案类型统计
         * @description 按照预案类型统计
         */
        post: operations["statisticByPlanTypeUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/bs/tbPlanFilings/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 更新
         * @description 更新
         */
        post: operations["updatePlanFilingsUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/deleteById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 删除转换任务
         * @description 删除转换任务
         */
        get: operations["deleteByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/fileList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 文件地址列表
         * @description 文件地址列表
         */
        get: operations["fileListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/getById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据id获取转换任务
         * @description 根据id获取转换任务
         */
        get: operations["getBySystemIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 获取转换任务列表
         * @description 获取转换任务列表
         */
        post: operations["getBySystemIdUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/progress": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** getProgress */
        get: operations["getProgressUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/saveOrUpdate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 新增/更新转换任务
         * @description 新增/更新转换任务
         */
        post: operations["saveOrUpdateLabelUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/updateFileList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 文件名更新/删/改
         * @description 文件名更新/删/改
         */
        post: operations["updateFileListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/changeData/uploadChangeFile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 上传转换数据
         * @description 上传转换数据
         */
        post: operations["uploadChangeFileUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/files/download/{fileDir}/{fileName}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** downloadFile */
        get: operations["downloadFileUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/files/uploadMultipleFiles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** uploadMultipleFiles */
        post: operations["uploadMultipleFilesUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/files/uploadZipFile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** uploadMultipleFiles */
        post: operations["uploadMultipleFilesUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/addMultipleFiles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 上传文件
         * @description 上传文件
         */
        post: operations["uploadMultipleFilesUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/delete/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        /**
         * 根据id删除文件
         * @description 根据id删除文件
         */
        delete: operations["deleteFileSettingsByIdUsingDELETE"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/download/{fileDir}/{fileName}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** downloadFile */
        get: operations["downloadFileUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/downloadPhoto/{fileDir}/{fileName}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 下载图片文件
         * @description 下载图片文件
         */
        get: operations["downloadPhotoFileUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/getById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据id查询文件
         * @description 根据id查询文件
         */
        get: operations["getFileSettingsByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询文件列表
         * @description 查询文件列表
         */
        post: operations["getAllFileSettingsUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        /**
         * 更新文件
         * @description 更新文件
         */
        put: operations["updateFileSettingsUsingPUT"];
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/fileSetting/uploadFile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 上传图片文件
         * @description 上传图片文件
         */
        post: operations["uploadFileUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/layerInfo/deleteById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 删除
         * @description 删除
         */
        post: operations["deleteByIdUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/layerInfo/getLayerInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取图层树
         * @description 获取图层树
         */
        get: operations["getLayerInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/layerInfo/getLayerTypeInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取图层类型配置
         * @description 获取图层类型配置
         */
        get: operations["getLayerTypeInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/layerInfo/getSystemSettingsByUserCode": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取用户系统设置信息
         * @description 获取用户系统设置信息
         */
        get: operations["getSystemSettingsByUserCodeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/layerInfo/saveOrUpdateLayerInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 新增/更新图层
         * @description 新增/更新图层
         */
        post: operations["saveOrUpdateLayerInfoUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/layerInfo/saveOrUpdateLayerType": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 新增/更新图层类型
         * @description 新增/更新图层类型
         */
        post: operations["saveOrUpdateLayerTypeUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/layerInfo/saveOrUpdateSystemSettings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 新增/更新系统设置
         * @description 新增/更新系统设置
         */
        post: operations["saveOrUpdateSystemSettingsUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/opLog/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询操作日志
         * @description 分页查询操作日志
         */
        post: operations["listPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/registerUser/config": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** register */
        get: operations["registerUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/registerUser/user": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** register */
        get: operations["registerUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/route/api": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** callExternalApi */
        get: operations["callExternalApiUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sceneInfo/deleteById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 删除标绘信息
         * @description 删除
         */
        get: operations["deleteByIdUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sceneInfo/export/json/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 导出标绘
         * @description 导出标绘信息
         */
        get: operations["exportJsonUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sceneInfo/getSceneInfo/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据id获取标绘
         * @description 获取标绘类型配置
         */
        get: operations["getSceneInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sceneInfo/import/json": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 导入标绘
         * @description 导入标绘
         */
        post: operations["parseJsonUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sceneInfo/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询标绘信息
         * @description 分页查询标绘信息
         */
        post: operations["listPageUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/sceneInfo/saveOrUpdateSceneInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 新增/更新标绘
         * @description 新增/更新标绘
         */
        post: operations["saveOrUpdateSceneInfoUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDept/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询所有机构数据
         * @description 查询所有机构数据
         */
        post: operations["listUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDept/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量删除机构数据
         * @description 批量删除机构数据
         */
        post: operations["removeByIdListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDept/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存机构信息
         * @description 保存机构信息
         */
        post: operations["saveUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDept/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修改机构信息
         * @description 修改资源信息
         */
        post: operations["updateUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDict/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询所有字典数据
         * @description 查询所有字典数据
         */
        post: operations["listUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDict/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询字典分页数据
         * @description 查询字典分页数据
         */
        post: operations["listPageUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDict/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量删除字典数据
         * @description 批量删除字典数据
         */
        post: operations["removeByIdListUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDict/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存字典信息
         * @description 保存字典信息
         */
        post: operations["saveUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDict/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修改字典信息
         * @description 修改字典信息
         */
        post: operations["updateUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDictData/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询所有字典数据
         * @description 查询所有字典数据
         */
        post: operations["listUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDictData/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量删除字典数据
         * @description 批量删除字典数据
         */
        post: operations["removeByIdListUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDictData/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存字典数据信息
         * @description 保存字典数据信息
         */
        post: operations["saveUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemDictData/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修改字典数据信息
         * @description 修改字典数据信息
         */
        post: operations["updateUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/download/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 查询附件下载
         * @description 查询附件下载
         */
        get: operations["downloadUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/downloadFiles": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 查询多个附件下载,多个文件会自动打包成ZIP
         * @description 查询多个附件下载,多个文件会自动打包成ZIP
         */
        get: operations["downloadFilesUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/downloadLocalFile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 下载项目本地模板附件
         * @description 通过文件名称下载项目本地模板附件
         */
        get: operations["downloadLocalFileUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询所有附件数据
         * @description 查询所有附件数据
         */
        post: operations["getListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询附件列表
         * @description 查询附件列表
         */
        post: operations["listUsingPOST_3"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询附件分页数据
         * @description 查询附件分页数据
         */
        post: operations["listPageUsingPOST_3"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量删除附件数据
         * @description 批量删除附件数据
         */
        post: operations["removeByIdListUsingPOST_3"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemFile/upload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 附件上传
         * @description 附件上传
         */
        post: operations["uploadUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemLog/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询信息
         * @description 分页查询信息
         */
        post: operations["listPageUsingPOST_4"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemLog/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存日志信息
         * @description 保存日志信息
         */
        post: operations["saveUsingPOST_3"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemLogExtend/getByLogId/{logId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据日志ID查询信息
         * @description 根据日志ID查询信息
         */
        get: operations["getByLogIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/getAll": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取全部模块
         * @description 获取全部模块
         */
        get: operations["getAllUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/getResources": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据模块id获取资源
         * @description 根据模块id获取资源
         */
        get: operations["getResourcesUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询
         * @description 分页查询
         */
        post: operations["listPageUsingPOST_5"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/listResourcesByModuleId/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 设置目录
         * @description 设置目录
         */
        get: operations["listResourcesByModuleIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 删除
         * @description 删除
         */
        post: operations["removeByIdListUsingPOST_4"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存资源信息
         * @description 保存资源信息
         */
        post: operations["saveUsingPOST_4"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/settingResources": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 设置目录
         * @description 设置目录
         */
        post: operations["settingResourcesUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemModule/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修改资源信息
         * @description 修改资源信息
         */
        post: operations["updateUsingPOST_3"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemResources/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询所有资源数据
         * @description 查询所有资源数据
         */
        post: operations["listUsingPOST_4"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemResources/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量删除资源数据
         * @description 批量删除资源数据
         */
        post: operations["removeByIdListUsingPOST_5"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemResources/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存资源信息
         * @description 保存资源信息
         */
        post: operations["saveUsingPOST_5"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemResources/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修改资源信息
         * @description 修改资源信息
         */
        post: operations["updateUsingPOST_4"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemRole/assignResource": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 角色绑定权限
         * @description 角色绑定权限
         */
        post: operations["assignResourceUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/assignRole": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分配角色
         * @description 分配角色
         */
        post: operations["assignRoleUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/changePassword": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 修改当前用户密码
         * @description 修改当前用户密码
         */
        post: operations["changePasswordUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/getCurrUserInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取当前登录用户信息
         * @description 获取当前登录用户信息
         */
        get: operations["getCurrUserInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 列表查询用户信息
         * @description 列表查询用户信息
         */
        post: operations["getListUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/getUserRoleNumStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 用户角色数量统计
         * @description 用户角色数量统计
         */
        get: operations["getUserRoleNumStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/lastUpdateTime": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 最后更新时间
         * @description 最后更新时间
         */
        post: operations["lastUpdateTimeUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询用户信息
         * @description 分页查询用户信息
         */
        post: operations["listPageUsingPOST_6"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/removeByIdList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 批量删除数据
         * @description 批量删除数据
         */
        post: operations["removeByIdListUsingPOST_6"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/removeUserFromSpd": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 批量删除数据（预案系统调用）
         * @description 批量删除数据（预案系统调用）
         */
        get: operations["removeUserFromSpdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存用户信息
         * @description 保存用户信息
         */
        post: operations["saveUserUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/saveUserFromSpd": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 保存用户信息（预案系统调用）
         * @description 保存用户信息（预案系统调用）
         */
        post: operations["saveUserFromSpdUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/system/systemUser/updateUserFromSpd": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 更新用户信息（预案系统调用）
         * @description 更新用户信息（预案系统调用）
         */
        post: operations["updateUserFromSpdUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/test/run": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** test */
        get: operations["testUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/visit/log/getLog": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取访问日志 */
        get: operations["getVisitLogUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /** 标绘图层 */
        BiaoHuiTuCeng: {
            /** @description appCode */
            appCode?: string;
            /** @description createBy */
            createBy?: string;
            /**
             * Format: date-time
             * @description createTime
             */
            createTime?: string;
            /** @description 文件路径 */
            fileDir?: string;
            /** @description ID */
            id?: string;
            /**
             * Format: int32
             * @description idx
             */
            idx?: number;
            /** @description key */
            key?: string;
            /** @description parentId */
            parentId?: string;
            /** @description remark */
            remark?: string;
            /** @description updateBy */
            updateBy?: string;
            /**
             * Format: date-time
             * @description updateTime
             */
            updateTime?: string;
            /** @description 用户名 */
            userName?: string;
            /** @description value */
            value?: string;
        };
        /**
         * ChangeDataTask
         * @description 数据转换任务信息
         */
        ChangeDataTask: {
            /** @description 转换任务名称 */
            changeTask?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createdAt?: string;
            /** @description shp文件转换高度 */
            height?: string;
            /** @description 任务的唯一标识 */
            id?: string;
            /** @description 发布路径 */
            publicPath?: string;
            /** @description 源数据格式 */
            sourceFormat?: string;
            /** @description 源数据路径 */
            sourcePath?: string;
            /**
             * Format: int32
             * @description 状态 0: 未开始，1:正在转换， 2: 转换完成， 3:转换出错
             */
            status?: number;
            /** @description 转换后格式 */
            targetFormat?: string;
            /** @description 目标文件路径 */
            targetPath?: string;
        };
        /**
         * 查询排序配置项
         * @description 查询排序配置项
         */
        ChaXunPaiXuPeiZhiXiang: {
            /**
             * @description 排序方式
             * @example ASC/asc;DESC/desc
             */
            orderType?: string;
            /** @description 属性名称 */
            propertyName?: string;
        };
        /** FileListOpt */
        FileListOpt: {
            /** @description 文件名 */
            fileName?: string;
            /** @description 操作类型 add: 新增, update: 更新, del: 删除  */
            optType?: string;
            /** @description 文件 source: 源文件,  target: 目标文件 */
            type?: string;
            /** @description 更新文件, 文件名更新时传 */
            updateName?: string;
        };
        /** FileSystemResource */
        FileSystemResource: {
            description?: string;
            /** Format: binary */
            file?: string;
            filename?: string;
            inputStream?: components["schemas"]["InputStream"];
            open?: boolean;
            outputStream?: components["schemas"]["OutputStream"];
            path?: string;
            readable?: boolean;
            /** Format: uri */
            uri?: string;
            /** Format: url */
            url?: string;
            writable?: boolean;
        };
        /** InputStream */
        InputStream: Record<string, never>;
        /** LayerInfoDTO */
        LayerInfoDTO: {
            /** @description 是否自动获取网格集 */
            autoGridSet?: string;
            /** @description config */
            config?: string;
            /** @description 目录还是节点  dir: 目录,  layer: 节点 */
            dirOrLayer?: string;
            /** @description 父ID */
            fid?: string;
            /**
             * Format: int64
             * @description 旋转角度
             */
            heading?: number;
            /**
             * Format: int64
             * @description 相机高度
             */
            height?: number;
            /** @description ID */
            id?: string;
            /** @description 相机纬度 */
            latitude?: string;
            /** @description 图层bbox,对应经纬度顺序为[W,S,E,N] */
            layerExtend?: string[];
            /** @description 图层名称 */
            layerName?: string;
            /** @description 图层类型 */
            layerType?: string;
            /** @description 相机经度 */
            longitude?: string;
            /**
             * Format: int64
             * @description 最大级别
             */
            maximumLevel?: number;
            /**
             * Format: int64
             * @description 最小级别
             */
            minimumLevel?: number;
            /**
             * Format: int64
             * @description 倾斜角度
             */
            pitch?: number;
            /**
             * Format: int64
             * @description 翻滚角度
             */
            roll?: number;
            /**
             * Format: int64
             * @description 排序字典
             */
            sort?: number;
            /** @description 网格集 */
            tileMatrixSetID?: string;
            /** @description 图层URL */
            url?: string;
            /** @description 用户名 */
            userName?: string;
        };
        /** LayerInfoVO */
        LayerInfoVO: {
            /** @description 是否自动获取网格集 */
            autoGridSet?: string;
            /** @description 子节点 */
            children?: components["schemas"]["LayerInfoVO"][];
            /** @description config */
            config?: string;
            /** @description 目录还是节点  dir: 目录,  layer: 节点 */
            dirOrLayer?: string;
            /** @description 父ID */
            fid?: string;
            /**
             * Format: int64
             * @description 旋转角度
             */
            heading?: number;
            /**
             * Format: int64
             * @description 相机高度
             */
            height?: number;
            /** @description ID */
            id?: string;
            /** @description 相机纬度 */
            latitude?: string;
            /** @description 图层bbox,对应经纬度顺序为[W,S,E,N] */
            layerExtend?: string[];
            /** @description 图层名称 */
            layerName?: string;
            /** @description 图层类型 */
            layerType?: string;
            /** @description 图层类型配置详细信息 */
            layerTypeDetail?: components["schemas"]["TuCengLeiXingPeiZhiShuXing"];
            /** @description 相机经度 */
            longitude?: string;
            /**
             * Format: int64
             * @description 最大级别
             */
            maximumLevel?: number;
            /**
             * Format: int64
             * @description 最小级别
             */
            minimumLevel?: number;
            /**
             * Format: int64
             * @description 倾斜角度
             */
            pitch?: number;
            /**
             * Format: int64
             * @description 翻滚角度
             */
            roll?: number;
            /**
             * Format: int64
             * @description 排序字典
             */
            sort?: number;
            /** @description 网格集 */
            tileMatrixSetID?: string;
            /** @description 图层URL */
            url?: string;
            /** @description 用户名 */
            userName?: string;
        };
        /**
         * LoginUserInfoDTO
         * @description 用户信息、角色信息、权限信息DTO
         */
        LoginUserInfoDTO: {
            /** @description 机构名 */
            deptName?: string;
            /** @description 权限集合 */
            permissionList?: string[];
            /** @description 角色集合 */
            roleList?: string[];
            /** @description 用户名Id */
            systemUser?: components["schemas"]["SystemUserDuiXiang"];
            /** @description 用户主键id */
            userId?: string;
        };
        /** OutputStream */
        OutputStream: Record<string, never>;
        /**
         * PageParam
         * @description 分页参数
         */
        PageParam: {
            /** @description 查询返回数据 */
            records?: Record<string, never>[];
            /** @description 分页查询合计对象 */
            summary?: Record<string, never>;
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«标绘图层»Req
         * @description 分页参数
         */
        PageParamBiaoHuiTuCengReq: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["BiaoHuiTuCeng"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«标绘图层»Res
         * @description 分页参数
         */
        PageParamBiaoHuiTuCengRes: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["BiaoHuiTuCeng"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["BiaoHuiTuCeng"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«ChangeDataTask»Req
         * @description 分页参数
         */
        PageParamChangeDataTaskReq: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["ChangeDataTask"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«ChangeDataTask»Res
         * @description 分页参数
         */
        PageParamChangeDataTaskRes: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["ChangeDataTask"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["ChangeDataTask"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«时间段»
         * @description 分页参数
         */
        PageParamShiJianDuan: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["ShiJianDuan"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«SystemDict对象»
         * @description 分页参数
         */
        PageParamSystemDictDuiXiang: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SystemDictDuiXiang"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SystemDictDuiXiang"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«SystemDictQueryDTO»
         * @description 分页参数
         */
        PageParamSystemDictQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["SystemDictQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«SystemFile对象»Req
         * @description 分页参数
         */
        PageParamSystemFileDuiXiangReq: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["SystemFileDuiXiang"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«SystemFile对象»Res
         * @description 分页参数
         */
        PageParamSystemFileDuiXiangRes: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SystemFileDuiXiang"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SystemFileDuiXiang"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«SystemLog对象»
         * @description 分页参数
         */
        PageParamSystemLogDuiXiang: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SystemLogDuiXiang"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SystemLogDuiXiang"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«SystemLogQueryDTO»
         * @description 分页参数
         */
        PageParamSystemLogQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["SystemLogQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«SystemModule对象»
         * @description 分页参数
         */
        PageParamSystemModuleDuiXiang: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["SystemModuleDuiXiang"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«SystemUserDTO对象»
         * @description 分页参数
         */
        PageParamSystemUserDTODuiXiang: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SystemUserDTODuiXiang"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SystemUserDTODuiXiang"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«SystemUserQueryDTO»
         * @description 分页参数
         */
        PageParamSystemUserQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["SystemUserQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«TbMessage对象»Req
         * @description 分页参数
         */
        PageParamTbMessageDuiXiangReq: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["TbMessageDuiXiang"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«TbMessage对象»Res
         * @description 分页参数
         */
        PageParamTbMessageDuiXiangRes: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["TbMessageDuiXiang"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["TbMessageDuiXiang"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«TbPlanFilingsQueryDTO»
         * @description 分页参数
         */
        PageParamTbPlanFilingsQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["TbPlanFilingsQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«TbPlanFilingsSaveDTO»
         * @description 分页参数
         */
        PageParamTbPlanFilingsSaveDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["TbPlanFilingsSaveDTORes"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["TbPlanFilingsSaveDTORes"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«TOpLog»
         * @description 分页参数
         */
        PageParamTOpLog: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["TOpLog"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["TOpLog"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«文件设置实体»
         * @description 分页参数
         */
        PageParamWenJianSheZhiShiTi: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["WenJianSheZhiShiTi"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /** Resource */
        Resource: {
            description?: string;
            /** Format: binary */
            file?: string;
            filename?: string;
            inputStream?: components["schemas"]["InputStream"];
            open?: boolean;
            readable?: boolean;
            /** Format: uri */
            uri?: string;
            /** Format: url */
            url?: string;
        };
        /** Result */
        Result: {
            /** Format: int32 */
            code?: number;
            data?: Record<string, never>;
            message?: string;
        };
        /** Result«标绘图层» */
        ResultBiaoHuiTuCeng: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["BiaoHuiTuCeng"];
            message?: string;
        };
        /** Result«boolean» */
        Resultboolean: {
            /** Format: int32 */
            code?: number;
            data?: boolean;
            message?: string;
        };
        /** Result«List«LayerInfoVO»» */
        ResultListLayerInfoVO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["LayerInfoVO"][];
            message?: string;
        };
        /** Result«List«SystemDept对象»» */
        ResultListSystemDeptDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemDeptDuiXiang0"][];
            message?: string;
        };
        /** Result«List«SystemDictData对象»» */
        ResultListSystemDictDataDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemDictDataDuiXiang"][];
            message?: string;
        };
        /** Result«List«SystemDict对象»» */
        ResultListSystemDictDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemDictDuiXiang"][];
            message?: string;
        };
        /** Result«List«SystemFileDTO»» */
        ResultListSystemFileDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemFileDTO"][];
            message?: string;
        };
        /** Result«List«SystemFile对象»» */
        ResultListSystemFileDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemFileDuiXiang"][];
            message?: string;
        };
        /** Result«List«SystemResourcesDTO对象»» */
        ResultListSystemResourcesDTODuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemResourcesDTODuiXiang"][];
            message?: string;
        };
        /** Result«List«SystemUser对象»» */
        ResultListSystemUserDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemUserDuiXiang"][];
            message?: string;
        };
        /** Result«List«TbPlanFilingsStatisticByPlanTypeDTO»» */
        ResultListTbPlanFilingsStatisticByPlanTypeDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TbPlanFilingsStatisticByPlanTypeDTO"][];
            message?: string;
        };
        /** Result«LocalDateTime» */
        ResultLocalDateTime: {
            /** Format: int32 */
            code?: number;
            /** Format: date-time */
            data?: string;
            message?: string;
        };
        /** Result«LoginUserInfoDTO» */
        ResultLoginUserInfoDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["LoginUserInfoDTO"];
            message?: string;
        };
        /** Result«object» */
        Resultobject: {
            /** Format: int32 */
            code?: number;
            data?: Record<string, never>;
            message?: string;
        };
        /** Result«PageParam» */
        ResultPageParam: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParam"];
            message?: string;
        };
        /** Result«PageParam«标绘图层»» */
        ResultPageParamBiaoHuiTuCeng: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamBiaoHuiTuCengRes"];
            message?: string;
        };
        /** Result«PageParam«ChangeDataTask»» */
        ResultPageParamChangeDataTask: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamChangeDataTaskRes"];
            message?: string;
        };
        /** Result«PageParam«SystemDict对象»» */
        ResultPageParamSystemDictDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSystemDictDuiXiang"];
            message?: string;
        };
        /** Result«PageParam«SystemFile对象»» */
        ResultPageParamSystemFileDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSystemFileDuiXiangRes"];
            message?: string;
        };
        /** Result«PageParam«SystemLog对象»» */
        ResultPageParamSystemLogDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSystemLogDuiXiang"];
            message?: string;
        };
        /** Result«PageParam«SystemUserDTO对象»» */
        ResultPageParamSystemUserDTODuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSystemUserDTODuiXiang"];
            message?: string;
        };
        /** Result«PageParam«TbMessage对象»» */
        ResultPageParamTbMessageDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamTbMessageDuiXiangRes"];
            message?: string;
        };
        /** Result«PageParam«TbPlanFilingsSaveDTO»» */
        ResultPageParamTbPlanFilingsSaveDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamTbPlanFilingsSaveDTO"];
            message?: string;
        };
        /** Result«PageParam«TOpLog»» */
        ResultPageParamTOpLog: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamTOpLog"];
            message?: string;
        };
        /** Result«string» */
        Resultstring: {
            /** Format: int32 */
            code?: number;
            data?: string;
            message?: string;
        };
        /** Result«SystemFile对象» */
        ResultSystemFileDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemFileDuiXiang"];
            message?: string;
        };
        /** Result«SystemLogExtend» */
        ResultSystemLogExtend: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SystemLogExtend"];
            message?: string;
        };
        /** Result«图层类型配置属性» */
        ResultTuCengLeiXingPeiZhiShuXing: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TuCengLeiXingPeiZhiShuXing"];
            message?: string;
        };
        /** Result«UserRoleNumStatisticDTO» */
        ResultUserRoleNumStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["UserRoleNumStatisticDTO"];
            message?: string;
        };
        /** Result«VisitLog» */
        ResultVisitLog: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["VisitLog"];
            message?: string;
        };
        /** Result«系统设置VO» */
        ResultXiTongSheZhiVO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["XiTongSheZhiVO"];
            message?: string;
        };
        /** 时间段 */
        ShiJianDuan: {
            /** @description 结束时间 */
            endTime?: string;
            /** @description 开始时间  */
            startTime?: string;
        };
        /**
         * SystemDept对象
         * @description 机构表
         */
        SystemDeptDuiXiang: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 机构编码 */
            deptCode?: string;
            /** @description 机构名 */
            deptName?: string;
            /** @description 机构全称 */
            fullName?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 父主键 */
            parentId?: string;
            /** @description 引用组织机构编号（条线组织机构） */
            refId?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 级别：1-国家级 2-省级 3-市级 */
            typeGrade?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemDept对象0
         * @description 机构表
         */
        SystemDeptDuiXiang0: {
            /** @description 子集列表 */
            children?: components["schemas"]["SystemDeptDuiXiang0"][];
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 机构编码 */
            deptCode?: string;
            /** @description 机构名 */
            deptName?: string;
            /** @description 机构全称 */
            fullName?: string;
            hasChildren?: boolean;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 父主键 */
            parentId?: string;
            /** @description 引用组织机构编号（条线组织机构） */
            refId?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 级别：1-国家级 2-省级 3-市级 */
            typeGrade?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemDeptSaveDTO
         * @description 机构保存DTO
         */
        SystemDeptSaveDTO: {
            /** @description 机构编码 */
            deptCode?: string;
            /** @description 机构名 */
            deptName?: string;
            /** @description 机构全称 */
            fullName?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 父主键 */
            parentId: string;
            /** @description 备注 */
            remark?: string;
            /** @description 级别：1-国家级 2-省级 3-市级 */
            typeGrade?: string;
        };
        /**
         * SystemDeptUpdateDTO
         * @description 机构修改DTO
         */
        SystemDeptUpdateDTO: {
            /** @description 机构编码 */
            deptCode?: string;
            /** @description 机构名 */
            deptName?: string;
            /** @description 机构全称 */
            fullName?: string;
            /** @description id主键 */
            id: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 父主键 */
            parentId: string;
            /** @description 备注 */
            remark?: string;
            /** @description 级别：1-国家级 2-省级 3-市级 */
            typeGrade?: string;
        };
        /**
         * SystemDictData对象
         * @description 字典数据表
         */
        SystemDictDataDuiXiang: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 字典数据key */
            dataKey?: string;
            /** @description 上级字典数据key（构建树状使用） */
            dataParentKey?: string;
            /** @description 数据状态（1-启用，0-禁用） */
            dataStatus?: string;
            /** @description 字典数据value */
            dataValue?: string;
            /** @description 字典id */
            dictId?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 备注 */
            remark?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemDictDataQueryDTO
         * @description 字典数据查询DTO
         */
        SystemDictDataQueryDTO: {
            /** @description 字典id */
            dictId: string;
        };
        /**
         * SystemDictDataSaveDTO
         * @description 字典数据信息保存DTO
         */
        SystemDictDataSaveDTO: {
            /** @description 字典数据key */
            dataKey: string;
            /** @description 上级字典数据key（构建树状使用） */
            dataParentKey?: string;
            /** @description 字典数据value */
            dataValue: string;
            /** @description 字典id */
            dictId: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx: number;
            /** @description 备注 */
            remark?: string;
        };
        /**
         * SystemDictDataUpdateDTO
         * @description 字典信息修改DTO
         */
        SystemDictDataUpdateDTO: {
            /** @description 字典数据key */
            dataKey: string;
            /** @description 上级字典数据key（构建树状使用） */
            dataParentKey?: string;
            /** @description 数据状态（1-启用，0-禁用） */
            dataStatus?: string;
            /** @description 字典数据value */
            dataValue: string;
            /** @description 字典id */
            dictId: string;
            /** @description id主键 */
            id: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx: number;
            /** @description 备注 */
            remark?: string;
        };
        /**
         * SystemDict对象
         * @description 字典表
         */
        SystemDictDuiXiang: {
            /** @description 字典码 */
            code?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 数据状态（1-启用，0-禁用） */
            dataStatus?: string;
            /** @description 主键id */
            id?: string;
            /** @description 字典名称 */
            name?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 字典类型(1-系统字典，2-业务字典) */
            type?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemDictQueryDTO
         * @description 字典查询DTO
         */
        SystemDictQueryDTO: {
            /** @description 字典码 */
            code?: string;
            /** @description 字典名称 */
            name?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 字典类型(1-系统字典，2-业务字典) */
            type: string;
        };
        /**
         * SystemDictSaveDTO
         * @description 字典信息保存DTO
         */
        SystemDictSaveDTO: {
            /** @description 字典码 */
            code: string;
            /** @description 字典名称 */
            name: string;
            /** @description 备注 */
            remark?: string;
            /** @description 字典类型(1-系统字典，2-业务字典) */
            type: string;
        };
        /**
         * SystemDictUpdateDTO
         * @description 字典信息修改DTO
         */
        SystemDictUpdateDTO: {
            /** @description 字典码 */
            code: string;
            /** @description 数据状态（1-启用，0-禁用） */
            dataStatus?: string;
            /** @description id主键 */
            id: string;
            /** @description 字典名称 */
            name: string;
            /** @description 备注 */
            remark?: string;
            /** @description 字典类型(1-系统字典，2-业务字典) */
            type: string;
        };
        /**
         * SystemFileDTO
         * @description 附件查询DTO
         */
        SystemFileDTO: {
            /** @description 文件类型 */
            contentType?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 文件状态 */
            dataStatus?: string;
            /** @description 存储目录 */
            dir?: string;
            /** @description 文件扩展名 */
            extName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 文件md5值 */
            md5?: string;
            /** @description 文件名称 */
            name?: string;
            /** @description 附加业务关联1(业务类型) */
            param1?: string;
            /** @description 附加业务关联2(业务id1) */
            param2?: string;
            /** @description 附加业务关联3(业务id2) */
            param3?: string;
            /** @description 附加业务关联4 (业务id3) */
            param4?: string;
            /** @description 附加业务关联5 */
            param5?: string;
            /** @description 存储路径 */
            path?: string;
            /**
             * Format: int64
             * @description 文件大小
             */
            size?: number;
            /**
             * Format: int64
             * @description 总文件大小
             */
            totalSize?: number;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemFile对象
         * @description 附件表
         */
        SystemFileDuiXiang: {
            /** @description 文件类型 */
            contentType?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 文件状态 */
            dataStatus?: string;
            /** @description 存储目录 */
            dir?: string;
            /** @description 文件扩展名 */
            extName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 文件md5值 */
            md5?: string;
            /** @description 文件名称 */
            name?: string;
            /** @description 存储路径 */
            path?: string;
            /**
             * Format: int64
             * @description 文件大小
             */
            size?: number;
            /**
             * Format: int64
             * @description 总文件大小
             */
            totalSize?: number;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemFileQueryDTO
         * @description 系统模块DTO
         */
        SystemFileQueryDTO: {
            /** @description 文件类型 */
            contentType?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 文件状态 */
            dataStatus?: string;
            /** @description 存储目录 */
            dir?: string;
            /** @description 文件扩展名 */
            extName?: string;
            /** @description 文件Id */
            fileId?: string;
            /** @description 主键id */
            id?: string;
            /** @description 文件md5值 */
            md5?: string;
            /** @description 文件名称 */
            name?: string;
            /** @description 附加业务关联1(业务类型) */
            param1?: string;
            /** @description 文件Id */
            param1List?: string[];
            /** @description 附加业务关联2(业务id1) */
            param2?: string;
            /** @description 附加业务关联3(业务id2) */
            param3?: string;
            /** @description 附加业务关联4 (业务id3) */
            param4?: string;
            /** @description 附加业务关联4 (业务id3) */
            param5?: string;
            /** @description 存储路径 */
            path?: string;
            /**
             * Format: int64
             * @description 文件大小
             */
            size?: number;
            /**
             * Format: int64
             * @description 总文件大小
             */
            totalSize?: number;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemLog对象
         * @description 日志表
         */
        SystemLogDuiXiang: {
            /**
             * Format: date-time
             * @description 执行开始时间
             */
            beginRequestTime?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /**
             * Format: date-time
             * @description 执行结束时间
             */
            endRequestTime?: string;
            /** @description 异常类名 */
            exceptionClass?: string;
            /** @description 异常信息 */
            exceptionMessage?: string;
            /** @description 主键id */
            id?: string;
            /** @description 日志说明 */
            logDescription?: string;
            /** @description 日志级别：INFO/ERROR */
            logLevel?: string;
            /** @description 访问入参 */
            requestParams?: string;
            /** @description 访问出参 */
            requestReturn?: string;
            /** @description 操作方式:POST/GET */
            requestType?: string;
            /** @description 请求URI */
            requestUri?: string;
            /**
             * Format: int64
             * @description 执行耗时
             */
            spendTime?: number;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description 用户ID */
            userId?: string;
            /** @description 用户名称 */
            userName?: string;
        };
        /**
         * SystemLogExtend
         * @description 日志扩展表
         */
        SystemLogExtend: {
            /** @description 主键id */
            id?: string;
            /** @description 异常信息 */
            logException?: string;
            /** @description 日志ID */
            logId?: string;
            /** @description 访问出参 */
            logReturn?: string;
        };
        /**
         * SystemLogQueryDTO
         * @description 日志查询DTO
         */
        SystemLogQueryDTO: {
            /**
             * Format: date-time
             * @description 执行开始时间-结束
             */
            beginRequestTimeEnd?: string;
            /**
             * Format: date-time
             * @description 执行开始时间-开始
             */
            beginRequestTimeStart?: string;
            /** @description 方法类 */
            className?: string;
            /** @description 异常类名 */
            exceptionClass?: string;
            /** @description 异常信息 */
            exceptionMessage?: string;
            /** @description 日志说明 */
            logDescription?: string;
            /** @description 日志级别：INFO/ERROR */
            logLevel?: string;
            /** @description 方法名 */
            methodName?: string;
            /** @description 访问入参 */
            requestParams?: string;
            /** @description 访问出参 */
            requestReturn?: string;
            /** @description 操作方式:POST/GET */
            requestType?: string;
            /** @description 请求URI */
            requestUri?: string;
            /**
             * Format: int64
             * @description 执行耗时
             */
            spendTime?: number;
            /** @description 用户ID */
            userId?: string;
            /** @description 用户名称 */
            userName?: string;
        };
        /**
         * SystemModuleDTO
         * @description 系统模块DTO
         */
        SystemModuleDTO: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 数据状态（1-已经删除，0-未删除） */
            dataStatus?: string;
            /** @description 模块图标 */
            icon?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 模块名称 */
            name?: string;
            /** @description 资源list */
            resourceIdList?: string[];
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemModule对象
         * @description 系统模块
         */
        SystemModuleDuiXiang: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 数据状态（1-已经删除，0-未删除） */
            dataStatus?: string;
            /** @description 模块图标 */
            icon?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 模块名称 */
            name?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemResourcesDTO对象
         * @description 资源信息DTO
         */
        SystemResourcesDTODuiXiang: {
            /**
             * @description 菜单固定标签
             * @example false
             */
            affix?: boolean;
            /** @description 访问组件名称 */
            componentName?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 授权标识 */
            enName?: string;
            /** @description 图标 */
            icon?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 资源名称 */
            name?: string;
            /** @description 父级资源id */
            parentId?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 角色List */
            roleList?: string[];
            /** @description 系统类型:1-三维预案推演系统 2-指挥调度系统 */
            systemType?: string;
            /** @description 资源类型：1-目录， 2-菜单 ，3-按钮 */
            type?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description 访问路径 */
            url?: string;
        };
        /**
         * SystemResourcesQueryDTO
         * @description 资源信息查询DTO
         */
        SystemResourcesQueryDTO: {
            /** @description 角色id */
            roleId?: string;
            /** @description 系统类型:1-三维预案推演系统 2-指挥调度系统 */
            systemType?: string;
        };
        /**
         * SystemResourcesSaveDTO
         * @description 资源信息保存DTO
         */
        SystemResourcesSaveDTO: {
            /**
             * @description 菜单固定标签
             * @example false
             */
            affix?: boolean;
            /** @description 访问组件名称 */
            componentName?: string;
            /** @description 授权标识 */
            enName?: string;
            /** @description 图标 */
            icon?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx: number;
            /** @description 资源名称 */
            name: string;
            /** @description 父级资源id，顶级传0 */
            parentId: string;
            /** @description 备注 */
            remark?: string;
            /** @description 资源类型：1-目录， 2-菜单 ，3-按钮 */
            type: string;
            /** @description 访问路径 */
            url?: string;
        };
        /**
         * SystemResourcesUpdateDTO
         * @description 资源信息修改DTO
         */
        SystemResourcesUpdateDTO: {
            /**
             * @description 菜单固定标签
             * @example false
             */
            affix?: boolean;
            /** @description 访问组件名称 */
            componentName?: string;
            /** @description 授权标识 */
            enName?: string;
            /** @description 图标 */
            icon?: string;
            /** @description id主键 */
            id: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx: number;
            /** @description 资源名称 */
            name: string;
            /** @description 父级资源id，顶级传0 */
            parentId: string;
            /** @description 备注 */
            remark?: string;
            /** @description 资源类型：1-目录， 2-菜单 ，3-按钮 */
            type: string;
            /** @description 访问路径 */
            url?: string;
        };
        /**
         * SystemRoleAssignResourceDTO
         * @description 角色分配资源DTO
         */
        SystemRoleAssignResourceDTO: {
            /** @description 角色ID */
            id: string;
            /** @description 资源idList */
            resourceIdList?: string[];
        };
        /**
         * SystemRole对象
         * @description 角色表
         */
        SystemRoleDuiXiang: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 角色名称(英文) */
            enName?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 角色名称 */
            name?: string;
            /** @description 父角色id */
            parentId?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 系统类型:1-三维预案推演系统 2-指挥调度系统 */
            systemType?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * SystemUserAssignRoleDTO
         * @description 用户分配角色DTO
         */
        SystemUserAssignRoleDTO: {
            /** @description 用户ID */
            id: string;
            /** @description 角色idList */
            roleIdList?: string[];
        };
        /**
         * SystemUserChangePasswordDTO
         * @description 修改密码DTO
         */
        SystemUserChangePasswordDTO: {
            /** @description 新密码 */
            newPassword: string;
            /** @description 旧密码 */
            oldPassword: string;
        };
        /**
         * SystemUserDTO对象
         * @description 用户信息表DTO
         */
        SystemUserDTODuiXiang: {
            /** @description 头像 */
            avatar?: string;
            /**
             * Format: date
             * @description 生日
             */
            birthday?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 数据状态（1-未删除，0-删除） */
            dataStatus?: string;
            /** @description 机构id */
            deptId?: string;
            /** @description 机构名 */
            deptName?: string;
            /** @description 邮箱 */
            email?: string;
            /**
             * Format: date-time
             * @description 账户过期时间
             */
            expirationTime?: string;
            /** @description 主键id */
            id?: string;
            /** @description 职务 */
            job?: string;
            /** @description 昵称 */
            nickName?: string;
            /**
             * Format: date-time
             * @description 最后一次修改密码时间
             */
            passwordLastChangeTime?: string;
            /** @description 手机号 */
            phone?: string;
            /** @description 真名 */
            realName?: string;
            /** @description 角色List */
            roleList?: components["schemas"]["SystemRoleDuiXiang"][];
            /** @description 性别（1-男，0-女） */
            sex?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description 用户名 */
            userName?: string;
            /** @description 密码 */
            userPassword?: string;
        };
        /**
         * SystemUser对象
         * @description 用户信息表
         */
        SystemUserDuiXiang: {
            /** @description 头像 */
            avatar?: string;
            /**
             * Format: date
             * @description 生日
             */
            birthday?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 数据状态（1-未删除，0-删除） */
            dataStatus?: string;
            /** @description 机构id */
            deptId?: string;
            /** @description 邮箱 */
            email?: string;
            /**
             * Format: date-time
             * @description 账户过期时间
             */
            expirationTime?: string;
            /** @description 主键id */
            id?: string;
            /** @description 职务 */
            job?: string;
            /** @description 昵称 */
            nickName?: string;
            /**
             * Format: date-time
             * @description 最后一次修改密码时间
             */
            passwordLastChangeTime?: string;
            /** @description 手机号 */
            phone?: string;
            /** @description 真名 */
            realName?: string;
            /** @description 性别（1-男，0-女） */
            sex?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description 用户名 */
            userName?: string;
            /** @description 密码 */
            userPassword?: string;
        };
        /**
         * SystemUserQueryDTO
         * @description 用户信息查询DTO
         */
        SystemUserQueryDTO: {
            /** @description 创建时间 */
            createTime?: string;
            /** @description 机构id */
            deptId?: string;
            /** @description 邮箱 */
            email?: string;
            /** @description 关键字 */
            keyword?: string;
            /** @description 昵称 */
            nickName?: string;
            /** @description 手机号 */
            phone?: string;
            /** @description 真名 */
            realName?: string;
            /** @description 角色类别 */
            roleId?: string;
            /** @description 性别（1-男，0-女） */
            sex?: string;
            /** @description 用户名 */
            userName?: string;
        };
        /**
         * SystemUserSaveDTO
         * @description 用户信息表保存DTO
         */
        SystemUserSaveDTO: {
            /** @description 角色ID */
            roleId: string;
            /** @description 用户编码列表 */
            userCodeList: string[];
        };
        /**
         * TbMessage对象
         * @description 消息通知
         */
        TbMessageDuiXiang: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 主键id */
            id?: string;
            /** @description 是否已读：1-已读 0-未读 */
            isRead?: string;
            /** @description 预案备案ID */
            planFilingsId?: string;
            /** @description 预案名称 */
            planName?: string;
            /** @description 编制单位ID */
            preparationUnitId?: string;
            /** @description 编制单位名称 */
            preparationUnitName?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description 用户ID */
            userId?: string;
        };
        /**
         * TbPlanFilingsQueryDTO
         * @description 预案备案查询对象
         */
        TbPlanFilingsQueryDTO: {
            /** @description 事件类型：1-自然灾害、2-事故灾难、3-社会安全、4-公共卫生、5-其他 */
            eventType?: string;
            /** @description 备案规则ID */
            filingsRuleId?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 关键字 */
            keyword?: string;
            /** @description 预案级别：1--省级、2-市级 */
            planLevel?: string;
            /** @description 预案类型：1-总体应急预案、2-专项应急预案、3-部门应急预案、4-联合应急预案 */
            planType?: string;
            /** @description 编制单位ID */
            preparationUnitId?: string;
        };
        /**
         * TbPlanFilingsSaveDTOReq
         * @description 预案备案保存对象
         */
        TbPlanFilingsSaveDTOReq: {
            /** @description 文号 */
            docNum?: string;
            /** @description 应急预案附件ID */
            emergencyFileId?: string;
            /** @description 事件类型：1-自然灾害、2-事故灾难、3-社会安全、4-公共卫生、5-其他 */
            eventType?: string;
            /** @description 预案简本附件ID */
            filingsFileId?: string;
            /** @description 备案单位ID列表 */
            filingsUnitIdList?: string[];
            /** @description 是否涉密 */
            isClassified?: string;
            /** @description 操作手册附件ID */
            optFileId?: string;
            /** @description 其他附件ID列表 */
            otherFileIdList?: string[];
            /** @description 预案级别：1--省级、2-市级 */
            planLevel?: string;
            /** @description 预案名称 */
            planName?: string;
            /** @description 预案类型：1-总体应急预案、2-专项应急预案、3-部门应急预案、4-联合应急预案 */
            planType?: string;
            /** @description 编制单位ID */
            preparationUnitId?: string;
        };
        /**
         * TbPlanFilingsSaveDTORes
         * @description 预案备案保存对象
         */
        TbPlanFilingsSaveDTORes: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 文号 */
            docNum?: string;
            /** @description 应急预案附件 */
            emergencyFile?: components["schemas"]["SystemFileDuiXiang"];
            /** @description 事件类型：1-自然灾害、2-事故灾难、3-社会安全、4-公共卫生、5-其他 */
            eventType?: string;
            /** @description 预案简本附件 */
            filingsFile?: components["schemas"]["SystemFileDuiXiang"];
            /** @description 备案规则ID */
            filingsRuleId?: string;
            /**
             * Format: date-time
             * @description 备案时间
             */
            filingsTime?: string;
            /** @description 备案单位ID列表 */
            filingsUnitIdList?: string[];
            /** @description 备案单位名称 */
            filingsUnitName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 是否涉密 */
            isClassified?: string;
            /** @description 操作手册附件 */
            optFile?: components["schemas"]["SystemFileDuiXiang"];
            /** @description 其他附件列表 */
            otherFileList?: components["schemas"]["SystemFileDuiXiang"][];
            /** @description 预案级别：1--省级、2-市级 */
            planLevel?: string;
            /** @description 预案名称 */
            planName?: string;
            /** @description 预案类型：1-总体应急预案、2-专项应急预案、3-部门应急预案、4-联合应急预案 */
            planType?: string;
            /** @description 编制单位ID */
            preparationUnitId?: string;
            /** @description 编制单位名称 */
            preparationUnitName?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * TbPlanFilingsStatisticByPlanTypeDTO
         * @description 按照预案类型统计
         */
        TbPlanFilingsStatisticByPlanTypeDTO: {
            /**
             * Format: int32
             * @description 数量
             */
            num?: number;
            /** @description 预案类型 */
            planType?: string;
        };
        /**
         * TbPlanFilingsUpdateDTO
         * @description 预案备案更新对象
         */
        TbPlanFilingsUpdateDTO: {
            /** @description 文号 */
            docNum?: string;
            /** @description 应急预案附件ID */
            emergencyFileId?: string;
            /** @description 事件类型：1-自然灾害、2-事故灾难、3-社会安全、4-公共卫生、5-其他 */
            eventType?: string;
            /** @description 预案简本附件ID */
            filingsFileId?: string;
            /** @description 备案单位ID列表 */
            filingsUnitIdList?: string[];
            /** @description 主键ID */
            id?: string;
            /** @description 是否涉密 */
            isClassified?: string;
            /** @description 操作手册附件ID */
            optFileId?: string;
            /** @description 其他附件ID列表 */
            otherFileIdList?: string[];
            /** @description 预案级别：1--省级、2-市级 */
            planLevel?: string;
            /** @description 预案名称 */
            planName?: string;
            /** @description 预案类型：1-总体应急预案、2-专项应急预案、3-部门应急预案、4-联合应急预案 */
            planType?: string;
            /** @description 编制单位ID */
            preparationUnitId?: string;
        };
        /**
         * TOpLog
         * @description 系统操作日志
         */
        TOpLog: {
            /** @description 请求耗时 */
            handleTime?: string;
            id?: string;
            /** @description 操作事件 */
            opEvent?: string;
            /** @description 入参json */
            opParam?: string;
            /** @description 操作接口 */
            opPort?: string;
            /** @description 出参json */
            opResult?: string;
            /**
             * Format: date-time
             * @description 操作事件
             */
            opTime?: string;
            /** @description 操作用户 */
            opUser?: string;
        };
        /** 图层类型配置属性 */
        TuCengLeiXingPeiZhiShuXing: {
            /** @description auth */
            auth?: string;
            /** @description config */
            config?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 通用样式 */
            customStyle?: string;
            /** @description 默认显示 */
            defaultShowLayer?: string;
            /** @description EGIS密码 */
            egisPassword?: string;
            /** @description EGIS用户名 */
            egisUsername?: string;
            /** @description 瓦片格式 */
            format?: string;
            id?: string;
            /** @description 图层名称 */
            layer?: string;
            /** @description 图层Id */
            layerId?: string;
            /** @description legend */
            legend?: string;
            /**
             * Format: int64
             * @description 最大层级
             */
            maximumLevel?: number;
            /**
             * Format: int64
             * @description 最小层级
             */
            minimumLevel?: number;
            /** @description 图层类型名称 */
            name?: string;
            /** @description 代理URL地址 */
            proxyUrl?: string;
            /** @description 图层范围 */
            rectangle?: string;
            /** @description spare */
            spare?: string;
            /** @description 样式 */
            style?: string;
            /** @description 格网集 */
            tileMatrixSetId?: string;
            /**
             * Format: int64
             * @description 瓦片大小
             */
            titleSize?: number;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description URL地址 */
            url?: string;
            /** @description 用户名 */
            userName?: string;
        };
        /** UploadProgress */
        UploadProgress: {
            /** Format: int64 */
            bytesRead?: number;
            /** Format: int64 */
            contentLength?: number;
            done?: boolean;
        };
        /**
         * UserRoleNumStatisticDTO
         * @description 用户角色数量信息查询
         */
        UserRoleNumStatisticDTO: {
            /**
             * Format: int32
             * @description 管理人员数量
             */
            adminNum?: number;
            /**
             * Format: int32
             * @description 普通人员数量
             */
            commonNum?: number;
        };
        /**
         * VisitLog
         * @description 浏览记录
         */
        VisitLog: {
            /**
             * Format: int32
             * @description 下载次数
             */
            downloadCount?: number;
            /**
             * Format: int32
             * @description 主键id
             */
            id?: number;
            /**
             * Format: int32
             * @description 登录次数
             */
            loginCount?: number;
            /**
             * Format: int32
             * @description 其他次数
             */
            otherCount?: number;
        };
        /** 文件设置实体 */
        WenJianSheZhiShiTi: {
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 文件名 */
            fileName?: string;
            /** @description 文件url */
            fileUrl?: string;
            /** @description ID */
            id?: string;
            /** @description 用户名 */
            userName?: string;
        };
        /** 系统设置实体类 */
        XiTongSheZhiShiTiLei: {
            /** @description 动画 */
            animation?: string;
            /** @description 大气 */
            atmosphere?: string;
            /** @description baseURL */
            baseUrl?: string;
            /** @description 北斗网格码名称 */
            beidouNetName?: string;
            /** @description 北斗网格码地址 */
            beidouNetUrl?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 创建人账号 */
            creator?: string;
            /** @description 三维数据处理地址名称 */
            dataProcessingName?: string;
            /** @description 三维数据处理地址 */
            dataProcessingUrl?: string;
            /** @description 数据资源名称 */
            dataResourcesName?: string;
            /** @description 数据资源url */
            dataResourcesUrl?: string;
            /** @description 初始化视角 */
            defaultAngle?: string;
            /** @description 初始行政区划 */
            defaultUnit?: string;
            /** @description 初始行政区划名称 */
            defaultUnitName?: string;
            /** @description icenter名称 */
            icenterName?: string;
            /** @description icenter url */
            icenterUrl?: string;
            id?: string;
            /** @description 是否显示行政区划边界 */
            isShowUnitBorder?: string;
            /** @description 光照 */
            light?: string;
            /** @description 经纬度格式 */
            locationFormat?: string;
            /** @description 系统logo地址 */
            logoUrl?: string;
            /** @description 度量单位 */
            measuringUnit?: string;
            /** @description 系统菜单字符串 */
            menus?: string;
            /** @description 中台门户名称 */
            middlePlatformName?: string;
            /** @description 中台门户url */
            middlePlatformUrl?: string;
            /**
             * Format: int64
             * @description 分辨率
             */
            resolution?: number;
            /** @description 服务资源名称 */
            servicesResourcesName?: string;
            /** @description 服务资源 url */
            servicesResourcesUrl?: string;
            /** @description 天空盒 */
            skyBox?: string;
            /** @description 时空中台名称 */
            spaceTimePlatformName?: string;
            /** @description 时空中台url */
            spaceTimePlatformUrl?: string;
            /** @description 时空门户名称 */
            spaceTimeWebName?: string;
            /** @description 时空门户url */
            spaceTimeWebUrl?: string;
            /** @description 边界颜色 */
            stroke?: string;
            /** @description 边界宽度 */
            strokeWidth?: string;
            /** @description 地表要素提取名称 */
            surfaceExtractName?: string;
            /** @description 地表要素提取地址 */
            surfaceExtractUrl?: string;
            /** @description 系统标题 */
            sysTitle?: string;
            /** @description 主题色 */
            themeColor?: string;
            /** @description 行政区划边界 */
            unitBorder?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description 用户名 */
            userName?: string;
        };
        /** 系统设置VO */
        XiTongSheZhiVO: {
            /** @description 动画 */
            animation?: string;
            /** @description 大气 */
            atmosphere?: string;
            /** @description baseURL */
            baseUrl?: string;
            /** @description 北斗网格码名称 */
            beidouNetName?: string;
            /** @description 北斗网格码地址 */
            beidouNetUrl?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 创建人账号 */
            creator?: string;
            /** @description 三维数据处理地址名称 */
            dataProcessingName?: string;
            /** @description 三维数据处理地址 */
            dataProcessingUrl?: string;
            /** @description 数据资源名称 */
            dataResourcesName?: string;
            /** @description 数据资源url */
            dataResourcesUrl?: string;
            /** @description 初始化视角 */
            defaultAngle?: string;
            /** @description 初始行政区划 */
            defaultUnit?: string;
            /** @description 初始行政区划名称 */
            defaultUnitName?: string;
            /** @description icenter名称 */
            icenterName?: string;
            /** @description icenter url */
            icenterUrl?: string;
            id?: string;
            /** @description 是否显示行政区划边界 */
            isShowUnitBorder?: string;
            /** @description 光照 */
            light?: string;
            /** @description 经纬度格式 */
            locationFormat?: string;
            /** @description 系统logo地址 */
            logoUrl?: string;
            /** @description 度量单位 */
            measuringUnit?: string;
            /** @description 系统菜单字符串 */
            menus?: string;
            /** @description 中台门户名称 */
            middlePlatformName?: string;
            /** @description 中台门户url */
            middlePlatformUrl?: string;
            /**
             * Format: int64
             * @description 分辨率
             */
            resolution?: number;
            /** @description 服务资源名称 */
            servicesResourcesName?: string;
            /** @description 服务资源 url */
            servicesResourcesUrl?: string;
            /** @description 天空盒 */
            skyBox?: string;
            /** @description 时空中台名称 */
            spaceTimePlatformName?: string;
            /** @description 时空中台url */
            spaceTimePlatformUrl?: string;
            /** @description 时空门户名称 */
            spaceTimeWebName?: string;
            /** @description 时空门户url */
            spaceTimeWebUrl?: string;
            /** @description 边界颜色 */
            stroke?: string;
            /** @description 边界宽度 */
            strokeWidth?: string;
            /** @description 地表要素提取名称 */
            surfaceExtractName?: string;
            /** @description 地表要素提取地址 */
            surfaceExtractUrl?: string;
            /** @description 系统标题 */
            sysTitle?: string;
            /** @description 主题色 */
            themeColor?: string;
            /** @description 行政区划边界 */
            unitBorder?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description 用户名 */
            userName?: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    loginAuthUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    loginAuthUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    deleteByIdUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    lastUpdateTimeUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultLocalDateTime"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_7: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamTbMessageDuiXiangReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamTbMessageDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    notReadNumUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    readAllUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    readByIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadFilesUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    lastUpdateTimeUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultLocalDateTime"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_8: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamTbPlanFilingsQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamTbPlanFilingsSaveDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST_7: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": unknown;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    savePlanFilingsUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TbPlanFilingsSaveDTOReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    statisticByPlanTypeUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TbPlanFilingsQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTbPlanFilingsStatisticByPlanTypeDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updatePlanFilingsUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TbPlanFilingsUpdateDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    deleteByIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fileListUsingGET: {
        parameters: {
            query?: {
                /** @description 对应每一层目录文件 */
                fileName?: string;
                /** @description 文件目录, source: 源文件, target: 目标文件 */
                type?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getBySystemIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getBySystemIdUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamChangeDataTaskReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamChangeDataTask"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getProgressUsingGET: {
        parameters: {
            query: {
                /** @description processId */
                processId: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["UploadProgress"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveOrUpdateLabelUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ChangeDataTask"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateFileListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["FileListOpt"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    uploadChangeFileUsingPOST: {
        parameters: {
            query?: {
                /** @description 上传文件 */
                multipartFile?: string;
                /** @description 上传文件路径 */
                uploadFilePath?: string;
            };
            header: {
                /** @description 获取id 用于查询进度 */
                processId: string;
            };
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadFileUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fileDir */
                fileDir: string;
                /** @description fileName */
                fileName: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["FileSystemResource"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    uploadMultipleFilesUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string[];
                "application/octet-stream": string[];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string[];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    uploadMultipleFilesUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
                "application/octet-stream": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string[];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    uploadMultipleFilesUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string[];
                "application/octet-stream": string[];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    deleteFileSettingsByIdUsingDELETE: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": boolean;
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadFileUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fileDir */
                fileDir: string;
                /** @description fileName */
                fileName: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["FileSystemResource"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadPhotoFileUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fileDir */
                fileDir: string;
                /** @description fileName */
                fileName: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "image/png": components["schemas"]["FileSystemResource"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFileSettingsByIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["WenJianSheZhiShiTi"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getAllFileSettingsUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamWenJianSheZhiShiTi"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParam"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateFileSettingsUsingPUT: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WenJianSheZhiShiTi"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": boolean;
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    uploadFileUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
                "application/octet-stream": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    deleteByIdUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getLayerInfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListLayerInfoVO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getLayerTypeInfoUsingGET: {
        parameters: {
            query?: {
                /** @description layerId */
                layerId?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTuCengLeiXingPeiZhiShuXing"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSystemSettingsByUserCodeUsingGET: {
        parameters: {
            query?: {
                /** @description userCode */
                userCode?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultXiTongSheZhiVO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveOrUpdateLayerInfoUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["LayerInfoDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveOrUpdateLayerTypeUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TuCengLeiXingPeiZhiShuXing"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveOrUpdateSystemSettingsUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["XiTongSheZhiShiTiLei"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamShiJianDuan"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamTOpLog"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    registerUsingGET_1: {
        parameters: {
            query: {
                /** @description user */
                user: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    registerUsingGET: {
        parameters: {
            query: {
                /** @description pwd */
                pwd: string;
                /** @description sameUser */
                sameUser: string;
                /** @description user */
                user: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    callExternalApiUsingGET: {
        parameters: {
            query: {
                /** @description url */
                url: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "image/png": components["schemas"]["Resource"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    deleteByIdUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    exportJsonUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSceneInfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultBiaoHuiTuCeng"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    parseJsonUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
                "application/octet-stream": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultboolean"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamBiaoHuiTuCengReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamBiaoHuiTuCeng"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveOrUpdateSceneInfoUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["BiaoHuiTuCeng"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDeptDuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSystemDeptDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": unknown;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDeptSaveDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDeptUpdateDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSystemDictDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamSystemDictQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSystemDictDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": unknown;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDictSaveDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDictUpdateDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDictDataQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSystemDictDataDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": unknown;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDictDataSaveDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemDictDataUpdateDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadFilesUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadLocalFileUsingGET: {
        parameters: {
            query: {
                /** @description fileName */
                fileName: string;
                /** @description localFileDir */
                localFileDir?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemFileDuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSystemFileDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listUsingPOST_3: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemFileQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSystemFileDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_3: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamSystemFileDuiXiangReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSystemFileDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST_3: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": unknown;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    uploadUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
                "application/octet-stream": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultSystemFileDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_4: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamSystemLogQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSystemLogDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUsingPOST_3: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemLogQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getByLogIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description logId */
                logId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultSystemLogExtend"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getAllUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getResourcesUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 模块id */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_5: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamSystemModuleDuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSystemUserDTODuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listResourcesByModuleIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST_4: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string[];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUsingPOST_4: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemModuleDuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    settingResourcesUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemModuleDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateUsingPOST_3: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemModuleDuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listUsingPOST_4: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemResourcesQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSystemResourcesDTODuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST_5: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": unknown;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUsingPOST_5: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemResourcesSaveDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateUsingPOST_4: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemResourcesUpdateDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    assignResourceUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemRoleAssignResourceDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    assignRoleUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemUserAssignRoleDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    changePasswordUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemUserChangePasswordDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getCurrUserInfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultLoginUserInfoDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemUserQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSystemUserDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getUserRoleNumStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultUserRoleNumStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    lastUpdateTimeUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultLocalDateTime"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_6: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamSystemUserQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSystemUserDTODuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeByIdListUsingPOST_6: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": unknown;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeUserFromSpdUsingGET: {
        parameters: {
            query?: {
                /** @description names */
                names?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUserUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemUserSaveDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUserFromSpdUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemUserSaveDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateUserFromSpdUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["SystemUserSaveDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    testUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getVisitLogUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultVisitLog"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
}
