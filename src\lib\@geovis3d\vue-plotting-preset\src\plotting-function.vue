<!-- 标绘功能树 -->
<script lang="ts" setup>
import type { ScrollbarInstance } from 'element-plus';
import { ElScrollbar } from 'element-plus';

import { computed, ref } from 'vue';
import GridView from './grid-view.vue';
import { usePlotInjectState } from './state';

defineOptions({ name: 'PlottingPresetFunction' });

const { plotTreeList } = usePlotInjectState()!;

const filterKeyword = refThrottled(ref(''), 300, true, false);

const treeList = computed(() => {
  return plotTreeList.value;
});

const collapseValues = ref<string[]>([]);

watchEffect(() => {
  collapseValues.value = plotTreeList.value.map(e => e.uuid!);
});

const scrollbarRef = shallowRef<ScrollbarInstance>();

const isntance = getCurrentInstance();

async function scrollTo(ref: string) {
  collapseValues.value = treeList.value.map(e => e.uuid!);
  await nextTick();
  scrollbarRef.value?.setScrollTop(isntance?.refs?.[ref]?.[0]?.offsetTop);
}
</script>

<template>
  <el-input
    v-model="filterKeyword"
    class="search mb-5px mt-16px h-42px px-24px"
    placeholder="请输入关键字"
  />
  <el-row class="anchor-button-group px-24px">
    <div
      v-for="item in treeList"
      :key="item.uuid"
      class="anchor-button"
      @click="scrollTo(item.uuid!)"
    >
      {{ item.label }}
    </div>
  </el-row>
  <!-- 树图 -->
  <ElScrollbar ref="scrollbarRef" flex="1" px-24px>
    <el-collapse v-model="collapseValues">
      <el-collapse-item
        v-for="item in treeList"
        :key="item.uuid"
        :title="item.label"
        :name="item.uuid"
      >
        <template #title>
          <el-icon
            class="leader-icon i-material-symbols:arrow-right"
            :class="{ active: collapseValues.includes(item.uuid!) }"
          />
          <div :ref="item.uuid">
            {{ item.label }}
          </div>
        </template>
        <GridView :data="item.children ?? []" />
      </el-collapse-item>
    </el-collapse>
  </ElScrollbar>
</template>

<style scoped lang="scss">
.leader-icon {
  transition: transform 0.3s;

  &.active {
    transform: rotate(90deg);
  }
}

.anchor-button-group {
  display: flex;
  padding: 10px 24px 5px;
}

.el-collapse-item {
  :deep(.el-collapse-item__arrow) {
    display: none;
  }
}

.anchor-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  margin-right: 4px;
  margin-bottom: 4px;
  font-size: 12px;
  cursor: pointer;
  border: 1px solid rgb(155 193 255 / 30%);
  border-radius: 2px;
  transition: background 0.3s;

  &:hover {
    background: rgb(155 193 255 / 30%);
  }
}

.el-scrollbar {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 0;
  max-height: calc(100vh - 300px);
  overflow: hidden;
}

.breadcrumb {
  display: flex;
  margin-bottom: 20px;
  font-size: 14px;
  cursor: default;

  .breadcrumb-button,
  .breadcrumb-divider,
  .breadcrumb-label {
    padding: 0 6px;
  }

  .breadcrumb-button {
    font-weight: bold;
    color: #fff;
    cursor: pointer;
  }

  .breadcrumb-label {
    color: rgb(179 179 179);
    cursor: not-allowed;
  }
}
</style>
