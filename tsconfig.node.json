{"extends": "@tsconfig/node20/tsconfig.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "target": "esnext", "lib": ["esnext"], "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "types": ["node"], "noEmit": true, "forceConsistentCasingInFileNames": true}, "include": ["./internals/**/*", "vite.config.*", "vite.*.*", "uno.config.*"]}