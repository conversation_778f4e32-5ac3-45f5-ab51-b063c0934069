import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.ClassificationPrimitive} 构造函数参数
 */
export type ClassificationPrimitiveConstructorOptions = ConstructorParameters<
  typeof Cesium.ClassificationPrimitive
>[0];

/**
 * {@link Cesium.ClassificationPrimitive} 拓展用法与 {@link Cesium.ClassificationPrimitive} 基本一致。
 *
 * `GcClassificationPrimitive.event`鼠标事件监听
 */
export class GcClassificationPrimitive extends Cesium.ClassificationPrimitive {
  constructor(options?: ClassificationPrimitiveConstructorOptions) {
    super(options);

    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
