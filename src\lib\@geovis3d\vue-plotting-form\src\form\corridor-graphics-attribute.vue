<!-- CorridorGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { CorridorGraphicsKey, CorridorGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { CorridorGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian3ArrayAttribute from './cartesian3-array-attribute.vue';
import ClassificationTypeAttribute from './classification-type-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import CornerTypeAttribute from './corner-type-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'CorridorGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: CorridorGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.CorridorGraphics, CorridorGraphicsSerializateJSON>({
  graphic: () => props.entity?.corridor,
  omit: props.omit,
  toJSON: (graphics, omit) => CorridorGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => CorridorGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="corridor"
    graphics-field="show"
    label="可见"
  />
  <Cartesian3ArrayAttribute
    v-if="!hide?.includes('positions')"
    v-model="model.positions"
    graphics="corridor"
    graphics-field="positions"
    label="positions"
  />
  <NumberAttribute
    v-if="!hide?.includes('width')"
    v-model="model.width"
    graphics="corridor"
    graphics-field="width"
    label="宽"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('height')"
    v-model="model.height"
    graphics="corridor"
    graphics-field="height"
    label="高"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="corridor"
    graphics-field="heightReference"
    label="高度参照"
  />
  <NumberAttribute
    v-if="!hide?.includes('extrudedHeight')"
    v-model="model.extrudedHeight"
    graphics="corridor"
    graphics-field="extrudedHeight"
    label="拉伸高度"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('extrudedHeightReference')"
    v-model="model.extrudedHeightReference"
    graphics="corridor"
    graphics-field="extrudedHeightReference"
    label="拉伸参照"
  />
  <CornerTypeAttribute
    v-if="!hide?.includes('cornerType')"
    v-model="model.cornerType"
    graphics="corridor"
    graphics-field="cornerType"
    label="拐角类型"
  />
  <NumberAttribute
    v-if="!hide?.includes('granularity')"
    v-model="model.granularity"
    graphics="corridor"
    graphics-field="granularity"
    label="渲染粒度"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="corridor"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="corridor"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="corridor"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="corridor"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="corridor"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="corridor"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="corridor"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <ClassificationTypeAttribute
    v-if="!hide?.includes('classificationType')"
    v-model="model.classificationType"
    graphics="corridor"
    graphics-field="classificationType"
    label="贴地类型"
  />
  <NumberAttribute
    v-if="!hide?.includes('zIndex')"
    v-model="model.zIndex"
    graphics="corridor"
    graphics-field="zIndex"
    label="层级"
    :precision="0"
  />
</template>
