/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by @xiankq/openapi-typescript-expand
// Power by openapi-typescript

import {bdvRequest} from "./request";



/**
 * @tag 自然灾害-防汛防风专题
 * @summary 政务基底查询
 * @url /disaster/floodWindPrevention/administrativeBaseById/{id}
 * @method post
 * @description 政务基底查询
 */

export module DisasterFloodWindPreventionAdministrativeBaseByIdIdUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/administrativeBaseById/{id}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 政务基底查询
 * @url /disaster/floodWindPrevention/administrativeBaseById/{id}
 * @method post
 * @description 政务基底查询
 */

export function disasterFloodWindPreventionAdministrativeBaseByIdIdUsingPost(options:DisasterFloodWindPreventionAdministrativeBaseByIdIdUsingPost.Options):Promise<DisasterFloodWindPreventionAdministrativeBaseByIdIdUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/administrativeBaseById/{id}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人明细查询
 * @url /disaster/floodWindPrevention/dutyPersonById/{id}
 * @method get
 * @description 三方责任人明细查询
 */

export module DisasterFloodWindPreventionDutyPersonByIdIdUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/dutyPersonById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人明细查询
 * @url /disaster/floodWindPrevention/dutyPersonById/{id}
 * @method get
 * @description 三方责任人明细查询
 */

export function disasterFloodWindPreventionDutyPersonByIdIdUsingGet(options:DisasterFloodWindPreventionDutyPersonByIdIdUsingGet.Options):Promise<DisasterFloodWindPreventionDutyPersonByIdIdUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/dutyPersonById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人数量总数
 * @url /disaster/floodWindPrevention/dutyPersonCount/{year}
 * @method get
 * @description 三方责任人数量总数
 */

export module DisasterFloodWindPreventionDutyPersonCountYearUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/dutyPersonCount/{year}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人数量总数
 * @url /disaster/floodWindPrevention/dutyPersonCount/{year}
 * @method get
 * @description 三方责任人数量总数
 */

export function disasterFloodWindPreventionDutyPersonCountYearUsingGet(options:DisasterFloodWindPreventionDutyPersonCountYearUsingGet.Options):Promise<DisasterFloodWindPreventionDutyPersonCountYearUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/dutyPersonCount/{year}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人分页查询
 * @url /disaster/floodWindPrevention/dutyPersonListPage
 * @method post
 * @description 三方责任人分页查询
 */

export module DisasterFloodWindPreventionDutyPersonListPageUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/dutyPersonListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人分页查询
 * @url /disaster/floodWindPrevention/dutyPersonListPage
 * @method post
 * @description 三方责任人分页查询
 */

export function disasterFloodWindPreventionDutyPersonListPageUsingPost(options:DisasterFloodWindPreventionDutyPersonListPageUsingPost.Options):Promise<DisasterFloodWindPreventionDutyPersonListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/dutyPersonListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人点位列表查询
 * @url /disaster/floodWindPrevention/dutyPersonPointsList/{year}
 * @method get
 * @description 三方责任人点位列表查询
 */

export module DisasterFloodWindPreventionDutyPersonPointsListYearUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/dutyPersonPointsList/{year}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三方责任人点位列表查询
 * @url /disaster/floodWindPrevention/dutyPersonPointsList/{year}
 * @method get
 * @description 三方责任人点位列表查询
 */

export function disasterFloodWindPreventionDutyPersonPointsListYearUsingGet(options:DisasterFloodWindPreventionDutyPersonPointsListYearUsingGet.Options):Promise<DisasterFloodWindPreventionDutyPersonPointsListYearUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/dutyPersonPointsList/{year}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防责任人下载
 * @url /disaster/floodWindPrevention/dutyPersonReport
 * @method post
 * @description 三方责任人数量总数
 */

export module DisasterFloodWindPreventionDutyPersonReportUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/dutyPersonReport']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防责任人下载
 * @url /disaster/floodWindPrevention/dutyPersonReport
 * @method post
 * @description 三方责任人数量总数
 */

export function disasterFloodWindPreventionDutyPersonReportUsingPost(options:DisasterFloodWindPreventionDutyPersonReportUsingPost.Options):Promise<DisasterFloodWindPreventionDutyPersonReportUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/dutyPersonReport',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防责任人市县数量统计
 * @url /disaster/floodWindPrevention/dutyPersonStatistic/{year}
 * @method get
 * @description 三防责任人市县数量统计
 */

export module DisasterFloodWindPreventionDutyPersonStatisticYearUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/dutyPersonStatistic/{year}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防责任人市县数量统计
 * @url /disaster/floodWindPrevention/dutyPersonStatistic/{year}
 * @method get
 * @description 三防责任人市县数量统计
 */

export function disasterFloodWindPreventionDutyPersonStatisticYearUsingGet(options:DisasterFloodWindPreventionDutyPersonStatisticYearUsingGet.Options):Promise<DisasterFloodWindPreventionDutyPersonStatisticYearUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/dutyPersonStatistic/{year}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 仓库应急物资详情
 * @url /disaster/floodWindPrevention/emergencySuppliesList/{tableRowId}
 * @method post
 * @description 仓库应急物资详情
 */

export module DisasterFloodWindPreventionEmergencySuppliesListTableRowIdUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/emergencySuppliesList/{tableRowId}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 仓库应急物资详情
 * @url /disaster/floodWindPrevention/emergencySuppliesList/{tableRowId}
 * @method post
 * @description 仓库应急物资详情
 */

export function disasterFloodWindPreventionEmergencySuppliesListTableRowIdUsingPost(options:DisasterFloodWindPreventionEmergencySuppliesListTableRowIdUsingPost.Options):Promise<DisasterFloodWindPreventionEmergencySuppliesListTableRowIdUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/emergencySuppliesList/{tableRowId}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 应急专家分页查询
 * @url /disaster/floodWindPrevention/exportListPage
 * @method post
 * @description 应急专家分页查询
 */

export module DisasterFloodWindPreventionExportListPageUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/exportListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 应急专家分页查询
 * @url /disaster/floodWindPrevention/exportListPage
 * @method post
 * @description 应急专家分页查询
 */

export function disasterFloodWindPreventionExportListPageUsingPost(options:DisasterFloodWindPreventionExportListPageUsingPost.Options):Promise<DisasterFloodWindPreventionExportListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/exportListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 风险隐患类型统计
 * @url /disaster/floodWindPrevention/hazTypeStatistic
 * @method get
 * @description 风险隐患类型统计
 */

export module DisasterFloodWindPreventionHazTypeStatisticUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/hazTypeStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 风险隐患类型统计
 * @url /disaster/floodWindPrevention/hazTypeStatistic
 * @method get
 * @description 风险隐患类型统计
 */

export function disasterFloodWindPreventionHazTypeStatisticUsingGet(options:DisasterFloodWindPreventionHazTypeStatisticUsingGet.Options):Promise<DisasterFloodWindPreventionHazTypeStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/hazTypeStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 风险隐患年度统计
 * @url /disaster/floodWindPrevention/hazYearStatistic
 * @method get
 * @description 风险隐患年度统计
 */

export module DisasterFloodWindPreventionHazYearStatisticUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/hazYearStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 风险隐患年度统计
 * @url /disaster/floodWindPrevention/hazYearStatistic
 * @method get
 * @description 风险隐患年度统计
 */

export function disasterFloodWindPreventionHazYearStatisticUsingGet(options:DisasterFloodWindPreventionHazYearStatisticUsingGet.Options):Promise<DisasterFloodWindPreventionHazYearStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/hazYearStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 防汛防风分页查询
 * @url /disaster/floodWindPrevention/listPage
 * @method post
 * @description 防汛防风分页查询
 */

export module DisasterFloodWindPreventionListPageUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 防汛防风分页查询
 * @url /disaster/floodWindPrevention/listPage
 * @method post
 * @description 防汛防风分页查询
 */

export function disasterFloodWindPreventionListPageUsingPost(options:DisasterFloodWindPreventionListPageUsingPost.Options):Promise<DisasterFloodWindPreventionListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 模块维度数量统计
 * @url /disaster/floodWindPrevention/numberStatistic
 * @method post
 * @description 模块维度数量统计
 */

export module DisasterFloodWindPreventionNumberStatisticUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/numberStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 模块维度数量统计
 * @url /disaster/floodWindPrevention/numberStatistic
 * @method post
 * @description 模块维度数量统计
 */

export function disasterFloodWindPreventionNumberStatisticUsingPost(options:DisasterFloodWindPreventionNumberStatisticUsingPost.Options):Promise<DisasterFloodWindPreventionNumberStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/numberStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 报表下载
 * @url /disaster/floodWindPrevention/report
 * @method post
 * @description 报表下载
 */

export module DisasterFloodWindPreventionReportUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/report']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 报表下载
 * @url /disaster/floodWindPrevention/report
 * @method post
 * @description 报表下载
 */

export function disasterFloodWindPreventionReportUsingPost(options:DisasterFloodWindPreventionReportUsingPost.Options):Promise<DisasterFloodWindPreventionReportUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/report',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库市县统计
 * @url /disaster/floodWindPrevention/sfReservoirCityStatistic
 * @method get
 * @description 三防信息网-水库市县统计
 */

export module DisasterFloodWindPreventionSfReservoirCityStatisticUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirCityStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库市县统计
 * @url /disaster/floodWindPrevention/sfReservoirCityStatistic
 * @method get
 * @description 三防信息网-水库市县统计
 */

export function disasterFloodWindPreventionSfReservoirCityStatisticUsingGet(options:DisasterFloodWindPreventionSfReservoirCityStatisticUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirCityStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirCityStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库数量查询
 * @url /disaster/floodWindPrevention/sfReservoirCount
 * @method get
 * @description 三防信息网-水库数量查询
 */

export module DisasterFloodWindPreventionSfReservoirCountUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirCount']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库数量查询
 * @url /disaster/floodWindPrevention/sfReservoirCount
 * @method get
 * @description 三防信息网-水库数量查询
 */

export function disasterFloodWindPreventionSfReservoirCountUsingGet(options:DisasterFloodWindPreventionSfReservoirCountUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirCountUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirCount',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库详情
 * @url /disaster/floodWindPrevention/sfReservoirDetail/{id}
 * @method get
 * @description 三防信息网-水库详情
 */

export module DisasterFloodWindPreventionSfReservoirDetailIdUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirDetail/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库详情
 * @url /disaster/floodWindPrevention/sfReservoirDetail/{id}
 * @method get
 * @description 三防信息网-水库详情
 */

export function disasterFloodWindPreventionSfReservoirDetailIdUsingGet(options:DisasterFloodWindPreventionSfReservoirDetailIdUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirDetailIdUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirDetail/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库矢量数据查询
 * @url /disaster/floodWindPrevention/sfReservoirGeometry/{id}
 * @method get
 * @description 三防信息网-水库矢量数据查询
 */

export module DisasterFloodWindPreventionSfReservoirGeometryIdUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirGeometry/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库矢量数据查询
 * @url /disaster/floodWindPrevention/sfReservoirGeometry/{id}
 * @method get
 * @description 三防信息网-水库矢量数据查询
 */

export function disasterFloodWindPreventionSfReservoirGeometryIdUsingGet(options:DisasterFloodWindPreventionSfReservoirGeometryIdUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirGeometryIdUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirGeometry/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-超汛线24小时趋势统计：1-水库 2-河流
 * @url /disaster/floodWindPrevention/sfReservoirHourStatistic
 * @method get
 * @description 三防信息网-超汛线24小时趋势统计：1-水库 2-河流
 */

export module DisasterFloodWindPreventionSfReservoirHourStatisticUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirHourStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-超汛线24小时趋势统计：1-水库 2-河流
 * @url /disaster/floodWindPrevention/sfReservoirHourStatistic
 * @method get
 * @description 三防信息网-超汛线24小时趋势统计：1-水库 2-河流
 */

export function disasterFloodWindPreventionSfReservoirHourStatisticUsingGet(options:DisasterFloodWindPreventionSfReservoirHourStatisticUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirHourStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirHourStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库分页查询
 * @url /disaster/floodWindPrevention/sfReservoirListPage
 * @method post
 * @description 三防信息网-水库分页查询
 */

export module DisasterFloodWindPreventionSfReservoirListPageUsingPost {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库分页查询
 * @url /disaster/floodWindPrevention/sfReservoirListPage
 * @method post
 * @description 三防信息网-水库分页查询
 */

export function disasterFloodWindPreventionSfReservoirListPageUsingPost(options:DisasterFloodWindPreventionSfReservoirListPageUsingPost.Options):Promise<DisasterFloodWindPreventionSfReservoirListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库预案详情
 * @url /disaster/floodWindPrevention/sfReservoirPlanByCode/{reservoirCode}
 * @method get
 * @description 三防信息网-水库预案详情
 */

export module DisasterFloodWindPreventionSfReservoirPlanByCodeReservoirCodeUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirPlanByCode/{reservoirCode}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库预案详情
 * @url /disaster/floodWindPrevention/sfReservoirPlanByCode/{reservoirCode}
 * @method get
 * @description 三防信息网-水库预案详情
 */

export function disasterFloodWindPreventionSfReservoirPlanByCodeReservoirCodeUsingGet(options:DisasterFloodWindPreventionSfReservoirPlanByCodeReservoirCodeUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirPlanByCodeReservoirCodeUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirPlanByCode/{reservoirCode}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库上球列表
 * @url /disaster/floodWindPrevention/sfReservoirPointsList
 * @method get
 * @description 三防信息网-水库上球列表
 */

export module DisasterFloodWindPreventionSfReservoirPointsListUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirPointsList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库上球列表
 * @url /disaster/floodWindPrevention/sfReservoirPointsList
 * @method get
 * @description 三防信息网-水库上球列表
 */

export function disasterFloodWindPreventionSfReservoirPointsListUsingGet(options:DisasterFloodWindPreventionSfReservoirPointsListUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirPointsListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirPointsList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库预案下载
 * @url /disaster/floodWindPrevention/sfReservoirReservePlanDownload/{reservoirCode}
 * @method get
 * @description 三防信息网-水库预案下载
 */

export module DisasterFloodWindPreventionSfReservoirReservePlanDownloadReservoirCodeUsingGet {
  export type Operation = paths['/disaster/floodWindPrevention/sfReservoirReservePlanDownload/{reservoirCode}']['get'];
  export type Result = any;
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-防汛防风专题
 * @summary 三防信息网-水库预案下载
 * @url /disaster/floodWindPrevention/sfReservoirReservePlanDownload/{reservoirCode}
 * @method get
 * @description 三防信息网-水库预案下载
 */

export function disasterFloodWindPreventionSfReservoirReservePlanDownloadReservoirCodeUsingGet(options:DisasterFloodWindPreventionSfReservoirReservePlanDownloadReservoirCodeUsingGet.Options):Promise<DisasterFloodWindPreventionSfReservoirReservePlanDownloadReservoirCodeUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/floodWindPrevention/sfReservoirReservePlanDownload/{reservoirCode}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录明细查询
 * @url /disaster/forestFire/dutyPersonById/{id}
 * @method get
 * @description 森林防火通讯录明细查询
 */

export module DisasterForestFireDutyPersonByIdIdUsingGet {
  export type Operation = paths['/disaster/forestFire/dutyPersonById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录明细查询
 * @url /disaster/forestFire/dutyPersonById/{id}
 * @method get
 * @description 森林防火通讯录明细查询
 */

export function disasterForestFireDutyPersonByIdIdUsingGet(options:DisasterForestFireDutyPersonByIdIdUsingGet.Options):Promise<DisasterForestFireDutyPersonByIdIdUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/dutyPersonById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录下载
 * @url /disaster/forestFire/dutyPersonDownload
 * @method post
 * @description 森林防火通讯录下载
 */

export module DisasterForestFireDutyPersonDownloadUsingPost {
  export type Operation = paths['/disaster/forestFire/dutyPersonDownload']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录下载
 * @url /disaster/forestFire/dutyPersonDownload
 * @method post
 * @description 森林防火通讯录下载
 */

export function disasterForestFireDutyPersonDownloadUsingPost(options:DisasterForestFireDutyPersonDownloadUsingPost.Options):Promise<DisasterForestFireDutyPersonDownloadUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/dutyPersonDownload',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录分页查询
 * @url /disaster/forestFire/dutyPersonListPage
 * @method post
 * @description 森林防火通讯录分页查询
 */

export module DisasterForestFireDutyPersonListPageUsingPost {
  export type Operation = paths['/disaster/forestFire/dutyPersonListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录分页查询
 * @url /disaster/forestFire/dutyPersonListPage
 * @method post
 * @description 森林防火通讯录分页查询
 */

export function disasterForestFireDutyPersonListPageUsingPost(options:DisasterForestFireDutyPersonListPageUsingPost.Options):Promise<DisasterForestFireDutyPersonListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/dutyPersonListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录点位列表查询
 * @url /disaster/forestFire/dutyPersonPointsList
 * @method get
 * @description 森林防火通讯录点位列表查询
 */

export module DisasterForestFireDutyPersonPointsListUsingGet {
  export type Operation = paths['/disaster/forestFire/dutyPersonPointsList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林防火通讯录点位列表查询
 * @url /disaster/forestFire/dutyPersonPointsList
 * @method get
 * @description 森林防火通讯录点位列表查询
 */

export function disasterForestFireDutyPersonPointsListUsingGet(options:DisasterForestFireDutyPersonPointsListUsingGet.Options):Promise<DisasterForestFireDutyPersonPointsListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/dutyPersonPointsList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾明细查询
 * @url /disaster/forestFire/fireById/{id}
 * @method get
 * @description 森林火灾明细查询
 */

export module DisasterForestFireFireByIdIdUsingGet {
  export type Operation = paths['/disaster/forestFire/fireById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾明细查询
 * @url /disaster/forestFire/fireById/{id}
 * @method get
 * @description 森林火灾明细查询
 */

export function disasterForestFireFireByIdIdUsingGet(options:DisasterForestFireFireByIdIdUsingGet.Options):Promise<DisasterForestFireFireByIdIdUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/fireById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林危险信息(市县统计)
 * @url /disaster/forestFire/fireCityStatistic/{year}
 * @method get
 * @description 森林危险信息(市县统计)
 */

export module DisasterForestFireFireCityStatisticYearUsingGet {
  export type Operation = paths['/disaster/forestFire/fireCityStatistic/{year}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林危险信息(市县统计)
 * @url /disaster/forestFire/fireCityStatistic/{year}
 * @method get
 * @description 森林危险信息(市县统计)
 */

export function disasterForestFireFireCityStatisticYearUsingGet(options:DisasterForestFireFireCityStatisticYearUsingGet.Options):Promise<DisasterForestFireFireCityStatisticYearUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/fireCityStatistic/{year}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾下载
 * @url /disaster/forestFire/fireDownload
 * @method post
 * @description 森林火灾下载
 */

export module DisasterForestFireFireDownloadUsingPost {
  export type Operation = paths['/disaster/forestFire/fireDownload']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾下载
 * @url /disaster/forestFire/fireDownload
 * @method post
 * @description 森林火灾下载
 */

export function disasterForestFireFireDownloadUsingPost(options:DisasterForestFireFireDownloadUsingPost.Options):Promise<DisasterForestFireFireDownloadUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/fireDownload',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾分页查询
 * @url /disaster/forestFire/fireListPage
 * @method post
 * @description 森林火灾分页查询
 */

export module DisasterForestFireFireListPageUsingPost {
  export type Operation = paths['/disaster/forestFire/fireListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾分页查询
 * @url /disaster/forestFire/fireListPage
 * @method post
 * @description 森林火灾分页查询
 */

export function disasterForestFireFireListPageUsingPost(options:DisasterForestFireFireListPageUsingPost.Options):Promise<DisasterForestFireFireListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/fireListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾点位列表查询
 * @url /disaster/forestFire/firePointsList
 * @method get
 * @description 森林火灾点位列表查询
 */

export module DisasterForestFireFirePointsListUsingGet {
  export type Operation = paths['/disaster/forestFire/firePointsList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 森林火灾点位列表查询
 * @url /disaster/forestFire/firePointsList
 * @method get
 * @description 森林火灾点位列表查询
 */

export function disasterForestFireFirePointsListUsingGet(options:DisasterForestFireFirePointsListUsingGet.Options):Promise<DisasterForestFireFirePointsListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/firePointsList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点日报下载
 * @url /disaster/forestFire/networkDayReportDownload
 * @method post
 * @description 
 */

export module DisasterForestFireNetworkDayReportDownloadUsingPost {
  export type Operation = paths['/disaster/forestFire/networkDayReportDownload']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点日报下载
 * @url /disaster/forestFire/networkDayReportDownload
 * @method post
 * @description 
 */

export function disasterForestFireNetworkDayReportDownloadUsingPost(options:DisasterForestFireNetworkDayReportDownloadUsingPost.Options):Promise<DisasterForestFireNetworkDayReportDownloadUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkDayReportDownload',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点明细查询
 * @url /disaster/forestFire/networkPointsById/{id}
 * @method get
 * @description 网络森林火点明细查询
 */

export module DisasterForestFireNetworkPointsByIdIdUsingGet {
  export type Operation = paths['/disaster/forestFire/networkPointsById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点明细查询
 * @url /disaster/forestFire/networkPointsById/{id}
 * @method get
 * @description 网络森林火点明细查询
 */

export function disasterForestFireNetworkPointsByIdIdUsingGet(options:DisasterForestFireNetworkPointsByIdIdUsingGet.Options):Promise<DisasterForestFireNetworkPointsByIdIdUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkPointsById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点日报统计:yyyy-MM-dd
 * @url /disaster/forestFire/networkPointsDayStatistic/{statisticDate}
 * @method get
 * @description 天眼森林火点日报统计
 */

export module DisasterForestFireNetworkPointsDayStatisticStatisticDateUsingGet {
  export type Operation = paths['/disaster/forestFire/networkPointsDayStatistic/{statisticDate}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点日报统计:yyyy-MM-dd
 * @url /disaster/forestFire/networkPointsDayStatistic/{statisticDate}
 * @method get
 * @description 天眼森林火点日报统计
 */

export function disasterForestFireNetworkPointsDayStatisticStatisticDateUsingGet(options:DisasterForestFireNetworkPointsDayStatisticStatisticDateUsingGet.Options):Promise<DisasterForestFireNetworkPointsDayStatisticStatisticDateUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkPointsDayStatistic/{statisticDate}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点维度统计:1-市县统计 2-火情强度统计 3-树种类型 4-植被类型
 * @url /disaster/forestFire/networkPointsFieldStatistic/{fieldKey}
 * @method get
 * @description 网络森林火点维度统计
 */

export module DisasterForestFireNetworkPointsFieldStatisticFieldKeyUsingGet {
  export type Operation = paths['/disaster/forestFire/networkPointsFieldStatistic/{fieldKey}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点维度统计:1-市县统计 2-火情强度统计 3-树种类型 4-植被类型
 * @url /disaster/forestFire/networkPointsFieldStatistic/{fieldKey}
 * @method get
 * @description 网络森林火点维度统计
 */

export function disasterForestFireNetworkPointsFieldStatisticFieldKeyUsingGet(options:DisasterForestFireNetworkPointsFieldStatisticFieldKeyUsingGet.Options):Promise<DisasterForestFireNetworkPointsFieldStatisticFieldKeyUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkPointsFieldStatistic/{fieldKey}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点点位列表查询
 * @url /disaster/forestFire/networkPointsList
 * @method get
 * @description 网络森林火点点位列表查询
 */

export module DisasterForestFireNetworkPointsListUsingGet {
  export type Operation = paths['/disaster/forestFire/networkPointsList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点点位列表查询
 * @url /disaster/forestFire/networkPointsList
 * @method get
 * @description 网络森林火点点位列表查询
 */

export function disasterForestFireNetworkPointsListUsingGet(options:DisasterForestFireNetworkPointsListUsingGet.Options):Promise<DisasterForestFireNetworkPointsListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkPointsList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点分页查询
 * @url /disaster/forestFire/networkPointsListPage
 * @method post
 * @description 网络森林火点分页查询
 */

export module DisasterForestFireNetworkPointsListPageUsingPost {
  export type Operation = paths['/disaster/forestFire/networkPointsListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点分页查询
 * @url /disaster/forestFire/networkPointsListPage
 * @method post
 * @description 网络森林火点分页查询
 */

export function disasterForestFireNetworkPointsListPageUsingPost(options:DisasterForestFireNetworkPointsListPageUsingPost.Options):Promise<DisasterForestFireNetworkPointsListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkPointsListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点简报数据查询
 * @url /disaster/forestFire/networkSimpleReportData
 * @method post
 * @description 网络森林火点简报数据查询
 */

export module DisasterForestFireNetworkSimpleReportDataUsingPost {
  export type Operation = paths['/disaster/forestFire/networkSimpleReportData']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点简报数据查询
 * @url /disaster/forestFire/networkSimpleReportData
 * @method post
 * @description 网络森林火点简报数据查询
 */

export function disasterForestFireNetworkSimpleReportDataUsingPost(options:DisasterForestFireNetworkSimpleReportDataUsingPost.Options):Promise<DisasterForestFireNetworkSimpleReportDataUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkSimpleReportData',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点简报下载
 * @url /disaster/forestFire/networkSimpleReportDownload
 * @method post
 * @description 
 */

export module DisasterForestFireNetworkSimpleReportDownloadUsingPost {
  export type Operation = paths['/disaster/forestFire/networkSimpleReportDownload']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 网络森林火点简报下载
 * @url /disaster/forestFire/networkSimpleReportDownload
 * @method post
 * @description 
 */

export function disasterForestFireNetworkSimpleReportDownloadUsingPost(options:DisasterForestFireNetworkSimpleReportDownloadUsingPost.Options):Promise<DisasterForestFireNetworkSimpleReportDownloadUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/networkSimpleReportDownload',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点日报统计:yyyy-MM-dd
 * @url /disaster/forestFire/tyPointsDayStatistic/{statisticDate}
 * @method get
 * @description 天眼森林火点日报统计
 */

export module DisasterForestFireTyPointsDayStatisticStatisticDateUsingGet {
  export type Operation = paths['/disaster/forestFire/tyPointsDayStatistic/{statisticDate}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点日报统计:yyyy-MM-dd
 * @url /disaster/forestFire/tyPointsDayStatistic/{statisticDate}
 * @method get
 * @description 天眼森林火点日报统计
 */

export function disasterForestFireTyPointsDayStatisticStatisticDateUsingGet(options:DisasterForestFireTyPointsDayStatisticStatisticDateUsingGet.Options):Promise<DisasterForestFireTyPointsDayStatisticStatisticDateUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/tyPointsDayStatistic/{statisticDate}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点维度统计:1-市县统计
 * @url /disaster/forestFire/tyPointsFieldStatistic/{fieldKey}
 * @method get
 * @description 天眼森林火点维度统计
 */

export module DisasterForestFireTyPointsFieldStatisticFieldKeyUsingGet {
  export type Operation = paths['/disaster/forestFire/tyPointsFieldStatistic/{fieldKey}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点维度统计:1-市县统计
 * @url /disaster/forestFire/tyPointsFieldStatistic/{fieldKey}
 * @method get
 * @description 天眼森林火点维度统计
 */

export function disasterForestFireTyPointsFieldStatisticFieldKeyUsingGet(options:DisasterForestFireTyPointsFieldStatisticFieldKeyUsingGet.Options):Promise<DisasterForestFireTyPointsFieldStatisticFieldKeyUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/tyPointsFieldStatistic/{fieldKey}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点列表查询
 * @url /disaster/forestFire/tyPointsList
 * @method post
 * @description 天眼森林火点列表查询
 */

export module DisasterForestFireTyPointsListUsingPost {
  export type Operation = paths['/disaster/forestFire/tyPointsList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点列表查询
 * @url /disaster/forestFire/tyPointsList
 * @method post
 * @description 天眼森林火点列表查询
 */

export function disasterForestFireTyPointsListUsingPost(options:DisasterForestFireTyPointsListUsingPost.Options):Promise<DisasterForestFireTyPointsListUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/tyPointsList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点分页查询
 * @url /disaster/forestFire/tyPointsListPage
 * @method post
 * @description 天眼森林火点分页查询
 */

export module DisasterForestFireTyPointsListPageUsingPost {
  export type Operation = paths['/disaster/forestFire/tyPointsListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-森林火情专题
 * @summary 天眼森林火点分页查询
 * @url /disaster/forestFire/tyPointsListPage
 * @method post
 * @description 天眼森林火点分页查询
 */

export function disasterForestFireTyPointsListPageUsingPost(options:DisasterForestFireTyPointsListPageUsingPost.Options):Promise<DisasterForestFireTyPointsListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/forestFire/tyPointsListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 直升机列表
 * @url /disaster/forestFireProtection/helicopterList
 * @method get
 * @description 直升机列表
 */

export module DisasterForestFireProtectionHelicopterListUsingGet {
  export type Operation = paths['/disaster/forestFireProtection/helicopterList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 直升机列表
 * @url /disaster/forestFireProtection/helicopterList
 * @method get
 * @description 直升机列表
 */

export function disasterForestFireProtectionHelicopterListUsingGet(options:DisasterForestFireProtectionHelicopterListUsingGet.Options):Promise<DisasterForestFireProtectionHelicopterListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFireProtection/helicopterList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 直升机场周边水库列表
 * @url /disaster/forestFireProtection/helicopterReservoirList/{heliportId}
 * @method get
 * @description 直升机场周边水库列表
 */

export module DisasterForestFireProtectionHelicopterReservoirListHeliportIdUsingGet {
  export type Operation = paths['/disaster/forestFireProtection/helicopterReservoirList/{heliportId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 直升机场周边水库列表
 * @url /disaster/forestFireProtection/helicopterReservoirList/{heliportId}
 * @method get
 * @description 直升机场周边水库列表
 */

export function disasterForestFireProtectionHelicopterReservoirListHeliportIdUsingGet(options:DisasterForestFireProtectionHelicopterReservoirListHeliportIdUsingGet.Options):Promise<DisasterForestFireProtectionHelicopterReservoirListHeliportIdUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFireProtection/helicopterReservoirList/{heliportId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 直升机场列表
 * @url /disaster/forestFireProtection/heliportList
 * @method get
 * @description 直升机场列表
 */

export module DisasterForestFireProtectionHeliportListUsingGet {
  export type Operation = paths['/disaster/forestFireProtection/heliportList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 直升机场列表
 * @url /disaster/forestFireProtection/heliportList
 * @method get
 * @description 直升机场列表
 */

export function disasterForestFireProtectionHeliportListUsingGet(options:DisasterForestFireProtectionHeliportListUsingGet.Options):Promise<DisasterForestFireProtectionHeliportListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFireProtection/heliportList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 水库列表
 * @url /disaster/forestFireProtection/reservoirList
 * @method get
 * @description 水库列表
 */

export module DisasterForestFireProtectionReservoirListUsingGet {
  export type Operation = paths['/disaster/forestFireProtection/reservoirList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-森林消防接口
 * @summary 水库列表
 * @url /disaster/forestFireProtection/reservoirList
 * @method get
 * @description 水库列表
 */

export function disasterForestFireProtectionReservoirListUsingGet(options:DisasterForestFireProtectionReservoirListUsingGet.Options):Promise<DisasterForestFireProtectionReservoirListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/forestFireProtection/reservoirList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-普查专题
 * @summary 市县地图信息
 * @url /disaster/generalSurvey/cityMapInfo
 * @method post
 * @description 市县地图信息
 */

export module DisasterGeneralSurveyCityMapInfoUsingPost {
  export type Operation = paths['/disaster/generalSurvey/cityMapInfo']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 自然灾害-普查专题
 * @summary 市县地图信息
 * @url /disaster/generalSurvey/cityMapInfo
 * @method post
 * @description 市县地图信息
 */

export function disasterGeneralSurveyCityMapInfoUsingPost(options:DisasterGeneralSurveyCityMapInfoUsingPost.Options):Promise<DisasterGeneralSurveyCityMapInfoUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/generalSurvey/cityMapInfo',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-普查专题
 * @summary 海浪频率统计列表
 * @url /disaster/generalSurvey/waveFrequencyList
 * @method get
 * @description 市县地图信息
 */

export module DisasterGeneralSurveyWaveFrequencyListUsingGet {
  export type Operation = paths['/disaster/generalSurvey/waveFrequencyList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 自然灾害-普查专题
 * @summary 海浪频率统计列表
 * @url /disaster/generalSurvey/waveFrequencyList
 * @method get
 * @description 市县地图信息
 */

export function disasterGeneralSurveyWaveFrequencyListUsingGet(options:DisasterGeneralSurveyWaveFrequencyListUsingGet.Options):Promise<DisasterGeneralSurveyWaveFrequencyListUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/generalSurvey/waveFrequencyList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 美国气象网-查询Grib2数据：1001-风场
 * @url /disaster/prediction/grib2Data
 * @method post
 * @description 美国气象网-查询Grib2数据
 */

export module DisasterPredictionGrib2DataUsingPost {
  export type Operation = paths['/disaster/prediction/grib2Data']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 美国气象网-查询Grib2数据：1001-风场
 * @url /disaster/prediction/grib2Data
 * @method post
 * @description 美国气象网-查询Grib2数据
 */

export function disasterPredictionGrib2DataUsingPost(options:DisasterPredictionGrib2DataUsingPost.Options):Promise<DisasterPredictionGrib2DataUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/grib2Data',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 美国气象网-查询Grib2数据时间列表：1001-风场
 * @url /disaster/prediction/grib2DataTimeList/{code}
 * @method get
 * @description 美国气象网-查询Grib2数据
 */

export module DisasterPredictionGrib2DataTimeListCodeUsingGet {
  export type Operation = paths['/disaster/prediction/grib2DataTimeList/{code}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 美国气象网-查询Grib2数据时间列表：1001-风场
 * @url /disaster/prediction/grib2DataTimeList/{code}
 * @method get
 * @description 美国气象网-查询Grib2数据
 */

export function disasterPredictionGrib2DataTimeListCodeUsingGet(options:DisasterPredictionGrib2DataTimeListCodeUsingGet.Options):Promise<DisasterPredictionGrib2DataTimeListCodeUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/prediction/grib2DataTimeList/{code}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 云图-雷达分页查询
 * @url /disaster/prediction/radarListPage
 * @method post
 * @description 雷达分页查询
 */

export module DisasterPredictionRadarListPageUsingPost {
  export type Operation = paths['/disaster/prediction/radarListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 云图-雷达分页查询
 * @url /disaster/prediction/radarListPage
 * @method post
 * @description 雷达分页查询
 */

export function disasterPredictionRadarListPageUsingPost(options:DisasterPredictionRadarListPageUsingPost.Options):Promise<DisasterPredictionRadarListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/radarListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-降水市县统计
 * @url /disaster/prediction/rainfallCityStatistic
 * @method post
 * @description 三防信息网-降水市县统计
 */

export module DisasterPredictionRainfallCityStatisticUsingPost {
  export type Operation = paths['/disaster/prediction/rainfallCityStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-降水市县统计
 * @url /disaster/prediction/rainfallCityStatistic
 * @method post
 * @description 三防信息网-降水市县统计
 */

export function disasterPredictionRainfallCityStatisticUsingPost(options:DisasterPredictionRainfallCityStatisticUsingPost.Options):Promise<DisasterPredictionRainfallCityStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/rainfallCityStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-降水明细
 * @url /disaster/prediction/rainfallDetail
 * @method post
 * @description 三防信息网-降水明细
 */

export module DisasterPredictionRainfallDetailUsingPost {
  export type Operation = paths['/disaster/prediction/rainfallDetail']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-降水明细
 * @url /disaster/prediction/rainfallDetail
 * @method post
 * @description 三防信息网-降水明细
 */

export function disasterPredictionRainfallDetailUsingPost(options:DisasterPredictionRainfallDetailUsingPost.Options):Promise<DisasterPredictionRainfallDetailUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/rainfallDetail',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-降水列表查询
 * @url /disaster/prediction/rainfallList
 * @method post
 * @description 三防信息网-降水列表查询
 */

export module DisasterPredictionRainfallListUsingPost {
  export type Operation = paths['/disaster/prediction/rainfallList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-降水列表查询
 * @url /disaster/prediction/rainfallList
 * @method post
 * @description 三防信息网-降水列表查询
 */

export function disasterPredictionRainfallListUsingPost(options:DisasterPredictionRainfallListUsingPost.Options):Promise<DisasterPredictionRainfallListUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/rainfallList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 云图-卫星云图分页查询
 * @url /disaster/prediction/satelliteListPage
 * @method post
 * @description 卫星云图分页查询
 */

export module DisasterPredictionSatelliteListPageUsingPost {
  export type Operation = paths['/disaster/prediction/satelliteListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 云图-卫星云图分页查询
 * @url /disaster/prediction/satelliteListPage
 * @method post
 * @description 卫星云图分页查询
 */

export function disasterPredictionSatelliteListPageUsingPost(options:DisasterPredictionSatelliteListPageUsingPost.Options):Promise<DisasterPredictionSatelliteListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/satelliteListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 云图-降雨量分页查询
 * @url /disaster/prediction/spccListPage
 * @method post
 * @description 降雨量分页查询
 */

export module DisasterPredictionSpccListPageUsingPost {
  export type Operation = paths['/disaster/prediction/spccListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 云图-降雨量分页查询
 * @url /disaster/prediction/spccListPage
 * @method post
 * @description 降雨量分页查询
 */

export function disasterPredictionSpccListPageUsingPost(options:DisasterPredictionSpccListPageUsingPost.Options):Promise<DisasterPredictionSpccListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/spccListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 中国海洋预报-天文潮未来30天预报
 * @url /disaster/prediction/tideForecast30Day/{tideId}
 * @method post
 * @description 天文潮未来30天预报
 */

export module DisasterPredictionTideForecast30DayTideIdUsingPost {
  export type Operation = paths['/disaster/prediction/tideForecast30Day/{tideId}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 中国海洋预报-天文潮未来30天预报
 * @url /disaster/prediction/tideForecast30Day/{tideId}
 * @method post
 * @description 天文潮未来30天预报
 */

export function disasterPredictionTideForecast30DayTideIdUsingPost(options:DisasterPredictionTideForecast30DayTideIdUsingPost.Options):Promise<DisasterPredictionTideForecast30DayTideIdUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/tideForecast30Day/{tideId}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 中国海洋预报-天文潮分页查询
 * @url /disaster/prediction/tideListPage
 * @method post
 * @description 天文潮分页查询
 */

export module DisasterPredictionTideListPageUsingPost {
  export type Operation = paths['/disaster/prediction/tideListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 中国海洋预报-天文潮分页查询
 * @url /disaster/prediction/tideListPage
 * @method post
 * @description 天文潮分页查询
 */

export function disasterPredictionTideListPageUsingPost(options:DisasterPredictionTideListPageUsingPost.Options):Promise<DisasterPredictionTideListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/tideListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 中国海洋预报-天文潮站点上球
 * @url /disaster/prediction/tidePointsList
 * @method post
 * @description 天文潮站点上球
 */

export module DisasterPredictionTidePointsListUsingPost {
  export type Operation = paths['/disaster/prediction/tidePointsList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 中国海洋预报-天文潮站点上球
 * @url /disaster/prediction/tidePointsList
 * @method post
 * @description 天文潮站点上球
 */

export function disasterPredictionTidePointsListUsingPost(options:DisasterPredictionTidePointsListUsingPost.Options):Promise<DisasterPredictionTidePointsListUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/tidePointsList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-台风轨迹列表
 * @url /disaster/prediction/typhoonDetailList/{typhoonNo}
 * @method post
 * @description 三防信息网-台风轨迹列表
 */

export module DisasterPredictionTyphoonDetailListTyphoonNoUsingPost {
  export type Operation = paths['/disaster/prediction/typhoonDetailList/{typhoonNo}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-台风轨迹列表
 * @url /disaster/prediction/typhoonDetailList/{typhoonNo}
 * @method post
 * @description 三防信息网-台风轨迹列表
 */

export function disasterPredictionTyphoonDetailListTyphoonNoUsingPost(options:DisasterPredictionTyphoonDetailListTyphoonNoUsingPost.Options):Promise<DisasterPredictionTyphoonDetailListTyphoonNoUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/typhoonDetailList/{typhoonNo}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-台风列表
 * @url /disaster/prediction/typhoonList
 * @method post
 * @description 三防信息网-台风列表
 */

export module DisasterPredictionTyphoonListUsingPost {
  export type Operation = paths['/disaster/prediction/typhoonList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-台风列表
 * @url /disaster/prediction/typhoonList
 * @method post
 * @description 三防信息网-台风列表
 */

export function disasterPredictionTyphoonListUsingPost(options:DisasterPredictionTyphoonListUsingPost.Options):Promise<DisasterPredictionTyphoonListUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/typhoonList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风市县统计
 * @url /disaster/prediction/windCityStatistic
 * @method post
 * @description 三防信息网-大风市县统计
 */

export module DisasterPredictionWindCityStatisticUsingPost {
  export type Operation = paths['/disaster/prediction/windCityStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风市县统计
 * @url /disaster/prediction/windCityStatistic
 * @method post
 * @description 三防信息网-大风市县统计
 */

export function disasterPredictionWindCityStatisticUsingPost(options:DisasterPredictionWindCityStatisticUsingPost.Options):Promise<DisasterPredictionWindCityStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/windCityStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风明细
 * @url /disaster/prediction/windDetail
 * @method post
 * @description 三防信息网-大风明细
 */

export module DisasterPredictionWindDetailUsingPost {
  export type Operation = paths['/disaster/prediction/windDetail']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风明细
 * @url /disaster/prediction/windDetail
 * @method post
 * @description 三防信息网-大风明细
 */

export function disasterPredictionWindDetailUsingPost(options:DisasterPredictionWindDetailUsingPost.Options):Promise<DisasterPredictionWindDetailUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/windDetail',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风列表查询
 * @url /disaster/prediction/windList
 * @method post
 * @description 三防信息网-大风列表查询
 */

export module DisasterPredictionWindListUsingPost {
  export type Operation = paths['/disaster/prediction/windList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风列表查询
 * @url /disaster/prediction/windList
 * @method post
 * @description 三防信息网-大风列表查询
 */

export function disasterPredictionWindListUsingPost(options:DisasterPredictionWindListUsingPost.Options):Promise<DisasterPredictionWindListUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/windList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风分页查询
 * @url /disaster/prediction/windPageList
 * @method post
 * @description 三防信息网-大风分页查询
 */

export module DisasterPredictionWindPageListUsingPost {
  export type Operation = paths['/disaster/prediction/windPageList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预报专题
 * @summary 三防信息网-大风分页查询
 * @url /disaster/prediction/windPageList
 * @method post
 * @description 三防信息网-大风分页查询
 */

export function disasterPredictionWindPageListUsingPost(options:DisasterPredictionWindPageListUsingPost.Options):Promise<DisasterPredictionWindPageListUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/prediction/windPageList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 自然灾害预警分页查询
 * @url /disaster/waring/disasterTypeListPage
 * @method post
 * @description 自然灾害预警分页查询
 */

export module DisasterWaringDisasterTypeListPageUsingPost {
  export type Operation = paths['/disaster/waring/disasterTypeListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 自然灾害预警分页查询
 * @url /disaster/waring/disasterTypeListPage
 * @method post
 * @description 自然灾害预警分页查询
 */

export function disasterWaringDisasterTypeListPageUsingPost(options:DisasterWaringDisasterTypeListPageUsingPost.Options):Promise<DisasterWaringDisasterTypeListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/disasterTypeListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 自然灾害预警类型统计
 * @url /disaster/waring/disasterTypeStatistic
 * @method post
 * @description 自然灾害预警类型统计
 */

export module DisasterWaringDisasterTypeStatisticUsingPost {
  export type Operation = paths['/disaster/waring/disasterTypeStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 自然灾害预警类型统计
 * @url /disaster/waring/disasterTypeStatistic
 * @method post
 * @description 自然灾害预警类型统计
 */

export function disasterWaringDisasterTypeStatisticUsingPost(options:DisasterWaringDisasterTypeStatisticUsingPost.Options):Promise<DisasterWaringDisasterTypeStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/disasterTypeStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 防火责任人统计
 * @url /disaster/waring/firePreventionStatistic
 * @method get
 * @description 防火责任人统计
 */

export module DisasterWaringFirePreventionStatisticUsingGet {
  export type Operation = paths['/disaster/waring/firePreventionStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 防火责任人统计
 * @url /disaster/waring/firePreventionStatistic
 * @method get
 * @description 防火责任人统计
 */

export function disasterWaringFirePreventionStatisticUsingGet(options:DisasterWaringFirePreventionStatisticUsingGet.Options):Promise<DisasterWaringFirePreventionStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/waring/firePreventionStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 深林火灾趋势统计
 * @url /disaster/waring/forestFireStatistic
 * @method get
 * @description 深林火灾趋势统计
 */

export module DisasterWaringForestFireStatisticUsingGet {
  export type Operation = paths['/disaster/waring/forestFireStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 深林火灾趋势统计
 * @url /disaster/waring/forestFireStatistic
 * @method get
 * @description 深林火灾趋势统计
 */

export function disasterWaringForestFireStatisticUsingGet(options:DisasterWaringForestFireStatisticUsingGet.Options):Promise<DisasterWaringForestFireStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/disaster/waring/forestFireStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-根据ID查询矢量数据
 * @url /disaster/waring/oceanGeometry/{id}
 * @method post
 * @description 海洋预警-根据ID查询矢量数据
 */

export module DisasterWaringOceanGeometryIdUsingPost {
  export type Operation = paths['/disaster/waring/oceanGeometry/{id}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-根据ID查询矢量数据
 * @url /disaster/waring/oceanGeometry/{id}
 * @method post
 * @description 海洋预警-根据ID查询矢量数据
 */

export function disasterWaringOceanGeometryIdUsingPost(options:DisasterWaringOceanGeometryIdUsingPost.Options):Promise<DisasterWaringOceanGeometryIdUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/oceanGeometry/{id}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-数据列表查询
 * @url /disaster/waring/oceanList
 * @method post
 * @description 海洋预警-数据列表查询
 */

export module DisasterWaringOceanListUsingPost {
  export type Operation = paths['/disaster/waring/oceanList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-数据列表查询
 * @url /disaster/waring/oceanList
 * @method post
 * @description 海洋预警-数据列表查询
 */

export function disasterWaringOceanListUsingPost(options:DisasterWaringOceanListUsingPost.Options):Promise<DisasterWaringOceanListUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/oceanList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-分页查询
 * @url /disaster/waring/oceanListPage
 * @method post
 * @description 海洋预警-分页查询
 */

export module DisasterWaringOceanListPageUsingPost {
  export type Operation = paths['/disaster/waring/oceanListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-分页查询
 * @url /disaster/waring/oceanListPage
 * @method post
 * @description 海洋预警-分页查询
 */

export function disasterWaringOceanListPageUsingPost(options:DisasterWaringOceanListPageUsingPost.Options):Promise<DisasterWaringOceanListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/oceanListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-类型统计
 * @url /disaster/waring/oceanTypeStatistic
 * @method post
 * @description 海洋预警-类型统计
 */

export module DisasterWaringOceanTypeStatisticUsingPost {
  export type Operation = paths['/disaster/waring/oceanTypeStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 海洋预警-类型统计
 * @url /disaster/waring/oceanTypeStatistic
 * @method post
 * @description 海洋预警-类型统计
 */

export function disasterWaringOceanTypeStatisticUsingPost(options:DisasterWaringOceanTypeStatisticUsingPost.Options):Promise<DisasterWaringOceanTypeStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/oceanTypeStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象事件市县统计
 * @url /disaster/waring/weatherCityStatistic
 * @method post
 * @description 气象事件市县统计
 */

export module DisasterWaringWeatherCityStatisticUsingPost {
  export type Operation = paths['/disaster/waring/weatherCityStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象事件市县统计
 * @url /disaster/waring/weatherCityStatistic
 * @method post
 * @description 气象事件市县统计
 */

export function disasterWaringWeatherCityStatisticUsingPost(options:DisasterWaringWeatherCityStatisticUsingPost.Options):Promise<DisasterWaringWeatherCityStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/weatherCityStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象事件类型统计
 * @url /disaster/waring/weatherEventTypeStatistic
 * @method post
 * @description 气象事件类型统计
 */

export module DisasterWaringWeatherEventTypeStatisticUsingPost {
  export type Operation = paths['/disaster/waring/weatherEventTypeStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象事件类型统计
 * @url /disaster/waring/weatherEventTypeStatistic
 * @method post
 * @description 气象事件类型统计
 */

export function disasterWaringWeatherEventTypeStatisticUsingPost(options:DisasterWaringWeatherEventTypeStatisticUsingPost.Options):Promise<DisasterWaringWeatherEventTypeStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/weatherEventTypeStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象分页查询
 * @url /disaster/waring/weatherListPage
 * @method post
 * @description 气象分页查询
 */

export module DisasterWaringWeatherListPageUsingPost {
  export type Operation = paths['/disaster/waring/weatherListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象分页查询
 * @url /disaster/waring/weatherListPage
 * @method post
 * @description 气象分页查询
 */

export function disasterWaringWeatherListPageUsingPost(options:DisasterWaringWeatherListPageUsingPost.Options):Promise<DisasterWaringWeatherListPageUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/weatherListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象严重程度统计
 * @url /disaster/waring/weatherSeriousStatistic
 * @method post
 * @description 气象严重程度统计
 */

export module DisasterWaringWeatherSeriousStatisticUsingPost {
  export type Operation = paths['/disaster/waring/weatherSeriousStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 自然灾害-预警专题
 * @summary 气象严重程度统计
 * @url /disaster/waring/weatherSeriousStatistic
 * @method post
 * @description 气象严重程度统计
 */

export function disasterWaringWeatherSeriousStatisticUsingPost(options:DisasterWaringWeatherSeriousStatisticUsingPost.Options):Promise<DisasterWaringWeatherSeriousStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/disaster/waring/weatherSeriousStatistic',
    method:'post',
    ...options,
  });
}
export interface paths {
    "/disaster/floodWindPrevention/administrativeBaseById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 政务基底查询
         * @description 政务基底查询
         */
        post: operations["administrativeBaseByIdUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/dutyPersonById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三方责任人明细查询
         * @description 三方责任人明细查询
         */
        get: operations["dutyPersonByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/dutyPersonCount/{year}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三方责任人数量总数
         * @description 三方责任人数量总数
         */
        get: operations["dutyPersonCountUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/dutyPersonListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三方责任人分页查询
         * @description 三方责任人分页查询
         */
        post: operations["dutyPersonListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/dutyPersonPointsList/{year}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三方责任人点位列表查询
         * @description 三方责任人点位列表查询
         */
        get: operations["dutyPersonPointsListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/dutyPersonReport": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防责任人下载
         * @description 三方责任人数量总数
         */
        post: operations["dutyPersonReportUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/dutyPersonStatistic/{year}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防责任人市县数量统计
         * @description 三防责任人市县数量统计
         */
        get: operations["dutyPersonStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/emergencySuppliesList/{tableRowId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 仓库应急物资详情
         * @description 仓库应急物资详情
         */
        post: operations["emergencySuppliesListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/exportListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 应急专家分页查询
         * @description 应急专家分页查询
         */
        post: operations["exportListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/hazTypeStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 风险隐患类型统计
         * @description 风险隐患类型统计
         */
        get: operations["hazTypeStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/hazYearStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 风险隐患年度统计
         * @description 风险隐患年度统计
         */
        get: operations["hazYearStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 防汛防风分页查询
         * @description 防汛防风分页查询
         */
        post: operations["listPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/numberStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 模块维度数量统计
         * @description 模块维度数量统计
         */
        post: operations["numberStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/report": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 报表下载
         * @description 报表下载
         */
        post: operations["reportUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirCityStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-水库市县统计
         * @description 三防信息网-水库市县统计
         */
        get: operations["sfReservoirCityStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirCount": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-水库数量查询
         * @description 三防信息网-水库数量查询
         */
        get: operations["sfReservoirCountUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirDetail/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-水库详情
         * @description 三防信息网-水库详情
         */
        get: operations["sfReservoirDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirGeometry/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-水库矢量数据查询
         * @description 三防信息网-水库矢量数据查询
         */
        get: operations["sfReservoirGeometryUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirHourStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-超汛线24小时趋势统计：1-水库 2-河流
         * @description 三防信息网-超汛线24小时趋势统计：1-水库 2-河流
         */
        get: operations["sfOverReHourStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-水库分页查询
         * @description 三防信息网-水库分页查询
         */
        post: operations["sfReservoirListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirPlanByCode/{reservoirCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-水库预案详情
         * @description 三防信息网-水库预案详情
         */
        get: operations["sfReservoirPlanByCodeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirPointsList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-水库上球列表
         * @description 三防信息网-水库上球列表
         */
        get: operations["sfReservoirPointsListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/floodWindPrevention/sfReservoirReservePlanDownload/{reservoirCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 三防信息网-水库预案下载
         * @description 三防信息网-水库预案下载
         */
        get: operations["sfReservoirReservePlanDownloadUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/dutyPersonById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 森林防火通讯录明细查询
         * @description 森林防火通讯录明细查询
         */
        get: operations["dutyPersonByIdUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/dutyPersonDownload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 森林防火通讯录下载
         * @description 森林防火通讯录下载
         */
        post: operations["dutyPersonDownloadUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/dutyPersonListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 森林防火通讯录分页查询
         * @description 森林防火通讯录分页查询
         */
        post: operations["dutyPersonListPageUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/dutyPersonPointsList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 森林防火通讯录点位列表查询
         * @description 森林防火通讯录点位列表查询
         */
        get: operations["dutyPersonPointsListUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/fireById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 森林火灾明细查询
         * @description 森林火灾明细查询
         */
        get: operations["fireByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/fireCityStatistic/{year}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 森林危险信息(市县统计)
         * @description 森林危险信息(市县统计)
         */
        get: operations["fireCityStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/fireDownload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 森林火灾下载
         * @description 森林火灾下载
         */
        post: operations["fireDownloadUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/fireListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 森林火灾分页查询
         * @description 森林火灾分页查询
         */
        post: operations["fireListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/firePointsList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 森林火灾点位列表查询
         * @description 森林火灾点位列表查询
         */
        get: operations["firePointsListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkDayReportDownload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 网络森林火点日报下载 */
        post: operations["networkDayReportUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkPointsById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 网络森林火点明细查询
         * @description 网络森林火点明细查询
         */
        get: operations["networkPointsByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkPointsDayStatistic/{statisticDate}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 网络森林火点日报统计:yyyy-MM-dd
         * @description 天眼森林火点日报统计
         */
        get: operations["networkPointsDayStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkPointsFieldStatistic/{fieldKey}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 网络森林火点维度统计:1-市县统计 2-火情强度统计 3-树种类型 4-植被类型
         * @description 网络森林火点维度统计
         */
        get: operations["networkPointsFieldStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkPointsList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 网络森林火点点位列表查询
         * @description 网络森林火点点位列表查询
         */
        get: operations["networkPointsListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkPointsListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 网络森林火点分页查询
         * @description 网络森林火点分页查询
         */
        post: operations["networkPointsListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkSimpleReportData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 网络森林火点简报数据查询
         * @description 网络森林火点简报数据查询
         */
        post: operations["networkSimpleReportDataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/networkSimpleReportDownload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 网络森林火点简报下载 */
        post: operations["networkSimpleReportUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/tyPointsDayStatistic/{statisticDate}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 天眼森林火点日报统计:yyyy-MM-dd
         * @description 天眼森林火点日报统计
         */
        get: operations["tyPointsDayStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/tyPointsFieldStatistic/{fieldKey}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 天眼森林火点维度统计:1-市县统计
         * @description 天眼森林火点维度统计
         */
        get: operations["tyPointsFieldStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/tyPointsList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 天眼森林火点列表查询
         * @description 天眼森林火点列表查询
         */
        post: operations["tyPointsListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFire/tyPointsListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 天眼森林火点分页查询
         * @description 天眼森林火点分页查询
         */
        post: operations["tyPointsListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFireProtection/helicopterList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 直升机列表
         * @description 直升机列表
         */
        get: operations["helicopterListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFireProtection/helicopterReservoirList/{heliportId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 直升机场周边水库列表
         * @description 直升机场周边水库列表
         */
        get: operations["helicopterReservoirListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFireProtection/heliportList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 直升机场列表
         * @description 直升机场列表
         */
        get: operations["heliportListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/forestFireProtection/reservoirList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 水库列表
         * @description 水库列表
         */
        get: operations["reservoirListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/generalSurvey/cityMapInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 市县地图信息
         * @description 市县地图信息
         */
        post: operations["cityMapInfoListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/generalSurvey/waveFrequencyList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 海浪频率统计列表
         * @description 市县地图信息
         */
        get: operations["waveFrequencyListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/grib2Data": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 美国气象网-查询Grib2数据：1001-风场
         * @description 美国气象网-查询Grib2数据
         */
        post: operations["grib2DataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/grib2DataTimeList/{code}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 美国气象网-查询Grib2数据时间列表：1001-风场
         * @description 美国气象网-查询Grib2数据
         */
        get: operations["grib2DataTimeListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/radarListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 云图-雷达分页查询
         * @description 雷达分页查询
         */
        post: operations["radarListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/rainfallCityStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-降水市县统计
         * @description 三防信息网-降水市县统计
         */
        post: operations["rainfallCityStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/rainfallDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-降水明细
         * @description 三防信息网-降水明细
         */
        post: operations["rainfallDetailUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/rainfallList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-降水列表查询
         * @description 三防信息网-降水列表查询
         */
        post: operations["rainfallListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/satelliteListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 云图-卫星云图分页查询
         * @description 卫星云图分页查询
         */
        post: operations["satelliteListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/spccListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 云图-降雨量分页查询
         * @description 降雨量分页查询
         */
        post: operations["spccListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/tideForecast30Day/{tideId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 中国海洋预报-天文潮未来30天预报
         * @description 天文潮未来30天预报
         */
        post: operations["tideForecast30DayUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/tideListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 中国海洋预报-天文潮分页查询
         * @description 天文潮分页查询
         */
        post: operations["tideListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/tidePointsList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 中国海洋预报-天文潮站点上球
         * @description 天文潮站点上球
         */
        post: operations["tidePointsListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/typhoonDetailList/{typhoonNo}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-台风轨迹列表
         * @description 三防信息网-台风轨迹列表
         */
        post: operations["typhoonDetailListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/typhoonList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-台风列表
         * @description 三防信息网-台风列表
         */
        post: operations["typhoonListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/windCityStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-大风市县统计
         * @description 三防信息网-大风市县统计
         */
        post: operations["windCityStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/windDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-大风明细
         * @description 三防信息网-大风明细
         */
        post: operations["windDetailUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/windList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-大风列表查询
         * @description 三防信息网-大风列表查询
         */
        post: operations["windListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/prediction/windPageList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 三防信息网-大风分页查询
         * @description 三防信息网-大风分页查询
         */
        post: operations["windPageListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/disasterTypeListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 自然灾害预警分页查询
         * @description 自然灾害预警分页查询
         */
        post: operations["disasterTypeListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/disasterTypeStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 自然灾害预警类型统计
         * @description 自然灾害预警类型统计
         */
        post: operations["disasterTypeStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/firePreventionStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 防火责任人统计
         * @description 防火责任人统计
         */
        get: operations["firePreventionStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/forestFireStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 深林火灾趋势统计
         * @description 深林火灾趋势统计
         */
        get: operations["forestFireStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/oceanGeometry/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 海洋预警-根据ID查询矢量数据
         * @description 海洋预警-根据ID查询矢量数据
         */
        post: operations["oceanGeometryUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/oceanList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 海洋预警-数据列表查询
         * @description 海洋预警-数据列表查询
         */
        post: operations["oceanListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/oceanListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 海洋预警-分页查询
         * @description 海洋预警-分页查询
         */
        post: operations["oceanListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/oceanTypeStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 海洋预警-类型统计
         * @description 海洋预警-类型统计
         */
        post: operations["oceanTypeStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/weatherCityStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 气象事件市县统计
         * @description 气象事件市县统计
         */
        post: operations["weatherCityStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/weatherEventTypeStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 气象事件类型统计
         * @description 气象事件类型统计
         */
        post: operations["weatherEventTypeStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/weatherListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 气象分页查询
         * @description 气象分页查询
         */
        post: operations["weatherListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/disaster/waring/weatherSeriousStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 气象严重程度统计
         * @description 气象严重程度统计
         */
        post: operations["weatherSeriousStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /**
         * AdministrativeBaseDTO
         * @description 政务基底
         */
        AdministrativeBaseDTO: {
            chClassification?: string;
            /** Format: int32 */
            chId?: number;
            children?: components["schemas"]["AdministrativeBaseDTO"][];
            /** Format: int32 */
            chParentId?: number;
            chRemark?: string;
            chSearch?: string;
            chShp?: string;
            chWmts?: string;
            label?: string;
            layer?: string;
            type?: string;
        };
        /**
         * 查询排序配置项
         * @description 查询排序配置项
         */
        ChaXunPaiXuPeiZhiXiang: {
            /**
             * @description 排序方式
             * @example ASC/asc;DESC/desc
             */
            orderType?: string;
            /** @description 属性名称 */
            propertyName?: string;
        };
        /**
         * CityMapInfoDTO
         * @description 市县地图对象
         */
        CityMapInfoDTO: {
            /** @description 市县名称 */
            cityName?: string;
            /** @description 主键ID */
            id?: string;
            /** @description Layer地址 */
            layerUrl?: string;
            /** @description 地图名称 */
            name?: string;
            /**
             * Format: int32
             * @description 序号
             */
            sort?: number;
            /** @description 类型 */
            type?: string;
            /** @description 地址 */
            url?: string;
        };
        /**
         * CityStatisticQueryDTO
         * @description 按市县统计入参
         */
        CityStatisticQueryDTO: {
            /** @description 统计维度：0：市县统计 1：水库-工程规模统计 2：地质灾害隐患点-灾害类型统计 3：旅游景点景区-日最大承载游客统计 4：易淹易涝点-影响人数统计 5-渔船-渔船抗风等级统计 6：渔港-避风船只容量统计 */
            dimension: string;
            /** @description 类型编码（哪一个表） */
            typeCode: string;
            /** @description 年度 */
            year: string;
        };
        /**
         * CloudChartPageQueryDTO
         * @description 云图分页查询对象
         */
        CloudChartPageQueryDTO: {
            /** @description 结束时间，雷达和卫星时间格式yyyyMMddHHmmss，降水量时间格式yyyyMMddHH */
            endTime?: string;
            /** @description 开始时间，雷达和卫星时间格式yyyyMMddHHmmss，降水量时间格式yyyyMMddHH */
            startTime?: string;
        };
        /**
         * CommonResourceDTO
         * @description 公共资源对象
         */
        CommonResourceDTO: {
            /** @description 位置 */
            address?: string;
            /**
             * Format: int32
             * @description 序号
             */
            index?: number;
            /** @description 名称 */
            name?: string;
            /** @description 电话 */
            phoneNo?: string;
            /** @description 类别 */
            type?: string;
        };
        /**
         * DisasterWarnDTO
         * @description 自然灾害预警对象
         */
        DisasterWarnDTO: {
            /** @description 预警编码 */
            code?: string;
            /** @description 预警内容 */
            content?: string;
            /** @description 发布时间 */
            dataTime?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 预警等级编码 */
            levelCode?: string;
            /** @description 预警等级名称 */
            levelName?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 预警名称 */
            name?: string;
            /** @description 位置名称 */
            stationName?: string;
            /** @description 预警标题 */
            title?: string;
            /** @description 预警类型编码 */
            typeCode?: string;
            /** @description 预警类型名称 */
            typeName?: string;
        };
        /**
         * DisasterWarnQueryDTO
         * @description 自然灾害预警统计
         */
        DisasterWarnQueryDTO: {
            /** @description 预警发布结束时间 yyyy-MM-dd HH:mm:ss */
            dataTimeEnd?: string;
            /** @description 预警发布开始时间 yyyy-MM-dd HH:mm:ss */
            dataTimeStart?: string;
            /** @description 预警等级 */
            level?: string;
            /** @description 预警状态(0.未解除预警  1.已解除预警) */
            status?: string;
        };
        /**
         * DutyPersonDTO
         * @description 责任人
         */
        DutyPersonDTO: {
            /**
             * Format: int32
             * @description 序号
             */
            index?: number;
            /** @description 级别 */
            level?: string;
            /** @description 姓名 */
            name?: string;
            /** @description 电话 */
            phoneNo?: string;
            /** @description 分类 */
            type?: string;
        };
        /**
         * DutyPersonQueryDTO
         * @description 三防责任人分页查询对象
         */
        DutyPersonQueryDTO: {
            /** @description 名称或地址 */
            keyword?: string;
            /** @description 行政区划 */
            regionCode?: string;
            /** @description 年度 */
            year: string;
        };
        /**
         * EmergencySuppliesDTO
         * @description 应急物资详情
         */
        EmergencySuppliesDTO: {
            /** @description 数量 */
            count?: string;
            /** @description 规格 */
            model?: string;
            /** @description 物资名称 */
            name?: string;
            /** @description 所属分类 */
            type?: string;
            /** @description 单位 */
            unit?: string;
        };
        /**
         * ExpertInformationDTO
         * @description 应急专家
         */
        ExpertInformationDTO: {
            /** @description 出生年月 */
            birthDate?: string;
            /** @description 获得称号(0-无，1-两院院士，2-博士生导师，3-国务院特殊津贴) */
            designate?: string;
            /** @description 学历(0-大专,1-本科,2-硕士,3-博士) */
            education?: string;
            /** @description 邮箱 */
            email?: string;
            /** @description id */
            id?: string;
            /** @description 身份证号码 */
            identity?: string;
            /** @description 专家名称 */
            name?: string;
            /** @description 国籍 */
            nationality?: string;
            /** @description 联系方式 */
            phoneNo?: string;
            /** @description 从事行业及方向 */
            profession?: string;
            /** @description 现从事行业描述 */
            professionDesc?: string;
            /** @description 职业资格(0-注册安全工程师，1-安全生产类，2-其他特殊方特类) */
            professionQualifications?: string;
            /** @description 来源:注册来源(0-导入，1-注册) */
            registerSource?: string;
            /** @description 性别 */
            sex?: string;
            /** @description 专业领域(安全生产/防灾救灾减灾/其他特殊专业) */
            specialtyDomain?: string;
            /** @description 激活状态(0-未激活,1-已激活) */
            status?: string;
            /** @description 职称(0-中级,1-高级) */
            title?: string;
            /** @description 专家类型(0-国家专家,1-省级专家,2-地市专家,3-区县专家) */
            type?: string;
        };
        /**
         * ExpertInformationQueryDTO
         * @description 应急专家查询对象
         */
        ExpertInformationQueryDTO: {
            /** @description 关键词 */
            keyword?: string;
        };
        /**
         * FloodWindPreventionDTO
         * @description 分页结果对象
         */
        FloodWindPreventionDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 联系人 */
            contractor?: string;
            /** @description 联系电话 */
            contractPhoneNo?: string;
            /** @description id */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 名称 */
            name?: string;
            /** @description tableRowId */
            tableRowId?: string;
        };
        /**
         * FloodWindPreventionQueryDTO
         * @description 分页查询对象
         */
        FloodWindPreventionQueryDTO: {
            /** @description 市县 */
            city?: string;
            /** @description 名称或地址 */
            keyword?: string;
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 年度 */
            year: string;
        };
        /**
         * ForestFireDTO
         * @description 火灾对象
         */
        ForestFireDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 市县名称 */
            city?: string;
            /** @description 人员伤亡描述 */
            deathTollDetail?: string;
            /** @description 火情简介(或说明) */
            description?: string;
            /** @description 持续时间 */
            durationTime?: string;
            /** @description 发现时间 */
            findTime?: string;
            /** @description 火场总面积(公顷)  */
            fireArea?: string;
            /** @description 起火原因 */
            fireCause?: string;
            /** @description 火场指挥员 */
            fireCommander?: string;
            /** @description 火灾级别 */
            fireLevel?: string;
            /** @description 火灾名称 */
            fireName?: string;
            /** @description 编号 */
            fireNo?: string;
            /** @description 起火时间 */
            fireTime?: string;
            /** @description 主键id */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 总损失 (万元)  */
            lossToll?: string;
            /** @description 出动扑火人员总数 */
            putoutFireCrew?: string;
            /** @description 扑灭时间 */
            putoutTime?: string;
            /** @description 受害森林总面积描述 */
            sufferForestAreaDetail?: string;
            /** @description 树种组成 */
            treeCategory?: string;
            /** @description 林木损失详情 */
            treeLossDetail?: string;
            /** @description 天气情况 */
            weather?: string;
        };
        /**
         * ForestFireDutyPersonDTO
         * @description 市县森林防灭火责任人通讯录
         */
        ForestFireDutyPersonDTO: {
            /** @description 备注 */
            bz?: string;
            /**
             * Format: int64
             * @description 主键id
             */
            id?: number;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 联系电话 */
            lxdh?: string;
            /** @description 市县（乡、镇、村）名称 */
            sxmc?: string;
            /** @description 所在市县 */
            szsx?: string;
            /** @description 姓名 */
            xm?: string;
            /** @description 职务 */
            zw?: string;
        };
        /**
         * ForestFireDutyPersonQueryDTO
         * @description 森林防灭火责任人通讯录查询对象
         */
        ForestFireDutyPersonQueryDTO: {
            /** @description 职务 */
            duty?: string;
            /** @description 姓名 */
            name?: string;
            /** @description 行政区划 */
            regionCode?: string;
        };
        /**
         * ForestFirePointsDayReportDTOReq
         * @description 森林火点日报报告对象
         */
        ForestFirePointsDayReportDTOReq: {
            /** @description 各市县火情起数统计 */
            cityStatisticList?: components["schemas"]["TextValueDTO"][];
            /** @description 结束时间 */
            endTime?: string;
            /**
             * Format: int32
             * @description 火情数量
             */
            fireNum?: number;
            /** @description 火情核实情况 */
            fireVerification: string;
            /** @description 编号 */
            reportCode: string;
            /** @description 日期 */
            reportDate?: string;
            /** @description 单位 */
            reportUnit?: string;
            /** @description 开始时间 */
            startTime?: string;
            /** @description 火灾防控建议 */
            suggestions: string;
            /** @description 火情发生时刻统计 */
            timeStatisticList?: components["schemas"]["TextValueDTO"][];
            /** @description 标题 */
            title?: string;
            /** @description 火情类型起数统计 */
            typeStatisticList?: components["schemas"]["TextValueDTO"][];
        };
        /**
         * ForestFirePointsDayReportDTORes
         * @description 森林火点简报报告对象
         */
        ForestFirePointsDayReportDTORes: {
            /** @description 事发地点 */
            address?: string;
            /**
             * Format: int32
             * @description 方位度
             */
            angle?: number;
            /** @description 伤亡情况 */
            casualties?: string;
            /** @description 复核情况 */
            description?: string;
            /** @description 距离 */
            distance?: string;
            /** @description 责任体系 */
            dutyPersonDTOList?: components["schemas"]["DutyPersonDTO"][];
            /** @description 救援物资 */
            emergencyResourcesList?: components["schemas"]["CommonResourceDTO"][];
            /** @description 火点编号 */
            fireCode?: string;
            /** @description 事发时间 */
            happenTime?: string;
            /** @description 高程 */
            height?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 医疗保障资源 */
            medicalResourcesDTOList?: components["schemas"]["MedicalResourcesDTO"][];
            /** @description 期数 */
            periodNumber?: string;
            /** @description 重点防护目标（变电站/天然气/加油站） */
            protectResourcesList?: components["schemas"]["CommonResourceDTO"][];
            /**
             * Format: int32
             * @description 范围
             */
            radius?: number;
            /** @description 作战地图 */
            reportImage: string;
            /** @description 报告时间 */
            reportTime?: string;
            /** @description 标题 */
            reportTitle?: string;
            /** @description 单位 */
            reportUnit?: string;
            /** @description 救援力量（消防队伍/扑火队伍/社会救援组织） */
            rescueForceDTOList?: components["schemas"]["CommonResourceDTO"][];
            /** @description 植被分布 */
            veg?: string;
        };
        /**
         * ForestFirePointsDayStatisticDTO
         * @description 森林火点日报查询对象
         */
        ForestFirePointsDayStatisticDTO: {
            /** @description 各市县火情起数统计 */
            cityStatisticList?: components["schemas"]["StatisticDTO"][];
            /** @description 火情发生时刻统计 */
            timeStatisticList?: components["schemas"]["StatisticDTO"][];
            /** @description 火情类型起数统计 */
            typeStatisticList?: components["schemas"]["StatisticDTO"][];
        };
        /**
         * ForestFireProtHelicopter
         * @description 森林消防直升机信息
         */
        ForestFireProtHelicopter: {
            /**
             * Format: double
             * @description 巡航速度(公里/小时)
             */
            cruiseSpeed?: number;
            /** @description 描述 */
            desc?: string;
            /**
             * Format: double
             * @description 外吊挂能力(公斤)
             */
            externalHangingCapacity?: number;
            /**
             * Format: double
             * @description 高度（米）
             */
            height?: number;
            /** @description 主键id */
            id?: string;
            /** @description 图片相对路径 */
            imagePaths?: string;
            /**
             * Format: double
             * @description 长度（米）
             */
            length?: number;
            /**
             * Format: double
             * @description 最大航程(公里)
             */
            maxRange?: number;
            /**
             * Format: double
             * @description 最大起飞重量(公斤)
             */
            maxTakeoffWeight?: number;
            /**
             * Format: int32
             * @description 最大可运载(乘客或任务员)
             */
            maxTransportablePassenger?: number;
            /**
             * Format: int32
             * @description 最大可运载(飞行员)
             */
            maxTransportablePilot?: number;
            /** @description 直升机名称 */
            name?: string;
            /**
             * Format: double
             * @description 有效载荷(公斤)
             */
            payload?: number;
            /**
             * Format: double
             * @description 旋翼直径（米）
             */
            rotorDiameter?: number;
            /**
             * Format: double
             * @description 宽度（米）
             */
            width?: number;
        };
        /**
         * ForestFireProtHelicopterReservoir
         * @description 森林消防直升机周边水库信息
         */
        ForestFireProtHelicopterReservoir: {
            /** @description 市县编码 */
            cityCode?: string;
            /** @description 市县名称 */
            cityName?: string;
            /** @description 净空 */
            clearance?: string;
            /** @description 描述 */
            desc?: string;
            /**
             * Format: double
             * @description 距离
             */
            distance?: number;
            /** @description 主键id */
            id?: string;
            /** @description 图片相对路径 */
            imagePaths?: string;
            /** @description 维度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 水库名称 */
            name?: string;
            /**
             * Format: double
             * @description 蓄水量（m）
             */
            storageCapacity?: number;
            /** @description 乡镇编码 */
            townshipCode?: string;
            /** @description 乡镇名称 */
            townshipName?: string;
            /** @description 真方位 */
            trueAzimuth?: string;
        };
        /**
         * ForestFireProtHeliport对象
         * @description 森林消防直升机场信息
         */
        ForestFireProtHeliportDuiXiang: {
            /** @description 地址 */
            address?: string;
            /**
             * Format: double
             * @description 海拔高度（m）
             */
            altitude?: number;
            /** @description 描述 */
            desc?: string;
            /** @description 方位 */
            direction?: string;
            /** @description 主键id */
            id?: string;
            /** @description 图片相对路径 */
            imagePaths?: string;
            /**
             * Format: double
             * @description 维度
             */
            latitude?: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude?: number;
            /** @description 机场名称 */
            name?: string;
            /** @description 行政区划编码 */
            regionCode?: string;
            /** @description 行政区划名称 */
            regionName?: string;
            /** @description 跑道长 */
            runwayLength?: string;
            /** @description 跑道面 */
            runwaySurface?: string;
            /** @description 跑道宽 */
            runwayWidth?: string;
            /** @description 类型：1-通航直升机场 2-硬化直升机坪 3-无保障临时起降点 */
            type?: string;
        };
        /**
         * ForestFireProtReservoir
         * @description 森林消防水库信息
         */
        ForestFireProtReservoir: {
            /** @description 市县编码 */
            cityCode?: string;
            /** @description 市县名称 */
            cityName?: string;
            /** @description 净空 */
            clearance?: string;
            /** @description 描述 */
            desc?: string;
            /** @description 主键id */
            id?: string;
            /** @description 图片相对路径 */
            imagePaths?: string;
            /** @description 维度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 水库名称 */
            name?: string;
            /**
             * Format: double
             * @description 蓄水量（m）
             */
            storageCapacity?: number;
            /** @description 乡镇编码 */
            townshipCode?: string;
            /** @description 乡镇名称 */
            townshipName?: string;
        };
        /**
         * ForestFireQueryDTO
         * @description 火灾查询对象
         */
        ForestFireQueryDTO: {
            /** @description 火灾级别 */
            fireLevel?: string;
            /** @description 火灾名称 */
            fireName?: string;
            /** @description 市县编码 */
            regionCode?: string;
        };
        /**
         * ForestFireSimpleReportFormDTO
         * @description 森林火点简报入参对象
         */
        ForestFireSimpleReportFormDTO: {
            /**
             * Format: int32
             * @description 距离，单位：公里
             */
            distance: number;
            /**
             * Format: int32
             * @description 火点id
             */
            id: number;
            /**
             * Format: double
             * @description 纬度
             */
            latitude: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude: number;
        };
        /**
         * ForestFireStatisticDTO
         * @description 深林火灾趋势统计
         */
        ForestFireStatisticDTO: {
            /**
             * Format: int32
             * @description 火点数量
             */
            firePointNum?: number;
            /**
             * Format: int32
             * @description 深林火灾数量
             */
            forestFireNum?: number;
            /** @description 年度 */
            year?: string;
        };
        /**
         * Grib2DTO
         * @description Grib2数据对象
         */
        Grib2DTO: {
            /** @description 编码 */
            code?: string;
            /**
             * Format: date-time
             * @description 数据时间
             */
            dataTime?: string;
            /** @description 文件地址 */
            filePath?: string;
            /** @description 主键id */
            id?: string;
            /** @description 名称 */
            name?: string;
        };
        /**
         * Grib2QueryDTO
         * @description Grib2数据查询对象
         */
        Grib2QueryDTO: {
            /** @description 编码 */
            code?: string;
            /**
             * Format: date-time
             * @description 数据时间
             */
            dataTime?: string;
        };
        /**
         * MedicalResourcesDTO
         * @description 医疗保障资源
         */
        MedicalResourcesDTO: {
            /** @description 医疗资源位置 */
            address?: string;
            /** @description 距离 */
            distance?: string;
            /**
             * Format: int32
             * @description 序号
             */
            index?: number;
            /** @description 医疗资源名称 */
            name?: string;
            /** @description 医疗资源类别 */
            type?: string;
        };
        /**
         * NetworkForestFirePointsDetailDTO
         * @description 网络森林火点明细对象
         */
        NetworkForestFirePointsDetailDTO: {
            /** @description 行政编码 */
            adminCode?: string;
            /**
             * Format: int32
             * @description 灌木%
             */
            bush?: number;
            /**
             * Format: int32
             * @description 城市或荒漠%
             */
            city?: number;
            /** @description 市县行政区划名 */
            cityRegion?: string;
            /** @description 火情编号 */
            code?: string;
            /**
             * Format: int32
             * @description 方位度
             */
            direction?: number;
            /**
             * Format: double
             * @description 距离火点
             */
            distance?: number;
            /**
             * Format: int64
             * @description 高程aver
             */
            elevationAver?: number;
            /**
             * Format: int64
             * @description 高程max
             */
            elevationMax?: number;
            /**
             * Format: int64
             * @description 高程min
             */
            elevationMin?: number;
            /**
             * Format: int32
             * @description 农田及自然植被%
             */
            farm?: number;
            /**
             * Format: int32
             * @description 森林%
             */
            forestPercent?: number;
            /** Format: int32 */
            g10?: number;
            /** Format: int32 */
            g20?: number;
            /** Format: int32 */
            g30?: number;
            /** Format: int32 */
            g40?: number;
            /** Format: int32 */
            g50?: number;
            /** Format: int32 */
            g60?: number;
            /**
             * Format: int32
             * @description GF0
             */
            gf0?: number;
            /**
             * Format: int32
             * @description GFaver
             */
            gfAver?: number;
            /**
             * Format: int32
             * @description GF%
             */
            gfPercent?: number;
            /**
             * Format: int64
             * @description 热点数
             */
            hotspot?: number;
            /**
             * Format: int64
             * @description 主键ID
             */
            id?: number;
            /**
             * Format: int32
             * @description 强度G
             */
            intensityG?: number;
            /**
             * Format: int32
             * @description 强度T
             */
            intensityT?: number;
            /**
             * @description 是否核查
             * @example false
             */
            isCheck?: boolean;
            /** @description 纬度 */
            latitude?: string;
            /**
             * Format: int32
             * @description 草地%
             */
            lawnPercent?: number;
            /** @description 经度 */
            longitude?: string;
            /**
             * Format: date-time
             * @description 观测时间
             */
            observeTime?: string;
            /** @description 地名 */
            place?: string;
            /** @description 真实植被(土地)类型 */
            realVegType?: string;
            /**
             * Format: int32
             * @description 重复火情出现次数（半小时内，经纬度相识--3位小数）
             */
            repeat?: number;
            /** @description 树种类型 */
            speciesType?: string;
            /**
             * Format: int32
             * @description G阈值
             */
            thresholdG?: number;
            /**
             * Format: int32
             * @description T阈值
             */
            thresholdT?: number;
            /** @description 预测植被(土地)类型 */
            vegType?: string;
            /**
             * Format: int64
             * @description 像素x
             */
            x?: number;
            /**
             * Format: int64
             * @description 像素y
             */
            y?: number;
        };
        /**
         * NetworkForestFirePointsDTO
         * @description 网络森林火点对象
         */
        NetworkForestFirePointsDTO: {
            /** @description 数据时间 */
            dateTime?: string;
            /** @description 最早发现火灾的系统处理时间 */
            detailList?: components["schemas"]["NetworkForestFirePointsDetailDTO"][];
            /** @description 主键ID */
            id?: string;
        };
        /**
         * OceanDataListDTO
         * @description 海洋预警列表对象
         */
        OceanDataListDTO: {
            /** @description 预警时间 */
            alarmDate?: string;
            /** @description 预警类型 */
            alarmType?: string;
            /** @description 描述 */
            description?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 等级 */
            level?: string;
            /** @description 部门名称 */
            organName?: string;
            /** @description 标题 */
            title?: string;
            /** @description 更新时间 */
            updateDate?: string;
        };
        /**
         * OceanTypeQueryDTO
         * @description 海洋预警查询
         */
        OceanTypeQueryDTO: {
            /** @description 预警类型 */
            alarmType?: string;
            /** @description 结束日期 yyyy-MM-dd */
            endDate?: string;
            /** @description 是否去除非预警数据：1-是 0-否 */
            isExcludeWaringData?: string;
            /** @description 开始日期 yyyy-MM-dd */
            startDate?: string;
        };
        /**
         * PageParam«CloudChartPageQueryDTO»
         * @description 分页参数
         */
        PageParamCloudChartPageQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["CloudChartPageQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«DisasterWarnDTO»
         * @description 分页参数
         */
        PageParamDisasterWarnDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["DisasterWarnDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["DisasterWarnDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«DisasterWarnQueryDTO»
         * @description 分页参数
         */
        PageParamDisasterWarnQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["DisasterWarnQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«DutyPersonQueryDTO»
         * @description 分页参数
         */
        PageParamDutyPersonQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["DutyPersonQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«ExpertInformationDTO»
         * @description 分页参数
         */
        PageParamExpertInformationDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["ExpertInformationDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["ExpertInformationDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«ExpertInformationQueryDTO»
         * @description 分页参数
         */
        PageParamExpertInformationQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["ExpertInformationQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«FloodWindPreventionDTO»
         * @description 分页参数
         */
        PageParamFloodWindPreventionDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["FloodWindPreventionDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["FloodWindPreventionDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«FloodWindPreventionQueryDTO»
         * @description 分页参数
         */
        PageParamFloodWindPreventionQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["FloodWindPreventionQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«ForestFireDTO»
         * @description 分页参数
         */
        PageParamForestFireDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["ForestFireDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["ForestFireDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«ForestFireDutyPersonDTO»
         * @description 分页参数
         */
        PageParamForestFireDutyPersonDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["ForestFireDutyPersonDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["ForestFireDutyPersonDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«ForestFireDutyPersonQueryDTO»
         * @description 分页参数
         */
        PageParamForestFireDutyPersonQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["ForestFireDutyPersonQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«ForestFireQueryDTO»
         * @description 分页参数
         */
        PageParamForestFireQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["ForestFireQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«NetworkForestFirePointsDTO»
         * @description 分页参数
         */
        PageParamNetworkForestFirePointsDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["NetworkForestFirePointsDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["NetworkForestFirePointsDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«OceanDataListDTO»
         * @description 分页参数
         */
        PageParamOceanDataListDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["OceanDataListDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["OceanDataListDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«OceanTypeQueryDTO»
         * @description 分页参数
         */
        PageParamOceanTypeQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["OceanTypeQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«RadarDTO»
         * @description 分页参数
         */
        PageParamRadarDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["RadarDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["RadarDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«SatelliteDTO»
         * @description 分页参数
         */
        PageParamSatelliteDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SatelliteDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SatelliteDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«SfReservoirDTO»
         * @description 分页参数
         */
        PageParamSfReservoirDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SfReservoirDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SfReservoirDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«SfReservoirQueryDTO»
         * @description 分页参数
         */
        PageParamSfReservoirQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["SfReservoirQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«SpccDTO»
         * @description 分页参数
         */
        PageParamSpccDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SpccDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SpccDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«TideDTO»
         * @description 分页参数
         */
        PageParamTideDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["TideDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["TideDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«TideQueryDTO»
         * @description 分页参数
         */
        PageParamTideQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["TideQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«TYForestFirePointsDTO»
         * @description 分页参数
         */
        PageParamTYForestFirePointsDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["TYForestFirePointsDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["TYForestFirePointsDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«TYForestFirePointsQueryDTO»
         * @description 分页参数
         */
        PageParamTYForestFirePointsQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["TYForestFirePointsQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«WeatherInfoDTO»
         * @description 分页参数
         */
        PageParamWeatherInfoDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["WeatherInfoDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["WeatherInfoDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«WeatherInfoQueryDTO»
         * @description 分页参数
         */
        PageParamWeatherInfoQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["WeatherInfoQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«WindDTO»
         * @description 分页参数
         */
        PageParamWindDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["WindDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["WindDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«WindQueryDTO»
         * @description 分页参数
         */
        PageParamWindQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["WindQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PointListDTO
         * @description 上球点列表对象DTO
         */
        PointListDTO: {
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
        };
        /**
         * RadarDTO
         * @description 雷达对象
         */
        RadarDTO: {
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 原文件名 */
            fileName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 图片路径 */
            imgPath?: string;
            /** @description 左下经纬度 */
            leftBottomLonLat?: string;
            /** @description 左上经纬度 */
            leftTopLonLat?: string;
            /** @description 日期 */
            occurDate?: string;
            /** @description 右下经纬度 */
            rightBottomLonLat?: string;
            /** @description 右上经纬度 */
            rightTopLonLat?: string;
            /** @description 请求路径 */
            uri?: string;
        };
        /**
         * RainfallDetailDTO
         * @description 降雨明细对象
         */
        RainfallDetailDTO: {
            /** @description 站点地址 */
            address?: string;
            /** @description 市行政编码 */
            cityCode?: string;
            /** @description 市行政名称 */
            cityName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 最大降雨量 */
            maxRainfallValue?: string;
            /** @description 站点名称 */
            name?: string;
            /** @description 隶属行业名称 */
            strongName?: string;
            /** @description 隶属行业编码 */
            strongType?: string;
            /** @description 累计降雨量 */
            sumRainfallValue?: string;
            /** @description 乡镇行政编码 */
            townCode?: string;
            /** @description 乡镇行政名称 */
            townName?: string;
            /** @description 站点类别 */
            type?: string;
            /** @description 站点名称 */
            typeName?: string;
        };
        /**
         * RainfallDTO
         * @description 降雨对象
         */
        RainfallDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 市县 */
            cityName?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 最大降雨量 */
            maxRainfallValue?: string;
            /** @description 站点名称 */
            stateName?: string;
            /** @description 累计降雨量 */
            sumRainfallValue?: string;
        };
        /**
         * RainfallQueryDTO
         * @description 降水查询对象
         */
        RainfallQueryDTO: {
            /**
             * Format: date-time
             * @description 结束时间
             */
            endTime?: string;
            /** @description 站点ID */
            id?: string;
            /** @description 市县编码 */
            regionCode?: string;
            /**
             * Format: date-time
             * @description 开始时间
             */
            startTime?: string;
        };
        /** Result«AdministrativeBaseDTO» */
        ResultAdministrativeBaseDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["AdministrativeBaseDTO"];
            message?: string;
        };
        /** Result«DutyPersonDTO» */
        ResultDutyPersonDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["DutyPersonDTO"];
            message?: string;
        };
        /** Result«ForestFireDTO» */
        ResultForestFireDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireDTO"];
            message?: string;
        };
        /** Result«ForestFireDutyPersonDTO» */
        ResultForestFireDutyPersonDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireDutyPersonDTO"];
            message?: string;
        };
        /** Result«ForestFirePointsDayReportDTO» */
        ResultForestFirePointsDayReportDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFirePointsDayReportDTORes"];
            message?: string;
        };
        /** Result«ForestFirePointsDayStatisticDTO» */
        ResultForestFirePointsDayStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFirePointsDayStatisticDTO"];
            message?: string;
        };
        /** Result«Grib2DTO» */
        ResultGrib2DTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["Grib2DTO"];
            message?: string;
        };
        /** Result«List«CityMapInfoDTO»» */
        ResultListCityMapInfoDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["CityMapInfoDTO"][];
            message?: string;
        };
        /** Result«List«EmergencySuppliesDTO»» */
        ResultListEmergencySuppliesDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["EmergencySuppliesDTO"][];
            message?: string;
        };
        /** Result«List«ForestFireProtHelicopter»» */
        ResultListForestFireProtHelicopter: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireProtHelicopter"][];
            message?: string;
        };
        /** Result«List«ForestFireProtHelicopterReservoir»» */
        ResultListForestFireProtHelicopterReservoir: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireProtHelicopterReservoir"][];
            message?: string;
        };
        /** Result«List«ForestFireProtHeliport对象»» */
        ResultListForestFireProtHeliportDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireProtHeliportDuiXiang"][];
            message?: string;
        };
        /** Result«List«ForestFireProtReservoir»» */
        ResultListForestFireProtReservoir: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireProtReservoir"][];
            message?: string;
        };
        /** Result«List«ForestFireStatisticDTO»» */
        ResultListForestFireStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireStatisticDTO"][];
            message?: string;
        };
        /** Result«List«OceanDataListDTO»» */
        ResultListOceanDataListDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["OceanDataListDTO"][];
            message?: string;
        };
        /** Result«List«PointListDTO»» */
        ResultListPointListDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PointListDTO"][];
            message?: string;
        };
        /** Result«List«RainfallDTO»» */
        ResultListRainfallDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["RainfallDTO"][];
            message?: string;
        };
        /** Result«List«SfReservoirPointsDTO»» */
        ResultListSfReservoirPointsDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SfReservoirPointsDTO"][];
            message?: string;
        };
        /** Result«List«StatisticDTO»» */
        ResultListStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["StatisticDTO"][];
            message?: string;
        };
        /** Result«List«TextValueDTO»» */
        ResultListTextValueDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TextValueDTO"][];
            message?: string;
        };
        /** Result«List«TideDTO»» */
        ResultListTideDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TideDTO"][];
            message?: string;
        };
        /** Result«List«TiePointsListDTO»» */
        ResultListTiePointsListDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TiePointsListDTO"][];
            message?: string;
        };
        /** Result«List«TYForestFirePointsDTO»» */
        ResultListTYForestFirePointsDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TYForestFirePointsDTO"][];
            message?: string;
        };
        /** Result«List«TyphoonDetailDTO»» */
        ResultListTyphoonDetailDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TyphoonDetailDTO"][];
            message?: string;
        };
        /** Result«List«TyphoonDTO»» */
        ResultListTyphoonDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TyphoonDTO"][];
            message?: string;
        };
        /** Result«List«waveFrequencyDTO»» */
        ResultListwaveFrequencyDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WaveFrequencyDTO"][];
            message?: string;
        };
        /** Result«List«WindDTO»» */
        ResultListWindDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WindDTO"][];
            message?: string;
        };
        /** Result«NetworkForestFirePointsDetailDTO» */
        ResultNetworkForestFirePointsDetailDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["NetworkForestFirePointsDetailDTO"];
            message?: string;
        };
        /** Result«PageParam«DisasterWarnDTO»» */
        ResultPageParamDisasterWarnDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamDisasterWarnDTO"];
            message?: string;
        };
        /** Result«PageParam«ExpertInformationDTO»» */
        ResultPageParamExpertInformationDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamExpertInformationDTO"];
            message?: string;
        };
        /** Result«PageParam«FloodWindPreventionDTO»» */
        ResultPageParamFloodWindPreventionDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamFloodWindPreventionDTO"];
            message?: string;
        };
        /** Result«PageParam«ForestFireDTO»» */
        ResultPageParamForestFireDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamForestFireDTO"];
            message?: string;
        };
        /** Result«PageParam«ForestFireDutyPersonDTO»» */
        ResultPageParamForestFireDutyPersonDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamForestFireDutyPersonDTO"];
            message?: string;
        };
        /** Result«PageParam«NetworkForestFirePointsDTO»» */
        ResultPageParamNetworkForestFirePointsDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamNetworkForestFirePointsDTO"];
            message?: string;
        };
        /** Result«PageParam«OceanDataListDTO»» */
        ResultPageParamOceanDataListDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamOceanDataListDTO"];
            message?: string;
        };
        /** Result«PageParam«RadarDTO»» */
        ResultPageParamRadarDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamRadarDTO"];
            message?: string;
        };
        /** Result«PageParam«SatelliteDTO»» */
        ResultPageParamSatelliteDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSatelliteDTO"];
            message?: string;
        };
        /** Result«PageParam«SfReservoirDTO»» */
        ResultPageParamSfReservoirDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSfReservoirDTO"];
            message?: string;
        };
        /** Result«PageParam«SpccDTO»» */
        ResultPageParamSpccDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSpccDTO"];
            message?: string;
        };
        /** Result«PageParam«TideDTO»» */
        ResultPageParamTideDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamTideDTO"];
            message?: string;
        };
        /** Result«PageParam«TYForestFirePointsDTO»» */
        ResultPageParamTYForestFirePointsDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamTYForestFirePointsDTO"];
            message?: string;
        };
        /** Result«PageParam«WeatherInfoDTO»» */
        ResultPageParamWeatherInfoDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamWeatherInfoDTO"];
            message?: string;
        };
        /** Result«PageParam«WindDTO»» */
        ResultPageParamWindDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamWindDTO"];
            message?: string;
        };
        /** Result«RainfallDetailDTO» */
        ResultRainfallDetailDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["RainfallDetailDTO"];
            message?: string;
        };
        /** Result«SfReservoirDTO» */
        ResultSfReservoirDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SfReservoirDTO"];
            message?: string;
        };
        /** Result«SfReservoirPlanDTO» */
        ResultSfReservoirPlanDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SfReservoirPlanDTO"];
            message?: string;
        };
        /** Result«TextValueDTO» */
        ResultTextValueDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TextValueDTO"];
            message?: string;
        };
        /**
         * SatelliteDTO
         * @description 卫星云图对象
         */
        SatelliteDTO: {
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 原文件名 */
            fileName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 图片路径 */
            imgPath?: string;
            /** @description 左下经纬度 */
            leftBottomLonLat?: string;
            /** @description 左上经纬度 */
            leftTopLonLat?: string;
            /** @description 日期 */
            occurDate?: string;
            /** @description 右下经纬度 */
            rightBottomLonLat?: string;
            /** @description 右上经纬度 */
            rightTopLonLat?: string;
            /** @description 请求路径 */
            uri?: string;
        };
        /**
         * SfReservoirDTO
         * @description 三防信息网-水库对象
         */
        SfReservoirDTO: {
            /** @description 校核洪水位 */
            chfllv?: string;
            /** @description 水库编码 */
            code?: string;
            /** @description 死库容 */
            ddst?: string;
            /** @description 死水位 */
            ddwtlv?: string;
            /** @description 水库地址 */
            dmstatpl?: string;
            /** @description 集水面积 */
            drbsar?: string;
            /** @description 设计洪水位 */
            dsfllv?: string;
            /** @description 调节库容 */
            efst?: string;
            /** @description 工程等别 */
            encl?: string;
            /** @description 工程等别名称 */
            enclName?: string;
            /** @description 主键ID */
            id?: string;
            /** @description labelMaxZoom */
            labelMaxZoom?: string;
            /** @description labelMinZoom */
            labelMinZoom?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description maxZoom */
            maxZoom?: string;
            /** @description 水库名称 */
            name?: string;
            /** @description 正常水位 */
            nrwtlv?: string;
            /** @description 调洪库容 */
            rgflrscp?: string;
            /** @description 调节特性 */
            rsadch?: string;
            /** @description 人工报讯站编码 */
            skcd?: string;
            /** @description 自动测站编码 */
            stcd?: string;
            /** @description 总库容 */
            ttst?: string;
            /** @description 管理单位 */
            unit?: string;
        };
        /**
         * SfReservoirPlanDTO
         * @description 三防信息网-水库预案对象
         */
        SfReservoirPlanDTO: {
            /** @description 预案附件名称 */
            fileName?: string;
            /** @description 预案附件地址 */
            filePath?: string;
            /** @description 预案信息 */
            planText?: string;
            /** @description 预案ID */
            plantId?: string;
        };
        /**
         * SfReservoirPointsDTO
         * @description 三防信息网-水库上球点列表对象DTO
         */
        SfReservoirPointsDTO: {
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 人工报讯站编码 */
            skcd?: string;
            /** @description 自动测站编码 */
            stcd?: string;
        };
        /**
         * SfReservoirQueryDTO
         * @description 三防信息网-水库查询对象
         */
        SfReservoirQueryDTO: {
            /** @description 水库名称 */
            name?: string;
            /** @description 行政区划代码 */
            regionCode?: string;
        };
        /**
         * SpccDTO
         * @description 降雨量对象
         */
        SpccDTO: {
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 原文件名 */
            fileName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 图片路径 */
            imgPath?: string;
            /** @description 左下经纬度 */
            leftBottomLonLat?: string;
            /** @description 左上经纬度 */
            leftTopLonLat?: string;
            /** @description 日期 */
            occurDate?: string;
            /** @description 右下经纬度 */
            rightBottomLonLat?: string;
            /** @description 右上经纬度 */
            rightTopLonLat?: string;
            /** @description 请求路径 */
            uri?: string;
        };
        /**
         * StatisticDTO
         * @description 统计对象
         */
        StatisticDTO: {
            /** @description 编码 */
            code?: string;
            /** @description 描述 */
            text?: string;
            /** @description 数量/百分比 */
            value?: string;
        };
        /**
         * TextValueDTO
         * @description TextValueDTO
         */
        TextValueDTO: {
            /** @description text */
            text?: string;
            /** @description value */
            value?: string;
        };
        /**
         * TideDTO
         * @description 中国海洋预报-天文潮预报
         */
        TideDTO: {
            /** @description 潮位基准面 */
            cxdatum?: string;
            /** @description 高潮时间1 */
            httime1?: string;
            /** @description 高潮时间2 */
            httime2?: string;
            /** @description 高潮1潮位(cm) */
            htvalue1?: string;
            /** @description 高潮2潮位(cm) */
            htvalue2?: string;
            /** @description 主键id */
            id?: string;
            /** @description 低潮时间1 */
            lttime1?: string;
            /** @description 低潮时间2 */
            lttime2?: string;
            /** @description 低潮1潮位(cm) */
            ltvalue1?: string;
            /** @description 低潮2潮位(cm) */
            ltvalue2?: string;
            /**
             * Format: date
             * @description 日期
             */
            posttime?: string;
            /** @description 天文潮编码 */
            tideId?: string;
            /** @description 天文潮名称 */
            tideName?: string;
        };
        /**
         * TideQueryDTO
         * @description 中国海洋预报-天文潮预报查询对象
         */
        TideQueryDTO: {
            /** @description 天文潮名称 */
            tideName?: string;
        };
        /**
         * TiePointsListDTO
         * @description 中国海洋预报-天文潮站点上球对象
         */
        TiePointsListDTO: {
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 站点名称 */
            name?: string;
        };
        /**
         * TYForestFirePointsDTO
         * @description 天眼森林火点对象
         */
        TYForestFirePointsDTO: {
            /** @description 火灾地址 */
            address?: string;
            /** @description 报警次数 */
            alarmTimes?: string;
            /** @description 过火面积 */
            area?: string;
            /** @description 重大火灾关注指数 */
            attentionValue?: string;
            /** @description 火点亮温值 */
            brightness?: string;
            /** @description 城市 */
            city?: string;
            /** @description 火点置信度 */
            confidence?: string;
            /** @description 最近更新火灾的影像时间 */
            endDatetime?: string;
            /** @description 最近更新火灾的系统处理时间 */
            endSysDatetime?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 火灾报警标识 */
            isAlarm?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 火点像素个数 */
            pixelCount?: string;
            /** @description 省份 */
            province?: string;
            /** @description 文件发布时间 */
            releaseDatetime?: string;
            /** @description 使用的遥感卫星名称 */
            remoteSensing?: string;
            /** @description 最早报警的影像时间 */
            startAlarmRsDatetime?: string;
            /** @description 最早报警的系统时间 */
            startAlarmSysDatetime?: string;
            /** @description 最早发现火灾的影像时间 */
            startDatetime?: string;
            /** @description 最早发现火灾的系统处理时间 */
            startSysDatetime?: string;
        };
        /**
         * TYForestFirePointsQueryDTO
         * @description 天眼森林火点查询对象
         */
        TYForestFirePointsQueryDTO: {
            /** @description 日期查询-结束时间，格式 yyyy-MM-dd */
            datetimeEnd?: string;
            /** @description 日期查询-开始时间，格式 yyyy-MM-dd */
            datetimeStart?: string;
            /** @description 行政区划 */
            regionCode?: string;
        };
        /**
         * TyphoonDetailDTO
         * @description 台风明细对象
         */
        TyphoonDetailDTO: {
            /** @description 预报 */
            forecast?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 参考位置 */
            moveDir?: string;
            /** @description 移动速度（单位：m/s） */
            moveSpeed?: string;
            /** @description 风力（单位：级） */
            power?: string;
            /** @description 压力 */
            pressure?: string;
            /** @description 圆形7 */
            radius7?: string;
            /** @description 圆形json7 */
            radius7Quad?: string;
            /** @description 圆形10 */
            radius10?: string;
            /** @description 圆形json10 */
            radius10Quad?: string;
            /** @description 圆形12 */
            radius12?: string;
            /** @description 圆形json12 */
            radius12Quad?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 风速（单位：m/s） */
            speed?: string;
            /** @description 强度 */
            strong?: string;
            /**
             * Format: date-time
             * @description 时间
             */
            time?: string;
            /** @description 台风编号 */
            typhoonNo?: string;
        };
        /**
         * TyphoonDTO
         * @description 台风对象
         */
        TyphoonDTO: {
            /**
             * Format: date-time
             * @description 开始时间
             */
            beginTime?: string;
            /**
             * Format: date-time
             * @description 结束时间
             */
            endTime?: string;
            /** @description 英文名称 */
            enName?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 是否是当前台风：1-是 0-否 */
            isCurrent?: string;
            /** @description 登录地址 */
            land?: string;
            /** @description 中文名称 */
            name?: string;
            /** @description 台风编号 */
            typhoonNo?: string;
        };
        /**
         * TyphoonQueryDTO
         * @description 台风查询对象
         */
        TyphoonQueryDTO: {
            /** @description 是否是当前台风：1-是 0-否 */
            isCurrent?: string;
            /** @description 年度 */
            year?: string;
        };
        /**
         * waveFrequencyDTO
         * @description 海浪频率统计对象
         */
        WaveFrequencyDTO: {
            /**
             * Format: bigdecimal
             * @description 北东北方位频率（%）
             */
            bdbfw?: number;
            /**
             * Format: bigdecimal
             * @description 北方位频率（%）
             */
            bfw?: number;
            /**
             * Format: bigdecimal
             * @description 北西北方位频率（%）
             */
            bxbfw?: number;
            /**
             * Format: bigdecimal
             * @description 中心维度
             */
            centerLat?: number;
            /**
             * Format: bigdecimal
             * @description 中心经度
             */
            centerLng?: number;
            /**
             * Format: bigdecimal
             * @description 东北方位频率（%）
             */
            dbfw?: number;
            /**
             * Format: bigdecimal
             * @description 东东北方位频率（%）
             */
            ddbfw?: number;
            /**
             * Format: bigdecimal
             * @description 东东南方位频率（%）
             */
            ddnfw?: number;
            /**
             * Format: bigdecimal
             * @description 东方位频率（%）
             */
            dfw?: number;
            /**
             * Format: bigdecimal
             * @description 东南方位频率（%）
             */
            dnfw?: number;
            /**
             * Format: bigdecimal
             * @description 南东南方位频率（%）
             */
            dnnfw?: number;
            /** @description 主键ID */
            id?: string;
            /** @description 海浪分级 */
            level?: string;
            /** @description 海域名称 */
            name?: string;
            /**
             * Format: bigdecimal
             * @description 南方位频率（%）
             */
            nfw?: number;
            /**
             * Format: bigdecimal
             * @description 南西南方位频率（%）
             */
            nxnfw?: number;
            /**
             * Format: int32
             * @description 序号
             */
            sort?: number;
            /** @description 类型 */
            type?: string;
            /**
             * Format: bigdecimal
             * @description 西北方位频率（%）
             */
            xbfw?: number;
            /**
             * Format: bigdecimal
             * @description 西北西方位频率（%）
             */
            xbxfw?: number;
            /**
             * Format: bigdecimal
             * @description 西方位频率（%）
             */
            xfw?: number;
            /**
             * Format: bigdecimal
             * @description 西南方位频率（%）
             */
            xnfw?: number;
            /**
             * Format: bigdecimal
             * @description 西西南方位频率（%）
             */
            xxnfw?: number;
        };
        /**
         * WeatherInfoDTO
         * @description 气象信息
         */
        WeatherInfoDTO: {
            /** @description 事件发生概率：Observed(确定发生)VeryLikely(非常可能)Likely(可能发生)Unlikely(不大可能)Unknown(未知)可信度未知。默认Unknown */
            certainty?: string;
            /** @description 发布内容 */
            content?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 对建议采取措施的描述 */
            instruction?: string;
            /** @description 发布单位名称 */
            sender?: string;
            /** @description 发布时间 */
            sendTime?: string;
            /** @description 严重程度:Red(红色预警/I级/特别重大)Orange(橙色预警/II级/重大)Yellow(黄色预警/III/较大)Blue(蓝色预警/IV/一般)Unknown(未知,9)（注：界面上的级别列表显示如“红色预警/I级/特别重大”） */
            serious?: string;
            /** @description 紧急程度:Immediate(立即行动)Expected(准备行动)Future(等待行动)Past(已过去)Unknown(未知) */
            urgency?: string;
        };
        /**
         * WeatherInfoQueryDTO
         * @description 气象信息查询对象
         */
        WeatherInfoQueryDTO: {
            /** @description 事件发生概率：Observed(确定发生)VeryLikely(非常可能)Likely(可能发生)Unlikely(不大可能)Unknown(未知)可信度未知。默认Unknown */
            certainty?: string;
            /** @description 发布单位名称 */
            sender?: string;
            /** @description 严重程度:Red(红色预警/I级/特别重大)Orange(橙色预警/II级/重大)Yellow(黄色预警/III/较大)Blue(蓝色预警/IV/一般)Unknown(未知,9)（注：界面上的级别列表显示如“红色预警/I级/特别重大”） */
            serious?: string;
            /** @description 紧急程度:Immediate(立即行动)Expected(准备行动)Future(等待行动)Past(已过去)Unknown(未知) */
            urgency?: string;
        };
        /**
         * WindDTO
         * @description 大风对象
         */
        WindDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 市县 */
            cityName?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 最大风力(级) */
            maxWindPower?: string;
            /** @description 最大风速(m/s) */
            maxWindSpeed?: string;
        };
        /**
         * WindQueryDTO
         * @description 大风查询对象
         */
        WindQueryDTO: {
            /**
             * Format: date-time
             * @description 结束时间
             */
            endTime?: string;
            /** @description 站点ID */
            id?: string;
            /** @description 市县编码 */
            regionCode?: string;
            /**
             * Format: date-time
             * @description 开始时间
             */
            startTime?: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    administrativeBaseByIdUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultAdministrativeBaseDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonByIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultDutyPersonDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonCountUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description year */
                year: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamDutyPersonQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamFloodWindPreventionDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonPointsListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description year */
                year: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListPointListDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonReportUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["DutyPersonQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description year */
                year: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    emergencySuppliesListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description tableRowId */
                tableRowId: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListEmergencySuppliesDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    exportListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamExpertInformationQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamExpertInformationDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    hazTypeStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    hazYearStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamFloodWindPreventionQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamFloodWindPreventionDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    numberStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["CityStatisticQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    reportUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["FloodWindPreventionQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirCityStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirCountUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirDetailUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultSfReservoirDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirGeometryUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfOverReHourStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamSfReservoirQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSfReservoirDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirPlanByCodeUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description reservoirCode */
                reservoirCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultSfReservoirPlanDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirPointsListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSfReservoirPointsDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    sfReservoirReservePlanDownloadUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description reservoirCode */
                reservoirCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonByIdUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFireDutyPersonDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonDownloadUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ForestFireDutyPersonQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonListPageUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamForestFireDutyPersonQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamForestFireDutyPersonDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dutyPersonPointsListUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListPointListDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fireByIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFireDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fireCityStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description year */
                year: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fireDownloadUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ForestFireQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fireListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamForestFireQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamForestFireDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    firePointsListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListPointListDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkDayReportUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ForestFirePointsDayReportDTOReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkPointsByIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultNetworkForestFirePointsDetailDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkPointsDayStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description statisticDate */
                statisticDate: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFirePointsDayStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkPointsFieldStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fieldKey */
                fieldKey: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkPointsListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListPointListDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkPointsListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamTYForestFirePointsQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamNetworkForestFirePointsDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkSimpleReportDataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ForestFireSimpleReportFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFirePointsDayReportDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    networkSimpleReportUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ForestFirePointsDayReportDTOReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tyPointsDayStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description statisticDate */
                statisticDate: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFirePointsDayStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tyPointsFieldStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fieldKey */
                fieldKey: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tyPointsListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TYForestFirePointsQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTYForestFirePointsDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tyPointsListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamTYForestFirePointsQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamTYForestFirePointsDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    helicopterListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestFireProtHelicopter"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    helicopterReservoirListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description heliportId */
                heliportId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestFireProtHelicopterReservoir"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    heliportListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestFireProtHeliportDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    reservoirListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestFireProtReservoir"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    cityMapInfoListUsingPOST: {
        parameters: {
            query?: {
                /** @description 市县名称 */
                cityName?: string;
                /** @description 地图名称 */
                name?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListCityMapInfoDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    waveFrequencyListUsingGET: {
        parameters: {
            query?: {
                /** @description type */
                type?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListwaveFrequencyDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    grib2DataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["Grib2QueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultGrib2DTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    grib2DataTimeListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description code */
                code: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    radarListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamCloudChartPageQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamRadarDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    rainfallCityStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["RainfallQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    rainfallDetailUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["RainfallQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultRainfallDetailDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    rainfallListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["RainfallQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListRainfallDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    satelliteListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamCloudChartPageQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSatelliteDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    spccListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamCloudChartPageQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSpccDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tideForecast30DayUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description tideId */
                tideId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTideDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tideListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamTideQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamTideDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tidePointsListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTiePointsListDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    typhoonDetailListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description typhoonNo */
                typhoonNo: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTyphoonDetailDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    typhoonListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TyphoonQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTyphoonDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    windCityStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WindQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    windDetailUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WindQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultRainfallDetailDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    windListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WindQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListWindDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    windPageListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamWindQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamWindDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    disasterTypeListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamDisasterWarnQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamDisasterWarnDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    disasterTypeStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["DisasterWarnQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    firePreventionStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forestFireStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestFireStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    oceanGeometryUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTextValueDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    oceanListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["OceanTypeQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListOceanDataListDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    oceanListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamOceanTypeQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamOceanDataListDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    oceanTypeStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["OceanTypeQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    weatherCityStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    weatherEventTypeStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WeatherInfoQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    weatherListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamWeatherInfoQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamWeatherInfoDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    weatherSeriousStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WeatherInfoQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
}
