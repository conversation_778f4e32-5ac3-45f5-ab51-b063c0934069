<!-- 通行度分析 -->
<script lang="ts" setup>
import type { AllGeoJSON } from '@turf/turf';
import type { PolygonHierarchy } from 'cesium';
import type { Polygon } from 'geojson';
import { getCoord, shortestPath } from '@turf/turf';
import { cartesianToWgs84, CzPlotEntity, CzPlotScheme, PRESET_PLOT_SCHEMES_OPTIONS } from '@x3d/all';
import { useCzDataSource, useCzEntity, useCzEntityCollection, useCzViewer } from '@x3d/vue-hooks';
import { Color, GeoJsonDataSource } from 'cesium';

defineOptions({ name: 'PassableAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});

const startEntity = shallowRef<CzPlotEntity>();
useCzEntity(startEntity);

function plotStart() {
  startEntity.value = new CzPlotEntity({
    scheme: {
      type: 'Point',
      altitude: {
        visible: false,
      },
    },
    point: {
      pixelSize: 10,
      color: Color.AQUA,
      outlineWidth: 2,
      outlineColor: Color.WHITE.withAlpha(0.4),
    },
    label: {
      text: '起点',
      pixelOffset: new Cesium.Cartesian2(0, -25),
      font: '16px sans-serif',
    },
  },
  );
}

const endEntity = shallowRef<CzPlotEntity>();
useCzEntity(endEntity);

function plotEnd() {
  endEntity.value = new CzPlotEntity({
    scheme: {
      type: 'Point',
      altitude: {
        visible: false,
      },
    },
    point: {
      pixelSize: 10,
      color: Color.YELLOW,
      outlineWidth: 2,
      outlineColor: Color.WHITE.withAlpha(0.4),
    },
    label: {
      text: '终点',
      pixelOffset: new Cesium.Cartesian2(0, -25),
      font: '16px sans-serif',
    },
  },
  );
}

const obstaclesCollection = useCzEntityCollection();

function plotObstacles() {
  obstaclesCollection.add(
    new CzPlotEntity({
      scheme: {
        type: 'Polygon',
        altitude: {
          visible: false,
        },
      },
      polygon: {
        material: Color.RED.withAlpha(0.4),
        heightReference: Cesium.HeightReference.CLAMP_TO_TERRAIN,
      },
    }),
  );
}
const viewer = useCzViewer();

const path = shallowRef<AllGeoJSON>();

useCzDataSource(() => {
  if (path.value) {
    return GeoJsonDataSource.load(path.value, {
      clampToGround: true,
      fill: Cesium.Color.GREEN,

    });
  }
});

function trigger() {
  const currentTime = viewer.value?.clock.currentTime;

  const start = startEntity.value?.position?.getValue(currentTime);
  const end = endEntity.value?.position?.getValue(currentTime);
  if (!start || !end) {
    ElMessage.error('请绘制起点和终点');
    return;
  }

  const features: any[] = [];
  // 障碍缓冲区
  obstaclesCollection.scope.forEach((entity) => {
    const polygon: PolygonHierarchy = entity.polygon?.hierarchy?.getValue(currentTime);
    if (polygon) {
      const positions = polygon.positions.map(item => turf.getCoord(cartesianToWgs84(item) as any));
      features.push(
        turf.buffer(
          turf.polygon([[...positions, positions[0]]]),
          50,
          { units: 'meters' },
        ),
      );
    }
  });

  if (!features.length) {
    ElMessage.error('请绘制障碍');
    return;
  }
  const startCoord = getCoord(cartesianToWgs84(start) as any);
  const endCoord = getCoord(cartesianToWgs84(end) as any);

  // 起终点直线
  const straightLine = turf.lineString([startCoord, endCoord]);
  const crosses = features.some(feature => turf.booleanCrosses(straightLine, feature));

  // 起终点直线与障碍面均不相交，则直接返回直线
  if (!crosses) {
    path.value = straightLine;
    return;
  }

  // 障碍面汇总
  const obstacles = turf.featureCollection<Polygon>(features);
  path.value = shortestPath(startCoord, endCoord, {
    obstacles,
    resolution: 200,
  });
}

function clear() {
  startEntity.value = undefined;
  endEntity.value = undefined;
  obstaclesCollection.removeScope();
  path.value = undefined;
}
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="通行度分析"
    class="w-400px"

    @close="emits('close')"
  >
    <el-divider>绘制要素</el-divider>
    <div m="x-20px y-10px">
      <el-button type="primary" @click="plotStart()">
        绘制起点
      </el-button>
      <el-button type="primary" @click="plotEnd()">
        绘制终点
      </el-button>
    </div>
    <el-divider>绘制障碍</el-divider>
    <div m="x-20px y-10px">
      <!-- <el-button type="primary" @click="plotObstacles('Point')">
        绘制障碍点
      </el-button>
      <el-button type="primary" @click="plotObstacles('Polyline')">
        绘制障碍线
      </el-button> -->
      <el-button type="primary" @click="plotObstacles()">
        绘制障碍面
      </el-button>
    </div>
    <template #footer>
      <el-button class="primary" @click="trigger()">
        计算
      </el-button>
      <el-button class="plain-#FF6363 px-26px!" @click="clear()">
        清除
      </el-button>
    </template>
  </drag-card>
</template>
