/**
 * polygon-tail-straight-arrow 标绘配置 燕尾直箭头
 */

import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import { tailStraightArrow } from '@/lib/@geovis3d/geometry';
import * as Cesium from 'cesium';

export default <PlottingControllerOptions>{
  type: 'polygon-tail-straight-arrow',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
  location: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    if (positions.length < 2) {
      entity._cache = undefined;
      return;
    }
    const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);
    const coordinates = tailStraightArrow(coords).geometry.coordinates;
    entity._cache = new Cesium.PolygonHierarchy(
      coordinates.map(e => wgs84ToCartesian([...e, 0])),
    );
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
