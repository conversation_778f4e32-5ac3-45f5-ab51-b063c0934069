<!-- 图层样式编辑 -->
<script lang="ts" setup>
import { useCzScene } from '@x3d/vue-hooks';

defineOptions({ name: 'LayerTheme' });
const props = defineProps<{ modelValue: boolean }>();
const showAlpha = useVModel(props, 'modelValue');
const scene = useCzScene();
const originalTheme = ref({
  brightness: Cesium.ImageryLayer.DEFAULT_BRIGHTNESS,
  contrast: Cesium.ImageryLayer.DEFAULT_CONTRAST,
  hue: Cesium.ImageryLayer.DEFAULT_HUE,
  saturation: Cesium.ImageryLayer.DEFAULT_SATURATION,
  gamma: Cesium.ImageryLayer.DEFAULT_GAMMA,
  alpha: 1,
  colorToAlpha: 'rgba(0,0,0,0)',
  colorToAlphaThreshold: 1,
  translucency: false,
  backFaceAlpha: 1,
});
const { cloned: layerTheme, sync } = useCloned(originalTheme);
watchDeep(layerTheme, (opt) => {
  for (let i = 0; i < scene.value.imageryLayers.length; i++) {
    const layer = scene.value.imageryLayers.get(i);
    layer.brightness = opt.brightness;
    layer.contrast = opt.contrast;
    layer.hue = opt.hue;
    layer.saturation = opt.saturation;
    layer.gamma = opt.gamma;
    layer.alpha = opt.alpha;
    // layer.colorToAlpha = Cesium.Color.fromCssColorString(opt.colorToAlpha);
    // layer.colorToAlphaThreshold = opt.colorToAlphaThreshold;
  }
  scene.value.globe.translucency.enabled = opt.translucency;
  scene.value.globe.translucency.frontFaceAlpha = opt.backFaceAlpha;
});
</script>

<template>
  <el-popover
    placement="top"
    trigger="click"
    :teleported="false"
    :show-arrow="false"
    :visible="showAlpha"
    popper-style="width: 350px; height: auto; pointer-events:auto"
  >
    <template #reference>
      <slot />
    </template>
    <div class="flex flex-col justify-center px-20px py-15px">
      <header-title1 px="0!" pt="0!">
        图层样式编辑
        <template #extra>
          <el-button type="default" size="small" @click="sync">
            恢复默认
          </el-button>
        </template>
      </header-title1>
      <el-form label-position="top">
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="透明度">
              <el-slider
                v-model="layerTheme.alpha"
                :max="1"
                :min="0"
                :step="0.1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="亮度">
              <el-slider
                v-model="layerTheme.brightness"
                :max="1"
                :min="0"
                :step="0.1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对比度">
              <el-slider
                v-model="layerTheme.contrast"
                :max="1"
                :min="0"
                :step="0.1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="色彩">
              <el-slider
                v-model="layerTheme.hue"
                :max="3"
                :min="0"
                :step="0.1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="饱和度">
              <el-slider
                v-model="layerTheme.saturation"
                :max="3"
                :min="0"
                :step="0.1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="伽马值">
              <el-slider
                v-model="layerTheme.gamma"
                :max="3"
                :min="0"
                :step="0.1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否开启地表透明">
              <el-switch
                v-model="layerTheme.translucency"
                :active-value="true"
                :inactive-value="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地表透明度">
              <el-slider
                v-model="layerTheme.backFaceAlpha"
                :max="1"
                :min="0"
                :step="0.1"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </el-popover>
</template>
