import type { Coord, Feature, LineString } from '@turf/turf';

import { getCoord, lineString } from '@turf/turf';
import { getBaseLength, getThirdPoint, HALF_PI } from './common';

export interface FineArrowOptions {
  tailWidthFactor: number;
  neckWidthFactor: number;
  headWidthFactor: number;
  headAngle: number;
  neckAngle: number;
}
/**
 * 尖直箭头 两个控制点
 * @param points
 * @param options
 */
export function sharpStraightArrow(
  points: Coord[],
  options?: FineArrowOptions,
): Feature<LineString> {
  if (points.length < 2) {
    throw new Error('points.length must >=2');
  }
  const tailWidthFactor = options?.tailWidthFactor ?? 0.1;
  const neckWidthFactor = options?.neckWidthFactor ?? 0.2;
  const headWidthFactor = options?.headWidthFactor ?? 0.25;
  const headAngle = options?.headAngle ?? Math.PI / 8.5;
  const neckAngle = options?.neckAngle ?? Math.PI / 13;

  const coord1 = getCoord(points[0]);
  const coord2 = getCoord(points[1]);

  const len = getBaseLength(points);
  const tailWidth = len * tailWidthFactor;
  const neckWidth = len * neckWidthFactor;
  const headWidth = len * headWidthFactor;
  const tailLeft = getThirdPoint(coord2, coord1, HALF_PI, tailWidth, true).geometry.coordinates;
  const tailRight = getThirdPoint(coord2, coord1, HALF_PI, tailWidth, false).geometry.coordinates;
  const headLeft = getThirdPoint(coord1, coord2, headAngle, headWidth, false).geometry.coordinates;
  const headRight = getThirdPoint(coord1, coord2, headAngle, headWidth, true).geometry.coordinates;
  const neckLeft = getThirdPoint(coord1, coord2, neckAngle, neckWidth, false).geometry.coordinates;
  const neckRight = getThirdPoint(coord1, coord2, neckAngle, neckWidth, true).geometry.coordinates;
  const pList = [tailLeft, neckLeft, headLeft, coord2, headRight, neckRight, tailRight];
  return lineString(pList);
}
