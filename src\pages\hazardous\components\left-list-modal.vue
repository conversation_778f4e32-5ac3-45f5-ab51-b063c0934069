<!-- 危化企业列表上球 -->
<script lang="ts" setup>
import { productionHazardousChemicalsCompanyListUsingPost } from '@/genapi/production';
import { computedLoading } from '@/hooks/computed-loading';
import { CzEntity } from '@x3d/all';
import { useCzEntityCollection } from '@x3d/vue-hooks';
import $3DTiles from '../3dtiles';
import $危化企业png from '../assets/危化企业.png';
import {
  COMPANY_RISK_RANK_ENUM,
  COMPANY_RISK_RANK_ENUM_COLORS,
  COMPANY_TYPE_ENUM,
} from '../enum';
import { useCompanyActiveInject } from '../hooks';

defineOptions({ name: 'LeftListModal' });

const currentId = ref<string>();

const [points] = computedLoading(async () => {
  const { data } = await productionHazardousChemicalsCompanyListUsingPost({ data: {} });
  return data;
});

const companyActive = useCompanyActiveInject();

const entityEffect = useCzEntityCollection();

// 上球
watch(points, (points) => {
  entityEffect.removeScope();
  currentId.value = undefined;
  points?.forEach((item) => {
    const { latitude, longitude } = item;
    if (!latitude || !longitude)
      return;
    const entity = new CzEntity({
      position: Cesium.Cartesian3.fromDegrees(+longitude, +latitude, 0),
      billboard: {
        image: $危化企业png,
        width: 20,
        height: 20,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      },
    });
    entity.event.on('LEFT_CLICK', () => {
      currentId.value = item.id;
    });
    entityEffect.add(entity);
  });
});

// 当前选中的项
const data = computed(() => {
  if (!currentId.value || !points.value?.length)
    return;
  return points.value.find(e => e.id === currentId.value);
});

const position = computed(() => {
  const c = data.value;
  if (!c || !c.longitude || !c.latitude)
    return;
  return Cesium.Cartesian3.fromDegrees(+c.longitude, +c.latitude, 0);
});
</script>

<template>
  <LocatedPopper1
    v-if="position"
    :position="position"
    show-close
    :header="data?.companyName"
    class="!h-auto !w-680px"
    @close=" currentId = undefined"
  >
    <el-descriptions :column="2" border>
      <el-descriptions-item label="企业类型" align="center" :label-class-name="$style.label">
        {{ COMPANY_TYPE_ENUM[data?.companyType!] || '其他' }}
      </el-descriptions-item>
      <!-- <el-descriptions-item label="重大危险源等级" align="center" :label-class-name="$style.label">
        {{ HAZARD_RANK_ENUM[data?.companyType!] || '其他' }}
      </el-descriptions-item> -->
      <el-descriptions-item label="职工人数" align="center" :label-class-name="$style.label">
        {{ data?.peopleEmployee }}
      </el-descriptions-item>
      <el-descriptions-item label="动态风险等级" align="center" :label-class-name="$style.label">
        <span :style="{ color: COMPANY_RISK_RANK_ENUM_COLORS[data?.companyRiskRank!] }">
          {{ COMPANY_RISK_RANK_ENUM[data?.companyRiskRank!] || '离线' }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="行政区划" align="center" :label-class-name="$style.label">
        {{ data?.addressRegistry }}
      </el-descriptions-item>
    </el-descriptions>
    <div class="flex justify-center px-20px pt-20px">
      <el-button
        size="small"
        type="primary"
        class="button"
        @click="companyActive.detailCompanyCode = data?.companyCode"
      >
        <template #icon>
          <el-icon class="i-tabler:file-text" />
        </template>
        <span>详情</span>
      </el-button>
      <el-button
        size="small"
        type="primary"
        @click="companyActive.twoCompanyCode = data?.companyCode"
      >
        <template #icon>
          <el-icon class="i-tabler:hexagon-2-filled" />
        </template>
        <span>二维</span>
      </el-button>
      <el-button
        v-if="$3DTiles.find((e) => e.companyCode === data?.companyCode)?.tilesets?.length"
        size="small"
        type="primary"
        @click="companyActive.threeCompanyCode = data?.companyCode"
      >
        <template #icon>
          <el-icon class="i-tabler:hexagon-3-filled" />
        </template>
        <span>三维</span>
      </el-button>
    </div>
  </LocatedPopper1>
</template>

<style module>
  .label {
  white-space: nowrap;
}

.select-button5 {
  margin-right: 20px !important;
}
</style>
