import type * as Cesium from 'cesium';
import type { CesiumVectorEventCollection } from '../event';

import {
  CesiumScreenEventCollection,
  globalPrimitiveEventRegister,
  primitiveAppendRegister,
} from '../event';
import { Plotting } from '../plotting';
import { vectorMountedExtended } from '../vector/vector-mounted-extended';

declare module 'cesium' {
  export interface Viewer {
    readonly geovis3d: Geovis3d;
  }

  export interface Scene {
    readonly geovis3d: Geovis3d;
  }
}

function defineGeovis3d(viewer: Cesium.Viewer, geovis3d: Geovis3d) {
  [viewer, viewer.scene].forEach((data) => {
    Object.defineProperty(data, 'geovis3d', {
      get: () => geovis3d,
    });
  });
}

/**
 * Geovis3d拓展
 */
export class Geovis3d {
  /**
   * 注册拓展
   * @param viewer
   */
  static register(viewer: Cesium.Viewer): Geovis3d {
    if (viewer.geovis3d) {
      return viewer.geovis3d;
    }
    else {
      vectorMountedExtended(viewer);
      return new Geovis3d(viewer);
    }
  }

  /**
   * @internal
   */
  private constructor(viewer: Cesium.Viewer) {
    this._viewer = viewer;
    defineGeovis3d(viewer, this);
    this._screenEvent = new CesiumScreenEventCollection(viewer.scene);
    this._vectorEvent = globalPrimitiveEventRegister(viewer.scene, this._screenEvent);
    primitiveAppendRegister(this._vectorEvent);
    this._plotting = new Plotting(this);
  }

  /**
   * @internal
   */
  private _viewer: Cesium.Viewer;

  get viewer(): Cesium.Viewer {
    return this._viewer;
  }

  /**
   * @internal
   */
  private _screenEvent: CesiumScreenEventCollection;

  /**
   * 屏幕全局事件
   */
  get screenEvent(): CesiumScreenEventCollection {
    return this._screenEvent;
  }

  /**
   * @internal
   */
  private _vectorEvent: CesiumVectorEventCollection;

  /**
   * 具有primitive属性的全局事件
   */
  get vectorEvent(): CesiumVectorEventCollection {
    return this._vectorEvent;
  }

  extended(extendedFn: (geovis3d: Geovis3d) => void) {
    extendedFn(this);
  }

  /**
   * @internal
   */
  private _plotting: Plotting;

  /**
   * 标绘模块
   */
  get plotting(): Plotting {
    return this._plotting;
  }
}
