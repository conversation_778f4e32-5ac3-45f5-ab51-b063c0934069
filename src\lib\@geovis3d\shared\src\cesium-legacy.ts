import * as Cesium from 'cesium';

const version: string = (Cesium as any).VERSION;

/**
 *  输入对比方法及版本号 判断当前Cesium版本是否符合
 */
function comparison(judge: '<' | '>' | '=', targetVersion: string) {
  const currents = version.split('.').map(e => Number(e) || 0);
  const targets = targetVersion.split('.').map(e => Number(e) || 0);
  if (judge === '=') {
    return !targets.map((e, i) => e == (currents[i] || 0)).includes(false);
  }
  else if (judge === '<' || judge === '>') {
    let res = false;
    for (const [i, target] of targets.entries()) {
      const current = currents[i] || 0;
      if ((judge === '<' && current < target) || (judge === '>' && current > target)) {
        res = true;
        break;
      }
    }
    return res;
  }
  return false;
}

/**
 * 解决Cesium不同版本的适配器，根据输入的版本号及不同版本使用的数据进行判断返回
 * @param version 目标版本号
 * @param lessValue 当前Cesium版本号小于当前版本号时使用的值
 * @param greaterValue 当前Cesium版本号大于等于当前版本号时使用的值
 */
export function cesiumVersionLegacyValue<T = any>(
  version: string,
  lessValue: T,
  greaterValue: T,
): T {
  return comparison('<', version) ? lessValue : greaterValue;
}
