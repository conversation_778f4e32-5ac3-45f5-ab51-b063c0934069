import path from 'node:path';
import process from 'node:process';
import { fileURLToPath } from 'node:url';
import Legacy from '@vitejs/plugin-legacy';
import Vue from '@vitejs/plugin-vue';
import VueJSX from '@vitejs/plugin-vue-jsx';
import UnoCSS from 'unocss/vite';
import AutoImport from 'unplugin-auto-import/vite';
import UnpluginCesium from 'unplugin-cesium/vite';
import UnpluginElementPlus from 'unplugin-element-plus/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Icons from 'unplugin-icons/vite';
import {
  ElementPlusResolver,
  VueUseComponentsResolver,
} from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { VueRouterAutoImports } from 'unplugin-vue-router';
import VueRouter from 'unplugin-vue-router/vite';
import { defineConfig, loadEnv } from 'vite';

import { generateIconCollection } from './internals/svg-icon';
import { createProxy } from './vite.proxy';

// 自定义图标路径
const CUSTOM_ICON_PATH = fileURLToPath(new URL('./icons', import.meta.url));

// https://vitejs.dev/config/
export default defineConfig((config) => {
  const env = loadEnv(config.mode, process.cwd());

  const CustomIconsResolver = IconsResolver({
    prefix: 'i',
    customCollections: ['custom'],
  });

  return {
    resolve: {
      alias: {
        '@': path.resolve('./src'),
      },
    },
    base: env.VITE_PUBLIC_PATH,
    server: {
      port: 3000,
      proxy: createProxy(config),
    },
    build: {
      minify: 'terser',
      terserOptions: {
        format: {
          // 删除所有注释
          comments: false,
        },
      },
    },
    plugins: [
      VueRouter({
        extensions: ['.page.vue', '.layout.vue'],
        exclude: [
          ...(env.VITE_ENV !== 'HAINAN'
            // 海南部署特有页面
            ? [
                ...(env.VITE_ENV === 'SICHUAN' ? [] : ['src/pages/fire-factor/**']),
                // 'src/pages/fire-factor/**',
                'src/pages/hazardous/**',
                'src/pages/forest-fire/**',
                'src/pages/radar/**',
              ]
            : []),
          ...(+env.VITE_EXCLUDE_PAGE_CONVERT
            ? [
                'src/pages/convert/**',
              ]
            : []),
        ],
      }),
      Vue(),
      VueJSX(),
      UnoCSS(),
      UnpluginCesium({
        base: env.VITE_PUBLIC_PATH,
      }),
      Icons({
        autoInstall: true,
        customCollections: {
          custom: generateIconCollection(CUSTOM_ICON_PATH, {
            multiColor: true,
            varPrefix: 'custom',
          }),
        },
      }),
      AutoImport({
        resolvers: [
          ElementPlusResolver(),
          CustomIconsResolver,
        ],
        imports: [
          'vue',
          'pinia',
          // https://uvr.esm.is/introduction.html#auto-imports
          // unplugin-vue-router替代'vue-router',
          // 'vue-router',
          VueRouterAutoImports,
          '@vueuse/core',
          {
            'dayjs': [['default', 'dayjs']],
            'axios': [['default', 'axios']],
            'echarts': [['*', 'echarts']],
            '@turf/turf': [['*', 'turf']],
            'cesium': [['*', 'Cesium']],
            '@/genapi/cimapi': [['*', 'cimApi']],

          },
        ],
      }),
      // 组件自动引入
      Components({
        dirs: ['src/components', 'src/layout'],
        resolvers: [
          VueUseComponentsResolver(),
          ElementPlusResolver(),
          (name) => {
            if (name === 'VueEcharts' || name === 'VEcharts') {
              return {
                name: 'default',
                from: 'vue-echarts',
              };
            }
          },
        ],
      }),
      UnpluginElementPlus({}),
      Legacy({
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        modernPolyfills: true,
      }),
    ],
  };
});
