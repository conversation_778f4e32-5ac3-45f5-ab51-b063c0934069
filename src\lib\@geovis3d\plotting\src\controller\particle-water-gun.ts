import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84 } from '@/lib/@geovis3d/coordinate';
import { ParticleGraphics } from '@/lib/@geovis3d/core';
import { bezierFromOD } from '@/lib/@geovis3d/geometry';
import * as Cesium from 'cesium';

import { positionUpdate } from './utils/position-update';

function waterGunUpdateCallback(graphics: ParticleGraphics, p: Cesium.Particle, dt: number) {
  const { normalizedAge, life } = p;
  let index = Math.floor((normalizedAge + dt / life) * 10);
  index ||= 1;
  const coordinates: Cesium.Cartesian3[] = graphics._ODline;
  const distance = graphics._ODdistance;

  if (dt != 0 && coordinates && distance) {
    const current = coordinates?.[index];
    const prev = coordinates?.[index - 1];
    if (!current || !prev) {
      return;
    }
    const subtract = Cesium.Cartesian3.subtract(current, prev, new Cesium.Cartesian3());
    const ratio = dt / life;
    const speed = distance * ratio;
    const magnitude = Cesium.Cartesian3.magnitude(subtract) / 2;
    p.velocity = Cesium.Cartesian3.multiplyByScalar(subtract, magnitude / speed, subtract);
  }
}

/**
 * particle标绘配置 水枪
 */
export default <PlottingControllerOptions>{
  type: 'particle-water-gun',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
  location: { visible: true },
  altitude: { visible: true },
  control: { visible: true },
  scale: { visible: true },
  scaleCallback(entity, scale) {
    const prev = entity.particle!.endScale?.getValue(Cesium.JulianDate.now()) ?? 1;
    entity.particle!.endScale = new Cesium.ConstantProperty(prev * scale);
  },
  update(entity) {
    if (!entity.particle) {
      entity.particle = new ParticleGraphics({});
    }
    if (!entity.particle.updateEvent?._listeners?.includes(waterGunUpdateCallback)) {
      entity.particle.updateEvent.addEventListener(waterGunUpdateCallback);
    }
    // 粒子影响定义态点位的高度获取   定义态结束后再显示粒子系统
    entity.particle!.show = new Cesium.ConstantProperty(!entity.plotting.defining);
    positionUpdate(entity, 'start');

    const coordinates = entity.plotting.coordinates.getValue().map(e => e.position);
    if (coordinates.length < 2) {
      return;
    }

    const coord0: any = cartesianToWgs84(coordinates[0]);
    const coord1: any = cartesianToWgs84(coordinates[1]);
    const distance = Cesium.Cartesian3.distance(coordinates[0], coordinates[1]);
    const height = distance / 3 + (coord0[2] + coord1[2]) / 2;
    const od = bezierFromOD(coord0, coord1, height, 10).map(e =>
      Cesium.Cartesian3.fromDegrees(e.x, e.y, e.z),
    );
    entity.particle._ODline = od;
    let distanceCount = 0;
    od.forEach((e, index) => {
      if (index != 0) {
        distanceCount += Cesium.Cartesian3.distance(e, od[index - 1]);
      }
    });
    entity.particle._ODdistance = distanceCount;
  },
  destroy(entity) {
    entity.particle?.updateEvent?.removeEventListener(waterGunUpdateCallback);
  },
};
