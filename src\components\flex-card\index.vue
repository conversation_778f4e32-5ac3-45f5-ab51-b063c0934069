<!-- card1 -->
<script lang="ts" setup>
defineOptions({ name: 'BasicCard' });
withDefaults(defineProps<{ title?: string; showClose?: boolean; showMenu?: boolean }>(), {
  showClose: true,
  showMenu: true,
});
const emits = defineEmits<{
  (event: 'close'): void;
  (event: 'clickMenu'): void;
}>();
defineSlots<{
  default: () => VNode[];
  header?: () => VNode[];
  headerTitle?: () => VNode[];
  headerExtra?: () => VNode[];
  footer?: () => VNode[];
}>();
const cardRef = shallowRef<HTMLDivElement>();
const cardHeaderRef = shallowRef<HTMLDivElement>();
const yRef = shallowRef<HTMLDivElement>();
const xRightRef = shallowRef<HTMLDivElement>();
const xLeftRef = shallowRef<HTMLDivElement>();
const elWidth = ref<number>(400);
const elHeight = ref<number>(720);
const pressed = ref(false);
const inX = ref(false);
const inY = ref(false);
function handleClose() {
  emits('close');
}
const { style } = useDraggable(cardHeaderRef, {
  initialValue: { y: 72 },
});
const { x: rx } = useMouseInElement(xRightRef);
const { x: lx } = useMouseInElement(xLeftRef);
const { y } = useMouseInElement(yRef);
watch(rx, (cur, his) => {
  if (inX.value) {
    elWidth.value = cardRef.value?.clientWidth || 400;
    const change = cur - his;
    elWidth.value += change;
  }
});
watch(lx, (cur, his) => {
  if (inX.value) {
    elWidth.value = cardRef.value?.clientWidth || 400;
    const change = cur - his;
    elWidth.value += change;
  }
});
watch(y, (cur, his) => {
  if (inY.value) {
    elHeight.value = cardRef.value?.clientHeight || 720;
    const change = cur - his;
    elHeight.value += change;
  }
});
useEventListener(xRightRef, 'mousedown', () => {
  inX.value = true;
});
useEventListener(xLeftRef, 'mousedown', () => {
  inX.value = true;
});
useEventListener(yRef, 'mousedown', () => {
  inY.value = true;
});
useEventListener(document, 'mouseup', () => {
  inY.value = false;
  inX.value = false;
});
</script>

<template>
  <div
    ref="cardRef"
    class="flex-card"
    flex="~ col"
    :style="`${style};width:${elWidth}px;height:${elHeight}px`"
  >
    <div ref="cardHeaderRef" cursor="move" select="none">
      <slot name="header">
        <header-title1 b-b="1px! solid #fff/10%">
          <template #default>
            <slot name="headerTitle">
              {{ title }}
            </slot>
          </template>
          <template #extra>
            <slot name="headerExtra">
              <el-button link class="h-25px! p-0!" @click="$emit('clickMenu')">
                <el-icon class="i-material-symbols:more-vert" text="20px!" color="#fff" />
              </el-button>
              <el-button link class="h-25px! p-0!" @click="handleClose">
                <el-icon class="i-material-symbols:close" text="20px! #fff!" />
              </el-button>
            </slot>
          </template>
        </header-title1>
      </slot>
    </div>
    <el-scrollbar flex="1">
      <slot />
    </el-scrollbar>
    <div
      v-if="$slots.footer"
      class="footer-container"
      p="24px"
      flex="~ justify-between"
    >
      <slot name="footer" />
    </div>

    <div
      ref="yRef"
      class="absolute bottom--5px left-0 right-0 h-10px cursor-n-resize"
      @mousedown="pressed = true"
      @mouseup="pressed = true"
    />
    <div
      ref="xRightRef"
      class="absolute bottom-0 left--5px top-0 w-10px cursor-e-resize"
      @mousedown="pressed = true"
      @mouseup="pressed = true"
    />
    <div
      ref="xLeftRef"
      class="absolute bottom-0 right--5px top-0 w-10px cursor-e-resize"
      @mousedown="pressed = true"
      @mouseup="pressed = true"
    />
  </div>
</template>

<style lang="scss" scoped>
.flex-card {
  position: fixed;
  box-sizing: border-box;
  min-width: 400px;
  min-height: 400px;
  background: var(--el-bg-color);
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 8px;
  opacity: 1;
}

.footer-container {
  border-top: 1px solid rgb(255 255 255 / 10%);
  box-shadow: 0 -6px 16px 0 rgb(0 0 0 / 40%);
}
</style>
