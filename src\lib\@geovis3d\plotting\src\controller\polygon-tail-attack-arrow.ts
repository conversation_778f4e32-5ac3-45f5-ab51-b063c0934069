import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import { tailedAttackArrow } from '@/lib/@geovis3d/geometry';
import * as Cesium from 'cesium';

/**
 * polygon-tail-attack-arrow 标绘配置 燕尾攻击箭头
 */
export default <PlottingControllerOptions>{
  type: 'polygon-tail-attack-arrow',
  manualTerminate: entity => entity.plotting.coordinates.getLength() >= 3,
  location: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  // interval: { visible: true },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);

    if (coords.length < 3) {
      entity._cache = undefined;
      return;
    }
    const coordinates = tailedAttackArrow(coords).geometry.coordinates;
    entity._cache = new Cesium.PolygonHierarchy(
      coordinates.map(e => wgs84ToCartesian([...e, 0])),
    );
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
