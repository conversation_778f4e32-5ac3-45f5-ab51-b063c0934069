import type { Plotting<PERSON>ontroller, PlottingControllerConstructorOptions } from '@/lib/@geovis3d/core';

import type { ControlPointSymbolSerializateJSON } from './control-point-symbol';
import type { SampledPointSymbolSerializateJSON } from './sampled-point-symbol';
import { ControlPointSymbolSerializate } from './control-point-symbol';
import { SampledPointSymbolSerializate } from './sampled-point-symbol';

export interface PlottingControllerSerializateJSON {
  type?: string;
  coordinates?: ControlPointSymbolSerializateJSON[];
  sampleds?: SampledPointSymbolSerializateJSON[];
}

export type PlottingControllerKey = keyof PlottingControllerSerializateJSON;

export class PlottingControllerSerializate {
  private constructor() {}

  static toJSON(data?: PlottingController): PlottingControllerSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }

    return {
      type: data.type,
      coordinates: data?.coordinates
        .getValue()
        ?.map(item => ControlPointSymbolSerializate.toJSON(item)!),
      sampleds: data?.sampleds
        .getValue()
        ?.map(item => SampledPointSymbolSerializate.toJSON(item)!),
    };
  }

  static fromJSON(
    json?: PlottingControllerSerializateJSON,
  ): PlottingControllerConstructorOptions | undefined {
    if (!json) {
      return undefined;
    }

    return {
      type: json.type,
      coordinates: json?.coordinates?.map(item => ControlPointSymbolSerializate.fromJSON(item)!),
      sampleds: json?.sampleds?.map(item => SampledPointSymbolSerializate.fromJSON(item)!),
    };
  }
}
