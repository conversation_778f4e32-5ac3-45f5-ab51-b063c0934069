<!-- CornerTypeAttribute -->
<script lang="ts" setup>
import type { CornerTypeSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'CornerTypeAttribute' });

const props = defineProps<{
  modelValue?: CornerTypeSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: CornerTypeSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const options = [
  {
    label: '裁剪',
    value: 'BEVELED',
  },
  {
    label: '交点',
    value: 'MITERED',
  },
  {
    label: '平滑',
    value: 'ROUNDED',
  },
];
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="model" :options="options" :clearable="false" />
  </el-form-item>
</template>
