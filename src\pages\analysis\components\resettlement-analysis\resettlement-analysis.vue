<!-- 安置点选址 -->
<script lang="ts" setup>
import type { Cartesian3 } from 'cesium';
import { toPublicPath } from '@/utils/resolve-path';
import { cartesianToWgs84, CzPlotEntity, CzPlotScheme, PRESET_PLOT_SCHEMES_OPTIONS } from '@x3d/all';
import { useCzDataSource, useCzEntity, useCzViewer } from '@x3d/vue-hooks';
import { Color, GeoJsonDataSource } from 'cesium';

defineOptions({ name: 'ResettlementAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});
const viewer = useCzViewer();

const exampleGeojsonURL = toPublicPath('/示例安置点.geojson');

const resettlementDataSource = shallowRef<GeoJsonDataSource>();
useCzDataSource(() => {
  return resettlementDataSource.value;
});

watchEffect(() => {
  if (resettlementDataSource.value) {
    viewer.value.flyTo(resettlementDataSource.value);
  }
});
function resetResettlement() {
  resettlementDataSource.value?.entities.values.forEach((entity) => {
    if (entity.position) {
      entity.billboard = undefined;
      entity.point = new Cesium.PointGraphics({
        pixelSize: 10,
        color: Color.YELLOW,
        outlineWidth: 2,
        outlineColor: Color.WHITE.withAlpha(0.4),
      });
      entity.label = new Cesium.LabelGraphics({
        font: '16px sans-serif',
        pixelOffset: new Cesium.Cartesian2(0, -25),

      });
    }
  });
}
// 加载示例安置点
async function resetResettlementGeojson() {
  resettlementDataSource.value = await GeoJsonDataSource.load(exampleGeojsonURL);
  resetResettlement();
}

const accidentPointEntity = shallowRef<CzPlotEntity>();
useCzEntity(accidentPointEntity);

function plotAccidentPoint() {
  accidentPointEntity.value = new CzPlotEntity({
    scheme: {
      type: 'Point',
      altitude: {
        visible: false,
      },
    },
    point: {
      pixelSize: 10,
      color: Color.RED,
      outlineWidth: 2,
      outlineColor: Color.WHITE.withAlpha(0.8),
    },
    label: {
      text: '事故点',
      pixelOffset: new Cesium.Cartesian2(0, -25),
      font: '16px sans-serif',
    },
  },
  );
}

const accidentPolygonEntity = shallowRef<CzPlotEntity>();
useCzEntity(accidentPolygonEntity);

function plotAccidentPolygon() {
  accidentPolygonEntity.value = new CzPlotEntity({
    scheme: {
      type: 'Polygon',
      altitude: {
        visible: false,
      },
    },
    polygon: {
      material: Color.RED.withAlpha(0.4),
      heightReference: Cesium.HeightReference.CLAMP_TO_TERRAIN,
    },
  });
}

const endEntity = shallowRef<CzPlotEntity>();
useCzEntity(endEntity);

function trigger() {
  const currentTime = viewer.value.clock.currentTime;
  const areaPositions = accidentPolygonEntity.value?.polygon?.hierarchy?.getValue(currentTime)?.positions.map((position: Cartesian3) => cartesianToWgs84(position));
  const pointCartesian = accidentPointEntity.value?.position?.getValue(currentTime);
  if (!pointCartesian || !areaPositions.length) {
    ElMessage.error('请先绘制事故点与影响范围');
    return;
  }
  if (!resettlementDataSource.value?.entities.values.length) {
    ElMessage.error('请加载安置点集');
    return;
  }
  resetResettlement();
  const point = turf.point(cartesianToWgs84(pointCartesian) as any);
  const area = turf.polygon([[...areaPositions, areaPositions[0]]]);

  const calcs = resettlementDataSource.value?.entities.values.map((entity) => {
    const position = entity.position?.getValue(currentTime);
    if (!position) {
      return {
        id: entity.id,
        invalid: true, // 无效点位
        distance: Number.MAX_SAFE_INTEGER,
      };
    }
    const wgs84 = cartesianToWgs84(position);
    const invalid = turf.booleanPointInPolygon([wgs84[0], wgs84[1]], area);
    return {
      id: entity.id,
      invalid, // 无效点位
      distance: turf.distance(point, turf.point([wgs84[0], wgs84[1]])),
    };
  });
  const effects = calcs.filter(item => !item.invalid);
  if (!effects.length) {
    ElMessage.warning('没有符合条件的安置点位');
    return;
  }

  const data = effects.sort((a, b) => a.distance - b.distance)[0];
  const entity = resettlementDataSource.value?.entities.values.find(item => item.id === data.id);
  if (entity) {
    entity.point!.color = new Cesium.ConstantProperty(Cesium.Color.GREEN);
    entity.label!.text = new Cesium.ConstantProperty('推荐安置点');
  }
}

function clear() {
  accidentPointEntity.value = undefined;
  accidentPolygonEntity.value = undefined;
  resettlementDataSource.value = undefined;
}
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="安置点选址"
    class="w-400px"
    @close="emits('close')"
  >
    <el-divider>安置点集</el-divider>
    <div m="x-20px y-10px">
      <el-button type="primary" @click="resetResettlementGeojson()">
        添加
      </el-button>
      <!--  <el-button type="primary" @click="plotEnd()">
        绘制终点
      </el-button> -->
    </div>
    <el-divider>绘制事故要素</el-divider>
    <div m="x-20px y-10px">
      <el-button type="primary" @click="plotAccidentPoint()">
        绘制事故点
      </el-button>
      <el-button type="primary" @click="plotAccidentPolygon()">
        绘制影响范围
      </el-button>
    </div>
    <template #footer>
      <el-button class="primary" @click="trigger()">
        计算
      </el-button>
      <el-button class="plain-#FF6363 px-26px!" @click="clear()">
        清除
      </el-button>
    </template>
  </drag-card>
</template>
