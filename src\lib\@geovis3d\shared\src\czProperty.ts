import type { JulianDate, Property } from 'cesium';

import { CallbackProperty, ConstantProperty } from 'cesium';
import { isFunction } from './utils';

/**
 * 是否是Cesium.Property
 * @param val
 */
export function isCzProperty<T extends Property = Property>(val: any): val is T {
  return val && isFunction(val?.getValue);
}

/**
 * 将可能是Property的值转换成目标值
 * ```typescript
 * toCzPropertyValue('val') //=> 'val'
 * toCzPropertyValue(new ConstantProperty('val')) //=> 'val'
 * toCzPropertyValue(new CallbackProperty(()=>'val')) //=> 'val'
 * ```
 * @param val
 */
export function toCzPropertyValue(val: any, time?: JulianDate): any {
  return isCzProperty(val) ? val.getValue(time) : val;
}

export type PropertyCallback<T = any> = (time: JulianDate, result?: T) => T;

/**
 * 将可能是Property的值转换成目标值
 * ```typescript
 * toCzProperty('val') //=> new ConstantProperty('val')
 * toCzProperty(()=>'val',isConstant) //=> new CallbackProperty(val, isConstant)
 * toCzProperty(new AnyProperty()) //=> new AnyProperty()
 * ```
 * @param val
 * @param isConstant
 */
export function toCzProperty<T>(
  val: T | PropertyCallback<T> | Property,
  isConstant = false,
): Property {
  return isCzProperty(val)
    ? val
    : isFunction(val)
      ? new CallbackProperty(val, isConstant)
      : new ConstantProperty(val);
}
