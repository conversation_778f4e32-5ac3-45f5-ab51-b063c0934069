import type { JulianDate } from 'cesium';

import { isFunction } from '@/lib/@geovis3d/core';

export function getMaybePropertyValue<T extends Record<string, any>, Omit extends string>(
  data: T,
  time: JulianDate,
  omit?: Omit[],
) {
  return <K extends Omit>(key: K): any | undefined => {
    const maybeProperty = omit?.includes(key) ? undefined : data?.[key];
    if (isFunction(maybeProperty?.getValue)) {
      return maybeProperty?.getValue('time');
    }
    else {
      return maybeProperty;
    }
  };
}

export function getSerializateJsonValue<JSON extends Record<string, any>, Omit extends string>(
  json: JSON,
  omit?: Omit[],
) {
  return <K extends Omit>(key: K): JSON[K] | undefined => {
    return omit?.includes(key) ? undefined : json?.[key];
  };
}
