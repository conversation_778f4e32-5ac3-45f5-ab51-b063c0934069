<!-- 功能面板 -->
<script lang="ts" setup>
import PlottingFunction from './plotting-function.vue';
import PlottingLayerList from './plotting-layer-list.vue';
import { usePlotInjectState } from './state';

export interface PlottingPresetPanelProps {
  modelValue?: any;
}

defineOptions({ name: 'PlottingPresetPanel' });

defineProps<PlottingPresetPanelProps>();

const { currentLayer, saveLayer } = usePlotInjectState()!;

async function onBack() {
  await ElMessageBox.confirm(
    `将"${currentLayer.value?.remark}"保存到云端。`,
    '是否保存？',
    {
      type: 'info',
      distinguishCancelAndClose: true,
      cancelButtonText: '不保存',
      confirmButtonText: '保存',
      callback: async (value: string) => {
        switch (value) {
          case 'cancel':
            currentLayer.value = undefined;
            break;
          case 'confirm':
            await saveLayer();
            currentLayer.value = undefined;
            break;

          default:
            break;
        }
      },
    },
  );
}
</script>

<template>
  <header-title1 b-b="1px! solid #fff/10%!">
    <template v-if="!currentLayer">
      <span class="icon icon-plot" />
      <span>标绘</span>
    </template>
    <template v-else>
      <span>{{ currentLayer.remark }}</span>
    </template>
    <template #extra>
      <el-button v-if="!currentLayer" link @click="$router.push({ path: '/home' })">
        <el-icon class="i-material-symbols:close" text="20px! #FFF!" />
      </el-button>
      <el-button v-else link @click="onBack">
        <el-icon class="i-material-symbols:close" text="20px! #FFF!" />
      </el-button>
    </template>
  </header-title1>

  <PlottingLayerList v-if="!currentLayer" />
  <PlottingFunction v-else />
</template>

<style module lang="scss">
.plotting-preset-panel {
  z-index: 1000;
  max-height: calc(100vh - 80px);
  background: url('./assets/background.svg');
  background-size: contain;
}
</style>

<style lang="scss" scoped>
.icon {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  margin: 0 0.2rem;
  background-size: 100% 100%;
}

.icon-plot {
  background-image: url('./assets/plot.svg');
}

.back-icon {
  padding: 2px;
  font-size: 1.3rem;
  cursor: pointer;
}

.close-button {
  cursor: pointer;
}
</style>
