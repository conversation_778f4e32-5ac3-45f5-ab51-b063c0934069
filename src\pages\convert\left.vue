<script lang="ts" setup>
import { computedLoading } from '@/hooks/computed-loading';
import { toStaticFilePath } from '@/utils/resolve-path';
import { cartographicToCartesian } from '@x3d/all';
import { useCzPrimitive, use<PERSON>z<PERSON>iewer } from '@x3d/vue-hooks';
import { RADAR_LIST } from './data';

defineOptions({ name: 'Left' });
const viewer = useCzViewer();
const show = ref(true);

const currentName = ref<string>();

const [data, isLoading] = computedLoading(async () => {
  if (currentName.value) {
    const { data } = await axios.get(toStaticFilePath(`/radar/${currentName.value}`));
    return data.map((e: any) => {
      return {
        ...e,
        x: +e.x,
        y: +e.y,
        s: +e.s || 0,
        v: +e.v || 0,
        position: Cesium.Cartesian3.fromDegrees(+e.x, +e.y, +e.h + 60),

      };
    }) as any[];
  }
  else {
    return [];
  }
});

// useCzDataSource(async () => {
//   if (!points.value?.length) {
//     return;
//   }

//   const dataSource = await ContourDataSource.load({
//     interpolate: {
//       points: featureCollection(points.value ?? []),
//       options: {
//         property: 's',
//         units: 'meters',
//       },
//       cellSize: 10,
//     },
//     surface: turf.geometryCollection([turf.bboxPolygon(turf.bbox(featureCollection(points.value ?? []))).geometry]).geometry,
//     isobands: {
//       breaks: [-4, 3, 2, 1, 0, 1, 2, 3, 4],
//       options: {
//         zProperty: 's',
//         breaksProperties: [
//           { fill: '#e3e3ff' },
//           { fill: '#c6c6ff' },
//           { fill: '#a9aaff' },
//           { fill: '#8e8eff' },
//           { fill: '#7171ff' },
//           { fill: '#5554ff' },
//           { fill: '#3939ff' },
//           { fill: '#1b1cff' },
//         ],
//       },
//     },

//   });

//   console.log(dataSource.entities.values);

//   return dataSource;
// });

const center = computed(() => {
  if (data.value?.length) {
    const center = Cesium.Rectangle.center(Cesium.Rectangle.fromCartesianArray(data.value.map(item => item.position)));
    center.height = 5000;
    return cartographicToCartesian(center);
  }
  return undefined;
});

function getColorByValue(v: number) {
  // 定义颜色范围
  const yellow = Cesium.Color.YELLOW.clone();
  const green = Cesium.Color.GREEN.clone();
  const blue = Cesium.Color.BLUE.clone();

  if (v <= -27) {
    return yellow;
  }

  if (v >= 50) {
    return blue;
  }

  if (v > -27 && v < 27) {
    const t = (v - (-27)) / (27 - (-27));
    return Cesium.Color.lerp(yellow, green, t, new Cesium.Color());
  }

  if (v >= 27 && v < 50) {
    const t = (v - 27) / (50 - 27);
    return Cesium.Color.lerp(green, blue, t, new Cesium.Color());
  }
}
useCzPrimitive(() => {
  const list = data.value ?? [];

  const instances = list
    ?.map((item: any) => {
    // 创建立方体几何体
      const boxGeometry = new Cesium.BoxGeometry({
        vertexFormat: Cesium.VertexFormat.POSITION_AND_NORMAL, // 顶点格式
        minimum: new Cesium.Cartesian3(-2.5, -2.5, -2.5), // 最小点
        maximum: new Cesium.Cartesian3(2.5, 2.5, 2.5), // 最大点
      });

      // index == 0 && console.log();

      // 将几何体转换为实例以添加到场景中
      return new Cesium.GeometryInstance({
        geometry: boxGeometry,
        modelMatrix: Cesium.Transforms.eastNorthUpToFixedFrame(item.position), // 模型矩阵（用于位置和方向）
        attributes: {
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(getColorByValue(item.v)!),
          depthFailColor: Cesium.ColorGeometryInstanceAttribute.fromColor(getColorByValue(item.v)!),
        },
      });
    }) ?? [];

  // 将实例添加到图元中
  const primitive = new Cesium.Primitive({
    geometryInstances: instances,
    appearance: new Cesium.PerInstanceColorAppearance({
      translucent: false, // 是否透明
      flat: true,
      faceForward: false,
    }),
    depthFailAppearance: new Cesium.PerInstanceColorAppearance({
      flat: true,
      faceForward: false,
      translucent: false, // 深度测试失败时的外观,
    }),
  });

  return primitive;
});
watchEffect(() => {
  center.value && viewer.value.scene.camera.flyTo({
    destination: (center.value),
  });
});
</script>

<template>
  <layout-left-panel>
    <div
      flex="~ col"
      h="100%"
      p="b-46px t-64px"
      :w="show ? '400px' : '0'"
      transition="all 300"
      of="hidden"
    >
      <header-title1 b-b="1px! solid #fff/10%!">
        雷达监测
      </header-title1>
      <el-scrollbar class="flex-1 of-hidden">
        <div
          v-for="(item, index) in RADAR_LIST"
          :key="item"
          class="scrollbar-item"
          cursor="pointer"
          flex="~ items-center"
          @click="currentName = item"
        >
          <el-text un-text="16px! #fff!" flex="1" truncated>
            {{ index + 1 }}.{{ item }}
          </el-text>
        </div>
      </el-scrollbar>
    </div>
    <el-button link class="toggle-btn" @click="show = !show">
      <el-icon
        class="i-material-symbols:chevron-left"
        text="20px! #FFF!"
        transition="all 300"
        :rotate="show ? '0' : '180'"
        inline-block
      />
    </el-button>
  </layout-left-panel>
</template>

<style lang="scss" scoped>
.content {
  position: relative;
  height: 100vh;
  pointer-events: auto;
  background: var(--el-bg-color);
}

.toggle-btn {
  position: absolute;
  top: 521px;
  left: 100%;
  z-index: 99;
  width: 24px;
  height: 56px;
  background: var(--el-bg-color) !important;
  border-radius: 0 4px 4px 0 !important;
}

.upload-btn {
  margin: 24px 34px;
  background-color: #4176ff;
  border-radius: 6px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  width: 29.3518vh;
  height: 11.8333vh;
  padding: 5px;
}

.scrollbar-item {
  padding: 12px;
  margin: 10px;
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  border-radius: 4px;
}

.scrollbar-item:hover {
  background: rgb(137 169 255 / 20%);
}
</style>
