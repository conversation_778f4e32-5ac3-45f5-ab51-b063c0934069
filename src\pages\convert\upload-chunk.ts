import { chunkUploadMergeUsingPost, chunkUploadUploadChunkUsingPost } from '@/genapi/cimapi';

export interface UploadChunkOptions {
}

export interface UploadChunkRetrun {
}

export async function uploadChunk(
  options: {
    file: File;
    uuid: any;
    filename: string;
    uploadFilePath: string;
    onUploadProgress: (loaded: number, total: number) => void;
  },
) {
  const { file, uuid, filename, uploadFilePath, onUploadProgress } = options;
  // 定义每个分片的大小为 1MB
  const chunkSize = 1 * 1024 * 1024; // 1MB
  // 计算总分片数
  const totalChunks = Math.ceil(file.size / chunkSize);

  for (let i = 0; i < totalChunks; i++) {
    // 计算当前分片的起始位置
    const start = i * chunkSize;
    // 计算当前分片的结束位置
    const end = Math.min(start + chunkSize, file.size);
    // 创建当前分片的 Blob 对象
    const blob = file.slice(start, end);

    // 创建表单数据对象
    const formData = new FormData();
    // 添加当前分片的文件
    formData.append('file', blob);
    // 添加分片索引
    formData.append('index', i as any);
    // 添加总分片数
    formData.append('hash', uuid);

    try {
      await chunkUploadUploadChunkUsingPost({
        params: {},
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: ({ loaded }: any) => {
          onUploadProgress?.(start + loaded, file.size);
        },
      });
    }
    catch (error) {
      console.error(`Failed to upload chunk ${i}:`, error);
    }
  }
  const res = await chunkUploadMergeUsingPost({
    data: {
      hash: uuid,
      filename,
      total: totalChunks,
      uploadFilePath,

    },
  });
  onUploadProgress?.(file.size, file.size);
  return res;
}
