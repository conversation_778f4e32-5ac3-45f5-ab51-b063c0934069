<script lang="ts" setup>
defineOptions({ name: 'Right' });
const emitter = useEventBus('pointDetail');
const showDetail = ref(false);
const detail = ref<{
  longitude?: string | number;
  latitude?: string | number;
  address?: string;
  name?: string;
}>({});

emitter.on((res: any) => {
  showDetail.value = res.isShow;
  detail.value = res.data.value;
});
</script>

<template>
  <div v-if="showDetail" class="file-right-content">
    <el-card style="max-width: 480px">
      <template #header>
        <div class="card-header">
          <header-title1> {{ detail.name }} </header-title1>
        </div>
      </template>
      <el-descriptions
        title=""
        :column="1"
        border
        :label-width="$vh(10)"
      >
        <el-descriptions-item label="地址">
          {{ detail.address }}
        </el-descriptions-item>
        <el-descriptions-item label="经度">
          {{ Number(detail?.longitude)?.toFixed(6) }}
        </el-descriptions-item>
        <el-descriptions-item label="纬度">
          {{ Number(detail?.latitude)?.toFixed(6) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.file-right-content {
  position: absolute;
  top: 80px;
  right: 20px;
  width: 450px;
  max-height: 300px;
  background: var(--el-bg-color); // #292B2E;
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 6px;
}

::v-deep(.el-card) {
  border: 0 red;
}

::v-deep(.el-card__header) {
  padding: 5px;
}

.header-title1 {
  padding: 5px;
}

::v-deep(.el-descriptions__label) {
  flex: 0 0 80px; /* 固定宽度或占比 */
  width: 90px; /* 保持固定宽度 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis; /* 溢出显示省略号 */
  white-space: nowrap; /* 不换行 */
}

::v-deep(.el-descriptions__content) {
  flex: 1; /* 内容部分自适应 */
}

::v-deep(.el-descriptions__label.el-descriptions__cell.is-bordered-label) {
  background: transparent;
}
</style>
