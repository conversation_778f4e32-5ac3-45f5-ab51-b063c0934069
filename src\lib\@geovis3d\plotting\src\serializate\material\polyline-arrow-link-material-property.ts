import type { MaterialPropertySerializateController } from './material-property';
import { PolylineArrowLinkMaterialProperty } from '@/lib/@geovis3d/material';

import * as Cesium from 'cesium';

import { ColorSerializate } from '../color';

export interface PolylineArrowLinkMaterialPropertySerializateJSON {
  color?: string;
  time?: number;
}

/**
 * PolylineArrowLinkMaterialProperty序列化加入缓存
 * @internal
 */
export default <
  MaterialPropertySerializateController<
    'PolylineArrowLinkMaterialProperty',
    PolylineArrowLinkMaterialProperty,
    PolylineArrowLinkMaterialPropertySerializateJSON
  >
>{
  type: 'PolylineArrowLinkMaterialProperty',
  hit: (property) => {
    return property instanceof PolylineArrowLinkMaterialProperty;
  },
  toJSON(property, time) {
    time ??= Cesium.JulianDate.now();
    const data = property?.getValue(time) ?? {};
    return {
      type: 'PolylineArrowLinkMaterialProperty',
      params: {
        color: ColorSerializate.toJSON(data.color),
        time: data.time,
      },
    };
  },
  fromJSON(json) {
    const { color, time } = json?.params ?? {};
    return new PolylineArrowLinkMaterialProperty({
      color: ColorSerializate.fromJSON(color),
      time,
    });
  },
};
