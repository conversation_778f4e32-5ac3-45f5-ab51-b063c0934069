<!-- StringAttribute -->
<script lang="ts" setup>
import { useVModel } from '@vueuse/core';

defineOptions({ name: 'StringAttribute' });

const props = defineProps<{
  modelValue?: string;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: string): void;
}>();

const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-form-item :label="label">
    <el-input v-model="model" />
  </el-form-item>
</template>
