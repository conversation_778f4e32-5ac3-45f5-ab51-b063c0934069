import * as Cesium from 'cesium';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface Cartesian3SerializateJSON {
  x?: number;
  y?: number;
  z?: number;
}

export type Cartesian3Key = keyof Cartesian3SerializateJSON;

export class Cartesian3Serializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.Cartesian3,
    time?: Cesium.JulianDate,
  ): Cartesian3SerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      x: getValue('x'),
      y: getValue('y'),
      z: getValue('z'),
    };
  }

  static fromJSON(json?: Cartesian3SerializateJSON): Cesium.Cartesian3 | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json);

    return new Cesium.Cartesian3(getValue('x'), getValue('y'), getValue('z'));
  }
}
