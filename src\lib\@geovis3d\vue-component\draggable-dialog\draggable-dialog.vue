<!-- 弹出层组件 -->

<script lang="ts" setup>
import type { VNode } from 'vue-demi';
import { useDraggable, useMounted } from '@vueuse/core';

import { shallowRef, watch, watchEffect } from 'vue-demi';
import { ResolveVnode } from '../resolve-vnode/resolve-vnode';

export interface CommonDialogProps {
  leader?: string | (() => VNode | string);
  title?: string | (() => VNode | string);
  extra?: string | (() => VNode | string);
  draggable?: boolean;
  to?: any;
  x?: number;
  y?: number;
  modelValue?: boolean;
}
defineOptions({ name: 'CommonDialog', inheritAttrs: false });

const props = withDefaults(defineProps<CommonDialogProps>(), {
  draggable: true,
  modelValue: true,
  to: 'body',
  x: 0,
  y: 0,
});

const emit = defineEmits<{
  (event: 'update:x', value: number): void;
  (event: 'update:y', value: number): void;
  (event: 'update:modelValue', value: boolean): void;
}>();

const containerRef = shallowRef<HTMLElement>();

const headerRef = shallowRef<HTMLElement>();

const { style, x, y } = useDraggable(containerRef, {
  handle: headerRef,
});

// 令x，y与props双向绑定
watchEffect(async () => {
  // await nextTick();
  x.value = props.x || 0;
  y.value = props.y || 0;
});

watch([x, y], () => {
  emit('update:x', x.value);
  emit('update:y', y.value);
});
const isMounted = useMounted();

defineExpose({
  elRef: containerRef,
});
</script>

<template>
  <Teleport v-if="isMounted" :to="to">
    <transition name="el-fade-in" v-bind="$attrs">
      <div
        v-if="modelValue"
        ref="containerRef"
        class="common-dialog"
        :style="style"
      >
        <div ref="headerRef" class="header" :class="{ draggable }">
          <div class="leader">
            <slot name="leader">
              <ResolveVnode :render="leader" />
            </slot>
          </div>
          <div class="title">
            <slot name="title">
              <ResolveVnode :render="title" />
            </slot>
          </div>
          <div class="extra">
            <slot name="extra">
              <ResolveVnode :render="extra" />
            </slot>
          </div>
        </div>

        <div class="content">
          <slot />
        </div>
      </div>
    </transition>
  </Teleport>
</template>

<style scoped>
.common-dialog {
  position: absolute;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 300px;
  min-height: 200px;
  padding: 10px 16px;
  overflow: hidden;
  color: #fff;
  touch-action: none;
  background-color: rgb(0 0 0 / 30%);
  background-size: 100% 100%;
}

.header {
  display: flex;
  align-items: center;
  height: 30px;
  overflow: hidden;
}

.header.draggable {
  padding-bottom: 10px;
  cursor: move;
}

.title {
  display: flex;
  flex: 1;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.extra {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.content {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}
</style>
