import * as Cesium from 'cesium';

export class CesiumDataLoader {
  private viewer: Cesium.Viewer;
  private geoJsonDataSource?: Cesium.GeoJsonDataSource;
  private onClickCallback?: (entity: Cesium.Entity) => void; // 回调函数类型定义

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
  }

  public loadGeoJSON(url: string, name: string): void {
    if (this.geoJsonDataSource) {
      this.viewer.dataSources.remove(this.geoJsonDataSource, true);
    }

    Cesium.GeoJsonDataSource.load(url, {
      stroke: Cesium.Color.AQUA,
      fill: Cesium.Color.AQUA.withAlpha(0.1),
    }).then((dataSource) => {
      this.geoJsonDataSource = dataSource;
      dataSource.name = name;
      this.viewer.dataSources.add(this.geoJsonDataSource);
      this.viewer.flyTo(this.geoJsonDataSource);
      this.addClickEventHandler();
    }).catch((error) => {
      console.error('加载数据失败:', error);
    });
  }

  public removeGeoJSON(): void {
    if (this.geoJsonDataSource) {
      this.viewer.dataSources.remove(this.geoJsonDataSource, true);
      this.geoJsonDataSource = undefined;
    }
  }

  // 设置点击事件回调函数
  public setOnClickCallback(callback: (entity: Cesium.Entity) => void): void {
    this.onClickCallback = callback;
  }

  private addClickEventHandler(): void {
    if (!this.geoJsonDataSource)
      return;

    // 获取 screenSpaceEventHandler 实例
    const handler = this.viewer.screenSpaceEventHandler;

    // 添加点击事件监听
    handler.setInputAction((click: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
      const pickedObject = this.viewer.scene.pick(click.position);
      if (Cesium.defined(pickedObject) && Cesium.defined(pickedObject.id)) {
        const entity = pickedObject.id as Cesium.Entity;
        if (this.onClickCallback) {
          this.onClickCallback(entity); // 调用回调函数处理点击事件
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }
}

interface modelOption {
  position: Cesium.Cartesian3;
  modelUrl: string;
  id: string;
}

// 定义一个模型管理类
export class CesiumModelManager {
  private viewer: Cesium.Viewer;
  private dataSourceCollection: Cesium.DataSourceCollection;
  private dataSource: Cesium.CustomDataSource;

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer;
    this.dataSourceCollection = viewer.dataSources;
    this.dataSource = new Cesium.CustomDataSource('modelDataSource');
    this.dataSourceCollection.add(this.dataSource);
  }

  // 加载模型的方法
  public addModel(option: modelOption): void {
    const modelEntity = new Cesium.Entity({
      id: option.id,
      position: option.position,
      model: {
        uri: option.modelUrl,
        minimumPixelSize: 64,
      },
    });
    this.dataSource.entities.add(modelEntity);
    this.viewer.flyTo(modelEntity);
  }

  // 删除模型的方法
  public removeModel(id: string): void {
    const entity = this.dataSource.entities.getById(id);
    if (entity) {
      this.dataSource.entities.remove(entity);
    }
  }

  // 清除所有模型
  public clearAllModels(): void {
    this.dataSource.entities.removeAll();
  }
}
