<!-- NumberArrayAttribute -->
<script lang="ts" setup>
import { ref } from 'vue';

import { useShallowBinding } from './hooks';

defineOptions({ name: 'NumberArrayAttribute' });

const props = defineProps<{
  modelValue?: number[];
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: number[]): void;
}>();

const model = ref<number[]>([]);

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = [...(value ?? [])]),
  rtl: value => emit('update:modelValue', [...(value ?? [])]),
});
</script>

<template>
  <div class="number-array-attribute">
    NumberArrayAttribute
  </div>
</template>
