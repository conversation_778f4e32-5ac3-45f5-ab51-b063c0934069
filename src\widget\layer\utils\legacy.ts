import type { components } from '@/genapi/cimapi';
import type { LayerConfig } from '../assets/types';

export interface LayerInfo {
  id?: string;
  fid?: string;
  layerName?: string;
  dirOrLayer?: 'dir' | 'layer' | string;
  config?: LayerConfig;
  children?: LayerInfo[];
}

export function legacy(data: components['schemas']['LayerInfoVO'][]): LayerInfo[] {
  return data.map((item) => {
    item = {
      ...item,
    };

    const config = JSON.parse(item.config || '{}');

    if (!config.type && item.layerType) {
      config.type = item.layerType;
      item.layerType = undefined;
    }

    // 兼容旧版
    if (!config.name && item.layerName) {
      config.name = item.layerName;
    }

    // bbox兼容旧版
    if (!config.rectangle && item.layerExtend) {
      const layerExtend = item.layerExtend || [];
      let rectangle: any = layerExtend.map(item => Number(item) ?? undefined);
      if (rectangle.every((item: any) => !item)) {
        rectangle = undefined;
      }
      config.rectangle = rectangle;
    }

    // egis兼容旧版
    if (!config.egis && item.layerTypeDetail?.egisUsername) {
      config.egis = {
        username: item.layerTypeDetail?.egisUsername,
        password: item.layerTypeDetail?.egisPassword,
      };
      item.layerTypeDetail.egisUsername = undefined;
      item.layerTypeDetail.egisPassword = undefined;
    }

    // 相机兼容旧版
    if (!config.camera && item.longitude) {
      config.camera = {
        longitude: Number.isNaN(Number(item.longitude)) ? undefined : Number(item.longitude),
        latitude: Number.isNaN(Number(item.latitude)) ? undefined : Number(item.latitude),
        height: Number.isNaN(Number(item.height)) ? undefined : Number(item.height),
        heading: Number.isNaN(Number(item.heading)) ? undefined : Number(item.heading),
        pitch: Number.isNaN(Number(item.pitch)) ? undefined : Number(item.pitch),
        roll: Number.isNaN(Number(item.roll)) ? undefined : Number(item.roll),
      };
      item.longitude = undefined;
      item.latitude = undefined;
      item.height = undefined;
      item.heading = undefined;
      item.pitch = undefined;
      item.roll = undefined;
    }

    // 图例兼容旧版
    if (!config.legend && item.layerTypeDetail?.legend) {
      config.legend = JSON.parse(item.layerTypeDetail?.legend || '{}');
      item.layerTypeDetail.legend = undefined;
    }

    // 初始化显示兼容旧版
    if (config.defaultVisible === undefined && item.layerTypeDetail?.defaultShowLayer) {
      config.defaultVisible = item.layerTypeDetail.defaultShowLayer === 'true';
      item.layerTypeDetail.defaultShowLayer = undefined;
    }

    // 图层配置兼容旧版
    if (!config.layer && item.layerTypeDetail) {
      const customStyle = JSON.parse(item.layerTypeDetail?.customStyle || '{}');
      config.layer = {
        layer: item.layerTypeDetail?.layer,
        url: item.layerTypeDetail?.url,
        minimumLevel: Number(item.layerTypeDetail?.minimumLevel) || undefined,
        maximumLevel: Number(item.layerTypeDetail?.maximumLevel) || undefined,
        format: item.layerTypeDetail?.format,
        style: item.layerTypeDetail?.style,
        titleSize: Number(item.layerTypeDetail?.titleSize) || undefined,
        tileMatrixSetID: item.layerTypeDetail?.tileMatrixSetId as any || undefined,
        lineColor: customStyle.lineColor,
        fillColor: customStyle.fillColor,
        fillOutlineColor: customStyle.fillOutlineColor,
      };
    }

    const res = {
      id: item.id,
      fid: item.fid,
      layerName: item.layerName,
      dirOrLayer: item.dirOrLayer,
      config,
      children: legacy(item.children ?? []),
    };

    return res;
  });
}
