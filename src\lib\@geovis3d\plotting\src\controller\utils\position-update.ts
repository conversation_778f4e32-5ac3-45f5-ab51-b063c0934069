import type { PlottingEntity } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';

/**
 * 通用的位置更新器
 * @param entity
 */
export function positionUpdate(entity: PlottingEntity, type: 'start' | 'center' = 'center') {
  if (entity.plotting.playing && entity.plotting.sampleds.getLength() > 1) {
    const dataSource = entity.entityCollection.owner as Cesium.DataSource;
    const startTime = dataSource.clock?.startTime;
    entity.position = entity.plotting.sampleds.computeSampledPositionProperty(startTime.clone());
  }
  else {
    let position
      = type == 'center'
        ? entity.plotting.coordinates.getCenter()
        : entity.plotting.coordinates.getByIndex(0)?.clone();
    position ??= entity.plotting.mousePosition?.clone();
    entity.position = new Cesium.ConstantPositionProperty(position);
  }
}
