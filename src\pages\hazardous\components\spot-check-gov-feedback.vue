<!-- 政府巡查反馈 -->
<script lang="ts" setup>
import type {
  HdcGovernmentPatrolFbQueryDTO,
} from '@/genapi/production';
import {
  productionHazardousChemicalsGovernmentPatrolFeedbackPageUsingPost,
  productionHazardousChemicalsGovernmentPatrolFeedbackReportIdUsingGet,
} from '@/genapi/production';
import { usePage } from '@/hooks/use-page';

defineOptions({ name: 'SpotCheckGovFeedback' });

const query = reactive<HdcGovernmentPatrolFbQueryDTO>({});

const datetime = computed<any>({
  get: () => [query.createTimeEnd, query.createTimeEnd],
  set(value) {
    query.createTimeEnd = value?.[0];
    query.createTimeEnd = value?.[1];
  },
});

const { records, pageSize, currentPage, isLoading, total, execute } = usePage({
  immediate: true,
  initPageSize: 30,
  async fetch({ currentPage, pageSize }) {
    const { data } = await productionHazardousChemicalsGovernmentPatrolFeedbackPageUsingPost({
      data: {
        current: currentPage,
        size: pageSize,
        query,
      },
    });
    return {
      records: data?.records ?? [],
      total: data?.total ?? 0,
    };
  },
});

watch(
  () => query,
  () => execute({ currentPage: 1 }),
  {
    deep: true,
  },
);
const dialog = ref(false);

// 巡查报告
const {
  state: data,
  execute: trigger,
  isLoading: dialogLoading,
} = useAsyncState(async (id: string) => {
  dialog.value = true;
  const { data } = await productionHazardousChemicalsGovernmentPatrolFeedbackReportIdUsingGet({ path: { id } });
  return data;
}, undefined, {
  immediate: false,
});
</script>

<template>
  <el-form inline>
    <el-form-item>
      <el-date-picker
        v-model="datetime"
        type="daterange"
        value-format="YYYY-MM-DD"
        :disabled-date="(v:any) => $toDayjs(v).isAfter($toDayjs())"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
      />
    </el-form-item>
  </el-form>

  <div v-loading="isLoading" class="flex-1 overflow-hidden">
    <el-table :data="records" height="100%" stripe>
      <el-table-column label="序号" align="center" type="index" />
      <el-table-column v-slot="{ row }" label="行政区划" align="center">
        {{ row.regionName || '海南省' }}
      </el-table-column>
      <el-table-column label="系统在线情况" align="center">
        <el-table-column label="在线率" align="center" prop="onlineRate" />
        <el-table-column label="排名" align="center" prop="onlineSort" />
      </el-table-column>
      <el-table-column label="视频监控在线情况" align="center">
        <el-table-column label="在线率" align="center" prop="videoOnlineRateName" />
        <el-table-column label="排名" align="center" prop="videoOnlineSort" />
      </el-table-column>
      <el-table-column label="安全承诺情况" align="center">
        <el-table-column label="承诺率" align="center" prop="promiseRate" />
        <el-table-column label="排名" align="center" prop="promiseSort" />
      </el-table-column>
      <el-table-column label="超24小时未销警指标数" align="center">
        <el-table-column label="数量" align="center" prop="latestAlarmTargetCount" />
        <el-table-column label="持续时长" align="center" prop="latestAlarmTargetInterval" />
      </el-table-column>
      <el-table-column label="预警处置及督办情况" align="center">
        <el-table-column label="预警未销警" align="center" prop="warnCount" />
        <el-table-column label="预警未反馈" align="center" prop="warnNoRespCount" />
        <el-table-column label="通报未反馈" align="center" prop="warnNoticeNoRespCount" />
      </el-table-column>
      <el-table-column label="其他情况" align="center" prop="xxxxxx" />
      <el-table-column label="巡查人" align="center" prop="contactor" />
      <el-table-column label="巡查时间" align="center" prop="createTime" />
      <el-table-column label="反馈状态" align="center" prop="inspectStatusName" />
      <el-table-column label="反馈时间" align="center" prop="feedbackTime" />
      <el-table-column v-slot="{ row }" label="操作" align="center">
        <el-button type="primary" @click="trigger(0, row.id)">
          操作
        </el-button>
      </el-table-column>
    </el-table>
  </div>
  <el-pagination
    v-model:page-size="pageSize"
    v-model:current-page="currentPage"
    class="justify-end"
    :total="total"
  />
  <el-dialog v-model="dialog" title="巡查报告">
    <el-scrollbar v-loading="dialogLoading" class="overflow-hidden max-h-700px!">
      <div v-loading="dialogLoading" class="html-warper" v-html="data?.text" />
    </el-scrollbar>
  </el-dialog>
</template>

<style lang="scss" scoped>
  .html-warper {
  padding: 40px;
  font-size: 20px;
  color: black;
  background: #fff;

  :deep().report-code {
    text-align: center;
  }

  :deep().report-name {
    padding-bottom: 20px;
    font-size: 40px;
    font-weight: bold;
    color: red;
    text-align: center;
  }

  :deep().content {
    text-indent: 4rem;
  }

  :deep().end-name,
  :deep().end-time {
    text-align: right;
  }
}
</style>
