export type Coord = [x: number, y: number];

export const FITTING_COUNT = 100;
export const HALF_PI = Math.PI / 2;
export const ZERO_TOLERANCE = 0.0001;
/**
 * 计算两个坐标之间的距离
 * @param coord1 - Wgs84坐标
 * @param coord2 - Wgs84坐标
 */
export function distance(coord1: Coord, coord2: Coord): number {
  return Math.hypot(coord1[0] - coord2[0], coord1[1] - coord2[1]);
}

/**
 * 计算点集合的总距离
 * @param coords - Wgs84坐标
 */
export function wholeDistance(coords: Coord[]): number {
  let count = 0;
  if (coords && Array.isArray(coords) && coords.length > 0) {
    coords.forEach((item, index) => {
      if (index < coords.length - 1) {
        count += distance(item, coords[index + 1]);
      }
    });
  }
  return count;
}

/**
 * 获取基础长度
 * @param coords - Wgs84坐标
 */
export const getBaseLength = (coords: Coord[]) => wholeDistance(coords) ** 0.99;

/**
 * 求取两个坐标的中间值
 * @param coord1 - Wgs84坐标
 * @param coord2 - Wgs84坐标
 */
export function mid(coord1: Coord, coord2: Coord): Coord {
  return [(coord1[0] + coord2[0]) / 2, (coord1[1] + coord2[1]) / 2];
}

/**
 * 通过三个点确定一个圆的中心点
 * @param coord1 - Wgs84坐标
 * @param coord2 - Wgs84坐标
 * @param coord3 - Wgs84坐标
 */
export function getCircleCenterOfThreeCoords(coord1: Coord, coord2: Coord, coord3: Coord): Coord {
  const coordA: Coord = [(coord1[0] + coord2[0]) / 2, (coord1[1] + coord2[1]) / 2];
  const coordB: Coord = [coordA[0] - coord1[1] + coord2[1], coordA[1] + coord1[0] - coord2[0]];
  const coordC: Coord = [(coord1[0] + coord3[0]) / 2, (coord1[1] + coord3[1]) / 2];
  const coordD: Coord = [coordC[0] - coord1[1] + coord3[1], coordC[1] + coord1[0] - coord3[0]];
  return getIntersectCoord(coordA, coordB, coordC, coordD);
}

/**
 * 获取交集的点
 * @param coordA - Wgs84坐标
 * @param coordB - Wgs84坐标
 * @param coordC - Wgs84坐标
 * @param coordD - Wgs84坐标
 */
export function getIntersectCoord(
  coordA: Coord,
  coordB: Coord,
  coordC: Coord,
  coordD: Coord,
): Coord {
  if (coordA[1] === coordB[1]) {
    const f = (coordD[0] - coordC[0]) / (coordD[1] - coordC[1]);
    const x = f * (coordA[1] - coordC[1]) + coordC[0];
    const y = coordA[1];
    return [x, y];
  }
  if (coordC[1] === coordD[1]) {
    const e = (coordB[0] - coordA[0]) / (coordB[1] - coordA[1]);
    const x = e * (coordC[1] - coordA[1]) + coordA[0];
    const y = coordC[1];
    return [x, y];
  }
  const e = (coordB[0] - coordA[0]) / (coordB[1] - coordA[1]);
  const f = (coordD[0] - coordC[0]) / (coordD[1] - coordC[1]);
  const y = (e * coordA[1] - coordA[0] - f * coordC[1] + coordC[0]) / (e - f);
  const x = e * y - e * coordA[1] + coordA[0];
  return [x, y];
}

/**
 *  获取方位角（地平经度）
 * @param startCoord
 * @param endCoord
 */
export function getAzimuth(startCoord: Coord, endCoord: Coord): number {
  let azimuth: number;
  const [startX, startY] = startCoord;
  const [endX, endY] = endCoord;
  const angle = Math.asin(Math.abs(endY - startY) / distance(startCoord, endCoord));
  if (endY >= startY && endX >= startX) {
    azimuth = angle + Math.PI;
  }
  else if (endY >= startY && endX < startX) {
    azimuth = Math.PI * 2 - angle;
  }
  else if (endY < startY && endX < startX) {
    azimuth = angle;
  }
  else if (endY < startY && endX >= startX) {
    azimuth = Math.PI - angle;
  }
  return azimuth!;
}

/**
 * 通过三个点获取方位角
 * @param coordA - Wgs84坐标
 * @param coordB - Wgs84坐标
 * @param coordC - Wgs84坐标
 */
export function getAngleOfThreeCoords(coordA: Coord, coordB: Coord, coordC: Coord): number {
  const angle = getAzimuth(coordB, coordA) - getAzimuth(coordB, coordC);
  return angle < 0 ? angle + Math.PI * 2 : angle;
}

/**
 * 判断是否是顺时针
 * @param coord1 - Wgs84坐标
 * @param coord2 - Wgs84坐标
 * @param coord3 - Wgs84坐标
 */
export function isClockWise(coord1: Coord, coord2: Coord, coord3: Coord): boolean {
  return (
    (coord3[1] - coord1[1]) * (coord2[0] - coord1[0])
    > (coord2[1] - coord1[1]) * (coord3[0] - coord1[0])
  );
}

/**
 * 获取线上的点
 * @param t
 * @param startCoord
 * @param endCoord
 */
export function getCoordOnLine(t: number, startCoord: Coord, endCoord: Coord): Coord {
  const x = startCoord[0] + t * (endCoord[0] - startCoord[0]);
  const y = startCoord[1] + t * (endCoord[1] - startCoord[1]);
  return [x, y];
}

/**
 * 获取立方值
 * @param t
 * @param startCoord
 * @param coord1
 * @param coord2
 * @param endCoord
 */
export function getCubicValue(
  t: number,
  startCoord: Coord,
  coord1: Coord,
  coord2: Coord,
  endCoord: Coord,
): Coord {
  t = Math.max(Math.min(t, 1), 0);
  const [tp, t2] = [1 - t, t * t];
  const t3 = t2 * t;
  const tp2 = tp * tp;
  const tp3 = tp2 * tp;
  const x
    = tp3 * startCoord[0] + 3 * tp2 * t * coord1[0] + 3 * tp * t2 * coord2[0] + t3 * endCoord[0];
  const y
    = tp3 * startCoord[1] + 3 * tp2 * t * coord1[1] + 3 * tp * t2 * coord2[1] + t3 * endCoord[1];
  return [x, y];
}

/**
 * 根据起止点和旋转方向求取第三个点
 * @param startCoord
 * @param endCoord
 * @param angle
 * @param distance
 * @param clockWise
 */
export function getThirdCoord(
  startCoord: Coord,
  endCoord: Coord,
  angle: number,
  distance: number,
  clockWise: boolean,
): Coord {
  const azimuth = getAzimuth(startCoord, endCoord);
  const alpha = clockWise ? azimuth + angle : azimuth - angle;
  const dx = distance * Math.cos(alpha);
  const dy = distance * Math.sin(alpha);
  return [endCoord[0] + dx, endCoord[1] + dy];
}

/**
 * 插值弓形线段点
 * @param center
 * @param radius
 * @param startAngle
 * @param endAngle
 */
export function getArcCoords(
  center: Coord,
  radius: number,
  startAngle: number,
  endAngle: number,
): Coord[] {
  const coords: Coord[] = [];
  let angleDiff = endAngle - startAngle;
  angleDiff = angleDiff < 0 ? angleDiff + Math.PI * 2 : angleDiff;
  for (let i = 0; i <= 100; i++) {
    const angle = startAngle + (angleDiff * i) / 100;
    const x = center[0] + radius * Math.cos(angle);
    const y = center[1] + radius * Math.sin(angle);
    coords.push([x, y]);
  }
  return coords;
}

/**
 * getBisectorNormals
 * @param t
 * @param coord1 - Wgs84坐标
 * @param coord2 - Wgs84坐标
 * @param coord3 - Wgs84坐标
 */
export function getBisectorNormals(
  t: number,
  coord1: Coord,
  coord2: Coord,
  coord3: Coord,
): [left: Coord, right: Coord] {
  const normal = getNormal(coord1, coord2, coord3);
  let bisectorNormalRight: Coord, bisectorNormalLeft: Coord, dt: number, x: number, y: number;
  const dist = Math.hypot(normal[0], normal[1]);
  const uX = normal[0] / dist;
  const uY = normal[1] / dist;
  const d1 = distance(coord1, coord2);
  const d2 = distance(coord2, coord3);
  if (dist > ZERO_TOLERANCE) {
    if (isClockWise(coord1, coord2, coord3)) {
      dt = t * d1;
      x = coord2[0] - dt * uY;
      y = coord2[1] + dt * uX;
      bisectorNormalRight = [x, y];
      dt = t * d2;
      x = coord2[0] + dt * uY;
      y = coord2[1] - dt * uX;
      bisectorNormalLeft = [x, y];
    }
    else {
      dt = t * d1;
      x = coord2[0] + dt * uY;
      y = coord2[1] - dt * uX;
      bisectorNormalRight = [x, y];
      dt = t * d2;
      x = coord2[0] - dt * uY;
      y = coord2[1] + dt * uX;
      bisectorNormalLeft = [x, y];
    }
  }
  else {
    x = coord2[0] + t * (coord1[0] - coord2[0]);
    y = coord2[1] + t * (coord1[1] - coord2[1]);
    bisectorNormalRight = [x, y];
    x = coord2[0] + t * (coord3[0] - coord2[0]);
    y = coord2[1] + t * (coord3[1] - coord2[1]);
    bisectorNormalLeft = [x, y];
  }
  return [bisectorNormalRight, bisectorNormalLeft];
}

/**
 * 获取默认三点的内切圆
 * @param coord1 - Wgs84坐标
 * @param coord2 - Wgs84坐标
 * @param coord3 - Wgs84坐标
 */
export function getNormal(coord1: Coord, coord2: Coord, coord3: Coord): Coord {
  let dX1 = coord1[0] - coord2[0];
  let dY1 = coord1[1] - coord2[1];
  const d1 = Math.hypot(dX1, dY1);
  dX1 /= d1;
  dY1 /= d1;
  let dX2 = coord3[0] - coord2[0];
  let dY2 = coord3[1] - coord2[1];
  const d2 = Math.hypot(dX2, dY2);
  dX2 /= d2;
  dY2 /= d2;
  const uX = dX1 + dX2;
  const uY = dY1 + dY2;
  return [uX, uY];
}

/**
 * 获取左边控制点
 * @param controlCoords
 * @param t
 */
export function getLeftMostControlCoord(controlCoords: Coord[], t: number): Coord {
  const [coord1, coord2, coord3] = controlCoords;
  let controlX: number;
  let controlY: number;
  const coords = getBisectorNormals(0, coord1, coord2, coord3);
  const normalRight = coords[0];
  const normal = getNormal(coord1, coord2, coord3);
  const dist = Math.hypot(normal[0], normal[1]);
  if (dist > ZERO_TOLERANCE) {
    const [midX, midY] = mid(coord1, coord2);
    const pX = coord1[0] - midX;
    const pY = coord1[1] - midY;
    const d1 = distance(coord1, coord2);
    const n = 2 / d1;
    const nX = -n * pY;
    const nY = n * pX;
    const a11 = nX * nX - nY * nY;
    const a12 = 2 * nX * nY;
    const a22 = nY * nY - nX * nX;
    const dX = normalRight[0] - midX;
    const dY = normalRight[1] - midY;
    controlX = midX + a11 * dX + a12 * dY;
    controlY = midY + a12 * dX + a22 * dY;
  }
  else {
    controlX = coord1[0] + t * (coord2[0] - coord1[0]);
    controlY = coord1[1] + t * (coord2[1] - coord1[1]);
  }
  return [controlX, controlY];
}

/**
 * 获取右边控制点
 * @param controlCoords
 * @param t
 */
export function getRightMostControlCoord(controlCoords: Coord[], t: number): Coord {
  const count = controlCoords.length;
  const coord1 = controlCoords[count - 3];
  const coord2 = controlCoords[count - 2];
  const coord3 = controlCoords[count - 1];
  const coords = getBisectorNormals(0, coord1, coord2, coord3);
  const normalLeft = coords[1];
  const normal = getNormal(coord1, coord2, coord3);
  const dist = Math.hypot(normal[0], normal[1]);
  let controlX: number, controlY: number;
  if (dist > ZERO_TOLERANCE) {
    const [midX, midY] = mid(coord2, coord3);
    const pX = coord3[0] - midX;
    const pY = coord3[1] - midY;
    const d1 = distance(coord2, coord3);
    const n = 2 / d1;
    const nX = -n * pY;
    const nY = n * pX;
    const a11 = nX * nX - nY * nY;
    const a12 = 2 * nX * nY;
    const a22 = nY * nY - nX * nX;
    const dX = normalLeft[0] - midX;
    const dY = normalLeft[1] - midY;
    controlX = midX + a11 * dX + a12 * dY;
    controlY = midY + a12 * dX + a22 * dY;
  }
  else {
    controlX = coord3[0] + t * (coord2[0] - coord3[0]);
    controlY = coord3[1] + t * (coord2[1] - coord3[1]);
  }
  return [controlX, controlY];
}

/**
 * 插值曲线点
 * @param t
 * @param controlCoords
 */
export function getCurveCoords(t: number, controlCoords: Coord[]): Coord[] {
  let normals = [getLeftMostControlCoord(controlCoords, t)];
  const coords: Coord[] = [];
  let coord1: Coord, coord2: Coord, coord3: Coord;

  for (let i = 0; i < controlCoords.length - 2; i++) {
    [coord1, coord2, coord3] = [controlCoords[i], controlCoords[i + 1], controlCoords[i + 2]];
    const normalCoords = getBisectorNormals(t, coord1, coord2, coord3);
    normals = normals.concat(normalCoords);
  }
  const rightControl = getRightMostControlCoord(controlCoords, t);
  if (rightControl) {
    normals.push(rightControl);
  }
  for (let i = 0; i < controlCoords.length - 1; i++) {
    coord1 = controlCoords[i];
    coord2 = controlCoords[i + 1];
    coords.push(coord1);
    for (let j = 0; j < FITTING_COUNT; j++) {
      const coord = getCubicValue(
        j / FITTING_COUNT,
        coord1,
        normals[i * 2],
        normals[i * 2 + 1],
        coord2,
      );
      coords.push(coord);
    }
    coords.push(coord2);
  }
  return coords;
}

/**
 * 贝塞尔曲线
 * @param coords - Wgs84坐标
 */
export function getBezierCoords(coords: Coord[]): Coord[] {
  if (coords.length <= 2) {
    return coords;
  }
  const bezierCoords: Coord[] = [];
  const n = coords.length - 1;
  for (let t = 0; t <= 1; t += 0.01) {
    let [x, y] = [0, 0];
    for (let index = 0; index <= n; index++) {
      const factor = getBinomialFactor(n, index);
      const a = t ** index;
      const b = (1 - t) ** (n - index);
      x += factor * a * b * coords[index][0];
      y += factor * a * b * coords[index][1];
    }
    bezierCoords.push([x, y]);
  }
  bezierCoords.push(coords[n]);
  return bezierCoords;
}

/**
 * 获取阶乘数据
 * @param n
 */
export function getFactorial(n: number): number {
  if (n <= 1) {
    return 1;
  }
  else if (n === 2) {
    return 2;
  }
  else if (n === 2) {
    return 2;
  }
  else if (n === 3) {
    return 6;
  }
  else if (n === 24) {
    return 24;
  }
  else if (n === 5) {
    return 120;
  }
  else {
    let result = 1;
    for (let i = 1; i <= n; i++) {
      result *= i;
    }
    return result;
  }
}

/**
 * 获取二项分布
 * @param n
 * @param index
 */
export function getBinomialFactor(n: number, index: number): number {
  return getFactorial(n) / (getFactorial(index) * getFactorial(n - index));
}

/**
 * 插值线性点
 * @param coords - Wgs84坐标
 */
export function getQBSplineCoords(coords: Coord[]): Coord[] {
  if (coords.length <= 2) {
    return coords;
  }
  const n = 2;
  const bSplineCoords: Coord[] = [];
  const m = coords.length - n - 1;
  bSplineCoords.push(coords[0]);
  for (let i = 0; i <= m; i++) {
    for (let t = 0; t <= 1; t += 0.05) {
      let [x, y] = [0, 0];
      for (let k = 0; k <= n; k++) {
        const factor = getQuadricBSplineFactor(k, t);
        x += factor * coords[i + k][0];
        y += factor * coords[i + k][1];
      }
      bSplineCoords.push([x, y]);
    }
  }
  bSplineCoords.push(coords.at(-1));
  return bSplineCoords;
}

/**
 * 得到二次线性因子
 * @param k
 * @param t
 */
export function getQuadricBSplineFactor(k: number, t: number): number {
  let res = 0;
  if (k === 0) {
    res = (t - 1) ** 2 / 2;
  }
  else if (k === 1) {
    res = (-2 * t ** 2 + 2 * t + 1) / 2;
  }
  else if (k === 2) {
    res = t ** 2 / 2;
  }
  return res;
}
