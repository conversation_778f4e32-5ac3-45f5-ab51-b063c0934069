import type { SampledPointSymbol } from '@/lib/@geovis3d/core';
import type { Cartesian3SerializateJSON } from './cartesian3';
import { Cartesian3Serializate } from './cartesian3';

export interface SampledPointSymbolSerializateJSON {
  id: string;

  duration: number;

  position: Cartesian3SerializateJSON;
}

export type SampledPointSymbolKey = keyof SampledPointSymbolSerializateJSON;

export class SampledPointSymbolSerializate {
  private constructor() {}

  static toJSON(data?: SampledPointSymbol): SampledPointSymbolSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }

    return {
      id: data.id,
      duration: data.duration,
      position: Cartesian3Serializate.toJSON(data.position)!,
    };
  }

  static fromJSON(json?: SampledPointSymbolSerializateJSON): SampledPointSymbol | undefined {
    if (!json) {
      return undefined;
    }

    return {
      id: json.id,
      duration: json.duration,
      position: Cartesian3Serializate.fromJSON(json.position)!,
    };
  }
}
