import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.PolylineCollection} 构造函数参数
 */
export type PolylineCollectionConstructorOptions = ConstructorParameters<
  typeof Cesium.PolylineCollection
>[0];

/**
 * {@link Cesium.PolylineCollection} 拓展用法与 {@link Cesium.PolylineCollection} 基本一致。
 *
 * `GcPolylineCollection.event`鼠标事件监听
 */
export class GcPolylineCollection extends Cesium.PolylineCollection {
  constructor(options?: PolylineCollectionConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
