/**
 * 将泛型转换成可空类型
 */
export type Nullable<T> = T | null | undefined;

/**
 * JS基础类型
 */
export type BasicType = number | string | boolean | symbol | bigint | null | undefined;

/**
 * 传入泛型控制参数及返回值的函数
 */
export type ArgsFunction<Args extends any[] = any[], Return = void> = (...args: Args) => Return;

export type AnyFn = (...args: any[]) => any;

/**
 * 函数返回值可能为Promise
 */
export type MaybePromiseFn<Args extends any[] = any[], Return = void> =
  | ArgsFunction<Args, Return>
  | ArgsFunction<Args, Promise<Return>>;
