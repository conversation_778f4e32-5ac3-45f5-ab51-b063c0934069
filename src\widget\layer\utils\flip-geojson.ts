import type { Feature, Geometry, Position } from 'geojson';

/**
 * 将GEOJSON的经纬度坐标翻转为纬度和经度。
 */
export function flipGeojson(geojson: Feature): Feature {
  const geometry = flipGeometryCoordinates(geojson.geometry);
  return {
    ...geojson,
    geometry,
  };
}

function flipGeometryCoordinates(geometry: Geometry): Geometry {
  switch (geometry.type) {
    case 'Point':
      return {
        type: 'Point',
        coordinates: flipCoordinatesPair(geometry.coordinates),
      };
    case 'LineString':
      return {
        type: 'LineString',
        coordinates: geometry.coordinates.map(flipCoordinatesPair),
      };
    case 'Polygon':
      return {
        type: 'Polygon',
        coordinates: geometry.coordinates.map(ring => ring.map(flipCoordinatesPair)),
      };
    case 'MultiPoint':
      return {
        type: 'MultiPoint',
        coordinates: geometry.coordinates.map(flipCoordinatesPair),
      };
    case 'MultiLineString':
      return {
        type: 'MultiLineString',
        coordinates: geometry.coordinates.map(line => line.map(flipCoordinatesPair)),
      };
    case 'MultiPolygon':
      return {
        type: 'MultiPolygon',
        coordinates: geometry.coordinates.map(polygon => polygon.map(ring => ring.map(flipCoordinatesPair))),
      };
    default:
      throw new Error(`Unsupported geometry type: ${geometry.type}`);
  }
}

function flipCoordinatesPair([longitude, latitude]: Position): Position {
  return [latitude, longitude];
}
