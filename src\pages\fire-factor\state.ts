import { CzEntity } from '@x3d/all';
import { useCzEntities } from '@x3d/vue-hooks';
import { Cartesian3 } from 'cesium';
import { commonControllerGetAreaBoundary } from './api';
import mockData from './mockData2.json';

/**
 * 页面全局变量
 */
export const [createFireFactorState, injectFireFactorState] = createInjectionState(() => {
  const polylinePositions = computedAsync(async () => {
    const { data } = await commonControllerGetAreaBoundary({
      areaCode: '460000',
      areaRank: null,
      incodes: '460105\',\'460203\',\'460204\',\'460205\',\'460400\',\'469001\',\'469002\',\'469005\',\'469006\',\'469007\',\'469021\',\'469022\',\'469023\',\'469024\',\'469025\',\'469026\',\'469027\',\'469028\',\'469029\',\'469030',
      dataFlag: 'rank34',
    });

    return data?.map((item: any) => {
      return {
        ...item,
        positions: Cartesian3.fromDegreesArray(item.coordinate.split(' ').map((e: any) => e.split(',').map((e: any) => +e)).flat()),
      };
    });
  }, []);

  // 市县边界
  useCzEntities(() => {
    return polylinePositions.value?.map((item: any) => {
      return new Cesium.Entity({
        polyline: {
          positions: Cartesian3.fromDegreesArray(item.coordinate.split(' ').map((e: any) => e.split(',').map((e: any) => +e)).flat()),
          width: 1,
          material: Cesium.Color.SKYBLUE,
        },
      });
    });
  });

  // 站点列表
  const { state: stationList } = useAsyncState(
    async () => {
      const records = mockData.records;

      return records.sort((a, b) => +b.risklevel - +a.risklevel);
    },
    [],
    {
      immediate: true,
    },
  );

  const activeStationId = ref<string>();

  useCzEntities(() => {
    return stationList.value?.map((item) => {
      const entity = new CzEntity({
        position: Cartesian3.fromDegrees(item.lon, item.lat),
        cursor: 'pointer',
        point: {
          pixelSize: 10,
          color: +item.risklevel > 2 ? Cesium.Color.RED : Cesium.Color.AQUA,
        },
      });
      entity.event.on('LEFT_CLICK', () => {
        activeStationId.value = item.id;
      });
      return entity;
    });
  });

  const activeStation = computed(() => {
    return stationList.value?.find(item => item.id === activeStationId.value);
  });

  return {
    activeStationId,
    activeStation,
  };
});
