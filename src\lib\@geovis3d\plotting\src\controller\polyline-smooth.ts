import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import { Bezier } from 'bezier-js';
import * as Cesium from 'cesium';

/**
 * polyline-smooth
 */
export default <PlottingControllerOptions>{
  type: 'polyline-smooth',
  manualTerminate: entity => entity.plotting.coordinates.getLength() > 1,
  center: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  interval: { visible: true },
  update(entity) {
    if (!entity.polyline) {
      entity.polyline = new Cesium.PolylineGraphics({
        material: Cesium.Color.RED,
        width: 2,
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    let cache: Cesium.Cartesian3[] = [];
    if (positions.length < 2) {
      return;
    }
    const xyzList = positions
      .map(position => cartesianToWgs84(position))
      .map(([x, y, z]) => ({ x, y, z }));
    const cartesian3s = new Bezier(xyzList)
      .getLUT()
      .map(({ x, y, z }) => wgs84ToCartesian([x, y, z || 0]));
    cache = cartesian3s;
    entity.polyline!.positions = new Cesium.CallbackProperty(() => cache, false);
  },
};
