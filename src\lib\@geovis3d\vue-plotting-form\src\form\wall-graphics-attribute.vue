<!-- WallGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { WallGraphicsKey, WallGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { WallGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian3ArrayAttribute from './cartesian3-array-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberArrayAttribute from './number-array-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'WallGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: WallGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.WallGraphics, WallGraphicsSerializateJSON>({
  graphic: () => props.entity?.wall,
  omit: props.omit,
  toJSON: (graphics, omit) => WallGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => WallGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="wall"
    graphics-field="show"
    label="可见"
  />
  <Cartesian3ArrayAttribute
    v-if="!hide?.includes('positions')"
    v-model="model.positions"
    graphics="wall"
    graphics-field="positions"
    label="positions"
  />
  <NumberArrayAttribute
    v-if="!hide?.includes('minimumHeights')"
    v-model="model.minimumHeights"
    graphics="wall"
    graphics-field="minimumHeights"
    label="底部高度"
  />
  <NumberArrayAttribute
    v-if="!hide?.includes('maximumHeights')"
    v-model="model.maximumHeights"
    graphics="wall"
    graphics-field="maximumHeights"
    label="顶部高度"
  />
  <NumberAttribute
    v-if="!hide?.includes('granularity')"
    v-model="model.granularity"
    graphics="wall"
    graphics-field="granularity"
    label="渲染粒度"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="wall"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="wall"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="wall"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="wall"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="wall"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="wall"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="wall"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
