import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { ParticleGraphics } from '@/lib/@geovis3d/core';
import * as Cesium from 'cesium';

import { positionUpdate } from './utils/position-update';

/**
 * particle标绘配置 通用粒子
 */
export default <PlottingControllerOptions>{
  type: 'particle',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 1,
  location: { visible: true },
  altitude: { visible: true },
  scale: { visible: true },
  scaleCallback(entity, scale) {
    const prev = entity.particle!.endScale?.getValue(Cesium.JulianDate.now()) ?? 1;
    entity.particle!.endScale = new Cesium.ConstantProperty(prev * scale);
  },
  update(entity) {
    if (!entity.particle) {
      entity.particle = new ParticleGraphics({});
    }
    // 粒子影响定义态点位的高度获取   定义态结束后再显示粒子系统
    entity.particle!.show = new Cesium.ConstantProperty(!entity.plotting.defining);
    positionUpdate(entity);
  },
};
