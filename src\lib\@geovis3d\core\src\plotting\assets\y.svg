<svg width="40" height="41" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <filter x="-22.4%" y="-16%" width="144.9%" height="144.9%" filterUnits="objectBoundingBox" id="prefix__a">
            <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" in="shadowBlurOuter1" />
        </filter>
        <filter x="-38.9%" y="-27.8%" width="177.8%" height="177.8%" filterUnits="objectBoundingBox" id="prefix__c">
            <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 0.00372360947 0 0 0 0 0.0580977667 0 0 0 0 0.0786062047 0 0 0 0.5 0"
                in="shadowBlurOuter1" />
        </filter>
        <path id="prefix__b" d="M2.4 2.4h31.2v31.2H2.4z" />
        <path
            d="M18.006 23.277c.42 0 .744.313.744.693v2.337c0 .38-.324.693-.744.693s-.756-.302-.756-.693V23.97c0-.38.336-.693.756-.693zm-7.91-9.581l6.722.01-3.036 4.001-.868-.932c-1.062.455-1.442.932-1.442 1.095 0 .51 2.245 1.973 6.539 1.973s6.538-1.464 6.538-1.973c0-.228-.737-1.063-2.992-1.594a1.23 1.23 0 01.553-2.396c3.112.726 4.89 2.18 4.89 3.99 0 2.906-4.522 4.434-9 4.434s-9-1.518-9-4.434c0-1.182.759-2.212 2.18-2.993l-1.085-1.181zM18.005 9c.408 0 .744.313.744.704v9c0 .38-.324.694-.744.694s-.756-.314-.756-.705V9.704c0-.391.348-.704.756-.704z"
            id="prefix__d" transform="translate(5 7)" />
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="prefix__e">
            <stop stop-color="#FFF" offset="0%" />
            <stop stop-color="#CCE7FF" offset="100%" />
        </linearGradient>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <g transform="translate(2 1)">
            <use fill="#000" filter="url(#prefix__a)" xlink:href="#prefix__b" />
            <use fill="#1465C9" xlink:href="#prefix__b" />
        </g>
        <g transform="translate(-3 -6)" fill-rule="nonzero">
            <use fill="#000" filter="url(#prefix__c)" xlink:href="#prefix__d" />
            <use fill="url(#prefix__e)" xlink:href="#prefix__d" />
        </g>
        <path stroke="#0085FF" stroke-width="2" d="M3 35v2M36 36h2M36 2h2M3 1v2" />
    </g>
</svg>