<script lang="ts" setup>
import type { DEMO_LIST } from './demoList';
import * as Cesium from 'cesium';

defineOptions({ name: '<PERSON>per' });
const props = defineProps<PopperProps>();

const emit = defineEmits<PopperEmits>();

export interface PopperProps {
  item?: typeof DEMO_LIST[0];
}

export interface PopperEmits {
  (event: 'update:item', data?: any): void;
}

const item = useVModel(props, 'item', emit);
const tempVideo = ref(new URL('./assets//imgs/video.mp4', import.meta.url).href);

const position = computed(() => {
  const { longitude, latitude } = item.value ?? {};
  return (longitude && latitude) ? Cesium.Cartesian3.fromDegrees(+longitude, +latitude) : undefined;
});
const title = computed(() => {
  return item.value?.name;
});
</script>

<template>
  <located-popper1
    v-if="position"
    :position="position"
    :header="title"
    class="max-h-560px! w-700px!"
    show-close
    @close="item = undefined, position = undefined"
  >
    <el-scrollbar wrap-class="py-16px px-30px">
      <video :src="tempVideo" loop autoplay controls />
      <el-descriptions :column="1" border my="15px">
        <el-descriptions-item label="植被信息">
          林地，草地
        </el-descriptions-item>
        <el-descriptions-item label="含水率">
          含水率:0.1
        </el-descriptions-item>
        <el-descriptions-item label="墒情">
          21%
        </el-descriptions-item>
      </el-descriptions>
    </el-scrollbar>
  </located-popper1>
</template>
