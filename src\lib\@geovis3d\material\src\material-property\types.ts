import type * as Cesium from 'cesium';

/**
 * Cesium.Material.fabric参数
 * @property type - 用于声明 fabric 对象最终会生成什么材质，如果是官方内置的，直接用官方内置的，否则则创建自定义的材质并缓存。
 * @property materials - 可再塞进去子一级的 fabric，构成复合材质
 * @property source - glsl代码
 * @property uniforms - 传glsl代码的变量
 */
export interface CesiumMaterialFabricOptions<
  U extends Record<string, any>,
  M extends Cesium.Material = any,
> {
  type: string;
  materials?: M;
  source?: string;
  components?: string;
  uniforms?: U;
}

/**
 * Cesium.Material参数
 * @property strict - 严格模式
 * @property translucent - 是否半透明
 * @property minificationFilter
 * @property magnificationFilter
 * @property fabric - 矩阵配置
 */
export interface CesiumMaterialOptions<
  U extends Record<string, any>,
  M extends Cesium.Material = any,
> {
  strict?: boolean;
  translucent?: boolean | ((...params: any[]) => any);
  minificationFilter?: Cesium.TextureMinificationFilter;
  magnificationFilter?: Cesium.TextureMagnificationFilter;
  fabric: CesiumMaterialFabricOptions<U, M>;
}
