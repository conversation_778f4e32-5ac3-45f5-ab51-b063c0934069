import type { MaybeRefOrGetter } from 'vue-demi';
import { throttle } from '@/lib/@geovis3d/core';
import { useCzEventListener } from '@x3d/vue-hooks';

import { computed, ref, toRaw, toRef, toValue, watch } from 'vue';

export interface UseShallowBindingOptions<T> {
  left: MaybeRefOrGetter<T>;
  right: MaybeRefOrGetter<T>;
  ltr: (value: T) => void;
  rtl: (value: T) => void;
}

/**
 * 两个对象之前的浅双向绑定
 */
export function useShallowBinding<T>(options: UseShallowBindingOptions<T>) {
  const leftValue = toRef(options.left);
  const rightValue = toRef(options.right);

  // 浅判断
  function dirty() {
    if (!rightValue.value) {
      return true;
    }
    const leftKeys = Object.keys(leftValue.value ?? {});
    const rightKeys = Object.keys(leftValue.value ?? {});
    const keys = [...new Set([...leftKeys, ...Object.keys(rightKeys)])] as (keyof T)[];
    let index = 0;
    let changed = false;
    while (index < keys.length) {
      const key = keys[index];
      if (rightValue.value?.[key] === leftValue.value?.[key]) {
        index++;
      }
      else {
        changed = true;
        break;
      }
    }
    return changed;
  }
  watch(
    leftValue,
    (leftValue) => {
      dirty() && options?.ltr(toRaw(leftValue));
    },
    {
      immediate: true,
    },
  );
  watch(
    rightValue,
    (rightValue) => {
      dirty() && options?.rtl(toRaw(rightValue));
    },
    {
      deep: true,
    },
  );
}

export interface UseGraphicsBindingOptions<G, J> {
  graphic: MaybeRefOrGetter<G | undefined>;
  omit?: (keyof J)[];
  toJSON: (value?: G, omit?: (keyof J)[]) => J | undefined;
  fromJSON: (value?: J, omit?: (keyof J)[]) => G | undefined;
}

/**
 * 输入一个graphic,实现graphic和json的相互绑定
 * @param options
 * @returns ref<JSON>
 */
export function useGraphicsBinding<G, J>(options: UseGraphicsBindingOptions<G, J>) {
  const omit = options.omit ?? [];
  const graphic = computed<any>(() => {
    return toRaw(toValue(options.graphic));
  });

  const toJSON = () => options.toJSON(graphic.value, omit)!;

  const model = ref(toJSON());

  const fromJSON = () => {
    if (model.value) {
      return options.fromJSON(model.value, omit);
    }
  };
  // graphics发生变化时需要重载变动的属性
  // 由于可能是对象类型，所以调用JSON.stringify深度对比，避免无效变动导致UI重载
  useCzEventListener(
    () => graphic.value?.definitionChanged,
    throttle((_entity, field) => {
      if (omit.includes(field)) {
        return;
      }
      const newModel = toJSON() as any;
      const newValue = newModel?.[field];
      const preValue = toRaw(model.value?.[field]);
      if (JSON.stringify(newValue) != JSON.stringify(preValue)) {
        model.value[field] = newValue;
      }
    }, 10),
  );

  // 面板操作导致属性变化时，判断属性是否真正发生变化，然后写入graphics中
  // 两种情况
  // 1.属性为Cesium.Property：此时调用自身的equals进行对比
  // 2.属性为基础类型：直接进行变量对比
  watch(
    model,
    throttle((model) => {
      model = toRaw(model);
      const newGraphics = fromJSON() as any;
      Object.keys(model).forEach((key: any) => {
        if (omit.includes(key)) {
          return;
        }
        const newValue = newGraphics?.[key];
        const prevValue = toRaw(graphic.value?.[key]);
        if (graphic.value && !newValue?.equals?.(prevValue) && newValue !== prevValue) {
          graphic.value[key] = newValue;
        }
      });
    }, 5),
    { deep: true },
  );

  return model;
}
