<!-- 天际线分析 -->
<script lang="ts" setup>
import type { PostProcessStageCollection, PostProcessStageComposite } from 'cesium';

import { useTerrainCheck } from '@/hooks/useTerrainCheck';
import { use<PERSON>z<PERSON>iewer } from '@x3d/vue-hooks';

defineOptions({ name: 'SkylineAnalysis' });
const startup = ref(true);
const viewer = useCzViewer();
let silhouette: PostProcessStageComposite;
let skylineAnayStages: PostProcessStageCollection;
// const customWidth = ref(1);
// const edgeFs = `
//       uniform sampler2D depthTexture;
//       uniform float length;
//       uniform vec4 color;
//       varying vec2 v_textureCoordinates;
//       void main(void) {
//           float directions[3];
//           directions[0] = -1.0;
//           directions[1] = 0.0;
//           directions[2] = 1.0;
//           float scalars[3];
//           scalars[0] = 3.0;
//           scalars[1] = 10.0;
//           scalars[2] = 3.0;
//           float czm_pixelRatio_width = float(${customWidth.value});
//           float padx = czm_pixelRatio_width / czm_viewport.z;
//           float pady = czm_pixelRatio_width / czm_viewport.w;
//           #ifdef CZM_SELECTED_FEATURE
//               bool selected = false;
//               for (int i = 0; i < 3; ++i) {
//                   float dir = directions[i];
//                   selected = selected || czm_selected(vec2(-padx, dir * pady));
//                   selected = selected || czm_selected(vec2(padx, dir * pady));
//                   selected = selected || czm_selected(vec2(dir * padx, -pady));
//                   selected = selected || czm_selected(vec2(dir * padx, pady));
//                   if (selected) {
//                       break;
//                   }
//               }
//               if (!selected) {
//                   gl_FragColor = vec4(color.rgb, 0.0);
//                   return;
//               }
//           #endif
//           float horizEdge = 0.0;
//           float vertEdge = 0.0;
//           for (int i = 0; i < 3; ++i) {
//               float dir = directions[i];
//               float scale = scalars[i];
//               horizEdge -= texture2D(depthTexture, v_textureCoordinates + vec2(-padx, dir * pady)).x * scale;
//               horizEdge += texture2D(depthTexture, v_textureCoordinates + vec2(padx, dir * pady)).x * scale;
//               vertEdge -= texture2D(depthTexture, v_textureCoordinates + vec2(dir * padx, -pady)).x * scale;
//               vertEdge += texture2D(depthTexture, v_textureCoordinates + vec2(dir * padx, pady)).x * scale;
//           }
//           float len = sqrt(horizEdge * horizEdge + vertEdge * vertEdge);
//           gl_FragColor = vec4(color.rgb, len > length ? color.a : 0.0);
//       }`;
// 打开天际线分析
function openSkylineAnay() {
  if (skylineAnayStages) {
    silhouette.enabled = true;
    return;
  }
  skylineAnayStages = viewer.value.scene.postProcessStages;
  const edgeDetection = Cesium.PostProcessStageLibrary.createEdgeDetectionStage();
  // const edgeDetection = new Cesium.PostProcessStage({
  //   name: 'myEdgeDetection',
  //   fragmentShader: edgeFs,
  //   uniforms: {
  //     length: 0.5,
  //     color: Cesium.Color.BLACK,
  //   },
  // });
  const postProccessStage = new Cesium.PostProcessStage({
    fragmentShader:
      'uniform sampler2D colorTexture;'
      + 'uniform sampler2D depthTexture;'
      + 'in vec2 v_textureCoordinates;'
      + 'void main(void)'
      + '{'
      + 'float depth = czm_readDepth(depthTexture, v_textureCoordinates);'
      + 'vec4 color = texture(colorTexture, v_textureCoordinates);'
      + 'if(depth<1.0 - 0.000001){'
      + 'out_FragColor = color;'
      + '}'
      + 'else{'
      + 'out_FragColor = vec4(1.0,0.0,0.0,1.0);'
      + '}'
      + '}',
  });

  // PostProcessStage:要使用的片段着色器。默认sampler2D制服是colorTexture和depthTexture。
  const postProccesStage_1 = new Cesium.PostProcessStage({
    fragmentShader:
      'uniform sampler2D colorTexture;'
      + 'uniform sampler2D redTexture;'
      + 'uniform sampler2D silhouetteTexture;'
      + 'in vec2 v_textureCoordinates;'
      + 'void main(void)'
      + '{'
      + 'vec4 redcolor=texture(redTexture, v_textureCoordinates);'
      + 'vec4 silhouetteColor = texture(silhouetteTexture, v_textureCoordinates);'
      + 'vec4 color = texture(colorTexture, v_textureCoordinates);'
      + 'if(redcolor.r == 1.0){'
      + 'out_FragColor = mix(color, vec4(5.0,0.0,0.0,1.0), silhouetteColor.a);'
      + '}'
      + 'else{'
      + 'out_FragColor = color;'
      + '}'
      + '}',
    uniforms: {
      redTexture: postProccessStage.name,
      silhouetteTexture: edgeDetection.name,
    },
  });

  silhouette = new Cesium.PostProcessStageComposite({
    stages: [edgeDetection, postProccessStage, postProccesStage_1],
    inputPreviousStageTexture: false,
    uniforms: edgeDetection.uniforms,
  });
  skylineAnayStages.add(silhouette);
}

// 关闭天际线分析
function closeSkylineAnay() {
  silhouette.enabled = false;
}
watchImmediate(startup, (bool) => {
  bool ? openSkylineAnay() : closeSkylineAnay();
});
onUnmounted(() => {
  skylineAnayStages.remove(silhouette);
});

const terrainCheck = useTerrainCheck();
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="天际线分析"
    class="h-188px w-400px"
  >
    <el-text v-if="!terrainCheck" p="x-24px!" type="danger">
      未检测到存在地形，请打开图层勾选地形
    </el-text>
    <el-form mt="24px">
      <el-form-item label="天际线" :label-width="$vh(80)">
        <el-switch v-model="startup" />
      </el-form-item>
    </el-form>
  </drag-card>
</template>

<style lang="scss" scope>
.skyline-analysis {
  display: inline-block;
}
</style>
