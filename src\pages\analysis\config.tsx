import { ElIcon } from 'element-plus';
import BimFusionAnalysis from './components/bim-fusion-analysis/bim-fusion-analysis.vue';
import BimModelAnalysis from './components/bim-model-analysis/bim-model-analysis.vue';
import BufferAnalysis from './components/buffer-analysis/buffer-analysis.vue';
import ElevationAnalysis from './components/elevation-analysis/elevation-analysis.vue';
import InundationAnalysis from './components/inundation-analysis/inundation-analysis.vue';
// import PanoramaAnalysis from './components/panorama-analysis/panorama-analysis.vue';
import PassableAnalysis from './components/passable-analysis/passable-analysis.vue';
import ProfileAnalysis from './components/profile-analysis/profile-analysis.vue';
import ResettlementAnalysis from './components/resettlement-analysis/resettlement-analysis.vue';
import Roaming from './components/roaming/roaming.vue';
import ScreensCompare from './components/screens-compare/screens-compare.vue';
import ShadowAnalysis from './components/shadow-analysis/shadow-analysis.vue';
import SkylineAnalysis from './components/skyline-analysis/skyline-analysis.vue';
import SlopeAnalysis from './components/slope-analysis/slope-analysis.vue';
import SplitLayer from './components/split-layer/index.vue';
import SurfaceExcavation from './components/surface-excavation/surface-excavation.vue';
import TilesetFlatten from './components/tileset-flatten/tileset-flatten.vue';
import TinRoadAnalysis from './components/tin-road-analysis/tin-road-analysis.vue';
import TopologyAnalysis from './components/topology-analysis/topology-analysis.vue';
import VideoFusionAnalysis from './components/video-fusion-analysis/video-fusion-analysis.vue';
import ViewshedAnalysis from './components/viewshed-analysis/viewshed-analysis.vue';
import VisibilityAnalysis from './components/visibility-analysis/visibility-analysis.vue';
import WaterModelAnalysis from './components/water-model-analysis/water-model-analysis.vue';

export const checked = shallowReactive<Map<string, Component>>(new Map());

export const menuList = [
  {
    name: '对比分析',
    icon: () => <ElIcon class="i-custom:comparative-analysis" />,
    children: [
      {
        name: '卷帘分析',
        icon: () => <ElIcon class="i-custom:split-layer" />,
        component: SplitLayer,
        load: true,
      },
      {
        name: '多屏对比',
        icon: () => <ElIcon class="i-custom:screens-compare" />,
        component: ScreensCompare,
        load: false,
      },
    ],
  },
  {
    name: '空间分析',
    icon: () => <ElIcon class="i-custom:space-analysis" />,
    children: [
      {
        name: '天际线分析',
        icon: () => <ElIcon class="i-custom:sky-line" />,
        component: SkylineAnalysis,
        load: false,
      },
      {
        name: '通视分析',
        icon: () => <ElIcon class="i-custom:visibility-analysis" />,
        component: VisibilityAnalysis,
        load: true,
      },
      {
        name: '开敞度分析',
        icon: () => <ElIcon class="i-custom:open-analysis" />,
        load: false,
      },
      {
        name: '可视域分析',
        icon: () => <ElIcon class="i-custom:viewshed-analysis" />,
        component: ViewshedAnalysis,
        load: false,
      },
      {
        name: '阴影分析',
        component: ShadowAnalysis,
        load: false,
        icon: () => <ElIcon class="i-custom:shadow-analysis" />,
      },
      {
        name: '漫游',
        component: Roaming,
        icon: () => <ElIcon class="i-custom:shadow-analysis" />,
      },
    ],
  },
  {
    name: '大地分析',
    icon: () => <ElIcon class="i-custom:geodetic-analysis" />,
    children: [
      {
        name: '地表分析',
        icon: () => <ElIcon class="i-custom:surface-analysis" />,
        load: false,
      },
      {
        name: '地质分析',
        icon: () => <ElIcon class="i-custom:geological-analysis" />,
        load: false,
      },
      {
        name: '剖面分析',
        icon: () => <ElIcon class="i-custom:profile-analysis" />,
        component: ProfileAnalysis,
        load: true,
      },
      {
        name: '淹没分析',
        component: InundationAnalysis,
        icon: () => <ElIcon class="i-custom:inundation-analysis" />,
        load: true,
      },
      {
        name: '坡度坡向分析',
        icon: () => <ElIcon class="i-custom:slope-analysis" />,
        component: SlopeAnalysis,
        load: true,
      },
      {
        name: '等高线分析',
        component: ElevationAnalysis,
        icon: () => <ElIcon class="i-custom:elevation-analysis" />,
        load: true,
      },
      {
        name: '填挖方分析',
        icon: () => <ElIcon class="i-custom:surface-excavation" />,
        component: SurfaceExcavation,
        load: false,
      },
    ],
  },
  {
    name: '模型分析',
    icon: () => <ElIcon class="i-custom:model-analysis" />,
    children: [
      {
        name: '倾斜压平',
        icon: () => <ElIcon class="i-custom:tileset-flatten" />,
        component: TilesetFlatten,
        load: false,
      },
      {
        name: '拓扑分析',
        icon: () => <ElIcon class="i-custom:topology-analysis" />,
        component: TopologyAnalysis,
        load: true,
      },
    ],
  },
  {
    name: '专题分析',
    icon: () => <ElIcon class="i-custom:thematic-anaysis" />,
    children: [
      {
        name: '缓冲区分析',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: BufferAnalysis,
        load: true,
      },
      {
        name: '通行度分析',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: PassableAnalysis,
        load: true,
      },
      {
        name: '安置点选址',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: ResettlementAnalysis,
        load: true,
      },
      {
        name: '视频融合分析',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: VideoFusionAnalysis,
        load: true,
      },
      {
        name: '精模与水体',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: WaterModelAnalysis,
        load: true,
      },
      {
        name: 'BIM与倾斜摄影',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: BimModelAnalysis,
        load: true,
      },
      {
        name: 'BIM融合分析',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: BimFusionAnalysis,
        load: true,
      },
      {
        name: 'TIN地形与道路',
        icon: () => <ElIcon class="i-custom:buffer-analysis" />,
        component: TinRoadAnalysis,
        load: true,
      },
      // {
      //   name: '全景分析',
      //   icon: () => <ElIcon class="i-custom:buffer-analysis" />,
      //   component: PanoramaAnalysis,
      //   load: false,
      // },
    ],
  },
];
