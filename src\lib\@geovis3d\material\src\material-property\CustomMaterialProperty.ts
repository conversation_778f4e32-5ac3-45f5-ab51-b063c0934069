import type * as Cesium from 'cesium';

export abstract class CustomMaterialProperty implements Cesium.MaterialProperty {
  abstract fromJson(): any;

  abstract toJSON(): any;

  abstract isConstant: boolean;

  abstract definitionChanged: Cesium.Event<(...args: any[]) => void>;

  abstract getType(time: Cesium.JulianDate): string;

  abstract getValue(time: Cesium.JulianDate, result?: any): any;

  abstract equals(other?: Cesium.Property): boolean;
}
