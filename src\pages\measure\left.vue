<script lang="ts" setup>
import { menuList } from './assets/MenuList';

defineOptions({ name: 'Left' });

const show = ref(true);

const router = useRouter();
</script>

<template>
  <layout-left-panel>
    <div
      class="measure-sty"
      p="b-46px t-64px"
    >
      <div
        flex="~ col"
        h="100%"
        :w="show ? '400px' : '0'"
        transition="all 300"
        of="hidden"
      >
        <header-title1 b-b="1px! solid #fff/10%!">
          量测
          <template #extra>
            <el-button link>
              <el-icon
                class="i-material-symbols:close"
                text="20px! #FFF!"
                @click="router.push({ path: '/home' })"
              />
            </el-button>
          </template>
        </header-title1>
        <el-scrollbar p="x-16px y-12px" flex="1" of="auto">
          <el-menu :default-openeds="menuList.map((e) => e.name)">
            <el-sub-menu v-for="item in menuList" :key="item.name" :index="item.name">
              <template #title>
                <el-icon :class="item.icon" text="20px! #FFF!" />
                {{ item.name }}
              </template>
              <el-menu-item
                v-for="subitem in item.children"
                :key="subitem.name"
              >
                {{ subitem.name }}
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
      </div>
      <el-button link class="toggle-btn" @click="show = !show">
        <el-icon
          class="i-material-symbols:chevron-left"
          text="20px! #FFF!"
          transition="all 300"
          :rotate="show ? '0' : '180'"
          inline-block
        />
      </el-button>
    </div>
  </layout-left-panel>
</template>

<style lang="scss" scoped>
.measure-sty {
  position: relative;
  height: 100vh;
  pointer-events: auto;
  background: var(--el-bg-color);
}

.toggle-btn {
  position: absolute;
  top: 521px;
  left: 100%;
  z-index: 99;
  width: 24px;
  height: 56px;
  background: var(--el-bg-color) !important;
  border-radius: 0 4px 4px 0 !important;
}
</style>
