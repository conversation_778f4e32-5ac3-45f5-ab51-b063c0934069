<!-- 时间线播放组件 -->
<script lang="ts" setup>
import { useTimelinePlayer } from './use-timeline-player';

defineOptions({ name: 'TimelinePlayer' });

const { date, length, selectHour, prev, next, isPlaying, resumePlay, pausePlay, reset } = useTimelinePlayer();

const marks = Array.from({ length: length.value })
  .reduce((map: Record<string, any>, _current, index) => {
    map[index] = index < 10 ? `0${index}` : `${index}`;
    return map;
  }, {} as Record<string, any>);
</script>

<template>
  <div class="timeline-player">
    <div flex="~ justify-between">
      <div flex="~ items-center" text="18px!">
        <el-icon class="i-custom:timeline" mr="12px" text="22px!" />
        时间轴
      </div>
      <div class="absolute left-50%" translate-x="-50%" flex="~">
        <el-button
          class="player-button"
          text
          :disabled="selectHour <= 0"
          @click="prev()"
        >
          <el-icon class="i-tabler:player-skip-back-filled" />
        </el-button>
        <el-button
          class="player-button"
          :class="{ 'is-playing': isPlaying } "
          text
          @click="isPlaying ? pausePlay() : resumePlay()"
        >
          <el-icon v-if="isPlaying" class="i-tabler:player-pause-filled" />
          <el-icon v-else class="i-tabler:player-play-filled" />
        </el-button>
        <el-button
          class="player-button"
          text
          :disabled="selectHour >= length - 1"
          @click="next()"
        >
          <el-icon class="i-tabler:player-skip-forward-filled" />
        </el-button>
      </div>
      <div flex="~">
        <el-button
          title="重置"
          text
          size="small"
          un-text="20px!"
          h="32px!"
          @click="reset()"
        >
          <el-icon class="i-tabler:reload" />
        </el-button>
        <el-date-picker
          v-model="date"
          :clearable="false"
          class="w-154px!"
          format="YYYY年MM月DD日"
        />
      </div>
    </div>
    <el-slider
      v-model="selectHour"
      :show-tooltip="false"
      :step="1"
      :max="length - 1"
      :marks="marks"
      :show-stops="false"
    />
  </div>
</template>

<style lang="scss">
.timeline-player {
  position: absolute;
  bottom: 16px;
  left: 50%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 828px;
  height: 124px;
  padding: 12px 30px 4px;
  color: #e2f4ff;
  background: rgb(6 41 73 / 70%);
  border-radius: 2px;
  transform: translateX(-50%);

  .player-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    margin: 0 12px;
    font-size: 20px;
    color: #e2f4ff;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.2s;

    &:hover,
    &.is-playing {
      background: rgb(18 75 118 / 80%);
      box-shadow: inset 0 0 6px 2px #2f7eb0;
    }
  }

  .el-slider {
    --el-slider-height: 24px;
    --el-color-info: #d4eaf6;

    .el-slider__bar {
      background: none;
    }

    .el-slider__button {
      width: 3px;
      height: 26px;
      margin-top: 15px;
      background: #ffe85b;
      border: none;
      border-radius: 1px;

      :hover {
        transform: scale(1);
      }
    }

    .el-slider__runway {
      background: linear-gradient(270deg, rgb(75 129 206 / 5%) 0%, rgb(52 71 139 / 70%) 50%, rgb(75 129 206 / 5%) 100%);

      &::after {
        position: absolute;
        bottom: 40%;
        left: 50%;
        width: 100%;
        height: 1px;
        pointer-events: none;
        content: '';
        background: #5d80b6;
        transform: translateX(-50%);
      }
    }

    .el-slider__marks-text {
      font-size: 15px;
      transform: translateX(-50%) translateY(-38px);
    }

    .el-slider__marks-stop {
      background: none;

      &::before {
        position: absolute;
        bottom: 40%;
        left: 50%;
        width: 1px;
        height: 14px;
        pointer-events: none;
        content: '';
        background: #5d80b6;
        transform: translateX(-50%);
      }
    }
  }
}
</style>
