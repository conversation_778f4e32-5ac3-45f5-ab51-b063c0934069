import type { Component } from 'vue';

export const MaterialPropertyAttributeCollection = new Set<{
  label: string;
  type: string;
  component: Component;
}>();

const names: Record<string, string> = {
  CircleWaveMaterialProperty: '扩散波',
  ColorMaterialProperty: '纯色',
  ImageGifMaterialProperty: 'GIF图',
  ImageMaterialProperty: '图片',
  OilMaterialProperty: '溢油',
  PolylineArrowLinkMaterialProperty: '动态箭头线',
  PolylineAttackLinkMaterialProperty: '攻击特效线',
  PolylinePulseLinkMaterialProperty: '滋滋线',
  PolylineTrailLinkMaterialProperty: '追踪线',
  RedtideMaterialProperty: '赤潮',
  GreentideMaterialProperty: '绿潮',
  ChemicalMaterialProperty: '危化品',
};

// 自动载入
const glob = import.meta.glob('./*-property-attribute.vue', {
  eager: true,
  import: 'default',
});
Object.keys(glob).forEach((key: string) => {
  const component: any = glob[key];
  // 烤串转大写驼峰
  const name = key.replaceAll(
    /^\.\/(\w)((\w|-)+)-attribute\.vue$/g,
    ($0, $1: string, $2: string) => {
      return $1.toLocaleUpperCase() + $2.replaceAll(/-(\w)/g, ($0, $1) => $1.toLocaleUpperCase());
    },
  );

  MaterialPropertyAttributeCollection.add({
    label: names[name],
    type: name,
    component,
  });
});
