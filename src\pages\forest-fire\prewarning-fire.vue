<!-- 森林火情预警 -->
<script lang="ts" setup>
import type { components } from '@/genapi/analysis';
import { analysisForestGetFireDataByTimeUsingGet } from '@/genapi/analysis';

import { CzEntity } from '@x3d/all';
import { useCzEntities } from '@x3d/vue-hooks';
import PrewarningFirePopper from './prewarning-fire-popper.vue';

defineComponent({ name: 'PrewarningFire' });
const timeFormatter = 'YYYY-MM-DD HH:mm:ss';
const filterParams = reactive<{ city: string; name: string }>({
  city: '',
  name: '',
});

const shortcuts = [
  {
    text: '3小时',
    value: [
      dayjs().subtract(3, 'hour').format(timeFormatter),
      dayjs().format(timeFormatter),
    ] as [string, string],
  },
  {
    text: '6小时',
    value: [
      dayjs().subtract(6, 'hour').format(timeFormatter),
      dayjs().format(timeFormatter),
    ] as [string, string],
  },
  {
    text: '12小时',
    value: [
      dayjs().subtract(12, 'hour').format(timeFormatter),
      dayjs().format(timeFormatter),
    ] as [string, string],
  },
];
const dateRange = ref(shortcuts[0].value);
const selectedPoint = shallowRef<components['schemas']['ForestFireGroupByStationVoDuiXiang']>();
const { execute, state, isLoading } = useAsyncState(async () => {
  const { data } = await analysisForestGetFireDataByTimeUsingGet({
    params: {
      city: filterParams.city,
      name: filterParams.name,
      startDateTime: dateRange.value?.[0],
      endDateTime: dateRange.value?.[1],
    },
  });

  selectedPoint.value = undefined;
  return data;
}, undefined);

watch([dateRange, filterParams], () => {
  execute();
});

useCzEntities(() => {
  return state.value?.groupData?.map((item) => {
    const entity = new CzEntity({
      position: Cesium.Cartesian3.fromDegrees(+item.longitude!, +item.latitude!),
      point: {
        pixelSize: 10,
        outlineWidth: 2,
        color: Cesium.Color.RED,
      },
      label: {
        text: `${item.stationName?.slice(0, 6)}\n${item.stationName!.slice(6, item.stationName!.length)}`,
        scale: 0.5,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        fillColor: Cesium.Color.RED,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        pixelOffset: new Cesium.Cartesian2(0, -10),
      },
    });
    entity.event.on('LEFT_CLICK', () => {
      selectedPoint.value = item;
    });
    return entity;
  });
});
</script>

<template>
  <PrewarningFirePopper v-model="selectedPoint" />
</template>

<style scoped lang="scss">
.prewarning-fire {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.el-form {
  --el-form-label-font-size: 14px;

  .el-form-item {
    display: flex;
    align-items: center;
  }
}
</style>

<style lang="scss">
.el-select-dropdown {
  --el-font-size-base: 14px;
}
</style>
