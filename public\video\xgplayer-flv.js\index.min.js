!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("core-js/modules/es.array.concat.js"),require("xgplayer"),require("core-js/modules/es.regexp.exec.js"),require("core-js/modules/es.regexp.test.js"),require("core-js/modules/es.object.to-string.js"),require("core-js/modules/es.object.assign.js"),require("core-js/modules/web.dom-collections.for-each.js"),require("core-js/modules/es.object.keys.js"),require("core-js/modules/es.promise.js"),require("core-js/modules/es.array.iterator.js"),require("core-js/modules/es.string.iterator.js"),require("core-js/modules/web.dom-collections.iterator.js"),require("core-js/modules/web.url.js"),require("core-js/modules/web.url-search-params.js"),require("core-js/modules/es.array.slice.js"),require("core-js/modules/es.array.splice.js"),require("core-js/modules/es.function.name.js"),require("core-js/modules/es.array.map.js"),require("core-js/modules/es.json.stringify.js"),require("core-js/modules/es.array.join.js"),require("core-js/modules/es.array.includes.js"),require("core-js/modules/es.promise.finally.js"),require("core-js/modules/es.array.filter.js"),require("core-js/modules/es.typed-array.uint8-array.js"),require("core-js/modules/esnext.typed-array.at.js"),require("core-js/modules/es.typed-array.copy-within.js"),require("core-js/modules/es.typed-array.every.js"),require("core-js/modules/es.typed-array.fill.js"),require("core-js/modules/es.typed-array.filter.js"),require("core-js/modules/es.typed-array.find.js"),require("core-js/modules/es.typed-array.find-index.js"),require("core-js/modules/esnext.typed-array.find-last.js"),require("core-js/modules/esnext.typed-array.find-last-index.js"),require("core-js/modules/es.typed-array.for-each.js"),require("core-js/modules/es.typed-array.includes.js"),require("core-js/modules/es.typed-array.index-of.js"),require("core-js/modules/es.typed-array.iterator.js"),require("core-js/modules/es.typed-array.join.js"),require("core-js/modules/es.typed-array.last-index-of.js"),require("core-js/modules/es.typed-array.map.js"),require("core-js/modules/es.typed-array.reduce.js"),require("core-js/modules/es.typed-array.reduce-right.js"),require("core-js/modules/es.typed-array.reverse.js"),require("core-js/modules/es.typed-array.set.js"),require("core-js/modules/es.typed-array.slice.js"),require("core-js/modules/es.typed-array.some.js"),require("core-js/modules/es.typed-array.sort.js"),require("core-js/modules/es.typed-array.subarray.js"),require("core-js/modules/es.typed-array.to-locale-string.js"),require("core-js/modules/es.typed-array.to-string.js"),require("core-js/modules/es.string.replace.js"),require("core-js/modules/es.number.is-nan.js"),require("core-js/modules/es.number.constructor.js"),require("core-js/modules/es.object.get-prototype-of.js"),require("core-js/modules/es.symbol.js"),require("core-js/modules/es.string.trim.js"),require("core-js/modules/es.set.js"),require("core-js/modules/es.string.includes.js"),require("core-js/modules/es.number.is-finite.js"),require("core-js/modules/es.regexp.to-string.js"),require("core-js/modules/es.regexp.flags.js"),require("core-js/modules/es.string.pad-start.js"),require("core-js/modules/es.array.find.js"),require("core-js/modules/es.array.from.js"),require("core-js/modules/es.array-buffer.constructor.js"),require("core-js/modules/es.typed-array.int8-array.js"),require("core-js/modules/es.typed-array.uint8-clamped-array.js"),require("core-js/modules/es.typed-array.int16-array.js"),require("core-js/modules/es.typed-array.uint16-array.js"),require("core-js/modules/es.typed-array.int32-array.js"),require("core-js/modules/es.typed-array.uint32-array.js"),require("core-js/modules/es.typed-array.float32-array.js"),require("core-js/modules/es.typed-array.float64-array.js")):"function"==typeof define&&define.amd?define(["core-js/modules/es.array.concat.js","xgplayer","core-js/modules/es.regexp.exec.js","core-js/modules/es.regexp.test.js","core-js/modules/es.object.to-string.js","core-js/modules/es.object.assign.js","core-js/modules/web.dom-collections.for-each.js","core-js/modules/es.object.keys.js","core-js/modules/es.promise.js","core-js/modules/es.array.iterator.js","core-js/modules/es.string.iterator.js","core-js/modules/web.dom-collections.iterator.js","core-js/modules/web.url.js","core-js/modules/web.url-search-params.js","core-js/modules/es.array.slice.js","core-js/modules/es.array.splice.js","core-js/modules/es.function.name.js","core-js/modules/es.array.map.js","core-js/modules/es.json.stringify.js","core-js/modules/es.array.join.js","core-js/modules/es.array.includes.js","core-js/modules/es.promise.finally.js","core-js/modules/es.array.filter.js","core-js/modules/es.typed-array.uint8-array.js","core-js/modules/esnext.typed-array.at.js","core-js/modules/es.typed-array.copy-within.js","core-js/modules/es.typed-array.every.js","core-js/modules/es.typed-array.fill.js","core-js/modules/es.typed-array.filter.js","core-js/modules/es.typed-array.find.js","core-js/modules/es.typed-array.find-index.js","core-js/modules/esnext.typed-array.find-last.js","core-js/modules/esnext.typed-array.find-last-index.js","core-js/modules/es.typed-array.for-each.js","core-js/modules/es.typed-array.includes.js","core-js/modules/es.typed-array.index-of.js","core-js/modules/es.typed-array.iterator.js","core-js/modules/es.typed-array.join.js","core-js/modules/es.typed-array.last-index-of.js","core-js/modules/es.typed-array.map.js","core-js/modules/es.typed-array.reduce.js","core-js/modules/es.typed-array.reduce-right.js","core-js/modules/es.typed-array.reverse.js","core-js/modules/es.typed-array.set.js","core-js/modules/es.typed-array.slice.js","core-js/modules/es.typed-array.some.js","core-js/modules/es.typed-array.sort.js","core-js/modules/es.typed-array.subarray.js","core-js/modules/es.typed-array.to-locale-string.js","core-js/modules/es.typed-array.to-string.js","core-js/modules/es.string.replace.js","core-js/modules/es.number.is-nan.js","core-js/modules/es.number.constructor.js","core-js/modules/es.object.get-prototype-of.js","core-js/modules/es.symbol.js","core-js/modules/es.string.trim.js","core-js/modules/es.set.js","core-js/modules/es.string.includes.js","core-js/modules/es.number.is-finite.js","core-js/modules/es.regexp.to-string.js","core-js/modules/es.regexp.flags.js","core-js/modules/es.string.pad-start.js","core-js/modules/es.array.find.js","core-js/modules/es.array.from.js","core-js/modules/es.array-buffer.constructor.js","core-js/modules/es.typed-array.int8-array.js","core-js/modules/es.typed-array.uint8-clamped-array.js","core-js/modules/es.typed-array.int16-array.js","core-js/modules/es.typed-array.uint16-array.js","core-js/modules/es.typed-array.int32-array.js","core-js/modules/es.typed-array.uint32-array.js","core-js/modules/es.typed-array.float32-array.js","core-js/modules/es.typed-array.float64-array.js"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).FlvPlayer=t(null,e.Player)}(this,(function(e,t){"use strict";function r(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function n(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){d(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function i(){i=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},a=s.iterator||"@@iterator",o=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(D){c=function(e,t,r){return e[t]=r}}function l(e,t,r,i){var s=t&&t.prototype instanceof f?t:f,a=Object.create(s.prototype),o=new j(i||[]);return n(a,"_invoke",{value:x(e,r,o)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(D){return{type:"throw",arg:D}}}e.wrap=l;var h={};function f(){}function p(){}function v(){}var y={};c(y,a,(function(){return this}));var _=Object.getPrototypeOf,m=_&&_(_(T([])));m&&m!==t&&r.call(m,a)&&(y=m);var g=v.prototype=f.prototype=Object.create(y);function b(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function k(e,t){function i(n,s,a,o){var u=d(e[n],e,s);if("throw"!==u.type){var c=u.arg,l=c.value;return l&&"object"==typeof l&&r.call(l,"__await")?t.resolve(l.__await).then((function(e){i("next",e,a,o)}),(function(e){i("throw",e,a,o)})):t.resolve(l).then((function(e){c.value=e,a(c)}),(function(e){return i("throw",e,a,o)}))}o(u.arg)}var s;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){i(e,r,t,n)}))}return s=s?s.then(n,n):n()}})}function x(e,t,r){var n="suspendedStart";return function(i,s){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw s;return{value:void 0,done:!0}}for(r.method=i,r.arg=s;;){var a=r.delegate;if(a){var o=w(a,r);if(o){if(o===h)continue;return o}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=d(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===h)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function w(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var i=d(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,h;var s=i.arg;return s?s.done?(t[e.resultName]=s.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):s:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function T(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:A}}function A(){return{value:void 0,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=c(v,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,c(e,u,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},b(k.prototype),c(k.prototype,o,(function(){return this})),e.AsyncIterator=k,e.async=function(t,r,n,i,s){void 0===s&&(s=Promise);var a=new k(l(t,r,n,i),s);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},b(g),c(g,u,"Generator"),c(g,a,(function(){return this})),c(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=T,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],a=s.completion;if("root"===s.tryLoc)return n("end");if(s.tryLoc<=this.prev){var o=r.call(s,"catchLoc"),u=r.call(s,"finallyLoc");if(o&&u){if(this.prev<s.catchLoc)return n(s.catchLoc,!0);if(this.prev<s.finallyLoc)return n(s.finallyLoc)}else if(o){if(this.prev<s.catchLoc)return n(s.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return n(s.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var a=s?s.completion:{};return a.type=e,a.arg=t,s?(this.method="next",this.next=s.finallyLoc,h):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),E(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;E(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:T(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),h}},e}function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function a(e,t,r,n,i,s,a){try{var o=e[s](a),u=o.value}catch(c){return void r(c)}o.done?t(u):Promise.resolve(u).then(n,i)}function o(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var s=e.apply(t,r);function o(e){a(s,n,i,o,u,"next",e)}function u(e){a(s,n,i,o,u,"throw",e)}o(void 0)}))}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,A(n.key),n)}}function l(e,t,r){return t&&c(e.prototype,t),r&&c(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function d(e,t,r){return(t=A(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function h(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}function f(e){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function p(e,t){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function v(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function y(e,t,r){return(y=v()?Reflect.construct.bind():function(e,t,r){var n=[null];n.push.apply(n,t);var i=new(Function.bind.apply(e,n));return r&&p(i,r.prototype),i}).apply(null,arguments)}function _(e){var t="function"==typeof Map?new Map:void 0;return _=function(e){if(null===e||(r=e,-1===Function.toString.call(r).indexOf("[native code]")))return e;var r;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return y(e,arguments,f(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),p(n,e)},_(e)}function m(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function g(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function b(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return g(e)}function k(e){var t=v();return function(){var r,n=f(e);if(t){var i=f(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return b(this,r)}}function x(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=f(e)););return e}function w(){return w="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=x(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(arguments.length<3?e:r):i.value}},w.apply(this,arguments)}function S(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,s,a,o=[],u=!0,c=!1;try{if(s=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=s.call(r)).done)&&(o.push(n.value),o.length!==t);u=!0);}catch(l){c=!0,i=l}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw i}}return o}}(e,t)||j(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e){return function(e){if(Array.isArray(e))return T(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||j(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e,t){if(e){if("string"==typeof e)return T(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?T(e,t):void 0}}function T(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function A(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function D(){var e,t,r=new Promise((function(r,n){e=r,t=n}));return r.used=!1,r.resolve=function(){return r.used=!0,e.apply(void 0,arguments)},r.reject=function(){return r.used=!0,t.apply(void 0,arguments)},r}function L(){try{return parseInt(performance.now(),10)}catch(e){return(new Date).getTime()}}var O,C=function(){function e(){u(this,e)}return l(e,null,[{key:"start",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6||1===e.length&&e.start(0)<0?0:e.start(0):0}},{key:"end",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6?0:e.end(e.length-1):0}},{key:"get",value:function(e){if(e)try{return e.buffered}catch(t){}}},{key:"buffers",value:function(e,t){if(!e||!e.length)return[];for(var r=[],n=0,i=e.length;n<i;n++){var s=r.length;if(s&&t){var a=r[s-1],o=a[1];if(e.start(n)-o<=t){var u=e.end(n);u>o&&(a[1]=u)}else r.push([e.start(n),e.end(n)])}else r.push([e.start(n),e.end(n)])}return r}},{key:"totalLength",value:function(e){return e&&e.length?e.reduce((function(e,t){return e+(t[1]-t[0])}),0):0}},{key:"info",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!t||!t.length)return{start:0,end:0,buffers:[]};for(var i=0,s=0,a=0,o=0,u=0,c=0,l=0,d=e.buffers(t,n),h=0,f=d.length;h<f;h++){var p=d[h];if(r+n>=p[0]&&r<=p[1])i=p[0],s=p[1],a=h;else{if(r+n<p[0]){o=p[0],u=p[1];break}r+n>p[1]&&(c=p[0],l=p[1])}}return{start:i,end:s,index:a,buffers:d,nextStart:o,nextEnd:u,prevStart:c,prevEnd:l,currentTime:r,behind:r-i,remaining:s?s-r:0,length:e.totalLength&&e.totalLength(d)}}}]),e}(),R="network",B="network_timeout",P="network_forbidden",U="network_notfound",M="network_range_not_satisfiable",I="demux",F="remux",N="media",q="drm",V="other",z="runtime",G={FLV:"FLV",HLS:"HLS",MP4:"MP4",FMP4:"FMP4",MSE_ADD_SB:"MSE_ADD_SB",MSE_APPEND_BUFFER:"MSE_APPEND_BUFFER",MSE_OTHER:"MSE_OTHER",MSE_FULL:"MSE_FULL",OPTION:"OPTION",DASH:"DASH",LICENSE:"LICENSE",CUSTOM_LICENSE:"CUSTOM_LICENSE",MSE_HIJACK:"MSE_HIJACK",EME_HIJACK:"EME_HIJACK",SIDX:"SIDX",NO_CANPLAY_ERROR:"NO_CANPLAY_ERROR",BUFFERBREAK_ERROR:"BUFFERBREAK_ERROR",WAITING_TIMEOUT_ERROR:"WAITING_TIMEOUT_ERROR",MEDIA_ERR_ABORTED:"MEDIA_ERR_ABORTED",MEDIA_ERR_NETWORK:"MEDIA_ERR_NETWORK",MEDIA_ERR_DECODE:"MEDIA_ERR_DECODE",MEDIA_ERR_SRC_NOT_SUPPORTED:"MEDIA_ERR_SRC_NOT_SUPPORTED",MEDIA_ERR_CODEC_NOT_SUPPORTED:"MEDIA_ERR_CODEC_NOT_SUPPORTED",MEDIA_ERR_URL_EMPTY:"MEDIA_ERR_URL_EMPTY"},H=(d(O={},"manifest",{HLS:1100,DASH:1200}),d(O,R,2100),d(O,B,2101),d(O,P,2103),d(O,U,2104),d(O,M,2116),d(O,I,{FLV:3100,HLS:3200,MP4:3300,FMP4:3400,SIDX:3410}),d(O,F,{FMP4:4100,MP4:4200}),d(O,N,{MEDIA_ERR_ABORTED:5101,MEDIA_ERR_NETWORK:5102,MEDIA_ERR_DECODE:5103,MEDIA_ERR_SRC_NOT_SUPPORTED:5104,MEDIA_ERR_CODEC_NOT_SUPPORTED:5105,MEDIA_ERR_URL_EMPTY:5106,MSE_ADD_SB:5200,MSE_APPEND_BUFFER:5201,MSE_OTHER:5202,MSE_FULL:5203,MSE_HIJACK:5204,EME_HIJACK:5301}),d(O,q,{LICENSE:7100,CUSTOM_LICENSE:7200}),d(O,V,8e3),d(O,z,{NO_CANPLAY_ERROR:9001,BUFFERBREAK_ERROR:9002,WAITING_TIMEOUT_ERROR:9003}),O),K=function(e){h(r,e);var t=k(r);function r(e,n,i,s,a){var o;return u(this,r),(o=t.call(this,a||(null==i?void 0:i.message))).errorType=e===B?R:e,o.originError=i,o.ext=s,o.errorCode=H[e][n]||H[e],o.errorMessage=o.message,o.errorCode||(o.errorType=V,o.errorCode=H[o.errorType]),o}return l(r,null,[{key:"create",value:function(e,t,n,i,s){return e instanceof r?e:(e instanceof Error&&(n=e,e=""),e||(e=V),new r(e,t,n,i,s))}},{key:"network",value:function(e){var t;return new r(null!=e&&e.isTimeout?B:R,null,e instanceof Error?e:null,{url:null==e?void 0:e.url,response:null==e?void 0:e.response,httpCode:null==e||null===(t=e.response)||void 0===t?void 0:t.status})}}]),r}(_(Error)),W="undefined"!=typeof window,Q={DEBUG:1,LOG:2,WARN:3,ERROR:4},Y=["Boolean","Number","String","Undefined","Null","Date","Object"],X=function(){function e(t,r){u(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),this.logCacheLevel=(null==r?void 0:r.logCacheLevel)||3,this.logMaxSize=(null==r?void 0:r.logMaxSize)||204800,this.logSize=0,this.logTextArray=[]}return l(e,[{key:"debug",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[Q.DEBUG].concat(n)),e.disabled||(t=console).debug.apply(t,[this._prefix,J()].concat(n))}},{key:"log",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[Q.LOG].concat(n)),e.disabled||(t=console).log.apply(t,[this._prefix,J()].concat(n))}},{key:"warn",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[Q.WARN].concat(n)),e.disabled||(t=console).warn.apply(t,[this._prefix,J()].concat(n))}},{key:"error",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[Q.ERROR].concat(n)),e.disabled||(t=console).error.apply(t,[this._prefix,J()].concat(n))}},{key:"logCache",value:function(e){if(!(e<this.logCacheLevel)){var t="";try{for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];var s=n.map((function(e){return $(e)}));t=this._prefix+J()+JSON.stringify(s)}catch(o){return}if(e>=this.logCacheLevel&&(this.logSize+=t.length,this.logTextArray.push(t)),this.logSize>this.logMaxSize){var a=this.logTextArray.shift();this.logSize-=a.length}}}},{key:"getLogCache",value:function(){var e=this.logTextArray.join("\n");return this.reset(),e}},{key:"reset",value:function(){this.logTextArray=[],this.logSize=0}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}},{key:"setLogLevel",value:function(e){this.logCacheLevel=e}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();function J(){return(new Date).toLocaleString()}function Z(e){if("object"!==s(e))return e;var t=Object.prototype.toString.call(e).slice(8,-1);switch(t){case"Array":case"Uint8Array":case"ArrayBuffer":return t+"["+e.length+"]";case"Object":return"{}";default:return t}}function $(e,t,r){r||(r=1),t||(t=2);var n={};if(!e||"object"!==s(e))return e;var i=Object.prototype.toString.call(e).slice(8,-1);if(!Y.includes(i))return i;if(!(r>t)){for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(r===t?n[a]=Z(e[a]):"object"===s(e[a])?n[a]=$(e[a],t,r+1):n[a]=e[a]);return n}}function ee(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];try{return W?e&&"undefined"!=typeof ManagedMediaSource?ManagedMediaSource:window.MediaSource:null}catch(t){}}function te(e){return/ManagedMediaSource/gi.test(Object.prototype.toString.call(e))}d(X,"disabled",!0);var re="appendBuffer",ne="removeBuffer",ie="updateDuration",se=function(){function e(t,r){var n=this;u(this,e),d(this,"media",null),d(this,"mediaSource",null),d(this,"_openPromise",D()),d(this,"_queue",Object.create(null)),d(this,"_sourceBuffer",Object.create(null)),d(this,"_mseFullFlag",{}),d(this,"_st",0),d(this,"_opst",0),d(this,"_logger",null),d(this,"_config",null),d(this,"_url",null),d(this,"_onStartStreaming",(function(){n._logger.debug("startstreaming")})),d(this,"_onEndStreaming",(function(){n._logger.debug("endstreaming")})),d(this,"_onSBUpdateEnd",(function(e){var t=n._queue[e];if(t){var r=t[0];if((null==r?void 0:r.opName)!==ie&&t.shift(),r){var i=L()-n._opst;n._logger.debug("UpdateEnd",r.opName,i,r.context),r.promise.resolve({name:r.opName,context:r.context,costtime:i}),n._startQueue(e)}}})),d(this,"_onSBUpdateError",(function(e,t){var r=n._queue[e];if(r){var i=r[0];i&&(n._logger.error("UpdateError",e,i.opName,i.context),i.promise.reject(new K(N,G.MSE_APPEND_BUFFER,t)))}})),this._config=Object.assign(e.getDefaultConfig(),r),t&&this.bindMedia(t),this._logger=new X("MSE"),this._config.openLog&&X.enable()}var t,r,n;return l(e,[{key:"isOpened",get:function(){var e;return"open"===(null===(e=this.mediaSource)||void 0===e?void 0:e.readyState)}},{key:"url",get:function(){return this._url}},{key:"duration",get:function(){var e;return(null===(e=this.mediaSource)||void 0===e?void 0:e.duration)||-1}},{key:"isEnded",get:function(){return!!this.mediaSource&&"ended"===this.mediaSource.readyState}},{key:"streaming",get:function(){return!te(this.mediaSource)||this.mediaSource.streaming}},{key:"isFull",value:function(t){return t?this._mseFullFlag[t]:this._mseFullFlag[e.VIDEO]}},{key:"updateDuration",value:function(e){var t=this,r=this.mediaSource&&this.mediaSource.duration>e;if(this.mediaSource&&this.mediaSource.duration>e){var n=0;if(Object.keys(this._sourceBuffer).forEach((function(e){try{n=Math.max(t.bufferEnd(e)||0,n)}catch(r){}})),e<n)return Promise.resolve()}return this._enqueueBlockingOp((function(){t.isEnded?t._logger.debug("setDuration but ended"):t.mediaSource&&(t.mediaSource.duration=e,t._logger.debug("setDuration",e))}),ie,{isReduceDuration:r})}},{key:"open",value:function(){var e=this;if(this._openPromise.used&&!this.isOpened&&this.mediaSource){var t=this.mediaSource;t.addEventListener("sourceopen",(function r(){var n=L()-e._st;e._logger.debug("sourceopen",n),t.removeEventListener("sourceopen",r),e._openPromise.resolve({costtime:n})})),this._openPromise=D()}return this._openPromise}},{key:"bindMedia",value:(n=o(i().mark((function e(t){var r,n,s,a,o=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.mediaSource&&!this.media){e.next=3;break}return e.next=3,this.unbindMedia();case 3:if(r=ee(this._config.preferMMS),t&&r){e.next=6;break}throw new Error("Param media or MediaSource does not exist");case 6:return this.media=t,n=this.mediaSource=new r,s=te(n),this._st=L(),a=function e(){var r=L()-o._st;o._logger.debug("sourceopen"),n.removeEventListener("sourceopen",e),URL.revokeObjectURL(t.src),o._openPromise.resolve({costtime:r})},n.addEventListener("sourceopen",a),s&&(n.addEventListener("startstreaming",this._onStartStreaming),n.addEventListener("endstreaming",this._onEndStreaming)),this._url=URL.createObjectURL(n),t.src=this._url,t.disableRemotePlayback=s,e.abrupt("return",this._openPromise);case 17:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"unbindMedia",value:(r=o(i().mark((function e(){var t,r,n,s=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._openPromise.used||this._openPromise.resolve(),t=this.mediaSource){if(Object.keys(this._queue).forEach((function(e){var t=s._queue[e];t&&t.forEach((function(e){var t,r;return null===(t=e.promise)||void 0===t||null===(r=t.resolve)||void 0===r?void 0:r.call(t)}))})),r=!!this.media&&this.media.readyState>=1,n="open"===t.readyState,r&&n)try{t.endOfStream()}catch(i){}Object.keys(this._sourceBuffer).forEach((function(e){try{t.removeSourceBuffer(s._sourceBuffer[e])}catch(i){}})),te(t)&&(t.removeEventListener("startstreaming",this._onStartStreaming),t.removeEventListener("endstreaming",this._onEndStreaming))}if(this.media){this.media.disableRemotePlayback=!1,this.media.removeAttribute("src");try{this.media.load()}catch(i){}this.media=null}this.mediaSource=null,this._openPromise=D(),this._queue=Object.create(null),this._sourceBuffer=Object.create(null);case 8:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"createSource",value:function(e,t){if(!this._sourceBuffer[e]&&this.mediaSource){var r;try{r=this._sourceBuffer[e]=this.mediaSource.addSourceBuffer(t)}catch(n){throw new K(N,G.MSE_ADD_SB,n)}r.mimeType=t,r.addEventListener("updateend",this._onSBUpdateEnd.bind(this,e)),r.addEventListener("error",this._onSBUpdateError.bind(this,e))}}},{key:"changeType",value:function(e,t){var r=this,n=this._sourceBuffer[e];return this.mediaSource&&n&&n.mimeType!==t?"function"!=typeof n.changeType?Promise.reject():this._enqueueOp(e,(function(){n.changeType(t),n.mimeType=t,r._onSBUpdateEnd(e)}),"changeType",{mimeType:t}):Promise.resolve()}},{key:"createOrChangeSource",value:function(e,t){return this.createSource(e,t),this.changeType(e,t)}},{key:"append",value:function(e,t,r){var n=this;return t&&t.byteLength&&this._sourceBuffer[e]?this._enqueueOp(e,(function(){var i;n.mediaSource&&!n.media.error&&(n._logger.debug("MSE APPEND START",r),n._opst=L(),null===(i=n._sourceBuffer[e])||void 0===i||i.appendBuffer(t))}),re,r):Promise.resolve()}},{key:"remove",value:function(e,t,r,n){var i=this,s=!1;return this._mseFullFlag[e]&&(s=!0),this._enqueueOp(e,(function(){if(i.mediaSource&&!i.media.error){var s=i._sourceBuffer[e];t>=r||!s?i._onSBUpdateEnd(e):(i._opst=L(),i._logger.debug("MSE REMOVE START",e,t,r,n),s.remove(t,r))}}),ne,n,s)}},{key:"clearBuffer",value:function(e,t){var r,n=this;return Object.keys(this._sourceBuffer).forEach((function(i){r=n.remove(i,e,t)})),r||Promise.resolve()}},{key:"clearAllBuffer",value:function(){var e,t=this;return Object.keys(this._sourceBuffer).forEach((function(r){var n=t._sourceBuffer[r];e=t.remove(r,0,C.end(C.get(n)))})),e}},{key:"clearOpQueues",value:function(e,t){var r;this._logger.debug("MSE clearOpQueue START");var n=this._queue[e];if(t&&n)this._queue[e]=[];else if(n&&n[e]&&!(n.length<5)){var i=[];n.forEach((function(e){e.context&&e.context.isinit&&i.push(e)})),this._queue[e]=n.slice(0,2),i.length>0&&(r=this._queue[e]).push.apply(r,i)}}},{key:"endOfStream",value:function(e){var t=this;return this.mediaSource&&"open"===this.mediaSource.readyState?this._enqueueBlockingOp((function(){var r=t.mediaSource;r&&"open"===r.readyState&&(t._logger.debug("MSE endOfStream START"),e?r.endOfStream(e):r.endOfStream())}),"endOfStream"):Promise.resolve()}},{key:"setLiveSeekableRange",value:function(e,t){var r=this.mediaSource;e<0||t<e||null==r||!r.setLiveSeekableRange||"open"!==r.readyState||r.setLiveSeekableRange(e,t)}},{key:"getSourceBuffer",value:function(e){return this._sourceBuffer[e]}},{key:"buffered",value:function(e){return C.get(this._sourceBuffer[e])}},{key:"bufferStart",value:function(e){return C.start(this.buffered(e))}},{key:"bufferEnd",value:function(e){return C.end(this.buffered(e))}},{key:"_enqueueOp",value:function(e,t,r,n,i){var s=this;if(!this.mediaSource)return Promise.resolve();var a=this._queue[e]=this._queue[e]||[],o={exec:t,promise:D(),opName:r,context:n};return i?(a.splice(0,0,o),this._mseFullFlag[e]=!1,this._startQueue(e)):a.push(o),this.isOpened||this.isEnded?1===a.length&&this._startQueue(e):this._openPromise.then((function(){1===a.length&&s._startQueue(e)})),o.promise}},{key:"_enqueueBlockingOp",value:(t=o(i().mark((function e(t,r,n){var s,a,o=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mediaSource){e.next=2;break}return e.abrupt("return",Promise.resolve());case 2:if((s=Object.keys(this._sourceBuffer)).length){e.next=5;break}return e.abrupt("return",t());case 5:return a=[],s.forEach((function(e){var t=o._queue[e],i=D();a.push(i),t.push({exec:function(){i.resolve()},promise:i,opName:r,context:n}),1===t.length&&o._startQueue(e)})),e.abrupt("return",Promise.all(a).then((function(){try{return t()}finally{s.forEach((function(e){var t=o._queue[e],r=o._sourceBuffer[e];null==t||t.shift(),r&&r.updating||o._startQueue(e)}))}})));case 8:case"end":return e.stop()}}),e,this)}))),function(e,r,n){return t.apply(this,arguments)})},{key:"_startQueue",value:function(e){var t=this._queue[e];if(t){var r=t[0];if(r&&!this._mseFullFlag[e])try{r.exec()}catch(n){n&&n.message&&n.message.indexOf("SourceBuffer is full")>=0?(this._mseFullFlag[e]=!0,this._logger.error("[MSE error],  context,",r.context," ,name,",r.opName,",err,SourceBuffer is full"),r.promise.reject(new K(N,G.MSE_FULL,n))):(this._logger.error(n),r.promise.reject(new K(N,G.MSE_OTHER,n)),t.shift(),this._startQueue(e))}}}},{key:"setTimeoffset",value:function(e,t,r){var n=this;return this._enqueueOp(e,(function(){t<0&&(t+=.001),n._sourceBuffer[e].timestampOffset=t,n._onSBUpdateEnd(e)}),"setTimeoffset",r)}},{key:"abort",value:function(e,t){var r=this;return this.isOpened?this._enqueueOp(e,(function(){r._sourceBuffer[e].abort(),r._onSBUpdateEnd(e)}),"abort",t):Promise.resolve()}}],[{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"',t=ee();if(!t)return!1;try{return t.isTypeSupported(e)}catch(r){return this._logger.error(e,r),!1}}},{key:"getDefaultConfig",value:function(){return{openLog:!1,preferMMS:!1}}}]),e}();d(se,"VIDEO","video"),d(se,"AUDIO","audio");var ae="fetch",oe="xhr",ue="arraybuffer",ce="text",le="json",de=function(e){h(r,e);var t=k(r);function r(e,n,i,s){var a;return u(this,r),d(g(a=t.call(this,s)),"retryCount",0),d(g(a),"isTimeout",!1),d(g(a),"loaderType",ae),d(g(a),"startTime",0),d(g(a),"endTime",0),d(g(a),"options",{}),a.url=e,a.request=n,a.response=i,a}return l(r)}(_(Error)),he=Object.prototype.toString;function fe(e){if("[object Object]"!==he.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function pe(e){if(e&&null!==e[0]&&void 0!==e[0]&&(0!==e[0]||null!==e[1]&&void 0!==e[1])){var t="bytes="+e[0]+"-";return e[1]&&(t+=e[1]),t}}function ve(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ye(e,t){if(e){if(!t)return e;var r,n=Object.keys(t).map((function(e){if(null!=(r=t[e]))return Array.isArray(r)?e+="[]":r=[r],r.map((function(t){var r;return r=t,"[object Date]"===he.call(r)?t=t.toISOString():function(e){return null!==e&&"object"===s(e)}(t)&&(t=JSON.stringify(t)),"".concat(ve(e),"=").concat(ve(t))})).join("&")})).filter(Boolean).join("&");if(n){var i=e.indexOf("#");-1!==i&&(e=e.slice(0,i)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}}function _e(e,t,r,n,i,s,a,o,u,c,l){return i=null!=i?parseFloat(i):null,n=parseInt(n||"0",10),Number.isNaN(n)&&(n=0),{data:e,done:t,options:{range:u,vid:c,index:o,contentLength:n,age:i,startTime:s,firstByteTime:a,endTime:Date.now(),priOptions:l},response:r}}function me(e,t){return Math.round(8*e*1e3/t/1024)}var ge="error",be="core.ttfb",ke="core.loadstart",xe="core.loadresponseheaders",we="core.loadcomplete",Se="core.loadretry",Ee="core.sourcebuffercreated",je="core.analyzedurationexceeded",Te="core.removebuffer",Ae="core.buffereos",De="core.keyframe",Le="core.metadataparsed",Oe="core.sei",Ce="core.seiintime",Re="core.flvscriptdata",Be="core.lowdecode",Pe="core.switchurlsuccess",Ue="core.switchurlfailed",Me="core.demuxedtrack",Ie="core.streamexception",Fe="LARGE_AV_FIRST_FRAME_GAP_DETECT",Ne="LARGE_VIDEO_DTS_GAP_DETECT",qe="LARGE_AUDIO_DTS_GAP_DETECT",Ve="AUDIO_GAP_DETECT",ze="AUDIO_OVERLAP_DETECT",Ge="MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT",He="real_time_speed",Ke={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,n,s,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var o=new i(n,s||e,a),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],o]:e._events[u].push(o):(e._events[u]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,s=n.length,a=new Array(s);i<s;i++)a[i]=n[i].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var u,c,l=this._events[o],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,i),!0;case 5:return l.fn.call(l.context,t,n,i,s),!0;case 6:return l.fn.call(l.context,t,n,i,s,a),!0}for(c=1,u=new Array(d-1);c<d;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var h,f=l.length;for(c=0;c<f;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),d){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,n);break;case 4:l[c].fn.call(l[c].context,t,n,i);break;default:if(!u)for(h=1,u=new Array(d-1);h<d;h++)u[h-1]=arguments[h];l[c].fn.apply(l[c].context,u)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||a(this,s);else{for(var u=0,c=[],l=o.length;u<l;u++)(o[u].fn!==t||i&&!o[u].once||n&&o[u].context!==n)&&c.push(o[u]);c.length?this._events[s]=1===c.length?c[0]:c:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o}(Ke);var We=Ke.exports,Qe=2097152,Ye=function(e){h(n,e);var t,r=k(n);function n(){var e;return u(this,n),d(g(e=r.call(this)),"_abortController",null),d(g(e),"_timeoutTimer",null),d(g(e),"_reader",null),d(g(e),"_response",null),d(g(e),"_aborted",!1),d(g(e),"_index",-1),d(g(e),"_range",null),d(g(e),"_receivedLength",0),d(g(e),"_running",!1),d(g(e),"_logger",null),d(g(e),"_vid",""),d(g(e),"_onProcessMinLen",0),d(g(e),"_onCancel",null),d(g(e),"_priOptions",null),e}return l(n,[{key:"load",value:function(e){var t,r=this,n=e.url,s=e.vid,a=e.timeout,u=e.responseType,c=e.onProgress,l=e.index,d=e.onTimeout,h=e.onCancel,f=e.range,p=e.transformResponse,v=e.request,y=e.params,_=e.logger,m=e.method,g=e.headers,b=e.body,k=e.mode,x=e.credentials,w=e.cache,S=e.redirect,E=e.referrer,j=e.referrerPolicy,T=e.onProcessMinLen,A=e.priOptions;this._logger=_,this._aborted=!1,this._onProcessMinLen=T,this._onCancel=h,this._abortController="undefined"!=typeof AbortController&&new AbortController,this._running=!0,this._index=l,this._range=f||[0,0],this._vid=s||n,this._priOptions=A||{};var D={method:m,headers:g,body:b,mode:k,credentials:x,cache:w,redirect:S,referrer:E,referrerPolicy:j,signal:null===(t=this._abortController)||void 0===t?void 0:t.signal},L=!1;clearTimeout(this._timeoutTimer),n=ye(n,y);var O=pe(f);O&&(g=v?v.headers:D.headers=D.headers||(Headers?new Headers:{}),Headers&&g instanceof Headers?g.append("Range",O):g.Range=O),a&&(this._timeoutTimer=setTimeout((function(){if(L=!0,r.cancel(),d){var e=new de(n,D,null,"timeout");e.isTimeout=!0,d(e,{index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions})}}),a));var C=Date.now();return this._logger.debug("[fetch load start], index,",l,",range,",f),new Promise((function(e,t){fetch(v||n,v?void 0:D).then(function(){var s=o(i().mark((function s(a){var o,d,h,v;return i().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(clearTimeout(r._timeoutTimer),r._response=a,!r._aborted&&r._running){i.next=4;break}return i.abrupt("return");case 4:if(p&&(a=p(a,n)||a),a.ok){i.next=7;break}throw new de(n,D,a,"bad network response");case 7:if(o=Date.now(),u!==ce){i.next=15;break}return i.next=11,a.text();case 11:d=i.sent,r._running=!1,i.next=37;break;case 15:if(u!==le){i.next=22;break}return i.next=18,a.json();case 18:d=i.sent,r._running=!1,i.next=37;break;case 22:if(!c){i.next=29;break}return r.resolve=e,r.reject=t,r._loadChunk(a,c,C,o),i.abrupt("return");case 29:return i.next=31,a.arrayBuffer();case 31:d=i.sent,d=new Uint8Array(d),r._running=!1,h=Date.now()-C,v=me(d.byteLength,h),r.emit(He,{speed:v,len:d.byteLength,time:h,vid:r._vid,index:r._index,range:r._range,priOptions:r._priOptions});case 37:r._logger.debug("[fetch load end], index,",l,",range,",f),e(_e(d,!0,a,a.headers.get("Content-Length"),a.headers.get("age"),C,o,l,f,r._vid,r._priOptions));case 39:case"end":return i.stop()}}),s)})));return function(e){return s.apply(this,arguments)}}()).catch((function(e){var i;clearTimeout(r._timeoutTimer),r._running=!1,r._aborted&&!L||((e=e instanceof de?e:new de(n,D,null,null===(i=e)||void 0===i?void 0:i.message)).startTime=C,e.endTime=Date.now(),e.isTimeout=L,e.options={index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions},t(e))}))}))}},{key:"cancel",value:(t=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._aborted){e.next=2;break}return e.abrupt("return");case 2:if(this._aborted=!0,this._running=!1,!this._response){e.next=14;break}if(e.prev=5,!this._reader){e.next=9;break}return e.next=9,this._reader.cancel();case 9:e.next=13;break;case 11:e.prev=11,e.t0=e.catch(5);case 13:this._response=this._reader=null;case 14:if(this._abortController){try{this._abortController.abort()}catch(t){}this._abortController=null}this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions});case 16:case"end":return e.stop()}}),e,this,[[5,11]])}))),function(){return t.apply(this,arguments)})},{key:"_loadChunk",value:function(e,t,r,n){var s=this;if(!e.body||!e.body.getReader){this._running=!1;var a=new de(e.url,"",e,"onProgress of bad response.body.getReader");return a.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},void this.reject(a)}this._onProcessMinLen>0&&(this._cache=new Uint8Array(Qe),this._writeIdx=0);var u,c,l,d=this._reader=e.body.getReader(),h=function(){var a=o(i().mark((function a(){var o,f,p,v,y,_,m,g;return i().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return c=Date.now(),i.prev=1,i.next=4,d.read();case 4:u=i.sent,l=Date.now(),i.next=13;break;case 8:return i.prev=8,i.t0=i.catch(1),l=Date.now(),s._aborted||(s._running=!1,i.t0.options={index:s._index,range:s._range,vid:s._vid,priOptions:s._priOptions},s.reject(i.t0)),i.abrupt("return");case 13:if(f=(null===(o=s._range)||void 0===o?void 0:o.length)>0?s._range[0]:0,p=f+s._receivedLength,!s._aborted){i.next=19;break}return s._running=!1,t(void 0,!1,{range:[p,p],vid:s._vid,index:s._index,startTime:c,endTime:l,st:r,firstByteTime:n,priOptions:s._priOptions},e),i.abrupt("return");case 19:v=u.value?u.value.byteLength:0,s._receivedLength+=v,s._logger.debug("【fetchLoader,onProgress call】,task,",s._range,", start,",p,", end,",f+s._receivedLength,", done,",u.done),s._onProcessMinLen>0?s._writeIdx+v>=s._onProcessMinLen||u.done?((y=new Uint8Array(s._writeIdx+v)).set(s._cache.slice(0,s._writeIdx),0),v>0&&y.set(u.value,s._writeIdx),s._writeIdx=0,s._logger.debug("【fetchLoader,onProgress enough】,done,",u.done,",len,",y.byteLength,", writeIdx,",s._writeIdx)):v>0&&s._writeIdx+v<Qe?(s._cache.set(u.value,s._writeIdx),s._writeIdx+=v,s._logger.debug("【fetchLoader,onProgress cache】,len,",v,", writeIdx,",s._writeIdx)):v>0&&(_=new Uint8Array(s._writeIdx+v+2048),s._logger.debug("【fetchLoader,onProgress extra start】,size,",s._writeIdx+v+2048,", datalen,",v,", writeIdx,",s._writeIdx),_.set(s._cache.slice(0,s._writeIdx),0),v>0&&_.set(u.value,s._writeIdx),s._writeIdx+=v,delete s._cache,s._cache=_,s._logger.debug("【fetchLoader,onProgress extra end】,len,",v,", writeIdx,",s._writeIdx)):y=u.value,(y&&y.byteLength>0||u.done)&&t(y,u.done,{range:[s._range[0]+s._receivedLength-(y?y.byteLength:0),s._range[0]+s._receivedLength],vid:s._vid,index:s._index,startTime:c,endTime:l,st:r,firstByteTime:n,priOptions:s._priOptions},e),u.done?(m=Date.now()-r,g=me(s._receivedLength,m),s.emit(He,{speed:g,len:s._receivedLength,time:m,vid:s._vid,index:s._index,range:s._range,priOptions:s._priOptions}),s._running=!1,s._logger.debug("[fetchLoader onProgress end],task,",s._range,",done,",u.done),s.resolve(_e(u,!0,e,e.headers.get("Content-Length"),e.headers.get("age"),r,n,s._index,s._range,s._vid,s._priOptions))):h();case 25:case"end":return i.stop()}}),a,null,[[1,8]])})));return function(){return a.apply(this,arguments)}}();h()}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}}],[{key:"isSupported",value:function(){return!("undefined"==typeof fetch)}}]),n}(We);var Xe=function(e){h(r,e);var t=k(r);function r(){var e;return u(this,r),d(g(e=t.call(this)),"_xhr",null),d(g(e),"_aborted",!1),d(g(e),"_timeoutTimer",null),d(g(e),"_range",null),d(g(e),"_receivedLength",0),d(g(e),"_url",null),d(g(e),"_onProgress",null),d(g(e),"_index",-1),d(g(e),"_headers",null),d(g(e),"_currentChunkSizeKB",384),d(g(e),"_timeout",null),d(g(e),"_xhr",null),d(g(e),"_withCredentials",null),d(g(e),"_startTime",-1),d(g(e),"_loadCompleteResolve",null),d(g(e),"_loadCompleteReject",null),d(g(e),"_runing",!1),d(g(e),"_logger",!1),d(g(e),"_vid",""),d(g(e),"_responseType",void 0),d(g(e),"_credentials",void 0),d(g(e),"_method",void 0),d(g(e),"_transformResponse",void 0),d(g(e),"_firstRtt",void 0),d(g(e),"_onCancel",null),d(g(e),"_priOptions",null),e}return l(r,[{key:"load",value:function(e){var t=this;clearTimeout(this._timeoutTimer),this._logger=e.logger,this._range=e.range,this._onProgress=e.onProgress,this._index=e.index,this._headers=e.headers,this._withCredentials="include"===e.credentials||"same-origin"===e.credentials,this._body=e.body||null,e.method&&(this._method=e.method),this._timeout=e.timeout||null,this._runing=!0,this._vid=e.vid||e.url,this._responseType=e.responseType,this._firstRtt=-1,this._onTimeout=e.onTimeout,this._onCancel=e.onCancel,this._request=e.request,this._priOptions=e.priOptions||{},this._logger.debug("【xhrLoader task】, range",this._range),this._url=ye(e.url,e.params);var r=Date.now();return new Promise((function(e,r){t._loadCompleteResolve=e,t._loadCompleteReject=r,t._startLoad()})).catch((function(e){if(clearTimeout(t._timeoutTimer),t._runing=!1,!t._aborted)throw(e=e instanceof de?e:new de(t._url,t._request)).startTime=r,e.endTime=Date.now(),e.options={index:t._index,vid:t._vid,priOptions:t._priOptions},e}))}},{key:"_startLoad",value:function(){var e=null;if(this._responseType===ue&&this._range&&this._range.length>1)if(this._onProgress){this._firstRtt=-1;var t=1024*this._currentChunkSizeKB,r=this._range[0]+this._receivedLength,n=this._range[1];t<this._range[1]-r&&(n=r+t),e=[r,n],this._logger.debug("[xhr_loader->],tast :",this._range,", SubRange, ",e)}else e=this._range,this._logger.debug("[xhr_loader->],tast :",this._range,", allRange, ",e);this._internalOpen(e)}},{key:"_internalOpen",value:function(e){var t=this;try{this._startTime=Date.now();var r=this._xhr=new XMLHttpRequest;r.open(this._method||"GET",this._url,!0),r.responseType=this._responseType,this._timeout&&(r.timeout=this._timeout),r.withCredentials=this._withCredentials,r.onload=this._onLoad.bind(this),r.onreadystatechange=this._onReadyStatechange.bind(this),r.onerror=function(e){var r,n,i;t._running=!1;var s=new de(t._url,t._request,null==e||null===(r=e.currentTarget)||void 0===r?void 0:r.response,"xhr.onerror.status:"+(null==e||null===(n=e.currentTarget)||void 0===n?void 0:n.status)+",statusText,"+(null==e||null===(i=e.currentTarget)||void 0===i?void 0:i.statusText));s.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(s)},r.ontimeout=function(e){t.cancel();var r=new de(t._url,t._request,{status:408},"timeout");t._onTimeout&&(r.isTimeout=!0,t._onTimeout(r,{index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions})),r.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(r)};var n=this._headers||{},i=pe(e);i&&(n.Range=i),n&&Object.keys(n).forEach((function(e){r.setRequestHeader(e,n[e])})),this._logger.debug("[xhr.send->] tast,",this._range,",load sub range, ",e),r.send(this._body)}catch(s){s.options={index:this._index,range:e,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(s)}}},{key:"_onReadyStatechange",value:function(e){2===e.target.readyState&&this._firstRtt<0&&(this._firstRtt=Date.now())}},{key:"_onLoad",value:function(e){var t,r=e.target.status;if(r<200||r>299){var i=new de(this._url,null,n(n({},e.target.response),{},{status:r}),"bad response,status:"+r);return i.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(i)}var s,a=null,o=!1,u=(null===(t=this._range)||void 0===t?void 0:t.length)>0?this._range[0]:0;if(this._responseType===ue){var c,l=new Uint8Array(e.target.response);if(s=u+this._receivedLength,l&&l.byteLength>0){this._receivedLength+=l.byteLength;var d=Date.now()-this._startTime,h=me(this._receivedLength,d);this.emit(He,{speed:h,len:this._receivedLength,time:d,vid:this._vid,index:this._index,range:[s,u+this._receivedLength],priOptions:this._priOptions})}a=l,o=!((null===(c=this._range)||void 0===c?void 0:c.length)>1&&this._range[1]&&this._receivedLength<this._range[1]-this._range[0]),this._logger.debug("[xhr load done->], tast :",this._range,", start",s,"end ",u+this._receivedLength,",dataLen,",l?l.byteLength:0,",receivedLength",this._receivedLength,",index,",this._index,", done,",o)}else o=!0,a=e.target.response;var f={ok:r>=200&&r<300,status:r,statusText:this._xhr.statusText,url:this._xhr.responseURL,headers:this._getHeaders(this._xhr),body:this._xhr.response};this._transformResponse&&(f=this._transformResponse(f,this._url)||f),this._onProgress&&this._onProgress(a,o,{index:this._index,vid:this._vid,range:[s,u+this._receivedLength],startTime:this._startTime,endTime:Date.now(),priOptions:this._priOptions},f),o?(this._runing=!1,this._loadCompleteResolve&&this._loadCompleteResolve(_e(this._onProgress?null:a,o,f,f.headers["content-length"],f.headers.age,this._startTime,this._firstRtt,this._index,this._range,this._vid,this._priOptions))):this._startLoad()}},{key:"cancel",value:function(){if(!this._aborted)return this._aborted=!0,this._runing=!1,w(f(r.prototype),"removeAllListeners",this).call(this),this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions}),this._xhr?this._xhr.abort():void 0}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}},{key:"_getHeaders",value:function(e){var t,r={},n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=j(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){o=!0,s=e},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw s}}}}(e.getAllResponseHeaders().trim().split("\r\n"));try{for(n.s();!(t=n.n()).done;){var i=t.value.split(": ");r[i[0].toLowerCase()]=i.slice(1).join(": ")}}catch(s){n.e(s)}finally{n.f()}return r}}],[{key:"isSupported",value:function(){return"undefined"!=typeof XMLHttpRequest}}]),r}(We),Je=["retry","retryDelay","onRetryError","transformError"],Ze=function(){function e(t,r){u(this,e),this.promise=D(),this.alive=!!r.onProgress,!r.logger&&(r.logger=new X("Loader")),this._loaderType=t,this._loader=t===ae&&"undefined"!=typeof fetch?new Ye:new Xe,this._config=r,this._retryCount=0,this._retryTimer=null,this._canceled=!1,this._retryCheckFunc=r.retryCheckFunc,this._logger=r.logger}var t;return l(e,[{key:"exec",value:function(){var e=this,t=this._config,r=t.retry,n=t.retryDelay,s=t.onRetryError,a=t.transformError,u=m(t,Je),c=function(){var t=o(i().mark((function t(){var o,l,d;return i().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e._loader.load(u);case 3:o=t.sent,e.promise.resolve(o),t.next=27;break;case 7:if(t.prev=7,t.t0=t.catch(0),e._loader.running=!1,e._logger.debug("[task request catch err]",t.t0),!e._canceled){t.next=13;break}return t.abrupt("return");case 13:if(t.t0.loaderType=e._loaderType,t.t0.retryCount=e._retryCount,l=t.t0,a&&(l=a(l)||l),s&&e._retryCount>0&&s(l,e._retryCount,{index:u.index,vid:u.vid,range:u.range,priOptions:u.priOptions}),e._retryCount++,d=!0,e._retryCheckFunc&&(d=e._retryCheckFunc(t.t0)),!(d&&e._retryCount<=r)){t.next=26;break}return clearTimeout(e._retryTimer),e._logger.debug("[task request setTimeout],retry",e._retryCount,",retry range,",u.range),e._retryTimer=setTimeout(c,n),t.abrupt("return");case 26:e.promise.reject(l);case 27:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(){return t.apply(this,arguments)}}();return c(),this.promise}},{key:"cancel",value:(t=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return clearTimeout(this._retryTimer),this._canceled=!0,this._loader.running=!1,e.abrupt("return",this._loader.cancel());case 4:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"running",get:function(){return this._loader&&this._loader.running}},{key:"loader",get:function(){return this._loader}}]),e}();function $e(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if((t=t.filter(Boolean)).length<2)return t[0];var n=new Uint8Array(t.reduce((function(e,t){return e+t.byteLength}),0)),i=0;return t.forEach((function(e){n.set(e,i),i+=e.byteLength})),n}function et(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new Promise((function(t){return setTimeout(t,e)}))}var tt=function(e){h(s,e);var t,r=k(s);function s(e){var t;return u(this,s),d(g(t=r.call(this,e)),"type",ae),d(g(t),"_queue",[]),d(g(t),"_alive",[]),d(g(t),"_currentTask",null),d(g(t),"_finnalUrl",""),d(g(t),"_config",void 0),t._config=function(e){return n({loaderType:ae,retry:0,retryDelay:0,timeout:0,request:null,onTimeout:void 0,onProgress:void 0,onRetryError:void 0,transformRequest:void 0,transformResponse:void 0,transformError:void 0,responseType:ce,range:void 0,url:"",params:void 0,method:"GET",headers:{},body:void 0,mode:void 0,credentials:void 0,cache:void 0,redirect:void 0,referrer:void 0,referrerPolicy:void 0,integrity:void 0,onProcessMinLen:0},e)}(e),t._config.loaderType!==oe&&Ye.isSupported()||(t.type=oe),t.log=e.logger,t}return l(s,[{key:"isFetch",value:function(){return this.type===ae}},{key:"load",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"string"!=typeof e&&e?r=e:r.url=e||r.url||this._config.url,(r=Object.assign({},this._config,r)).params&&(r.params=Object.assign({},r.params)),r.headers&&fe(r.headers)&&(r.headers=Object.assign({},r.headers)),r.body&&fe(r.body)&&(r.body=Object.assign({},r.body)),r.transformRequest&&(r=r.transformRequest(r)||r),r.logger=this.log;var n=new Ze(this.type,r);return n.loader.on(He,(function(e){t.emit(He,e)})),this._queue.push(n),1!==this._queue.length||this._currentTask&&this._currentTask.running||this._processTask(),n.promise}},{key:"cancel",value:(t=o(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this._queue.map((function(e){return e.cancel()})).concat(this._alive.map((function(e){return e.cancel()}))),this._currentTask&&t.push(this._currentTask.cancel()),this._queue=[],this._alive=[],e.next=6,Promise.all(t);case 6:return e.next=8,et();case 8:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"_processTask",value:function(){var e=this;if(this._currentTask=this._queue.shift(),this._currentTask){this._currentTask.alive&&this._alive.push(this._currentTask);var t=this._currentTask.exec().catch((function(e){}));t&&"function"==typeof t.finally&&t.finally((function(){var t,r;null!==(t=e._currentTask)&&void 0!==t&&t.alive&&(null===(r=e._alive)||void 0===r?void 0:r.length)>0&&(e._alive=e._alive.filter((function(t){return t&&t!==e._currentTask}))),e._processTask()}))}}}],[{key:"isFetchSupport",value:function(){return Ye.isSupported()}}]),s}(We),rt=function(){function e(){u(this,e),d(this,"_prevCurrentTime",0)}return l(e,[{key:"do",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2?arguments[2]:void 0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(e){var i=e.currentTime,s=0;if(this._prevCurrentTime===i){var a=C.info(C.get(e),i);if(!a.buffers.length)return;r&&a.nextStart||a.nextStart&&a.nextStart-i<t?s=a.nextStart+.1:a.end&&a.end-i>n&&!e.seeking&&(s=i+.1)}this._prevCurrentTime=i,s&&i!==s&&(e.currentTime=s)}}}]),e}(),nt=function(){function e(t){var r=this;u(this,e),d(this,"_seiSet",new Set),this.emitter=t,t.on(Oe,(function(e){e&&r._seiSet.add(e)}))}return l(e,[{key:"throw",value:function(e,t){var r=this;if(null!=e&&this._seiSet.size){var n=e-.2,i=e+.2,s=[];this._seiSet.forEach((function(e){e.time>=n&&e.time<=i&&s.push(e)})),s.forEach((function(e){r._seiSet.delete(e),r.emitter.emit(Ce,e)})),t&&this._seiSet.forEach((function(t){t.time<e-5&&r._seiSet.delete(t)}))}}},{key:"reset",value:function(){this._seiSet.clear()}}]),e}(),it=function(){function e(){u(this,e),d(this,"_chunkSpeeds",[]),d(this,"_speeds",[])}return l(e,[{key:"addRecord",value:function(e,t){e&&t&&(this._speeds.push(8e3*e/t),this._speeds=this._speeds.slice(-3))}},{key:"addChunkRecord",value:function(e,t){e&&t&&(this._chunkSpeeds.push(8e3*e/t),this._chunkSpeeds=this._chunkSpeeds.slice(-100))}},{key:"getAvgSpeed",value:function(){return this._chunkSpeeds.length||this._speeds.length?this._speeds.length?this._speeds.reduce((function(e,t){return e+t}))/this._speeds.length:this._chunkSpeeds.reduce((function(e,t){return e+t}))/this._chunkSpeeds.length:0}},{key:"getLatestSpeed",value:function(){return this._chunkSpeeds.length||this._speeds.length?this._speeds.length?this._speeds[this._speeds.length-1]:this._chunkSpeeds[this._chunkSpeeds.length-1]:0}},{key:"reset",value:function(){this._chunkSpeeds=[],this._speeds=[]}}]),e}(),st=function(){function e(t){u(this,e),d(this,"encodeType",""),d(this,"audioCodec",""),d(this,"videoCodec",""),d(this,"domain",""),d(this,"fps",0),d(this,"bitrate",0),d(this,"width",0),d(this,"height",0),d(this,"samplerate",0),d(this,"channelCount",0),d(this,"gop",0),d(this,"_bitsAccumulateSize",0),d(this,"_bitsAccumulateDuration",0),d(this,"_startGopId",-1),this._timescale=t}return l(e,[{key:"getStats",value:function(){return{encodeType:this.encodeType,audioCodec:this.audioCodec,videoCodec:this.videoCodec,domain:this.domain,fps:this.fps,bitrate:this.bitrate,width:this.width,height:this.height,samplerate:this.samplerate,channelCount:this.channelCount,gop:this.gop}}},{key:"setEncodeType",value:function(e){this.encodeType=e}},{key:"setFpsFromScriptData",value:function(e){var t,r=e.data,n=null==r||null===(t=r.onMetaData)||void 0===t?void 0:t.framerate;n&&n>0&&n<100&&(this.fps=n)}},{key:"setVideoMeta",value:function(e){if(this.width=e.width,this.height=e.height,this.videoCodec=e.codec,this.encodeType=e.codecType,e.fpsNum&&e.fpsDen){var t=e.fpsNum/e.fpsDen;t>0&&t<100&&(this.fps=t)}}},{key:"setAudioMeta",value:function(e){this.audioCodec=e.codec,this.samplerate=e.sampleRate,this.channelCount=e.channelCount}},{key:"setDomain",value:function(e){this.domain=e.split("/").slice(2,3)[0]}},{key:"updateBitrate",value:function(e){var t=this;if((!this.fps||this.fps>=100)&&e.length){var r=e.reduce((function(e,t){return e+t.duration}),0)/e.length;this.fps=Math.round(this._timescale/r)}e.forEach((function(e){-1===t._startGopId&&(t._startGopId=e.gopId),e.gopId===t._startGopId&&t.gop++,t._bitsAccumulateDuration+=e.duration/(t._timescale/1e3),t._bitsAccumulateSize+=e.units.reduce((function(e,t){return e+t.length}),0),t._bitsAccumulateDuration>=1e3&&(t.bitrate=8*t._bitsAccumulateSize,t._bitsAccumulateDuration=0,t._bitsAccumulateSize=0)}))}}]),e}(),at=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;u(this,e),d(this,"_core",null),d(this,"_samples",[]),this._core=t,this._timescale=r,this._stats=new st(r),this._bindEvents()}return l(e,[{key:"getStats",value:function(){var e,t,r,i,s,a,o,u=(null===(e=this._core)||void 0===e?void 0:e.media)||{},c=u.currentTime,l=void 0===c?0:c,d=u.decodeFps,h=void 0===d?0:d;return n(n({},this._stats.getStats()),{},{downloadSpeed:(null===(t=this._core)||void 0===t||null===(r=t.speedInfo)||void 0===r?void 0:r.call(t).speed)||0,avgSpeed:(null===(i=this._core)||void 0===i||null===(s=i.speedInfo)||void 0===s?void 0:s.call(i).avgSpeed)||0,currentTime:l,bufferEnd:(null===(a=this._core)||void 0===a||null===(o=a.bufferInfo())||void 0===o?void 0:o.remaining)||0,decodeFps:h})}},{key:"_bindEvents",value:function(){var e=this;this._core.on(Me,(function(t){var r=t.videoTrack;return e._stats.updateBitrate(r.samples)})),this._core.on(Re,(function(t){e._stats.setFpsFromScriptData(t)})),this._core.on(Le,(function(t){"video"===t.type?e._stats.setVideoMeta(t.track):e._stats.setAudioMeta(t.track)})),this._core.on(be,(function(t){e._stats.setDomain(t.responseUrl)}))}},{key:"reset",value:function(){this._samples=[],this._stats=new st(this._timescale)}}]),e}(),ot="video",ut="audio",ct="metadata",lt="avc",dt="hevc",ht="aac",ft="g7110a",pt="g7110m",vt="LARGE_AV_SHIFT",yt="LARGE_VIDEO_GAP",_t="LARGE_VIDEO_GAP_BETWEEN_CHUNK",mt="LARGE_AUDIO_GAP",gt="AUDIO_FILLED",bt="AUDIO_DROPPED",kt=function(){function e(){u(this,e),d(this,"id",1),d(this,"type",ot),d(this,"codecType",lt),d(this,"pid",-1),d(this,"hvcC",void 0),d(this,"codec",""),d(this,"timescale",0),d(this,"formatTimescale",0),d(this,"sequenceNumber",0),d(this,"baseMediaDecodeTime",0),d(this,"baseDts",0),d(this,"duration",0),d(this,"warnings",[]),d(this,"samples",[]),d(this,"pps",[]),d(this,"sps",[]),d(this,"vps",[]),d(this,"fpsNum",0),d(this,"fpsDen",0),d(this,"sarRatio",[]),d(this,"width",0),d(this,"height",0),d(this,"nalUnitSize",4),d(this,"present",!1),d(this,"isVideoEncryption",!1),d(this,"isAudioEncryption",!1),d(this,"isVideo",!0),d(this,"kid",null),d(this,"pssh",null),d(this,"ext",void 0)}return l(e,[{key:"reset",value:function(){this.sequenceNumber=this.width=this.height=this.fpsDen=this.fpsNum=this.duration=this.baseMediaDecodeTime=this.timescale=0,this.codec="",this.present=!1,this.pid=-1,this.pps=[],this.sps=[],this.vps=[],this.sarRatio=[],this.samples=[],this.warnings=[],this.hvcC=null}},{key:"exist",value:function(){return!!(this.pps.length&&this.sps.length&&this.codec)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isVideoEncryption}}]),e}(),xt=function(){function e(){u(this,e),d(this,"id",2),d(this,"type",ut),d(this,"codecType",ht),d(this,"pid",-1),d(this,"codec",""),d(this,"sequenceNumber",0),d(this,"sampleDuration",0),d(this,"timescale",0),d(this,"formatTimescale",0),d(this,"baseMediaDecodeTime",0),d(this,"duration",0),d(this,"warnings",[]),d(this,"samples",[]),d(this,"baseDts",0),d(this,"sampleSize",16),d(this,"sampleRate",0),d(this,"channelCount",0),d(this,"objectType",0),d(this,"sampleRateIndex",0),d(this,"config",[]),d(this,"present",!1),d(this,"isVideoEncryption",!1),d(this,"isAudioEncryption",!1),d(this,"kid",null),d(this,"ext",void 0)}return l(e,[{key:"reset",value:function(){this.sequenceNumber=0,this.timescale=0,this.sampleDuration=0,this.sampleRate=0,this.channelCount=0,this.baseMediaDecodeTime=0,this.present=!1,this.pid=-1,this.codec="",this.samples=[],this.config=[],this.warnings=[]}},{key:"exist",value:function(){return!!(this.sampleRate&&this.channelCount&&this.codec&&this.codecType===ht)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isAudioEncryption}}]),e}(),wt=function(){function e(t,r,n){u(this,e),d(this,"flag",{}),d(this,"keyframe",!1),d(this,"gopId",0),d(this,"duration",0),d(this,"size",0),d(this,"units",[]),d(this,"chromaFormat",420),this.originPts=this.pts=t,this.originDts=this.dts=r,n&&(this.units=n)}return l(e,[{key:"cts",get:function(){return this.pts-this.dts}},{key:"setToKeyframe",value:function(){this.keyframe=!0,this.flag.dependsOn=2,this.flag.isNonSyncSample=0}}]),e}(),St=l((function e(t,r,n,i){u(this,e),d(this,"duration",1024),d(this,"flag",{dependsOn:2,isNonSyncSample:0}),d(this,"keyframe",!0),this.originPts=this.pts=this.dts=t,this.data=r,this.size=r.byteLength,this.sampleOffset=i,n&&(this.duration=n)})),Et=l((function e(t,r){u(this,e),d(this,"time",0),this.data=t,this.originPts=this.pts=r})),jt=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r)}(Et),Tt=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r)}(Et),At=function(){function e(){u(this,e),d(this,"id",3),d(this,"type",ct),d(this,"timescale",0),d(this,"flvScriptSamples",[]),d(this,"seiSamples",[])}return l(e,[{key:"exist",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length||!this.timescale)}},{key:"reset",value:function(){this.timescale=0,this.flvScriptSamples=[],this.seiSamples=[]}},{key:"hasSample",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length)}}]),e}(),Dt=function(){function e(t){if(u(this,e),d(this,"_bytesAvailable",void 0),d(this,"_bitsAvailable",0),d(this,"_word",0),!t)throw new Error("ExpGolomb data params is required");this._data=t,this._bytesAvailable=t.byteLength,this._bytesAvailable&&this._loadWord()}return l(e,[{key:"bitsAvailable",get:function(){return this._bitsAvailable}},{key:"_loadWord",value:function(){var e=this._data.byteLength-this._bytesAvailable,t=Math.min(4,this._bytesAvailable);if(0===t)throw new Error("No bytes available");var r=new Uint8Array(4);r.set(this._data.subarray(e,e+t)),this._word=new DataView(r.buffer).getUint32(0),this._bitsAvailable=8*t,this._bytesAvailable-=t}},{key:"skipBits",value:function(e){if(this._bitsAvailable>e)this._word<<=e,this._bitsAvailable-=e;else{e-=this._bitsAvailable;var t=Math.floor(e/8);e-=8*t,this._bytesAvailable-=t,this._loadWord(),this._word<<=e,this._bitsAvailable-=e}}},{key:"readBits",value:function(e){if(e>32)throw new Error("Cannot read more than 32 bits");var t=Math.min(this._bitsAvailable,e),r=this._word>>>32-t;return this._bitsAvailable-=t,this._bitsAvailable>0?this._word<<=t:this._bytesAvailable>0&&this._loadWord(),(t=e-t)>0&&this._bitsAvailable?r<<t|this.readBits(t):r}},{key:"skipLZ",value:function(){var e;for(e=0;e<this._bitsAvailable;++e)if(0!=(this._word&2147483648>>>e))return this._word<<=e,this._bitsAvailable-=e,e;return this._loadWord(),e+this.skipLZ()}},{key:"skipUEG",value:function(){this.skipBits(1+this.skipLZ())}},{key:"readUEG",value:function(){var e=this.skipLZ();return this.readBits(e+1)-1}},{key:"readEG",value:function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}},{key:"readBool",value:function(){return 1===this.readBits(1)}},{key:"readUByte",value:function(){return this.readBits(8)}},{key:"skipScalingList",value:function(e){for(var t=8,r=8,n=0;n<e;n++)0!==r&&(r=(t+this.readEG()+256)%256),t=0===r?t:r}}]),e}(),Lt=function(){function e(t){u(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]")}return l(e,[{key:"warn",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).warn.apply(t,[this._prefix].concat(n))}}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();d(Lt,"disabled",!0);var Ot=function(){function e(){u(this,e)}return l(e,null,[{key:"decode",value:function(t){for(var r=[],n=t,i=0,s=t.length;i<s;)if(n[i]<128)r.push(String.fromCharCode(n[i])),++i;else{if(n[i]<192);else if(n[i]<224){if(e._checkContinuation(n,i,1)){var a=(31&n[i])<<6|63&n[i+1];if(a>=128){r.push(String.fromCharCode(65535&a)),i+=2;continue}}}else if(n[i]<240){if(e._checkContinuation(n,i,2)){var o=(15&n[i])<<12|(63&n[i+1])<<6|63&n[i+2];if(o>=2048&&55296!=(63488&o)){r.push(String.fromCharCode(65535&o)),i+=3;continue}}}else if(n[i]<248&&e._checkContinuation(n,i,3)){var u=(7&n[i])<<18|(63&n[i+1])<<12|(63&n[i+2])<<6|63&n[i+3];if(u>65536&&u<1114112){u-=65536,r.push(String.fromCharCode(u>>>10|55296)),r.push(String.fromCharCode(1023&u|56320)),i+=4;continue}}r.push(String.fromCharCode(65533)),++i}return r.join("")}},{key:"_checkContinuation",value:function(e,t,r){var n=e;if(t+r<n.length){for(;r--;)if(128!=(192&n[++t]))return!1;return!0}return!1}}]),e}(),Ct="undefined"!=typeof window,Rt=Ct&&navigator.userAgent.toLocaleLowerCase(),Bt=Ct&&/^((?!chrome|android).)*safari/.test(Rt),Pt=Ct&&Rt.includes("firefox"),Ut=Ct&&Rt.includes("android");function Mt(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t=t.filter(Boolean);var n=new Uint8Array(t.reduce((function(e,t){return e+t.byteLength}),0)),i=0;return t.forEach((function(e){n.set(e,i),i+=e.byteLength})),n}function It(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function Ft(e){for(var t,r="avc1.",n=0;n<3;n++)(t=e[n].toString(16)).length<2&&(t="0".concat(t)),r+=t;return r}function Nt(e){if(!Array.isArray(e)){for(var t=[],r="",n=0;n<e.length;n++)n%2&&(r=e[n-1]+e[n],t.push(parseInt(r,16)),r="");return t}return e.map((function(e){return parseInt(e,16)}))}var qt=function(){function e(){u(this,e)}return l(e,null,[{key:"parseAnnexB",value:function(e){for(var t=e.length,r=2,n=0;null!==e[r]&&void 0!==e[r]&&1!==e[r];)r++;if((n=++r+2)>=t)return[];for(var i=[];n<t;)switch(e[n]){case 0:if(0!==e[n-1]){n+=2;break}if(0!==e[n-2]){n++;break}r!==n-2&&i.push(e.subarray(r,n-2));do{n++}while(1!==e[n]&&n<t);n=(r=n+1)+2;break;case 1:if(0!==e[n-1]||0!==e[n-2]){n+=3;break}r!==n-2&&i.push(e.subarray(r,n-2)),n=(r=n+1)+2;break;default:n+=3}return r<t&&i.push(e.subarray(r)),i}},{key:"parseAvcC",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(!(e.length<4)){for(var r,n=e.length,i=[],s=0;s+t<n;)if(r=It(e,s),3===t&&(r>>>=8),s+=t,r){if(s+r>n)break;i.push(e.subarray(s,s+r)),s+=r}return i}}},{key:"parseSEI",value:function(e,t){for(var r=e.length,n=t?2:1,i=0,s=0,a="";255===e[n];)i+=255,n++;for(i+=e[n++];255===e[n];)s+=255,n++;if(s+=e[n++],5===i&&r>n+16)for(var o=0;o<16;o++)a+=e[n].toString(16),n++;return{payload:e.subarray(n,n+s),type:i,size:s,uuid:a}}},{key:"removeEPB",value:function(e){for(var t=e.byteLength,r=[],n=1;n<t-2;)0===e[n]&&0===e[n+1]&&3===e[n+2]?(r.push(n+2),n+=2):n++;if(!r.length)return e;var i=t-r.length,s=new Uint8Array(i),a=0;for(n=0;n<i;a++,n++)a===r[0]&&(a++,r.shift()),s[n]=e[a];return s}}]),e}(),Vt=function(){function e(){u(this,e)}return l(e,null,[{key:"parseAVCDecoderConfigurationRecord",value:function(t){if(!(t.length<7)){for(var r,n,i=1+(3&t[4]),s=[],a=[],o=6,u=31&t[5],c=0;c<u;c++)if(n=t[o]<<8|t[o+1],o+=2,n){var l=t.subarray(o,o+n);o+=n,s.push(l),r||(r=e.parseSPS(qt.removeEPB(l)))}var d,h=t[o];o++;for(var f=0;f<h;f++)d=t[o]<<8|t[o+1],o+=2,d&&(a.push(t.subarray(o,o+d)),o+=d);return{sps:r,spsArr:s,ppsArr:a,nalUnitSize:i}}}},{key:"parseSPS",value:function(e){var t=new Dt(e);t.readUByte();var r=t.readUByte(),n=t.readUByte(),i=t.readUByte();t.skipUEG();var s=420;if(100===r||110===r||122===r||244===r||44===r||83===r||86===r||118===r||128===r||138===r||144===r){var a=t.readUEG();if(a<=3&&(s=[0,420,422,444][a]),3===a&&t.skipBits(1),t.skipUEG(),t.skipUEG(),t.skipBits(1),t.readBool())for(var o=3!==a?8:12,u=0;u<o;u++)t.readBool()&&(u<6?t.skipScalingList(16):t.skipScalingList(64))}t.skipUEG();var c=t.readUEG();if(0===c)t.readUEG();else if(1===c){t.skipBits(1),t.skipUEG(),t.skipUEG();for(var l=t.readUEG(),d=0;d<l;d++)t.skipUEG()}t.skipUEG(),t.skipBits(1);var h=t.readUEG(),f=t.readUEG(),p=t.readBits(1);0===p&&t.skipBits(1),t.skipBits(1);var v,y,_,m,g,b=0,k=0,x=0,w=0;if(t.readBool()&&(b=t.readUEG(),k=t.readUEG(),x=t.readUEG(),w=t.readUEG()),t.readBool()){if(t.readBool())switch(t.readUByte()){case 1:v=[1,1];break;case 2:v=[12,11];break;case 3:v=[10,11];break;case 4:v=[16,11];break;case 5:v=[40,33];break;case 6:v=[24,11];break;case 7:v=[20,11];break;case 8:v=[32,11];break;case 9:v=[80,33];break;case 10:v=[18,11];break;case 11:v=[15,11];break;case 12:v=[64,33];break;case 13:v=[160,99];break;case 14:v=[4,3];break;case 15:v=[3,2];break;case 16:v=[2,1];break;case 255:v=[t.readUByte()<<8|t.readUByte(),t.readUByte()<<8|t.readUByte()]}if(t.readBool()&&t.readBool(),t.readBool()&&(t.readBits(4),t.readBool()&&t.readBits(24)),t.readBool()&&(t.readUEG(),t.readUEG()),t.readBool()){var S=t.readBits(32),E=t.readBits(32);y=t.readBool(),g=(_=E)/(m=2*S)}}return{codec:Ft(e.subarray(1,4)),profileIdc:r,profileCompatibility:n,levelIdc:i,chromaFormat:s,width:Math.ceil(16*(h+1)-2*(b+k)),height:(2-p)*(f+1)*16-(p?2:4)*(x+w),sarRatio:v,fpsNum:_,fpsDen:m,fps:g,fixedFrame:y}}}]),e}(),zt=function(){function e(){u(this,e)}return l(e,null,[{key:"getRateIndexByRate",value:function(t){return e.FREQ.indexOf(t)}},{key:"parseADTS",value:function(t,r){for(var n=t.length,i=0;i+2<n&&(255!==t[i]||240!=(246&t[i+1]));)i++;if(!(i>=n)){var s=i,a=[],o=(60&t[i+2])>>>2,u=e.FREQ[o];if(!u)throw new Error("Invalid sampling index: ".concat(o));for(var c,l,d=1+((192&t[i+2])>>>6),h=(1&t[i+2])<<2|(192&t[i+3])>>>6,f=e._getConfig(o,h,d),p=f.config,v=f.codec,y=0,_=e.getFrameDuration(u);i+7<n;)if(255===t[i]&&240==(246&t[i+1])){if(n-i<(l=(3&t[i+3])<<11|t[i+4]<<3|(224&t[i+5])>>5))break;c=2*(1&~t[i+1]),a.push({pts:r+y*_,data:t.subarray(i+7+c,i+l)}),y++,i+=l}else i++;return{skip:s,remaining:i>=n?void 0:t.subarray(i),frames:a,samplingFrequencyIndex:o,sampleRate:u,objectType:d,channelCount:h,codec:v,config:p,originCodec:"mp4a.40.".concat(d)}}}},{key:"parseAudioSpecificConfig",value:function(t){if(t.length){var r=t[0]>>>3,n=(7&t[0])<<1|t[1]>>>7,i=(120&t[1])>>>3,s=e.FREQ[n];if(s){var a=e._getConfig(n,i,r);return{samplingFrequencyIndex:n,sampleRate:s,objectType:r,channelCount:i,config:a.config,codec:a.codec,originCodec:"mp4a.40.".concat(r)}}}}},{key:"getFrameDuration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4;return 1024*t/e}},{key:"_getConfig",value:function(e,t,r){var n,i,s=[];return Pt?e>=6?(n=5,i=e-3):(n=2,i=e):Ut?(n=2,i=e):(n=2===r||5===r?r:5,i=e,e>=6?i=e-3:1===t&&(n=2,i=e)),s[0]=n<<3,s[0]|=(14&e)>>1,s[1]=(1&e)<<7,s[1]|=t<<3,5===n&&(s[1]|=(14&i)>>1,s[2]=(1&i)<<7,s[2]|=8,s[3]=0),{config:s,codec:"mp4a.40.".concat(n)}}},{key:"getSilentFrame",value:function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}}}]),e}();d(zt,"FREQ",[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350]);var Gt=function(){function e(){u(this,e)}return l(e,null,[{key:"parseHEVCDecoderConfigurationRecord",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(t.length<23)){r=r||{};for(var n,i,s,a,o,u=1+(3&t[21]),c=[],l=[],d=[],h=23,f=t[22],p=0;p<f;p++){s=63&t[h],a=t[h+1]<<8|t[h+2],h+=3;for(var v=0;v<a;v++)if(o=t[h]<<8|t[h+1],h+=2,o){switch(s){case 32:var y=t.subarray(h,h+o);n||(n=e.parseVPS(qt.removeEPB(y),r)),d.push(y);break;case 33:var _=t.subarray(h,h+o);i||(i=e.parseSPS(qt.removeEPB(_),r)),c.push(_);break;case 34:l.push(t.subarray(h,h+o))}h+=o}}return{hvcC:r,sps:i,spsArr:c,ppsArr:l,vpsArr:d,nalUnitSize:u}}}},{key:"parseVPS",value:function(t,r){r=r||{};var n=new Dt(t);n.readUByte(),n.readUByte(),n.readBits(12);var i=n.readBits(3);return r.numTemporalLayers=Math.max(r.numTemporalLayers||0,i+1),n.readBits(17),e._parseProfileTierLevel(n,i,r),r}},{key:"parseSPS",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r=r||{};var n=new Dt(t);n.readUByte(),n.readUByte(),n.readBits(4);var i=n.readBits(3);r.numTemporalLayers=Math.max(i+1,r.numTemporalLayers||0),r.temporalIdNested=n.readBits(1),e._parseProfileTierLevel(n,i,r),n.readUEG();var s=r.chromaFormatIdc=n.readUEG(),a=420;s<=3&&(a=[0,420,422,444][s]);var o=0;3===s&&(o=n.readBits(1));var u,c,l,d,h=n.readUEG(),f=n.readUEG(),p=n.readBits(1);if(1===p&&(u=n.readUEG(),c=n.readUEG(),l=n.readUEG(),d=n.readUEG()),r.bitDepthLumaMinus8=n.readUEG(),r.bitDepthChromaMinus8=n.readUEG(),1===p){var v=1!==s&&2!==s||0!==o?1:2,y=1===s&&0===o?2:1;h-=v*(c+u),f-=y*(d+l)}return{codec:"hev1.1.6.L93.B0",width:h,height:f,chromaFormat:a,hvcC:r}}},{key:"_parseProfileTierLevel",value:function(e,t,r){var n=r.generalTierFlag||0;r.generalProfileSpace=e.readBits(2),r.generalTierFlag=Math.max(e.readBits(1),n),r.generalProfileIdc=Math.max(e.readBits(5),r.generalProfileIdc||0),r.generalProfileCompatibilityFlags=e.readBits(32),r.generalConstraintIndicatorFlags=[e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8)];var i=e.readBits(8);n<r.generalTierFlag?r.generalLevelIdc=i:r.generalLevelIdc=Math.max(i,r.generalLevelIdc||0);var s=[],a=[];if(t>e.bitsAvailable)throw new Error("maxSubLayersMinus inavlid size ".concat(t));for(var o=0;o<t;o++)s[o]=e.readBits(1),a[o]=e.readBits(1);t>0&&e.readBits(2*(8-t));for(var u=0;u<t;u++)0!==s[u]&&(e.readBits(2),e.readBits(1),e.readBits(5),e.readBits(16),e.readBits(16),e.readBits(4),e.readBits(16),e.readBits(16),e.readBits(12)),0!==a[u]&&e.readBits(8)}}]),e}(),Ht=1e3,Kt=5e3,Wt=function(){function e(t,r,n){u(this,e),this.videoTrack=t,this.audioTrack=r,this.metadataTrack=n,this._baseDts=-1,this._baseDtsInited=!1,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastVideoDuration=0,this._keyFrameInNextChunk=!1,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0}return l(e,[{key:"fix",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t=Math.round(1e3*t);var i=this.videoTrack,s=this.audioTrack;!r&&n||(this._videoLastSample=null,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0),r&&!n&&(this._baseDtsInited=!1),this._baseDtsInited||this._calculateBaseDts(s,i),!n&&t&&(this._audioNextPts=this._videoNextDts=t);var a=this._baseDtsInited&&(this._videoTimestampBreak||!this.videoTrack.exist())&&(this._audioTimestampBreak||!this.audioTrack.exist());if(a&&this._resetBaseDtsWhenStreamBreaked(),this._fixAudio(s),this._keyFrameInNextChunk=!1,this._fixVideo(i),this.metadataTrack.exist()){var o=this.metadataTrack.timescale;this.metadataTrack.seiSamples.forEach((function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/o})),this.metadataTrack.flvScriptSamples.forEach((function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/o}))}i.samples.length&&(i.baseMediaDecodeTime=i.samples[0].dts),s.samples.length&&(s.baseMediaDecodeTime=s.samples[0].pts*s.timescale/1e3)}},{key:"_fixVideo",value:function(e){var t=this,r=e.samples;if(r.length){var n;if(r.forEach((function(e){e.dts-=t._baseDts,e.pts-=t._baseDts,e.keyframe&&(t._keyFrameInNextChunk=!0)})),e.fpsNum&&e.fpsDen)n=e.timescale*(e.fpsDen/e.fpsNum);else if(e.length>1){var i=e.samples[0],s=e.samples[r.length-1];n=Math.floor((s.dts-i.dts)/(r.length-1))}else n=this._lastVideoDuration||40;var a=r.pop();if(this._videoLastSample&&r.unshift(this._videoLastSample),this._videoLastSample=a,r.length){if(void 0===this._videoNextDts){var o=r[0];this._videoNextDts=o.dts}var u=r.length,c=0,l=r[0],d=this._videoNextDts-l.dts;if(Math.abs(d)>200){var h;if(Math.abs(l.dts-this._lastVideoExceptionChunkFirstDtsDot)>5e3)this._lastVideoExceptionChunkFirstDtsDot=l.dts,e.warnings.push({type:_t,nextDts:this._videoNextDts,firstSampleDts:l.dts,nextSampleDts:null===(h=r[1])||void 0===h?void 0:h.dts,sampleDuration:d});this._videoTimestampBreak>=5?(this._videoNextDts=l.dts,this._videoTimestampBreak=0):(l.dts+=d,l.pts+=d,this.audioTrack.exist()||(this._videoTimestampBreak=1))}for(var f=0;f<u;f++){var p=r[f].dts,v=r[f+1];((c=f<u-1?v.dts-p:a?a.dts-p:n)>1e3||c<0)&&(this._videoTimestampBreak++,Math.abs(p-this._lastVideoExceptionLargeGapDot)>5e3&&(this._lastVideoExceptionLargeGapDot=p,e.warnings.push({type:yt,time:p/e.timescale,dts:p,originDts:r[f].originDts,nextDts:this._videoNextDts,sampleDuration:c,refSampleDuration:n})),c=n),r[f].duration=c,this._videoNextDts+=c,this._lastVideoDuration=c}}}}},{key:"_fixAudio",value:function(e){var t=this,r=e.samples;r.length&&(r.forEach((function(e){e.dts=e.pts-=t._baseDts})),this._doFixAudioInternal(e,r,1e3))}},{key:"_calculateBaseDts",value:function(e,t){var r=e.samples,n=t.samples;if(!r.length&&!n.length)return!1;var i=1/0,s=1/0;r.length&&(e.baseDts=i=r[0].pts),n.length&&(t.baseDts=s=n[0].dts),this._baseDts=Math.min(i,s);var a=s-i;return Number.isFinite(a)&&Math.abs(a)>500&&t.warnings.push({type:vt,videoBaseDts:s,audioBasePts:i,baseDts:this._baseDts,delta:a}),this._baseDtsInited=!0,!0}},{key:"_resetBaseDtsWhenStreamBreaked",value:function(){this._calculateBaseDts(this.audioTrack,this.videoTrack)&&(this.audioTrack.exist()?this.videoTrack.exist()?this._baseDts-=Math.min(this._audioNextPts,this._videoNextDts):this._baseDts-=this._audioNextPts:this._baseDts-=this._videoNextDts,this._videoTimestampBreak=0,this._audioTimestampBreak=0)}},{key:"_doFixAudioInternal",value:function(e,t,r){e.sampleDuration||(e.sampleDuration=e.codecType===ht?zt.getFrameDuration(e.timescale,r):this._getG711Duration(e));var n=e.sampleDuration,i=e.codecType===ht?1024:n*e.timescale/1e3;if(void 0===this._audioNextPts){var s=t[0];this._audioNextPts=s.pts}for(var a=0;a<t.length;a++){var o=this._audioNextPts,u=t[a],c=u.pts-o;if(0===a&&this._audioTimestampBreak>=5&&this._keyFrameInNextChunk&&(o=this._audioNextPts=u.dts,c=0,this._audioTimestampBreak=0),!this._audioTimestampBreak&&c>=3*n&&c<=Ht&&!Bt){var l=this._getSilentFrame(e)||t[0].data.subarray(),d=Math.floor(c/n);Math.abs(u.pts-this._lastAudioExceptionGapDot)>Kt&&(this._lastAudioExceptionGapDot=u.pts,e.warnings.push({type:gt,pts:u.pts,originPts:u.originPts,count:d,nextPts:o,refSampleDuration:n}));for(var h=0;h<d;h++){var f=new St(Math.floor(this._audioNextPts+n)-Math.floor(this._audioNextPts),l,i);f.originPts=Math.floor(this._baseDts+o),t.splice(a,0,f),this._audioNextPts+=n,a++}a--}else c<=-3*n&&c>=-1e3?(Math.abs(u.pts-this._lastAudioExceptionOverlapDot)>Kt&&(this._lastAudioExceptionOverlapDot=u.pts,e.warnings.push({type:bt,pts:u.pts,originPts:u.originPts,nextPts:o,refSampleDuration:n})),t.splice(a,1),a--):(Math.abs(c)>Ht&&(this._audioTimestampBreak++,Math.abs(u.pts-this._lastAudioExceptionLargeGapDot)>Kt&&(this._lastAudioExceptionLargeGapDot=u.pts,e.warnings.push({type:mt,time:u.pts/1e3,pts:u.pts,originPts:u.originPts,nextPts:o,sampleDuration:c,refSampleDuration:n}))),u.dts=u.pts=o,u.duration=i,this._audioNextPts+=n)}}},{key:"_getG711Duration",value:function(e){var t=e.sampleSize,r=e.channelCount,n=e.sampleRate,i=e.samples[0];if(i)return 2*i.data.byteLength/r/(t/8)/n*1e3}},{key:"_getSilentFrame",value:function(e){return e.codecType===ht?zt.getSilentFrame(e.codec,e.channelCount):new Uint8Array(8*e.sampleDuration*e.channelCount)}}]),e}(),Qt=function(){function e(){u(this,e)}return l(e,null,[{key:"parse",value:function(t){if(!(t.length<3)){var r={},n=e._parseValue(new DataView(t.buffer,t.byteOffset,t.byteLength)),i=e._parseValue(new DataView(t.buffer,t.byteOffset+n.size,t.byteLength-n.size));return r[n.data]=i.data,r}}},{key:"_parseValue",value:function(t){var r,n=t.byteLength,i=1,s=!1;switch(t.getUint8(0)){case 0:r=t.getFloat64(1),i+=8;break;case 1:r=!!t.getUint8(1),i+=1;break;case 2:var a=e._parseString(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i));r=a.data,i+=a.size;break;case 3:r={};var o=0;for(9==(16777215&t.getUint32(n-4))&&(o=3);i<n-4;){var u=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-o)),c=u.size,l=u.data;if(u.isEnd)break;r[l.name]=l.value,i+=c}if(i<=n-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 8:r={},i+=4;var d=0;for(9==(16777215&t.getUint32(n-4))&&(d=3);i<n-8;){var h=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-d)),f=h.size,p=h.data;if(h.isEnd)break;r[p.name]=p.value,i+=f}if(i<=n-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 9:r=void 0,i=1,s=!0;break;case 10:r=[];var v=t.getUint32(1);i+=4;for(var y=0;y<v;y++){var _=e._parseValue(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i)),m=_.data,g=_.size;r.push(m),i+=g}break;case 11:var b=t.getFloat64(i)+6e4*t.getInt16(i+8);r=new Date(b),i+=10;break;case 12:var k=t.getUint32(1);i+=4,r="",k>0&&(r=Ot.decode(new Uint8Array(t.buffer,t.byteOffset+i,k))),i+=k;break;default:i=n}return{data:r,size:i,isEnd:s}}},{key:"_parseString",value:function(e){var t=e.getUint16(0),r="";return t>0&&(r=Ot.decode(new Uint8Array(e.buffer,e.byteOffset+2,t))),{data:r,size:2+t}}},{key:"_parseObject",value:function(t){if(!(t.byteLength<3)){var r=e._parseString(t),n=e._parseValue(new DataView(t.buffer,t.byteOffset+r.size,t.byteLength-r.size));return{data:{name:r.data,value:n.data},size:r.size+n.size,isEnd:n.isEnd}}}}]),e}(),Yt=new Lt("FlvDemuxer"),Xt=function(){function e(t,r,n){u(this,e),d(this,"_headerParsed",!1),d(this,"_remainingData",null),d(this,"_gopId",0),d(this,"_needAddMetaBeforeKeyFrameNal",!0),this.videoTrack=t||new kt,this.audioTrack=r||new xt,this.metadataTrack=n||new At,this._fixer=new Wt(this.videoTrack,this.audioTrack,this.metadataTrack)}return l(e,[{key:"demux",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=this.audioTrack,s=this.videoTrack,a=this.metadataTrack;if(!r&&n||(this._remainingData=null),r&&(this._headerParsed=!1),r?(s.reset(),i.reset(),a.reset()):(s.samples=[],i.samples=[],a.seiSamples=[],a.flvScriptSamples=[],s.warnings=[],i.warnings=[],this._remainingData&&(t=Mt(this._remainingData,t),this._remainingData=null)),!t.length)return{videoTrack:s,audioTrack:i,metadataTrack:a};var o=0;if(!this._headerParsed){if(!e.probe(t))throw new Error("Invalid flv file");i.present=(4&t[4])>>>2!=0,s.present=0!=(1&t[4]),this._headerParsed=!0,o=It(t,5)+4}for(var u,c,l,d,h,f=t.length;o+15<f&&(u=t[o],!(o+15+(c=t[o+1]<<16|t[o+2]<<8|t[o+3])>f));)l=(t[o+7]<<24>>>0)+(t[o+4]<<16)+(t[o+5]<<8)+t[o+6],o+=11,d=t.subarray(o,o+c),8===u?this._parseAudio(d,l):9===u?this._parseVideo(d,l):18===u?this._parseScript(d,l):Yt.warn("Invalid tag type: ".concat(u)),(h=It(t,o+=c))!==11+c&&Yt.warn("Invalid PrevTagSize ".concat(h," (").concat(11+c,")")),o+=4;return o<f&&(this._remainingData=t.subarray(o)),i.formatTimescale=s.formatTimescale=s.timescale=a.timescale=1e3,i.timescale=i.sampleRate||0,!i.exist()&&i.hasSample()&&i.reset(),!s.exist()&&s.hasSample()&&s.reset(),{videoTrack:s,audioTrack:i,metadataTrack:a}}},{key:"fix",value:function(e,t,r){return this._fixer.fix(e,t,r),{videoTrack:this.videoTrack,audioTrack:this.audioTrack,metadataTrack:this.metadataTrack}}},{key:"demuxAndFix",value:function(e,t,r,n){return this.demux(e,t,r),this.fix(n,t,r)}},{key:"_parseAudio",value:function(t,r){if(t.length){var n=(240&t[0])>>>4,i=this.audioTrack;if(10!==n&&7!==n&&8!==n)return Yt.warn("Unsupported sound format: ".concat(n)),void i.reset();if(10!==n){var s=(12&t[0])>>2,a=(2&t[0])>>1,o=1&t[0];i.sampleRate=e.AUDIO_RATE[s],i.sampleSize=a?16:8,i.channelCount=o+1}10===n?this._parseAac(t,r):this._parseG711(t,r,n)}}},{key:"_parseG711",value:function(e,t,r){var n=this.audioTrack;n.codecType=7===r?ft:pt,n.sampleRate=8e3,n.codec=n.codecType,n.samples.push(new St(t,e.subarray(1)))}},{key:"_parseAac",value:function(e,t){var r=this.audioTrack;if(r.codecType=ht,0===e[1]){var n=zt.parseAudioSpecificConfig(e.subarray(2));n?(r.codec=n.codec,r.channelCount=n.channelCount,r.sampleRate=n.sampleRate,r.config=n.config,r.objectType=n.objectType,r.sampleRateIndex=n.samplingFrequencyIndex):(r.reset(),Yt.warn("Cannot parse AudioSpecificConfig",e))}else if(1===e[1]){if(null==t)return;r.samples.push(new St(t,e.subarray(2)))}else Yt.warn("Unknown AACPacketType: ".concat(e[1]))}},{key:"_parseVideo",value:function(e,t){var r=this;if(!(e.length<6)){var n=(240&e[0])>>>4,i=15&e[0],s=this.videoTrack;if(7!==i&&12!==i)return s.reset(),void Yt.warn("Unsupported codecId: ".concat(i));var a=12===i;s.codecType=a?dt:lt;var o=e[1],u=(e[2]<<16|e[3]<<8|e[4])<<8>>8;if(0===o){var c=e.subarray(5),l=a?Gt.parseHEVCDecoderConfigurationRecord(c):Vt.parseAVCDecoderConfigurationRecord(c);if(l){var d=l.hvcC,h=l.sps,f=l.ppsArr,p=l.spsArr,v=l.vpsArr,y=l.nalUnitSize;d&&(s.hvcC=s.hvcC||d),h&&(s.codec=h.codec,s.width=h.width,s.height=h.height,s.sarRatio=h.sarRatio,s.fpsNum=h.fpsNum,s.fpsDen=h.fpsDen),p.length&&(s.sps=p),f.length&&(s.pps=f),v&&v.length&&(s.vps=v),y&&(s.nalUnitSize=y)}else Yt.warn("Cannot parse ".concat(a?"HEVC":"AVC","DecoderConfigurationRecord"),e)}else if(1===o){var _=qt.parseAvcC(e.subarray(5),s.nalUnitSize);if((_=this._checkAddMetaNalToUnits(a,_,s))&&_.length){var m=new wt(t+u,t,_);1===n&&m.setToKeyframe(),s.samples.push(m),_.forEach((function(e){var n=a?e[0]>>>1&63:31&e[0];switch(n){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!a&&5!==n||a&&5===n)break;m.setToKeyframe();break;case 6:case 39:case 40:if(!a&&6!==n||a&&6===n)break;r.metadataTrack.seiSamples.push(new Tt(qt.parseSEI(qt.removeEPB(e),a),t+u))}})),m.keyframe&&this._gopId++,m.gopId=this._gopId}else Yt.warn("Cannot parse NALUs",e)}else 2===o||Yt.warn("Unknown AVCPacketType: ".concat(o))}}},{key:"_checkAddMetaNalToUnits",value:function(e,t,r){return e&&this._needAddMetaBeforeKeyFrameNal?t.map((function(e){return e[0]>>>1&63})).includes(32)?(this._needAddMetaBeforeKeyFrameNal=!1,t):(t.unshift(r.pps[0]),t.unshift(r.sps[0]),t.unshift(r.vps[0]),t.filter(Boolean)):(this._needAddMetaBeforeKeyFrameNal=!1,t)}},{key:"_parseScript",value:function(e,t){this.metadataTrack.flvScriptSamples.push(new jt(Qt.parse(e),t))}}],[{key:"probe",value:function(e){return 70===e[0]&&76===e[1]&&86===e[2]&&1===e[3]&&It(e,5)>=9}}]),e}();function Jt(e){for(var t=0,r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];n.forEach((function(e){t+=e.length}));var s=new e(t),a=0;return n.forEach((function(e){s.set(e,a),a+=e.length})),s}d(Xt,"AUDIO_RATE",[5500,11e3,22e3,44e3]),new Lt("TsDemuxer");var Zt=function(){function e(){u(this,e),this.buffer=new Uint8Array(0)}return l(e,[{key:"write",value:function(){for(var e=this,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];r.forEach((function(t){t?e.buffer=Jt(Uint8Array,e.buffer,t):window.console.warn(t)}))}}],[{key:"writeUint16",value:function(e){return new Uint8Array([e>>8&255,255&e])}},{key:"writeUint32",value:function(e){return new Uint8Array([e>>24,e>>16&255,e>>8&255,255&e])}}]),e}(),$t=Math.pow(2,32)-1,er=function(){function e(){u(this,e)}return l(e,null,[{key:"box",value:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=8+(r=r.filter(Boolean)).reduce((function(e,t){return e+t.byteLength}),0),s=new Uint8Array(i);s[0]=i>>24&255,s[1]=i>>16&255,s[2]=i>>8&255,s[3]=255&i,s.set(e,4);var a=8;return r.forEach((function(e){s.set(e,a),a+=e.byteLength})),s}},{key:"ftyp",value:function(t){return t.find((function(e){return e.type===ot&&e.codecType===dt}))?e.FTYPHEV1:e.FTYPAVC1}},{key:"initSegment",value:function(t){return Mt(e.ftyp(t),e.moov(t))}},{key:"pssh",value:function(t){var r=new Uint8Array([1,0,0,0].concat([16,119,239,236,192,178,77,2,172,227,60,30,82,226,251,75],[0,0,0,1],Nt(t.kid),[0,0,0,0]));return e.box(e.types.pssh,r)}},{key:"moov",value:function(t){if(t[0].useEME&&(t[0].encv||t[0].enca)){t[0].pssh||(t[0].pssh={kid:t[0].kid});var r=this.pssh(t[0].pssh);return e.box.apply(e,[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale),e.mvex(t)].concat(E(t.map((function(t){return e.trak(t)}))),[r]))}return e.box.apply(e,[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale)].concat(E(t.map((function(t){return e.trak(t)}))),[e.mvex(t)]))}},{key:"mvhd",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4,n=e.box(e.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,t>>24&255,t>>16&255,t>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]));return n}},{key:"trak",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.tkhdDuration||0,t.width,t.height),e.mdia(t))}},{key:"tkhd",value:function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=e.box(e.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>24&255,t>>16&255,t>>8&255,255&t,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>8&255,255&n,0,0,i>>8&255,255&i,0,0]));return s}},{key:"mdia",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minf(t))}},{key:"mdhd",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4,n=e.box(e.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,t>>24&255,t>>16&255,t>>8&255,255&t,85,196,0,0]));return n}},{key:"hdlr",value:function(t){return e.box(e.types.hdlr,e.HDLR_TYPES[t])}},{key:"minf",value:function(t){return e.box(e.types.minf,t.type===ot?e.VMHD:e.SMHD,e.DINF,e.stbl(t))}},{key:"stbl",value:function(t){var r=[];return t&&t.ext&&t.ext.stss&&r.push(e.stss(t.ext.stss.entries)),e.box(e.types.stbl,e.stsd(t),e.STTS,r[0],e.STSC,e.STSZ,e.STCO)}},{key:"stsd",value:function(t){var r;return r="audio"===t.type?t.useEME&&t.enca?e.enca(t):e.mp4a(t):t.useEME&&t.encv?e.encv(t):e.avc1hev1(t),e.box(e.types.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),r)}},{key:"enca",value:function(t){var r=t.enca.channelCount,n=t.enca.sampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,r,0,16,0,0,0,0,n>>8&255,255&n,0,0]),s=e.esds(t.config),a=e.sinf(t.enca);return e.box(e.types.enca,i,s,a)}},{key:"encv",value:function(t){var r,n,i=t.sps.length>0?t.sps[0]:[],s=t.pps.length>0?t.pps[0]:[],a=t.width,o=t.height,u=t.sarRatio[0],c=t.sarRatio[1],l=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,a>>8&255,255&a,o>>8&255,255&o,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),d=new Uint8Array((r=(n=[1,i[1],i[2],i[3],255,225,i.length>>>8&255,255&i.length]).concat.apply(n,E(i)).concat([1,s.length>>>8&255,255&s.length])).concat.apply(r,E(s))),h=new Uint8Array([0,0,88,57,0,15,200,192,0,4,86,72]),f=e.sinf(t.encv),p=new Uint8Array([u>>24,u>>16&255,u>>8&255,255&u,c>>24,c>>16&255,c>>8&255,255&c]);return e.box(e.types.encv,l,e.box(e.types.avcC,d),e.box(e.types.btrt,h),f,e.box(e.types.pasp,p))}},{key:"schi",value:function(t){var r=new Uint8Array([]),n=e.tenc(t);return e.box(e.types.schi,r,n)}},{key:"tenc",value:function(t){var r=new Uint8Array([0,0,0,0,0,0,255&t.default_IsEncrypted,255&t.default_IV_size].concat(Nt(t.default_KID)));return e.box(e.types.tenc,r)}},{key:"sinf",value:function(t){var r=new Uint8Array([]),n=new Uint8Array([t.data_format.charCodeAt(0),t.data_format.charCodeAt(1),t.data_format.charCodeAt(2),t.data_format.charCodeAt(3)]),i=new Uint8Array([0,0,0,0,99,101,110,99,0,1,0,0]),s=e.schi(t);return e.box(e.types.sinf,r,e.box(e.types.frma,n),e.box(e.types.schm,i),s)}},{key:"avc1hev1",value:function(t){var r=t.codecType===dt,n=r?e.types.hvc1:e.types.avc1,i=r?e.hvcC(t):e.avcC(t),s=[new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t.width>>8&255,255&t.width,t.height>>8&255,255&t.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),i];return r?s.push(e.box(e.types.fiel,new Uint8Array([1,0]))):t.sarRatio&&t.sarRatio.length>1&&s.push(e.pasp(t.sarRatio)),e.box.apply(e,[n].concat(s))}},{key:"avcC",value:function(t){var r,n,i,s=[],a=[];return t.sps.forEach((function(e){i=e.byteLength,s.push(i>>>8&255),s.push(255&i),s.push.apply(s,E(e))})),t.pps.forEach((function(e){i=e.byteLength,a.push(i>>>8&255),a.push(255&i),a.push.apply(a,E(e))})),e.box(e.types.avcC,new Uint8Array((r=(n=[1,s[3],s[4],s[5],255,224|t.sps.length]).concat.apply(n,s).concat([t.pps.length])).concat.apply(r,a)))}},{key:"hvcC",value:function(t){var r=t.hvcC;if(r instanceof ArrayBuffer||r instanceof Uint8Array)return r;var n,i=t.vps,s=t.sps,a=t.pps;if(r){var o=r.generalProfileCompatibilityFlags,u=r.generalConstraintIndicatorFlags,c=(i.length&&1)+(s.length&&1)+(a.length&&1);n=[1,r.generalProfileSpace<<6|r.generalTierFlag<<5|r.generalProfileIdc,o>>>24,o>>>16,o>>>8,o,u[0],u[1],u[2],u[3],u[4],u[5],r.generalLevelIdc,240,0,252,252|r.chromaFormatIdc,248|r.bitDepthLumaMinus8,248|r.bitDepthChromaMinus8,0,0,r.numTemporalLayers<<3|r.temporalIdNested<<2|3,c];var l=function(e){var t;n.push(e.length>>8,e.length),(t=n).push.apply(t,E(e))};i.length&&(n.push(160,0,i.length),i.forEach(l)),s.length&&(n.push(161,0,s.length),s.forEach(l)),a.length&&(n.push(162,0,a.length),a.forEach(l))}else n=[1,1,96,0,0,0,144,0,0,0,0,0,93,240,0,252,253,248,248,0,0,15,3,160,0,1,0,24,64,1,12,1,255,255,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,153,152,9,161,0,1,0,45,66,1,1,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,160,2,128,128,45,22,89,153,164,147,43,154,128,128,128,130,0,0,3,0,2,0,0,3,0,50,16,162,0,1,0,7,68,1,193,114,180,98,64];return e.box(e.types.hvcC,new Uint8Array(n))}},{key:"pasp",value:function(t){var r=S(t,2),n=r[0],i=r[1];return e.box(e.types.pasp,new Uint8Array([n>>24,n>>16&255,n>>8&255,255&n,i>>24,i>>16&255,i>>8&255,255&i]))}},{key:"mp4a",value:function(t){return e.box(e.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,t.sampleRate>>8&255,255&t.sampleRate,0,0]),t.config.length?e.esds(t.config):void 0)}},{key:"esds",value:function(t){var r=t.length;return e.box(e.types.esds,new Uint8Array([0,0,0,0,3,23+r,0,0,0,4,15+r,64,21,0,6,0,0,0,218,192,0,0,218,192,5].concat([r]).concat(t).concat([6,1,2])))}},{key:"mvex",value:function(t){return e.box.apply(e,[e.types.mvex].concat(E(t.map((function(t){return e.trex(t.id)})))))}},{key:"trex",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}},{key:"trex1",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,2,0,0,0,0,0,0,1,0,0]))}},{key:"trex2",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,4,0,0,0,0,0,2,0,0,0]))}},{key:"moof",value:function(t){return e.box.apply(e,[e.types.moof,e.mfhd(t[0].samples?t[0].samples[0].gopId:0)].concat(E(t.map((function(t){return e.traf(t)})))))}},{key:"mfhd",value:function(t){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"traf",value:function(t){var r=e.tfhd(t.id),n=e.tfdt(t,t.baseMediaDecodeTime),i=0;if(t.isVideo&&t.videoSenc&&t.videoSenc.forEach((function(e){i+=8,e.subsamples&&e.subsamples.length&&(i+=2,i+=6*e.subsamples.length)})),t.videoSencLength=i,t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)){if(t.isVideoEncryption){if(t.isVideo){var s=e.saiz(t),a=e.saio(t),o=e.trun1(t),u=e.senc(t);return e.box(e.types.traf,r,n,s,a,o,u)}if(t.isAudioEncryption){var c=e.sbgp(),l=e.saiz(t),d=e.saio(t),h=e.senc(t),f=e.trun1(t);return e.box(e.types.traf,r,n,c,l,d,h,f)}var p=e.sbgp(),v=e.trun1(t);return e.box(e.types.traf,r,n,p,v)}if(t.isVideo){var y=e.trun1(t);return e.box(e.types.traf,r,n,y)}var _=e.sbgp(),m=e.saiz(t),g=e.saio(t),b=e.senc(t),k=e.trun1(t);return e.box(e.types.traf,r,n,_,m,g,b,k)}var x=e.sdtp(t);return e.box(e.types.traf,r,n,x,e.trun(t.samples,x.byteLength+76))}},{key:"sdtp",value:function(t){var r=new Zt;return t.samples.forEach((function(e){r.write(new Uint8Array(t.isVideo?[e.keyframe?32:16]:[16]))})),e.box(e.types.sdtp,this.extension(0,0),r.buffer)}},{key:"trun1",value:function(t){var r=new Zt,n=Zt.writeUint32(t.samples.length),i=null;if(t.isVideo){var s=t.videoSencLength;i=Zt.writeUint32(16*t.samples.length+s+149),!t.isVideoEncryption&&t.isAudioEncryption&&(i=Zt.writeUint32(16*t.samples.length+92))}else{var a=12*t.samples.length+124;t.isAudioEncryption&&(a=12*t.samples.length+8*t.audioSenc.length+177),i=Zt.writeUint32(a)}return t.samples.forEach((function(e){r.write(Zt.writeUint32(e.duration)),r.write(Zt.writeUint32(e.size)),r.write(Zt.writeUint32(e.keyframe?33554432:65536)),t.isVideo&&r.write(Zt.writeUint32(e.cts?e.cts:0))})),e.box(e.types.trun,this.extension(0,t.flags),n,i,r.buffer)}},{key:"senc",value:function(t){var r=new Zt,n=t.samples.length,i=t.isVideo?16:8,s=t.isVideo?2:0,a=[],o=0;return t.isVideo?(a=t.videoSenc,o=t.videoSencLength):a=t.audioSenc,o=o||i*n,r.write(Zt.writeUint32(16+o),e.types.senc,this.extension(0,s)),r.write(Zt.writeUint32(n)),a.forEach((function(e){for(var t=0;t<e.InitializationVector.length;t++)r.write(new Uint8Array([e.InitializationVector[t]]));e.subsamples&&e.subsamples.length&&(r.write(Zt.writeUint16(e.subsamples.length)),e.subsamples.forEach((function(e){r.write(Zt.writeUint16(e.BytesOfClearData)),r.write(Zt.writeUint32(e.BytesOfProtectedData))})))})),r.buffer}},{key:"saio",value:function(t){var r=12*t.samples.length+141;!t.isVideo&&t.isAudioEncryption&&(r=149);var n=new Uint8Array([1,0,0,0,0,0,0,1,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r]);return e.box(e.types.saio,n)}},{key:"saiz",value:function(t){var r=t.samples.length,n=new Uint8Array([0,0,0,0,16,r>>24&255,r>>16&255,r>>8&255,255&r]);return e.box(e.types.saiz,n)}},{key:"sbgp",value:function(){var t=new Uint8Array([114,111,108,108,0,0,0,1,0,0,1,25,0,0,0,1]);return e.box(e.types.sbgp,this.extension(0,0),t)}},{key:"extension",value:function(e,t){return new Uint8Array([e,t>>16&255,t>>8&255,255&t])}},{key:"tfhd",value:function(t){return e.box(e.types.tfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"tfdt",value:function(t,r){var n=Math.floor(r/($t+1)),i=Math.floor(r%($t+1));return t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)?e.box(e.types.tfdt,new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i])):e.box(e.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,i>>24,i>>16&255,i>>8&255,255&i]))}},{key:"trun",value:function(t,r){var n=t.length,i=12+16*n;r+=8+i;var s=new Uint8Array(i);s.set([0,0,15,1,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0);for(var a=0;a<n;a++){var o=t[a],u=o.duration,c=o.size,l=o.flag,d=void 0===l?{}:l,h=o.cts,f=void 0===h?0:h;s.set([u>>>24&255,u>>>16&255,u>>>8&255,255&u,c>>>24&255,c>>>16&255,c>>>8&255,255&c,d.isLeading<<2|(null===d.dependsOn||void 0===d.dependsOn?1:d.dependsOn),d.isDependedOn<<6|d.hasRedundancy<<4|d.paddingValue<<1|(null===d.isNonSyncSample||void 0===d.isNonSyncSample?1:d.isNonSyncSample),61440&d.degradationPriority,15&d.degradationPriority,f>>>24&255,f>>>16&255,f>>>8&255,255&f],12+16*a)}return e.box(e.types.trun,s)}},{key:"moovMP4",value:function(t){return e.box.apply(e,[e.types.moov,e.mvhd(t[0].duration,t[0].timescale)].concat(E(t.map((function(t){return e.trackMP4(t)})))))}},{key:"trackMP4",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.duration,t.width,t.height),e.mdiaMP4(t))}},{key:"mdiaMP4",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minfMP4(t))}},{key:"minfMP4",value:function(t){return e.box(e.types.minf,t.type===ot?e.VMHD:e.SMHD,e.DINF,e.stblMP4(t))}},{key:"stblMP4",value:function(t){var r=t.ext,n=[e.stsd(t),e.stts(r.stts),e.stsc(r.stsc),e.stsz(r.stsz),e.stco(r.stco)];return r.stss.length&&n.push(e.stss(r.stss)),r.ctts.length&&n.push(e.ctts(r.ctts)),e.box.apply(e,[e.types.stbl].concat(n))}},{key:"stts",value:function(t){var r=t.length,n=new Uint8Array(8*r),i=0;return t.forEach((function(e){var t=e.value,r=e.count;n.set([r>>24,r>>16&255,r>>8&255,255&r,t>>24,t>>16&255,t>>8&255,255&t],i),i+=8})),e.box(e.types.stts,Mt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stsc",value:function(t){var r=t.length,n=new Uint8Array(12*r),i=0;return t.forEach((function(e){var t=e.firstChunk,r=e.samplesPerChunk,s=e.sampleDescIndex;n.set([t>>24,t>>16&255,t>>8&255,255&t,r>>24,r>>16&255,r>>8&255,255&r,s>>24,s>>16&255,s>>8&255,255&s],i),i+=12})),e.box(e.types.stsc,Mt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stsz",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stsz,Mt(new Uint8Array([0,0,0,0,0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stco",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stco,Mt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stss",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stss,Mt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"ctts",value:function(t){var r=t.length,n=new Uint8Array(8*r),i=0;return t.forEach((function(e){var t=e.value,r=e.count;n.set([r>>24,r>>16&255,r>>8&255,255&r,t>>24,t>>16&255,t>>8&255,255&t],i),i+=8})),e.box(e.types.ctts,Mt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"styp",value:function(){return e.box(e.types.styp,new Uint8Array([109,115,100,104,0,0,0,0,109,115,100,104,109,115,105,120]))}},{key:"sidx",value:function(t){var r=t.timescale,n=t.samples[0].duration,i=n*t.samples.length,s=t.samples[0].sampleOffset*n,a=8;t.samples.forEach((function(e){a+=e.size}));var o=0;if(t.isVideo){var u,c=0;t.videoSenc&&(u=t.videoSenc),t.isVideo&&u.forEach((function(e){c+=8,e.subsamples&&e.subsamples.length&&(c+=2,c+=6*e.subsamples.length)})),t.videoSencLength=c,o=a+141+16*t.samples.length+c,t.useEME&&t.isAudioEncryption&&!t.isVideoEncryption&&(o=a+16*t.samples.length+84)}else o=a+116+12*t.samples.length,t.useEME&&t.isAudioEncryption&&(o=a+169+12*t.samples.length+8*t.audioSenc.length);var l=new Uint8Array([0,0,0,0,0,0,0,255&t.id,r>>24&255,r>>16&255,r>>8&255,255&r,s>>24&255,s>>16&255,s>>8&255,255&s,0,0,0,0,0,0,0,1,0,o>>16&255,o>>8&255,255&o,i>>24&255,i>>16&255,i>>8&255,255&i,144,0,0,0]);return e.box(e.types.sidx,l)}},{key:"mdat",value:function(t){return e.box(e.types.mdat,t)}}]),e}();d(er,"types",["avc1","avcC","hvc1","hvcC","dinf","dref","esds","ftyp","hdlr","mdat","mdhd","mdia","mfhd","minf","moof","moov","mp4a","mvex","mvhd","pasp","stbl","stco","stsc","stsd","stsz","stts","tfdt","tfhd","traf","trak","trex","tkhd","vmhd","smhd","ctts","stss","styp","pssh","sidx","sbgp","saiz","saio","senc","trun","encv","enca","sinf","btrt","frma","tenc","schm","schi","mehd","fiel","sdtp"].reduce((function(e,t){return e[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)],e}),Object.create(null))),d(er,"HDLR_TYPES",{video:new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),audio:new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0])}),d(er,"FTYPAVC1",er.box(er.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]))),d(er,"FTYPHEV1",er.box(er.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,104,101,118,49]))),d(er,"DINF",er.box(er.types.dinf,er.box(er.types.dref,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1])))),d(er,"VMHD",er.box(er.types.vmhd,new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]))),d(er,"SMHD",er.box(er.types.smhd,new Uint8Array([0,0,0,0,0,0,0,0]))),d(er,"StblTable",new Uint8Array([0,0,0,0,0,0,0,0])),d(er,"STTS",er.box(er.types.stts,er.StblTable)),d(er,"STSC",er.box(er.types.stsc,er.StblTable)),d(er,"STSZ",er.box(er.types.stsz,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]))),d(er,"STCO",er.box(er.types.stco,er.StblTable));var tr=function(){function e(t,r){u(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),e.disabled=r}return l(e,[{key:"debug",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).debug.apply(t,[this._prefix].concat(n))}}},{key:"log",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).log.apply(t,[this._prefix].concat(n))}}},{key:"warn",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).warn.apply(t,[this._prefix].concat(n))}}},{key:"error",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).error.apply(t,[this._prefix].concat(n))}}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();d(tr,"disabled",!0);for(var rr=function(){function e(t,r,n){u(this,e),this.videoTrack=t,this.audioTrack=r;var i=/Chrome\/([^.]+)/.exec(navigator.userAgent);this.forceFirstIDR=i&&Number(i[1])<50,this.log=new tr("FMP4Remuxer",!n||!n.openLog||!n.openLog)}return l(e,[{key:"remux",value:function(){var e,t,r,n,i,s=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=this.videoTrack,u=this.audioTrack,c=o.exist(),l=u.exist(),d=[];return s&&(a&&a.initMerge?(c&&d.push(this.videoTrack),l&&d.push(this.audioTrack),r=er.initSegment(d)):(c&&(e=er.initSegment([this.videoTrack])),l&&(t=er.initSegment([this.audioTrack])))),c&&o.hasSample()&&(n=this._remuxVideo()),l&&u.hasSample()&&(i=this._remuxAudio()),o.samples=[],u.samples=[],{initSegment:r,videoInitSegment:e,audioInitSegment:t,videoSegment:n,audioSegment:i}}},{key:"_remuxVideo",value:function(){var e=this.videoTrack;this.forceFirstIDR&&(e.samples[0].flag={dependsOn:2,isNonSyncSample:0});var t=e.samples,r=0;t.forEach((function(e){r+=e.units.reduce((function(e,t){return e+t.byteLength}),0),r+=4*e.units.length}));for(var n,i=new Uint8Array(r),s=new DataView(i.buffer),a=function(e,r){r=t[o];var a=0;r.units.forEach((function(t){s.setUint32(e,t.byteLength),e+=4,i.set(t,e),e+=t.byteLength,a+=4+t.byteLength})),r.size=a,c=e,n=r},o=0,u=t.length,c=0;o<u;o++)a(c,n);var l=er.mdat(i);return Mt(er.moof([e]),l)}},{key:"_remuxAudio",value:function(){var e=this.audioTrack,t=new Uint8Array(e.samples.reduce((function(e,t){return e+t.size}),0));e.samples.reduce((function(e,r){return t.set(r.data,e),e+r.size}),0);var r=er.mdat(t);return Mt(er.moof([e]),r)}},{key:"reset",value:function(){this.videoTrack.reset(),this.audioTrack.reset()}}]),e}(),nr=function(){function e(){u(this,e)}return l(e,[{key:"mixIn",value:function(e){return Object.assign(this,e)}},{key:"clone",value:function(){var e=new this.constructor;return Object.assign(e,this),e}}],[{key:"create",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return y(this,t)}}]),e}(),ir=function(e){h(r,e);var t=k(r);function r(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4*n.length;u(this,r),e=t.call(this);var s=n;if(s instanceof ArrayBuffer&&(s=new Uint8Array(s)),(s instanceof Int8Array||s instanceof Uint8ClampedArray||s instanceof Int16Array||s instanceof Uint16Array||s instanceof Int32Array||s instanceof Uint32Array||s instanceof Float32Array||s instanceof Float64Array)&&(s=new Uint8Array(s.buffer,s.byteOffset,s.byteLength)),s instanceof Uint8Array){for(var a=s.byteLength,o=[],c=0;c<a;c+=1)o[c>>>2]|=s[c]<<24-c%4*8;e.words=o,e.sigBytes=a}else e.words=n,e.sigBytes=i;return e}return l(r,[{key:"toString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:sr;return e.stringify(this)}},{key:"concat",value:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var s=0;s<i;s+=1){var a=r[s>>>2]>>>24-s%4*8&255;t[n+s>>>2]|=a<<24-(n+s)%4*8}else for(var o=0;o<i;o+=4)t[n+o>>>2]=r[o>>>2];return this.sigBytes+=i,this}},{key:"clamp",value:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=Math.ceil(t/4)}},{key:"clone",value:function(){var e=w(f(r.prototype),"clone",this).call(this);return e.words=this.words.slice(0),e}}],[{key:"random",value:function(e){for(var t,n=[],i=function(e){var t=e,r=987654321,n=4294967295;return function(){var e=((r=36969*(65535&r)+(r>>16)&n)<<16)+(t=18e3*(65535&t)+(t>>16)&n)&n;return e/=4294967296,(e+=.5)*(Math.random()>.5?1:-1)}},s=0;s<e;s+=4){var a=i(4294967296*(t||Math.random()));t=987654071*a(),n.push(4294967296*a()|0)}return new r(n,e)}}]),r}(nr),sr={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i+=1){var s=t[i>>>2]>>>24-i%4*8&255;n.push((s>>>4).toString(16)),n.push((15&s).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new ir(r,t/2)}},ar=function(e){for(var t=e.length,r=[],n=0;n<t;n+=1)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new ir(r,t)},or=function(e){return ar(unescape(encodeURIComponent(e)))},ur=function(e){h(r,e);var t=k(r);function r(){var e;return u(this,r),(e=t.call(this))._minBufferSize=0,e}return l(r,[{key:"reset",value:function(){this._data=new ir,this._nDataBytes=0}},{key:"_append",value:function(e){var t=e;"string"==typeof t&&(t=or(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}},{key:"_process",value:function(e){var t,r=this._data,n=this.blockSize,i=r.words,s=r.sigBytes,a=s/(4*n),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*n,u=Math.min(4*o,s);if(o){for(var c=0;c<o;c+=n)this._doProcessBlock(i,c);t=i.splice(0,o),r.sigBytes-=u}return new ir(t,u)}},{key:"clone",value:function(){var e=w(f(r.prototype),"clone",this).call(this);return e._data=this._data.clone(),e}}]),r}(nr),cr=function(e){h(r,e);var t=k(r);function r(e){var n;return u(this,r),(n=t.call(this)).blockSize=16,n.cfg=Object.assign(new nr,e),n.reset(),n}return l(r,[{key:"reset",value:function(){w(f(r.prototype),"reset",this).call(this),this._doReset()}},{key:"update",value:function(e){return this._append(e),this._process(),this}},{key:"finalize",value:function(e){return e&&this._append(e),this._doFinalize()}}],[{key:"_createHelper",value:function(e){return function(t,r){return new e(r).finalize(t)}}},{key:"_createHmacHelper",value:function(e){return function(t,r){return new lr(e,r).finalize(t)}}}]),r}(ur),lr=function(e){h(r,e);var t=k(r);function r(e,n){var i;u(this,r),i=t.call(this);var s=new e;i._hasher=s;var a=n;"string"==typeof a&&(a=or(a));var o=s.blockSize,c=4*o;a.sigBytes>c&&(a=s.finalize(n)),a.clamp();var l=a.clone();i._oKey=l;var d=a.clone();i._iKey=d;for(var h=l.words,f=d.words,p=0;p<o;p+=1)h[p]^=1549556828,f[p]^=909522486;return l.sigBytes=c,d.sigBytes=c,i.reset(),i}return l(r,[{key:"reset",value:function(){var e=this._hasher;e.reset(),e.update(this._iKey)}},{key:"update",value:function(e){return this._hasher.update(e),this}},{key:"finalize",value:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}}]),r}(nr),dr={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],s=0;s<r;s+=3)for(var a=(t[s>>>2]>>>24-s%4*8&255)<<16|(t[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|t[s+2>>>2]>>>24-(s+2)%4*8&255,o=0;o<4&&s+.75*o<r;o+=1)i.push(n.charAt(a>>>6*(3-o)&63));var u=n.charAt(64);if(u)for(;i.length%4;)i.push(u);return i.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){this._reverseMap=[],n=this._reverseMap;for(var i=0;i<r.length;i+=1)n[r.charCodeAt(i)]=i}var s=r.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return function(e,t,r){for(var n=[],i=0,s=0;s<t;s+=1)if(s%4){var a=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;n[i>>>2]|=a<<24-i%4*8,i+=1}return ir.create(n,i)}(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},hr=[],fr=0;fr<64;fr+=1)hr[fr]=4294967296*Math.abs(Math.sin(fr+1))|0;var pr=function(e,t,r,n,i,s,a){var o=e+(t&r|~t&n)+i+a;return(o<<s|o>>>32-s)+t},vr=function(e,t,r,n,i,s,a){var o=e+(t&n|r&~n)+i+a;return(o<<s|o>>>32-s)+t},yr=function(e,t,r,n,i,s,a){var o=e+(t^r^n)+i+a;return(o<<s|o>>>32-s)+t},_r=function(e,t,r,n,i,s,a){var o=e+(r^(t|~n))+i+a;return(o<<s|o>>>32-s)+t},mr=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r,[{key:"_doReset",value:function(){this._hash=new ir([1732584193,4023233417,2562383102,271733878])}},{key:"_doProcessBlock",value:function(e,t){for(var r=e,n=0;n<16;n+=1){var i=t+n,s=e[i];r[i]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}var a=this._hash.words,o=r[t+0],u=r[t+1],c=r[t+2],l=r[t+3],d=r[t+4],h=r[t+5],f=r[t+6],p=r[t+7],v=r[t+8],y=r[t+9],_=r[t+10],m=r[t+11],g=r[t+12],b=r[t+13],k=r[t+14],x=r[t+15],w=a[0],S=a[1],E=a[2],j=a[3];w=pr(w,S,E,j,o,7,hr[0]),j=pr(j,w,S,E,u,12,hr[1]),E=pr(E,j,w,S,c,17,hr[2]),S=pr(S,E,j,w,l,22,hr[3]),w=pr(w,S,E,j,d,7,hr[4]),j=pr(j,w,S,E,h,12,hr[5]),E=pr(E,j,w,S,f,17,hr[6]),S=pr(S,E,j,w,p,22,hr[7]),w=pr(w,S,E,j,v,7,hr[8]),j=pr(j,w,S,E,y,12,hr[9]),E=pr(E,j,w,S,_,17,hr[10]),S=pr(S,E,j,w,m,22,hr[11]),w=pr(w,S,E,j,g,7,hr[12]),j=pr(j,w,S,E,b,12,hr[13]),E=pr(E,j,w,S,k,17,hr[14]),S=pr(S,E,j,w,x,22,hr[15]),w=vr(w,S,E,j,u,5,hr[16]),j=vr(j,w,S,E,f,9,hr[17]),E=vr(E,j,w,S,m,14,hr[18]),S=vr(S,E,j,w,o,20,hr[19]),w=vr(w,S,E,j,h,5,hr[20]),j=vr(j,w,S,E,_,9,hr[21]),E=vr(E,j,w,S,x,14,hr[22]),S=vr(S,E,j,w,d,20,hr[23]),w=vr(w,S,E,j,y,5,hr[24]),j=vr(j,w,S,E,k,9,hr[25]),E=vr(E,j,w,S,l,14,hr[26]),S=vr(S,E,j,w,v,20,hr[27]),w=vr(w,S,E,j,b,5,hr[28]),j=vr(j,w,S,E,c,9,hr[29]),E=vr(E,j,w,S,p,14,hr[30]),S=vr(S,E,j,w,g,20,hr[31]),w=yr(w,S,E,j,h,4,hr[32]),j=yr(j,w,S,E,v,11,hr[33]),E=yr(E,j,w,S,m,16,hr[34]),S=yr(S,E,j,w,k,23,hr[35]),w=yr(w,S,E,j,u,4,hr[36]),j=yr(j,w,S,E,d,11,hr[37]),E=yr(E,j,w,S,p,16,hr[38]),S=yr(S,E,j,w,_,23,hr[39]),w=yr(w,S,E,j,b,4,hr[40]),j=yr(j,w,S,E,o,11,hr[41]),E=yr(E,j,w,S,l,16,hr[42]),S=yr(S,E,j,w,f,23,hr[43]),w=yr(w,S,E,j,y,4,hr[44]),j=yr(j,w,S,E,g,11,hr[45]),E=yr(E,j,w,S,x,16,hr[46]),S=yr(S,E,j,w,c,23,hr[47]),w=_r(w,S,E,j,o,6,hr[48]),j=_r(j,w,S,E,p,10,hr[49]),E=_r(E,j,w,S,k,15,hr[50]),S=_r(S,E,j,w,h,21,hr[51]),w=_r(w,S,E,j,g,6,hr[52]),j=_r(j,w,S,E,l,10,hr[53]),E=_r(E,j,w,S,_,15,hr[54]),S=_r(S,E,j,w,u,21,hr[55]),w=_r(w,S,E,j,v,6,hr[56]),j=_r(j,w,S,E,x,10,hr[57]),E=_r(E,j,w,S,f,15,hr[58]),S=_r(S,E,j,w,b,21,hr[59]),w=_r(w,S,E,j,d,6,hr[60]),j=_r(j,w,S,E,m,10,hr[61]),E=_r(E,j,w,S,c,15,hr[62]),S=_r(S,E,j,w,y,21,hr[63]),a[0]=a[0]+w|0,a[1]=a[1]+S|0,a[2]=a[2]+E|0,a[3]=a[3]+j|0}},{key:"_doFinalize",value:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32;var i=Math.floor(r/4294967296),s=r;t[15+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),t[14+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),e.sigBytes=4*(t.length+1),this._process();for(var a=this._hash,o=a.words,u=0;u<4;u+=1){var c=o[u];o[u]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}return a}},{key:"clone",value:function(){var e=w(f(r.prototype),"clone",this).call(this);return e._hash=this._hash.clone(),e}}]),r}(cr);cr._createHelper(mr),cr._createHmacHelper(mr);var gr=function(e){h(r,e);var t=k(r);function r(e){var n;return u(this,r),(n=t.call(this)).cfg=Object.assign(new nr,{keySize:4,hasher:mr,iterations:1},e),n}return l(r,[{key:"compute",value:function(e,t){for(var r,n=this.cfg,i=n.hasher.create(),s=ir.create(),a=s.words,o=n.keySize,u=n.iterations;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(var c=1;c<u;c+=1)r=i.finalize(r),i.reset();s.concat(r)}return s.sigBytes=4*o,s}}]),r}(nr),br=function(e){h(r,e);var t=k(r);function r(e,n,i){var s;return u(this,r),(s=t.call(this)).cfg=Object.assign(new nr,i),s._xformMode=e,s._key=n,s.reset(),s}return l(r,[{key:"reset",value:function(){w(f(r.prototype),"reset",this).call(this),this._doReset()}},{key:"process",value:function(e){return this._append(e),this._process()}},{key:"finalize",value:function(e){return e&&this._append(e),this._doFinalize()}}],[{key:"createEncryptor",value:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}},{key:"createDecryptor",value:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}},{key:"_createHelper",value:function(e){var t=function(e){return"string"==typeof e?Lr:Ar};return{encrypt:function(r,n,i){return t(n).encrypt(e,r,n,i)},decrypt:function(r,n,i){return t(n).decrypt(e,r,n,i)}}}}]),r}(ur);br._ENC_XFORM_MODE=1,br._DEC_XFORM_MODE=2,br.keySize=4,br.ivSize=4;var kr=function(e){h(r,e);var t=k(r);function r(e,n){var i;return u(this,r),(i=t.call(this))._cipher=e,i._iv=n,i}return l(r,null,[{key:"createEncryptor",value:function(e,t){return this.Encryptor.create(e,t)}},{key:"createDecryptor",value:function(e,t){return this.Decryptor.create(e,t)}}]),r}(nr);function xr(e,t,r){var n,i=e,s=this._iv;s?(n=s,this._iv=void 0):n=this._prevBlock;for(var a=0;a<r;a+=1)i[t+a]^=n[a]}var wr=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r)}(kr);wr.Encryptor=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r,[{key:"processBlock",value:function(e,t){var r=this._cipher,n=r.blockSize;xr.call(this,e,t,n),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}}]),r}(wr),wr.Decryptor=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r,[{key:"processBlock",value:function(e,t){var r=this._cipher,n=r.blockSize,i=e.slice(t,t+n);r.decryptBlock(e,t),xr.call(this,e,t,n),this._prevBlock=i}}]),r}(wr);var Sr={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[],a=0;a<n;a+=4)s.push(i);var o=ir.create(s,n);e.concat(o)},unpad:function(e){var t=e,r=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=r}},Er=function(e){h(r,e);var t=k(r);function r(e,n,i){var s;return u(this,r),(s=t.call(this,e,n,Object.assign({mode:wr,padding:Sr},i))).blockSize=4,s}return l(r,[{key:"reset",value:function(){var e;w(f(r.prototype),"reset",this).call(this);var t=this.cfg,n=t.iv,i=t.mode;this._xformMode===this.constructor._ENC_XFORM_MODE?e=i.createEncryptor:(e=i.createDecryptor,this._minBufferSize=1),this._mode=e.call(i,this,n&&n.words),this._mode.__creator=e}},{key:"_doProcessBlock",value:function(e,t){this._mode.processBlock(e,t)}},{key:"_doFinalize",value:function(){var e,t=this.cfg.padding;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}}]),r}(br),jr=function(e){h(r,e);var t=k(r);function r(e){var n;return u(this,r),(n=t.call(this)).mixIn(e),n}return l(r,[{key:"toString",value:function(e){return(e||this.formatter).stringify(this)}}]),r}(nr),Tr={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?ir.create([1398893684,1701076831]).concat(r).concat(t):t).toString(dr)},parse:function(e){var t,r=dr.parse(e),n=r.words;return 1398893684===n[0]&&1701076831===n[1]&&(t=ir.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),jr.create({ciphertext:r,salt:t})}},Ar=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r,null,[{key:"encrypt",value:function(e,t,r,n){var i=Object.assign(new nr,this.cfg,n),s=e.createEncryptor(r,i),a=s.finalize(t),o=s.cfg;return jr.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:s.blockSize,formatter:i.format})}},{key:"decrypt",value:function(e,t,r,n){var i=t,s=Object.assign(new nr,this.cfg,n);return i=this._parse(i,s.format),e.createDecryptor(r,s).finalize(i.ciphertext)}},{key:"_parse",value:function(e,t){return"string"==typeof e?t.parse(e,this):e}}]),r}(nr);Ar.cfg=Object.assign(new nr,{format:Tr});var Dr={execute:function(e,t,r,n){var i=n;i||(i=ir.random(8));var s=gr.create({keySize:t+r}).compute(e,i),a=ir.create(s.words.slice(t),4*r);return s.sigBytes=4*t,jr.create({key:s,iv:a,salt:i})}},Lr=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r,null,[{key:"encrypt",value:function(e,t,r,n){var i=Object.assign(new nr,this.cfg,n),s=i.kdf.execute(r,e.keySize,e.ivSize);i.iv=s.iv;var a=Ar.encrypt.call(this,e,t,s.key,i);return a.mixIn(s),a}},{key:"decrypt",value:function(e,t,r,n){var i=t,s=Object.assign(new nr,this.cfg,n);i=this._parse(i,s.format);var a=s.kdf.execute(r,e.keySize,e.ivSize,i.salt);return s.iv=a.iv,Ar.decrypt.call(this,e,i,a.key,s)}}]),r}(Ar);Lr.cfg=Object.assign(Ar.cfg,{kdf:Dr});for(var Or=[],Cr=[],Rr=[],Br=[],Pr=[],Ur=[],Mr=[],Ir=[],Fr=[],Nr=[],qr=[],Vr=0;Vr<256;Vr+=1)qr[Vr]=Vr<128?Vr<<1:Vr<<1^283;for(var zr=0,Gr=0,Hr=0;Hr<256;Hr+=1){var Kr=Gr^Gr<<1^Gr<<2^Gr<<3^Gr<<4;Kr=Kr>>>8^255&Kr^99,Or[zr]=Kr,Cr[Kr]=zr;var Wr=qr[zr],Qr=qr[Wr],Yr=qr[Qr],Xr=257*qr[Kr]^16843008*Kr;Rr[zr]=Xr<<24|Xr>>>8,Br[zr]=Xr<<16|Xr>>>16,Pr[zr]=Xr<<8|Xr>>>24,Ur[zr]=Xr,Xr=16843009*Yr^65537*Qr^257*Wr^16843008*zr,Mr[Kr]=Xr<<24|Xr>>>8,Ir[Kr]=Xr<<16|Xr>>>16,Fr[Kr]=Xr<<8|Xr>>>24,Nr[Kr]=Xr,zr?(zr=Wr^qr[qr[qr[Yr^Wr]]],Gr^=qr[qr[Gr]]):zr=Gr=1}var Jr=[0,1,2,4,8,16,32,64,128,27,54],Zr=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r,[{key:"_doReset",value:function(){var e;if(!this._nRounds||this._keyPriorReset!==this._key){this._keyPriorReset=this._key;var t=this._keyPriorReset,r=t.words,n=t.sigBytes/4;this._nRounds=n+6;var i=4*(this._nRounds+1);this._keySchedule=[];for(var s=this._keySchedule,a=0;a<i;a+=1)a<n?s[a]=r[a]:(e=s[a-1],a%n?n>6&&a%n==4&&(e=Or[e>>>24]<<24|Or[e>>>16&255]<<16|Or[e>>>8&255]<<8|Or[255&e]):(e=Or[(e=e<<8|e>>>24)>>>24]<<24|Or[e>>>16&255]<<16|Or[e>>>8&255]<<8|Or[255&e],e^=Jr[a/n|0]<<24),s[a]=s[a-n]^e);this._invKeySchedule=[];for(var o=this._invKeySchedule,u=0;u<i;u+=1){var c=i-u;e=u%4?s[c]:s[c-4],o[u]=u<4||c<=4?e:Mr[Or[e>>>24]]^Ir[Or[e>>>16&255]]^Fr[Or[e>>>8&255]]^Nr[Or[255&e]]}}}},{key:"encryptBlock",value:function(e,t){this._doCryptBlock(e,t,this._keySchedule,Rr,Br,Pr,Ur,Or)}},{key:"decryptBlock",value:function(e,t){var r=e,n=r[t+1];r[t+1]=r[t+3],r[t+3]=n,this._doCryptBlock(r,t,this._invKeySchedule,Mr,Ir,Fr,Nr,Cr),n=r[t+1],r[t+1]=r[t+3],r[t+3]=n}},{key:"_doCryptBlock",value:function(e,t,r,n,i,s,a,o){for(var u=e,c=this._nRounds,l=u[t]^r[0],d=u[t+1]^r[1],h=u[t+2]^r[2],f=u[t+3]^r[3],p=4,v=1;v<c;v+=1){var y=n[l>>>24]^i[d>>>16&255]^s[h>>>8&255]^a[255&f]^r[p];p+=1;var _=n[d>>>24]^i[h>>>16&255]^s[f>>>8&255]^a[255&l]^r[p];p+=1;var m=n[h>>>24]^i[f>>>16&255]^s[l>>>8&255]^a[255&d]^r[p];p+=1;var g=n[f>>>24]^i[l>>>16&255]^s[d>>>8&255]^a[255&h]^r[p];p+=1,l=y,d=_,h=m,f=g}var b=(o[l>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&f])^r[p];p+=1;var k=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[f>>>8&255]<<8|o[255&l])^r[p];p+=1;var x=(o[h>>>24]<<24|o[f>>>16&255]<<16|o[l>>>8&255]<<8|o[255&d])^r[p];p+=1;var w=(o[f>>>24]<<24|o[l>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p];p+=1,u[t]=b,u[t+1]=k,u[t+2]=x,u[t+3]=w}}]),r}(Er);Zr.keySize=8,Er._createHelper(Zr);var $r=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r)}(kr);$r.Encryptor=function(e){h(r,e);var t=k(r);function r(){return u(this,r),t.apply(this,arguments)}return l(r,[{key:"processBlock",value:function(e,t){var r=e,n=this._cipher,i=n.blockSize,s=this._iv,a=this._counter;s&&(this._counter=s.slice(0),a=this._counter,this._iv=void 0);var o=a.slice(0);n.encryptBlock(o,0),a[i-1]=a[i-1]+1|0;for(var u=0;u<i;u+=1)r[t+u]^=o[u]}}]),r}($r),$r.Decryptor=$r.Encryptor;var en=new X("BufferService"),tn=function(){function e(t,r,n){u(this,e),d(this,"flv",null),d(this,"_demuxer",new Xt),d(this,"_remuxer",null),d(this,"_mse",null),d(this,"_softVideo",null),d(this,"_sourceCreated",!1),d(this,"_needInitSegment",!0),d(this,"_discontinuity",!0),d(this,"_contiguous",!1),d(this,"_initSegmentId",""),d(this,"_cachedBuffer",null),d(this,"_demuxStartTime",0),d(this,"_opts",null),this.flv=t,this._opts=n,r?this._softVideo=r:(this._remuxer=new rr(this._demuxer.videoTrack,this._demuxer.audioTrack),this._mse=new se(null,{preferMMS:"boolean"==typeof n.preferMMS?n.preferMMS:!!n.perferMMS}),this._mse.bindMedia(t.media))}var t,r,s,a,c,h;return l(e,[{key:"baseDts",get:function(){var e,t;return null===(e=this._demuxer)||void 0===e||null===(t=e._fixer)||void 0===t?void 0:t._baseDts}},{key:"blobUrl",get:function(){var e;return null===(e=this._mse)||void 0===e?void 0:e.url}},{key:"isFull",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:se.VIDEO;return this._mse.isFull(e)}},{key:"seamlessSwitch",value:function(){this._needInitSegment=!0,this._discontinuity=!0,this._contiguous=!0,this._initSegmentId=""}},{key:"unContiguous",value:function(e){this._contiguous=!1,this._demuxStartTime=e}},{key:"reset",value:(h=o(i().mark((function e(){var t,r=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=r.length>0&&void 0!==r[0]&&r[0],!this._mse||t){e.next=6;break}return e.next=4,this._mse.unbindMedia();case 4:return e.next=6,this._mse.bindMedia(this.flv.media);case 6:this._needInitSegment=!0,this._discontinuity=!0,this._contiguous=!1,this._sourceCreated=!1,this._initSegmentId="";case 11:case"end":return e.stop()}}),e,this)}))),function(){return h.apply(this,arguments)})},{key:"endOfStream",value:(c=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=7;break}if(!this._sourceCreated){e.next=5;break}return e.next=4,this._mse.endOfStream();case 4:this.flv.emit(Ae);case 5:e.next=8;break;case 7:this._softVideo&&this._softVideo.endOfStream();case 8:case"end":return e.stop()}}),e,this)}))),function(){return c.apply(this,arguments)})},{key:"updateDuration",value:(a=o(i().mark((function e(t){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=7;break}if(this._mse.isOpened){e.next=4;break}return e.next=4,this._mse.open();case 4:return en.debug("update duration",t),e.next=7,this._mse.updateDuration(t);case 7:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"destroy",value:(s=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=3;break}return e.next=3,this._mse.unbindMedia();case 3:this._mse=null,this._softVideo=null,this._demuxer=null,this._remuxer=null;case 7:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"appendBuffer",value:(r=o(i().mark((function e(t){var r,n,s,a,o,u,c,l,d,h,f,p,v,y;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._cachedBuffer&&(t=$e(this._cachedBuffer,t),this._cachedBuffer=null),r=this._demuxer,t&&t.length&&r){e.next=4;break}return e.abrupt("return");case 4:e.prev=4,r.demuxAndFix(t,this._discontinuity,this._contiguous,this._demuxStartTime),e.next=11;break;case 8:throw e.prev=8,e.t0=e.catch(4),new K(I,G.FLV,e.t0);case 11:if(n=r.videoTrack,s=r.audioTrack,a=r.metadataTrack,o=n.exist(),u=s.exist(),this._opts.onlyAudio&&(o=!1,n.present=!1),this._opts.onlyVideo&&(u=!1,s.present=!1),!(!o&&n.present||!u&&s.present)){e.next=29;break}if(c=0,(l=o?n:u?s:void 0)&&l.samples.length&&(c=(l.samples[l.samples.length-1].originPts-l.samples[0].originPts)/l.timescale*1e3),!(c>this._opts.analyzeDuration)){e.next=27;break}en.warn("analyze duration exceeded, ".concat(c,"ms"),l),n.present=o,s.present=u,this.flv.emit(je,{duration:c}),e.next=29;break;case 27:return this._cachedBuffer=t,e.abrupt("return");case 29:if(d=n.type,h=s.type,this._fireEvents(n,s,a),this._discontinuity=!1,this._contiguous=!0,this._demuxStartTime=0,f=this._mse,this.flv.emit(Me,{videoTrack:n}),(p="".concat(n.codec,"/").concat(n.width,"/").concat(n.height,"/").concat(s.codec,"/").concat(s.config))!==this._initSegmentId&&(this._needInitSegment=!0,this._initSegmentId=p,this._emitMetaParsedEvent(n,s)),!f){e.next=65;break}if(this._sourceCreated){e.next=47;break}return e.next=43,f.open();case 43:o&&(en.log("codec: video/mp4;codecs=".concat(n.codec)),f.createSource(d,"video/mp4;codecs=".concat(n.codec))),u&&(en.log("codec: audio/mp4;codecs=".concat(s.codec)),f.createSource(h,"audio/mp4;codecs=".concat(s.codec))),this._sourceCreated=!0,this.flv.emit(Ee);case 47:e.prev=47,v=this._remuxer.remux(this._needInitSegment),e.next=54;break;case 51:throw e.prev=51,e.t1=e.catch(47),new K(F,G.FMP4,e.t1);case 54:if(!this._needInitSegment||v.videoInitSegment||v.audioInitSegment){e.next=56;break}return e.abrupt("return");case 56:return this._needInitSegment=!1,y=[],v.videoInitSegment&&y.push(f.append(d,v.videoInitSegment)),v.audioInitSegment&&y.push(f.append(h,v.audioInitSegment)),v.videoSegment&&y.push(f.append(d,v.videoSegment)),v.audioSegment&&y.push(f.append(h,v.audioSegment)),e.abrupt("return",Promise.all(y));case 65:this._softVideo&&this._softVideo.appendBuffer(n,s);case 66:case"end":return e.stop()}}),e,this,[[4,8],[47,51]])}))),function(e){return r.apply(this,arguments)})},{key:"evictBuffer",value:(t=o(i().mark((function e(t){var r,n,s,a=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.flv.media,this._mse&&this._demuxer&&r&&t&&!(t<0)){e.next=3;break}return e.abrupt("return");case 3:if(n=r.currentTime,!((s=n-t)<=0)){e.next=7;break}return e.abrupt("return");case 7:if(!(C.start(C.get(r))+1>=s)){e.next=10;break}return e.abrupt("return");case 10:return e.abrupt("return",this._mse.clearBuffer(0,s).then((function(){return a.flv.emit(Te,{removeEnd:s})})));case 11:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"_emitMetaParsedEvent",value:function(e,t){e.exist()&&this.flv.emit(Le,{type:"video",track:e,meta:{codec:e.codec,timescale:e.timescale,width:e.width,height:e.height,sarRatio:e.sarRatio,baseDts:e.baseDts}}),t.exist()&&this.flv.emit(Le,{type:"audio",track:t,meta:{codec:t.codec,channelCount:t.channelCount,sampleRate:t.sampleRate,timescale:t.timescale,baseDts:t.baseDts}}),en.debug("track parsed",e,t)}},{key:"_fireEvents",value:function(e,t,r){var i=this;en.debug(e.samples,t.samples),r.flvScriptSamples.forEach((function(e){i.flv.emit(Re,e),en.debug("flvScriptData",e)})),e.samples.forEach((function(e){e.keyframe&&i.flv.emit(De,{pts:e.pts})})),e.warnings.forEach((function(e){var t;switch(e.type){case vt:t=Fe;break;case yt:t=Ne;break;case _t:t=Ge}t&&i.flv.emit(Ie,n(n({},e),{},{type:t})),en.warn("video exception",e)})),t.warnings.forEach((function(e){var t;switch(e.type){case mt:t=qe;break;case gt:t=Ve;break;case bt:t=ze}t&&i.flv.emit(Ie,n(n({},e),{},{type:t})),en.warn("audio exception",e)})),r.seiSamples.forEach((function(e){i.flv.emit(Oe,n(n({},e),{},{sei:{code:e.data.type,content:e.data.payload,dts:e.pts}}))}))}}]),e}();function rn(e,t){var r=0,n=e.length-1,i=0,s=0,a=n;for(t<e[0]&&(r=0,s=a+1);s<=a;){if((i=s+Math.floor((a-s)/2))===n||t>=e[i]&&t<e[i+1]){r=i;break}e[i]<t?s=i+1:a=i-1}return r}var nn=new X("flv"),sn=.1,an=function(e){h(y,e);var t,r,s,a,c,f,p,v=k(y);function y(e){var t,r;return u(this,y),d(g(t=v.call(this)),"media",null),d(g(t),"_loading",!1),d(g(t),"_opts",null),d(g(t),"_bufferService",null),d(g(t),"_gapService",null),d(g(t),"_stats",null),d(g(t),"_mediaLoader",null),d(g(t),"_maxChunkWaitTimer",null),d(g(t),"_tickTimer",null),d(g(t),"_tickInterval",500),d(g(t),"_urlSwitching",!1),d(g(t),"_seamlessSwitching",!1),d(g(t),"_disconnectRetryCount",0),d(g(t),"_preLoadEndPoint",0),d(g(t),"_keyframes",null),d(g(t),"_acceptRanges",!0),d(g(t),"_onProgress",function(){var e=o(i().mark((function e(r,n,s,a){var o,u,c,l,d,h,f,p,v;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=s.startTime,u=s.endTime,c=s.st,l=s.firstByteTime,t._loading=!n,t._firstProgressEmit){e.next=11;break}if(t.media){e.next=6;break}return null===(d=t._mediaLoader)||void 0===d||d.cancel(),e.abrupt("return");case 6:h=a.headers,t.emit(be,{url:t._opts.url,responseUrl:a.url,elapsed:c?l-c:u-o}),t.emit(xe,{headers:h}),t._acceptRanges=!(null==h||!h.get("Accept-Ranges"))||!(null==h||!h.get("Content-Range")),t._firstProgressEmit=!0;case 11:if(t._bufferService){e.next=13;break}return e.abrupt("return");case 13:return clearTimeout(t._maxChunkWaitTimer),t._bandwidthService.addChunkRecord(null==r?void 0:r.byteLength,u-o),e.prev=15,e.next=18,t._bufferService.appendBuffer(r);case 18:null===(f=t._bufferService)||void 0===f||f.evictBuffer(t._opts.bufferBehind),e.next=31;break;case 21:if(e.prev=21,e.t0=e.catch(15),t.isLive||!t._bufferService.isFull()){e.next=30;break}return e.next=26,t._mediaLoader.cancel();case 26:return t._loading=!1,p=t.bufferInfo().remaining,t._opts.preloadTime=parseInt(p)/2,e.abrupt("return");case 30:return e.abrupt("return",t._emitError(K.create(e.t0)));case 31:if(t._urlSwitching&&(t._urlSwitching=!1,t.emit(Pe,{url:t._opts.url})),t._seamlessSwitching&&(t._seamlessSwitching=!1,t._tick()),!n||t.media.seeking){e.next=38;break}return t.emit(we),nn.debug("load done"),t.isLive&&t._disconnectRetryCount<=0&&t._end(),e.abrupt("return");case 38:if(t.isLive){e.next=40;break}return e.abrupt("return");case 40:(v=t._opts.maxReaderInterval)&&(clearTimeout(t._maxChunkWaitTimer),t._maxChunkWaitTimer=setTimeout((function(){if(t._disconnectRetryCount)return t._disconnectRetryCount--,void t.load();nn.debug("onMaxChunkWait",v),t._end()}),v));case 42:case"end":return e.stop()}}),e,null,[[15,21]])})));return function(t,r,n,i){return e.apply(this,arguments)}}()),d(g(t),"_onRetryError",(function(e,r){nn.debug("load retry",e,r),t.emit(Se,{error:K.network(e),retryTime:r})})),d(g(t),"_end",(function(){t._clear(),t._bufferService&&t._bufferService.endOfStream(),nn.debug("end stream")})),d(g(t),"_resetDisconnectCount",(function(){t._disconnectRetryCount=t._opts.disconnectRetryCount})),d(g(t),"_tick",(function(){clearTimeout(t._tickTimer);var e=g(t).media;if(e){t._tickTimer=setTimeout(t._tick,t._tickInterval);var r=C.end(C.get(e));if(!(r<sn)&&e.readyState){var n=t._opts;if(function(e){return e&&!e.paused&&!e.ended&&0!==e.playbackRate&&0!==e.readyState}(e))t._gapService&&t._gapService.do(e,n.maxJumpDistance,t.isLive,3);else{if(!e.currentTime&&t._gapService)return void t._gapService.do(e,n.maxJumpDistance,t.isLive,3);n.isLive&&4===e.readyState&&r>n.disconnectTime&&t.disconnect()}}}})),d(g(t),"_onPlay",(function(){var e,r,n=t._opts.softDecode||(null===(e=t.media)||void 0===e||null===(r=e.buffered)||void 0===r?void 0:r.length);if(t.isLive)!t._loading&&n&&t.replay(void 0,!0);else{var i=t.bufferInfo();(i.start||i.nextStart)>sn&&t._tick()}})),d(g(t),"_onSeeking",o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:!t.isLive&&t.seekable&&(t._preLoadEndPoint=-1,t._checkPreload());case 1:case"end":return e.stop()}}),e)})))),d(g(t),"_onTimeupdate",(function(){if(t.media){var e=t._opts,r=t.media.currentTime;if(e.isLive&&e.maxLatency&&e.targetLatency){var n=C.end(C.get(t.media));n-r>=e.maxLatency&&(t.media.currentTime=n-e.targetLatency)}t._seiService.throw(r,!0),e.isLive||!t.seekable||t._loading||t._checkPreload()}})),d(g(t),"_onWaiting",(function(){t.isLive&&!t._loading&&t._disconnectRetryCount&&(t._disconnectRetryCount--,t.load())})),d(g(t),"_onBufferUpdate",(function(){if(!t._opts.isLive){var e=t.bufferInfo(),r=e.end,n=e.nextEnd;Math.abs((r||n)-t.media.duration)<1&&(t._end(),t.media.readyState<=2&&t._tick())}})),d(g(t),"_checkPreload",o(i().mark((function e(){var r,n,s,a,o,u,c,l,d,h;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.bufferInfo(),n=r.remaining,s=void 0===n?0:n,a=t._opts,o=t._keyframes.filepositions,u=t._keyframes.times,c=t.media.currentTime,!(s<a.preloadTime)){e.next=19;break}if(l=rn(t._keyframes.times,c+s+sn),(d=rn(t._keyframes.times,c+s+t._opts.preloadTime))===l&&(d=l+1),t._preLoadEndPoint!==d){e.next=11;break}return e.abrupt("return");case 11:if(null!=(h=o[l])){e.next=14;break}return e.abrupt("return");case 14:return e.next=16,t._mediaLoader.cancel();case 16:t._loadData(null,[h,o[d]]),t._preLoadEndPoint=d,t._bufferService.unContiguous(u[l]);case 19:case"end":return e.stop()}}),e)})))),d(g(t),"_onFlvScriptData",(function(e){var r,n,i,s,a=null===(r=e.data)||void 0===r||null===(n=r.onMetaData)||void 0===n?void 0:n.keyframes,o=null===(i=e.data)||void 0===i||null===(s=i.onMetaData)||void 0===s?void 0:s.duration;a&&(t._keyframes=a),!t._opts.isLive&&o&&t._bufferService.updateDuration(o)})),t._opts=((r=n({retryCount:3,retryDelay:1e3,disconnectRetryCount:0,loadTimeout:1e4,maxReaderInterval:5e3,preloadTime:5,defaultVodLoadSize:1e7,isLive:!1,softDecode:!1,bufferBehind:10,maxJumpDistance:3,analyzeDuration:2e4,seamlesslyReload:!1,keepStatusAfterSwitch:!0,onlyVideo:!1,onlyAudio:!1,preferMMS:!1},e)).isLive&&r.preloadTime&&(r.maxLatency||(r.maxLatency=2*r.preloadTime),r.targetLatency||(r.targetLatency=r.preloadTime),null!==r.disconnectTime&&void 0!==r.disconnectTime||(r.disconnectTime=r.maxLatency)),r),t.media=t._opts.media||document.createElement("video"),t._opts.media=null,t._firstProgressEmit=!1,t._mediaLoader=new tt(n(n({},t._opts.fetchOptions),{},{retry:t._opts.retryCount,retryDelay:t._opts.retryDelay,timeout:t._opts.loadTimeout,onRetryError:t._onRetryError,onProgress:t._onProgress,responseType:"arraybuffer"})),t._disconnectRetryCount=t._opts.disconnectRetryCount,t._bufferService=new tn(g(t),t._opts.softDecode?t.media:void 0,t._opts),t._seiService=new nt(g(t)),t._bandwidthService=new it,t._stats=new at(g(t)),t._opts.softDecode||(t._gapService=new rt),t.media.addEventListener("play",t._onPlay),t.media.addEventListener("seeking",t._onSeeking),t.media.addEventListener("timeupdate",t._onTimeupdate),t.media.addEventListener("progress",t._onBufferUpdate),t.media.addEventListener("waiting",t._onWaiting),t.on(Re,t._onFlvScriptData),t}return l(y,[{key:"version",get:function(){return"3.0.16"}},{key:"isLive",get:function(){return this._opts.isLive}},{key:"baseDts",get:function(){var e;return null===(e=this._bufferService)||void 0===e?void 0:e.baseDts}},{key:"seekable",get:function(){return!!this._keyframes&&this._acceptRanges}},{key:"loader",get:function(){return this._mediaLoader}},{key:"blobUrl",get:function(){var e;return null===(e=this._bufferService)||void 0===e?void 0:e.blobUrl}},{key:"speedInfo",value:function(){return{speed:this._bandwidthService.getLatestSpeed(),avgSpeed:this._bandwidthService.getAvgSpeed()}}},{key:"getStats",value:function(){return this._stats.getStats()}},{key:"bufferInfo",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:sn;return C.info(C.get(this.media),null===(e=this.media)||void 0===e?void 0:e.currentTime,t)}},{key:"playbackQuality",value:function(){return function(e){if(!e)return{};if("function"==typeof e.getVideoPlaybackQuality){var t=e.getVideoPlaybackQuality();return{droppedVideoFrames:t.droppedVideoFrames||t.corruptedVideoFrames,totalVideoFrames:t.totalVideoFrames,creationTime:t.creationTime}}return{droppedVideoFrames:e.webkitDroppedFrameCount,totalVideoFrames:e.webkitDecodedFrameCount,creationTime:performance.now()}}(this.media)}},{key:"load",value:(p=o(i().mark((function e(t){var r,n=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.length>1&&void 0!==n[1]&&n[1],this._bufferService){e.next=3;break}return e.abrupt("return");case 3:return e.next=5,this._reset(r);case 5:this._loadData(t,this._opts.isLive?[]:[0,this._opts.defaultVodLoadSize]),clearTimeout(this._tickTimer),this._tickTimer=setTimeout(this._tick,this._tickInterval);case 8:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"replay",value:(f=o(i().mark((function e(){var t,r,n=this,s=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]?s[0]:this._opts.seamlesslyReload,r=s.length>1?s[1]:void 0,this.media){e.next=4;break}return e.abrupt("return");case 4:if(this._resetDisconnectCount(),!t){e.next=11;break}return e.next=8,this._clear();case 8:setTimeout((function(){n._loadData(n._opts.url),n._bufferService.seamlessSwitch(),n._seamlessSwitching=!0})),e.next=13;break;case 11:return e.next=13,this.load();case 13:return e.abrupt("return",this.media.play(!r).catch((function(){})));case 14:case"end":return e.stop()}}),e,this)}))),function(){return f.apply(this,arguments)})},{key:"disconnect",value:function(){return nn.debug("disconnect!"),this._clear()}},{key:"switchURL",value:(c=o(i().mark((function e(t,r){var n=this;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._bufferService){e.next=2;break}return e.abrupt("return");case 2:if(this._resetDisconnectCount(),r&&this._opts.isLive){e.next=8;break}return e.next=6,this.load(t);case 6:return this._urlSwitching=!0,e.abrupt("return",this.media.play(!0).catch((function(){})));case 8:return e.next=10,this._clear();case 10:setTimeout((function(){n._loadData(t),n._bufferService.seamlessSwitch(),n._urlSwitching=!0,n._seamlessSwitching=!0}));case 11:case"end":return e.stop()}}),e,this)}))),function(e,t){return c.apply(this,arguments)})},{key:"destroy",value:(a=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.media){e.next=2;break}return e.abrupt("return");case 2:return this.removeAllListeners(),this._seiService.reset(),this.media.removeEventListener("play",this._onPlay),this.media.removeEventListener("seeking",this._onSeeking),this.media.removeEventListener("timeupdate",this._onTimeupdate),this.media.removeEventListener("waiting",this._onWaiting),this.media.removeEventListener("progress",this._onBufferUpdate),e.next=11,Promise.all([this._clear(),this._bufferService.destroy()]);case 11:this.media=null,this._bufferService=null;case 13:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)})},{key:"_emitError",value:function(e){var t,r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];nn.table(e),nn.error(e),nn.error(null===(t=this.media)||void 0===t?void 0:t.error),this._urlSwitching&&(this._urlSwitching=!1,this._seamlessSwitching=!1,this.emit(Ue,e)),this.emit(ge,e),r&&(this._seiService.reset(),this._end())}},{key:"_reset",value:(s=o(i().mark((function e(){var t,r=arguments;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=r.length>0&&void 0!==r[0]&&r[0],this._seiService.reset(),this._bandwidthService.reset(),this._stats.reset(),e.next=6,this._clear();case 6:return e.next=8,this._bufferService.reset(t);case 8:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"_loadData",value:(r=o(i().mark((function e(t,r){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t&&(this._opts.url=t),n=t=this._opts.url,t){e.next=4;break}throw new Error("Source url is missing");case 4:if(this._opts.preProcessUrl&&(n=this._opts.preProcessUrl(t).url),this._mediaLoader.finnalUrl=n,this.emit(ke,{url:n}),nn.debug("load data, loading:",this._loading,n),!this._loading){e.next=11;break}return e.next=11,this._mediaLoader.cancel();case 11:return this._loading=!0,e.prev=12,e.next=15,this._mediaLoader.load({url:n,range:r});case 15:e.next=21;break;case 17:return e.prev=17,e.t0=e.catch(12),this._loading=!1,e.abrupt("return",this._emitError(K.network(e.t0)));case 21:case"end":return e.stop()}}),e,this,[[12,17]])}))),function(e,t){return r.apply(this,arguments)})},{key:"_clear",value:(t=o(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mediaLoader){e.next=3;break}return e.next=3,this._mediaLoader.cancel();case 3:clearTimeout(this._maxChunkWaitTimer),clearTimeout(this._tickTimer),this._loading=!1,this._firstProgressEmit=!1;case 7:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})}],[{key:"isSupported",value:function(e){return e&&"video"!==e&&"audio"!==e?"undefined"!=typeof WebAssembly:se.isSupported()}},{key:"enableLogger",value:function(){X.enable(),Lt.enable()}},{key:"disableLogger",value:function(){X.disable(),Lt.disable()}}]),y}(We);try{localStorage.getItem("xgd")?an.enableLogger():an.disableLogger()}catch(cn){}var on=function(){function e(t,r){var i=this;u(this,e),d(this,"_onLowDecode",(function(){var e,t,r,s,a=i._opts,o=a.media,u=a.innerDegrade,c=a.backupURL;null===(e=i._plugin)||void 0===e||null===(t=e.player)||void 0===t||t.emit("lowdecode",o.degradeInfo),null===(r=i._plugin)||void 0===r||null===(s=r.player)||void 0===s||s.emit("core_event",n(n({},o.degradeInfo),{},{eventName:Be})),1!==u&&3!==u||!c||i._degrade(c)})),d(this,"_degrade",(function(e){var t=i._plugin.player,r=t.video;if("MVideo"===(null==r?void 0:r.TAG)){var n=t.video.degradeVideo;t.video=n,r.degrade(e),e&&(t.config.url=e);var s=t.root.firstChild;"MVideo"===s.TAG&&t.root.replaceChild(n,s);var a=i._plugin.constructor.pluginName.toLowerCase();t.unRegisterPlugin(a),t.once("canplay",(function(){t.play()}))}})),d(this,"forceDegradeToVideo",(function(e){var t=i._opts.innerDegrade;1!==t&&3!==t||i._degrade(e)})),this._opts=t,this._plugin=r,this._init()}return l(e,[{key:"_init",value:function(){var e=this._opts,t=e.media,r=e.preloadTime,n=e.innerDegrade,i=e.decodeMode;t&&(n&&t.setAttribute("innerdegrade",n),r&&t.setAttribute("preloadtime",r),t.setDecodeMode&&t.setDecodeMode(i),this._bindEvents())}},{key:"_bindEvents",value:function(){this._opts.media.addEventListener("lowdecode",this._onLowDecode)}},{key:"destroy",value:function(){var e,t;null===(e=this._opts)||void 0===e||null===(t=e.media)||void 0===t||t.removeEventListener("lowdecode",this._onLowDecode),this._plugin=null}}]),e}(),un=function(e){h(i,e);var r=k(i);function i(){var e;u(this,i);for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];return d(g(e=r.call.apply(r,[this].concat(n))),"flv",null),d(g(e),"pluginExtension",null),d(g(e),"getStats",(function(){var t;return null===(t=e.flv)||void 0===t?void 0:t.getStats()})),d(g(e),"destroy",(function(){var t;e.flv&&(e.flv.destroy(),e.flv=null),null===(t=e.pluginExtension)||void 0===t||t.destroy(),e.pluginExtension=null})),d(g(e),"_onSwitchURL",(function(t,r){var n,i;e.flv&&(e.player.config.url=t,"object"===s(r)&&(r=r.seamless),e.flv.switchURL(t,r),!r&&null!==(n=e.player.config)&&void 0!==n&&null!==(i=n.flv)&&void 0!==i&&i.keepStatusAfterSwitch&&e._keepPauseStatus())})),d(g(e),"_keepPauseStatus",(function(){e.player.paused&&e.player.once("canplay",(function(){e.player.pause()}))})),d(g(e),"_onDefinitionChange",(function(t){var r=t.to;e.flv&&e.flv.switchURL(r)})),e}return l(i,[{key:"core",get:function(){return this.flv}},{key:"version",get:function(){var e;return null===(e=this.flv)||void 0===e?void 0:e.version}},{key:"softDecode",get:function(){var e,t,r=null===(e=this.player)||void 0===e||null===(t=e.config)||void 0===t?void 0:t.mediaType;return!!r&&"video"!==r&&"audio"!==r}},{key:"loader",get:function(){var e;return null===(e=this.flv)||void 0===e?void 0:e.loader}},{key:"beforePlayerInit",value:function(){var e=this,r=this.player.config;if(r.url){this.flv&&this.flv.destroy(),this.player.switchURL=this._onSwitchURL;var i,s=r.flv||{};if(null!==s.disconnectTime&&void 0!==s.disconnectTime||(s.disconnectTime=0),this.flv=new an(n({softDecode:this.softDecode,isLive:r.isLive,media:this.player.video,preProcessUrl:function(t,r){var n,i;return(null===(n=(i=e.player).preProcessUrl)||void 0===n?void 0:n.call(i,t,r))||{url:t,ext:r}}},s)),this.softDecode||t.BasePlugin.defineGetterOrSetter(this.player,{url:{get:function(){var t;return null===(t=e.flv)||void 0===t?void 0:t.blobUrl},configurable:!0}}),this.softDecode&&(this.pluginExtension=new on(n({media:this.player.video},r.flv),this),this.player.forceDegradeToVideo=function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return null===(t=e.pluginExtension)||void 0===t?void 0:t.forceDegradeToVideo.apply(t,n)}),r.isLive)null===(i=this.player)||void 0===i||i.useHooks("replay",(function(){var t;return null===(t=e.flv)||void 0===t?void 0:t.replay()}));this.on(t.Events.URL_CHANGE,this._onSwitchURL),this.on(t.Events.DESTROY,this.destroy),this._transError(),this._transCoreEvent(be),this._transCoreEvent(ke),this._transCoreEvent(xe),this._transCoreEvent(we),this._transCoreEvent(Se),this._transCoreEvent(Ee),this._transCoreEvent(je),this._transCoreEvent(Te),this._transCoreEvent(Ae),this._transCoreEvent(De),this._transCoreEvent(Le),this._transCoreEvent(Oe),this._transCoreEvent(Ce),this._transCoreEvent(Re),this._transCoreEvent(Ie),this._transCoreEvent(Pe),this._transCoreEvent(Ue),this.flv.load(r.url,!0)}}},{key:"_transError",value:function(){var e=this;this.flv.on(ge,(function(r){e.player&&e.player.emit(t.Events.ERROR,new t.Errors(e.player,r))}))}},{key:"_transCoreEvent",value:function(e){var t=this;this.flv.on(e,(function(r){t.player&&t.player.emit("core_event",n(n({},r),{},{eventName:e}))}))}}],[{key:"pluginName",get:function(){return"flv"}},{key:"isSupported",value:function(e,t){return an.isSupported(e,t)}},{key:"isSupportedMMS",value:function(){return"undefined"!=typeof ManagedMediaSource}}]),i}(t.BasePlugin);return d(un,"Flv",an),un}));
//# sourceMappingURL=index.min.js.map
