<!-- LabelStyleAttribute -->
<script lang="ts" setup>
import type { LabelStyleSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'LabelStyleAttribute' });

const props = defineProps<{
  modelValue?: LabelStyleSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: LabelStyleSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const options = [
  {
    label: '填充',
    value: 'FILL',
  },
  {
    label: '描边',
    value: 'OUTLINE',
  },
  {
    label: '填充和描边',
    value: 'FILL_AND_OUTLINE',
  },
];
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="model" :options="options" :clearable="false" />
  </el-form-item>
</template>
