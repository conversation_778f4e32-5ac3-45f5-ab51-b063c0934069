import type { AnyFn } from '../types';

export function isDef<T = any>(val?: T): val is T {
  return typeof val !== 'undefined';
}

const toString = Object.prototype.toString;

export function isBoolean(val: any): val is boolean {
  return typeof val === 'boolean';
}

export function isFunction<T extends AnyFn>(val: any): val is T {
  return typeof val === 'function';
}

export function isNumber(val: any): val is number {
  return typeof val === 'number';
}

export function isString(val: unknown): val is string {
  return typeof val === 'string';
}

export function isObject(val: any): val is object {
  return toString.call(val) === '[object Object]';
}

export function isWindow(val: any): val is Window {
  return typeof window !== 'undefined' && toString.call(val) === '[object Window]';
}

export function clamp(n: number, min: number, max: number) {
  return Math.min(max, Math.max(min, n));
}

export function noop() {}

export function rand(min: number, max: number) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export function hasOwn<T extends object, K extends keyof T>(val: T, key: K): key is K {
  return Object.prototype.hasOwnProperty.call(val, key);
}

export function isPromise<T extends Promise<any>>(val: any): val is T {
  return (
    !!val
    && (typeof val === 'object' || typeof val === 'function')
    && typeof (val as any).then == 'function'
  );
}

export function isBase64(val: string): boolean {
  const reg
    = /^\s*data:([a-z]+\/[\d+.a-z-]+(;[a-z-]+=[\da-z-]+)?)?(;base64)?,([\s\w!$%&'()*+,./:;=?@~\-]*?)\s*$/i;
  return reg.test(val);
}

/**
 * Object.keys的别名，旨在于返回数组类型与对象键名一致
 * @param o
 */
export function getKeys<T extends object>(o: T): (keyof T)[] {
  return Object.keys(o) as unknown as (keyof T)[];
}

/**
 * 对比两个数组，以左边为基准 判断右边的缺失值和新增值、不变值
 * @param left
 * @param right
 */
export function arrayDiff<T = any>(left: T[], right: T[]) {
  const missed: T[] = [];
  const added: T[] = [];

  if (left !== right) {
    left.forEach((item) => {
      const exist = right.find(e => e === item);
      !exist && missed.push(item);
    });
    right.forEach((item) => {
      const exist = left.find(e => e === item);
      !exist && added.push(item);
    });
  }
  const existed = left.filter(e => !missed.includes(e));
  return {
    missed,
    added,
    existed,
  };
}
