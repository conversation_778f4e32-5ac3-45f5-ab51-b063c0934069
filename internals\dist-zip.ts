import fs from 'node:fs';
import path from 'node:path';
import process from 'node:process';

import archiver from 'archiver';
import { normalizePath } from 'vite';

const cwd = normalizePath(process.cwd());

// 确保 dist 目录存在
const distDir = path.resolve(cwd, './dist');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

const output = fs.createWriteStream(path.resolve(cwd, './dist.zip'));
const archive = archiver('zip');

archive.on('error', (err) => {
  throw err;
});

archive.pipe(output);
archive.directory(distDir, 'dist');
archive.finalize();
