<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <meta name=viewport
        content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,minimal-ui">
    <meta name="referrer" content="no-referrer">
    <link rel="stylesheet" href="./style/index.min.css" />
    <title>Document</title>
    <script src="./xgplayer/index.min.js" charset="utf-8"></script>
    <script src="./xgplayer-hls.js/index.min.js" charset="utf-8"></script>
    <style>
        #dplayer {
            overflow: hidden;
        }

        html,
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        #mse {
            width: inherit !important;
            height: inherit !important;
        }

        #mse.full-screen video {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100vw !important;
            height: calc(100vw * 0.5625) !important;
        }
    </style>
    <script>
        function initVideo() {
            new window.Player({
                id: 'mse',
                url: location.search.replace("?url=", "").replace(/\&scaleType\=.*/, ""),
                playsinline: true,
                height: window.innerHeight,
                width: window.innerWidth,
                isLive: true,
                autoplay: true,
                autoplayMuted: true,
                controls: true,
                closeVideoDblclick: true,
                closeVideoClick: true,
                enableWorker: true,
                enableStashBuffer: true, // 启用缓存
                stashInitialSize: 2048, // 缓存大小2m
                lazyLoad: false,
                hasAudio: false,
                lazyLoadMaxDuration: 2 * 60,
                autoCleanupSourceBuffer: true, // 自动清除缓存
                autoCleanupMaxBackwardDuration: 65,
                autoCleanupMinBackwardDuration: 60,
                plugins: [window.Mp4Plugin]
            });
            location.href.match('scaleType=1') && document.querySelector("#mse").classList.toggle('full-screen')
        }
    </script>
</head>

<body onload="initVideo()">
    <div id="mse"></div>
</body>

</html>
