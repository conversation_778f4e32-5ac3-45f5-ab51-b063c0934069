import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';

import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';

import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PathGraphicsSerializateJSON {
  show?: boolean;
  leadTime?: number;
  trailTime?: number;
  width?: number;
  resolution?: number;
  material?: MaterialPropertySerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
}

export type PathGraphicsKey = keyof PathGraphicsSerializateJSON;

export class PathGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.PathGraphics,
    omit?: PathGraphicsKey[],
    time?: Cesium.JulianDate,
  ): PathGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      leadTime: getValue('leadTime'),
      trailTime: getValue('trailTime'),
      width: getValue('width'),
      resolution: getValue('resolution'),
      material: MaterialPropertySerializate.toJSON(data.material),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
    };
  }

  static fromJSON(
    json?: PathGraphicsSerializateJSON,
    omit?: PathGraphicsKey[],
  ): Cesium.PathGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.PathGraphics({
      show: getValue('show') ?? true,
      leadTime: getValue('leadTime'),
      trailTime: getValue('trailTime'),
      width: getValue('width'),
      resolution: getValue('resolution'),
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
    });
  }
}
