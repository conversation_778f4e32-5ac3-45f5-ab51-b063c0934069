import type { FeatureCollection } from 'geojson';
import type Feature from 'ol/Feature';
import type Filter from 'ol/format/filter/Filter';
import type { WriteTransactionOptions } from 'ol/format/WFS';
import { XMLParser } from 'fast-xml-parser';
import GML32 from 'ol/format/GML32';
import WFS from 'ol/format/WFS';

const xmlParser = new XMLParser({
  ignoreAttributes: false,
  attributeNamePrefix: '@_',
  allowBooleanAttributes: true,
  parseAttributeValue: true,
});

export interface WfsServiceConstructorOptions {
  url: string;
  /**
   * 图层名
   */
  typeName: string;

  featurePrefix?: string;

  featureNS?: string;
}

export interface TransactionHnadler {
  inserts?: Feature[];
  updates?: Feature[];
  deletes?: Feature[];
  options: Omit<WriteTransactionOptions, 'featurePrefix' | 'featureType' | 'featureNS'>;
}

export class WfsService {
  constructor(options: WfsServiceConstructorOptions) {
    this.url = options.url;
    this.typeName = options.typeName;
    this.featurePrefix = options.featurePrefix;
    this.featureNS = options.featureNS;
  }

  url: string;
  /**
   * 图层名
   */
  typeName: string;

  featurePrefix?: string;
  featureNS?: string;
  /**
   * @internal
   */
  private _capabilities?: any;

  /**
   * 检索有关服务的元数据，包括支持的操作和参数，以及可用功能类型的列表。
   */
  async getCapabilities() {
    if (!this._capabilities) {
      const { data } = await axios.request({
        url: this.url,
        method: 'get',
        params: {
          service: 'WFS',
          version: '2.0.0',
          request: 'GetCapabilities',
          typeName: this.typeName,
        },
      });
      const json = xmlParser.parse(data);
      this._capabilities = json;
    }
    return this._capabilities;
  }

  /**
   * @internal
   */
  private _describeFeatureType?: any;

  /**
   * 返回WFS实例提供或接受的功能类型和功能属性的结构描述。
   */
  async describeFeatureType() {
    if (!this._describeFeatureType) {
      const { data } = await axios.request({
        url: this.url,
        method: 'get',
        params: {
          service: 'WFS',
          version: '2.0.0',
          request: 'DescribeFeatureType',
          typeName: this.typeName,
        },
      });

      const json = xmlParser.parse(data);
      this._describeFeatureType = json;
    }
    return this._describeFeatureType;
  }

  /**
   * 获取属性键列表
   *
   * @returns 返回属性键列表的 Promise 对象，每个属性键包含以下属性：
   * - key: 属性键名称
   * - nillable: 是否可以为空
   * - type: 属性键类型
   */
  async getPropertyKeys(): Promise<Record<string, any>[]> {
    const desc = await this.describeFeatureType();
    const element = desc?.['xsd:schema']?.['xsd:complexType']?.['xsd:complexContent']?.['xsd:extension']?.['xsd:sequence']?.['xsd:element'];
    if (!Array.isArray(element)) {
      return [];
    }
    return element?.map((item: any) => {
      return {
        name: item['@_name'],
        nillable: item['@_nillable'],
        type: item['@_type'],
      };
    });
  }

  async getGeomKey(): Promise<Record<string, any> | undefined> {
    const keys = await this.getPropertyKeys();

    return keys.find(e => e.type.startsWith('gml:'));
  }

  /**
   * 返回WFS实例提供或接受的功能类型和功能属性的结构描述。
   */
  async getFeature(filter: Filter): Promise<FeatureCollection | undefined> {
    const wfs = new WFS({
      gmlFormat: new GML32(),
    });
    const { data } = await axios.request({
      url: this.url,
      method: 'post',
      headers: {
        'Content-Type': 'text/xml',
      },
      data: new XMLSerializer().serializeToString(
        wfs.writeGetFeature({
          featurePrefix: this.featurePrefix ?? '',
          featureNS: this.featureNS ?? '',
          featureTypes: [this.typeName],
          outputFormat: 'json',
          filter,

        }),
      ),
    });
    return data;
  }

  /**
   * 执行事务操作
   */
  async transaction(options: TransactionHnadler) {
    const wfs = new WFS({
      featureType: this.typeName,
      gmlFormat: new GML32({
        multiSurface: true,
      }),
    });

    const { data } = await axios.request({
      url: this.url,
      method: 'post',
      headers: {
        'Content-Type': 'text/xml',
      },
      data: new XMLSerializer().serializeToString(
        wfs.writeTransaction(
          options.inserts ?? [],
          options.updates ?? [],
          options.deletes ?? [],
          {
            ...options.options,
            featureType: this.typeName,
            featureNS: this.featureNS ?? '',
            featurePrefix: this.featurePrefix ?? '',
          },
        ),
      ),
    });
    return data;
  }

  /**
   * 清除缓存
   *
   */
  clearCache() {
    this._capabilities = undefined;
    this._describeFeatureType = undefined;
  }

  /**
   * 获取要素总数
   */
  async getFeatureCount(): Promise<number> {
    try {
      const { data } = await axios.request({
        url: this.url,
        method: 'get',
        params: {
          service: 'WFS',
          version: '1.1.0',
          request: 'GetFeature',
          typeName: this.typeName,
          resultType: 'hits',
        },
      });

      // 解析返回的XML以获取总数
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(data, 'text/xml');
      const featureCollection = xmlDoc.getElementsByTagName('wfs:FeatureCollection')[0];
      return Number.parseInt(featureCollection?.getAttribute('numberOfFeatures') || '0', 10);
    }
    catch (error) {
      console.error('获取要素总数失败:', error);
      return 0;
    }
  }

  /**
   * 获取分页数据（包含总数和当前页数据）
   */
  async getPaginatedFeatures(options: {
    page?: number;
    pageSize?: number;
    usePost?: boolean;
  } = {}): Promise<{
      features: FeatureCollection | undefined;
      total: number;
      page: number;
      pageSize: number;
      totalPages: number;
    }> {
    const {
      page = 1,
      pageSize = 10,
      usePost = true,
    } = options;

    const startIndex = (page - 1) * pageSize;

    // 获取总数
    const total = await this.getFeatureCount();

    // 获取当前页数据
    const features = usePost
      ? await this._getAllFeaturesPost({ maxFeatures: pageSize, startIndex })
      : await this._getAllFeaturesGet({ maxFeatures: pageSize, startIndex });

    const totalPages = Math.ceil(total / pageSize);

    return {
      features,
      total,
      page,
      pageSize,
      totalPages,
    };
  }

  /**
   * 内部方法：POST方式获取要素
   */
  private async _getAllFeaturesPost(options: {
    maxFeatures?: number;
    startIndex?: number;
    outputFormat?: string;
  } = {}): Promise<FeatureCollection | undefined> {
    const {
      maxFeatures = 10,
      startIndex = 0,
      outputFormat = 'application/json',
    } = options;

    const wfs = new WFS({
      gmlFormat: new GML32(),
    });

    const { data } = await axios.request({
      url: this.url,
      method: 'post',
      headers: {
        'Content-Type': 'text/xml',
      },
      data: new XMLSerializer().serializeToString(
        wfs.writeGetFeature({
          featurePrefix: this.featurePrefix ?? '',
          featureNS: this.featureNS ?? '',
          featureTypes: [this.typeName],
          outputFormat,
          maxFeatures,
          startIndex,
        }),
      ),
    });
    return data;
  }

  /**
   * 内部方法：GET方式获取要素
   */
  private async _getAllFeaturesGet(options: {
    maxFeatures?: number;
    startIndex?: number;
    outputFormat?: string;
  } = {}): Promise<FeatureCollection | undefined> {
    const {
      maxFeatures = 10,
      startIndex = 0,
      outputFormat = 'application/json',
    } = options;

    try {
      const { data } = await axios.request({
        url: this.url,
        method: 'get',
        params: {
          service: 'WFS',
          version: '1.1.0',
          request: 'GetFeature',
          typeName: this.typeName,
          outputFormat,
          maxFeatures,
          startIndex,
        },
      });
      return data;
    }
    catch (error) {
      console.error('GET方式查询要素失败:', error);
      return undefined;
    }
  }
}
