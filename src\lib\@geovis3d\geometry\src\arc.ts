import type { Coord, Feature, LineString } from '@turf/turf';

import { lineString } from '@turf/turf';
import {
  getArcPoints,
  getAzimuth,
  getBatchCoords,
  getCircleCenterOfThreePoints,
  isClockWise,
  mathDistance,
} from './common';

/**
 * 标绘画弓形算法，继承线要素相关方法和属性
 */
export function arc(points: Coord[]): Feature<LineString> {
  if (points.length < 3) {
    throw new Error('points.length must >=3');
  }

  const coord1 = points[0];
  const coord2 = points[1];
  const coord3 = points[2];
  let startAngle: number;
  let endAngle: number;

  const center = getCircleCenterOfThreePoints(coord1, coord2, coord3);
  const radius = mathDistance(coord1, center);
  const angle1 = getAzimuth(coord1, center);
  const angle2 = getAzimuth(coord2, center);
  if (isClockWise(coord1, coord2, coord3)) {
    startAngle = angle2;
    endAngle = angle1;
  }
  else {
    startAngle = angle1;
    endAngle = angle2;
  }
  const coords = getBatchCoords(getArcPoints(center, radius, startAngle, endAngle));
  return lineString(coords);
}
