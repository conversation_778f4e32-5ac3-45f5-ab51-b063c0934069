<!-- 海南省危化品风险预警信息 -->
<script lang="ts" setup>
import { productionHazardousChemicalsStatisticByKeyUsingPost } from '@/genapi/production';
import { COMPANY_RISK_RANK_ENUM } from '../enum';

defineOptions({ name: 'RightRiskStat' });

const { state, isLoading } = useAsyncState(async () => {
  const { data } = await productionHazardousChemicalsStatisticByKeyUsingPost({
    data: {
      key: '5',
    },
  });

  return data?.reduce((a, b) => {
    a[b.code!] = b.value;
    return a;
  }, {}) as Record<string, string>;
}, undefined);
const colors = ['#fe1311', '#ff9802', '#ffff1e', '#13c9f4', '#c1c1c1'];
</script>

<template>
  <HeaderTitle2> 海南省危化品企业风险预警信息 </HeaderTitle2>
  <div v-loading="isLoading" class="right-risk-stat">
    <div v-for="(item, key, index) in COMPANY_RISK_RANK_ENUM" :key="key" class="item">
      <div
        class="round"
        :style="{
          'border-color': colors[index],
        }"
      >
        <div>{{ state?.[key] || 0 }}</div>
      </div>
      <span class="name">{{ item }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
  .right-risk-stat {
  display: flex;

  .item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 20px;

    .round {
      display: flex;
      align-items: stretch;
      justify-content: center;
      width: 56px;
      height: 56px;
      border-style: solid;
      border-width: 6px;
      border-radius: 50%;

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        margin: 4px;
        border: 1px #fff solid;
        border-radius: 50%;
      }
    }

    .name {
      padding-top: 10px;
    }
  }
}
</style>
