<!-- 导航 -->
<script lang="ts" setup>
import type { PathModel, POIModel, POIOptions } from './common';
import { CzEntity, gcj02ToCartesian } from '@x3d/all';
import { useCzEntities, useCzEntity, useCzViewer } from '@x3d/vue-hooks';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';

import relativeTime from 'dayjs/plugin/relativeTime';
import endPNG from './assets/终点.png';
import startPNG from './assets/起点.png';
import passPNG from './assets/途径点.png';

import { pathDirection, poiSearch } from './common';

defineOptions({ name: 'NavigationTool' });

const emits = defineEmits<{
  (event: 'change'): void;
}>();

dayjs.extend(duration);
dayjs.extend(relativeTime);

const viewer = useCzViewer();

// 驾车or骑行
const isDrive = ref(true);

// 点位数组  第一项为起点  最后一项为终点，骑行没有途径点
const points = shallowReactive([null, null] as (POIModel | null)[]);

const loadings = shallowReactive([false, false] as boolean[]);

// 点位列表数组
const optionsList = shallowReactive([[], []] as POIOptions[][]);

// 骑行需要清空途径点 骑行没有途径点
watch(isDrive, (isDrive) => {
  if (!isDrive && points.length > 2) {
    points.splice(1, points.length - 2);
    loadings.splice(1, loadings.length - 2);
    optionsList.splice(1, optionsList.length - 2);
  }
});

// poi查询
async function remoteMethod(index: number, keywords: string) {
  loadings[index] = true;
  try {
    const res = await poiSearch(keywords);
    optionsList[index] = res?.options ?? [];
  }
  catch (error) {
    console.error(error);
  }
  loadings[index] = false;
}

// 添加途经点
function addPointEffect() {
  points.splice(-1, 0, null);
  loadings.splice(-1, 0, false);
  optionsList.splice(-1, 0, []);
}

// 移除途经点
function removePointEffect(index: number) {
  points.splice(index, 1);
  loadings.splice(index, 1);
  optionsList.splice(index, 1);
}

// 起点终点翻转
function reverse() {
  points.reverse();
  loadings.reverse();
  optionsList.reverse();
}

const pointsFilter = computed(() => {
  return points.filter(e => e?.latitude && e.longitude);
});

const { state: data, isLoading, execute } = useAsyncState(
  async (type: 'driving' | 'bicycling') => {
    if (pointsFilter.value.length >= 2) {
      const data = await pathDirection(pointsFilter.value as POIModel[], type);
      return data;
    }
  },
  undefined,
  {
    immediate: false,
  },
);

watch(
  pointsFilter,
  () => {
    current.value = undefined;
    execute(0, isDrive.value ? 'driving' : 'bicycling');
  },
  {
    flush: 'post',
  },
);

const current = shallowRef<PathModel>();

// 起点终点上球
useCzEntities(() => {
  if (current.value) {
    const { start, end, pass } = current.value;
    const pois = [start, ...(pass ?? []), end];
    return pois.map((item, index) => {
      const image = index === 0 ? startPNG : index === pois.length - 1 ? endPNG : passPNG;
      return new CzEntity({
        position: Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude),
        billboard: {
          image,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          scale: 0.5,
        },
      });
    });
  }
});

// 导航路径上球
const { entity: navEntity } = useCzEntity(() => {
  if (current.value) {
    const positions = current.value.steps.flatMap(step =>
      step.polyline.split(';').map((e) => {
        const lngLat = e.split(',').map(e => +e);
        return gcj02ToCartesian([lngLat[0], lngLat[1], 0]);
      }),
    );
    const entity = new CzEntity({
      polyline: {
        positions,
        width: 10,
        clampToGround: true,
        material: Cesium.Color.ORANGE,
      },
    });
    return entity;
  }
});

watch(navEntity, (navEntity) => {
  if (navEntity) {
    viewer.value.flyTo(navEntity, {
      offset: new Cesium.HeadingPitchRange(0, -Cesium.Math.PI / 2, 0),
    });
  }
});
</script>

<template>
  <div class="navigation-sty">
    <header-title1 style="display: flex; justify-content: space-between;width: 100%;">
      导航
      <el-button h="100%!" b="0px!" rd="0!" un-text="22px! #fff!" @click="emits('change')">
        <el-icon class="i-material-symbols:close-rounded" />
      </el-button>
    </header-title1>
    <el-divider />
    <div class="navigation-search h-46px w-100%" flex="~ items-center" b="0px solid #2F5476" style="padding: 0 8px">
      <div flex="~ 1 justify-around" font="bold" style="background-color: #000;">
        <div
          flex="~ items-center"
          cursor="pointer"
          :bg="!isDrive ? '#000' : '#292b2e'"
          style="justify-content: center;width: 50%;margin: 6px;border-radius: 3px;"
          @click="isDrive = true"
        >
          <el-icon class="i-custom:driving" mr="6px" />
          驾车
        </div>
        <div
          flex="~ items-center"
          cursor="pointer"
          :bg="isDrive ? '#000' : '#292b2e'"
          style="flex: 1;justify-content: center;margin: 6px;border-radius: 3px;"
          @click="isDrive = false"
        >
          <el-icon class="i-custom:bicycling" mr="6px" />
          骑行
        </div>
      </div>
    </div>

    <div flex="~ items-center" b="0px solid #2F5476">
      <el-button text w="68px!" h="68px!" un-text="24px!" @click="reverse">
        <el-icon class="i-custom:reverse" />
      </el-button>
      <div class="address-box-right" flex="1">
        <div v-for="(_item, index) in points" :key="index" flex="~ items-center">
          <div class="h-17px w-17px rd-9px" b="4px #fff" :style="{ background: index !== points.length - 1 ? `#46E2B0` : '#E25E46' }" />
          <el-select
            v-model="points[index]"
            :b-b="index !== points.length - 1 ? '1px solid #2F5476' : ''"
            :placeholder="index === 0
              ? '请输入起点或经纬'
              : index === points.length - 1
                ? '请输入终点或经纬'
                : '请输入途经点或经纬'
            "
            popper-class="w-360px!"
            :remote-method="(v) => remoteMethod(index, v)"
            value-key="id"
            class="flex-1"
            clearable
            filterable
            remote
            :loading="loadings[index]"
          >
            <el-option v-for="item in optionsList[index]" :key="item.value.id" :label="item.label" :value="item.value">
              <div class="flex" style="display: flex;gap:8px;align-items: center">
                <el-icon class="i-custom:poi" />
                <div flex="~ col">
                  <el-text flex="self-start!">
                    {{ item.value.name }}
                  </el-text>
                  <!-- <el-text flex="self-start!">
                    {{ item.value.address }}
                  </el-text> -->
                </div>
              </div>
            </el-option>
          </el-select>

          <div w="50px">
            <el-button v-if="index === 0 && isDrive" w="50px!" text @click="addPointEffect">
              <el-icon class="i-material-symbols:add-circle-outline-rounded" :size="18" />
            </el-button>
            <el-button
              v-if="index !== 0 && index !== points.length - 1"
              w="50px!"
              text
              @click="removePointEffect(index)"
            >
              <el-icon class="i-material-symbols:do-not-disturb-on-outline-rounded" :size="18" />
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-scrollbar
      v-if="points.filter((e) => !e).length === 0 || data?.length"
      v-loading="isLoading"
      max-h="456px!"
      b="0px solid #2F5476"
    >
      <el-collapse class="p-10px" accordion>
        <el-collapse-item
          v-for="(item, index) in data"
          :key="index"
          :name="index"
          class="list-item"
          @click="current = item"
        >
          <template #title>
            <div class="pl-10px text-18px" style="color: #96daef;">
              路线{{ index + 1 }} |
              {{
                `${dayjs
                  .duration(Number(item.duration || 0), "seconds")
                  .asMinutes()
                  .toFixed(0)}分钟`
              }}
              | {{ `${(Number(item.distance) / 1000).toFixed(0)}公里` }}

              | {{ item.traffic_lights || 0 }}个红绿灯
            </div>
          </template>
          <div v-for="(step, index2) in item.steps" :key="index2" m="y-5px" p="10px">
            <div class="flex items-center">
              <span class="pr-20px text-18px" font="bold">{{
                step.road || "当前道路"
              }}</span>
            </div>
            <span text="16px">{{ step.instruction }}</span>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.navigation-sty {
  background: var(--el-bg-color);
  border-radius: 6px;
}

.el-select {
  flex: 1;

  --el-select-input-color: #b8e5ff;
  --el-text-color-placeholder: #b8e5ff;

  :deep().el-select__wrapper {
    min-height: 53px;
    padding: 0 16px;
    font-size: var(--el-font-size-base);
    font-weight: bold;
    box-shadow: none;
  }
}

.header-title1 {
  padding: 8px;
}

::v-deep(.header-title1__content) {
  display: flex;
  justify-content: space-between;
}

::v-deep(.el-divider--horizontal) {
  margin: 0;
}

::v-deep(.el-select__placeholder.is-transparent) {
  font-size: 15px;
  color: #808283;
}

::v-deep(.el-select__selection) {
  height: 32px !important;
  background-color: #0007;
}
</style>
