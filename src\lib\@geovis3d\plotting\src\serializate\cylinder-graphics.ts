import type { ColorSerializateJSON } from './color';

import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type { HeightReferenceSerializateJSON, ShadowModeSerializateJSON } from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { ColorSerializate } from './color';

import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface CylinderGraphicsSerializateJSON {
  show?: boolean;
  length?: number;
  topRadius?: number;
  bottomRadius?: number;
  heightReference?: HeightReferenceSerializateJSON;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  numberOfVerticalLines?: number;
  slices?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
}

export type CylinderGraphicsKey = keyof CylinderGraphicsSerializateJSON;

export class CylinderGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.CylinderGraphics,
    omit?: CylinderGraphicsKey[],
    time?: Cesium.JulianDate,
  ): CylinderGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      length: getValue('length'),
      topRadius: getValue('topRadius') ?? 0,
      bottomRadius: getValue('bottomRadius') ?? 0,
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference'))
        ?? 'RELATIVE_TO_GROUND',
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      numberOfVerticalLines: getValue('numberOfVerticalLines'),
      slices: getValue('slices'),
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
    };
  }

  static fromJSON(
    json?: CylinderGraphicsSerializateJSON,
    omit?: CylinderGraphicsKey[],
  ): Cesium.CylinderGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.CylinderGraphics({
      show: getValue('show') ?? true,
      length: getValue('length'),
      topRadius: getValue('topRadius') ?? 0,
      bottomRadius: getValue('bottomRadius') ?? 0,
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      numberOfVerticalLines: getValue('numberOfVerticalLines'),
      slices: getValue('slices'),
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
    });
  }
}
