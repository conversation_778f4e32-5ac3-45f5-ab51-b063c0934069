<!-- 全景分析 -->
<script lang="ts" setup>
import { toStaticFilePath } from '@/utils/resolve-path';
import PanoramaDialog from './panorama-dialog.vue';

defineOptions({ name: 'PanoramaAnalysis' });

const panoramaDialogRef = templateRef('panoramaDialogRef');

const list = [
  {
    name: '场景1',
    path: toStaticFilePath('/panorama/tour/tour.xml'),
  },
];
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="全景分析"
    class="h-188px w-400px"
  >
    <el-scrollbar h="600px!" wrap-class="p-10px">
      <div v-for="item in list" :key="item.name" flex="~ justify-between" p="10px">
        <span> {{ item.name }} </span>
        <el-button type="primary" @click="panoramaDialogRef?.open(item.path)">
          查看
        </el-button>
      </div>
    </el-scrollbar>
    <PanoramaDialog ref="panoramaDialogRef" />
  </drag-card>
</template>
