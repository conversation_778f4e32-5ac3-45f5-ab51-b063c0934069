export type ShadowModeSerializateJSON = 'DISABLED' | 'ENABLED' | 'CAST_ONLY' | 'RECEIVE_ONLY';

export type ArcTypeSerializateJSON = 'NONE' | 'GEODESIC' | 'RHUMB';

export type HeightReferenceSerializateJSON = 'NONE' | 'CLAMP_TO_GROUND' | 'RELATIVE_TO_GROUND';

export type HorizontalOriginSerializateJSON = 'CENTER' | 'LEFT' | 'RIGHT';

export type VerticalOriginSerializateJSON = 'CENTER' | 'BOTTOM' | 'BASELINE' | 'TOP';

export type ClassificationTypeSerializateJSON = 'TERRAIN' | 'CESIUM_3D_TILE' | 'BOTH';

export type LabelStyleSerializateJSON = 'FILL' | 'OUTLINE' | 'FILL_AND_OUTLINE';

export type CornerTypeSerializateJSON = 'ROUNDED' | 'MITERED' | 'BEVELED';

export type ColorBlendModeSerializateJSON = 'HIGHLIGHT' | 'REPLACE' | 'MIX';

export type ReferenceFrameSerializateJSON = 'FIXED' | 'INERTIAL';

export class EnumSerializate {
  private constructor() {}
  static toJSON(enumObject: any, data?: any): any | undefined {
    if (data !== null && data !== undefined) {
      return Object.keys(enumObject).find(key => enumObject[key] === data);
    }
  }

  static fromJSON(enumObject: any, json?: any): any | undefined {
    if (json !== null && json !== undefined) {
      return enumObject[json];
    }
  }
}
