<!-- PointGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { PointGraphicsKey, PointGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { PointGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import NearFarScalarAttribute from './near-far-scalar-attribute.vue';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'PointGraphicsGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: PointGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.PointGraphics, PointGraphicsSerializateJSON>({
  graphic: () => props.entity?.point,
  omit: props.omit,
  toJSON: (graphics, omit) => PointGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => PointGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="point"
    graphics-field="show"
    label="可见"
  />
  <NumberAttribute
    v-if="!hide?.includes('pixelSize')"
    v-model="model.pixelSize"
    graphics="point"
    graphics-field="pixelSize"
    label="像素大小"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="point"
    graphics-field="heightReference"
    label="高度参照"
  />
  <ColorAttribute
    v-if="!hide?.includes('color')"
    v-model="model.color"
    graphics="point"
    graphics-field="color"
    label="颜色"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="point"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="point"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('scaleByDistance')"
    v-model="model.scaleByDistance"
    graphics="point"
    graphics-field="scaleByDistance"
    label="按距离缩放"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('translucencyByDistance')"
    v-model="model.translucencyByDistance"
    graphics="point"
    graphics-field="translucencyByDistance"
    label="按距离透明"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="point"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <NumberAttribute
    v-if="!hide?.includes('disableDepthTestDistance')"
    v-model="model.disableDepthTestDistance"
    graphics="point"
    graphics-field="disableDepthTestDistance"
    label="禁用深度检测距离"
    :precision="2"
  />
</template>
