<svg width="40" height="41" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <filter x="-22.4%" y="-16%" width="144.9%" height="144.9%" filterUnits="objectBoundingBox" id="prefix__a">
            <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" in="shadowBlurOuter1" />
        </filter>
        <filter x="-72.2%" y="-61.1%" width="244.5%" height="244.4%" filterUnits="objectBoundingBox" id="prefix__c">
            <feMorphology radius="1" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1" />
            <feOffset dy="2" in="shadowSpreadOuter1" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 0.00372360947 0 0 0 0 0.0580977667 0 0 0 0 0.0786062047 0 0 0 0.5 0"
                in="shadowBlurOuter1" />
        </filter>
        <filter x="-38.9%" y="-27.8%" width="177.8%" height="177.8%" filterUnits="objectBoundingBox" id="prefix__f">
            <feOffset dy="2" in="SourceAlpha" result="shadowOffsetOuter1" />
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1" />
            <feColorMatrix values="0 0 0 0 0.00372360947 0 0 0 0 0.0580977667 0 0 0 0 0.0786062047 0 0 0 0.5 0"
                in="shadowBlurOuter1" />
        </filter>
        <path id="prefix__b" d="M2.4 2.4h31.2v31.2H2.4z" />
        <path
            d="M27.102 10.015c.351-.041.594.003.73.133.134.13.188.365.161.706-.027.34-.064.726-.111 1.156-.047.43-.098.87-.152 1.32l-.162 1.34c-.054.444-.101.85-.142 1.218-.04.396-.152.648-.334.758-.182.109-.388.04-.618-.205-.135-.137-.31-.304-.526-.502a15.686 15.686 0 01-.547-.521c-.162-.15-.31-.202-.446-.154-.135.048-.29.16-.466.338-.364.368-.685.702-.962 1.003-.276.3-.604.64-.982 1.023-.08.082-.175.16-.283.235a1.14 1.14 0 01-.345.164.675.675 0 01-.385-.01c-.135-.041-.27-.137-.405-.287a5.132 5.132 0 00-.212-.204 4.044 4.044 0 01-.172-.164 4.044 4.044 0 00-.173-.164 2.87 2.87 0 01-.232-.246c-.27-.272-.379-.511-.324-.716.054-.204.222-.443.506-.716l.77-.819c.256-.273.56-.586.91-.941.257-.246.433-.464.527-.655.095-.191.048-.368-.141-.532-.122-.123-.277-.29-.466-.501a14.452 14.452 0 00-.486-.522c-.23-.232-.318-.437-.264-.614.054-.178.257-.287.608-.328.351-.04.746-.088 1.185-.143.439-.055.891-.106 1.357-.154.466-.047.921-.098 1.367-.153.445-.055.85-.102 1.215-.143zM16.85 20.25c.081.083.152.151.213.206.06.055.118.11.172.165l.162.185.243.227c.27.274.388.525.354.751-.033.227-.192.484-.475.772-.257.261-.527.539-.81.834-.284.295-.601.621-.952.978-.243.247-.395.45-.456.607-.06.158-.003.326.172.505.135.123.287.278.456.463.169.185.32.347.456.484.23.233.313.44.253.618-.061.178-.267.288-.618.33-.35.04-.742.088-1.174.143-.432.055-.881.11-1.347.165l-1.377.165c-.452.055-.86.103-1.225.144-.337.027-.577-.024-.718-.155-.142-.13-.2-.36-.173-.69.027-.342.065-.73.112-1.163.047-.432.098-.875.152-1.328l.162-1.348c.054-.446.1-.855.141-1.225.04-.412.152-.67.335-.772.182-.103.388-.038.617.195.135.137.32.316.557.535.236.22.429.405.577.556.162.165.297.237.405.217.108-.021.243-.12.405-.299.175-.178.344-.353.506-.525.162-.172.324-.34.486-.504.162-.165.327-.337.496-.515.169-.179.348-.364.537-.556.08-.082.168-.165.263-.247a.976.976 0 01.314-.185.598.598 0 01.364-.01c.129.034.267.126.415.277z"
            id="prefix__d" />
        <path
            d="M26.102 9.015c.351-.041.594.003.73.133.134.13.188.365.161.706-.027.34-.064.726-.111 1.156-.047.43-.098.87-.152 1.32l-.162 1.34c-.054.444-.101.85-.142 1.218-.04.396-.152.648-.334.758-.182.109-.388.04-.618-.205-.135-.137-.31-.304-.526-.502a15.686 15.686 0 01-.547-.521c-.162-.15-.31-.202-.446-.154-.135.048-.29.16-.466.338-.364.368-.685.702-.962 1.003-.276.3-.604.64-.982 1.023-.08.082-.175.16-.283.235a1.14 1.14 0 01-.345.164.675.675 0 01-.385-.01c-.135-.041-.27-.137-.405-.287a5.132 5.132 0 00-.212-.204 4.044 4.044 0 01-.172-.164 4.044 4.044 0 00-.173-.164 2.87 2.87 0 01-.232-.246c-.27-.272-.379-.511-.324-.716.054-.204.222-.443.506-.716l.77-.819c.256-.273.56-.586.91-.941.257-.246.433-.464.527-.655.095-.191.048-.368-.141-.532-.122-.123-.277-.29-.466-.501a14.452 14.452 0 00-.486-.522c-.23-.232-.318-.437-.264-.614.054-.178.257-.287.608-.328.351-.04.746-.088 1.185-.143.439-.055.891-.106 1.357-.154.466-.047.921-.098 1.367-.153.445-.055.85-.102 1.215-.143zM16.063 19.457c.06.055.118.11.172.165l.162.185.243.227c.27.274.388.525.354.751-.033.227-.192.484-.475.772-.257.261-.527.539-.81.834-.284.295-.601.621-.952.978-.243.247-.395.45-.456.607-.06.158-.003.326.172.505.135.123.287.278.456.463.169.185.32.347.456.484.23.233.313.44.253.618-.061.178-.267.288-.618.33-.35.04-.742.088-1.174.143-.432.055-.881.11-1.347.165l-1.377.165c-.452.055-.86.103-1.225.144-.337.027-.577-.024-.718-.155-.142-.13-.2-.36-.173-.69.027-.342.065-.73.112-1.163.047-.432.098-.875.152-1.328l.162-1.348c.054-.446.1-.855.141-1.225.04-.412.152-.67.335-.772.182-.103.388-.038.617.195.135.137.32.316.557.535.236.22.429.405.577.556.162.165.297.237.405.217.108-.021.243-.12.405-.299.175-.178.344-.353.506-.525.162-.172.324-.34.486-.504.162-.165.327-.337.496-.515.169-.179.348-.364.537-.556.08-.082.168-.165.263-.247a.976.976 0 01.314-.185.598.598 0 01.364-.01c.129.034.267.126.415.277.081.083.152.151.213.206z"
            id="prefix__g" />
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="prefix__e">
            <stop stop-color="#FFF" offset="0%" />
            <stop stop-color="#CCE7FF" offset="100%" />
        </linearGradient>
    </defs>
    <g fill="none" fill-rule="evenodd">
        <g transform="translate(2 1)">
            <use fill="#000" filter="url(#prefix__a)" xlink:href="#prefix__b" />
            <use fill="#1465C9" xlink:href="#prefix__b" />
        </g>
        <g opacity=".5" fill-rule="nonzero" transform="translate(2 1)">
            <use fill="#000" filter="url(#prefix__c)" xlink:href="#prefix__d" />
            <use fill="url(#prefix__e)" xlink:href="#prefix__d" />
        </g>
        <g fill-rule="nonzero" transform="translate(2 1)">
            <use fill="#000" filter="url(#prefix__f)" xlink:href="#prefix__g" />
            <use fill="url(#prefix__e)" xlink:href="#prefix__g" />
        </g>
        <path stroke="#0085FF" stroke-width="2" d="M3 35v2M36 36h2M36 2h2M3 1v2" />
    </g>
</svg>