import type { ColorSerializateJSON } from './color';

import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type {
  ArcTypeSerializateJSON,
  ClassificationTypeSerializateJSON,
  HeightReferenceSerializateJSON,
  ShadowModeSerializateJSON,
} from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import type { PolygonHierarchySerializateJSON } from './polygon-hierarchy';
import * as Cesium from 'cesium';
import { ColorSerializate } from './color';

import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { PolygonHierarchySerializate } from './polygon-hierarchy';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PolygonGraphicsSerializateJSON {
  show?: boolean;
  hierarchy?: PolygonHierarchySerializateJSON;
  height?: number;
  heightReference?: HeightReferenceSerializateJSON;
  extrudedHeight?: number;
  extrudedHeightReference?: HeightReferenceSerializateJSON;
  stRotation?: number;
  granularity?: number;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  perPositionHeight?: boolean;
  closeTop?: boolean;
  closeBottom?: boolean;
  arcType?: ArcTypeSerializateJSON;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  classificationType?: ClassificationTypeSerializateJSON;
  zIndex?: number;
  textureCoordinates?: PolygonHierarchySerializateJSON;
}

export type PolygonGraphicsKey = keyof PolygonGraphicsSerializateJSON;

export class PolygonGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.PolygonGraphics,
    omit?: PolygonGraphicsKey[],
    time?: Cesium.JulianDate,
  ): PolygonGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      hierarchy: PolygonHierarchySerializate.toJSON(getValue('hierarchy')),
      height: getValue('height'),
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      extrudedHeight: getValue('extrudedHeight'),
      extrudedHeightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('extrudedHeightReference'))
        ?? 'NONE',
      stRotation: getValue('stRotation') ?? 0,
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      perPositionHeight: getValue('perPositionHeight'),
      closeTop: getValue('closeTop') ?? true,
      closeBottom: getValue('closeBottom') ?? true,
      arcType: EnumSerializate.toJSON(Cesium.ArcType, getValue('arcType')) ?? 'GEODESIC',
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType:
        EnumSerializate.toJSON(Cesium.ClassificationType, getValue('classificationType')) ?? 'BOTH',
      zIndex: getValue('zIndex'),
      textureCoordinates: PolygonHierarchySerializate.toJSON(getValue('textureCoordinates')),
    };
  }

  static fromJSON(
    json?: PolygonGraphicsSerializateJSON,
    omit?: PolygonGraphicsKey[],
  ): Cesium.PolygonGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.PolygonGraphics({
      show: getValue('show') ?? true,
      hierarchy: PolygonHierarchySerializate.fromJSON(getValue('hierarchy')),
      height: getValue('height'),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      extrudedHeight: getValue('extrudedHeight'),
      extrudedHeightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('extrudedHeightReference'),
      ),
      stRotation: getValue('stRotation') ?? 0,
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      perPositionHeight: getValue('perPositionHeight'),
      closeTop: getValue('closeTop') ?? true,
      closeBottom: getValue('closeBottom') ?? true,
      arcType: EnumSerializate.fromJSON(Cesium.ArcType, getValue('arcType')),
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType: EnumSerializate.fromJSON(
        Cesium.ClassificationType,
        getValue('classificationType'),
      ),
      zIndex: getValue('zIndex'),
      textureCoordinates: PolygonHierarchySerializate.fromJSON(getValue('textureCoordinates')),
    });
  }
}
