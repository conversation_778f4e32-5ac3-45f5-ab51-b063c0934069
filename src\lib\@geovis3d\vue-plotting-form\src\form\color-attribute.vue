<!-- ColorAttribute -->
<script lang="ts" setup>
import type { ColorSerializateJSON } from '@/lib/@geovis3d/plotting';
import { useVModel } from '@vueuse/core';
import Color from 'color';

import { computed } from 'vue';

defineOptions({ name: 'ColorAttribute' });

const props = defineProps<{
  modelValue?: ColorSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: ColorSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const alpha = computed({
  get() {
    try {
      return Color(model.value).alpha() * 100;
    }
    catch {
      return 0;
    }
  },
  set(value: number) {
    try {
      model.value = Color(model.value)
        .alpha(value / 100)
        .toString();
    }
    catch {}
  },
});

const hex = ref('');

watchEffect(() => {
  hex.value = model.value ? Color(model.value).hex() : '';
});

// HEX值在输入框失去焦点后再进行双向绑定
function lazySetter() {
  model.value = Color(hex.value).alpha(alpha.value).toString();
}

const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577',
];
</script>

<template>
  <el-form-item :label="label">
    <div class="color-attribute">
      <el-color-picker v-model="model" show-alpha :predefine="predefineColors" />
      <el-input v-model="hex" @change="lazySetter" />
      <el-input-number
        v-model.number="alpha"
        :min="0"
        :max="100"
        :precision="0"
        :controls="false"
      />
      <span>%</span>
      <div
        class="icon-clear"
        :title="alpha == 0 ? '显示' : '一键透明'"
        @click="alpha = alpha == 0 ? 100 : 0"
      >
        <IconParkPreviewCloseOne v-if="alpha == 0" />
        <IconParkPreviewOpen v-else />
      </div>
    </div>
  </el-form-item>
</template>

<style scoped lang="scss">
.color-attribute {
  position: relative;
  display: flex;
  flex: 3.2;
  align-items: center;
  padding-left: 4px;
  overflow: hidden;
  line-height: 1;
  background-color: var(--el-fill-color-blank);
  border-radius: var(--el-border-radius-base);

  .el-input,
  .el-input-number {
    --el-fill-color-blank: transparent;

    :deep() .el-input__wrapper {
      padding: 0 2px;
    }
  }

  > .el-input {
    flex: 2;
  }

  > .el-input-number {
    flex: 1;
  }

  .icon-clear {
    display: flex;
    align-items: center;
    align-self: stretch;
    justify-content: center;
    width: 38px;
    margin-left: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s;

    &:hover {
      background: #9bc1ff4d;
    }
  }
}
</style>
