import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';

/**
 * polyline
 */
export default <PlottingControllerOptions>{
  type: 'polyline',
  manualTerminate: entity => entity.plotting.coordinates.getLength() > 1,
  center: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  interval: { visible: true },
  update(entity) {
    if (!entity.polyline) {
      entity.polyline = new Cesium.PolylineGraphics({
        material: Cesium.Color.RED,
        width: 2,
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    const cache = positions.length >= 2 ? positions : [];
    entity.polyline!.positions = new Cesium.CallbackProperty(() => cache, false);
  },
};
