import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.LabelCollection} 构造函数参数
 */
export type LabelCollectionConstructorOptions = ConstructorParameters<
  typeof Cesium.LabelCollection
>[0];

/**
 * {@link Cesium.LabelCollection} 拓展用法与 {@link Cesium.LabelCollection} 基本一致。
 *
 * `GcLabelCollection.event`鼠标事件监听
 */
export class GcLabelCollection extends Cesium.LabelCollection {
  constructor(options?: LabelCollectionConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
