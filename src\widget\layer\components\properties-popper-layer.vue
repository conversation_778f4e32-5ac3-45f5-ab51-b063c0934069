<!-- 影像属性弹窗 -->

<!-- WFS查询参考：https://docs.geoserver.org/latest/en/user/services/wfs/reference.html -->
<script lang="ts" setup>
import type { Feature } from 'ol';
import { cartesianToWgs84 } from '@x3d/all';
import { useCzDataSource, useCzViewer } from '@x3d/vue-hooks';
import { ElMessage, ElMessageBox } from 'element-plus';
import { GeoJSON } from 'ol/format';
import { useWfsState } from '../state/wfs-state';

defineOptions({ name: 'PropertiesPopperLayer' });

const { position, currentPropertyKeys, currentFeature, editting, currentWFSService } = useWfsState();

const viewer = useCzViewer();

useCzDataSource(() => {
  return currentFeature.value && !editting.value ? Cesium.GeoJsonDataSource.load(currentFeature.value) : undefined;
});

/**
 * 删除要素
 */
async function deleteFeature() {
  try {
    await ElMessageBox.confirm('确定要删除该要素吗？此操作不可撤销。', '删除要素确认', {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    });

    if (!currentFeature.value) {
      ElMessage.error('无法获取要素信息，删除失败');
      return;
    }

    // 使用 GeoJSON 格式转换当前要素
    const geojsonFormat = new GeoJSON();
    const feature = geojsonFormat.readFeature(currentFeature.value) as Feature;
    const geomKey = (await currentWFSService.value?.getGeomKey())?.name ?? 'the_geom';
    feature.setGeometryName(geomKey);

    // 使用 transaction 删除要素
    await currentWFSService.value?.transaction({
      deletes: [feature],
      options: {
        nativeElements: [],
      },
    });

    ElMessage.success(`要素删除成功！`);

    // 清除图层缓存
    for (let index = 0; index < viewer.value.imageryLayers.length; index++) {
      // @ts-expect-error 此处是Cesium的私有属性
      viewer.value.imageryLayers.get(index)._imageryCache = {};
    }

    // 关闭属性弹窗
    position.value = undefined;
  }
  catch (error: any) {
    // 用户取消删除
    if (error === 'cancel') {
      ElMessage.info('已取消删除操作');
      return;
    }
    console.error('删除失败:', error);
    ElMessage.error('删除失败，请检查服务器连接和权限');
  }
}
</script>

<template>
  <located-popper1
    v-if="position && currentFeature && !editting"
    :position="position"
    show-close
    header="属性信息"
    class="w-500px!"
    @close="position = undefined"
  >
    <template #extra>
      <el-button type="primary" @click="editting = true">
        编辑
      </el-button>
      <el-button type="danger" @click="deleteFeature()">
        删除
      </el-button>
    </template>
    <el-scrollbar h="250px!" wrap-class="p-10px">
      <el-descriptions title="" :column="1" border :label-width="$vh(10)">
        <el-descriptions-item label="经度">
          {{ cartesianToWgs84(position)?.[0]?.toFixed(5) }}
        </el-descriptions-item>
        <el-descriptions-item label="纬度">
          {{ cartesianToWgs84(position)?.[1]?.toFixed(5) }}
        </el-descriptions-item>
        <el-descriptions-item
          v-for="item in currentPropertyKeys?.filter(item => !item.type?.startsWith('gml:'))"
          :key="item.name"
          :label="item.name"
        >
          {{ currentFeature?.properties?.[item.name] }}
        </el-descriptions-item>
      </el-descriptions>
    </el-scrollbar>
  </located-popper1>
</template>

<style lang="scss">
.features {
  /* stylelint-disable-next-line selector-class-pattern */
  .featureInfo {
    width: 100%;
  }
}
</style>
