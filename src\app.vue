<script lang="ts" setup>
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import { useSettingStore } from './stores/setting';

defineOptions({ name: 'App' });
const settingStore = useSettingStore();
const appName = computed(() => settingStore.setting?.sysTitle || import.meta.env.VITE_APP_NAME);
useTitle(appName);
</script>

<template>
  <el-config-provider :locale="zhCn">
    <router-view />
  </el-config-provider>
</template>

<style>
#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>
