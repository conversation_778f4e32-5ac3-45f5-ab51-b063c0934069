import type { GcEntityConstructorOptions } from '../vector';
import type { ControlPointSymbol } from './plotting-coordinates-property';
import type { PlottingEntity } from './plotting-entity';

import type { SampledPointSymbol } from './plotting-sampleds-property';

import { coordinateToCartesian } from '@/lib/@geovis3d/coordinate';
import { effectHelper, throttle } from '@/lib/@geovis3d/shared';
import * as Cesium from 'cesium';
import { createEntityEffect } from '../vector';
import { plottingActiveCallback } from './plotting-active-callback';

import { PlottingCoordinatesProperty } from './plotting-coordinates-property';
import { plottingDefiningCallback } from './plotting-defining-callback';
import { plottingSampledCallback } from './plotting-sampled-callback';
import { PlottingSampledProperty } from './plotting-sampleds-property';

export type PlottingControllerDefinitionChanged = (
  scope: PlottingController,
  field: 'defining' | 'active' | 'enabled' | 'sampling' | 'playing',
  value: any,
  prevValue: any
) => void;

export interface PlottingChildOptions<T = boolean> {
  /**
   * 是否生成child-entity判断，每次渲染时都进行一次此回调进行判断
   */
  visible?: T | ((entity: PlottingEntity) => T);

  /**
   * child-entity的样式
   */
  style?: GcEntityConstructorOptions;
}

export interface PlottingControllerOptions {
  /**
   * 是否禁用
   */
  disabled?: boolean;

  type?: string;

  /**
   * 是否允许左键双击停止定义态。每次新增左键新增控制点时进行回调判断
   */
  manualTerminate?: (entity: PlottingEntity) => boolean;

  /**
   * 是否立即终止定义态。每次新增左键新增控制点时进行回调判断
   */
  forceTerminate?: (entity: PlottingEntity) => boolean;

  /**
   * 控制点配置
   */
  control?: PlottingChildOptions<boolean>;

  /**
   * 间隔点配置  loop首尾间也插入间隔点
   */
  interval?: PlottingChildOptions<boolean | 'loop'>;

  /**
   * 中心点配置
   */
  center?: PlottingChildOptions<boolean>;

  /**
   * 位置控制配置
   */
  location?: PlottingChildOptions<boolean>;

  /**
   * 海拔控制配置
   */
  altitude?: PlottingChildOptions<boolean>;

  /**
   * 缩放控制配置
   */
  scale?: PlottingChildOptions<boolean>;

  /**
   * 缩放控制回调
   */
  scaleCallback?: (entity: PlottingEntity, scale: number) => void;

  /**
   * 删除控制配置
   */
  delete?: PlottingChildOptions<boolean>;

  /**
   * X轴旋转控制配置
   */
  rotationX?: PlottingChildOptions<boolean>;

  /**
   * Y轴旋转控制配置
   */
  rotationY?: PlottingChildOptions<boolean>;

  /**
   * Z轴旋转控制配置
   */
  rotationZ?: PlottingChildOptions<boolean>;

  /**
   * 触发标绘更新
   */
  update?: (entity: PlottingEntity) => void;

  /**
   * 标绘被清除时回调
   */
  destroy?: (entity: PlottingEntity) => void;
}

/**
 * 标绘控制器构造参数
 */
export interface PlottingControllerConstructorOptions {
  /**
   * 如果传入则获取缓存中的控制器配置
   */
  type?: string;

  /**
   * 预设控制点坐标
   */
  coordinates?: ControlPointSymbol[];

  /**
   * 预设路径采样坐标
   */
  sampleds?: SampledPointSymbol[];

  /**
   * 控制器配置，传入此项则优先使用此控制器配置
   */
  options?: PlottingControllerOptions;
}

/**
 * 标绘控制器
 *
 * 通过传入`type`去获取缓存中的标绘控制器配置，亦或者直接传入`options`作为控制器配置
 */
export class PlottingController {
  /**
   * @internal
   */
  private static _controllerCache = new Map<string, PlottingControllerOptions>();

  /**
   * 添加标绘控制器配置到缓存中
   * @param type 缓存唯一值
   * @param controller 控制器配置
   */
  static addController(type: string, controller: PlottingControllerOptions) {
    this._controllerCache.set(type, controller);
  }

  constructor(entity: PlottingEntity, params?: PlottingControllerConstructorOptions) {
    this._entity = entity;
    this._type = params?.type;
    if (params?.options) {
      this._options = params.options;
    }
    else if (params?.type) {
      const data = PlottingController._controllerCache.get(params.type) ?? {};
      this._options = { ...data };
    }
    else {
      this._options = {};
    }

    this._coordinates = new PlottingCoordinatesProperty(params?.coordinates);
    this._sampled = new PlottingSampledProperty(this, params?.sampleds);

    createEntityEffect(entity, (onCleanup) => {
      // 鼠标坐标监听
      const [mouseMoveExecute, mouseMoveDestroy] = effectHelper((oncleanup) => {
        const stop = this.scene?.geovis3d.screenEvent.on(
          'MOUSE_MOVE',
          throttle((context: Cesium.ScreenSpaceEventHandler.MotionEvent) => {
            const mode = entity.model ? 'globePick' : 'auto';
            this._mousePosition = coordinateToCartesian(context.endPosition, this.scene!, mode);
            if (this._mousePosition) {
              this._update();
            }
          }, 1),
        );
        oncleanup(() => stop?.());
      });
      // 定义态时 保存鼠标坐标，非定义态清除鼠标坐标
      const stop = this.definitionChanged.addEventListener((_, field, value) => {
        if (field === 'defining' || field === 'sampling') {
          if (value) {
            mouseMoveExecute();
          }
          else {
            mouseMoveDestroy();
            this._mousePosition = undefined;
          }
        }
      });
      onCleanup(stop);
    });

    this.definitionChanged.addEventListener(() => {
      // 进入激活态时，如果控制点为空，则可认为新增标绘，此时自动进入定义态.
      if (!this.disabled && !this.defining && this.active && this.coordinates.getLength() === 0) {
        this._setDefining(true);
      }
      this._update();
    });

    this.coordinates.definitionChanged.addEventListener(() => {
      this._update();
    });

    this._entity.definitionChanged.addEventListener((_, field, value) => {
      if (field === 'isMounted') {
        if (value && this.coordinates.getLength() === 0) {
          this.active = true;
        }
        value ? this._update() : this._destroy();
      }
    });

    this.entity.event.on('LEFT_CLICK', () => {
      // 该标绘禁用状态时，不可点击
      if (this.disabled) {
        return;
      }
      const activeEntity = this.scene!.geovis3d.plotting.active;

      // 避免其他正在定义态的entity还在定义点时，点击到此entity
      if (!activeEntity?.plotting.defining) {
        this.active = true;
      }
    });

    plottingDefiningCallback(this);
    plottingActiveCallback(this);
    plottingSampledCallback(this);
  }

  get scene(): Cesium.Scene | undefined {
    const owner = this._entity?.entityCollection?.owner as any;
    return owner.clustering?._scene;
  }

  /**
   * @internal
   */
  private _type?: string;

  get type(): string | undefined {
    return this._type;
  }

  /**
   * @internal
   */
  private _entity: PlottingEntity;

  get entity(): PlottingEntity {
    return this._entity;
  }

  /**
   * @internal
   */
  private _options: PlottingControllerOptions;

  /**
   * 是否禁用
   */
  get disabled() {
    return this.options.disabled || !!this._entity.owner?.disabled;
  }

  /**
   * 控制器配置
   */
  get options(): PlottingControllerOptions {
    return this._options;
  }

  /**
   * @internal
   */
  private _coordinates: PlottingCoordinatesProperty;

  /**
   * 控制点数组
   */
  get coordinates(): PlottingCoordinatesProperty {
    return this._coordinates;
  }

  /**
   * @internal
   */
  private _mousePosition?: Cesium.Cartesian3;

  /**
   * 实时鼠标位置
   */
  get mousePosition(): Cesium.Cartesian3 | undefined {
    return this._mousePosition;
  }

  /**
   * @internal
   */
  private _sampled: PlottingSampledProperty;

  /**
   * 控制点数组
   */
  get sampleds(): PlottingSampledProperty {
    return this._sampled;
  }

  /**
   * @internal
   */
  private _defining?: boolean;

  /**
   * 标绘是否处于定义态
   */
  get defining(): boolean {
    return !!this._defining;
  }

  /**
   * 设置定义态
   * 暴露给内部使用
   * @internal
   */
  _setDefining(value: boolean) {
    value = !!value;
    if (value !== this._defining) {
      this._defining = value;
      this.definitionChanged.raiseEvent(this, 'defining', value, !value);
    }
  }

  /**
   * 标绘是否处于激活态
   */
  get active(): boolean {
    const entity = this.scene!.geovis3d.plotting.active;
    return !!this._entity && entity === this._entity;
  }

  /**
   * 标绘是否处于激活态
   */
  set active(value: boolean) {
    value = !!value;
    if (value === this.active) {
      return;
    }
    if (value) {
      this.scene!.geovis3d.plotting.active = this._entity;
    }
    else {
      this.scene!.geovis3d.plotting.active = undefined;
    }
  }

  /**
   * @internal
   */
  private _sampling?: boolean;

  /**
   * 是否处于路径采样态
   */
  get sampling(): boolean {
    return !!this._sampling;
  }

  /**
   * 设置路径采样态
   *
   * 只有在标绘完成后（defining==false && active==true)时才能激起
   */
  set sampling(value: boolean) {
    value = !!value;
    if (value !== this._sampling) {
      if (value && (this.defining || !this.active)) {
        throw new Error('只有在非定义态且激活时才可设置采样态为ture');
      }
      this._sampling = value;
      this.definitionChanged.raiseEvent(this, 'sampling', value, !value);
    }
  }

  /**
   * @internal
   */
  private _playing?: boolean;

  /**
   * 开始播放轨迹
   */
  get playing(): boolean {
    return !!this._playing;
  }

  /**
   * 开始播放轨迹
   */
  set playing(value: boolean) {
    value = !!value;
    if (value !== this._playing) {
      this._playing = value;
      this.definitionChanged.raiseEvent(this, 'playing', value, !value);
    }
  }

  /**
   * @internal
   */
  private _definitionChanged = new Cesium.Event<PlottingControllerDefinitionChanged>();

  /**
   * 属性发生变化时触发
   */
  get definitionChanged(): Cesium.Event<PlottingControllerDefinitionChanged> {
    return this._definitionChanged;
  }

  /**
   * 更新
   * @internal
   */
  private _update = throttle(() => {
    if (this._entity.isMounted) {
      this._options.update?.(this._entity);
      !this.active && !this.coordinates.getLength() && this._destroy();
    }
  }, 0);

  /**
   * 销毁
   * @internal
   */
  private _destroy() {
    this._options.destroy?.(this._entity);
    this._entity.entityCollection?.remove(this._entity);
  }
}
