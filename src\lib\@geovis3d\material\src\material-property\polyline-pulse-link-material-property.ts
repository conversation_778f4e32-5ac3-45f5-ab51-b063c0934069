import * as Cesium from 'cesium';

import PolylinePulseLinkPNG from './assets/polyline-pulse-link.png';
import { setCesiumMaterialCache } from './material-cache';
import PolylinePulseLink from './shaders/polyline-pulse-link.glsl?raw';

export interface PolylinePulseLinkMaterialUniforms {
  color?: Cesium.Color;
  time?: number;
  image?: string;
}

const uniforms: PolylinePulseLinkMaterialUniforms = {
  color: Cesium.Color.RED,
  time: 0,
  image: PolylinePulseLinkPNG,
};

const PolylinePulseLinkMaterialType = 'PolylinePulseLinkMaterialType';

export class PolylinePulseLinkMaterial extends Cesium.Material {
  constructor(options?: PolylinePulseLinkMaterialUniforms) {
    super({
      fabric: {
        type: PolylinePulseLinkMaterialType,
        source: PolylinePulseLink,
        uniforms: {
          color: options?.color ?? uniforms.color,
          time: options?.time ?? uniforms.time,
          image: options?.image ?? uniforms.image,
        },
      },
    });
  }
}

/**
 * 滋滋线
 */
export class PolylinePulseLinkMaterialProperty implements Cesium.MaterialProperty {
  constructor(options?: PolylinePulseLinkMaterialUniforms) {
    this._color = Cesium.defaultValue(options?.color, uniforms.color);
    // this._time = Cesium.defaultValue(options?.time, uniforms.time);
    this._time = performance.now();

    this._image = Cesium.defaultValue(options?.image, uniforms.image);
  }

  private _time: number;

  get time() {
    return this._time;
  }

  set time(value: number) {
    if (this._time !== value) {
      this._time = value;
    }
  }

  private _color: Cesium.Color;

  get color() {
    return this._color;
  }

  set color(value: Cesium.Color) {
    if (this._color !== value) {
      this._color = value;
    }
  }

  private _image: string;

  get image() {
    return this._image;
  }

  set image(value: string) {
    if (this._image !== value) {
      this._image = value;
    }
  }

  static get MaterialType() {
    return PolylinePulseLinkMaterialType;
  }

  getType(_time?: Cesium.JulianDate) {
    return PolylinePulseLinkMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: PolylinePulseLinkMaterialUniforms) {
    result ??= {};
    result.color = this.color;
    result.image = this.image;
    result.time = (performance.now() - this._time) / 1000;

    return result;
  }

  equals(other?: PolylinePulseLinkMaterialProperty) {
    return (
      this === other
      || (other instanceof PolylinePulseLinkMaterialProperty
        && this.color == other?.color
        && this.time == other?.time
        && this.image == other?.image)
    );
  }
}
setCesiumMaterialCache(PolylinePulseLinkMaterialType, {
  fabric: {
    type: PolylinePulseLinkMaterialType,
    uniforms,
    source: PolylinePulseLink,
  },
  translucent: () => true,
});
