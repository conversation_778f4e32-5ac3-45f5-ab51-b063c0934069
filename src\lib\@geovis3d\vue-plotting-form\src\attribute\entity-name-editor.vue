<script lang="ts" setup>
import type * as Cesium from 'cesium';
import { TextEllipsis } from '@/lib/@geovis3d/vue-component';
import { shallowRef, watch, watchEffect } from 'vue';

export interface EntityNameEditorProps {
  entity: Cesium.Entity;
  /**
   * 进入编辑状态
   */
  editable?: boolean;
}

export interface EntityNameEditorEmits {
  (event: 'update:editable', value?: boolean): void;
}

defineOptions({ name: 'EntityNameEditor' });

const props = defineProps<EntityNameEditorProps>();

const emit = defineEmits<EntityNameEditorEmits>();

const name = shallowRef<string>();

watchEffect(() => {
  name.value = props.entity.name;
});

watch(name, () => {
  // eslint-disable-next-line vue/no-mutating-props
  props.entity.name = name.value;
});
</script>

<template>
  <el-input v-if="editable" v-model="name" autofocus @click.stop @blur="emit('update:editable', false)" />
  <TextEllipsis v-else style="flex: 1">
    {{ name || '未命名标绘' }}
  </TextEllipsis>
</template>
