import type { Coord, Feature, LineString, Position } from '@turf/turf';

import { getCoord, lineString, midpoint } from '@turf/turf';
import { getArrowBodyPoints, getArrowHeadPoints } from './attack-arrow';
import {
  getBaseLength,
  getBatchCoords,
  getQBSplinePoints,
  getThirdPoint,
  isClockWise,
  mathDistance,
} from './common';

export interface TailedAttackArrowFactorOptions {
  headHeightFactor: number;
  headWidthFactor: number;
  neckHeightFactor: number;
  neckWidthFactor: number;
  tailWidthFactor: number;
  headTailFactor: number;
  swallowTailFactor: number;
}

/**
 * 进攻方向（尾）
 */
export function tailedAttackArrow(
  points: Coord[],
  options?: TailedAttackArrowFactorOptions,
): Feature<LineString> {
  if (points.length < 3) {
    throw new Error('points.length must >=3');
  }

  const headHeightFactor = options?.headHeightFactor ?? 0.18;
  const headWidthFactor = options?.headWidthFactor ?? 0.3;
  const neckHeightFactor = options?.neckHeightFactor ?? 0.85;
  const neckWidthFactor = options?.neckWidthFactor ?? 0.15;
  const tailWidthFactor = options?.tailWidthFactor ?? 0.1;
  const headTailFactor = options?.headTailFactor ?? 0.8;
  const swallowTailFactor = options?.swallowTailFactor ?? 1;

  const _options = {
    headHeightFactor,
    headWidthFactor,
    neckHeightFactor,
    neckWidthFactor,
    tailWidthFactor,
    headTailFactor,
    swallowTailFactor,
  };

  const clockWise = isClockWise(points[0], points[1], points[2]);

  const tailLeft = getCoord(clockWise ? points[1] : points[0]);
  const tailRight = getCoord(clockWise ? points[0] : points[1]);

  const midTail = midpoint(tailLeft, tailRight).geometry.coordinates;
  const boneCoords = [midTail].concat(points.slice(2).map(e => getCoord(e)));
  const headCoords = getArrowHeadPoints(boneCoords, tailLeft, tailRight, _options).map(
    e => e.geometry.coordinates,
  );
  const neckLeft = headCoords[0];
  const neckRight = headCoords[4];

  const tailWidth = mathDistance(tailLeft, tailRight);
  const allLen = getBaseLength(boneCoords);
  const len = allLen * tailWidthFactor * swallowTailFactor;
  const tailCoord = getThirdPoint(boneCoords[1], boneCoords[0], 0, len, true).geometry.coordinates;
  const factor = tailWidth / allLen;
  const bodyCoords = getBatchCoords(getArrowBodyPoints(boneCoords, neckLeft, neckRight, factor));
  const count = bodyCoords.length;
  let leftCoords: Position[] = [tailLeft, ...bodyCoords.slice(0, count / 2)];
  leftCoords.push(neckLeft);
  let rightCoords: Position[] = [tailRight].concat(bodyCoords.slice(count / 2, count));
  rightCoords.push(neckRight);
  leftCoords = getBatchCoords(getQBSplinePoints(leftCoords));
  rightCoords = getBatchCoords(getQBSplinePoints(rightCoords));
  const coords = [...leftCoords, ...headCoords, ...rightCoords.reverse(), tailCoord, leftCoords[0]];
  return lineString(coords);
}
