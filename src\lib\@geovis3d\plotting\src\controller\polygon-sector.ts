import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import * as turf from '@turf/turf';
import * as Cesium from 'cesium';

/**
 * polygon-sector 标绘配置
 */
export default <PlottingControllerOptions>{
  type: 'polygon-sector',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 3,
  location: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);
    if (coords.length <= 1) {
      entity._cache = undefined;
      return;
    }
    // 只有2个控制点  第3个点位为鼠标点绕点1旋转60度
    if (coords.length === 2) {
      const point3 = turf.transformRotate(turf.point(coords[1]), 60, {
        pivot: coords[0],
      });
      coords.push(point3.geometry.coordinates);
    }
    const radius = turf.distance(coords[0], coords[1]) || 0.01;
    const bearing1 = turf.bearing(coords[0], coords[1]);
    const bearing2 = turf.bearing(coords[0], coords[2]);
    const coordinates = turf.sector(coords[0], radius, bearing1, bearing2).geometry.coordinates[0];
    entity._cache = new Cesium.PolygonHierarchy(
      coordinates.map(e => wgs84ToCartesian([...e, 0])),
    );
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
