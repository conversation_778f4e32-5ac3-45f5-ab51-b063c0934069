import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.CompositeEntityCollection} 构造函数参数
 */
export type CompositeEntityCollectionConstructorOptions = ConstructorParameters<
  typeof Cesium.CompositeEntityCollection
>[0];

/**
 * {@link Cesium.CompositeEntityCollection} 拓展用法与 {@link Cesium.CompositeEntityCollection} 基本一致。
 *
 * `GcCompositeEntityCollection.event`鼠标事件监听
 */
export class GcCompositeEntityCollection extends Cesium.CompositeEntityCollection {
  constructor(options?: CompositeEntityCollectionConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
