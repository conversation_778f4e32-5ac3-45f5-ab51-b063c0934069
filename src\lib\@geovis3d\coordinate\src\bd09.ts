import type * as Cesium from 'cesium';

import type { BD09, Coordinate, GCJ02, WGS84 } from './types';
import gcoord from 'gcoord';
import { gcj02ToCartesian, gcj02ToCartographic, gcj02ToCoordinate, gcj02ToWgs84 } from './gcj02';

/**
 * BD09 转 Cartographic
 * @param position BD09
 * @returns Cesium.Cartographic
 */
export function bd09ToCartographic(position: BD09): Cesium.Cartographic {
  const gcj02 = bd09ToGcj02(position);
  return gcj02ToCartographic(gcj02);
}

/**
 * BD09 转 Cartesian3
 * @param position BD09
 * @returns Cesium.Cartesian3
 */
export function bd09ToCartesian(position: BD09): Cesium.Cartesian3 {
  const gcj02 = bd09ToGcj02(position);
  return gcj02ToCartesian(gcj02);
}

/**
 * BD09 转 Coordinate屏幕坐标
 * @param position BD09
 * @returns Coordinate
 */
export function bd09ToCoordinate(position: BD09, scene: Cesium.Scene): Coordinate {
  const gcj02 = bd09ToGcj02(position);
  return gcj02ToCoordinate(gcj02, scene);
}

/**
 * BD09 转 WGS84
 * @param position BD09
 * @returns WGS84
 */
export function bd09ToWgs84(position: BD09): WGS84 {
  const gcj02 = bd09ToGcj02(position);
  return gcj02ToWgs84(gcj02);
}

/**
 * BD09 转 GCJ02
 * @param position BD09
 * @returns GCJ02
 */
export function bd09ToGcj02(position: BD09): GCJ02 {
  const [x, y, height] = position;
  const [longitude, latitude] = gcoord.transform([x, y], gcoord.BD09, gcoord.GCJ02);
  return [longitude, latitude, height];
}
