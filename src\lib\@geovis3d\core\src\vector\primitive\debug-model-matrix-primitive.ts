import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.DebugModelMatrixPrimitive} 构造函数参数
 */
export type DebugModelMatrixPrimitiveConstructorOptions = ConstructorParameters<
  typeof Cesium.DebugModelMatrixPrimitive
>[0];

/**
 * {@link Cesium.DebugModelMatrixPrimitive} 拓展用法与 {@link Cesium.DebugModelMatrixPrimitive} 基本一致。
 *
 * `GcDebugModelMatrixPrimitive.event`鼠标事件监听
 */
export class GcDebugModelMatrixPrimitive extends Cesium.DebugModelMatrixPrimitive {
  constructor(options?: DebugModelMatrixPrimitiveConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
