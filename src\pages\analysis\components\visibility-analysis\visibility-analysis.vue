<!-- 通视分析 -->
<script lang="ts" setup>
import type { Color } from 'cesium';
import { useTerrainCheck } from '@/hooks/useTerrainCheck';
import { cartesianToWgs84, CzEntity, CzPlotEntity } from '@x3d/all';

import { useCzEntities, useCzViewer } from '@x3d/vue-hooks';
import { Cartesian3 } from 'cesium';

defineOptions({ name: 'VisibilityAnalysis' });

const terrainCheck = useTerrainCheck();

const analysisType = ref<string>('single'); // 默认单线
const viewPoint = ref<Cartesian3>(new Cartesian3(0, 0, 0));
const destPoint = ref<Cartesian3>(Cartesian3.ZERO);
const viewHeight = ref<number>(0);
const drawViewLine = shallowRef<CzPlotEntity>();
const entities = ref<CzEntity[]>([]);
const viewer = useCzViewer();
const depthTestAgainstTerrain = viewer.value.scene.globe.depthTestAgainstTerrain;
viewer.value.scene.globe.depthTestAgainstTerrain = true;

// 绘制线
function draw() {
  drawViewLine.value = new CzPlotEntity({
    scheme: {
      forceTerminate: (entity) => {
        const terminate = entity.record.positions.getLength() > 1;
        if (terminate) {
          const { record } = entity;
          const positions = record.positions.getPositions();
          drawViewLine.value = undefined;
          viewPoint.value = positions[0];
          destPoint.value = positions[1];
          viewHeight.value = 0;
          pickFromRay();
        }
        return terminate;
      },
      control: { visible: true },
      effect(entity) {
        entity.polyline ??= new Cesium.PolylineGraphics({
          arcType: Cesium.ArcType.NONE,
          depthFailMaterial: Cesium.Color.YELLOW,
          material: Cesium.Color.YELLOW,
          width: 2,
        });
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 1 ? positions : [];
        entity.polyline.positions = new Cesium.CallbackProperty(() => cache, false);
        entity.position = new Cesium.ConstantPositionProperty(
          record.positions.getCenter()!,
        );
      },
    },
  });
}
function drawLine(leftPoint: Cartesian3, secPoint: Cartesian3, color: Color) {
  return new CzEntity({
    polyline: {
      positions: new Cesium.CallbackProperty(() => [leftPoint, secPoint], false),
      arcType: Cesium.ArcType.NONE,
      width: 5,
      material: color,
      depthFailMaterial: color,
    },
  });
}
function pickFromRay() {
  let entitiesList: CzEntity[] = [];
  let destPoints = [];
  if (analysisType.value === 'single') {
    destPoints = [destPoint.value];
  }
  else {
    const vPoint = cartesianToWgs84(viewPoint.value);
    const dPoint = cartesianToWgs84(destPoint.value || Cartesian3.ZERO);
    const distance = turf.distance([vPoint[0], vPoint[1]], [dPoint[0], dPoint[1]]);
    destPoints = turf
      .getCoords(
        turf.circle([vPoint[0], vPoint[1]], distance, { steps: 20, units: 'kilometres' }),
      )[0]
      .map((p: [number, number]) => Cesium.Cartesian3.fromDegrees(p[0], p[1], dPoint[2]));
  }
  destPoints.forEach((car3) => {
    // 计算射线的方向，目标点left 视域点right
    const direction = Cesium.Cartesian3.normalize(
      Cesium.Cartesian3.subtract(car3, viewPoint.value, new Cesium.Cartesian3()),
      new Cesium.Cartesian3(),
    );
    // 建立射线
    const ray = new Cesium.Ray(viewPoint.value, direction);
    const result = viewer.value.scene?.pickFromRay(ray); // 计算交互点，返回第一个
    const obj = showIntersection(result?.position, car3, viewPoint.value) || [];
    entitiesList = entitiesList?.concat(obj);
  });
  entities.value = entitiesList;
}

// 处理交互点
function showIntersection(
  result: Cartesian3,
  destPoint: Cartesian3,
  viewPoint: Cartesian3,
): CzEntity[] {
  if (Cesium.defined(result) && result instanceof Cesium.Cartesian3) {
    return [
      drawLine(result, viewPoint, Cesium.Color.GREEN), // 可视区域
      drawLine(result, destPoint, Cesium.Color.RED), // 不可视区域
    ];
  }
  else {
    return [drawLine(viewPoint, destPoint, Cesium.Color.GREEN)];
  }
}
function clearVisible() {
  entities.value = [];
  destPoint.value = Cartesian3.ZERO;
  drawViewLine.value = undefined;
}
const entitiesSet = computed(() =>
  [drawViewLine.value, ...(entities.value || [])].filter(e => e),
);
watch(viewHeight, (cur, his) => {
  const position = cartesianToWgs84(viewPoint.value);
  viewPoint.value = Cesium.Cartesian3.fromDegrees(
    position[0],
    position[1],
    (position?.[2] || 0) + (cur - his),
  );
  entities.value.length && pickFromRay();
});
useCzEntities(() => entitiesSet.value);
onUnmounted(() => {
  viewer.value.scene.globe.depthTestAgainstTerrain = depthTestAgainstTerrain;
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="通视分析"
    class="w-400px"
  >
    <el-text v-if="!terrainCheck" p="x-24px!" type="danger">
      未检测到存在地形，请打开图层勾选地形
    </el-text>

    <div class="analy-row my-10px">
      <span>分析图例：</span>
      <div class="legend-two" />
      <span class="legend-text">可视</span>
      <div class="legend-thr" />
      <span class="legend-text">不可视</span>
    </div>
    <div flex="~ justify-around">
      <el-button type="primary" @click="(analysisType = 'single'), draw()">
        单线(点点)通视分析
      </el-button>
      <el-button type="primary" @click="(analysisType = 'circle'), draw()">
        圆形(多点)通视分析
      </el-button>
    </div>
    <el-form class="m-20px">
      <el-form-item label="视线高度">
        <el-slider v-model="viewHeight" :min="0" :max="100" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span />
      <el-button type="primary" class="plain-#FF6363 px-26px!" @click="clearVisible">
        清除
      </el-button>
    </template>
  </drag-card>
</template>

<style lang="scss" scope>
.analy-row {
  display: flex;
  padding: 10px 24px;
  line-height: 26px;

  .legend-one {
    width: 16px;
    height: 6px;
    margin-top: 8px;
    margin-right: 15px;
    margin-left: 15px;
    background: rgb(55 114 163 / 100%);
  }

  .legend-two {
    width: 16px;
    height: 6px;
    margin-top: 8px;
    margin-right: 10px;
    margin-left: 15px;
    background: rgb(0 128 0 / 100%);
  }

  .legend-thr {
    width: 16px;
    height: 6px;
    margin-top: 8px;
    margin-right: 10px;
    margin-left: 15px;
    background: rgb(255 0 0 / 100%);
  }

  .legend-for {
    width: 16px;
    height: 6px;
    margin-top: 8px;
    margin-right: 10px;
    margin-left: 15px;
    background: rgba(#ce9168, 1);
  }

  .legend-text {
    font-size: 16px;
  }

  .row-switch {
    margin-top: 4px;
  }
}
</style>
