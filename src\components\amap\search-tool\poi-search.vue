<!-- 资源&地理搜索组件 -->
<script lang="ts" setup>
import type { InputInstance, ScrollbarInstance } from 'element-plus';

import type { POIOptions } from './common';
import { ICON_MAP } from '@/assets/icon';
import { computedLoading } from '@/hooks/computed-loading';
import { Close, Loading, Position } from '@element-plus/icons-vue';
import { useDebounceFn } from '@vueuse/core';
import { CzEntity } from '@x3d/all';
import { useCzEntity, useCzViewer } from '@x3d/vue-hooks';
import { poiSearch } from './common';

defineOptions({ name: 'PoiSearch' });

const emit = defineEmits<{
  (event: 'change'): void;
}>();

const emitter = useEventBus('pointDetail');

const inputRef = ref<InputInstance>();
const viewer = useCzViewer();

const scrollbarRef = shallowRef<ScrollbarInstance>();
const loadingIcon = ref(false);
const keywords = ref('');

// 做一个防抖
const debouncedKeywords = useDebounceFn(async (keywords) => {
  if (!keywords) {
    loadingIcon.value = false;
    return undefined;
  }
  const data = await poiSearch(keywords);
  scrollbarRef.value?.setScrollTop(0);
  loadingIcon.value = false;
  return data;
}, 2000);

// 高德POI 搜索
const { state: poiResult, isLoading: poiResultLoading } = computedLoading(async () => {
  return await debouncedKeywords(keywords.value); // 无法监听到 因此要传递进去
});

// 属性弹窗
const modalVisible = ref(false);

// POI上球
const currentPOI = shallowRef<POIOptions>();
watch(keywords, () => {
  loadingIcon.value = true;
});
watch(poiResult, () => {
  currentPOI.value = undefined;
});

const position = computed(() => {
  if (!currentPOI.value) {
    return;
  }
  const { latitude, longitude } = currentPOI.value.value;
  return Cesium.Cartesian3.fromDegrees(+longitude, +latitude, 0);
});

const { entity } = useCzEntity(() => {
  if (position.value) {
    const entity = new CzEntity({
      position: position.value,
      billboard: {
        image: ICON_MAP.searchPoint,
        scale: 0.7,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      },
    });
    entity.event.on('LEFT_CLICK', () => (modalVisible.value = true));
    return entity;
  }
});

watch(entity, (entity) => {
  entity
  && viewer.value.flyTo(entity, {
    duration: 2,
    offset: new Cesium.HeadingPitchRange(
      Cesium.Math.toRadians(0),
      -Cesium.Math.PI / 2,
      17000,
    ),
  });
});

const showModal = ref(false);
const showSearch = ref(false);

watchEffect(() => {
  if (poiResult.value?.options?.length || keywords.value) {
    showModal.value = true;
  }
  else {
    showModal.value = false;
  }
});
const img = ref(new URL('./assets/search.png', import.meta.url).href);
function searchBtnClick() {
  showSearch.value = !showSearch.value;
  setTimeout(() => {
    inputRef.value?.focus();
  }, 100);
}
function setPoint(val: any) {
  currentPOI.value = currentPOI.value === val ? undefined : val;
  emitter.emit('pointDetail', {
    data: val,
    isShow: currentPOI.value === val,
  });
}

function changeSearch() {
  emit('change');
  emitter.emit('pointDetail', { data: {}, isShow: false });
}

function backSearchHome() {
  showSearch.value = false;
  showModal.value = false;
  emitter.emit('pointDetail', { data: {}, isShow: false });
  currentPOI.value = undefined;
  keywords.value = '';
}
</script>

<template>
  <div v-if="!showSearch" class="poi-search h-48px w-28%">
    <div text="15px" p="x-10px">
      <img :src="img" size="25px!">
    </div>
    <el-button h="100%!" b="0px!" w="50px!" un-text="24px!" @click="searchBtnClick">
      搜索
    </el-button>
  </div>
  <div v-if="showSearch" class="poi-search h-48px w-100%">
    <div text="18px" p="x-20px">
      <el-icon class="i-custom:search mt-8px" />
    </div>
    <!-- <ElInput ref="inputRef" v-model="keywords" class="search-input" placeholder="请输入地名或经纬" /> -->

    <el-input ref="inputRef" v-model="keywords" class="search-input" placeholder="请输入地名或经纬">
      <template #suffix>
        <el-icon color="#fff" class="is-loading mx-5px">
          <Loading v-if="loadingIcon" />
        </el-icon>
      </template>
    </el-input>

    <el-tooltip content="路径导航" placement="bottom" style="background-color: #000">
      <el-button h="100%!" b="0px!" w="50px!" un-text="18px!" @click="changeSearch">
        <el-icon>
          <Position />
        </el-icon>
      </el-button>
    </el-tooltip>

    <el-button h="100%!" b="0px!" un-text="18px!" @click="backSearchHome">
      <el-icon>
        <Close />
      </el-icon>
    </el-button>
  </div>
  <div
    v-if="showModal"
    max-h="780px!"
    rd="8px"
    p="b-16px"
    flex="~ col"
    class="modal-sty"
  >
    <header-title1> 地点 </header-title1>
    <ElScrollbar ref="scrollbarRef" flex="1" h="100%">
      <template v-if="!!poiResult?.options?.length || poiResultLoading">
        <div
          v-for="item in poiResult?.options"
          :key="item.value.id"
          v-loading="poiResultLoading"
          class="list-item"
          :class="{ active: currentPOI === item }"
          @click="setPoint(item)"
        >
          <div flex="~ items-center justify-center" h="100%" mr="12px">
            <el-icon class="i-custom:location" text="30px!" color="#4176FF" />
          </div>
          <div flex="~ col">
            <el-text flex="self-start!" text="18px!" font="bold" color="#fff!">
              {{ item.value.name }}
            </el-text>
            <el-text flex="self-start!" text="14px!" color="#7E8082!">
              {{ item.value.address }}
            </el-text>
          </div>
        </div>
      </template>
      <el-empty v-else />
    </ElScrollbar>
  </div>
</template>

<style scoped lang="scss">
.modal-sty {
  background: var(--el-bg-color);
  border: 1px solid #ffffff1a;
}

.poi-search {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  background: var(--el-bg-color); // #292b2e;
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 8px;

  .search-input {
    flex: 1;

    --el-input-color: #fff;
    --el-input-placeholder-color: #fff;

    :deep().el-input__wrapper {
      padding: 0;
      font-size: 18px;
      box-shadow: none;
    }
  }
}

.list-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 10px;
  cursor: pointer;

  &:hover,
  &.active {
    background: var(--el-bg-color);
  }
}
</style>
