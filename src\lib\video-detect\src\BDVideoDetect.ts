import type { ChannelInstance, VideoDetect, VideoTreeNode } from './abstract';

/** 大数据局视频方案（大华） */
export class BDVideoDetect implements VideoDetect {
  constructor(private readonly baseURL: string) {}

  private _request(params: RequsetParams) {
    return requset({ ...params, baseURL: this.baseURL } as any);
  }

  /** 获取摄像头总数量 */
  async getCount(): Promise<number> {
    const { data } = await this._request({
      url: '/evo-apigw/evo-brm/1.2.0/device/subsystem/page',
      method: 'post',
      data: {},
    });
    return data?.data?.totalRows;
  }

  async _getTree(id?: string) {
    const { data } = await this._request({
      url: '/evo-apigw/evo-brm/1.2.0/tree',
      method: 'post',
      data: {
        id: id ?? '001',
        checkStat: 0,
        stat: -1,
        showVirtualNode: 1,
      },
    });
    const list = data.data.value ?? [];
    list.forEach(e => (e.isLeaf = false));
    return list;
  }

  async _getSubList(id?: string, pageSize: number = 1000) {
    const { data } = await this._request({
      url: '/evo-apigw/evo-brm/1.2.0/device/channel/subsystem/page',
      method: 'post',
      data: {
        pageNum: 1,
        pageSize,
        ownerCode: id,
      },
    });
    const list = data.data.pageData ?? [];
    return list.map(e => ({
      ...e,
      isLeaf: true,
    }));
  }

  /**
   * 获取树列表
   * @type {params} 不传则返回根列表  传入上一次`getNodes` 的列表项则可获取下级
   */
  async getNodes(params?: VideoTreeNode): Promise<VideoTreeNode[]> {
    let list: any[] = [];
    if (!params?.ext?.id || params?.ext?.isParent) {
      list = await this._getTree(params?.ext?.id);
    }
    else if (!params?.ext?.isParent) {
      list = await this._getSubList(params?.ext?.id);
    }
    return list?.map((item) => {
      const onLine = !(item.isLeaf && item.isOnline != '1');
      return {
        name: item.name || item.channelName,
        code: item.id || item.channelCode,
        isLeaf: !!item.isLeaf,
        onLine,
        isChannel: !!item.channelCode,
        ext: item,
      };
    });
  }

  getInstance(data: VideoTreeNode) {
    return new BDChannelInstance(data, this.baseURL);
  }
}

/** 摄像头实例 */
export class BDChannelInstance implements ChannelInstance {
  constructor(public data: VideoTreeNode, private baseURL: string) {}
  private _request(params: RequsetParams) {
    return requset({ ...params, baseURL: this.baseURL } as any);
  }

  /** 获取实时流RTSP地址 */
  async realTimeRTSP(): Promise<string> {
    const { data: res = {} } = await this._request({
      url: '/evo-apigw//admin/API/MTS/Video/StartVideo',
      method: 'POST',
      data: {
        data: {
          channelId: this.data.ext.channelCode,
          dataType: '1',
          streamType: '1',
        },
      },
    });
    const url = res.data?.url?.split('|').find(e => e.includes('172.25.110.170:9100'));
    return `${url}?token=${res.data?.token}`;
  }
}

interface RequsetParams {
  url: string;
  method: string;
  data?: any;
  params?: any;
}

const fetch = axios.create().request;

function requset({ baseURL, url, method, data, params }) {
  return fetch({
    url: baseURL,
    method,
    headers: {
      'Ph-Url': url,
      'Ph-Accept': 'application/json;charset=UTF-8',
      'Ph-Content-Type': 'application/json;charset=UTF-8',
    },
    data: data || {},
    params: params || {},
  });
}
