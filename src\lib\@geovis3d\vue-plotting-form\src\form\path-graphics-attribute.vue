<!-- PathGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { PathGraphicsKey, PathGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { PathGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'PathGraphicsGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: PathGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.PathGraphics, PathGraphicsSerializateJSON>({
  graphic: () => props.entity?.path,
  omit: props.omit,
  toJSON: (graphics, omit) => PathGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => PathGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="path"
    graphics-field="show"
    label="可见"
  />
  <NumberAttribute
    v-if="!hide?.includes('leadTime')"
    v-model="model.leadTime"
    graphics="path"
    graphics-field="leadTime"
    label="提前秒数"
  />
  <NumberAttribute
    v-if="!hide?.includes('trailTime')"
    v-model="model.trailTime"
    graphics="path"
    graphics-field="trailTime"
    label="延迟秒数"
  />
  <NumberAttribute
    v-if="!hide?.includes('width')"
    v-model="model.width"
    graphics="path"
    graphics-field="width"
    label="宽"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('resolution')"
    v-model="model.resolution"
    graphics="path"
    graphics-field="resolution"
    label="分辨率"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="path"
    graphics-field="material"
    label="材质"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="path"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
