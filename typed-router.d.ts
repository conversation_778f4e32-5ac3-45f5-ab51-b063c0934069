/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/analysis/': RouteRecordInfo<'/analysis/', '/analysis', Record<never, never>, Record<never, never>>,
    '/convert/': RouteRecordInfo<'/convert/', '/convert', Record<never, never>, Record<never, never>>,
    '/directory/': RouteRecordInfo<'/directory/', '/directory', Record<never, never>, Record<never, never>>,
    '/draw/': RouteRecordInfo<'/draw/', '/draw', Record<never, never>, Record<never, never>>,
    '/emergency/': RouteRecordInfo<'/emergency/', '/emergency', Record<never, never>, Record<never, never>>,
    '/file/': RouteRecordInfo<'/file/', '/file', Record<never, never>, Record<never, never>>,
    '/home/': RouteRecordInfo<'/home/', '/home', Record<never, never>, Record<never, never>>,
    '/login/': RouteRecordInfo<'/login/', '/login', Record<never, never>, Record<never, never>>,
    '/servers/': RouteRecordInfo<'/servers/', '/servers', Record<never, never>, Record<never, never>>,
    '/system-log/': RouteRecordInfo<'/system-log/', '/system-log', Record<never, never>, Record<never, never>>,
    '/video/': RouteRecordInfo<'/video/', '/video', Record<never, never>, Record<never, never>>,
  }
}
