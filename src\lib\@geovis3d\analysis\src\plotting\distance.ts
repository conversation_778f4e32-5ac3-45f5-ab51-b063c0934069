import type { GcEntityConstructorOptions } from '@/lib/@geovis3d/core';
import { PlottingEntity } from '@/lib/@geovis3d/core';
import * as Cesium from 'cesium';

import { distance } from '../arithmetic/distance';

export class AnalysisDistanceEntity extends PlottingEntity {
  constructor(options?: GcEntityConstructorOptions) {
    super({
      polyline: {
        material: Cesium.Color.RED,
        width: 2,
      },
      ...options,
      plotting: {
        options: {
          forceTerminate: entity => entity.plotting.coordinates.getLength() > 1,
          center: { visible: false },
          control: { visible: true },
          altitude: { visible: false },
          interval: { visible: false },
          delete: { visible: true },
          update(entity) {
            const positions = entity.plotting.coordinates.getPositions();
            const mousePosition = entity.plotting.mousePosition;
            mousePosition && positions.push(mousePosition.clone());
            const cache = positions.length >= 2 ? positions : [];
            entity.polyline!.positions = new Cesium.CallbackProperty(() => cache, false);
            entity.position = new Cesium.ConstantPositionProperty(
              Cesium.Cartesian3.midpoint(positions[0], positions[1], new Cesium.Cartesian3()),
            );
            distance(positions).then((e) => {
              let text: string = '';
              if (e.count / 1000 > 10) {
                text = `${(e.count / 1000).toFixed(2)}km`;
              }
              else {
                text = `${(+e.count).toFixed(2)}m`;
              }
              entity.label = new Cesium.LabelGraphics({
                text,
                font: '16pt Source Han Sans CN',
                pixelOffset: new Cesium.Cartesian2(0, -20),
              });
            });
          },
        },
      },
    });
  }
}

export class AnalysisGroundDistanceEntity extends PlottingEntity {
  constructor(options?: GcEntityConstructorOptions) {
    super({
      polyline: {
        material: Cesium.Color.RED,
        width: 2,
        clampToGround: true,
      },
      ...options,
      plotting: {
        options: {
          forceTerminate: entity => entity.plotting.coordinates.getLength() > 1,
          center: { visible: false },
          control: { visible: true },
          altitude: { visible: false },
          interval: { visible: false },
          delete: { visible: true },
          update(entity) {
            const positions = entity.plotting.coordinates.getPositions();
            const mousePosition = entity.plotting.mousePosition;
            mousePosition && positions.push(mousePosition.clone());
            const cache = positions.length >= 2 ? positions : [];
            entity.polyline!.positions = new Cesium.CallbackProperty(() => cache, false);
            entity.position = new Cesium.ConstantPositionProperty(
              Cesium.Cartesian3.midpoint(positions[0], positions[1], new Cesium.Cartesian3()),
            );

            if (entity.scene) {
              distance(positions, {
                clampToGround: true,
                scene: entity.scene,
              }).then((e) => {
                let text: string = '';
                if (e.count / 1000 > 10) {
                  text = `${(e.count / 1000).toFixed(2)}km`;
                }
                else {
                  text = `${(+e.count).toFixed(2)}m`;
                }
                entity.label = new Cesium.LabelGraphics({
                  text,
                  font: '16pt Source Han Sans CN',
                  pixelOffset: new Cesium.Cartesian2(0, -20),
                });
              });
            }
          },
        },
      },
    });
  }
}
