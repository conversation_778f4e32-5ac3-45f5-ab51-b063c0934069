import type { DMSCoordinate } from './types';

/**
 * 十进制度 转度分秒
 * @param degrees
 * @param decimal 秒保留几位小数
 * @returns [度,分,秒]
 */
export function dmsEncode(degrees: number, decimal = 3): string {
  const str = `${degrees}`;
  let i = str.indexOf('.');
  const d = i < 0 ? str : str.slice(0, Math.max(0, i));
  let m = '0';
  let s = '0';
  if (i > 0) {
    m = `0${str.slice(Math.max(0, i))}`;
    m = `${+m * 60}`;
    i = m.indexOf('.');
    if (i > 0) {
      s = `0${m.slice(Math.max(0, i))}`;
      m = m.slice(0, Math.max(0, i));
      s = `${+s * 60}`;
      i = s.indexOf('.');
      s = s.slice(0, Math.max(0, i + 4));
      s = (+s).toFixed(decimal);
    }
  }
  return `${Math.abs(+d)}°${+m}′${+s}″`;
}

/**
 * 度分秒转 十进制度
 * @param degrees
 * @param decimal 保留几位小数
 * @returns [度,分,秒]
 */
export function dmsDecode(dmsCode: string, decimal = 6) {
  const [dd, msStr] = dmsCode.split('°') ?? [];
  const [mm, sStr] = msStr?.split('′') ?? [];
  const [ss] = sStr?.split('″');

  const d = Number(dd) || 0;
  const m = (Number(mm) || 0) / 60;
  const s = (Number(ss) || 0) / 60 / 60;
  const degrees = d + m + s;
  if (degrees == 0) {
    return 0;
  }
  else {
    let res = +degrees.toFixed(decimal);
    // 南、西 为负数
    if (['W', 'w', 'S', 's'].includes(dmsCode.at(-1)!)) {
      res = -res;
    }
    return res;
  }
}

/**
 * 十进制经纬转 度分秒
 * @param decimal 秒保留几位小数
 * @returns [经度字符串,纬度字符串]
 */
export function degreesToDms(position: number[], decimal = 3): DMSCoordinate {
  const [longitude, latitude] = position;
  const x = dmsEncode(longitude, decimal);
  const y = dmsEncode(latitude, decimal);
  return [`${x}${longitude > 0 ? 'E' : 'W'}`, `${y}${latitude > 0 ? 'N' : 'S'}`];
}

/**
 * 度分秒 转经纬十进制
 * @param decimal 秒保留几位小数
 * @returns [longitude,latitude]
 */
export function dmsToDegrees(position: DMSCoordinate, decimal = 6) {
  const [x, y] = position;
  const longitude = dmsDecode(x, decimal);
  const latitude = dmsDecode(y, decimal);
  return [longitude, latitude];
}
