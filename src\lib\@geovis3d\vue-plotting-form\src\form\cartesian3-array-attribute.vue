<!-- Cartesian3ArrayAttribute -->
<script lang="ts" setup>
import type { Cartesian3SerializateJSON } from '@/lib/@geovis3d/plotting';

import { computed, ref } from 'vue';
import Cartesian3Attribute from './cartesian3-attribute.vue';
import { useShallowBinding } from './hooks';

defineOptions({ name: 'Cartesian3ArrayAttribute' });

const props = withDefaults(
  defineProps<{
    modelValue?: Cartesian3SerializateJSON[];
    label?: string;
    leading?: string;
  }>(),
  {
    leading: '坐标',
  },
);

const emit = defineEmits<{
  (event: 'update:modelValue', value?: Cartesian3SerializateJSON[]): void;
}>();

const model = ref<Cartesian3SerializateJSON[]>([]);

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = [...(value ?? [])]),
  rtl: value => emit('update:modelValue', [...(value ?? [])]),
});

const options = computed(() => {
  return (
    props?.modelValue?.map((item, index) => {
      return {
        label: `${props.leading}${index + 1}`,
        value: index,
      };
    }) ?? []
  );
});

const index = ref();
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="index" :options="options" :clearable="false" />
  </el-form-item>
  <Cartesian3Attribute v-if="model[index]" v-model="model[index]" label="" />
</template>
