import type { CesiumVectorEventCollection } from '../../event';

import * as Cesium from 'cesium';

import { GcBillboardCollection } from './billboard-collection';

/**
 * Cesium.ParticleSystem构造参数
 *
 * 见:
 * {@link Cesium.ParticleSystem}
 */
export type ParticleSystemConstructorOptions = NonNullable<
  ConstructorParameters<typeof Cesium.ParticleSystem>[0]
>;

/**
 * 粒子系统二次封装
 *
 * 新增 `event` 事件监听
 */
export class GcParticleSystem extends Cesium.ParticleSystem {
  constructor(options?: ParticleSystemConstructorOptions) {
    super(options);

    this._billboardCollection = new GcBillboardCollection({});
  }

  /**
   * Cesium.ParticleSystem渲染时如果_billboardCollection为空，则会自动设置一个`BillboardCollection`,
   * 这里我们提前设置，让其不会去自动初始化
   * @internal
   */
  private readonly _billboardCollection: GcBillboardCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._billboardCollection.event;
  }
}
