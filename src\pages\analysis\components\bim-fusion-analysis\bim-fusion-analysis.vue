<!-- BIM与地形融合匹配，城市精细模型数据和地下管网融合：BIM群+地形块+半透明 -->
<script lang="ts" setup>
import { toStaticFilePath } from '@/utils/resolve-path';
import { cartesianToCartographic, cartographicToCartesian } from '@x3d/all';
import { useCzPrimitive, useCzPrimitives, useCzViewer } from '@x3d/vue-hooks';
import { CesiumTerrainProvider } from 'cesium';

defineOptions({ name: 'BimFusionAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

const viewer = useCzViewer();
watchEffect(async (oncleanup) => {
  if (viewer.value.terrainProvider) {
    const terrainProvider = viewer.value.terrainProvider;
    oncleanup(() => {
      viewer.value.terrainProvider = terrainProvider;
    });
  }
  viewer.value.terrainProvider = await CesiumTerrainProvider.fromUrl(toStaticFilePath('/dixing-scope'));
});
const urls = [
  // '/3d-tileset/buildchejian/up/HXLC/tileset.json',
  // '/3d-tileset/buildchejian/down/HXTC/tileset.json',
  // '/3d-tileset/buildchejian/up/HXTC/tileset.json',
  // '/3d-tileset/buildchejian/up/XUCD/tileset.json',
  // '/3d-tileset/buildchejian/in/QSBF/tileset.json',
  // '/3d-tileset/buildchejian/in/XUCD/tileset.json',
  // '/3d-tileset/buildchejian/in/PWNC/tileset.json',
  // '/3d-tileset/buildchejian/in/HXLC/tileset.json',
  // '/3d-tileset/buildchejian/in/TSBZ/tileset.json',
  // '/3d-tileset/buildchejian/in/HXTC/tileset.json',
  // '/3d-tileset/buildchejian/in/ZHJY/tileset.json',
  // '/3d-tileset/buildchejian/in/SSBF/tileset.json',
  // '/3d-tileset/buildchejian/in/BPDS/tileset.json',
  // '/3d-tileset/buildchejian/down/PWNC/tileset.json',
  // '/3d-tileset/buildchejian/down/HXLC/tileset.json',
  // '/3d-tileset/buildchejian/down/TSBZ/tileset.json',
  // '/3d-tileset/buildchejian/down/ZHJY/tileset.json',
  // '/3d-tileset/buildchejian/down/BPDS/tileset.json',
  // '/3d-tileset/buildchejian/down/SSBF/tileset.json',
  // '/3d-tileset/buildchejian/down/QSTJ/tileset.json',
  '/3d-tileset/buildchejian/down/XUCD/tileset.json',
  // '/3d-tileset/buildchejian/middle/XUCD/tileset.json',
  // '/3d-tileset/buildchejian/middle/HXLC/tileset.json',
  // '/3d-tileset/buildchejian/middle/TSBZ/tileset.json',
  // '/3d-tileset/buildchejian/middle/HXTC/tileset.json',
  // '/3d-tileset/buildchejian/middle/ZHJY/tileset.json',
  // '/3d-tileset/buildchejian/middle/SSBF/tileset.json',
  // '/3d-tileset/buildchejian/middle/BPDS/tileset.json',
  // '/3d-tileset/buildchejian/middle/QSTJ/tileset.json',
  // '/3d-tileset/buildchejian/up/QSTJ/tileset.json',
  // '/3d-tileset/buildchejian/up/TSBZ/tileset.json',
  // '/3d-tileset/buildchejian/up/ZHJY/tileset.json',
  // '/3d-tileset/buildchejian/up/SSBF/tileset.json',
  // '/3d-tileset/buildchejian/up/BPDS/tileset.json',
  '/3d-tileset/builddown/DXGW/tileset.json',
  // '/3d-tileset/buildqiang/tileset.json',
  '/3d-tileset/buildtujian/MWS/tileset.json',

  '/3d-tileset/buildtujian/ZHBG/tileset.json',
  '/3d-tileset/buildtujian/YYBZ/tileset.json',
  // '/3d-tileset/buildmian/DM/tileset.json',
];

const primitives = computedAsync(async () => {
  const pormises = urls.map(url => Cesium.Cesium3DTileset.fromUrl(toStaticFilePath(`${url}`)));

  return await Promise.all(pormises);
});

useCzPrimitives(primitives);

function transform(tileset: Cesium.Cesium3DTileset) {
  const originPosition = tileset?.boundingSphere?.center.clone();
  const originModelMatrix = tileset?.modelMatrix?.clone();
  const cartographic = cartesianToCartographic(originPosition!.clone());
  cartographic.longitude = cartographic.longitude + Cesium.Math.toRadians(0.00056);
  cartographic.latitude = cartographic.latitude - Cesium.Math.toRadians(0.0001);
  const position = cartographicToCartesian(cartographic);
  // 平移
  const diff = Cesium.Cartesian3.subtract(position, originPosition!, new Cesium.Cartesian3());
  const matrix = Cesium.Matrix4.fromTranslation(diff, new Cesium.Matrix4());
  tileset.modelMatrix = Cesium.Matrix4.multiply(originModelMatrix!, matrix, new Cesium.Matrix4());
}

watch(primitives, () => {
  primitives.value?.forEach((primitive) => {
    transform(primitive);
  });
});

const isActive = ref(true);

const { primitive: dm } = useCzPrimitive(() => {
  return !isActive.value ? undefined : Cesium.Cesium3DTileset.fromUrl(toStaticFilePath(`/3d-tileset/buildmian/DM/tileset.json`));
});
watch(dm, (dm) => {
  dm && transform(dm);
});

watchEffect(() => {
  viewer.value.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(112.512456, 27.934915, 808),
    orientation: Cesium.HeadingPitchRoll.fromDegrees(159, -40, 0),
  });
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="BIM深度融合分析"
    class="w-400px"
    @close="emits('close')"
  >
    <div m="x-20px y-10px">
      <el-button type="primary" @click="isActive = !isActive">
        显示管网
      </el-button>
    </div>
  </drag-card>
</template>
