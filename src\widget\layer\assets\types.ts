export type RectangleConfig = [west?: number, south?: number, east?: number, north?: number];
export type TileMatrixSetIDConfig =
/**
 * 墨卡托投影
 */
  'EPSG:3857' |
  /**
   * 经纬度投影
   */
  'EPSG:4326' |

   /**
    * 大地2000投影
    */
  'EPSG:4490' |
/**
 * 百度地图坐标系投影
 */
  'BD09' |
  /**
   * 火星坐标系投影
   */
  'GCJ02';

export interface WMSConfig {
  url?: string;
  layer?: string;
  // parameters?: any;
  // getFeatureInfoParameters?: any;
  // enablePickFeatures?: boolean;
  tileMatrixSetID?: TileMatrixSetIDConfig;
  // tileWidth?: number;
  // tileHeight?: number;
  titleSize?: number;
  minimumLevel?: number;
  maximumLevel?: number;
  format?: string;
}

export interface WMTSConfig {
  url?: string;
  format?: string;
  layer?: string;
  style?: string;
  // tileMatrixSetID: string;
  dimensions?: any;
  // tileWidth?: number;
  // tileHeight?: number;
  titleSize?: number;
  tileMatrixSetID?: TileMatrixSetIDConfig;
  minimumLevel?: number;
  maximumLevel?: number;
}

export interface TMSConfig {
  url?: string;
  // fileExtension?: string;
  minimumLevel?: number;
  maximumLevel?: number;
  tileMatrixSetID?: TileMatrixSetIDConfig;
  // tileWidth?: number;
  // tileHeight?: number;
  titleSize?: number;
  format?: string;
  // flipXY?: boolean;
}

export interface MVTConfig {
  url?: string;
  layer?: string;
  titleSize?: number;
  tileMatrixSetID?: TileMatrixSetIDConfig;
  minimumLevel?: number;
  maximumLevel?: number;
  lineColor?: string;
  fillColor?: string;
  fillOutlineColor?: string;

}

export interface UrlTemplateConfig {
  url?: string;
  minimumLevel?: number;
  maximumLevel?: number;
  tileMatrixSetID?: TileMatrixSetIDConfig;
  // tileWidth?: number;
  // tileHeight?: number;
  titleSize?: number;
}

export interface TerrainConfig {
  url?: string;
  requestVertexNormals?: boolean;
  requestWaterMask?: boolean;
  requestMetadata?: boolean;
}

export interface TileSetConfig {
  url?: string;
  /**
   * 单体化模型的url
   */
  singulatedUrl?: string;
}

export interface WFSConfig {
  url?: string;
  typeName?: string;
  featurePrefix?: string;
  featureNS?: string;
}

/**
 * 相机配置
 */
export interface CameraConfig {
  longitude?: number;
  latitude?: number;
  height?: number;
  heading?: number;
  pitch?: number;
  roll?: number;
}

/**
 * 图例配置
 */
export interface LegendConfig {
  visible?: boolean;
  list: {
    symbol?: string;
    color?: string;
    name?: string;
  }[];
}

/**
 * Egis验证配置
 */
export interface EgisConfig {
  username?: string;
  password?: string;
}

/**
 * 图层配置
 */
export interface LayerConfig {
  name?: string;
  defaultVisible?: boolean;
  type?: string;
  layer?: WMSConfig | WMTSConfig | TMSConfig | UrlTemplateConfig | TerrainConfig | TileSetConfig;
  rectangle?: RectangleConfig;
  egis?: EgisConfig;
  camera?: CameraConfig;
  legend?: LegendConfig;
  wfs?: WFSConfig;
}
