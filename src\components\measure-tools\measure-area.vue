<!-- 面积测量 -->
<script lang="ts" setup>
import { area, cartesianToWgs84, CzPlotEntity } from '@x3d/all';
import { useCzEntities } from '@x3d/vue-hooks';

defineOptions({ name: 'MeasureArea' });
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    isActive?: boolean;
  }>(),
  { modelValue: true },
);
const show = useVModel(props, 'modelValue');
const unit = ref(1);
const result = ref(0);
const plotEntity = shallowRef<CzPlotEntity>();
function initEntity() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      manualTerminate: entity => entity.record.positions.getLength() > 2,
      control: { visible: true },
      interval: { visible: 'loop' },
      effect(entity) {
        entity.polygon ??= new Cesium.PolygonGraphics({
          material: Cesium.Color.fromCssColorString('#FCC650').withAlpha(0.3),
        });
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);

        if (coords.length >= 3) {
          const hierarchy = new Cesium.PolygonHierarchy(positions);
          entity.polygon.hierarchy = new Cesium.CallbackProperty(() => hierarchy, false);
          entity.position = new Cesium.ConstantPositionProperty(record.positions.getCenter()!);
        }
        else {
          entity.polygon.hierarchy = undefined;
          entity.position = undefined;
        }

        positions.length > 2
        && area(positions).then((e) => {
          result.value = e;
          let text: string = '';
          if (e / 1000 / 1000 > 10) {
            text = `${(e / 1000 / 1000).toFixed(2)}km²`;
          }
          else {
            text = `${(+e).toFixed(2)}m²`;
          }
          entity.label = new Cesium.LabelGraphics({
            text,
            font: '16pt Source Han Sans CN',
            pixelOffset: new Cesium.Cartesian2(0, -20),
          });
        });
      },

    },
  });
}
useCzEntities(() => [plotEntity.value]);

initEntity();
defineExpose({ initEntity });
</script>

<template>
  <basic-card
    v-show="show"
    show-close
    title="量测设置"
    @close="show = false"
  >
    <div class="px-24px py-16px">
      面积测量
      <el-select v-model="unit" mt="8px">
        <el-option label=" 平方米（㎡）" :value="1" />
        <el-option label=" 平方公里（k㎡）" :value="1000000" />
        <el-option label=" 万平方公里（万k㎡）" :value="10000000000" />
      </el-select>
      <div text="#FCC650 14px" mt="12px" lh="24px">
        提示：左键在球上点击选点，单击鼠标右键结束 （至少选取三个点）
      </div>
    </div>
    <template #footer>
      <div flex="~ justify-between" class="my--8px w-full font-blod-18px">
        面积：
        <span> {{ (result / unit).toFixed(2) }}{{ unit === 1 ? "㎡" : unit === 1000000 ? "k㎡" : "万k㎡" }} </span>
      </div>
    </template>
  </basic-card>
</template>

<style lang="scss" scoped>
.el-select {
  :deep() &__wrapper {
    min-height: 44px !important;
  }
}
</style>
