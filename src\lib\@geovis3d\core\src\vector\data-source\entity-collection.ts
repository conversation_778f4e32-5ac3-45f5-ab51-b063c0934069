import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.EntityCollection} 拓展用法与 {@link Cesium.EntityCollection} 基本一致。
 *
 *
 * `GcEntityCollection.event`鼠标事件监听
 */
export class GcEntityCollection extends Cesium.EntityCollection {
  constructor(owner?: Cesium.DataSource | Cesium.CompositeEntityCollection) {
    super(owner);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
