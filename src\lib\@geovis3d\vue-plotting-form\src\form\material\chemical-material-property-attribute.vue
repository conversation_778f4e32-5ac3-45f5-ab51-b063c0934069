<!-- OIL_MATERIAL_PROPERTY_ATTRIBUTE 属性编辑 -->
<script lang="ts" setup>
import { useShallowBinding } from '../hooks';

defineOptions({ name: 'ChemicalMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: any;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: any): void;
}>();

const model = ref<any>();

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="危化品" /> -->
  <div />
</template>
