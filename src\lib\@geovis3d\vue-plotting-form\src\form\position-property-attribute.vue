<!-- PositionPropertyAttribute -->
<script lang="ts" setup>
import type { PositionPropertySerializateJSON } from '@/lib/@geovis3d/plotting';
import { useShallowBinding } from './hooks';

import JulianDateAttribute from './julian-date-attribute.vue';
import NumberArrayAttribute from './number-array-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ReferenceFrameAttribute from './reference-frame-attribute.vue';

defineOptions({ name: 'PositionPropertyAttribute' });

const props = defineProps<{
  modelValue?: PositionPropertySerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: PositionPropertySerializateJSON): void;
}>();

const model = ref<PositionPropertySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <ReferenceFrameAttribute v-model="model.referenceFrame" label="referenceFrame" />
  <NumberAttribute v-model="model.numberOfDerivatives" label="numberOfDerivatives" />
  <NumberArrayAttribute v-model="model.cartesian" label="cartesian" />
  cartographicDegrees?: WGS84;
  <JulianDateAttribute v-model="model.epoch" label="epoch" />
  <NumberAttribute v-model="model.nextTime" label="nextTime" />
  <NumberAttribute v-model="model.previousTime" label="previousTime" />
</template>
