<!-- PinBuilderAttribute -->
<script lang="ts" setup>
import type { PinBuilderSerializateJSON } from '@/lib/@geovis3d/plotting';
import { useShallowBinding } from './hooks';

defineOptions({ name: 'PinBuilderAttribute' });

const props = defineProps<{
  modelValue?: PinBuilderSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: PinBuilderSerializateJSON): void;
}>();

const model = ref<PinBuilderSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div class="pin-builder-attribute">
    PinBuilderAttribute
  </div>
</template>
