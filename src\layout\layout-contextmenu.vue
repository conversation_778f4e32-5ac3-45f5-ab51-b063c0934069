<script lang="ts" setup>
import type { Cartesian3 } from 'cesium';
import { vOnClickOutside } from '@vueuse/components';
import { canvasCoordToCartesian, cartesianToWgs84 } from '@x3d/all';
import { useCzEventListener, useCzScreenSpaceAction, useCzViewer } from '@x3d/vue-hooks';
import { ScreenSpaceEventHandler, ScreenSpaceEventType } from 'cesium';
import { onUnmounted, ref } from 'vue';

const viewer = useCzViewer();
const position = shallowRef<Cartesian3>();
const wgs84 = shallowRef({ longitude: 0, latitude: 0 });

watchEffect(() => {
  if (position.value) {
    const [longitude, latitude] = cartesianToWgs84(position.value);
    wgs84.value = { longitude, latitude };
  }
});

useCzScreenSpaceAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK, (ctx) => {
  const p = canvasCoordToCartesian(ctx.position, viewer.value.scene!);
  position.value = p;
});

function close() {
  position.value = undefined;
}

useCzEventListener(() => viewer.value.scene.camera.changed, () => close());
useCzEventListener(() => viewer.value.scene.camera.moveStart, () => close());

const startSurround = ref(false);
const surroundName = ref('环绕此位置');
const showPopup = ref(false);

const { copy } = useClipboard({ source: () => JSON.stringify(wgs84.value) });

onUnmounted(() => {
  if (viewer.value) {
    const handler = new ScreenSpaceEventHandler(viewer.value.scene.canvas);
    handler.removeInputAction(ScreenSpaceEventType.RIGHT_CLICK);
    handler.removeInputAction(ScreenSpaceEventType.LEFT_CLICK);
    handler.removeInputAction(ScreenSpaceEventType.WHEEL);
    handler.removeInputAction(ScreenSpaceEventType.LEFT_DOWN);
  }
});
// 复制坐标
function copyPosition() {
  close();
  copy();
  ElMessage.success('复制完成');
}

// 设为中心点
function flyToCenterPosition() {
  close();
  viewer.value.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(Number(wgs84.value.longitude), Number(wgs84.value.latitude), 7e5),
    duration: 1,
  });
  showPopup.value = false;
}
// 飞往此处
function flyToPosition() {
  viewer.value.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(Number(wgs84.value.longitude), Number(wgs84.value.latitude), 15000.0),
    duration: 2,
  });
  close();
}
// 绕点飞行
function flyToPoint() {
  close();
  if (!startSurround.value) {
    surroundName.value = '取消环绕';
    startSurround.value = !startSurround.value;
    viewer.value.clock.stopTime = Cesium.JulianDate.addDays(Cesium.JulianDate.now(), 999999, new Cesium.JulianDate());
    const options = {
      lng: Number(wgs84.value.longitude),
      lat: Number(wgs84.value.latitude),
      height: 15.8,
      heading: 0.0,
      pitch: 0.0,
      roll: 0.0,
    };
    const position = Cesium.Cartesian3.fromDegrees(options.lng, options.lat, options.height);
    // 相机看点的角度，如果大于0那么则是从地底往上看，所以要为负值，这里取-30度
    // 给定飞行一周所需时间，比如10s, 那么每秒转动度数
    const angle = 360 / 30;
    // 给定相机距离点多少距离飞行，这里取值为5000m
    const distance = 52000;
    const startTime = Cesium.JulianDate.fromDate(new Date());
    // var stopTime = Cesium.JulianDate.addSeconds(startTime, 10, new Cesium.JulianDate());
    viewer.value.clock.startTime = startTime.clone(); // 开始时间
    // viewer.clock.stopTime = stopTime.clone();     // 结速时间
    viewer.value.clock.currentTime = startTime.clone(); // 当前时间
    viewer.value.clock.clockRange = Cesium.ClockRange.CLAMPED; // 行为方式
    viewer.value.clock.clockStep = Cesium.ClockStep.SYSTEM_CLOCK; // 时钟设置为当前系统时间; 忽略所有其他设置。
    // 相机的当前heading
    const initialHeading = viewer.value.camera.heading;
    // var Exection = ref()
    viewer.value.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(options.lng, options.lat, distance),
      duration: 1.2,
      orientation: {
        heading: options.heading,
        pitch: viewer.value.camera.pitch,
      },
      complete() {
        const Exection = function TimeExecution() {
          // 当前已经过去的时间，单位s
          const delTime = Cesium.JulianDate.secondsDifference(viewer.value.clock.currentTime, viewer.value.clock.startTime);
          const heading = Cesium.Math.toRadians(delTime * angle) + initialHeading;
          viewer.value.scene.camera.setView({
            destination: position, // 点的坐标
            orientation: {
              heading,
              pitch: viewer.value.camera.pitch,
            },
          });
          viewer.value.scene.camera.moveBackward(distance);
          if (Cesium.JulianDate.compare(viewer.value.clock.currentTime, viewer.value.clock.stopTime) >= 0) {
            viewer.value.clock.onTick.removeEventListener(Exection);
          }
        };
        viewer.value.clock.onTick.addEventListener(Exection);
      },
    });
  }
  else {
    viewer.value.clock.stopTime = viewer.value.clock.startTime;
    surroundName.value = '环绕此位置';
    startSurround.value = !startSurround.value;
    showPopup.value = false;
  }
}
</script>

<template>
  <!-- 弹窗 -->
  <teleport v-if="position" :to="viewer?.container?.parentElement">
    <located-popper :position="position">
      <div
        id="layout-contextmenu"
        v-on-click-outside="() => close()"
        flex="~ col items-start"
        gap="18px"
        p="x-18px y-18px"
        bg="[var(--el-bg-color)]"
        rd="4px"
        un-children="m-0!"
      >
        <el-button link @click="copyPosition">
          复制坐标
        </el-button>
        <el-divider m="0px!" />
        <el-button link @click="flyToPosition">
          飞到此处
        </el-button>
        <el-button link @click="flyToCenterPosition">
          设为中心
        </el-button>
        <el-button link @click="flyToPoint">
          {{ surroundName }}
        </el-button>
      </div>
    </located-popper>
  </teleport>
</template>
