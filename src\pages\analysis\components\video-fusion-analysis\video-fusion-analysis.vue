<!-- 视频融合 -->
<script lang="ts" setup>
import { toStaticFilePath } from '@/utils/resolve-path';
import { CzPlotScheme, MaskBlurMaterialProperty, PRESET_PLOT_SCHEMES_OPTIONS } from '@x3d/all';
import { useCzEntity, useCzPrimitive, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'VideoFusionAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});

const viewer = useCzViewer();

const { primitive } = useCzPrimitive(
  () => Cesium.Cesium3DTileset.fromUrl(toStaticFilePath('/3d-tileset/qx-hfdxy/tileset.json')),
  {},
);

watchEffect(() => {
  primitive.value && viewer.value.flyTo(primitive.value);
});
const videoData = shallowRef();
const videoRef = templateRef('videoRef');
const { entity } = useCzEntity(() => {
  return new Cesium.Entity({
    rectangle: {
      coordinates: Cesium.Rectangle.fromDegrees(117.203928, 31.841866, 117.205272, 31.842750),
      material: new MaskBlurMaterialProperty({
        image: new Cesium.CallbackProperty(() => videoData.value, false),
      }),
      rotation: Cesium.Math.toRadians(-45),
      stRotation: Cesium.Math.toRadians(-45),
      classificationType: Cesium.ClassificationType.CESIUM_3D_TILE,
    },

  });
});
useIntervalFn(() => {
  if (entity.value && videoRef.value) {
    const canvas = document.createElement('canvas');
    const { videoHeight, videoWidth, width, height } = videoRef.value;
    const w = videoWidth || width;
    const h = videoHeight || height;
    canvas.width = w;
    canvas.height = h;
    canvas.getContext('2d')?.drawImage(videoRef.value, 0, 0, w, h);
    canvas.toBlob(blob => (videoData.value = URL.createObjectURL(blob!)));
  }
}, 32);
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="视频融合分析"
    class="w-400px"
    @close="emits('close')"
  >
    <!-- <el-divider>绘制要素</el-divider>
    <div m="x-20px y-10px">
      <el-button type="primary" @click="plotStart()">
        绘制起点
      </el-button>
      <el-button type="primary" @click="plotEnd()">
        绘制终点
      </el-button>
    </div>
    <el-divider>绘制障碍</el-divider> -->
    <div m="x-20px y-10px">
      <!-- <el-button type="primary" @click="plotObstacles('Point')">
        绘制障碍点
      </el-button>
      <el-button type="primary" @click="plotObstacles('Polyline')">
        绘制障碍线
      </el-button> -->
      <!-- <el-button type="primary" @click="plotObstacles()">
        绘制障碍面
      </el-button> -->
    </div>
    <video
      ref="videoRef"
      :src="toStaticFilePath('/lukou.mp4')"
      autoplay
      controls="false"
      loop
      muted
    />
  </drag-card>
</template>
