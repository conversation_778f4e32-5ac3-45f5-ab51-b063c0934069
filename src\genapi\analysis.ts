/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by @xiankq/openapi-typescript-expand
// Power by openapi-typescript

import {analysisRequest} from "./request";



/**
 * @tag 农业灾害
 * @summary 获取统计数据
 * @url /analysis/agri/getStatData
 * @method get
 * @description 获取统计数据
 */

export module AnalysisAgriGetStatDataUsingGet {
  export type Operation = paths['/analysis/agri/getStatData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 农业灾害
 * @summary 获取统计数据
 * @url /analysis/agri/getStatData
 * @method get
 * @description 获取统计数据
 */

export function analysisAgriGetStatDataUsingGet(options:AnalysisAgriGetStatDataUsingGet.Options):Promise<AnalysisAgriGetStatDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/agri/getStatData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 政务基底接口
 * @summary 获取政务基底数据
 * @url /analysis/zwjd/getChInterfaces
 * @method get
 * @description 
 */

export module AnalysisZwjdGetChInterfacesUsingGet {
  export type Operation = paths['/analysis/zwjd/getChInterfaces']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 政务基底接口
 * @summary 获取政务基底数据
 * @url /analysis/zwjd/getChInterfaces
 * @method get
 * @description 
 */

export function analysisZwjdGetChInterfacesUsingGet(options:AnalysisZwjdGetChInterfacesUsingGet.Options):Promise<AnalysisZwjdGetChInterfacesUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/zwjd/getChInterfaces',
    method:'get',
    ...options,
  });
}

/**
 * @tag 视频分组接口
 * @summary 获取视图关联的视频信息
 * @url /analysis/deviceGroup/getDevices/{id}
 * @method get
 * @description 获取视图关联的视频信息
 */

export module AnalysisDeviceGroupGetDevicesIdUsingGet {
  export type Operation = paths['/analysis/deviceGroup/getDevices/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 视频分组接口
 * @summary 获取视图关联的视频信息
 * @url /analysis/deviceGroup/getDevices/{id}
 * @method get
 * @description 获取视图关联的视频信息
 */

export function analysisDeviceGroupGetDevicesIdUsingGet(options:AnalysisDeviceGroupGetDevicesIdUsingGet.Options):Promise<AnalysisDeviceGroupGetDevicesIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/deviceGroup/getDevices/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 视频分组接口
 * @summary 分组树型列表
 * @url /analysis/deviceGroup/getList
 * @method get
 * @description 
 */

export module AnalysisDeviceGroupGetListUsingGet {
  export type Operation = paths['/analysis/deviceGroup/getList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 视频分组接口
 * @summary 分组树型列表
 * @url /analysis/deviceGroup/getList
 * @method get
 * @description 
 */

export function analysisDeviceGroupGetListUsingGet(options:AnalysisDeviceGroupGetListUsingGet.Options):Promise<AnalysisDeviceGroupGetListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/deviceGroup/getList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 视频分组接口
 * @summary 删除分组
 * @url /analysis/deviceGroup/remove/{id}
 * @method get
 * @description 删除分组
 */

export module AnalysisDeviceGroupRemoveIdUsingGet {
  export type Operation = paths['/analysis/deviceGroup/remove/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 视频分组接口
 * @summary 删除分组
 * @url /analysis/deviceGroup/remove/{id}
 * @method get
 * @description 删除分组
 */

export function analysisDeviceGroupRemoveIdUsingGet(options:AnalysisDeviceGroupRemoveIdUsingGet.Options):Promise<AnalysisDeviceGroupRemoveIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/deviceGroup/remove/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 视频分组接口
 * @summary 创建分组
 * @url /analysis/deviceGroup/save
 * @method post
 * @description 创建分组
 */

export module AnalysisDeviceGroupSaveUsingPost {
  export type Operation = paths['/analysis/deviceGroup/save']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 视频分组接口
 * @summary 创建分组
 * @url /analysis/deviceGroup/save
 * @method post
 * @description 创建分组
 */

export function analysisDeviceGroupSaveUsingPost(options:AnalysisDeviceGroupSaveUsingPost.Options):Promise<AnalysisDeviceGroupSaveUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/deviceGroup/save',
    method:'post',
    ...options,
  });
}

/**
 * @tag 视频分组接口
 * @summary 创建视图
 * @url /analysis/deviceGroup/saveView
 * @method post
 * @description 创建视图
 */

export module AnalysisDeviceGroupSaveViewUsingPost {
  export type Operation = paths['/analysis/deviceGroup/saveView']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 视频分组接口
 * @summary 创建视图
 * @url /analysis/deviceGroup/saveView
 * @method post
 * @description 创建视图
 */

export function analysisDeviceGroupSaveViewUsingPost(options:AnalysisDeviceGroupSaveViewUsingPost.Options):Promise<AnalysisDeviceGroupSaveViewUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/deviceGroup/saveView',
    method:'post',
    ...options,
  });
}

/**
 * @tag 视频分组接口
 * @summary 更新分组或视图
 * @url /analysis/deviceGroup/update
 * @method post
 * @description 更新分组或视图,更新视图时需要传parentId
 */

export module AnalysisDeviceGroupUpdateUsingPost {
  export type Operation = paths['/analysis/deviceGroup/update']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 视频分组接口
 * @summary 更新分组或视图
 * @url /analysis/deviceGroup/update
 * @method post
 * @description 更新分组或视图,更新视图时需要传parentId
 */

export function analysisDeviceGroupUpdateUsingPost(options:AnalysisDeviceGroupUpdateUsingPost.Options):Promise<AnalysisDeviceGroupUpdateUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/deviceGroup/update',
    method:'post',
    ...options,
  });
}

/**
 * @tag 文件处理接口
 * @summary 指挥对标初设-导出风险隐患文件
 * @url /analysis/file/export
 * @method get
 * @description 导出风险隐患文件
 */

export module AnalysisFileExportUsingGet {
  export type Operation = paths['/analysis/file/export']['get'];
  export type Result = any;
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 文件处理接口
 * @summary 指挥对标初设-导出风险隐患文件
 * @url /analysis/file/export
 * @method get
 * @description 导出风险隐患文件
 */

export function analysisFileExportUsingGet(options:AnalysisFileExportUsingGet.Options):Promise<AnalysisFileExportUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/file/export',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 获取近24小时所有站点火警预警总记录
 * @url /analysis/forest/get24HourFireCount
 * @method get
 * @description 
 */

export module AnalysisForestGet24HourFireCountUsingGet {
  export type Operation = paths['/analysis/forest/get24HourFireCount']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 获取近24小时所有站点火警预警总记录
 * @url /analysis/forest/get24HourFireCount
 * @method get
 * @description 
 */

export function analysisForestGet24HourFireCountUsingGet(options:AnalysisForestGet24HourFireCountUsingGet.Options):Promise<AnalysisForestGet24HourFireCountUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/get24HourFireCount',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 获取所有站点火警预警数据
 * @url /analysis/forest/getAllFireData
 * @method get
 * @description 
 */

export module AnalysisForestGetAllFireDataUsingGet {
  export type Operation = paths['/analysis/forest/getAllFireData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 获取所有站点火警预警数据
 * @url /analysis/forest/getAllFireData
 * @method get
 * @description 
 */

export function analysisForestGetAllFireDataUsingGet(options:AnalysisForestGetAllFireDataUsingGet.Options):Promise<AnalysisForestGetAllFireDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getAllFireData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 根据id获取站点火警预警数据
 * @url /analysis/forest/getFireDataById
 * @method get
 * @description 
 */

export module AnalysisForestGetFireDataByIdUsingGet {
  export type Operation = paths['/analysis/forest/getFireDataById']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 根据id获取站点火警预警数据
 * @url /analysis/forest/getFireDataById
 * @method get
 * @description 
 */

export function analysisForestGetFireDataByIdUsingGet(options:AnalysisForestGetFireDataByIdUsingGet.Options):Promise<AnalysisForestGetFireDataByIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getFireDataById',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 获取站点火警预警数据(精确到时间)
 * @url /analysis/forest/getFireDataByTime
 * @method get
 * @description 
 */

export module AnalysisForestGetFireDataByTimeUsingGet {
  export type Operation = paths['/analysis/forest/getFireDataByTime']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 获取站点火警预警数据(精确到时间)
 * @url /analysis/forest/getFireDataByTime
 * @method get
 * @description 
 */

export function analysisForestGetFireDataByTimeUsingGet(options:AnalysisForestGetFireDataByTimeUsingGet.Options):Promise<AnalysisForestGetFireDataByTimeUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getFireDataByTime',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 监测预警，获取火警明细
 * @url /analysis/forest/getFireDetail
 * @method get
 * @description 
 */

export module AnalysisForestGetFireDetailUsingGet {
  export type Operation = paths['/analysis/forest/getFireDetail']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 监测预警，获取火警明细
 * @url /analysis/forest/getFireDetail
 * @method get
 * @description 
 */

export function analysisForestGetFireDetailUsingGet(options:AnalysisForestGetFireDetailUsingGet.Options):Promise<AnalysisForestGetFireDetailUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getFireDetail',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 监测预警，获取站点火警列表
 * @url /analysis/forest/getFireList
 * @method get
 * @description 
 */

export module AnalysisForestGetFireListUsingGet {
  export type Operation = paths['/analysis/forest/getFireList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 监测预警，获取站点火警列表
 * @url /analysis/forest/getFireList
 * @method get
 * @description 
 */

export function analysisForestGetFireListUsingGet(options:AnalysisForestGetFireListUsingGet.Options):Promise<AnalysisForestGetFireListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getFireList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 获取站点列表
 * @url /analysis/forest/getStationList
 * @method get
 * @description 
 */

export module AnalysisForestGetStationListUsingGet {
  export type Operation = paths['/analysis/forest/getStationList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 获取站点列表
 * @url /analysis/forest/getStationList
 * @method get
 * @description 
 */

export function analysisForestGetStationListUsingGet(options:AnalysisForestGetStationListUsingGet.Options):Promise<AnalysisForestGetStationListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getStationList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 首页视频监控获取站点列表
 * @url /analysis/forest/getStationVideoList
 * @method get
 * @description 
 */

export module AnalysisForestGetStationVideoListUsingGet {
  export type Operation = paths['/analysis/forest/getStationVideoList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 首页视频监控获取站点列表
 * @url /analysis/forest/getStationVideoList
 * @method get
 * @description 
 */

export function analysisForestGetStationVideoListUsingGet(options:AnalysisForestGetStationVideoListUsingGet.Options):Promise<AnalysisForestGetStationVideoListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getStationVideoList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 获取站点气象数据
 * @url /analysis/forest/getWeatherData
 * @method get
 * @description 
 */

export module AnalysisForestGetWeatherDataUsingGet {
  export type Operation = paths['/analysis/forest/getWeatherData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 获取站点气象数据
 * @url /analysis/forest/getWeatherData
 * @method get
 * @description 
 */

export function analysisForestGetWeatherDataUsingGet(options:AnalysisForestGetWeatherDataUsingGet.Options):Promise<AnalysisForestGetWeatherDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/getWeatherData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 火点下载图片
 * @url /analysis/forest/sprhpt/download
 * @method get
 * @description 
 */

export module AnalysisForestSprhptDownloadUsingGet {
  export type Operation = paths['/analysis/forest/sprhpt/download']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 火点下载图片
 * @url /analysis/forest/sprhpt/download
 * @method get
 * @description 
 */

export function analysisForestSprhptDownloadUsingGet(options:AnalysisForestSprhptDownloadUsingGet.Options):Promise<AnalysisForestSprhptDownloadUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/sprhpt/download',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 获取海康视频融合平台事件列表
 * @url /analysis/forest/sprhpt/getEvents
 * @method get
 * @description 
 */

export module AnalysisForestSprhptGetEventsUsingGet {
  export type Operation = paths['/analysis/forest/sprhpt/getEvents']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 获取海康视频融合平台事件列表
 * @url /analysis/forest/sprhpt/getEvents
 * @method get
 * @description 
 */

export function analysisForestSprhptGetEventsUsingGet(options:AnalysisForestSprhptGetEventsUsingGet.Options):Promise<AnalysisForestSprhptGetEventsUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/sprhpt/getEvents',
    method:'get',
    ...options,
  });
}

/**
 * @tag 森林防火感知接口
 * @summary 视频融合平台下载全部图片
 * @url /analysis/forest/sprhpt/saveFile
 * @method get
 * @description 
 */

export module AnalysisForestSprhptSaveFileUsingGet {
  export type Operation = paths['/analysis/forest/sprhpt/saveFile']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 森林防火感知接口
 * @summary 视频融合平台下载全部图片
 * @url /analysis/forest/sprhpt/saveFile
 * @method get
 * @description 
 */

export function analysisForestSprhptSaveFileUsingGet(options:AnalysisForestSprhptSaveFileUsingGet.Options):Promise<AnalysisForestSprhptSaveFileUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forest/sprhpt/saveFile',
    method:'get',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method get
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingGet {
  export type Operation = paths['/analysis/forward/proxy']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method get
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingGet(options:AnalysisForwardProxyUsingGet.Options):Promise<AnalysisForwardProxyUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'get',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method put
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingPut {
  export type Operation = paths['/analysis/forward/proxy']['put'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method put
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingPut(options:AnalysisForwardProxyUsingPut.Options):Promise<AnalysisForwardProxyUsingPut.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'put',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method post
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingPost {
  export type Operation = paths['/analysis/forward/proxy']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method post
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingPost(options:AnalysisForwardProxyUsingPost.Options):Promise<AnalysisForwardProxyUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'post',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method delete
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingDelete {
  export type Operation = paths['/analysis/forward/proxy']['delete'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method delete
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingDelete(options:AnalysisForwardProxyUsingDelete.Options):Promise<AnalysisForwardProxyUsingDelete.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'delete',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method options
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingOptions {
  export type Operation = paths['/analysis/forward/proxy']['options'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method options
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingOptions(options:AnalysisForwardProxyUsingOptions.Options):Promise<AnalysisForwardProxyUsingOptions.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'options',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method head
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingHead {
  export type Operation = paths['/analysis/forward/proxy']['head'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method head
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingHead(options:AnalysisForwardProxyUsingHead.Options):Promise<AnalysisForwardProxyUsingHead.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'head',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method patch
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingPatch {
  export type Operation = paths['/analysis/forward/proxy']['patch'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method patch
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingPatch(options:AnalysisForwardProxyUsingPatch.Options):Promise<AnalysisForwardProxyUsingPatch.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'patch',
    ...options,
  });
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method trace
 * @description 代理转发接口1
 */

export module AnalysisForwardProxyUsingTrace {
  export type Operation = paths['/analysis/forward/proxy']['trace'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
    params: Query;
  };
}

/**
 * @tag 代理转发接口
 * @summary 代理转发接口1
 * @url /analysis/forward/proxy
 * @method trace
 * @description 代理转发接口1
 */

export function analysisForwardProxyUsingTrace(options:AnalysisForwardProxyUsingTrace.Options):Promise<AnalysisForwardProxyUsingTrace.Result> {
  return analysisRequest({
    url:'/analysis/forward/proxy',
    method:'trace',
    ...options,
  });
}

/**
 * @tag 电力接口
 * @summary 获取复工复产数据
 * @url /analysis/power/getFgfcData
 * @method post
 * @description 
 */

export module AnalysisPowerGetFgfcDataUsingPost {
  export type Operation = paths['/analysis/power/getFgfcData']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 电力接口
 * @summary 获取复工复产数据
 * @url /analysis/power/getFgfcData
 * @method post
 * @description 
 */

export function analysisPowerGetFgfcDataUsingPost(options:AnalysisPowerGetFgfcDataUsingPost.Options):Promise<AnalysisPowerGetFgfcDataUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/power/getFgfcData',
    method:'post',
    ...options,
  });
}

/**
 * @tag 电力接口
 * @summary 获取节日列表
 * @url /analysis/power/getHolidayList
 * @method get
 * @description 
 */

export module AnalysisPowerGetHolidayListUsingGet {
  export type Operation = paths['/analysis/power/getHolidayList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 电力接口
 * @summary 获取节日列表
 * @url /analysis/power/getHolidayList
 * @method get
 * @description 
 */

export function analysisPowerGetHolidayListUsingGet(options:AnalysisPowerGetHolidayListUsingGet.Options):Promise<AnalysisPowerGetHolidayListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/power/getHolidayList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 电力接口
 * @summary 获取生产状态监测数据
 * @url /analysis/power/getScztjcData
 * @method post
 * @description 
 */

export module AnalysisPowerGetScztjcDataUsingPost {
  export type Operation = paths['/analysis/power/getScztjcData']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 电力接口
 * @summary 获取生产状态监测数据
 * @url /analysis/power/getScztjcData
 * @method post
 * @description 
 */

export function analysisPowerGetScztjcDataUsingPost(options:AnalysisPowerGetScztjcDataUsingPost.Options):Promise<AnalysisPowerGetScztjcDataUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/power/getScztjcData',
    method:'post',
    ...options,
  });
}

/**
 * @tag 电力接口
 * @summary 获取统计数据
 * @url /analysis/power/getStatData
 * @method get
 * @description 
 */

export module AnalysisPowerGetStatDataUsingGet {
  export type Operation = paths['/analysis/power/getStatData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 电力接口
 * @summary 获取统计数据
 * @url /analysis/power/getStatData
 * @method get
 * @description 
 */

export function analysisPowerGetStatDataUsingGet(options:AnalysisPowerGetStatDataUsingGet.Options):Promise<AnalysisPowerGetStatDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/power/getStatData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 电力接口
 * @summary 获取日用电量统计数据
 * @url /analysis/power/getYdtjData
 * @method post
 * @description 
 */

export module AnalysisPowerGetYdtjDataUsingPost {
  export type Operation = paths['/analysis/power/getYdtjData']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 电力接口
 * @summary 获取日用电量统计数据
 * @url /analysis/power/getYdtjData
 * @method post
 * @description 
 */

export function analysisPowerGetYdtjDataUsingPost(options:AnalysisPowerGetYdtjDataUsingPost.Options):Promise<AnalysisPowerGetYdtjDataUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/power/getYdtjData',
    method:'post',
    ...options,
  });
}

/**
 * @tag 水库信息接口
 * @summary 获取水库历史蓄水量比较信息
 * @url /analysis/reservoir/compareReservoirStorage
 * @method get
 * @description 
 */

export module AnalysisReservoirCompareReservoirStorageUsingGet {
  export type Operation = paths['/analysis/reservoir/compareReservoirStorage']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 水库信息接口
 * @summary 获取水库历史蓄水量比较信息
 * @url /analysis/reservoir/compareReservoirStorage
 * @method get
 * @description 
 */

export function analysisReservoirCompareReservoirStorageUsingGet(options:AnalysisReservoirCompareReservoirStorageUsingGet.Options):Promise<AnalysisReservoirCompareReservoirStorageUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/reservoir/compareReservoirStorage',
    method:'get',
    ...options,
  });
}

/**
 * @tag 水库信息接口
 * @summary 获取水库基本信息、下游信息
 * @url /analysis/reservoir/getBaseInfo/{reservoirId}
 * @method get
 * @description 
 */

export module AnalysisReservoirGetBaseInfoReservoirIdUsingGet {
  export type Operation = paths['/analysis/reservoir/getBaseInfo/{reservoirId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 水库信息接口
 * @summary 获取水库基本信息、下游信息
 * @url /analysis/reservoir/getBaseInfo/{reservoirId}
 * @method get
 * @description 
 */

export function analysisReservoirGetBaseInfoReservoirIdUsingGet(options:AnalysisReservoirGetBaseInfoReservoirIdUsingGet.Options):Promise<AnalysisReservoirGetBaseInfoReservoirIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/reservoir/getBaseInfo/{reservoirId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 水库信息接口
 * @summary 获取水库详细信息
 * @url /analysis/reservoir/getDetail/{reservoirId}
 * @method get
 * @description 
 */

export module AnalysisReservoirGetDetailReservoirIdUsingGet {
  export type Operation = paths['/analysis/reservoir/getDetail/{reservoirId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
    params: Query;
  };
}

/**
 * @tag 水库信息接口
 * @summary 获取水库详细信息
 * @url /analysis/reservoir/getDetail/{reservoirId}
 * @method get
 * @description 
 */

export function analysisReservoirGetDetailReservoirIdUsingGet(options:AnalysisReservoirGetDetailReservoirIdUsingGet.Options):Promise<AnalysisReservoirGetDetailReservoirIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/reservoir/getDetail/{reservoirId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 水库信息接口
 * @summary 获取水库信息分组列表
 * @url /analysis/reservoir/groupList/{groupType}
 * @method get
 * @description 
 */

export module AnalysisReservoirGroupListGroupTypeUsingGet {
  export type Operation = paths['/analysis/reservoir/groupList/{groupType}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
    params: Query;
  };
}

/**
 * @tag 水库信息接口
 * @summary 获取水库信息分组列表
 * @url /analysis/reservoir/groupList/{groupType}
 * @method get
 * @description 
 */

export function analysisReservoirGroupListGroupTypeUsingGet(options:AnalysisReservoirGroupListGroupTypeUsingGet.Options):Promise<AnalysisReservoirGroupListGroupTypeUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/reservoir/groupList/{groupType}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 水库信息接口
 * @summary 获取水库位置信息
 * @url /analysis/reservoir/location
 * @method get
 * @description 
 */

export module AnalysisReservoirLocationUsingGet {
  export type Operation = paths['/analysis/reservoir/location']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 水库信息接口
 * @summary 获取水库位置信息
 * @url /analysis/reservoir/location
 * @method get
 * @description 
 */

export function analysisReservoirLocationUsingGet(options:AnalysisReservoirLocationUsingGet.Options):Promise<AnalysisReservoirLocationUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/reservoir/location',
    method:'get',
    ...options,
  });
}

/**
 * @tag 水库信息接口
 * @summary 获取水库统计信息
 * @url /analysis/reservoir/statisticCityType
 * @method get
 * @description 
 */

export module AnalysisReservoirStatisticCityTypeUsingGet {
  export type Operation = paths['/analysis/reservoir/statisticCityType']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 水库信息接口
 * @summary 获取水库统计信息
 * @url /analysis/reservoir/statisticCityType
 * @method get
 * @description 
 */

export function analysisReservoirStatisticCityTypeUsingGet(options:AnalysisReservoirStatisticCityTypeUsingGet.Options):Promise<AnalysisReservoirStatisticCityTypeUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/reservoir/statisticCityType',
    method:'get',
    ...options,
  });
}

/**
 * @tag 水库信息接口
 * @summary 获取水库类型信息
 * @url /analysis/reservoir/types
 * @method get
 * @description 
 */

export module AnalysisReservoirTypesUsingGet {
  export type Operation = paths['/analysis/reservoir/types']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 水库信息接口
 * @summary 获取水库类型信息
 * @url /analysis/reservoir/types
 * @method get
 * @description 
 */

export function analysisReservoirTypesUsingGet(options:AnalysisReservoirTypesUsingGet.Options):Promise<AnalysisReservoirTypesUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/reservoir/types',
    method:'get',
    ...options,
  });
}

/**
 * @tag 风险评估接口
 * @summary 获取列表
 * @url /analysis/riskEstimate/getList
 * @method get
 * @description 
 */

export module AnalysisRiskEstimateGetListUsingGet {
  export type Operation = paths['/analysis/riskEstimate/getList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 风险评估接口
 * @summary 获取列表
 * @url /analysis/riskEstimate/getList
 * @method get
 * @description 
 */

export function analysisRiskEstimateGetListUsingGet(options:AnalysisRiskEstimateGetListUsingGet.Options):Promise<AnalysisRiskEstimateGetListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/riskEstimate/getList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 风险报告接口
 * @summary 获取报告详情信息
 * @url /analysis/riskReport/getDetail
 * @method get
 * @description 
 */

export module AnalysisRiskReportGetDetailUsingGet {
  export type Operation = paths['/analysis/riskReport/getDetail']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 风险报告接口
 * @summary 获取报告详情信息
 * @url /analysis/riskReport/getDetail
 * @method get
 * @description 
 */

export function analysisRiskReportGetDetailUsingGet(options:AnalysisRiskReportGetDetailUsingGet.Options):Promise<AnalysisRiskReportGetDetailUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/riskReport/getDetail',
    method:'get',
    ...options,
  });
}

/**
 * @tag 风险报告接口
 * @summary 获取报告列表信息
 * @url /analysis/riskReport/getList
 * @method get
 * @description 
 */

export module AnalysisRiskReportGetListUsingGet {
  export type Operation = paths['/analysis/riskReport/getList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 风险报告接口
 * @summary 获取报告列表信息
 * @url /analysis/riskReport/getList
 * @method get
 * @description 
 */

export function analysisRiskReportGetListUsingGet(options:AnalysisRiskReportGetListUsingGet.Options):Promise<AnalysisRiskReportGetListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/riskReport/getList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 风险报告接口
 * @summary 删除报告
 * @url /analysis/riskReport/remove
 * @method get
 * @description 
 */

export module AnalysisRiskReportRemoveUsingGet {
  export type Operation = paths['/analysis/riskReport/remove']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 风险报告接口
 * @summary 删除报告
 * @url /analysis/riskReport/remove
 * @method get
 * @description 
 */

export function analysisRiskReportRemoveUsingGet(options:AnalysisRiskReportRemoveUsingGet.Options):Promise<AnalysisRiskReportRemoveUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/riskReport/remove',
    method:'get',
    ...options,
  });
}

/**
 * @tag 风险报告接口
 * @summary 新增或者更新
 * @url /analysis/riskReport/saveOrUpdate
 * @method post
 * @description 新增或者更新
 */

export module AnalysisRiskReportSaveOrUpdateUsingPost {
  export type Operation = paths['/analysis/riskReport/saveOrUpdate']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 风险报告接口
 * @summary 新增或者更新
 * @url /analysis/riskReport/saveOrUpdate
 * @method post
 * @description 新增或者更新
 */

export function analysisRiskReportSaveOrUpdateUsingPost(options:AnalysisRiskReportSaveOrUpdateUsingPost.Options):Promise<AnalysisRiskReportSaveOrUpdateUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/riskReport/saveOrUpdate',
    method:'post',
    ...options,
  });
}

/**
 * @tag 船舶接口
 * @summary 导出渔船excel
 * @url /analysis/ship/exportExcel
 * @method get
 * @description 
 */

export module AnalysisShipExportExcelUsingGet {
  export type Operation = paths['/analysis/ship/exportExcel']['get'];
  export type Result = any;
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 船舶接口
 * @summary 导出渔船excel
 * @url /analysis/ship/exportExcel
 * @method get
 * @description 
 */

export function analysisShipExportExcelUsingGet(options:AnalysisShipExportExcelUsingGet.Options):Promise<AnalysisShipExportExcelUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/ship/exportExcel',
    method:'get',
    ...options,
  });
}

/**
 * @tag 船舶接口
 * @summary 获取所有的渔船
 * @url /analysis/ship/getList
 * @method get
 * @description 
 */

export module AnalysisShipGetListUsingGet {
  export type Operation = paths['/analysis/ship/getList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 船舶接口
 * @summary 获取所有的渔船
 * @url /analysis/ship/getList
 * @method get
 * @description 
 */

export function analysisShipGetListUsingGet(options:AnalysisShipGetListUsingGet.Options):Promise<AnalysisShipGetListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/ship/getList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 船舶接口
 * @summary 同步基本信息
 * @url /analysis/ship/spt/baseinfo
 * @method get
 * @description 
 */

export module AnalysisShipSptBaseinfoUsingGet {
  export type Operation = paths['/analysis/ship/spt/baseinfo']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 船舶接口
 * @summary 同步基本信息
 * @url /analysis/ship/spt/baseinfo
 * @method get
 * @description 
 */

export function analysisShipSptBaseinfoUsingGet(options:AnalysisShipSptBaseinfoUsingGet.Options):Promise<AnalysisShipSptBaseinfoUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/ship/spt/baseinfo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 船舶接口
 * @summary 同步实时信息
 * @url /analysis/ship/spt/location
 * @method get
 * @description 
 */

export module AnalysisShipSptLocationUsingGet {
  export type Operation = paths['/analysis/ship/spt/location']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 船舶接口
 * @summary 同步实时信息
 * @url /analysis/ship/spt/location
 * @method get
 * @description 
 */

export function analysisShipSptLocationUsingGet(options:AnalysisShipSptLocationUsingGet.Options):Promise<AnalysisShipSptLocationUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/ship/spt/location',
    method:'get',
    ...options,
  });
}

/**
 * @tag 船舶接口
 * @summary 同步基本信息
 * @url /analysis/ship/spt/saveBaseinfo
 * @method get
 * @description 
 */

export module AnalysisShipSptSaveBaseinfoUsingGet {
  export type Operation = paths['/analysis/ship/spt/saveBaseinfo']['get'];
  export type Result = any;
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 船舶接口
 * @summary 同步基本信息
 * @url /analysis/ship/spt/saveBaseinfo
 * @method get
 * @description 
 */

export function analysisShipSptSaveBaseinfoUsingGet(options:AnalysisShipSptSaveBaseinfoUsingGet.Options):Promise<AnalysisShipSptSaveBaseinfoUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/ship/spt/saveBaseinfo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 土壤信息接口
 * @summary 获取土壤信息站数据
 * @url /analysis/soil/listStation
 * @method get
 * @description 
 */

export module AnalysisSoilListStationUsingGet {
  export type Operation = paths['/analysis/soil/listStation']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 土壤信息接口
 * @summary 获取土壤信息站数据
 * @url /analysis/soil/listStation
 * @method get
 * @description 
 */

export function analysisSoilListStationUsingGet(options:AnalysisSoilListStationUsingGet.Options):Promise<AnalysisSoilListStationUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/soil/listStation',
    method:'get',
    ...options,
  });
}

/**
 * @tag 土壤信息接口
 * @summary 获取指定时间的土壤信息站及其监测数据列表
 * @url /analysis/soil/listStationData
 * @method get
 * @description 
 */

export module AnalysisSoilListStationDataUsingGet {
  export type Operation = paths['/analysis/soil/listStationData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 土壤信息接口
 * @summary 获取指定时间的土壤信息站及其监测数据列表
 * @url /analysis/soil/listStationData
 * @method get
 * @description 
 */

export function analysisSoilListStationDataUsingGet(options:AnalysisSoilListStationDataUsingGet.Options):Promise<AnalysisSoilListStationDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/soil/listStationData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 土壤信息接口
 * @summary 获取指定土壤信息站监测数据列表
 * @url /analysis/soil/listStationValues/{stationId}
 * @method get
 * @description 
 */

export module AnalysisSoilListStationValuesStationIdUsingGet {
  export type Operation = paths['/analysis/soil/listStationValues/{stationId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 土壤信息接口
 * @summary 获取指定土壤信息站监测数据列表
 * @url /analysis/soil/listStationValues/{stationId}
 * @method get
 * @description 
 */

export function analysisSoilListStationValuesStationIdUsingGet(options:AnalysisSoilListStationValuesStationIdUsingGet.Options):Promise<AnalysisSoilListStationValuesStationIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/soil/listStationValues/{stationId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 土壤信息接口
 * @summary 获取土壤信息站更新信息
 * @url /analysis/soil/updateStatus
 * @method get
 * @description 
 */

export module AnalysisSoilUpdateStatusUsingGet {
  export type Operation = paths['/analysis/soil/updateStatus']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 土壤信息接口
 * @summary 获取土壤信息站更新信息
 * @url /analysis/soil/updateStatus
 * @method get
 * @description 
 */

export function analysisSoilUpdateStatusUsingGet(options:AnalysisSoilUpdateStatusUsingGet.Options):Promise<AnalysisSoilUpdateStatusUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/soil/updateStatus',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 下载陆地海洋预报内容
 * @url /analysis/tqyb/downloadLdhyyb
 * @method get
 * @description 
 */

export module AnalysisTqybDownloadLdhyybUsingGet {
  export type Operation = paths['/analysis/tqyb/downloadLdhyyb']['get'];
  export type Result = any;
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 天气预报接口
 * @summary 下载陆地海洋预报内容
 * @url /analysis/tqyb/downloadLdhyyb
 * @method get
 * @description 
 */

export function analysisTqybDownloadLdhyybUsingGet(options:AnalysisTqybDownloadLdhyybUsingGet.Options):Promise<AnalysisTqybDownloadLdhyybUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/downloadLdhyyb',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 获取海南各个市县
 * @url /analysis/tqyb/getCityOfHN
 * @method get
 * @description 
 */

export module AnalysisTqybGetCityOfHNUsingGet {
  export type Operation = paths['/analysis/tqyb/getCityOfHN']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 天气预报接口
 * @summary 获取海南各个市县
 * @url /analysis/tqyb/getCityOfHN
 * @method get
 * @description 
 */

export function analysisTqybGetCityOfHNUsingGet(options:AnalysisTqybGetCityOfHNUsingGet.Options):Promise<AnalysisTqybGetCityOfHNUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/getCityOfHN',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 获取区域geo数据
 * @url /analysis/tqyb/getGeoByAreaName
 * @method get
 * @description 
 */

export module AnalysisTqybGetGeoByAreaNameUsingGet {
  export type Operation = paths['/analysis/tqyb/getGeoByAreaName']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 天气预报接口
 * @summary 获取区域geo数据
 * @url /analysis/tqyb/getGeoByAreaName
 * @method get
 * @description 
 */

export function analysisTqybGetGeoByAreaNameUsingGet(options:AnalysisTqybGetGeoByAreaNameUsingGet.Options):Promise<AnalysisTqybGetGeoByAreaNameUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/getGeoByAreaName',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 获取陆地海洋预报列表
 * @url /analysis/tqyb/getLdhyyb
 * @method post
 * @description 
 */

export module AnalysisTqybGetLdhyybUsingPost {
  export type Operation = paths['/analysis/tqyb/getLdhyyb']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 天气预报接口
 * @summary 获取陆地海洋预报列表
 * @url /analysis/tqyb/getLdhyyb
 * @method post
 * @description 
 */

export function analysisTqybGetLdhyybUsingPost(options:AnalysisTqybGetLdhyybUsingPost.Options):Promise<AnalysisTqybGetLdhyybUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/getLdhyyb',
    method:'post',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 获取陆地海洋预报明细
 * @url /analysis/tqyb/getLdhyybmx
 * @method get
 * @description 
 */

export module AnalysisTqybGetLdhyybmxUsingGet {
  export type Operation = paths['/analysis/tqyb/getLdhyybmx']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 天气预报接口
 * @summary 获取陆地海洋预报明细
 * @url /analysis/tqyb/getLdhyybmx
 * @method get
 * @description 
 */

export function analysisTqybGetLdhyybmxUsingGet(options:AnalysisTqybGetLdhyybmxUsingGet.Options):Promise<AnalysisTqybGetLdhyybmxUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/getLdhyybmx',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 获取旅游天气数据
 * @url /analysis/tqyb/getLytqyb
 * @method get
 * @description 
 */

export module AnalysisTqybGetLytqybUsingGet {
  export type Operation = paths['/analysis/tqyb/getLytqyb']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 天气预报接口
 * @summary 获取旅游天气数据
 * @url /analysis/tqyb/getLytqyb
 * @method get
 * @description 
 */

export function analysisTqybGetLytqybUsingGet(options:AnalysisTqybGetLytqybUsingGet.Options):Promise<AnalysisTqybGetLytqybUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/getLytqyb',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 获取市县天气数据
 * @url /analysis/tqyb/getSxtqyb
 * @method get
 * @description 
 */

export module AnalysisTqybGetSxtqybUsingGet {
  export type Operation = paths['/analysis/tqyb/getSxtqyb']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 天气预报接口
 * @summary 获取市县天气数据
 * @url /analysis/tqyb/getSxtqyb
 * @method get
 * @description 
 */

export function analysisTqybGetSxtqybUsingGet(options:AnalysisTqybGetSxtqybUsingGet.Options):Promise<AnalysisTqybGetSxtqybUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/getSxtqyb',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 获取乡镇天气数据
 * @url /analysis/tqyb/getXztqyb
 * @method get
 * @description 
 */

export module AnalysisTqybGetXztqybUsingGet {
  export type Operation = paths['/analysis/tqyb/getXztqyb']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 天气预报接口
 * @summary 获取乡镇天气数据
 * @url /analysis/tqyb/getXztqyb
 * @method get
 * @description 
 */

export function analysisTqybGetXztqybUsingGet(options:AnalysisTqybGetXztqybUsingGet.Options):Promise<AnalysisTqybGetXztqybUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/getXztqyb',
    method:'get',
    ...options,
  });
}

/**
 * @tag 天气预报接口
 * @summary 测试
 * @url /analysis/tqyb/test
 * @method get
 * @description 
 */

export module AnalysisTqybTestUsingGet {
  export type Operation = paths['/analysis/tqyb/test']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 天气预报接口
 * @summary 测试
 * @url /analysis/tqyb/test
 * @method get
 * @description 
 */

export function analysisTqybTestUsingGet(options:AnalysisTqybTestUsingGet.Options):Promise<AnalysisTqybTestUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/tqyb/test',
    method:'get',
    ...options,
  });
}

/**
 * @tag 趋势分析接口
 * @summary 获取数据的最新时间
 * @url /analysis/trend/getDataLatestDate
 * @method get
 * @description 
 */

export module AnalysisTrendGetDataLatestDateUsingGet {
  export type Operation = paths['/analysis/trend/getDataLatestDate']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 趋势分析接口
 * @summary 获取数据的最新时间
 * @url /analysis/trend/getDataLatestDate
 * @method get
 * @description 
 */

export function analysisTrendGetDataLatestDateUsingGet(options:AnalysisTrendGetDataLatestDateUsingGet.Options):Promise<AnalysisTrendGetDataLatestDateUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/trend/getDataLatestDate',
    method:'get',
    ...options,
  });
}

/**
 * @tag 趋势分析接口
 * @summary 获取趋势分析统计数据
 * @url /analysis/trend/getStatData
 * @method get
 * @description 
 */

export module AnalysisTrendGetStatDataUsingGet {
  export type Operation = paths['/analysis/trend/getStatData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 趋势分析接口
 * @summary 获取趋势分析统计数据
 * @url /analysis/trend/getStatData
 * @method get
 * @description 
 */

export function analysisTrendGetStatDataUsingGet(options:AnalysisTrendGetStatDataUsingGet.Options):Promise<AnalysisTrendGetStatDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/trend/getStatData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 趋势分析接口
 * @summary 获取铁塔数据
 * @url /analysis/trend/getTowerData
 * @method post
 * @description 
 */

export module AnalysisTrendGetTowerDataUsingPost {
  export type Operation = paths['/analysis/trend/getTowerData']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 趋势分析接口
 * @summary 获取铁塔数据
 * @url /analysis/trend/getTowerData
 * @method post
 * @description 
 */

export function analysisTrendGetTowerDataUsingPost(options:AnalysisTrendGetTowerDataUsingPost.Options):Promise<AnalysisTrendGetTowerDataUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/trend/getTowerData',
    method:'post',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库和河流预警统计数据
 * @url /analysis/water/situation/basin/getReservoirRiverWarningCount
 * @method get
 * @description 
 */

export module AnalysisWaterSituationBasinGetReservoirRiverWarningCountUsingGet {
  export type Operation = paths['/analysis/water/situation/basin/getReservoirRiverWarningCount']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库和河流预警统计数据
 * @url /analysis/water/situation/basin/getReservoirRiverWarningCount
 * @method get
 * @description 
 */

export function analysisWaterSituationBasinGetReservoirRiverWarningCountUsingGet(options:AnalysisWaterSituationBasinGetReservoirRiverWarningCountUsingGet.Options):Promise<AnalysisWaterSituationBasinGetReservoirRiverWarningCountUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/basin/getReservoirRiverWarningCount',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水库预警-获取预警左侧列表数据
 * @url /analysis/water/situation/basin/getReservoirWarningData
 * @method post
 * @description 
 */

export module AnalysisWaterSituationBasinGetReservoirWarningDataUsingPost {
  export type Operation = paths['/analysis/water/situation/basin/getReservoirWarningData']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水库预警-获取预警左侧列表数据
 * @url /analysis/water/situation/basin/getReservoirWarningData
 * @method post
 * @description 
 */

export function analysisWaterSituationBasinGetReservoirWarningDataUsingPost(options:AnalysisWaterSituationBasinGetReservoirWarningDataUsingPost.Options):Promise<AnalysisWaterSituationBasinGetReservoirWarningDataUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/basin/getReservoirWarningData',
    method:'post',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取河流预警列表信息
 * @url /analysis/water/situation/basin/getRiverWarningData
 * @method post
 * @description 
 */

export module AnalysisWaterSituationBasinGetRiverWarningDataUsingPost {
  export type Operation = paths['/analysis/water/situation/basin/getRiverWarningData']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取河流预警列表信息
 * @url /analysis/water/situation/basin/getRiverWarningData
 * @method post
 * @description 
 */

export function analysisWaterSituationBasinGetRiverWarningDataUsingPost(options:AnalysisWaterSituationBasinGetRiverWarningDataUsingPost.Options):Promise<AnalysisWaterSituationBasinGetRiverWarningDataUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/basin/getRiverWarningData',
    method:'post',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取主要流域信息(如：南渡江、昌化江、万泉河)
 * @url /analysis/water/situation/basin/main
 * @method get
 * @description 
 */

export module AnalysisWaterSituationBasinMainUsingGet {
  export type Operation = paths['/analysis/water/situation/basin/main']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取主要流域信息(如：南渡江、昌化江、万泉河)
 * @url /analysis/water/situation/basin/main
 * @method get
 * @description 
 */

export function analysisWaterSituationBasinMainUsingGet(options:AnalysisWaterSituationBasinMainUsingGet.Options):Promise<AnalysisWaterSituationBasinMainUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/basin/main',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取流域的监测站点信息
 * @url /analysis/water/situation/basin/stations/{bashinId}
 * @method get
 * @description 
 */

export module AnalysisWaterSituationBasinStationsBashinIdUsingGet {
  export type Operation = paths['/analysis/water/situation/basin/stations/{bashinId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取流域的监测站点信息
 * @url /analysis/water/situation/basin/stations/{bashinId}
 * @method get
 * @description 
 */

export function analysisWaterSituationBasinStationsBashinIdUsingGet(options:AnalysisWaterSituationBasinStationsBashinIdUsingGet.Options):Promise<AnalysisWaterSituationBasinStationsBashinIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/basin/stations/{bashinId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取监测站详细信息
 * @url /analysis/water/situation/getDetail/{stationId}
 * @method get
 * @description 
 */

export module AnalysisWaterSituationGetDetailStationIdUsingGet {
  export type Operation = paths['/analysis/water/situation/getDetail/{stationId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取监测站详细信息
 * @url /analysis/water/situation/getDetail/{stationId}
 * @method get
 * @description 
 */

export function analysisWaterSituationGetDetailStationIdUsingGet(options:AnalysisWaterSituationGetDetailStationIdUsingGet.Options):Promise<AnalysisWaterSituationGetDetailStationIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/getDetail/{stationId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取监测站点信息分组列表
 * @url /analysis/water/situation/groupList/{groupType}
 * @method get
 * @description 
 */

export module AnalysisWaterSituationGroupListGroupTypeUsingGet {
  export type Operation = paths['/analysis/water/situation/groupList/{groupType}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取监测站点信息分组列表
 * @url /analysis/water/situation/groupList/{groupType}
 * @method get
 * @description 
 */

export function analysisWaterSituationGroupListGroupTypeUsingGet(options:AnalysisWaterSituationGroupListGroupTypeUsingGet.Options):Promise<AnalysisWaterSituationGroupListGroupTypeUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/groupList/{groupType}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库的安全转移信息
 * @url /analysis/water/situation/reservoir/getAqzyInfo/{skId}
 * @method get
 * @description 
 */

export module AnalysisWaterSituationReservoirGetAqzyInfoSkIdUsingGet {
  export type Operation = paths['/analysis/water/situation/reservoir/getAqzyInfo/{skId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库的安全转移信息
 * @url /analysis/water/situation/reservoir/getAqzyInfo/{skId}
 * @method get
 * @description 
 */

export function analysisWaterSituationReservoirGetAqzyInfoSkIdUsingGet(options:AnalysisWaterSituationReservoirGetAqzyInfoSkIdUsingGet.Options):Promise<AnalysisWaterSituationReservoirGetAqzyInfoSkIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/reservoir/getAqzyInfo/{skId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库的集雨面积
 * @url /analysis/water/situation/reservoir/getJymjInfo/{skId}
 * @method get
 * @description 
 */

export module AnalysisWaterSituationReservoirGetJymjInfoSkIdUsingGet {
  export type Operation = paths['/analysis/water/situation/reservoir/getJymjInfo/{skId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库的集雨面积
 * @url /analysis/water/situation/reservoir/getJymjInfo/{skId}
 * @method get
 * @description 
 */

export function analysisWaterSituationReservoirGetJymjInfoSkIdUsingGet(options:AnalysisWaterSituationReservoirGetJymjInfoSkIdUsingGet.Options):Promise<AnalysisWaterSituationReservoirGetJymjInfoSkIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/reservoir/getJymjInfo/{skId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库的淹没范围信息
 * @url /analysis/water/situation/reservoir/getYmfwInfo/{skId}
 * @method get
 * @description 
 */

export module AnalysisWaterSituationReservoirGetYmfwInfoSkIdUsingGet {
  export type Operation = paths['/analysis/water/situation/reservoir/getYmfwInfo/{skId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取水库的淹没范围信息
 * @url /analysis/water/situation/reservoir/getYmfwInfo/{skId}
 * @method get
 * @description 
 */

export function analysisWaterSituationReservoirGetYmfwInfoSkIdUsingGet(options:AnalysisWaterSituationReservoirGetYmfwInfoSkIdUsingGet.Options):Promise<AnalysisWaterSituationReservoirGetYmfwInfoSkIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/reservoir/getYmfwInfo/{skId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 获取监测站更新信息
 * @url /analysis/water/situation/updateStatus
 * @method get
 * @description 
 */

export module AnalysisWaterSituationUpdateStatusUsingGet {
  export type Operation = paths['/analysis/water/situation/updateStatus']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 获取监测站更新信息
 * @url /analysis/water/situation/updateStatus
 * @method get
 * @description 
 */

export function analysisWaterSituationUpdateStatusUsingGet(options:AnalysisWaterSituationUpdateStatusUsingGet.Options):Promise<AnalysisWaterSituationUpdateStatusUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/updateStatus',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取水雨情数据
 * @url /analysis/water/situation/zhsw/getSYQDetail
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetSYQDetailUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getSYQDetail']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取水雨情数据
 * @url /analysis/water/situation/zhsw/getSYQDetail
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetSYQDetailUsingGet(options:AnalysisWaterSituationZhswGetSYQDetailUsingGet.Options):Promise<AnalysisWaterSituationZhswGetSYQDetailUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getSYQDetail',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取基础信息数据
 * @url /analysis/water/situation/zhsw/getSkBaseInfo
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetSkBaseInfoUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getSkBaseInfo']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取基础信息数据
 * @url /analysis/water/situation/zhsw/getSkBaseInfo
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetSkBaseInfoUsingGet(options:AnalysisWaterSituationZhswGetSkBaseInfoUsingGet.Options):Promise<AnalysisWaterSituationZhswGetSkBaseInfoUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getSkBaseInfo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取水库水位详情数据
 * @url /analysis/water/situation/zhsw/getSkDetail
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetSkDetailUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getSkDetail']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取水库水位详情数据
 * @url /analysis/water/situation/zhsw/getSkDetail
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetSkDetailUsingGet(options:AnalysisWaterSituationZhswGetSkDetailUsingGet.Options):Promise<AnalysisWaterSituationZhswGetSkDetailUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getSkDetail',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网获取水库列表
 * @url /analysis/water/situation/zhsw/getSkList
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetSkListUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getSkList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网获取水库列表
 * @url /analysis/water/situation/zhsw/getSkList
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetSkListUsingGet(options:AnalysisWaterSituationZhswGetSkListUsingGet.Options):Promise<AnalysisWaterSituationZhswGetSkListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getSkList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取水库按类型统计数据
 * @url /analysis/water/situation/zhsw/getSkScaleStat
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetSkScaleStatUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getSkScaleStat']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取水库按类型统计数据
 * @url /analysis/water/situation/zhsw/getSkScaleStat
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetSkScaleStatUsingGet(options:AnalysisWaterSituationZhswGetSkScaleStatUsingGet.Options):Promise<AnalysisWaterSituationZhswGetSkScaleStatUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getSkScaleStat',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取视频数据
 * @url /analysis/water/situation/zhsw/getSkVideo
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetSkVideoUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getSkVideo']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取视频数据
 * @url /analysis/water/situation/zhsw/getSkVideo
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetSkVideoUsingGet(options:AnalysisWaterSituationZhswGetSkVideoUsingGet.Options):Promise<AnalysisWaterSituationZhswGetSkVideoUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getSkVideo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取责任人数据
 * @url /analysis/water/situation/zhsw/getSkZzr
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetSkZzrUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getSkZzr']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取责任人数据
 * @url /analysis/water/situation/zhsw/getSkZzr
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetSkZzrUsingGet(options:AnalysisWaterSituationZhswGetSkZzrUsingGet.Options):Promise<AnalysisWaterSituationZhswGetSkZzrUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getSkZzr',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取蓄水量数据
 * @url /analysis/water/situation/zhsw/getXSLDetail
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswGetXSLDetailUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/getXSLDetail']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-获取蓄水量数据
 * @url /analysis/water/situation/zhsw/getXSLDetail
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswGetXSLDetailUsingGet(options:AnalysisWaterSituationZhswGetXSLDetailUsingGet.Options):Promise<AnalysisWaterSituationZhswGetXSLDetailUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/getXSLDetail',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-导入视频
 * @url /analysis/water/situation/zhsw/importVideo
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswImportVideoUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/importVideo']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-导入视频
 * @url /analysis/water/situation/zhsw/importVideo
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswImportVideoUsingGet(options:AnalysisWaterSituationZhswImportVideoUsingGet.Options):Promise<AnalysisWaterSituationZhswImportVideoUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/importVideo',
    method:'get',
    ...options,
  });
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-导入责任人
 * @url /analysis/water/situation/zhsw/importZzr
 * @method get
 * @description 
 */

export module AnalysisWaterSituationZhswImportZzrUsingGet {
  export type Operation = paths['/analysis/water/situation/zhsw/importZzr']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 实时水情信息接口
 * @summary 水务厅智慧水网-导入责任人
 * @url /analysis/water/situation/zhsw/importZzr
 * @method get
 * @description 
 */

export function analysisWaterSituationZhswImportZzrUsingGet(options:AnalysisWaterSituationZhswImportZzrUsingGet.Options):Promise<AnalysisWaterSituationZhswImportZzrUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/water/situation/zhsw/importZzr',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取站点24小时统计数据（强降水，大风，温度等）
 * @url /analysis/weather/get24HourStatData
 * @method get
 * @description 
 */

export module AnalysisWeatherGet24HourStatDataUsingGet {
  export type Operation = paths['/analysis/weather/get24HourStatData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取站点24小时统计数据（强降水，大风，温度等）
 * @url /analysis/weather/get24HourStatData
 * @method get
 * @description 
 */

export function analysisWeatherGet24HourStatDataUsingGet(options:AnalysisWeatherGet24HourStatDataUsingGet.Options):Promise<AnalysisWeatherGet24HourStatDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/get24HourStatData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取台风距离数据
 * @url /analysis/weather/getDistanceData
 * @method get
 * @description 
 */

export module AnalysisWeatherGetDistanceDataUsingGet {
  export type Operation = paths['/analysis/weather/getDistanceData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取台风距离数据
 * @url /analysis/weather/getDistanceData
 * @method get
 * @description 
 */

export function analysisWeatherGetDistanceDataUsingGet(options:AnalysisWeatherGetDistanceDataUsingGet.Options):Promise<AnalysisWeatherGetDistanceDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getDistanceData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取风廓线雷达数据
 * @url /analysis/weather/getFkxldData
 * @method get
 * @description 
 */

export module AnalysisWeatherGetFkxldDataUsingGet {
  export type Operation = paths['/analysis/weather/getFkxldData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取风廓线雷达数据
 * @url /analysis/weather/getFkxldData
 * @method get
 * @description 
 */

export function analysisWeatherGetFkxldDataUsingGet(options:AnalysisWeatherGetFkxldDataUsingGet.Options):Promise<AnalysisWeatherGetFkxldDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getFkxldData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取内涝积水点数据
 * @url /analysis/weather/getNljsd
 * @method get
 * @description 
 */

export module AnalysisWeatherGetNljsdUsingGet {
  export type Operation = paths['/analysis/weather/getNljsd']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取内涝积水点数据
 * @url /analysis/weather/getNljsd
 * @method get
 * @description 
 */

export function analysisWeatherGetNljsdUsingGet(options:AnalysisWeatherGetNljsdUsingGet.Options):Promise<AnalysisWeatherGetNljsdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getNljsd',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取气候预测公报数据
 * @url /analysis/weather/getQhycgbData
 * @method get
 * @description 
 */

export module AnalysisWeatherGetQhycgbDataUsingGet {
  export type Operation = paths['/analysis/weather/getQhycgbData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取气候预测公报数据
 * @url /analysis/weather/getQhycgbData
 * @method get
 * @description 
 */

export function analysisWeatherGetQhycgbDataUsingGet(options:AnalysisWeatherGetQhycgbDataUsingGet.Options):Promise<AnalysisWeatherGetQhycgbDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getQhycgbData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取雷达数据
 * @url /analysis/weather/getRadarData
 * @method get
 * @description 
 */

export module AnalysisWeatherGetRadarDataUsingGet {
  export type Operation = paths['/analysis/weather/getRadarData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取雷达数据
 * @url /analysis/weather/getRadarData
 * @method get
 * @description 
 */

export function analysisWeatherGetRadarDataUsingGet(options:AnalysisWeatherGetRadarDataUsingGet.Options):Promise<AnalysisWeatherGetRadarDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getRadarData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取闪电定位数据
 * @url /analysis/weather/getSddwData
 * @method get
 * @description 
 */

export module AnalysisWeatherGetSddwDataUsingGet {
  export type Operation = paths['/analysis/weather/getSddwData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取闪电定位数据
 * @url /analysis/weather/getSddwData
 * @method get
 * @description 
 */

export function analysisWeatherGetSddwDataUsingGet(options:AnalysisWeatherGetSddwDataUsingGet.Options):Promise<AnalysisWeatherGetSddwDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getSddwData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取强降水近24小时统计数据
 * @url /analysis/weather/getStatDataOfQJS
 * @method get
 * @description 
 */

export module AnalysisWeatherGetStatDataOfQJSUsingGet {
  export type Operation = paths['/analysis/weather/getStatDataOfQJS']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取强降水近24小时统计数据
 * @url /analysis/weather/getStatDataOfQJS
 * @method get
 * @description 
 */

export function analysisWeatherGetStatDataOfQJSUsingGet(options:AnalysisWeatherGetStatDataOfQJSUsingGet.Options):Promise<AnalysisWeatherGetStatDataOfQJSUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getStatDataOfQJS',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取台风明细数据
 * @url /analysis/weather/getTfDetail
 * @method get
 * @description 
 */

export module AnalysisWeatherGetTfDetailUsingGet {
  export type Operation = paths['/analysis/weather/getTfDetail']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取台风明细数据
 * @url /analysis/weather/getTfDetail
 * @method get
 * @description 
 */

export function analysisWeatherGetTfDetailUsingGet(options:AnalysisWeatherGetTfDetailUsingGet.Options):Promise<AnalysisWeatherGetTfDetailUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getTfDetail',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取台风列表
 * @url /analysis/weather/getTfList
 * @method get
 * @description 
 */

export module AnalysisWeatherGetTfListUsingGet {
  export type Operation = paths['/analysis/weather/getTfList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取台风列表
 * @url /analysis/weather/getTfList
 * @method get
 * @description 
 */

export function analysisWeatherGetTfListUsingGet(options:AnalysisWeatherGetTfListUsingGet.Options):Promise<AnalysisWeatherGetTfListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getTfList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取易受淹村庄数据
 * @url /analysis/weather/getYsycz
 * @method get
 * @description 
 */

export module AnalysisWeatherGetYsyczUsingGet {
  export type Operation = paths['/analysis/weather/getYsycz']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取易受淹村庄数据
 * @url /analysis/weather/getYsycz
 * @method get
 * @description 
 */

export function analysisWeatherGetYsyczUsingGet(options:AnalysisWeatherGetYsyczUsingGet.Options):Promise<AnalysisWeatherGetYsyczUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getYsycz',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取云图数据
 * @url /analysis/weather/getYtldData
 * @method get
 * @description 
 */

export module AnalysisWeatherGetYtldDataUsingGet {
  export type Operation = paths['/analysis/weather/getYtldData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取云图数据
 * @url /analysis/weather/getYtldData
 * @method get
 * @description 
 */

export function analysisWeatherGetYtldDataUsingGet(options:AnalysisWeatherGetYtldDataUsingGet.Options):Promise<AnalysisWeatherGetYtldDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getYtldData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取易淹易涝点数据
 * @url /analysis/weather/getYyyldList
 * @method get
 * @description 
 */

export module AnalysisWeatherGetYyyldListUsingGet {
  export type Operation = paths['/analysis/weather/getYyyldList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取易淹易涝点数据
 * @url /analysis/weather/getYyyldList
 * @method get
 * @description 
 */

export function analysisWeatherGetYyyldListUsingGet(options:AnalysisWeatherGetYyyldListUsingGet.Options):Promise<AnalysisWeatherGetYyyldListUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/getYyyldList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 初始化气象数据
 * @url /analysis/weather/initQxData
 * @method get
 * @description 
 */

export module AnalysisWeatherInitQxDataUsingGet {
  export type Operation = paths['/analysis/weather/initQxData']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 气象天气接口
 * @summary 初始化气象数据
 * @url /analysis/weather/initQxData
 * @method get
 * @description 
 */

export function analysisWeatherInitQxDataUsingGet(options:AnalysisWeatherInitQxDataUsingGet.Options):Promise<AnalysisWeatherInitQxDataUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/initQxData',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取天气站实况数据
 * @url /analysis/weather/listAndStatistic
 * @method get
 * @description 
 */

export module AnalysisWeatherListAndStatisticUsingGet {
  export type Operation = paths['/analysis/weather/listAndStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取天气站实况数据
 * @url /analysis/weather/listAndStatistic
 * @method get
 * @description 
 */

export function analysisWeatherListAndStatisticUsingGet(options:AnalysisWeatherListAndStatisticUsingGet.Options):Promise<AnalysisWeatherListAndStatisticUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/listAndStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取天气站监测数据列表
 * @url /analysis/weather/listStationValues/{stationId}
 * @method get
 * @description 
 */

export module AnalysisWeatherListStationValuesStationIdUsingGet {
  export type Operation = paths['/analysis/weather/listStationValues/{stationId}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Query = Operation['parameters']['query'];
  export interface Options {
    [key: string]: unknown;
    params: Query;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取天气站监测数据列表
 * @url /analysis/weather/listStationValues/{stationId}
 * @method get
 * @description 
 */

export function analysisWeatherListStationValuesStationIdUsingGet(options:AnalysisWeatherListStationValuesStationIdUsingGet.Options):Promise<AnalysisWeatherListStationValuesStationIdUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/listStationValues/{stationId}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取风流场json文件数据
 * @url /analysis/weather/prediction/grib2Data
 * @method post
 * @description 
 */

export module AnalysisWeatherPredictionGrib2DataUsingPost {
  export type Operation = paths['/analysis/weather/prediction/grib2Data']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取风流场json文件数据
 * @url /analysis/weather/prediction/grib2Data
 * @method post
 * @description 
 */

export function analysisWeatherPredictionGrib2DataUsingPost(options:AnalysisWeatherPredictionGrib2DataUsingPost.Options):Promise<AnalysisWeatherPredictionGrib2DataUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/weather/prediction/grib2Data',
    method:'post',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取风流场时间数据
 * @url /analysis/weather/prediction/grib2DataTimeList/{code}
 * @method get
 * @description 
 */

export module AnalysisWeatherPredictionGrib2DataTimeListCodeUsingGet {
  export type Operation = paths['/analysis/weather/prediction/grib2DataTimeList/{code}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取风流场时间数据
 * @url /analysis/weather/prediction/grib2DataTimeList/{code}
 * @method get
 * @description 
 */

export function analysisWeatherPredictionGrib2DataTimeListCodeUsingGet(options:AnalysisWeatherPredictionGrib2DataTimeListCodeUsingGet.Options):Promise<AnalysisWeatherPredictionGrib2DataTimeListCodeUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/prediction/grib2DataTimeList/{code}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取天气站更新信息
 * @url /analysis/weather/updateStatus
 * @method get
 * @description 
 */

export module AnalysisWeatherUpdateStatusUsingGet {
  export type Operation = paths['/analysis/weather/updateStatus']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取天气站更新信息
 * @url /analysis/weather/updateStatus
 * @method get
 * @description 
 */

export function analysisWeatherUpdateStatusUsingGet(options:AnalysisWeatherUpdateStatusUsingGet.Options):Promise<AnalysisWeatherUpdateStatusUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/updateStatus',
    method:'get',
    ...options,
  });
}

/**
 * @tag 气象天气接口
 * @summary 获取强降水最新信息
 * @url /analysis/weather/water/getLatestNews
 * @method get
 * @description 
 */

export module AnalysisWeatherWaterGetLatestNewsUsingGet {
  export type Operation = paths['/analysis/weather/water/getLatestNews']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 气象天气接口
 * @summary 获取强降水最新信息
 * @url /analysis/weather/water/getLatestNews
 * @method get
 * @description 
 */

export function analysisWeatherWaterGetLatestNewsUsingGet(options:AnalysisWeatherWaterGetLatestNewsUsingGet.Options):Promise<AnalysisWeatherWaterGetLatestNewsUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/weather/water/getLatestNews',
    method:'get',
    ...options,
  });
}

/**
 * @tag 一张图接口
 * @summary 获取树列表
 * @url /analysis/yzt/getDetailObject
 * @method post
 * @description 
 */

export module AnalysisYztGetDetailObjectUsingPost {
  export type Operation = paths['/analysis/yzt/getDetailObject']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 一张图接口
 * @summary 获取树列表
 * @url /analysis/yzt/getDetailObject
 * @method post
 * @description 
 */

export function analysisYztGetDetailObjectUsingPost(options:AnalysisYztGetDetailObjectUsingPost.Options):Promise<AnalysisYztGetDetailObjectUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/yzt/getDetailObject',
    method:'post',
    ...options,
  });
}

/**
 * @tag 一张图接口
 * @summary 获取列表
 * @url /analysis/yzt/getList
 * @method post
 * @description 
 */

export module AnalysisYztGetListUsingPost {
  export type Operation = paths['/analysis/yzt/getList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 一张图接口
 * @summary 获取列表
 * @url /analysis/yzt/getList
 * @method post
 * @description 
 */

export function analysisYztGetListUsingPost(options:AnalysisYztGetListUsingPost.Options):Promise<AnalysisYztGetListUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/yzt/getList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 一张图接口
 * @summary 获取树列表
 * @url /analysis/yzt/getTreeList
 * @method post
 * @description 
 */

export module AnalysisYztGetTreeListUsingPost {
  export type Operation = paths['/analysis/yzt/getTreeList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 一张图接口
 * @summary 获取树列表
 * @url /analysis/yzt/getTreeList
 * @method post
 * @description 
 */

export function analysisYztGetTreeListUsingPost(options:AnalysisYztGetTreeListUsingPost.Options):Promise<AnalysisYztGetTreeListUsingPost.Result> {
  return analysisRequest({
    url:'/analysis/yzt/getTreeList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 一张图接口
 * @summary 初始化森林防火感知网视频
 * @url /analysis/yzt/initSlfhgz
 * @method get
 * @description 
 */

export module AnalysisYztInitSlfhgzUsingGet {
  export type Operation = paths['/analysis/yzt/initSlfhgz']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 一张图接口
 * @summary 初始化森林防火感知网视频
 * @url /analysis/yzt/initSlfhgz
 * @method get
 * @description 
 */

export function analysisYztInitSlfhgzUsingGet(options:AnalysisYztInitSlfhgzUsingGet.Options):Promise<AnalysisYztInitSlfhgzUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/yzt/initSlfhgz',
    method:'get',
    ...options,
  });
}

/**
 * @tag 一张图接口
 * @summary 初始化森林防火感知网视频2
 * @url /analysis/yzt/initSlfhgz2
 * @method get
 * @description 
 */

export module AnalysisYztInitSlfhgz2UsingGet {
  export type Operation = paths['/analysis/yzt/initSlfhgz2']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 一张图接口
 * @summary 初始化森林防火感知网视频2
 * @url /analysis/yzt/initSlfhgz2
 * @method get
 * @description 
 */

export function analysisYztInitSlfhgz2UsingGet(options:AnalysisYztInitSlfhgz2UsingGet.Options):Promise<AnalysisYztInitSlfhgz2UsingGet.Result> {
  return analysisRequest({
    url:'/analysis/yzt/initSlfhgz2',
    method:'get',
    ...options,
  });
}

/**
 * @tag 一张图接口
 * @summary 初始化易淹易涝点视频
 * @url /analysis/yzt/intiYyyl
 * @method get
 * @description 
 */

export module AnalysisYztIntiYyylUsingGet {
  export type Operation = paths['/analysis/yzt/intiYyyl']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 一张图接口
 * @summary 初始化易淹易涝点视频
 * @url /analysis/yzt/intiYyyl
 * @method get
 * @description 
 */

export function analysisYztIntiYyylUsingGet(options:AnalysisYztIntiYyylUsingGet.Options):Promise<AnalysisYztIntiYyylUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/yzt/intiYyyl',
    method:'get',
    ...options,
  });
}

/**
 * @tag 一张图接口
 * @summary 初始化模型
 * @url /analysis/yzt/model
 * @method get
 * @description 
 */

export module AnalysisYztModelUsingGet {
  export type Operation = paths['/analysis/yzt/model']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 一张图接口
 * @summary 初始化模型
 * @url /analysis/yzt/model
 * @method get
 * @description 
 */

export function analysisYztModelUsingGet(options:AnalysisYztModelUsingGet.Options):Promise<AnalysisYztModelUsingGet.Result> {
  return analysisRequest({
    url:'/analysis/yzt/model',
    method:'get',
    ...options,
  });
}
export interface paths {
    "/analysis/agri/getStatData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取统计数据
         * @description 获取统计数据
         */
        get: operations["getStatDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/deviceGroup/getDevices/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 获取视图关联的视频信息
         * @description 获取视图关联的视频信息
         */
        get: operations["getDevicesUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/deviceGroup/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 分组树型列表 */
        get: operations["getListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/deviceGroup/remove/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 删除分组
         * @description 删除分组
         */
        get: operations["removeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/deviceGroup/save": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 创建分组
         * @description 创建分组
         */
        post: operations["saveUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/deviceGroup/saveView": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 创建视图
         * @description 创建视图
         */
        post: operations["saveViewUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/deviceGroup/update": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 更新分组或视图
         * @description 更新分组或视图,更新视图时需要传parentId
         */
        post: operations["updateUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/file/export": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 指挥对标初设-导出风险隐患文件
         * @description 导出风险隐患文件
         */
        get: operations["exportUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/get24HourFireCount": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取近24小时所有站点火警预警总记录 */
        get: operations["get24HourFireCountUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getAllFireData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取所有站点火警预警数据 */
        get: operations["getAllFireDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getFireDataById": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 根据id获取站点火警预警数据 */
        get: operations["getFireDataByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getFireDataByTime": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取站点火警预警数据(精确到时间) */
        get: operations["getFireDataByTimeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getFireDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 监测预警，获取火警明细 */
        get: operations["getFireDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getFireList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 监测预警，获取站点火警列表 */
        get: operations["getFireListUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getStationList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取站点列表 */
        get: operations["getStationListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getStationVideoList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 首页视频监控获取站点列表 */
        get: operations["getStationVideoListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/getWeatherData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取站点气象数据 */
        get: operations["getWeatherDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/sprhpt/download": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 火点下载图片 */
        get: operations["getFireListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/sprhpt/getEvents": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取海康视频融合平台事件列表 */
        get: operations["getEventsUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forest/sprhpt/saveFile": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 视频融合平台下载全部图片 */
        get: operations["saveFileUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/forward/proxy": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        get: operations["forwardUsingGET"];
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        put: operations["forwardUsingPUT"];
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        post: operations["forwardUsingPOST"];
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        delete: operations["forwardUsingDELETE"];
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        options: operations["forwardUsingOPTIONS"];
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        head: operations["forwardUsingHEAD"];
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        patch: operations["forwardUsingPATCH"];
        /**
         * 代理转发接口1
         * @description 代理转发接口1
         */
        trace: operations["forwardUsingTRACE"];
    };
    "/analysis/power/getFgfcData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取复工复产数据 */
        post: operations["getFgfcDataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/power/getHolidayList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取节日列表 */
        get: operations["getHolidayListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/power/getScztjcData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取生产状态监测数据 */
        post: operations["getScztjcDataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/power/getStatData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取统计数据 */
        get: operations["getStatDataUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/power/getYdtjData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取日用电量统计数据 */
        post: operations["getYdtjDataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/reservoir/compareReservoirStorage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库历史蓄水量比较信息 */
        get: operations["compareReservoirStorageUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/reservoir/getBaseInfo/{reservoirId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库基本信息、下游信息 */
        get: operations["getBaseInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/reservoir/getDetail/{reservoirId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库详细信息 */
        get: operations["getDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/reservoir/groupList/{groupType}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库信息分组列表 */
        get: operations["groupListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/reservoir/location": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库位置信息 */
        get: operations["locationUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/reservoir/statisticCityType": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库统计信息 */
        get: operations["statisticCityTypeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/reservoir/types": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库类型信息 */
        get: operations["typesUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/riskEstimate/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取列表 */
        get: operations["getListUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/riskReport/getDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取报告详情信息 */
        get: operations["getDetailUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/riskReport/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取报告列表信息 */
        get: operations["getListUsingGET_2"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/riskReport/remove": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 删除报告 */
        get: operations["removeUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/riskReport/saveOrUpdate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 新增或者更新
         * @description 新增或者更新
         */
        post: operations["saveOrUpdateUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/ship/exportExcel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 导出渔船excel */
        get: operations["exportExcelUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/ship/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取所有的渔船 */
        get: operations["getListUsingGET_3"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/ship/spt/baseinfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 同步基本信息 */
        get: operations["baseinfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/ship/spt/location": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 同步实时信息 */
        get: operations["locationUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/ship/spt/saveBaseinfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 同步基本信息 */
        get: operations["saveBaseinfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/soil/listStation": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取土壤信息站数据 */
        get: operations["listStationUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/soil/listStationData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取指定时间的土壤信息站及其监测数据列表 */
        get: operations["listStationDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/soil/listStationValues/{stationId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取指定土壤信息站监测数据列表 */
        get: operations["listStationValuesUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/soil/updateStatus": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取土壤信息站更新信息 */
        get: operations["updateStatusUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/downloadLdhyyb": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 下载陆地海洋预报内容 */
        get: operations["downloadLdhyybUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/getCityOfHN": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取海南各个市县 */
        get: operations["getCityOfHNUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/getGeoByAreaName": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取区域geo数据 */
        get: operations["getGeoByAreaNameUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/getLdhyyb": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取陆地海洋预报列表 */
        post: operations["getLdhyybUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/getLdhyybmx": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取陆地海洋预报明细 */
        get: operations["getLdhyybmxUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/getLytqyb": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取旅游天气数据 */
        get: operations["getLytqybUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/getSxtqyb": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取市县天气数据 */
        get: operations["getSxtqybUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/getXztqyb": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取乡镇天气数据 */
        get: operations["getXztqybUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/tqyb/test": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 测试 */
        get: operations["testUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/trend/getDataLatestDate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取数据的最新时间 */
        get: operations["getDataLatestDateUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/trend/getStatData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取趋势分析统计数据 */
        get: operations["getStatDataUsingGET_2"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/trend/getTowerData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取铁塔数据 */
        post: operations["getTowerDataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/basin/getReservoirRiverWarningCount": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库和河流预警统计数据 */
        get: operations["getReservoirRiverWarningCountUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/basin/getReservoirWarningData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 水库预警-获取预警左侧列表数据 */
        post: operations["getReservoirWarningDataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/basin/getRiverWarningData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取河流预警列表信息 */
        post: operations["getRiverWarningDataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/basin/main": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取主要流域信息(如：南渡江、昌化江、万泉河) */
        get: operations["basinLocationUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/basin/stations/{bashinId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取流域的监测站点信息 */
        get: operations["basinStationsUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/getDetail/{stationId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取监测站详细信息 */
        get: operations["getDetailUsingGET_2"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/groupList/{groupType}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取监测站点信息分组列表 */
        get: operations["groupListUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/reservoir/getAqzyInfo/{skId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库的安全转移信息 */
        get: operations["getAqzyInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/reservoir/getJymjInfo/{skId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库的集雨面积 */
        get: operations["getJymjInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/reservoir/getYmfwInfo/{skId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取水库的淹没范围信息 */
        get: operations["getYmfwInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/updateStatus": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取监测站更新信息 */
        get: operations["updateStatusUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getSkBaseInfo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-获取基础信息数据 */
        get: operations["getSkBaseInfoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getSkDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-获取水库水位详情数据 */
        get: operations["getSkDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getSkList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网获取水库列表 */
        get: operations["getSkListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getSkScaleStat": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-获取水库按类型统计数据 */
        get: operations["getSkScaleStatUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getSkVideo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-获取视频数据 */
        get: operations["getSkVideoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getSkZzr": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-获取责任人数据 */
        get: operations["getSkZzrUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getSYQDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-获取水雨情数据 */
        get: operations["getSYQDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/getXSLDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-获取蓄水量数据 */
        get: operations["getXSLDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/importVideo": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-导入视频 */
        get: operations["importVideoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/water/situation/zhsw/importZzr": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 水务厅智慧水网-导入责任人 */
        get: operations["importZzrUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/get24HourStatData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取站点24小时统计数据（强降水，大风，温度等） */
        get: operations["get24HourStatDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getDistanceData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取台风距离数据 */
        get: operations["getDistanceDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getFkxldData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取风廓线雷达数据 */
        get: operations["getFkxldDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getNljsd": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取内涝积水点数据 */
        get: operations["getNljsdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getQhycgbData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取气候预测公报数据 */
        get: operations["getQhycgbDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getRadarData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取雷达数据 */
        get: operations["getRadarDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getSddwData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取闪电定位数据 */
        get: operations["getSddwDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getStatDataOfQJS": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取强降水近24小时统计数据 */
        get: operations["getStatDataOfQJSUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getTfDetail": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取台风明细数据 */
        get: operations["getTfDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getTfList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取台风列表 */
        get: operations["getTfListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getYsycz": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取易受淹村庄数据 */
        get: operations["getYsyczUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getYtldData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取云图数据 */
        get: operations["getYtldDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/getYyyldList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取易淹易涝点数据 */
        get: operations["getYyyldListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/initQxData": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 初始化气象数据 */
        get: operations["initQxDataUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/listAndStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取天气站实况数据 */
        get: operations["listAndStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/listStationValues/{stationId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取天气站监测数据列表 */
        get: operations["listStationValuesUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/prediction/grib2Data": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取风流场json文件数据 */
        post: operations["grib2DataUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/prediction/grib2DataTimeList/{code}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取风流场时间数据 */
        get: operations["grib2DataTimeListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/updateStatus": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取天气站更新信息 */
        get: operations["updateStatusUsingGET_2"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/weather/water/getLatestNews": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取强降水最新信息 */
        get: operations["getLatestNewsUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/yzt/getDetailObject": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取树列表 */
        post: operations["getDetailObjectUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/yzt/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取列表 */
        post: operations["getListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/yzt/getTreeList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** 获取树列表 */
        post: operations["getTreeListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/yzt/initSlfhgz": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 初始化森林防火感知网视频 */
        get: operations["initSlfhgzUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/yzt/initSlfhgz2": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 初始化森林防火感知网视频2 */
        get: operations["initSlfhgz2UsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/yzt/intiYyyl": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 初始化易淹易涝点视频 */
        get: operations["intiYyylUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/yzt/model": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 初始化模型 */
        get: operations["modelUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/analysis/zwjd/getChInterfaces": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 获取政务基底数据 */
        get: operations["getChInterfacesUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /**
         * 查询排序配置项
         * @description 查询排序配置项
         */
        ChaXunPaiXuPeiZhiXiang: {
            /**
             * @description 排序方式
             * @example ASC/asc;DESC/desc
             */
            orderType?: string;
            /** @description 属性名称 */
            propertyName?: string;
        };
        /**
         * DeviceGroup对象
         * @description 视频分组信息表
         */
        DeviceGroupDuiXiang: {
            /** @description 子节点集合 */
            children?: components["schemas"]["DeviceGroupDuiXiang"][];
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序
             */
            idx?: number;
            /** @description 类型 */
            name?: string;
            /** @description 父级ip */
            parentId?: string;
            /**
             * @description 比例类型(0-屏幕比例，1-全屏)
             * @enum {string}
             */
            scaleType?: "FULL" | "RATIO";
            /**
             * Format: int32
             * @description 分屏类型
             */
            splitScreenType?: number;
            /**
             * Format: int32
             * @description 类型(0-分组，1-视图)
             */
            type?: number;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * DeviceGroup对象0
         * @description 视频分组关联信息表
         */
        DeviceGroupDuiXiang0: {
            /** @description 视频主键 */
            code?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 分组ip */
            groupId?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序值
             */
            idx?: number;
            /** @description 名称 */
            name?: string;
            /** @description 类型(平台名称) */
            type?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description url */
            url?: string;
        };
        /**
         * DeviceGroup对象1
         * @description 视频分组关联信息表
         */
        DeviceGroupDuiXiang1: {
            /** @description 视频主键 */
            code?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 分组ip */
            groupId?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 排序值
             */
            idx?: number;
            /** @description 名称 */
            name?: string;
            /** @description 类型(平台名称) */
            type?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
            /** @description url */
            url?: string;
        };
        /**
         * DeviceGroupRelationSaveDTO对象
         * @description 分组视频关联提交DTO
         */
        DeviceGroupRelationSaveDTODuiXiang: {
            /** @description 设备视频信息 */
            devices?: components["schemas"]["DeviceGroupDuiXiang1"][];
            /** @description 视图名称 */
            name?: string;
            /** @description 所属上级分组ip */
            parentId?: string;
            /**
             * @description 比例类型(0-屏幕比例，1-全屏)
             * @enum {string}
             */
            scaleType?: "FULL" | "RATIO";
            /**
             * Format: int32
             * @description 分屏类型(1-2个，2-8个，3-12个)
             */
            splitScreenType?: number;
        };
        /**
         * DeviceGroupSaveDTO对象
         * @description 分组创建提交DTO
         */
        DeviceGroupSaveDTODuiXiang: {
            /** @description 节点id */
            id?: string;
            /** @description 名称 */
            name?: string;
            /** @description 所属上级分组ip（拖动更新时传入） */
            parentId?: string;
        };
        /** DeviceInfoVo */
        DeviceInfoVo: {
            /** @description ip */
            ip?: string;
            /** @description 类型名称 */
            name?: string;
            /** @description 来源平台，固定值,表示是森林防火感知网 */
            platform?: string;
            /** @description 状态(on-在线,off-离线) */
            status?: string;
            /** @description 视频播放url */
            url?: string;
        };
        /** DeviceOnlineVo */
        DeviceOnlineVo: {
            /** @description 设备列表 */
            devices?: components["schemas"]["DeviceInfoVo"][];
            /** @description 站点id */
            stationId?: string;
            /** @description 站点名称 */
            stationName?: string;
        };
        /**
         * DsHnqxfwQxgcsjDto对象
         * @description 气象观测数据Dto
         */
        DsHnqxfwQxgcsjDtoDuiXiang: {
            /** @description 站名所属市县 */
            city?: string;
            /**
             * Format: date-time
             * @description 爬取时间
             */
            crawlDate?: string;
            /** @description 关联站信息索引 */
            fkStationinfoSeq?: string;
            ground?: string;
            /** @description 维度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
            /** @description 观测时间 */
            observTime?: string;
            /**
             * Format: bigdecimal
             * @description 当前降水量
             */
            rainFallHour?: number;
            /**
             * Format: bigdecimal
             * @description 总累计降水量
             */
            rainFallHourSum?: number;
            /** @description 相对湿度 */
            relativeHumidity?: string;
            /** @description 最大相对湿度 */
            relativeHumidityMax?: string;
            /** @description 最小相对湿度 */
            relativeHumidityMin?: string;
            /** @description 下标 */
            rowIndex?: string;
            /** @description 站点id */
            stationId?: string;
            /** @description 站名 */
            stationName?: string;
            /** @description 最大温度 */
            tempMax?: string;
            /** @description 最小温度 */
            tempMin?: string;
            /** @description 当前温度 */
            tempNow?: string;
            /** @description 记录时间 */
            time?: string;
            vis?: string;
            /** @description 当前风向 */
            windDirectionNow?: string;
            /** @description 最大风速 */
            windSpeedMax?: string;
            /** @description 最小风速 */
            windSpeedMin?: string;
            /** @description 当前风速 */
            windSpeedNow?: string;
        };
        /** DsHnqxfwQxgcsjVo */
        DsHnqxfwQxgcsjVo: {
            /** @description 数据列表 */
            data?: components["schemas"]["DsHnqxfwQxgcsjDtoDuiXiang"][];
            /** @description 新闻 */
            news?: string;
        };
        /**
         * 风险报告对象Req
         * @description 风险报告对象
         */
        FengXianBaoGaoDuiXiangReq: {
            /** @description 内容 */
            content?: string;
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 主键id */
            id?: string;
            /**
             * Format: int32
             * @description 序号
             */
            idx?: number;
            /** @description 报告id */
            reportId?: string;
            /** @description 标题 */
            title?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /**
         * 风险报告对象Res
         * @description 风险报告对象
         */
        FengXianBaoGaoDuiXiangRes: {
            /** @description 创建人 */
            createBy?: string;
            /**
             * Format: date-time
             * @description 创建时间
             */
            createTime?: string;
            /** @description 主键id */
            id?: string;
            /** @description 标题 */
            title?: string;
            /** @description 更新人 */
            updateBy?: string;
            /**
             * Format: date-time
             * @description 更新时间
             */
            updateTime?: string;
        };
        /** FgfcReq */
        FgfcReq: {
            /** @description 节日时间 */
            jrsj?: string;
        };
        /** FkxldDataVo */
        FkxldDataVo: {
            /** @description 图片集合 */
            images?: string[];
            /** @description 站点id */
            stationId?: string;
            /** @description 站点名称 */
            stationName?: string;
        };
        /** FLCDateListVo */
        FLCDateListVo: {
            /** @description text */
            text?: string;
            /** @description value */
            value?: string;
        };
        /** FLCGribDataDto */
        FLCGribDataDto: {
            /** @description code */
            code?: string;
            /** @description 时间 */
            dataTime?: string;
            /** @description value */
            filePath?: string;
            /** @description id */
            id?: string;
            /** @description 名称 */
            name?: string;
        };
        /** FLCGribDataReq */
        FLCGribDataReq: {
            /**
             * Format: int32
             * @description code
             */
            code?: number;
            /** @description 时间 */
            dataTime?: string;
        };
        /**
         * ForestFireGroupByStationVo对象
         * @description 森林火情预警列表按站点分组
         */
        ForestFireGroupByStationVoDuiXiang: {
            /** @description 站点地址 */
            address?: string;
            /**
             * Format: int32
             * @description 该站点预警数量
             */
            count?: number;
            /** @description 气象站设备编号 */
            deviceNo?: string;
            devices?: components["schemas"]["DeviceInfoVo"][];
            /**
             * Format: int32
             * @description 站点id
             */
            id?: number;
            /**
             * Format: date-time
             * @description 最新预警记录时间
             */
            latestRecordTime?: string;
            /** @description 经度 */
            latitude?: string;
            /** @description 纬度 */
            longitude?: string;
            /** @description 所属单位 */
            ssdw?: string;
            /** @description 站点名称 */
            stationName?: string;
            /** @description 应急局分管副局长电话 */
            yjjfgfjzdh?: string;
            /** @description 应急局分管副局长姓名 */
            yjjfgfjzxm?: string;
            /** @description 应急局值班室电话 */
            yjjzbsdh?: string;
            /** @description 火情视频文件目录 */
            zw?: string;
        };
        /**
         * ForestFireListGroupVo对象
         * @description 森林火情预警列表，和按站点分组统计对象
         */
        ForestFireListGroupVoDuiXiang: {
            /** @description 站点名称 */
            groupData?: components["schemas"]["ForestFireGroupByStationVoDuiXiang"][];
            /** @description 列表数据 */
            listData?: components["schemas"]["ForestFireVoDuiXiang"][];
        };
        /**
         * ForestFireListVo对象
         * @description ForestFireListVo对象
         */
        ForestFireListVoDuiXiang: {
            /** @description 火情时间 */
            createTime?: string;
            /** @description 主键 */
            id?: string;
            /** @description 火情事件名称 */
            name?: string;
        };
        /**
         * ForestFireVo对象
         * @description ForestFireVo对象
         */
        ForestFireVoDuiXiang: {
            /** @description 站点地址 */
            address?: string;
            /** @description 火警时间 */
            createTime?: string;
            /** @description 气象站设备编号 */
            deviceNo?: string;
            devices?: components["schemas"]["DeviceInfoVo"][];
            /** @description 距离(单位米 0表示无效) */
            distance?: string;
            /** @description 高度(单位：米) */
            height?: string;
            /** @description 记录主键 */
            id?: string;
            /** @description 可见光图片文件 */
            kjgFilePath?: string;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lng?: string;
            /** Format: int64 */
            nfsid?: number;
            /**
             * Format: int64
             * @description 云台水平方向角度
             */
            ptzPositionX?: number;
            /**
             * Format: int64
             * @description 云台垂直方向角度
             */
            ptzPositionY?: number;
            /**
             * Format: int64
             * @description 云台光圈放大倍率
             */
            ptzPositionZoom?: number;
            /** @description 热成像图片文件目录 */
            rcxFilePath?: string;
            /**
             * Format: int32
             * @description 站点id
             */
            stationId?: number;
            /** @description 站点名称 */
            stationName?: string;
            /**
             * Format: int32
             * @description 状态(1-开始，2-结束)
             */
            status?: number;
            /** @description 温度 */
            temp?: string;
            /** @description 火情视频文件目录 */
            videoFilePath?: string;
        };
        /**
         * ForestStationVo对象
         * @description ForestStationVo对象
         */
        ForestStationVoDuiXiang: {
            /** @description 天气24小时记录 */
            weather24HourData?: components["schemas"]["ForestStationVoDuiXiang"][];
            /** @description 天气最新记录 */
            weatherData?: components["schemas"]["WeatherVo"];
        };
        /**
         * 复工复产vo对象
         * @description 复工复产vo对象
         */
        FuGongFuChanvoDuiXiang: {
            /** @description 节日之后状态 */
            jhStatus?: string;
            /** @description 节日之前状态 */
            jqStatus?: string;
            /** @description 节日状态 */
            jzStatus?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 企业名称 */
            orgName?: string;
            /** @description 是否复工复产 */
            sffgfc?: string;
        };
        /**
         * HKEventsListVo对象
         * @description HKEventsListVo对象
         */
        HKEventsListVoDuiXiang: {
            /** @description 点位编号 */
            algorithmName?: string;
            /** @description 事件关注度 */
            eventLvl?: string;
            /** @description 事件编号 */
            eventSerial?: string;
            /** @description 主键 */
            id?: string;
            /** @description 图片地址 */
            picUrl?: string;
            /** @description 点位编号 */
            sysExclusiveEquipCode?: string;
            /** @description 图片url */
            sysExclusiveEventImage?: string;
            /** @description 事件名称 */
            sysExclusiveEventTypeName?: string;
            sysExclusiveIndexCode?: string;
            /** @description 事件地址 */
            sysExclusivePlaceReport?: string;
            /** @description 纬度 */
            sysExclusivePointLatitude?: string;
            /** @description 经度 */
            sysExclusivePointLongitude?: string;
            /** @description 点位名称 */
            sysExclusivePointTitle?: string;
            /** @description 发生时间 */
            sysExclusiveReportTime?: string;
            /** @description 分析名称 */
            taskName?: string;
            /** @description 任务类型 */
            taskType?: string;
        };
        /**
         * HnqxfwTrsfInfoDto对象
         * @description 土壤水分数据信息Dto对象
         */
        HnqxfwTrsfInfoDtoDuiXiang: {
            /** @description 区域名称 */
            areaName?: string;
            /**
             * Format: date-time
             * @description 爬取时间
             */
            crawlDate?: string;
            /** @description 深度对应水分百分比(如：918 则 是 91.8%) */
            depthRhMap?: {
                [key: string]: string;
            };
            /** @description 所属空间字段信息 */
            geo?: string;
            /** @description 入库时间 */
            inputTime?: string;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
            /** @description 观测时间 */
            observTime?: string;
            /** @description 站点id */
            stationId?: string;
            /** @description 站点名称 */
            stationName?: string;
        };
        /**
         * HnsfSkInfoDto对象
         * @description 水库基本信息、下游信息Dto对象
         */
        HnsfSkInfoDtoDuiXiang: {
            /** @description 校核洪水位，米 */
            chfllv?: string;
            /** @description 市地区编码 */
            cityCode?: string;
            /** @description 市地区名称 */
            cityName?: string;
            /** @description 死库容，万立米 */
            ddst?: string;
            /** @description 死水位，米 */
            ddwtlv?: string;
            /** @description 坝址所在地点 */
            dmstatpl?: string;
            /** @description 下游信息 */
            downStreamInfo?: string;
            /** @description 设计洪水位，米 */
            dsfllv?: string;
            /** @description 调节库容库容 */
            efst?: string;
            /**
             * @description 工程等别
             * @enum {string}
             */
            encl?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
            /** @description 工程等别名称 */
            enclName?: string;
            /** @description 水库id */
            id?: string;
            /** @description 水库名称 */
            name?: string;
            /** @description 正常水位，米 */
            nrwtlv?: string;
            /** @description 调洪库容库容 */
            rgflrscp?: string;
            /** @description 人工报讯站编码 */
            skcd?: string;
            /** @description 自动测站编码 */
            stcd?: string;
            /** @description 总库容，万立米 */
            ttst?: string;
            /** @description 管理单位 */
            unit?: string;
        };
        /**
         * HnsfSkInfo对象
         * @description 海南三防信息服务网_水库数据
         */
        HnsfSkInfoDuiXiang: {
            /** @description 地区编码 */
            areaCode?: string;
            /** @description 校核洪水位，米 */
            chfllv?: string;
            /** @description 市地区编码 */
            cityCode?: string;
            /** @description 市地区名称 */
            cityName?: string;
            /** @description 编码 */
            code?: string;
            /** @description 爬取时间 */
            dataLoadTime?: string;
            /** @description 死库容，万立米 */
            ddst?: string;
            /** @description 死水位，米 */
            ddwtlv?: string;
            /** @description 坝址所在地点 */
            dmstatpl?: string;
            /** @description 集水面积 */
            drbsar?: string;
            /** @description 设计洪水位，米 */
            dsfllv?: string;
            /** @description 调节库容库容 */
            efst?: string;
            /**
             * @description 工程等别
             * @enum {string}
             */
            encl?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
            /** @description 工程等别名称 */
            enclName?: string;
            /** @description 集雨面积 */
            geometry?: string;
            /** @description 主键 */
            id?: string;
            labelMaxZoom?: string;
            labelMinZoom?: string;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lng?: string;
            maxZoom?: string;
            /** @description 水库名称 */
            name?: string;
            /** @description 正常水位，米 */
            nrwtlv?: string;
            /** @description 调洪库容库容 */
            rgflrscp?: string;
            /** @description 调节特性 */
            rsadch?: string;
            /** @description 人工报讯站编码 */
            skcd?: string;
            /** @description 自动测站编码 */
            stcd?: string;
            /** @description 总库容，万立米 */
            ttst?: string;
            /** @description 管理单位 */
            unit?: string;
        };
        /**
         * HnsfSsyq对象
         * @description 海南气象信息服务网_实时雨晴数据
         */
        HnsfSsyqDuiXiang: {
            /** @description 数据加载时间 */
            dataLoadTime?: string;
            /** @description 主键 */
            id?: string;
            /** @description 雨量，毫米/小时 */
            rainfallValue?: string;
            /** @description 站点编号 */
            stateId?: string;
            /** @description 时间 */
            time?: string;
            /** @description 雨量，毫米/小时,兼容旧的字段 */
            val?: string;
        };
        /**
         * HnsfXqxx对象
         * @description 海南三防信息服务网_水情信息
         */
        HnsfXqxxDuiXiang: {
            /** @description 河流id */
            basinId?: string;
            /** @description 河流名称 */
            basinName?: string;
            /** @description 当前库容，万立米 */
            capacity?: string;
            /** @description 爬取时间 */
            dataLoadTime?: string;
            /** @description 爬取时间(已去掉) */
            dataTime?: string;
            ensureVal?: string;
            floodVal?: string;
            /** @description 主键 */
            id?: string;
            /** @description 内部水位 */
            inVal?: string;
            /**
             * @description 是否接近保证水位
             * @example false
             */
            isClosedEnsureVal?: boolean;
            /** @description 是否超过汛限水位 */
            isOver?: string;
            /**
             * @description 是否超过保证水位
             * @example false
             */
            isOverEnsureVal?: boolean;
            /**
             * @description 是否50年一遇水位
             * @example false
             */
            isOverHighestVal?: boolean;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lng?: string;
            /** @description 站点名称 */
            name?: string;
            /** @description 超出水位量 */
            overEnsure?: string;
            /** @description 超出水位名称 */
            overName?: string;
            /** @description 超出水位类型 */
            overType?: string;
            /** @description 河流名称 */
            riverName?: string;
            /** @description 站点编号 */
            stateId?: string;
            /** @description 时间 */
            time?: string;
            /** @description 水位，米 */
            val?: string;
            /** @description 趋势(4-降，5-升，6-平) */
            waterPotential?: string;
        };
        /**
         * HnsfXxswJjsw对象
         * @description 海南三防信息服务网_汛限水位_警戒水位
         */
        HnsfXxswJjswDuiXiang: {
            /** @description 坝顶高程 */
            damCrestVal?: string;
            /** @description 爬取时间 */
            dataLoadTime?: string;
            /** @description 死水位，米 */
            deadVal?: string;
            /** @description 保证库容，万立米 */
            ensureCapacity?: string;
            /** @description 正常高水位/保证水位，米 */
            ensureVal?: string;
            /** @description 历史最高库水位出现时间 */
            highestTime?: string;
            /** @description 历史最高库水位，米 */
            highestVal?: string;
            /** @description 测站编码 */
            id?: string;
            /** @description 是否显示分钟 */
            isMinute?: string;
            /** @description 可纳库容 */
            knkr?: string;
            /** @description 可纳雨量 */
            knyl?: string;
            /** @description 历史最低库水位出现时间 */
            lowestTime?: string;
            /** @description 历史最低库水位，米 */
            lowestVal?: string;
            /** @description 坝顶高程 */
            spillwayHeight?: string;
            /** @description 溢洪道纬度 */
            spillwayLat?: string;
            /** @description 溢洪道经度 */
            spillwayLng?: string;
            /**
             * @description 指标类型 （reservoir:水库/river:河流)
             * @enum {string}
             */
            type?: "RESERVOIR" | "RIVER";
            /** @description 汛限库容，万立米 */
            warnCapacity?: string;
            /** @description 汛限水位/警戒水位 */
            warnVal?: string;
            /** @description 是否有闸 */
            withGate?: string;
        };
        /** LdhyybReq */
        LdhyybReq: {
            /**
             * Format: int32
             * @description 月份
             */
            month?: number;
            /**
             * Format: int32
             * @description 年份
             */
            year?: number;
        };
        /**
         * NljsdDto对象
         * @description 内涝积水点Dto
         */
        NljsdDtoDuiXiang: {
            /** @description 市县 */
            admn?: string;
            /** @description 影响区域 */
            affectArea?: string;
            /** @description 行政区划代码 */
            areaCode?: string;
            /** @description 维度 */
            lat?: string;
            /** @description 经度 */
            lng?: string;
            /** @description 名称 */
            name?: string;
            /** @description 影响区域 */
            remark?: string;
        };
        /**
         * PageParam«LdhyybReq»
         * @description 分页参数
         */
        PageParamLdhyybReq: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["LdhyybReq"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PmtInfoDto对象
         * @description 水库剖面图信息Dto对象
         */
        PmtInfoDtoDuiXiang: {
            /** @description 当前库容，万立米 */
            capacity?: string;
            /**
             * Format: date-time
             * @description 时间
             */
            crawlDate?: string;
            /** @description 坝顶高程 */
            damCrestVal?: string;
            /** @description 死水位，米 */
            deadVal?: string;
            /** @description 坝址所在地点 */
            dmstatpl?: string;
            /** @description 正常高水位/保证水位，米 */
            ensureVal?: string;
            /** @description 可纳雨量，mm */
            knyl?: string;
            /** @description 当前水位，米 */
            val?: string;
            /** @description 汛限水位/警戒水位 */
            warnVal?: string;
        };
        /** QhycgbDataVo */
        QhycgbDataVo: {
            /** @description 文章名称 */
            docName?: string;
            /** @description 文章路径 */
            docPath?: string;
            /** @description 录入时间 */
            inputTime?: string;
        };
        /** QhycgbListVo */
        QhycgbListVo: {
            /** @description 文章数据列表 */
            docData?: components["schemas"]["QhycgbDataVo"][];
            /** @description 年份 */
            year?: string;
        };
        /** QxgcsjVo */
        QxgcsjVo: {
            /**
             * Format: date-time
             * @description 爬取时间
             */
            crawlDate?: string;
            /** @description 关联站信息索引 */
            fkStationinfoSeq?: string;
            ground?: string;
            /** @description 唯一主键 */
            id?: string;
            /** @description 维度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
            /** @description 观测时间 */
            observTime?: string;
            /** @description 累计降水量 */
            rainFallHour?: string;
            /** @description 相对湿度 */
            relativeHumidity?: string;
            /** @description 下标 */
            rowIndex?: string;
            /** @description 站点id */
            stationId?: string;
            /** @description 站名 */
            stationName?: string;
            /** @description 最大温度 */
            tempMax?: string;
            /** @description 最小温度 */
            tempMin?: string;
            /** @description 当前温度 */
            tempNow?: string;
            /** @description 记录时间 */
            time?: string;
            vis?: string;
            /** @description 当前风向 */
            windDirectionNow?: string;
            /** @description 当前风速 */
            windSpeedNow?: string;
        };
        /**
         * ReservoirAqzyDto对象
         * @description 水库安全转移Dto对象
         */
        ReservoirAqzyDtoDuiXiang: {
            /** @description 淹没村庄 */
            floodVillage?: string;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
            /** @description 安全点名称 */
            placeName?: string;
        };
        /**
         * ReservoirCompareInfoDto对象
         * @description 水库蓄水量比较信息Dto对象
         */
        ReservoirCompareInfoDtoDuiXiang: {
            /** @description 地区编码 */
            areaCode?: string;
            /** @description 库容增减(当前库容-比较库容)，万立米 */
            changeCapacity?: string;
            /** @description 市地区编码 */
            cityCode?: string;
            /** @description 市地区名称 */
            cityName?: string;
            /** @description 比较库容，万立米（指定时间蓄水量）--水情 */
            compareCapacity?: string;
            /** @description 距正常库容（正常库容-当前库容），万立米 */
            compareNomalCapacity?: string;
            /** @description 比较水位，米（指定时间）--水情 */
            compareWaterLevel?: string;
            /** @description 当前库容（及当前蓄水量），万立米--水情 */
            currCapacity?: string;
            /** @description 当前水位，米--水情 */
            currWaterLevel?: string;
            /** @description 坝址所在地点 */
            dmstatpl?: string;
            /**
             * @description 工程等别
             * @enum {string}
             */
            encl?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
            /** @description 工程等别名称 */
            enclName?: string;
            /** @description 水库代码 */
            id?: string;
            /** @description 水库名称 */
            name?: string;
            /** @description 正常库容（及当前蓄水量），万立米--汛限/警戒 */
            nomalCapacity?: string;
            /** @description 正常高水位，米--汛限/警戒 */
            nomalWaterLevel?: string;
            /** @description 累计雨量（指定时间区间）--雨情 */
            rainfall?: string;
            /** @description 人工报讯站编码 */
            skcd?: string;
            /** @description 自动测站编码 */
            stcd?: string;
            /** @description 总库容（及最大蓄水量），万立米--水库信息 */
            totalCapacity?: string;
            /** @description 管理单位 */
            unit?: string;
            /** @description 蓄水率（当前库容/正常库容*100%），百分比 */
            waterStorageRatio?: string;
        };
        /**
         * ReservoirCompareReq对象
         * @description 水库蓄水量比较请求Req对象
         */
        ReservoirCompareReqDuiXiang: {
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
        };
        /**
         * ReservoirDetailDto对象
         * @description 水情详细信息Dto对象
         */
        ReservoirDetailDtoDuiXiang: {
            /** @description 剖面图信息 */
            pmt?: components["schemas"]["PmtInfoDtoDuiXiang"];
            /** @description 水库信息 */
            skInfo?: components["schemas"]["HnsfSkInfoDuiXiang"];
            /** @description 水情信息集合 */
            sqxxs?: components["schemas"]["HnsfXqxxDuiXiang"][];
            /** @description 余量信息集合 */
            ssyqs?: components["schemas"]["HnsfSsyqDuiXiang"][];
            /** @description 汛限水位/警戒水位信息集合 */
            xxswJjsws?: components["schemas"]["HnsfXxswJjswDuiXiang"][];
        };
        /**
         * ResourceSearchFormDTO
         * @description 查询入参
         */
        ResourceSearchFormDTO: {
            /**
             * Format: double
             * @description 距离,单位:km,必须填写经度和维度才会查询
             */
            distance?: number;
            /** @description 数据id */
            id?: string;
            /** @description 关键字 */
            keyword?: string;
            /**
             * Format: double
             * @description 纬度
             */
            latitude?: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude?: number;
            /** @description 查询内容 */
            queryContent?: string;
            /** @description 查询内容方式：拆分匹配-match；顺序匹配-like（默认） */
            queryContentType?: string;
            /**
             * @description 树节点点击类型
             * @enum {string}
             */
            type?: "CSAQZDGZQY" | "DXFMKS" | "LYJSP" | "MODEL" | "SLFHGZW" | "SPPT" | "YYYLDSP" | "ZDXSK";
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 年度 */
            year?: string;
        };
        /** Result */
        Result: {
            /** Format: int32 */
            code?: number;
            data?: Record<string, never>;
            message?: string;
        };
        /** Result«boolean» */
        Resultboolean: {
            /** Format: int32 */
            code?: number;
            data?: boolean;
            message?: string;
        };
        /** Result«DsHnqxfwQxgcsjVo» */
        ResultDsHnqxfwQxgcsjVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["DsHnqxfwQxgcsjVo"];
            message?: string;
        };
        /** Result«FLCGribDataDto» */
        ResultFLCGribDataDto: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["FLCGribDataDto"];
            message?: string;
        };
        /** Result«ForestFireListGroupVo对象» */
        ResultForestFireListGroupVoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireListGroupVoDuiXiang"];
            message?: string;
        };
        /** Result«ForestFireVo对象» */
        ResultForestFireVoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireVoDuiXiang"];
            message?: string;
        };
        /** Result«ForestStationVo对象» */
        ResultForestStationVoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestStationVoDuiXiang"];
            message?: string;
        };
        /** Result«HnsfSkInfoDto对象» */
        ResultHnsfSkInfoDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HnsfSkInfoDtoDuiXiang"];
            message?: string;
        };
        /** Result«int» */
        Resultint: {
            /** Format: int32 */
            code?: number;
            /** Format: int32 */
            data?: number;
            message?: string;
        };
        /** Result«List«DeviceGroup对象»» */
        ResultListDeviceGroupDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["DeviceGroupDuiXiang0"][];
            message?: string;
        };
        /** Result«List«DeviceOnlineVo»» */
        ResultListDeviceOnlineVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["DeviceOnlineVo"][];
            message?: string;
        };
        /** Result«List«DsHnqxfwQxgcsjDto对象»» */
        ResultListDsHnqxfwQxgcsjDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["DsHnqxfwQxgcsjDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«风险报告对象»» */
        ResultListFengXianBaoGaoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["FengXianBaoGaoDuiXiangRes"][];
            message?: string;
        };
        /** Result«List«FkxldDataVo»» */
        ResultListFkxldDataVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["FkxldDataVo"][];
            message?: string;
        };
        /** Result«List«FLCDateListVo»» */
        ResultListFLCDateListVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["FLCDateListVo"][];
            message?: string;
        };
        /** Result«List«ForestFireListVo对象»» */
        ResultListForestFireListVoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireListVoDuiXiang"][];
            message?: string;
        };
        /** Result«List«ForestFireVo对象»» */
        ResultListForestFireVoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestFireVoDuiXiang"][];
            message?: string;
        };
        /** Result«List«ForestStationVo对象»» */
        ResultListForestStationVoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ForestStationVoDuiXiang"][];
            message?: string;
        };
        /** Result«List«复工复产vo对象»» */
        ResultListFuGongFuChanvoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["FuGongFuChanvoDuiXiang"][];
            message?: string;
        };
        /** Result«List«HKEventsListVo对象»» */
        ResultListHKEventsListVoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HKEventsListVoDuiXiang"][];
            message?: string;
        };
        /** Result«List«HnqxfwTrsfInfoDto对象»» */
        ResultListHnqxfwTrsfInfoDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HnqxfwTrsfInfoDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«HnsfSkInfo对象»» */
        ResultListHnsfSkInfoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HnsfSkInfoDuiXiang"][];
            message?: string;
        };
        /** Result«List«HnsfXqxx对象»» */
        ResultListHnsfXqxxDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HnsfXqxxDuiXiang"][];
            message?: string;
        };
        /** Result«List«NljsdDto对象»» */
        ResultListNljsdDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["NljsdDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«QhycgbListVo»» */
        ResultListQhycgbListVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["QhycgbListVo"][];
            message?: string;
        };
        /** Result«List«QxgcsjVo»» */
        ResultListQxgcsjVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["QxgcsjVo"][];
            message?: string;
        };
        /** Result«List«ReservoirAqzyDto对象»» */
        ResultListReservoirAqzyDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ReservoirAqzyDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«ReservoirCompareInfoDto对象»» */
        ResultListReservoirCompareInfoDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ReservoirCompareInfoDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«RiskEstimate对象»» */
        ResultListRiskEstimateDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["RiskEstimateDuiXiang"][];
            message?: string;
        };
        /** Result«List«RiverWarningDetailDto对象»» */
        ResultListRiverWarningDetailDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["RiverWarningDetailDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«SddwDataVo»» */
        ResultListSddwDataVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SddwDataVo"][];
            message?: string;
        };
        /** Result«List«生产状态监测vo对象»» */
        ResultListShengChanZhuangTaiJianCevoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ShengChanZhuangTaiJianCevoDuiXiang"][];
            message?: string;
        };
        /** Result«List«StatisticReservoirCityTypeDto对象»» */
        ResultListStatisticReservoirCityTypeDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["StatisticReservoirCityTypeDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«StatisticReservoirTypeDto对象»» */
        ResultListStatisticReservoirTypeDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["StatisticReservoirTypeDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«string»» */
        ResultListstring: {
            /** Format: int32 */
            code?: number;
            data?: string[];
            message?: string;
        };
        /** Result«List«TfDistanceVo»» */
        ResultListTfDistanceVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TfDistanceVo"][];
            message?: string;
        };
        /** Result«List«TfListVo»» */
        ResultListTfListVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TfListVo"][];
            message?: string;
        };
        /** Result«List«铁塔数据vo对象»» */
        ResultListTieTaShuJuvoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TieTaShuJuvoDuiXiang"][];
            message?: string;
        };
        /** Result«List«铁塔统计数据vo对象»» */
        ResultListTieTaTongJiShuJuvoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TieTaTongJiShuJuvoDuiXiang"][];
            message?: string;
        };
        /** Result«List«WaterDetailDto对象»» */
        ResultListWaterDetailDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WaterDetailDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«用电统计vo对象»» */
        ResultListYongDianTongJivoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["YongDianTongJivoDuiXiang"][];
            message?: string;
        };
        /** Result«List«渔船基本信息vo对象»» */
        ResultListYuChuanJiBenXinXivoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["YuChuanJiBenXinXivoDuiXiang"][];
            message?: string;
        };
        /** Result«List«YyyldListVo»» */
        ResultListYyyldListVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["YyyldListVo"][];
            message?: string;
        };
        /** Result«List«ZHSWSkScaleStatDto对象»» */
        ResultListZHSWSkScaleStatDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkScaleStatDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«ZHSWSkSyqDto对象»» */
        ResultListZHSWSkSyqDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkSyqDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«ZHSWSkVideoDto对象»» */
        ResultListZHSWSkVideoDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkVideoDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«ZHSWSkXslDto对象»» */
        ResultListZHSWSkXslDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkXslDtoDuiXiang"][];
            message?: string;
        };
        /** Result«List«ZHSWSkZrrDto对象»» */
        ResultListZHSWSkZrrDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkZrrDtoDuiXiang"][];
            message?: string;
        };
        /** Result«Map«string,List«HnqxfwTrsfInfoDto对象»»» */
        ResultMapstringListHnqxfwTrsfInfoDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: {
                [key: string]: components["schemas"]["HnqxfwTrsfInfoDtoDuiXiang"][];
            };
            message?: string;
        };
        /** Result«Map«string,List«HnsfSkInfo对象»»» */
        ResultMapstringListHnsfSkInfoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: {
                [key: string]: components["schemas"]["HnsfSkInfoDuiXiang"][];
            };
            message?: string;
        };
        /** Result«Map«string,List«WaterDetailDto对象»»» */
        ResultMapstringListWaterDetailDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: {
                [key: string]: components["schemas"]["WaterDetailDtoDuiXiang"][];
            };
            message?: string;
        };
        /** Result«ReservoirDetailDto对象» */
        ResultReservoirDetailDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ReservoirDetailDtoDuiXiang"];
            message?: string;
        };
        /** Result«string» */
        Resultstring: {
            /** Format: int32 */
            code?: number;
            data?: string;
            message?: string;
        };
        /** Result«TfDetailVo» */
        ResultTfDetailVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TfDetailVo"];
            message?: string;
        };
        /** Result«WarningTypeDataVo» */
        ResultWarningTypeDataVo: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WarningTypeDataVo"];
            message?: string;
        };
        /** Result«ZHSWSkBaseDto对象» */
        ResultZHSWSkBaseDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkBaseDtoDuiXiang"];
            message?: string;
        };
        /** Result«ZHSWSkInfoDto对象» */
        ResultZHSWSkInfoDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkInfoDtoDuiXiang"];
            message?: string;
        };
        /** Result«ZHSWSkSwxxDto对象» */
        ResultZHSWSkSwxxDtoDuiXiang: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ZHSWSkSwxxDtoDuiXiang"];
            message?: string;
        };
        /**
         * RiskEstimate对象
         * @description 终验，风险评估
         */
        RiskEstimateDuiXiang: {
            /**
             * Format: int32
             * @description id
             */
            id?: number;
            /** @description 名称 */
            name?: string;
            /** @description url */
            url?: string;
        };
        /**
         * RiverWarningDetailDto对象
         * @description 河流预警明细Dto对象
         */
        RiverWarningDetailDtoDuiXiang: {
            basinName?: string;
            /** Format: int32 */
            warningCount?: number;
            warningData?: components["schemas"]["WaterDetailDtoDuiXiang"][];
        };
        /** SddwDataVo */
        SddwDataVo: {
            /** @description 时 */
            hour?: string;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
            /**
             * Format: bigdecimal
             * @description 强度
             */
            strength?: number;
        };
        /**
         * 生产状态监测vo对象
         * @description 生产状态监测vo对象
         */
        ShengChanZhuangTaiJianCevoDuiXiang: {
            /** @description 行业类型 */
            industryType?: string;
            /**
             * Format: int32
             * @description 是否异常(0-正常,1-异常)
             */
            isUnusal?: number;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 企业名称 */
            orgName?: string;
            /** @description 企业用电状态名称 */
            qyydztmc?: string;
            /** @description 24小时日用电量 */
            rydl24?: string[];
            /** @description 日用电量指数 */
            rydlzs?: string;
            /** @description 状态 */
            state?: string;
            /** @description 异常描述 */
            unusal?: string;
        };
        /**
         * StatisticReservoirCityTypeDto对象
         * @description 市县水库工程类型统计信息Dto对象
         */
        StatisticReservoirCityTypeDtoDuiXiang: {
            /** @description 市地区（行政区划）编码 */
            cityCode?: string;
            /** @description 市地区名称 */
            cityName?: string;
            /** @description 空间信息 */
            geo?: string;
            /**
             * Format: int64
             * @description 工程数量
             */
            totalNum?: number;
            /** @description 类型统计信息 */
            typeInfos?: {
                [key: string]: number;
            };
        };
        /**
         * StatisticReservoirTypeDto对象
         * @description 水库工程类型统计信息Dto对象
         */
        StatisticReservoirTypeDtoDuiXiang: {
            /**
             * Format: int64
             * @description 工程数量
             */
            num?: number;
            /**
             * @description 工程等别
             * @enum {string}
             */
            type?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
            /** @description 工程等别编码 */
            typeCode?: string;
            /** @description 工程等别名称 */
            typeName?: string;
        };
        /** TfDetailVo */
        TfDetailVo: {
            /**
             * Format: int32
             * @description 台风预计最大风力
             */
            maxPower?: number;
            /** @description 台风路径明细 */
            tfljDetail?: components["schemas"]["TfljDetailVo"][];
            /** @description 台风预计最大风力的时间 */
            time?: string;
        };
        /** TfDistanceVo */
        TfDistanceVo: {
            /** @description 区 */
            area?: string;
            /** @description 市县 */
            city?: string;
            /**
             * Format: bigdecimal
             * @description 距离
             */
            distance?: number;
            /** @description 乡镇 */
            towns?: string;
        };
        /** TfListVo */
        TfListVo: {
            /** @description 台风起始时间 */
            beginTime?: string;
            /** @description 台风结束时间 */
            endTime?: string;
            /** @description 台风英文名称 */
            enName?: string;
            /** @description 主键id */
            id?: string;
            /** @description 台风中文名称 */
            name?: string;
            /** @description 台风编号 */
            typhoonNo?: string;
        };
        /** TfljDetailVo */
        TfljDetailVo: {
            /** @description 预报 */
            forecast?: string;
            /** @description 主键id */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 移动方向 */
            moveDir?: string;
            /** @description 移动速度 */
            moveSpeed?: string;
            /** @description 台风中心风力 */
            power?: string;
            /** @description 台风中心气压 */
            pressure?: string;
            /** @description 7级 */
            radius7Quad?: string;
            /** @description 10级 */
            radius10Quad?: string;
            /** @description 12级 */
            radius12Quad?: string;
            /** @description 台风中心风速 */
            speed?: string;
            /** @description 强度 */
            strong?: string;
            /** @description 时间 */
            time?: string;
            /** @description 台风编号 */
            typhoonNo?: string;
        };
        /**
         * 铁塔数据vo对象
         * @description 铁塔数据vo对象
         */
        TieTaShuJuvoDuiXiang: {
            /** @description 名称 */
            anaName?: string;
            /** @description 点位 */
            points?: components["schemas"]["ReservoirCompareReqDuiXiang"][];
            /**
             * Format: bigdecimal
             * @description 面积
             */
            regionDamArea?: number;
            /**
             * Format: bigdecimal
             * @description 面积占比
             */
            regionDamAreaP?: number;
            /**
             * Format: int32
             * @description 数量
             */
            regionDamCnt?: number;
            /**
             * Format: bigdecimal
             * @description 数量占比
             */
            regionDamCntP?: number;
        };
        /**
         * 铁塔统计数据vo对象
         * @description 铁塔统计数据vo对象
         */
        TieTaTongJiShuJuvoDuiXiang: {
            /** @description 最新记录时间 */
            date?: string;
            /** @description 统计值 */
            statVal?: string;
            /** @description 标题 */
            title?: string;
            /** @description 单位 */
            unit?: string;
        };
        /** TowerDataReq */
        TowerDataReq: {
            /**
             * Format: date
             * @description 查询时间
             */
            date?: string;
            /**
             * @description 排序类型
             * @enum {string}
             */
            sortType?: "AREA" | "AREA_RATIO" | "QUANTITY" | "QUANTITY_RATIO";
        };
        /** WarningSubTypeDataVo */
        WarningSubTypeDataVo: {
            /** Format: int32 */
            count?: number;
            icon?: string;
            name?: string;
            /** Format: int32 */
            typeId?: number;
        };
        /** WarningTypeDataVo */
        WarningTypeDataVo: {
            subType?: components["schemas"]["WarningSubTypeDataVo"][];
            /** Format: int32 */
            subTypeCount?: number;
            typeName?: string;
        };
        /**
         * WaterDetailDto对象
         * @description 水情详细信息Dto对象
         */
        WaterDetailDtoDuiXiang: {
            /** @description 水库信息 */
            skInfo?: components["schemas"]["HnsfSkInfoDuiXiang"];
            /** @description 实时水情信息 */
            sqxx?: components["schemas"]["HnsfXqxxDuiXiang"];
            /** @description 实时雨情信息 */
            ssyq?: components["schemas"]["HnsfSsyqDuiXiang"];
            /** @description 实时汛限水位/警戒水位信息 */
            xxswJjsw?: components["schemas"]["HnsfXxswJjswDuiXiang"];
        };
        /** WaterStationReq */
        WaterStationReq: {
            /**
             * Format: date
             * @description 起始时间，格式：yyyy-MM-dd
             */
            endDate?: string;
            /** @description 关键字 */
            keyword?: string;
            /**
             * Format: date
             * @description 起始时间，格式：yyyy-MM-dd
             */
            startDate?: string;
        };
        /** WeatherVo */
        WeatherVo: {
            /** @description 大气压 (kPa) */
            atmos?: string;
            /** @description 设备编号 */
            deviceNo?: string;
            /** @description 湿度 (% RH) */
            humidity?: string;
            /** @description 雨量 (mm) */
            rainfall?: string;
            /** @description 温度(℃) */
            temp?: string;
            /** @description 风向 */
            windDirection?: string;
            /** @description 风速(m/s) */
            windSpeed?: string;
        };
        /** YdtjReq */
        YdtjReq: {
            /**
             * Format: date
             * @description 查询日期
             */
            date?: string;
        };
        /**
         * 用电统计vo对象
         * @description 用电统计vo对象
         */
        YongDianTongJivoDuiXiang: {
            /** @description 区域名称 */
            areaName?: string;
            /** @description 非煤矿山用电量 */
            fmksYdl?: string;
            /** @description 非煤矿山用电量占比 */
            fmksYdlzb?: string;
            /** @description 矿山用电量 */
            ksYdl?: string;
            /** @description 矿山用电量占比 */
            ksYdlzb?: string;
            /** @description 统计日期 */
            statDate?: string;
            /** @description 危化品用电量 */
            whpYdl?: string;
            /** @description 危化品用电量占比 */
            whpYdlzb?: string;
        };
        /**
         * 渔船基本信息vo对象
         * @description 渔船基本信息vo对象
         */
        YuChuanJiBenXinXivoDuiXiang: {
            /**
             * Format: bigdecimal
             * @description 航行方向
             */
            cog?: number;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
            /** @description 船舶id */
            mmsi?: string;
            /** @description 名称 */
            name?: string;
            /** @description 状态 */
            navistatName?: string;
            /** @description 船长(分米) */
            shipLen?: string;
            /** @description 船舶类型 */
            shiptypeName?: string;
            /** @description 船宽(分米) */
            shipWidth?: string;
        };
        /** YyyldListVo */
        YyyldListVo: {
            /**
             * Format: int32
             * @description id
             */
            id?: number;
            /** @description 纬度 */
            lat?: string;
            /** @description 经度 */
            lon?: string;
            /** @description 名称 */
            name?: string;
            /** @description 备注 */
            remark?: string;
            /** @description url */
            url?: string;
        };
        /**
         * ZHSWSkBaseDto对象
         * @description 智慧水网-水库基础信息Dto对象
         */
        ZHSWSkBaseDtoDuiXiang: {
            /** @description 地址 */
            address?: string;
            /** @description 当前可用库容 */
            dqkykr?: string;
            /** @description 集雨面积 */
            jymj?: string;
            /** @description 水库类型 */
            sklx?: string;
            /** @description 更新时间 */
            time?: string;
            /** @description 蓄水位 */
            xsw?: string;
            /** @description 24小时蓄水量变化 */
            xsw24?: string;
            /** @description 库容 */
            zkr?: string;
        };
        /**
         * ZHSWSkInfoDto对象
         * @description 智慧水网-水库信息Dto对象
         */
        ZHSWSkInfoDtoDuiXiang: {
            /** @description desc */
            desc?: string;
            /** @description 列表数据 */
            list?: components["schemas"]["ZHSWSkListDtoDuiXiang"][];
        };
        /**
         * ZHSWSkListDto对象
         * @description 智慧水网-水库列表Dto对象
         */
        ZHSWSkListDtoDuiXiang: {
            /** @description 超汛限水位 */
            cxxsw?: string;
            /** @description 规模 */
            engScal?: string;
            /** @description 纬度 */
            hmstLat?: string;
            /** @description 经度 */
            hmstLong?: string;
            /**
             * @description 是否超汛限
             * @example false
             */
            isOver?: boolean;
            /** @description 库上水位 */
            kssw?: string;
            /** @description 模型 */
            model?: components["schemas"]["ZHSWSkModelDtoDuiXiang"];
            /** @description 区域 */
            region?: string;
            /** @description 水库信息 */
            resName?: string;
            /** @description 时间 */
            time?: string;
        };
        /**
         * ZHSWSkModelDto对象
         * @description 智慧水网-水库列表模型Dto对象
         */
        ZHSWSkModelDtoDuiXiang: {
            /** @description 缩略图名称 */
            img?: string;
            /** @description 名称 */
            name?: string;
            /** @description url */
            url?: string;
        };
        /**
         * ZHSWSkScaleStatDto对象
         * @description 智慧水网-水库按规模类型统计Dto对象
         */
        ZHSWSkScaleStatDtoDuiXiang: {
            /**
             * Format: int32
             * @description 数量
             */
            count?: number;
            /** @description 规模值 */
            scale?: string;
            /** @description 规模名称 */
            scaleName?: string;
        };
        /**
         * ZHSWSkSwxxDto对象
         * @description 智慧水网-水库水位详细Dto对象
         */
        ZHSWSkSwxxDtoDuiXiang: {
            /**
             * Format: bigdecimal
             * @description 坝顶高程
             */
            bdgc?: number;
            /**
             * Format: bigdecimal
             * @description 超汛限
             */
            cxx?: number;
            /** @description 规模 */
            engScal?: string;
            /** @description 旱警水位 */
            hjsw?: string;
            /**
             * Format: bigdecimal
             * @description 校核水位
             */
            jhsw?: number;
            /**
             * Format: bigdecimal
             * @description 库上水位
             */
            kssw?: number;
            /**
             * Format: bigdecimal
             * @description 设计水位
             */
            sjsw?: number;
            /**
             * Format: bigdecimal
             * @description 死水位
             */
            ssw?: number;
            /** @description 更新时间 */
            time?: string;
            /**
             * Format: bigdecimal
             * @description 汛限水位
             */
            xxsw?: number;
            /** @description 堰顶高程 */
            ydgc?: string;
            /**
             * Format: bigdecimal
             * @description 正常水位
             */
            zcsw?: number;
        };
        /**
         * ZHSWSkSyqDto对象
         * @description 智慧水网-水库水雨情Dto对象
         */
        ZHSWSkSyqDtoDuiXiang: {
            /**
             * Format: bigdecimal
             * @description 出库流量
             */
            ckll?: number;
            /**
             * Format: bigdecimal
             * @description 旱警流量
             */
            hjll?: number;
            /**
             * Format: bigdecimal
             * @description 库上水位
             */
            kssw?: number;
            /**
             * Format: bigdecimal
             * @description 旱警水位
             */
            ljsw?: number;
            /**
             * Format: bigdecimal
             * @description 入库流量
             */
            rkll?: number;
            /** @description 更新时间 */
            time?: string;
            /**
             * Format: bigdecimal
             * @description 汛限水位
             */
            xxsw?: number;
            /**
             * Format: bigdecimal
             * @description 雨量
             */
            yl?: number;
        };
        /**
         * ZHSWSkVideoDto对象
         * @description 智慧水网-水库视频Dto对象
         */
        ZHSWSkVideoDtoDuiXiang: {
            /** @description channelId */
            channelId?: string;
            /** @description 名称 */
            name?: string;
            /** @description url */
            url?: string;
        };
        /**
         * ZHSWSkXslDto对象
         * @description 智慧水网-水库需水量Dto对象
         */
        ZHSWSkXslDtoDuiXiang: {
            /**
             * Format: bigdecimal
             * @description 库上水位
             */
            kssw?: number;
            /** @description 更新时间 */
            time?: string;
            /**
             * Format: bigdecimal
             * @description 需水量
             */
            xsl?: number;
        };
        /**
         * ZHSWSkZrrDto对象
         * @description 智慧水网-水库责任人Dto对象
         */
        ZHSWSkZrrDtoDuiXiang: {
            /** @description 职务 */
            job?: string;
            /** @description 搜集好 */
            phone?: string;
            /** @description 姓名 */
            realName?: string;
            /** @description 类型 */
            type?: string;
            /** @description 单位 */
            unit?: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    getStatDataUsingGET: {
        parameters: {
            query?: {
                /** @description end */
                end?: string;
                /** @description start */
                start?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDevicesUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListDeviceGroupDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingGET: {
        parameters: {
            query?: {
                /** @description 是否排除视图类型的节点，创建视图选择所属分组时传true，其他情况可不传 */
                excludeView?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListDeviceGroupDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["DeviceGroupSaveDTODuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveViewUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["DeviceGroupRelationSaveDTODuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["DeviceGroupSaveDTODuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    exportUsingGET: {
        parameters: {
            query?: {
                /** @description id */
                id?: string;
                /** @description type */
                type?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get24HourFireCountUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultint"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getAllFireDataUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestFireVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFireDataByIdUsingGET: {
        parameters: {
            query?: {
                /** @description id */
                id?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFireVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFireDataByTimeUsingGET: {
        parameters: {
            query?: {
                /** @description city */
                city?: string;
                /** @description endDateTime */
                endDateTime?: string;
                /** @description name */
                name?: string;
                /** @description startDateTime */
                startDateTime?: string;
                /** @description stationId */
                stationId?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFireListGroupVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFireDetailUsingGET: {
        parameters: {
            query?: {
                /** @description id */
                id?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestFireVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFireListUsingGET_1: {
        parameters: {
            query?: {
                /** @description endDate */
                endDate?: string;
                /** @description startDate */
                startDate?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestFireListVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getStationListUsingGET: {
        parameters: {
            query?: {
                /** @description keyword */
                keyword?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListForestStationVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getStationVideoListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListDeviceOnlineVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getWeatherDataUsingGET: {
        parameters: {
            query?: {
                /** @description deviceNo */
                deviceNo?: string;
                /** @description weatherDataType */
                weatherDataType?: "ATMOS" | "HUMIDITY" | "RAINFALL" | "TEMP" | "WIND_DIRECTION" | "WIND_SPEED";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultForestStationVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFireListUsingGET: {
        parameters: {
            query?: {
                /** @description url */
                url?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getEventsUsingGET: {
        parameters: {
            query?: {
                /** @description endDateTime */
                endDateTime?: string;
                /** @description startDateTime */
                startDateTime?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHKEventsListVoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveFileUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingGET: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingPUT: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingPOST: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingDELETE: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingOPTIONS: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingHEAD: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingPATCH: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    forwardUsingTRACE: {
        parameters: {
            query?: {
                /** @description httpMethod */
                httpMethod?: "DELETE" | "GET" | "HEAD" | "OPTIONS" | "PATCH" | "POST" | "PUT" | "TRACE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": string;
                };
            };
            /** @description No Content */
            204: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFgfcDataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["FgfcReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListFuGongFuChanvoDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getHolidayListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getScztjcDataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["YdtjReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListShengChanZhuangTaiJianCevoDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getStatDataUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTieTaTongJiShuJuvoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getYdtjDataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["YdtjReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListYongDianTongJivoDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    compareReservoirStorageUsingGET: {
        parameters: {
            query?: {
                /** @description 比较时间,空时不比较 */
                compareTime?: string;
                /** @description 雨量统计结束时间（包含），与开始时间空时不获取 */
                rainfallEndTime?: string;
                /** @description 雨量统计开始时间（包含），与结束时间任意空时不获取 */
                rainfallStartTime?: string;
                /** @description 水库名称，模糊约束 */
                reservoirNameLike?: string;
                /** @description 水库工程级别,默认所有 */
                reservoirTypes?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListReservoirCompareInfoDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getBaseInfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 水库编号 */
                reservoirId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultHnsfSkInfoDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDetailUsingGET: {
        parameters: {
            query?: {
                /** @description 结束时间，默认当前时间 */
                endTime?: unknown;
                /** @description 开始时间，默认今天凌晨 */
                startTime?: unknown;
            };
            header?: never;
            path: {
                /** @description 水库编号 */
                reservoirId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultReservoirDetailDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    groupListUsingGET: {
        parameters: {
            query?: {
                /** @description 水库工程级别,默认所有 */
                reservoirTypes?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
            };
            header?: never;
            path: {
                /** @description 分组类型 */
                groupType: "BY_BASIN" | "BY_CITY" | "BY_NONE" | "BY_TYPE";
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultMapstringListHnsfSkInfoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    locationUsingGET: {
        parameters: {
            query?: {
                /** @description 水库工程级别,默认所有 */
                reservoirTypes?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHnsfSkInfoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    statisticCityTypeUsingGET: {
        parameters: {
            query?: {
                /** @description 水库工程级别,默认所有 */
                reservoirTypes?: "FIVE" | "FOUR" | "ONE" | "THREE" | "TWO";
                /** @description 携带空间信息,默认false */
                withGeo?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticReservoirCityTypeDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    typesUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticReservoirTypeDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListRiskEstimateDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDetailUsingGET_1: {
        parameters: {
            query?: {
                /** @description reportId */
                reportId?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListFengXianBaoGaoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingGET_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListFengXianBaoGaoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    removeUsingGET_1: {
        parameters: {
            query?: {
                /** @description id */
                id?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultboolean"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveOrUpdateUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["FengXianBaoGaoDuiXiangReq"][];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    exportExcelUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingGET_3: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListYuChuanJiBenXinXivoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    baseinfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    locationUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    saveBaseinfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listStationUsingGET: {
        parameters: {
            query?: {
                /** @description 返回市县区行政区划空间信息,默认false */
                withGeo?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHnqxfwTrsfInfoDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listStationDataUsingGET: {
        parameters: {
            query?: {
                /** @description 起始时间（包含，最小单位时），默认最新时间或当前时间 */
                observtime?: unknown;
                /** @description 返回市县区行政区划空间信息,默认false */
                withGeo?: boolean;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHnqxfwTrsfInfoDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listStationValuesUsingGET: {
        parameters: {
            query: {
                /** @description 结束时间（不包含,最小单位时），默认最新时间或当前时间 */
                endTime?: unknown;
                /** @description 起始时间（包含，最小单位时），默认最新时间或当前时间 */
                startTime?: unknown;
                /** @description 监测站编号 */
                stationid: unknown;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultMapstringListHnqxfwTrsfInfoDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateStatusUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadLdhyybUsingGET: {
        parameters: {
            query?: {
                /** @description id */
                id?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getCityOfHNUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getGeoByAreaNameUsingGET: {
        parameters: {
            query?: {
                /** @description areaName */
                areaName?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getLdhyybUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamLdhyybReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getLdhyybmxUsingGET: {
        parameters: {
            query?: {
                /** @description id */
                id?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getLytqybUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSxtqybUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getXztqybUsingGET: {
        parameters: {
            query?: {
                /** @description cityStationId */
                cityStationId?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    testUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDataLatestDateUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getStatDataUsingGET_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTieTaTongJiShuJuvoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getTowerDataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["TowerDataReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTieTaShuJuvoDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getReservoirRiverWarningCountUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultWarningTypeDataVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getReservoirWarningDataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WaterStationReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultMapstringListWaterDetailDtoDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getRiverWarningDataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["WaterStationReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListRiverWarningDetailDtoDuiXiang"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    basinLocationUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHnsfXqxxDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    basinStationsUsingGET: {
        parameters: {
            query?: {
                /** @description 监测站类型,默认所有 */
                stationTypes?: "METEOROLOGICAL" | "RAINFALL" | "RESERVOIR_HYDROLOGY" | "RIVER_HYDROLOGY" | "RIVER_LEVEL" | "TIDE_LEVEL";
                /** @description 站点归属类别集合，空时不约束（如水文、气象、山洪、防汛、海南水文）,默认所有 */
                subjections?: string[];
            };
            header?: never;
            path: {
                /** @description 流域编号 */
                bashinId: unknown;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListWaterDetailDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDetailUsingGET_2: {
        parameters: {
            query?: {
                /** @description 结束时间，默认当前时间 */
                endTime?: unknown;
                /** @description 开始时间，默认今天凌晨 */
                startTime?: unknown;
            };
            header?: never;
            path: {
                /** @description stationId */
                stationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultReservoirDetailDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    groupListUsingGET_1: {
        parameters: {
            query?: {
                /** @description 站点归属流域编号,默认所有 */
                basinIds?: string[];
                /** @description 是否只显示预警数据,默认false */
                onlyWarningData?: boolean;
                /** @description 监测站类型,默认所有 */
                stationTypes?: "METEOROLOGICAL" | "RAINFALL" | "RESERVOIR_HYDROLOGY" | "RIVER_HYDROLOGY" | "RIVER_LEVEL" | "TIDE_LEVEL";
                /** @description 站点归属类别集合，空时不约束（如水文、气象、山洪、防汛、海南水文）,默认所有 */
                subjections?: string[];
                /** @description 指定监测起始时间,默认当天8点 */
                time?: unknown;
            };
            header?: never;
            path: {
                /** @description 分组类型 */
                groupType: "BY_BASIN" | "BY_CITY" | "BY_NONE" | "BY_TYPE";
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultMapstringListWaterDetailDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getAqzyInfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 水库编号 */
                skId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListReservoirAqzyDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getJymjInfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 水库编号 */
                skId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getYmfwInfoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 水库编号 */
                skId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListReservoirAqzyDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateStatusUsingGET_1: {
        parameters: {
            query?: {
                /** @description 站点归属流域编号,默认所有 */
                basinIds?: string[];
                /** @description 监测站编号集合,默认所有 */
                stationIds?: string[];
                /** @description 监测站类型集合,默认所有 */
                stationTypes?: "METEOROLOGICAL" | "RAINFALL" | "RESERVOIR_HYDROLOGY" | "RIVER_HYDROLOGY" | "RIVER_LEVEL" | "TIDE_LEVEL";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSkBaseInfoUsingGET: {
        parameters: {
            query?: {
                /** @description resName */
                resName?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultZHSWSkBaseDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSkDetailUsingGET: {
        parameters: {
            query?: {
                /** @description resName */
                resName?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultZHSWSkSwxxDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSkListUsingGET: {
        parameters: {
            query?: {
                /** @description scaleList */
                scaleList?: string[];
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultZHSWSkInfoDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSkScaleStatUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListZHSWSkScaleStatDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSkVideoUsingGET: {
        parameters: {
            query?: {
                /** @description resName */
                resName?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListZHSWSkVideoDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSkZzrUsingGET: {
        parameters: {
            query?: {
                /** @description resName */
                resName?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListZHSWSkZrrDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSYQDetailUsingGET: {
        parameters: {
            query?: {
                /** @description et */
                et?: string;
                /** @description resName */
                resName?: string;
                /** @description st */
                st?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListZHSWSkSyqDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getXSLDetailUsingGET: {
        parameters: {
            query?: {
                /** @description et */
                et?: string;
                /** @description resName */
                resName?: string;
                /** @description st */
                st?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListZHSWSkXslDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    importVideoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
                "application/octet-stream": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    importZzrUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": string;
                "application/octet-stream": string;
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    get24HourStatDataUsingGET: {
        parameters: {
            query?: {
                /** @description type */
                type?: "HUMIDITY" | "TEMP" | "WATER" | "WIND";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListDsHnqxfwQxgcsjDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDistanceDataUsingGET: {
        parameters: {
            query?: {
                /** @description lat */
                lat?: string;
                /** @description lon */
                lon?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTfDistanceVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getFkxldDataUsingGET: {
        parameters: {
            query?: {
                /** @description 指定时间(格式：yyyy-MM-dd) */
                date?: unknown;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListFkxldDataVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getNljsdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListNljsdDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getQhycgbDataUsingGET: {
        parameters: {
            query?: {
                /** @description 气候预测公报类型枚举 */
                qhycgbType?: "EXTEND" | "MONTH" | "SEASON" | "YEAR";
                /** @description 指定年份(选择全部时，不传该参数) */
                year?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListQhycgbListVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getRadarDataUsingGET: {
        parameters: {
            query: {
                /** @description HK_230_JS（海口230公里1小时降水）、HK_230_JBFSL（海口230公里基本反射率），以此类推 */
                radarType: "DF_230_JBFSL" | "DF_230_JBSDT" | "DF_230_ZHFSL" | "DF_460_JBFSL" | "HK_230_JBFSL" | "HK_230_JBSDT" | "HK_230_JS" | "HK_230_ZHFSL" | "HK_460_JBFSL" | "LDPT" | "SY_150_JBFSL" | "SY_300_JBFSL" | "SY_300_JBSDT" | "SY_75_JBFSL" | "XS_150_JBFSL" | "XS_150_JBSDT" | "XS_300_JBFSL" | "XS_75_JBFSL";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSddwDataUsingGET: {
        parameters: {
            query?: {
                /** @description 指定时间(格式：yyyy-MM-dd) */
                date?: unknown;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSddwDataVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getStatDataOfQJSUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListDsHnqxfwQxgcsjDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getTfDetailUsingGET: {
        parameters: {
            query?: {
                /** @description typhoonNo */
                typhoonNo?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTfDetailVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getTfListUsingGET: {
        parameters: {
            query?: {
                /** @description year */
                year?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTfListVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getYsyczUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListNljsdDtoDuiXiang"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getYtldDataUsingGET: {
        parameters: {
            query: {
                /** @description FY2G_SWHW-风云2G三维红外；FY4A_PMHW-风云4A平面红外；JGWX-极轨卫星，以此类推 */
                ytldType: "FY2G_KJG" | "FY2G_PMHW" | "FY2G_SWHW" | "FY4A_KJG" | "FY4A_PMHW" | "JGWX";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getYyyldListUsingGET: {
        parameters: {
            query?: {
                /** @description keyword */
                keyword?: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListYyyldListVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    initQxDataUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listAndStatisticUsingGET: {
        parameters: {
            query?: {
                /** @description 结束时间（包含,最小单位时），默认最新时间或当前时间 */
                endTime?: unknown;
                /** @description 类型(强降水、温度、湿度、大风) */
                natureDisasterType?: "HUMIDITY" | "TEMP" | "WATER" | "WIND";
                /** @description 起始时间（包含，最小单位时），默认最新时间或当前时间 */
                startTime?: unknown;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultDsHnqxfwQxgcsjVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listStationValuesUsingGET_1: {
        parameters: {
            query: {
                /** @description 结束时间（包含,最小单位时），默认最新时间或当前时间 */
                endTime?: unknown;
                /** @description 起始时间（包含，最小单位时），默认最新时间或当前时间 */
                startTime?: unknown;
                /** @description 监测站编号 */
                stationid: unknown;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListQxgcsjVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    grib2DataUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["FLCGribDataReq"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultFLCGribDataDto"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    grib2DataTimeListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description 1001 */
                code: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListFLCDateListVo"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    updateStatusUsingGET_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getLatestNewsUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Resultstring"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDetailObjectUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ResourceSearchFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ResourceSearchFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getTreeListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ResourceSearchFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    initSlfhgzUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    initSlfhgz2UsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    intiYyylUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    modelUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getChInterfacesUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["Result"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
}
