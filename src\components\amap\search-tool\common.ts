import { dmsDecode, gcj02ToWgs84, wgs84ToGcj02 } from '@x3d/all';

import * as AMapPlugin from '../AMapPlugin';

// 地点关键字是否是一个经纬坐标，如果是  则返回坐标，类型为wgs84
function keywordsAsLngLat(keywords: string) {
  try {
    // 十进制
    const regexp = /^-?(\d|[1-9]\d|1[0-7]\d|180)((\.)(\d+))?,-?(\d|[1-8]\d|90)((\.)(\d+))?$/;
    if (keywords?.match(regexp)) {
      const [lng, lat] = keywords.split(',');
      return [+lng, +lat] as [number, number];
    }
  }
  catch { }
  try {
    // 度分秒
    const regexp2
            = /^((\d|[1-9]\d|1[0-7]\d)°(\d|[0-5]\d)′(\d|[0-5]\d)(\.\d{1,2})?″?)|(180°0′0″?)[EW],((\d|[1-8]\d)°(\d|[0-5]\d)′(\d|[0-5]\d)(\.\d{1,2})?″?$)|(90°0′0″?)[NS]$/i;
    if (keywords?.match(regexp2)) {
      const [lng, lat] = keywords.split(',');
      return [dmsDecode(lng), dmsDecode(lat)] as [number, number];
    }
  }
  catch { }
  return null;
}

export interface POIModel {
  id: string;
  name: string;
  longitude: number;
  latitude: number;
  address: string;
}

export interface POIOptions {
  label: string;
  value: POIModel;
}
function uuid() {
  return (Math.random() * Date.now()).toString();
}

/**
 * 经纬或地名搜索合并
 *
 * keywords 经纬或地名  经纬时为 `longitude,latitude`格式  支持度分秒 wgs84
 *
 * lnglat 如果是经纬解析   则返回解析成功值 wgs84
 *
 * options  poi列表 wgs84
 */

export async function poiSearch(keywords: string) {
  if (!keywords) {
    return null;
  }
  const lngLat = keywordsAsLngLat(keywords);
  let pois: any[] = [];
  // 经纬度搜索
  if (lngLat) {
    const gcj02 = wgs84ToGcj02(lngLat);
    const res = await AMapPlugin.Service.placeAround({
      location: `${gcj02[0]},${gcj02[1]}`,
      offset: 50,
    });
    pois = res?.pois ?? [];
  }
  // 周边搜索
  else {
    const res = await AMapPlugin.Service.placeText({
      keywords,
      offset: 50,
      // region: '海南省',
      // city_limit: true,
    });
    pois = res?.pois ?? [];
  }

  const list: POIModel[]
        = pois?.map((e) => {
          const [lng, lat] = e.location.split(',');
          // 函数间流转，统一用wgs84格式
          const wgs84 = gcj02ToWgs84([+lng, +lat]);
          const { id, name, cityname, adname, address } = e;
          return {
            id,
            name,
            longitude: wgs84[0],
            latitude: wgs84[1],
            address: `${cityname}${cityname === adname ? '' : adname}${address}`,
          };
        }) ?? [];
  const options: POIOptions[] = list.map(e => ({ label: e.name, value: e }));
  if (lngLat) {
    const name = lngLat.join(',');
    options.unshift({
      label: name,
      value: {
        id: uuid(),
        name,
        longitude: lngLat[0],
        latitude: lngLat[1],
        address: '经纬定位',
      },
    });
  }

  return {
    options,
    lngLat,
  };
}

// 导航路线
export interface PathModel {
  distance?: number;
  duration?: number;
  start: POIModel; // 起点
  end: POIModel; // 终点
  pass?: POIModel[]; // 途径点
  restriction: string;
  strategy: string;
  toll_distance: string;
  tolls: string;
  traffic_lights: string;
  steps: StepModel[];
}

/** 导航路段 */
export interface StepModel {
  action: string;
  assistant_action: string;
  distance: number;
  duration: number;
  instruction: string;
  orientation: string;
  polyline: string;
  road: string;
  tolls?: number;
  tmcs?: TmcsModel;
}

/** 路段路况 */
export interface TmcsModel {
  distance: number;
  status: '未知' | '畅通' | '缓行' | '拥堵' | '严重拥堵';
  polyline: string;
}

function getLnglat(point: POIModel) {
  const { longitude, latitude } = point;
  const data = wgs84ToGcj02([longitude, latitude]);
  return `${data[0]},${data[1]}`;
}

/**
 * 导航路径规划
 * points 途径路径，第一个为起点，最后一个为终点，中间point为途径点，只有驾车规划途经点才会生效
 *
 * type driving 驾车  bicycling骑行
 */
export async function pathDirection(points: POIModel[], type: 'driving' | 'bicycling') {
  points = toRaw(points);
  let paths: PathModel[] = [];

  // 起点
  const start = { ...points[0] };
  // 终点
  const end = { ...points.at(-1) };
  // 途径点
  try {
    if (type === 'driving') {
      const pass: POIModel[] = [];

      // 浅拷贝
      pass.push(...points.slice(1, -1).map(e => ({ ...e })));

      const res = await AMapPlugin.Service.directionDriving({
        origin: getLnglat(start),
        destination: getLnglat(end as any),
        waypoints: pass.map(e => getLnglat(e)).join(';'),
        strategy: 10, // 驾车选择策略
      });
      paths = res?.route?.paths ?? [];

      paths.forEach((e) => {
        e.start = start;
        e.end = end as any;
        e.pass = pass;
      });
    }
    else if (type === 'bicycling') {
      const res = await AMapPlugin.Service.directionBicycling({
        origin: getLnglat(start),
        destination: getLnglat(end as any),
      });
      paths = res?.data?.paths ?? [];
      paths.forEach((e) => {
        e.start = start;
        e.end = end as any;
      });
    }
  }
  catch (error) {
    console.error(error);
  }

  return paths;
}
