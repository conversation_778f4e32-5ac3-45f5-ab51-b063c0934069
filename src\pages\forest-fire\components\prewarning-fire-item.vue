<!-- 森林火情预警 列表item -->
<script lang="ts" setup>
import type { ForestFireGroupByStationVoDuiXiang } from '@/genapi/forecast';

defineProps<{ data: ForestFireGroupByStationVoDuiXiang; sort: number }>();
defineComponent({ name: 'PrewarnincgFireItem' });
</script>

<template>
  <div class="prewarning-fire-item">
    <div flex="~ justify-between">
      <div class="title" color="#E2F4FF" font-size="2.4rem" mb="15px">
        {{ sort }}.{{ data.stationName }}
      </div>
      <div v-if="data.count" color="#B8DBF0" font-size="2.2rem">
        预警数量：
        <span color="#FF3F10">{{ data.count }}</span>
      </div>
    </div>
    <div color="#9FB3C6" font-size="2rem">
      最新预警时间：{{ data.latestRecordTime || "当前时间暂无预警信息" }}
    </div>
    <el-divider my="!16px" border-style="dashed" />
    <div class="content">
      <div pb="10px" flex whitespace-nowrap>
        分管负责人：{{ data.yjjfgfjzxm }}
        <tel :show-video="false" icon-size="14px">
          {{ data.yjjfgfjzdh }}
        </tel>
        （{{ data.ssdw }}-{{ data.zw }}）
      </div>
      <div flex>
        值班室电话：<tel :show-video="false" icon-size="14px">
          {{ data.yjjzbsdh }}
        </tel>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.prewarning-fire-item {
  width: 710px;
  height: 194px;
  padding: 15px 24px;
  margin-bottom: 15px;
  font-family: PingFangSC, 'PingFang SC';
  text-align: left;
  cursor: pointer;
  background: rgb(26 93 165 / 10%);
  border-top: 2px solid #2398cd;
  border-radius: 2px;

  .el-divider {
    --el-border-color: #5090ce;
  }

  .content {
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
    color: #b8dbf0;
    text-align: left;
  }

  &:hover,
  &.active {
    background: rgb(26 93 165 / 40%);
  }
}
</style>
