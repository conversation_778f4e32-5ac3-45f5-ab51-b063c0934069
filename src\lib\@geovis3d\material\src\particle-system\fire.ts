import type { ParticleSystemConstructorOptions } from '@/lib/@geovis3d/core';
import { GcParticleSystem } from '@/lib/@geovis3d/core';
import * as Cesium from 'cesium';

import image from './assets/fire.png';

/**
 * 火焰粒子特效系统构造函数参数
 */
export type FireParticleSystemConstructorOptions = Omit<ParticleSystemConstructorOptions, 'image'>;

/**
 * 火焰特效粒子系统
 */
export class FireParticleSystem extends GcParticleSystem {
  constructor(options?: FireParticleSystemConstructorOptions) {
    const gravityScratch = new Cesium.Cartesian3();
    super({
      ...options,
      image,
      startColor: options?.startColor ?? new Cesium.Color(1, 1, 1, 1),
      endColor: options?.endColor ?? new Cesium.Color(0.5, 0, 0, 0),
      startScale: options?.startScale ?? 0,
      endScale: options?.endScale ?? 10,
      minimumParticleLife: options?.minimumParticleLife ?? 1,
      maximumParticleLife: options?.maximumParticleLife ?? 6,
      minimumSpeed: options?.minimumSpeed ?? 1,
      maximumSpeed: options?.maximumSpeed ?? 4,
      imageSize: options?.imageSize ?? new Cesium.Cartesian2(25, 25),
      emissionRate: options?.emissionRate ?? 5,
      lifetime: options?.lifetime ?? 16,
      loop: options?.loop ?? true,
      sizeInMeters: options?.sizeInMeters ?? true,
      emitter: options?.emitter ?? new Cesium.ConeEmitter(Cesium.Math.toRadians(45)),
      updateCallback(p, dt) {
        const position = p.position;
        Cesium.Cartesian3.normalize(position, gravityScratch);
        Cesium.Cartesian3.multiplyByScalar(gravityScratch, 0 * dt, gravityScratch);
        p.velocity = Cesium.Cartesian3.add(p.velocity, gravityScratch, p.velocity);
      },
    });
  }
}
