import { Cartesian3 } from 'cesium';
import { commonControllerGetAreaBoundary, FIRE_FACTOR_BASE_URL, oneMapCtrGetRightLayer, oneMapCtrLoadLayersDetail } from '../api';

const FIRE_COLORS = [
  Cesium.Color.TRANSPARENT,
  Cesium.Color.fromCssColorString('#00e600'),
  Cesium.Color.fromCssColorString('#1089fc'),
  Cesium.Color.fromCssColorString('#fef500'),
  Cesium.Color.fromCssColorString('#feb800'),
  Cesium.Color.fromCssColorString('#fd473a'),
];

export const FIRE_FACTOR_LAYERS = [
  {
    name: '凋落物含水率',
    legendName: '含水率(%)',
    legends: [
      ['>0.6', '#0FD16B'],
      ['0.6', 'rgba(19,191,55,0.8)'],
      ['0.55', 'rgba(19,191,55,0.6)'],
      ['0.5', 'rgba(19,191,55,0.4)'],
      ['0.45', '#90DD0E'],
      ['0.4', '#C2EE08'],
      ['0.35', '#E1ED08'],
      ['0.3', '#FFED08'],
      ['0.25', '#FFD808'],
      ['0.2', '#FFC508'],
      ['0.15', '#FFAA08'],
    ],
    async getLayer() {
      const { data } = await oneMapCtrGetRightLayer({
        areaIsn: '00000000.00000021.',
        areaId: '460000',
        areaId2: '2b36576129924b5496f8d775d40f94ce',
        keyword: '',
        staCode: '',
        type: 'meml',
      });
      return new Cesium.ImageryLayer(
        new Cesium.WebMapServiceImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/evecom/big-screen/geoserver-lh/${data.layerprovince}/wms`,
          layers: `${data.layerid}:${data.layername}`,
          tileWidth: 1699,
          tileHeight: 907,
          rectangle: Cesium.Rectangle.fromDegrees(107.42849746741211, 18.012901797111404, 112.09494967133824, 20.504056681726475),
          tilingScheme: new Cesium.GeographicTilingScheme(),
          parameters: {
            TRANSPARENT: true,
            FORMAT: `image/png`,
          },
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
  {
    name: '土壤含水率',
    legendName: '含水率(%)',
    legends: [
      ['>0.3', '#0FD16B'],
      ['0.25', 'rgba(19,191,55,0.8)'],
      ['0.2', 'rgba(19,191,55,0.6)'],
      ['0.15', '#90DD0E'],
      ['0.1', '#FFC508'],
      ['0.05', '#FFAA08'],
    ],
    async getLayer() {
      const { data } = await oneMapCtrLoadLayersDetail({ type: '10', areaId: '460000' });
      const bbox = data.bbox.split(',').map((item: string) => +item);
      return new Cesium.ImageryLayer(
        new Cesium.SingleTileImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/files/${data.image.replace(`/data/images/layers/`, '')}`,
          rectangle: Cesium.Rectangle.fromDegrees(...bbox),
          tileWidth: 256,
          tileHeight: 256,
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
  {
    name: '地表温度',
    legendName: '湿度(℃)',
    legends: [
      ['>40', 'rgb(224,2,2)'],
      ['37~40', 'rgb(249,86,4)'],
      ['35~37', 'rgb(251,151,145)'],
      ['32~35', 'rgb(251,206,164)'],
      ['28~32', 'rgb(249,241,194)'],
      ['24~28', 'rgb(247,252,153)'],
      ['20~24', 'rgb(189,253,133)'],
      ['16~20', 'rgb(208,253,208)'],
      ['12~16', 'rgb(240,254,239)'],
      ['8~12', 'rgb(212,250,251)'],
      ['4~8', 'rgb(170,233,245)'],
      ['0~4', 'rgb(125,209,249)'],
      ['-4~0', 'rgb(57,160,244)'],
      ['-8~-4', 'rgb(31,117,208)'],
      ['-12~-8', 'rgb(28,93,168)'],
      ['<-12', 'rgb(8,47,132)'],
    ],
    async getLayer() {
      const { data } = await oneMapCtrLoadLayersDetail({ type: '11', areaId: '460000' });
      const bbox = data.bbox.split(',').map((item: string) => +item);

      return new Cesium.ImageryLayer(
        new Cesium.SingleTileImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/files/${data.image.replace(`/data/images/layers/`, '')}`,
          rectangle: Cesium.Rectangle.fromDegrees(...bbox),
          tileWidth: 256,
          tileHeight: 256,
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
  {
    name: '地表湿度',
    legendName: '湿度(%)',
    legends: [
      ['>=90', '#061E78'],
      ['90-80', '#061E78'],
      ['80-70', '#1B3BA7'],
      ['70-60', '#2B5CC2'],
      ['60-50', '#3C7EDC'],
      ['50-40', '#6B9EE0'],
      ['40-30', '#99D2CB'],
      ['30-20', '#99D2CB'],
      ['<20', '#97E8AD'],
    ],
    async getLayer() {
      const { data } = await oneMapCtrLoadLayersDetail({ type: '12', areaId: '460000' });

      const bbox = data.bbox.split(',').map((item: string) => +item);
      return new Cesium.ImageryLayer(
        new Cesium.SingleTileImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/files/${data.image.replace(`/data/images/layers/`, '')}`,
          rectangle: Cesium.Rectangle.fromDegrees(...bbox),
          tileWidth: 256,
          tileHeight: 256,
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
  {
    name: '光照度',
    legendName: '光照度(lx)',
    legends: [
      ['>=5000', '#B1162F'],
      ['3000', '#DB3513'],
      ['2000', '#ED6118'],
      ['1000', '#EF8F19'],
      ['750', '#F1B526'],
      ['500', '#F5D22E'],
      ['300', '#F5D22B'],
      ['200', '#F9F23B'],
      ['150', '#E0F547'],
      ['100', '#BEF247'],
      ['75', '#95EB5B'],
      ['50', '#6AD883'],
      ['25', '#50BF9C'],
      ['10', '#39A399'],
    ],
    async getLayer() {
      const { data } = await oneMapCtrLoadLayersDetail({ type: '13', areaId: '460000' });
      const bbox = data.bbox.split(',').map((item: string) => +item);
      return new Cesium.ImageryLayer(
        new Cesium.SingleTileImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/files/${data.image.replace(`/data/images/layers/`, '')}`,
          rectangle: Cesium.Rectangle.fromDegrees(...bbox),
          tileWidth: 256,
          tileHeight: 256,
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
  {
    name: '日累计降水量',
    legendName: '雨量(mm)',
    legends: [
      ['250', '#750027'],
      ['100', '#950186'],
      ['50', '#0001FB'],
      ['25', '#69C7E9'],
      ['10', '#3CBB3B'],
      ['0.1', '#A4F391'],
      ['0', '#FFFFB9'],
    ],
    async getLayer() {
      const { data } = await oneMapCtrLoadLayersDetail({ type: '14', areaId: '460000' });
      const bbox = data.bbox.split(',').map((item: string) => +item);
      return new Cesium.ImageryLayer(
        new Cesium.SingleTileImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/files/${data.image.replace(`/data/images/layers/`, '')}`,
          rectangle: Cesium.Rectangle.fromDegrees(...bbox),
          tileWidth: 256,
          tileHeight: 256,
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
  {
    name: '火险分级',
    legendName: '火险分级',
    legends: [
      ['极度(五级)', '#fd473a'],
      ['高度(四级)', '#feb800'],
      ['较高(三级)', '#fef500'],
      ['中度(二级)', '#1089fc'],
      ['低度(一级)', '#00e600'],
    ],
    async getDataSource() {
      const { data } = await commonControllerGetAreaBoundary({
        areaCode: '460000',
        areaRank: null,
        incodes: '460105\',\'460203\',\'460204\',\'460205\',\'460400\',\'469001\',\'469002\',\'469005\',\'469006\',\'469007\',\'469021\',\'469022\',\'469023\',\'469024\',\'469025\',\'469026\',\'469027\',\'469028\',\'469029\',\'469030',
        dataFlag: 'rank34',
      });
      const { data: stats } = await oneMapCtrGetRightLayer({ areaIsn: '00000000.00000021.', areaId: '460000', areaId2: '2b36576129924b5496f8d775d40f94ce', areaRank: 2, type: 'hxqht', isGfLayer: true });
      const dataSource = new Cesium.CustomDataSource();
      data?.forEach((item: any) => {
        const positions = Cartesian3.fromDegreesArray(item.coordinate.split(' ').map((e: any) => e.split(',').map((e: any) => +e)).flat());
        const stat = stats.find((e: any) => e.areacode === item.areacode);
        const color = FIRE_COLORS[+stat?.danger || 0];
        const entity = new Cesium.Entity({
          polygon: {
            hierarchy: positions,
            material: color.withAlpha(0.3),
          },
        });
        dataSource.entities.add(entity);
      });
      return dataSource;
    },
  },
  {
    name: '林地植被种类',
    legendName: '植被类型',
    legends: [
      ['常绿针叶林', '#005E00'],
      ['常绿阔叶林', '#008200'],
      ['落叶针叶林', '#00A000'],
      ['落叶阔叶林', '#00C800'],
      ['混交林', '#00F000'],
      ['疏林地', '#46FF46'],
      ['灌木林', '#70FF70'],
      ['草地', '#9BFF9B'],
      ['湿地', '#C8FFC8'],
      ['农田', '#FFFFBE'],
      ['城市建设用地', '#C8C8C8'],
      ['裸地', '#F0DCB4'],
      ['水域', '#A0E6FF'],
    ],
    async getLayer() {
      const { data } = await oneMapCtrLoadLayersDetail({ type: '14', areaId: '460000' });
      const bbox = data.bbox.split(',').map((item: string) => +item);
      return new Cesium.ImageryLayer(
        new Cesium.SingleTileImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/files/${data.image.replace(`/data/images/layers/`, '')}`,
          rectangle: Cesium.Rectangle.fromDegrees(...bbox),
          tileWidth: 256,
          tileHeight: 256,
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
  {
    name: '可燃物含水率',
    legendName: '含水率(%)',
    legends: [
      ['<5', '#FF0000'], // 极度干燥，高火险
      ['5-10', '#FF4500'],
      ['10-15', '#FF8C00'],
      ['15-20', '#FFA500'],
      ['20-25', '#FFD700'],
      ['25-30', '#FFFF00'],
      ['30-35', '#ADFF2F'],
      ['35-40', '#7CFC00'],
      ['40-45', '#32CD32'],
      ['45-50', '#008000'],
      ['50-60', '#006400'],
      ['60-70', '#4682B4'],
      ['70-80', '#1E90FF'],
      ['>80', '#0000FF'], // 非常湿润，低火险
    ],
    async getLayer() {
      const { data } = await oneMapCtrLoadLayersDetail({ type: '14', areaId: '460000' });
      const bbox = data.bbox.split(',').map((item: string) => +item);
      return new Cesium.ImageryLayer(
        new Cesium.SingleTileImageryProvider({
          url: `${FIRE_FACTOR_BASE_URL}/ffmw/files/${data.image.replace(`/data/images/layers/`, '')}`,
          rectangle: Cesium.Rectangle.fromDegrees(...bbox),
          tileWidth: 256,
          tileHeight: 256,
        }),
        {
          alpha: 0.8,
        },
      );
    },
  },
];
