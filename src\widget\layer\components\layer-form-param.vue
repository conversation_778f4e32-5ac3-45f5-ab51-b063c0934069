<script lang="tsx" setup>
import type { MVTConfig, TerrainConfig, TileSetConfig, TMSConfig, UrlTemplateConfig, WMSConfig, WMTSConfig } from '../assets/types';

export interface LayerFormParamProps {
  modelValue?: WMSConfig | WMTSConfig | TMSConfig | UrlTemplateConfig | MVTConfig | TerrainConfig | TileSetConfig;
  type?: string;
}

export interface LayerFormParamEmits {
  (event: 'update:modelValue', data?: WMSConfig | WMTSConfig | TMSConfig | UrlTemplateConfig | TerrainConfig | TileSetConfig): void;
}

defineOptions({ name: 'LayerFormParam' });

const props = defineProps<LayerFormParamProps>();
const emit = defineEmits<LayerFormParamEmits>();

const model = useVModel(props, 'modelValue', emit, { deep: true, defaultValue: {}, clone: true, passive: true });

// watchDeep(
//   () => model.value?.customStyle,
//   (str) => {
//     customStyle.value = JSON.parse(str || '{}');
//   },
//   {
//     immediate: true,
//   },
// );

// watchDeep(
//   () => JSON.stringify(customStyle.value),
//   (str) => {
//     data.value!.customStyle = str;
//   },
// );

const matrixSetOptions = [
  {
    label: 'EPSG:4326(经纬度投影)',
    value: 'EPSG:4326',
  },
  {
    label: 'EPSG:3857(墨卡托投影)',
    value: 'EPSG:3857',
  },
  {
    label: 'EPSG:4490(大地2000投影)',
    value: 'EPSG:4490',
  },
  {
    label: 'default028mm',
    value: 'default028mm',
  },
  {
    label: 'BD09(百度地图)',
    value: 'BD09',
  },
  {
    label: 'GCJ02(高德地图、腾讯地图)',
    value: 'GCJ02',
  },
  {
    label: '自动格网集',
    value: '自动格网集',
  },
];

const formatOptions = [
  {
    label: 'image/jpeg',
    value: 'image/jpeg',
  },
  {
    label: 'image/png',
    value: 'image/png',
  },
];

const titleSizeOptions = [
  {
    label: '256',
    value: 256,
  },
  {
    label: '512',
    value: 512,
  },
];

const FROM_RECORD: Record<string, any> = {
  WMS: () => {
    const data = model as Ref<WMSConfig>;
    return (
      <>
        <ElFormItem label="URL地址">
          <ElInput
            type="textarea"
            rows={8}
            v-model={data.value!.url}
            placeholder="请输入URL地址"
          />
        </ElFormItem>
        <ElFormItem label="图层名称">
          <ElInput
            v-model={data.value.layer}
            placeholder="请输入图层名称"
          />
        </ElFormItem>
        <ElFormItem label="格网集">
          <ElSelectV2
            v-model={data.value.tileMatrixSetID}
            options={matrixSetOptions}
            placeholder="请选择格网集"
          />
        </ElFormItem>
        <ElFormItem label="瓦片格式">
          <ElSelectV2
            v-model={data.value.format}
            options={formatOptions}
            placeholder="请选择瓦片格式"
          />
        </ElFormItem>
        <ElFormItem label="瓦片大小">
          <ElSelectV2
            v-model={data.value.titleSize}
            options={titleSizeOptions}
            placeholder="请选择瓦片大小"
          />
        </ElFormItem>
      </>
    );
  },
  WMTS: () => {
    const data = model as Ref<WMTSConfig>;
    return (
      <>
        <ElFormItem label="URL地址">
          <ElInput
            type="textarea"
            rows={8}
            v-model={data.value!.url}
            placeholder="请输入URL地址"
          />
        </ElFormItem>
        <ElFormItem label="图层名称">
          <ElInput
            v-model={data.value!.layer}
            placeholder="请输入图层名称"
          />
        </ElFormItem>
        <ElFormItem label="格网集">
          <ElSelectV2
            v-model={data.value!.tileMatrixSetID}
            options={matrixSetOptions}
            placeholder="请选择格网集"
          />
        </ElFormItem>
        <ElFormItem label="瓦片格式">
          <ElSelectV2
            v-model={data.value!.format}
            options={formatOptions}
            placeholder="请选择瓦片格式"
          />
        </ElFormItem>
        <ElFormItem label="最小层级">
          <ElInputNumber
            v-model={data.value!.minimumLevel}
            placeholder="请输入最小层级"
            controls-position="right"
            class="w-100%! text-left!"
            min={0}
            max={21}
          />
        </ElFormItem>
        <ElFormItem label="最大层级">
          <ElInputNumber
            v-model={data.value!.maximumLevel}
            placeholder="请输入最大层级"
            controls-position="right"
            class="w-100%! text-left!"
            min={0}
            max={21}
          />
        </ElFormItem>
        <ElFormItem label="样式">
          <ElInput
            v-model={data.value!.style}
            placeholder="请输入样式"
          />
        </ElFormItem>
      </>
    );
  },
  TMS: () => {
    const data = model as Ref<TMSConfig>;
    return (
      <>
        <ElFormItem label="URL地址">
          <ElInput
            type="textarea"
            rows={8}
            v-model={data.value!.url}
            placeholder="请输入URL地址"
          />
        </ElFormItem>
        <ElFormItem label="格网集">
          <ElSelectV2
            v-model={data.value!.tileMatrixSetID}
            options={matrixSetOptions}
            placeholder="请选择格网集"
          />
        </ElFormItem>
        <ElFormItem label="瓦片格式">
          <ElSelectV2
            v-model={data.value!.format}
            options={formatOptions}
            placeholder="请选择瓦片格式"
          />
        </ElFormItem>
      </>
    );
  },
  URLtemp: () => {
    const data = model as Ref<UrlTemplateConfig>;
    return (
      <>
        <ElFormItem label="URL地址">
          <ElInput
            type="textarea"
            rows={8}
            v-model={data.value!.url}
            placeholder="请输入URL地址"
          />
        </ElFormItem>
        <ElFormItem label="格网集">
          <ElSelectV2
            v-model={data.value!.tileMatrixSetID}
            options={matrixSetOptions}
            placeholder="请选择格网集"
          />
        </ElFormItem>
        <ElFormItem label="最小层级">
          <ElInputNumber
            v-model={data.value!.minimumLevel}
            placeholder="请输入最小层级"
            controls-position="right"
            class="w-100%! text-left!"
            min={0}
            max={21}
          />
        </ElFormItem>
        <ElFormItem label="最大层级">
          <ElInputNumber
            v-model={data.value!.maximumLevel}
            placeholder="请输入最大层级"
            controls-position="right"
            class="w-100%! text-left!"
            min={0}
            max={21}
          />
        </ElFormItem>

      </>
    );
  },
  MVT: () => {
    const data = model as Ref<MVTConfig>;
    return (
      <>
        <ElFormItem label="URL地址">
          <ElInput
            type="textarea"
            rows={8}
            v-model={data.value!.url}
            placeholder="请输入URL地址"
          />
        </ElFormItem>
        <ElFormItem label="图层名称">
          <ElInput
            v-model={data.value!.layer}
            placeholder="请输入图层名称"
          />
        </ElFormItem>
        <ElFormItem label="格网集">
          <ElSelectV2
            v-model={data.value!.tileMatrixSetID}
            options={matrixSetOptions}
            placeholder="请选择格网集"
          />
        </ElFormItem>
        <ElFormItem label="最小层级">
          <ElInputNumber
            v-model={data.value!.minimumLevel}
            placeholder="请输入最小层级"
            controls-position="right"
            class="w-100%! text-left!"
            min={0}
            max={21}
          />
        </ElFormItem>
        <ElFormItem label="最大层级">
          <ElInputNumber
            v-model={data.value!.maximumLevel}
            placeholder="请输入最大层级"
            controls-position="right"
            class="w-100%! text-left!"
            min={0}
            max={21}
          />
        </ElFormItem>
        <ElFormItem label="线段颜色">
          <ElColorPicker v-model={data.value.lineColor}></ElColorPicker>
        </ElFormItem>
        <ElFormItem label="填充颜色">
          <ElColorPicker v-model={data.value.fillColor}></ElColorPicker>
        </ElFormItem>
        <ElFormItem label="描边颜色">
          <ElColorPicker v-model={data.value.fillOutlineColor}></ElColorPicker>
        </ElFormItem>
      </>
    );
  },
  cesiumdem: () => {
    const data = model as Ref<TerrainConfig>;
    return (
      <>
        <ElFormItem label="URL地址">
          <ElInput
            type="textarea"
            rows={8}
            v-model={data.value!.url}
            placeholder="请输入URL地址"
          />
        </ElFormItem>

      </>
    );
  },
  倾斜摄影: () => {
    const data = model as Ref<TileSetConfig>;
    return (
      <>
        <ElFormItem label="URL地址">
          <ElInput
            type="textarea"
            rows={8}
            v-model={data.value!.url}
            placeholder="请输入URL地址"
          />
        </ElFormItem>

      </>
    );
  },
};
</script>

<template>
  <el-form :label-width="$vh(88)" p="x-20px">
    <component :is="FROM_RECORD[type]" v-if="type && FROM_RECORD[type]" />
  </el-form>
</template>
