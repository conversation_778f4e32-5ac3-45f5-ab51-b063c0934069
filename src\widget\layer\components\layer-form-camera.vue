<script lang="ts" setup>
import type { CameraConfig } from '../assets/types';
import { useCzViewer } from '@x3d/vue-hooks';

export interface LayerFormCameraProps {
  modelValue?: CameraConfig;
}

export interface LayerFormCameraEmits {
  (event: 'update:modelValue', data?: CameraConfig): void;
}

defineOptions({ name: 'LayerFormCamera' });
const props = defineProps<LayerFormCameraProps>();
const emit = defineEmits<LayerFormCameraEmits>();

const model = useVModel(props, 'modelValue', emit, { deep: true, defaultValue: {}, clone: true, passive: true });

const viewer = useCzViewer();

function setViewer() {
  const { longitude, latitude, height } = viewer.value.camera.positionCartographic;
  const { heading, pitch, roll } = viewer.value.camera;
  model.value!.longitude = Cesium.Math.toDegrees(longitude);
  model.value!.latitude = Cesium.Math.toDegrees(latitude);
  model.value!.height = height;
  model.value!.heading = Cesium.Math.toDegrees(heading);
  model.value!.pitch = Cesium.Math.toDegrees(pitch);
  model.value!.roll = Cesium.Math.toDegrees(roll);
}

function clearViewer() {
  model.value!.longitude = undefined;
  model.value!.latitude = undefined;
  model.value!.height = undefined;
  model.value!.heading = undefined;
  model.value!.pitch = undefined;
  model.value!.roll = undefined;
}
</script>

<template>
  <div flex="~ col">
    <header-title2 my="16px" content="默认相机视图">
      <template #extra>
        <el-button size="small" @click="setViewer">
          采用当前视角
        </el-button>
        <el-button size="small" @click="clearViewer">
          一键清除
        </el-button>
      </template>
    </header-title2>
    <el-form grid="~ cols-2" :label-width="$vh(60)" p="x-20px">
      <el-form-item v-model="model!.heading" label="方向角">
        <el-input-number
          v-model="model!.heading"
          placeholder="请输入"
          controls-position="right"
          :precision="1"
        />
      </el-form-item>
      <el-form-item label="倾斜角">
        <el-input-number
          v-model="model!.pitch"
          controls-position="right"
          placeholder="请输入"
          :precision="1"
        />
      </el-form-item>

      <el-form-item label="滚动角">
        <el-input-number
          v-model="model!.roll"
          controls-position="right"
          placeholder="请输入"
          :precision="1"
        />
      </el-form-item>
      <el-form-item label="高度">
        <el-input-number
          v-model="model!.height"
          controls-position="right"
          placeholder="请输入"
          :precision="1"
        />
      </el-form-item>
      <el-form-item label="经度">
        <el-input-number
          v-model="model!.longitude"
          controls-position="right"
          placeholder="请输入"
          :min="-180"
          :max="180"
          :precision="5"
        />
      </el-form-item>
      <el-form-item label="纬度">
        <el-input-number
          v-model="model!.latitude"
          controls-position="right"
          placeholder="请输入"
          :min="-90"
          :max="90"
          :precision="5"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
