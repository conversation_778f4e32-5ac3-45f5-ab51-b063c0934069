import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84 } from '@/lib/@geovis3d/coordinate';
import * as Cesium from 'cesium';

export default <PlottingControllerOptions>{
  type: 'polygon',
  manualTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
  location: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  interval: { visible: 'loop' },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);

    if (coords.length < 3) {
      entity._cache = null;
      return;
    }
    entity._cache = new Cesium.PolygonHierarchy(positions);
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
