const GEOVISEARTH_GET_TOKEN_URL = import.meta.env.VITE_GEOVISEARTH_GET_TOKEN_URL;
const GEOVISEARTH_URL = import.meta.env.VITE_GEOVISEARTH_URL;

/**
 * 获取星图地球验证数据
 */
export async function getGeovisAuthorization() {
  const GEOVISEARTH_AUTHORIZATION = localStorage.getItem('GEOVISEARTH_AUTHORIZATION');
  let data: any = {};
  if (GEOVISEARTH_AUTHORIZATION) {
    try {
      data = JSON.parse(GEOVISEARTH_AUTHORIZATION);
    }
    catch (error) {
      console.error(error);
    }
  }
  // 判断是否过期
  if (!data.expireTime || +data.expireTime * 1000 <= Date.now()) {
    const { data: res } = await axios.get(GEOVISEARTH_GET_TOKEN_URL);
    data = res.data ?? {};
    localStorage.setItem('GEOVISEARTH_AUTHORIZATION', JSON.stringify(data));
  }
  return {
    secretId: data.secretId,
    clientId: data.clientId,
    expireTime: data.expireTime,
    sign: data.sign,
  };
}

export function getGeovisAuthorizationStr() {
  const GEOVISEARTH_AUTHORIZATION = localStorage.getItem('GEOVISEARTH_AUTHORIZATION')!;

  try {
    const data = JSON.parse(GEOVISEARTH_AUTHORIZATION);
    const { secretId, clientId, expireTime, sign } = data;
    const token = `secretId=${secretId}&clientId=${clientId}&expireTime=${expireTime}&sign=${sign}`;
    return token;
  }
  catch (error) {
    console.error(error);
  }
}

export interface GeoV1DistrictGettreelistOptions {
  /**
   * 行政区编码（可以只传code获取行政区树，如果同时传code和root，code优先，也可以不传参数获取全国树）
   */
  code?: string;
  /**
   * 行政区名称（可以只传root获取行政区树，也可以不传参数获取全国树）
   */
  root?: string;
}

/**
 * 行政区编码查询服务
 * @param options
 */
export async function geoV1DistrictGettreelist(options: GeoV1DistrictGettreelistOptions) {
  const params = await getGeovisAuthorization();
  const { data } = await axios.request({
    url: `${GEOVISEARTH_URL}/geo/v1/district/gettreelist`,
    method: 'get',
    params: {
      ...params,
      ...options,
    },
  });
  return data;
}
