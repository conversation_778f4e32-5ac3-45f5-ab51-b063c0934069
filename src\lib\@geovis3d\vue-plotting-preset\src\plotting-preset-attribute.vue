<!-- 属性面板 -->
<script lang="ts" setup>
import type { DraggableDialog } from '@/lib/@geovis3d/vue-component';
import { TextEllipsis } from '@/lib/@geovis3d/vue-component';
import { PlottingAttributeForm, PlottingAttributeList } from '@/lib/@geovis3d/vue-plotting-form';
import { computed } from 'vue';
import { usePlotInjectState } from './state';

defineOptions({ name: 'PlottingPresetAttribute' });

// ======拖拽定位相关 初始化右侧======
const containerRef = shallowRef<InstanceType<typeof DraggableDialog>>();
const elRef = computed(() => containerRef.value?.elRef);
const position = reactive({ x: 0, y: 0 });
const { width } = useElementSize(elRef, undefined, { box: 'border-box' });
const windowSize = useWindowSize();
watch(width, (width) => {
  position.x = windowSize.width.value - width - 40;
  position.y = 80;
});

const {
  dataSource,
  active,
  plottings,
  setActive,
  currentLayer,
} = usePlotInjectState()!;

// 手动关闭详情面板
const visible = ref(true);

watchArray(
  () => plottings.value ?? [],
  (value, prev) => {
    if (value.length > prev.length) {
      visible.value = true;
    }
  },
);

watch(
  () => !!active.value,
  (bool) => {
    if (bool) {
      visible.value = true;
    }
  },
);

function onclose() {
  setActive(undefined);
  visible.value = false;
}
</script>

<template>
  <teleport v-if="visible && dataSource" ref="containerRef" to="#layout-content-right">
    <basic-card
      class="mb-10px mr-10px"
      :class="$style['plotting-preset-attribute']"
      @close="onclose"
    >
      <template #title>
        <div class="breadcrumb" text="16px!">
          <TextEllipsis class="breadcrumb-button" @click="setActive(undefined)">
            标绘图层
          </TextEllipsis>
          <div v-show="active">
            /
          </div>
          <div v-show="active" class="breadcrumb-label">
            <TextEllipsis>
              {{ active?.name || "未命名标绘" }}
            </TextEllipsis>
          </div>
        </div>
      </template>
      <el-form v-show="currentLayer && !active" pt="20px" px="20px">
        <el-form-item label="图层名称">
          <el-input v-model="currentLayer!.remark" placeholder="请输入图层名称" />
        </el-form-item>
      </el-form>
      <PlottingAttributeForm v-if="active" :data-source="dataSource" />
      <PlottingAttributeList v-else :data-source="dataSource" />
    </basic-card>
  </teleport>
</template>

<style module lang="scss">
.plotting-preset-attribute {
  width: 300px;
  height: calc(100vh - 200px);
  background: url('./assets/background.svg');
  background-size: contain;
}
</style>

<style lang="scss" scoped>
.breadcrumb {
  display: flex;
  overflow: hidden;
  cursor: default;

  > * {
    padding: 0 3px;
  }

  .breadcrumb-button {
    font-weight: bold;
    color: #fff;
    cursor: pointer;
  }

  .breadcrumb-label {
    flex: 1;
    color: rgb(179 179 179);
  }
}

.close-button {
  cursor: pointer;
}
</style>
