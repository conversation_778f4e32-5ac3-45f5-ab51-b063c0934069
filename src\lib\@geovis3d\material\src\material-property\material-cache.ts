import type { CesiumMaterialOptions } from './types';

import * as Cesium from 'cesium';

const Material = Cesium.Material as any;

/**
 * 将材质加入到cesium缓存中
 * @param type
 * @param options
 */
export function setCesiumMaterialCache<
  U extends Record<string, any> = any,
  M extends Cesium.Material = any,
>(type: string, options: CesiumMaterialOptions<U, M>) {
  return Material._materialCache.addMaterial(type, options);
}

/**
 * 从cesium缓存中获取材质
 * @param type
 */
export function getCesiumMaterialCache<
  U extends Record<string, any> = any,
  M extends Cesium.Material = any,
>(type: string): CesiumMaterialOptions<U, M> {
  return Material._materialCache.getMaterial(type);
}
