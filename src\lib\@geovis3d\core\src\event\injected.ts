import * as Cesium from 'cesium';

import { CesiumScreenEventCollection } from './cesium-screen-event-collection';
import { CesiumVectorEventCollection } from './cesium-vector-event-collection';

let inject = false;
if (!inject) {
  Object.defineProperties(Cesium.Scene.prototype, {
    screenEvent: {
      get() {
        this._screenEvent ??= new CesiumScreenEventCollection(this);
        return this._screenEvent;
      },
    },
    vectorEvent: {
      get() {
        this._vectorEvent ??= new CesiumVectorEventCollection();
        return this._vectorEvent;
      },
    },
  });
  inject = true;
}
