<!-- 新增图层目录 -->
<script lang="ts" setup>
import type { LayerInfo } from '../utils/legacy';
import { layerInfoSaveOrUpdateLayerInfoUsingPost } from '@/genapi/cimapi';
import { useAsync } from '@/hooks/use-async';
import { usePromiseModal } from '@/hooks/use-promise-modal';
import { useLayerState } from '../state/layer-state';

defineOptions({ name: 'GroupEditDialog' });

const { refreshTreeList } = useLayerState();
const data = ref<LayerInfo>({});

const { isActive, open, completed, forceClose } = usePromiseModal<void, LayerInfo>(
  (item) => {
    data.value = JSON.parse(JSON.stringify(item)) || {};
  },
);

defineExpose({
  open,
  forceClose,
  completed,
});

const { execute, isLoading } = useAsync(async () => {
  await layerInfoSaveOrUpdateLayerInfoUsingPost({
    data: {
      ...data.value,
      config: JSON.stringify(data.value.config || {}),
    },
  });
  ElMessage.success('保存成功');
  await refreshTreeList();
  completed();
});
</script>

<template>
  <el-dialog
    :model-value="isActive"
    :title="`${data?.id ? '新增分组' : '编辑分组'}`"
    :width="$vh(432)"
    :height="$vh(268)"
  >
    <div flex="~ items-center">
      <span whitespace="nowrap" mr="12px">分组名称</span>
      <el-input
        v-model="data.layerName"
        input-style="height:36px;border-radius:4px;"
        placeholder="请输入目录名称"
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="forceClose('close')">
          取消
        </el-button>
        <el-button type="primary" :loading="isLoading" @click="execute()">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
