<script setup lang="ts">
import type { ComponentSize } from 'element-plus';
import { changeDataDeleteByIdIdUsingGet, changeDataListPageUsingPost } from '@/genapi/cimapi';
import { Loading, Search } from '@element-plus/icons-vue';
import dayjs from 'dayjs';

const currentTab = inject('currentTab');
const currentItem = ref(null);
const searchValue = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const pageSizes = ref([10, 20, 30, 40]);
const size = ref<ComponentSize>('default');
const background = ref(false);
const disabled = ref(false);

const taskTableData = ref([]);
const visibleDrawer = ref(false);
const pagination = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  size: 'default',
  background: false,
});

/**
 * 状态 0: 未开始，1:正在转换， 2: 转换完成， 3:转换出错
 */
const statusMap = {
  0: '未开始',
  1: '正在转换',
  2: '转换完成',
  3: '转换出错',
};

/**
 * 序号
 * @param index 序号
 */
const indexMethod = (index: number) => index + 1;

/**
 * 获取模型转换任务列表
 */
async function getTaskList() {
  const { code, data } = await changeDataListPageUsingPost({
    data: {
      current: pagination.currentPage,
      size: pagination.pageSize,
      extra: {},
      orderConfigList: [
        {
          propertyName: '',
          orderType: '',
        },
      ],
      query: {
        changeTask: searchValue.value,
      },
    },
  });
  if (code === 200) {
    taskTableData.value = data?.records || [];
    pagination.total = Number(data?.total) || 0;
  }
}
getTaskList();

function handleSizeChange(val: number) {
  pageSize.value = val;
  getTaskList();
}
function handleCurrentChange(val: number) {
  currentPage.value = val;
  getTaskList();
}

/**
 * 搜索
 */
function handleSearch() {
  getTaskList();
}
/**
 * 新建转换
 */
function handleCreate() {
  currentTab.value = 'create';
}
/**
 * 详情
 * @param row
 */
function handleDetail(row: any) {
  currentItem.value = row;
  visibleDrawer.value = true;
}

const isLoading = ref(false);
const dialogPublish = ref(false);
const currentPublishItem = ref(null);
/**
 * 发布
 * @param row
 */
function handlePublish(row: any) {
  dialogPublish.value = true;
  currentPublishItem.value = Cesium.clone(row);
}

const copyInput = ref<HTMLInputElement>();
function copyClick() {
  copyInput.value.select();
  document.execCommand('copy');
  ElMessage.success('已复制到粘贴板');
}
/**
 * 删除
 * @param row
 */
async function handleDelete(row: any) {
  const { code } = await changeDataDeleteByIdIdUsingGet({
    path: {
      id: row.id,
    },
  });
  if (code === 200) {
    ElMessage.success('删除成功');
    getTaskList();
  }
}

function cancelClick() {
  visibleDrawer.value = false;
}
function confirmClick() {
  visibleDrawer.value = false;
}
</script>

<template>
  <div class="details-main">
    <div class="convert-title">
      倾斜摄影转换
    </div>
    <div class="search">
      <div>
        <span class="text-14px color-#ffffffD9">任务搜索：</span>
        <el-input
          v-model="searchValue"
          class="ml-10px"
          style="width: 280px"
          placeholder="请输入任务名称"
          clearable
          :prefix-icon="Search"
          @clear="handleSearch"
          @keyup.enter="handleSearch"
        />
      </div>
      <el-button type="primary" @click="handleCreate">
        新建转换
      </el-button>
    </div>
    <div class="table-content">
      <el-table height="100%" :stripe="true" :data="taskTableData">
        <el-table-column type="index" :index="indexMethod" label="序号" width="70" />
        <el-table-column prop="changeTask" label="转换任务名称" width="200" />
        <el-table-column prop="sourceFormat" label="源数据格式" width="140" />
        <el-table-column prop="targetFormat" label="转换后格式" width="140" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            {{ statusMap[scope.row.status as keyof typeof statusMap] }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间">
          <template #default="scope">
            {{ dayjs(scope.row.createdAt).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <!-- 详情 发布 删除 -->
            <el-button type="primary" link @click="handleDetail(scope.row)">
              详情
            </el-button>
            <el-button type="primary" link @click="handlePublish(scope.row)">
              <el-icon v-if="isLoading" class="is-loading" size="16">
                <Loading />
              </el-icon>
              <span v-else>发布</span>
            </el-button>
            <el-popconfirm width="220" icon-color="#626AEF" title="确认删除此任务?" @confirm="handleDelete(scope.row)">
              <template #reference>
                <el-button link type="primary" size="small">
                  删除
                </el-button>
              </template>
              <template #actions="{ confirm, cancel }">
                <el-button size="small" @click="cancel">
                  取消
                </el-button>
                <el-button type="danger" size="small" @click="confirm">
                  确认
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <div class="details-footer">
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :size="size"
        :disabled="disabled"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <el-dialog v-model="dialogPublish" width="450px" :show-close="true">
    <template #header>
      <h4 class="text-20px color-#ffffffD9">
        发布地址
      </h4>
    </template>
    <div>
      <el-row :gutter="10">
        <el-col :span="20">
          <el-input ref="copyInput" v-model="currentPublishItem.publicPath" placeholder="发布地址" />
        </el-col>
        <el-col :span="2">
          <el-button type="primary" @click="copyClick">
            复制
          </el-button>
        </el-col>
      </el-row>
    </div>
    <!-- <template #footer>
      <div class="publish-footer">
        <el-button type="primary" @click="cancelClick">
          取消
        </el-button>
        <el-button type="primary" @click="confirmClick">
          确认
        </el-button>
      </div>
    </template> -->
  </el-dialog>
  <el-drawer v-model="visibleDrawer" direction="rtl">
    <template #header>
      <h4 class="text-20px color-#ffffffD9">
        转换任务详情
      </h4>
    </template>
    <template #default>
      <div v-if="currentItem">
        <div class="form-item">
          <span>任务名称：</span>
          <span>{{ currentItem.changeTask }}</span>
        </div>
        <div class="form-item">
          <span>源数据格式：</span>
          <span>{{ currentItem.sourceFormat }}</span>
        </div>
        <div class="form-item">
          <span>源数据地址：</span>
          <span>{{ currentItem.sourcePath }}</span>
        </div>
        <div class="form-item">
          <span>转换后格式：</span>
          <span>{{ currentItem.targetFormat }}</span>
        </div>
        <div class="form-item">
          <span>转换后地址：</span>
          <span>{{ currentItem.targetPath }}</span>
        </div>
        <div class="form-item">
          <span>创建时间：</span>
          <span>{{ dayjs(currentItem.createdAt).format('YYYY-MM-DD HH:mm:ss') }}</span>
        </div>
        <div class="form-item">
          <span>状态：</span>
          <span>{{ statusMap[currentItem.status as keyof typeof statusMap] }}</span>
        </div>
        <div class="form-item">
          <span>发布地址:</span>
          <span>{{ currentItem.publicPath }}</span>
        </div>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">
          关闭
        </el-button>
        <el-button type="primary" @click="confirmClick">
          确认
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped lang="scss">
.details-main {
  position: relative;
  height: calc(100% - 70px);
  padding: 20px 45px;
}

.details-footer {
  display: flex;
  align-items: center;
  height: 70px;
  padding: 0 45px;
  border-top: 1px solid #2f3238;
}

.convert-title {
  position: relative;
  height: 50px;
  font-size: 20px;
  font-weight: 600;
  line-height: 50px;
  color: #ffffffd9;
  text-indent: 15px;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    width: 5px;
    height: 20px;
    content: '';
    background-color: #4584ff;
    transform: translate(-50%, -50%);
  }
}

.search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  margin: 15px 0;

  // margin-top: 10px;
}

.table-content {
  position: relative;
  box-sizing: border-box;
  height: calc(100% - 150px);
  padding: 10px 0;
}

.form-item {
  height: 40px;
  font-size: 16px;
  line-height: 40px;
  color: #d9d7d7d9;

  > span:first-child {
    display: inline-block;
    width: 130px;
  }

  > span:last-child {
    display: inline-block;
    color: #ffffffd9;
  }
}
</style>
