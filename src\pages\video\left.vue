<script lang="ts" setup>
import { ICON_MAP } from '@/assets/icon/index';
import { toDayjs } from '@/utils/to-dayjs';
import { CzEntity } from '@x3d/all';
import { useCzEntities } from '@x3d/vue-hooks';
import { DEMO_LIST } from './demoList';
import popper from './popper.vue';
import ReportDialog from './report-dialog.vue';

defineOptions({ name: 'Left' });
const show = ref(true);

const current = shallowRef<any>();

useCzEntities(() => {
  return DEMO_LIST.map((item) => {
    const entity = new CzEntity({
      position: Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude),
      billboard: {
        image: ICON_MAP.video,
        scale: 0.7,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
    });
    entity.event.on('LEFT_CLICK', () => {
      current.value = item;
    });
    return entity;
  });
});

const showReportDialog = ref(false);
</script>

<template>
  <layout-left-panel>
    <div class="video-sty" p="b-46px t-64px">
      <div flex="~ col" h="100%" :w="show ? '400px' : '0'" transition="all 300" of="hidden">
        <header-title1 b-b="1px! solid #fff/10%!">
          监控列表
          <template #extra>
            <el-button link>
              <el-icon class="i-material-symbols:close" text="20px! #FFF!" @click="$router.push({ path: '/home' })" />
            </el-button>
          </template>
        </header-title1>
        <el-scrollbar style="max-height: 800px;">
          <div
            v-for="(item, index) in DEMO_LIST"
            :key="index"
            class="scrollbar-item"
            cursor="pointer"
            @click="current = item"
          >
            <el-text un-text="16px! #fff!">
              海南省 {{ toDayjs().format('YYYY年MM月DD日') }} {{ item.name }}
            </el-text>
          </div>
        </el-scrollbar>
        <el-button type="primary" m="x-20px t-10px" size="large" @click="showReportDialog = true">
          报告
        </el-button>
      </div>
    </div>
  </layout-left-panel>
  <popper v-model:item="current" />
  <ReportDialog v-model="showReportDialog" />
</template>

<style lang="scss" scoped>
.video-sty {
  position: relative;
  height: 100vh;
  pointer-events: auto;
  background: var(--el-bg-color);
}

.toggle-btn {
  position: absolute;
  top: 521px;
  left: 100%;
  z-index: 99;
  width: 24px;
  height: 56px;
  background: var(--el-bg-color) !important; // #292b2e
  border-radius: 0 4px 4px 0 !important;
}

.upload-btn {
  margin: 24px 34px;
  background-color: #4176ff;
  border-radius: 6px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  width: 29.3518vh;
  height: 11.8333vh;
  padding: 5px;
}

.scrollbar-item {
  display: flex;
  gap: 15px;
  align-items: center;
  height: 50px;
  padding: 12px;
  margin: 10px;
  color: var(--el-color-primary);
  text-align: center;
  background: var(--el-color-primary-light-9);
  border-radius: 4px;
}

.scrollbar-item:hover {
  background: rgb(137 169 255 / 20%);
}
</style>
