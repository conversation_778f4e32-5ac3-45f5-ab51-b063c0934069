import { blobFromDataURL, isBase64 } from '@/lib/@geovis3d/shared';
import * as Cesium from 'cesium';
import omggif from 'omggif';

import { getCesiumMaterialCache, setCesiumMaterialCache } from './material-cache';

async function getGifFrames(gif: string) {
  let blob: Blob;
  if (isBase64(gif)) {
    blob = blobFromDataURL(gif);
  }
  else {
    blob = await new Promise<Blob>((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('get', gif, true);
      xhr.responseType = 'blob';
      xhr.onerror = reject;
      xhr.onloadend = async function () {
        const blob = this.response;
        resolve(blob);
      };
      xhr.send();
    });
    const arrayBuffer = await blob.arrayBuffer();
    const intArray = new Uint8Array(arrayBuffer);
    const reader = new omggif.GifReader(intArray);
    const info = reader.frameInfo(0);
    const images = new Array(reader.numFrames()).fill(0).map((_, index) => {
      const image = new ImageData(info.width, info.height);

      reader.decodeAndBlitFrameRGBA(index, image.data);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      canvas.width = image.width;
      canvas.height = image.height;
      ctx.putImageData(image, 0, 0);
      const res = canvas.toDataURL('image/png');
      return res;
    });
    return images;
  }
}

export type ImageMaterialPropertyConstructorOptions = ConstructorParameters<
  typeof Cesium.ImageMaterialProperty
>[0];

export type ImageGifMaterialPropertyConstructorOptions = ImageMaterialPropertyConstructorOptions & {
  gif: string;
};

/**
 * gif图片纹理
 */
export class ImageGifMaterialProperty extends Cesium.ImageMaterialProperty {
  constructor(options: ImageGifMaterialPropertyConstructorOptions) {
    super({
      image: new Cesium.CallbackProperty(() => {
        let image: string | null = null;
        if (this._frames) {
          image = this._frames[this._currentFramesIndex];
          if (this._dirty) {
            if (this._currentFramesIndex == this._frames.length - 1) {
              this._currentFramesIndex = 1;
            }
            else {
              this._currentFramesIndex++;
            }
          }
          this._dirty = !this._dirty;
        }
        return image;
      }, true),
      repeat: options.repeat,
      color: options.color,
      transparent: options.transparent,
    });

    this._options = options ?? {};
    getGifFrames(options.gif).then((frames) => {
      this._frames = frames;
    });
  }

  /**
   * @internal
   */
  _options: any;

  _dirty = false;

  /**
   * @internal
   */
  private _frames?: string[];

  /**
   * @internal
   */
  private _currentFramesIndex = 1;

  static readonly MaterialType = 'ImageGifMaterial';

  getType(): string {
    return ImageGifMaterialProperty.MaterialType;
  }

  getValue(time: Cesium.JulianDate, result?: any) {
    result ??= {};
    Object.assign(result, super.getValue(time, result));
    result.gif = this._options.gif;
    return result;
  }
}

setCesiumMaterialCache(ImageGifMaterialProperty.MaterialType, getCesiumMaterialCache('Image'));
