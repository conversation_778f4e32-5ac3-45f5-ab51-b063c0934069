<script lang="ts" setup>
import { computedLoading } from '@/hooks/computed-loading';
import { toDayjs } from '@/utils/to-dayjs';
import { FIRE_FACTOR_BASE_URL, oneMapCtrInitInfo } from '../api';
import { injectFireFactorState } from '../state';

defineOptions({ name: 'StationPopper' });

const { activeStationId, activeStation } = injectFireFactorState()!;

const position = computed(() => {
  const { lon, lat } = activeStation.value ?? {};

  if (lon && lat) {
    return Cesium.Cartesian3.fromDegrees(+lon, +lat);
  }
  else {
    return undefined;
  }
});

const [detail, isLoading] = computedLoading(async () => {
  const { data } = await oneMapCtrInitInfo({ id: activeStationId.value, type: 'dlwhsl', timetype: '2' });
  return data;
});

const currentTab = ref('火险因子监测');
</script>

<template>
  <located-popper1
    v-if="position"
    :position="position"
    :header="activeStation?.name"
    show-close
    @close="activeStationId = undefined"
  >
    <el-tabs v-model="currentTab" m="x-20px!">
      <el-tab-pane label="火险因子监测" name="火险因子监测" />
      <el-tab-pane label="周边物候信息" name="周边物候信息" />
      <el-tab-pane label="测站基本信息" name="测站基本信息" />
    </el-tabs>
    <div v-loading="isLoading" h="400px" m="x-20px b-20px">
      <!-- 火险因子监测 -->
      <div v-if="currentTab === '火险因子监测'" flex="~ col">
        <header-title1 p="x-0! y-5px!" content-class="text-16px!">
          站点动态监测
        </header-title1>
        <el-descriptions
          :column="2"
          border
        >
          <el-descriptions-item label="凋落物含水率">
            {{ detail?.zddtjc?.dlwhsl ?? '--' }}%
          </el-descriptions-item>
          <el-descriptions-item label="土壤含水率">
            {{ detail?.zddtjc?.trhsl ?? '--' }}%
          </el-descriptions-item>
          <el-descriptions-item label="地表温度">
            {{ detail?.zddtjc?.dbwd ?? '--' }}℃
          </el-descriptions-item>
          <el-descriptions-item label="地表湿度">
            {{ detail?.zddtjc?.dbsd ?? '--' }}%
          </el-descriptions-item>
          <el-descriptions-item label="空气温度">
            {{ detail?.zddtjc?.kqwd ?? '--' }}℃
          </el-descriptions-item>
          <el-descriptions-item label="空气湿度">
            {{ detail?.zddtjc?.kqsd ?? '--' }}%
          </el-descriptions-item>
          <el-descriptions-item label="光照度">
            {{ detail?.zddtjc?.gzd ?? '--' }}lx
          </el-descriptions-item>
          <el-descriptions-item label="日累计降雨量">
            {{ detail?.zddtjc?.yl ?? '0' }}mm
          </el-descriptions-item>
          <el-descriptions-item label="火险等级">
            {{ detail?.alarmLv }}
          </el-descriptions-item>
          <el-descriptions-item label="采集时间">
            {{ detail?.collecttime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <!-- 周边物候信息 -->
      <div v-if="currentTab === '周边物候信息'" flex="~">
        <div flex="1">
          <header-title1 p="x-0! y-5px!" content-class="text-16px!">
            物候参数
          </header-title1>
          <el-descriptions
            :column="2"
            border
          >
            <el-descriptions-item label="凋落物层厚度">
              {{ detail?.info?.humuslayerland ?? '--' }}cm
            </el-descriptions-item>
            <el-descriptions-item label="腐殖质层厚度">
              {{ detail?.info?.litterlayerland ?? '--' }}cm
            </el-descriptions-item>
            <el-descriptions-item label="是否返青">
              {{ +detail?.info?.isfq === 2 ? '未返青' : '返青' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否降雪">
              {{ +detail?.info?.issnow === 2 ? '未降雪' : '降雪' }}
            </el-descriptions-item>
            <el-descriptions-item label="植被类型">
              {{ detail?.info?.foresttype ?? '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="森林郁闭度">
              {{ detail?.info?.forestybd ?? '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="站点坡向">
              {{ detail?.info?.slopedirect ?? '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="站点坡度">
              {{ detail?.info?.slopenum ?? '--' }}°
            </el-descriptions-item>
            <el-descriptions-item label="站点海拔">
              {{ detail?.info?.altitude ?? '--' }}米
            </el-descriptions-item>
            <el-descriptions-item label="站点编号">
              {{ detail?.info?.code ?? '--' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div w="20px" />
        <div flex="1 col">
          <header-title1 p="x-0! y-5px!" content-class="text-16px!">
            凋落物信息
          </header-title1>
          <el-image :src="`${FIRE_FACTOR_BASE_URL}/kong/kong-gateway/ffmw-ms/system/fileCtr/downFile?id=${detail?.info?.litterpic}`" />
        </div>
      </div>

      <!-- 测站基本信息 -->
      <el-scrollbar v-if="currentTab === '测站基本信息'">
        <header-title1 p="x-0! y-5px!" content-class="text-16px!">
          基本信息
        </header-title1>
        <el-descriptions
          :column="2"
          border
        >
          <el-descriptions-item label="站点名称">
            {{ detail?.info?.name ?? '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="站点编号">
            {{ detail?.info?.code ?? '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="设备位置">
            {{ detail?.info?.areanames?.replaceAll('/', '') ?? '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="经纬度">
            {{ detail?.info?.lon }},{{ detail?.info?.lat }}
          </el-descriptions-item>
          <el-descriptions-item label="管理单位">
            {{ `${detail?.info?.areaname}应急管理部门` }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ detail?.info?.dutyman }}、{{ detail?.info?.dutymantel }}
          </el-descriptions-item>
          <el-descriptions-item label="安装时间">
            {{ detail?.info?.builddate ?? '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="运行时长">
            {{ toDayjs().diff(toDayjs(detail?.info?.builddate), 'd') }}天
          </el-descriptions-item>
        </el-descriptions>

        <div flex="~">
          <div flex="1 col">
            <header-title1 p="x-0! y-5px!" content-class="text-16px!">
              设备参数
            </header-title1>
            <el-descriptions
              :column="1"
              border
            >
              <el-descriptions-item label="板载温度">
                {{ detail?.info?.boardtemp ?? '--' }}℃
              </el-descriptions-item>
              <el-descriptions-item label="板载湿度">
                {{ detail?.info?.boardhumidity ?? '--' }}%
              </el-descriptions-item>
              <el-descriptions-item label="板载气压">
                --
              </el-descriptions-item>
              <el-descriptions-item label="太阳能电压">
                {{ detail?.info?.solarvoltage ?? '--' }}v
              </el-descriptions-item>
              <el-descriptions-item label="信号强度">
                {{ detail?.info?.signalintensity ?? '--' }}dBm
              </el-descriptions-item>
              <el-descriptions-item label="蓄电池电压">
                {{ detail?.info?.voltagecell ?? '--' }}v
              </el-descriptions-item>
              <el-descriptions-item label="SIM卡">
                {{ detail?.info?.sim ?? '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="倾斜警报">
                {{ +detail?.info?.tiltwarn === 1 ? '无警报' : '警报' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div w="20px" />
          <div flex="[2] col">
            <header-title1 p="x-0! y-5px!" content-class="text-16px!">
              设备照片
            </header-title1>

            <el-carousel indicator-position="outside">
              <el-carousel-item v-for="item in detail?.info?.installpic?.split(',')" :key="item">
                <el-image :src="`${FIRE_FACTOR_BASE_URL}/kong/kong-gateway/ffmw-ms/system/fileCtr/downFile?id=${item}`" />
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </located-popper1>
</template>

<style lang="scss" scoped>
.el-descriptions :deep().el-descriptions__table {
  table-layout: fixed;
}
</style>
