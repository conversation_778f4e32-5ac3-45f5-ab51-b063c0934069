import type { Primitive, Viewer } from 'cesium';

import { Cartesian3, Cartographic } from 'cesium';
import asideMaterial from '../assets/aside-material.png';
import soilMaterial from '../assets/soil-material.jpg';

interface optProp {
  height: number;
  splitNum: number;
  wallImg?: string;// 坑壁图片
  bottomImg?: string;// 坑底图片
}
interface wallDataProp {
  lerp_pos: Cartographic[];
  bottom_pos: Cartesian3[];
  no_height_top: Cartesian3[];
}
export class ExcavationTool {
  constructor(viewer: Viewer, options: optProp) {
    this._viewer = viewer;
    this._options = {
      wallImg: asideMaterial,
      bottomImg: soilMaterial,
      ...options,
    };
  }

  private bottomSurface: Primitive | undefined;
  private wellWall: Primitive | undefined;
  private _viewer: Viewer;
  private _options: optProp;
  private wallData = {} as wallDataProp;
  excavateMinHeight = 999;

  clear() {
    if (this._viewer.scene.globe.clippingPlanes) {
      this._viewer.scene.globe.clippingPlanes.enabled = false;
      this._viewer.scene.globe.clippingPlanes.removeAll();
      this._viewer.scene.globe.clippingPlanes = undefined;
    }
    if (this.bottomSurface) {
      this._viewer.scene.primitives.remove(this.bottomSurface);
      this.bottomSurface = undefined;
    }
    if (this.wellWall) {
      this._viewer.scene.primitives.remove(this.wellWall);
      this.wellWall = undefined;
    }
    this._viewer.scene.render();
  }

  updateData = useDebounceFn((points: Cartesian3[]) => {
    // 剖切之前将所有面清除
    this.clear();
    const clippingPlanesList = [];
    // 计算两个笛卡尔函数的分量差异
    const car3Difference = Cartesian3.subtract(points[0], points[1], new Cartesian3());
    for (let index = 0; index < points.length; ++index) {
      const s = (index + 1) % points.length;
      const cartographic = Cartographic.fromCartesian(points[index]);
      const curHeight = cartographic.height;
      if (curHeight < this.excavateMinHeight) {
        this.excavateMinHeight = curHeight;
      }
      // 计算中间点
      let curMidPoint = Cartesian3.midpoint(points[index], points[s], new Cartesian3());
      curMidPoint = Cartesian3.multiplyByScalar(curMidPoint, 0.5, curMidPoint);
      // 中间点标准化
      const curMidPointNormal = Cartesian3.normalize(curMidPoint, new Cartesian3());
      // 相邻向量相减
      let curMidPointDifference = Cartesian3.subtract(points[s], curMidPoint, new Cartesian3());
      curMidPointDifference = Cartesian3.normalize(curMidPointDifference, curMidPointDifference);
      // 叉乘
      let curMidPointCross = Cartesian3.cross(curMidPointDifference, curMidPointNormal, new Cartesian3());
      curMidPointCross = Cartesian3.normalize(curMidPointCross, curMidPointCross);
      const plane = new Cesium.Plane(curMidPointCross, 0);
      const distance = Cesium.Plane.getPointDistance(plane, curMidPoint);
      clippingPlanesList.push(new Cesium.ClippingPlane(curMidPointCross, distance));
    }
    this._viewer.scene.globe.clippingPlanes = new Cesium.ClippingPlaneCollection({
      planes: clippingPlanesList,
      edgeWidth: 1,
      edgeColor: Cesium.Color.WHITE,
      enabled: true,
    });
    this.prepareWell(points);
    this.createWell(this.wallData);
  }, 500);

  // 计算并更新wallData
  prepareWell(points: Cartesian3[]) {
    const pointLength = points.length;
    const heightDiff = this.excavateMinHeight - this._options.height;
    const no_height_top = [];
    const bottom_pos = [];
    const lerp_pos = [];
    for (let m = 0; m < pointLength; m++) {
      const n = m === pointLength - 1 ? 0 : m + 1;
      const point0 = [Cartographic.fromCartesian(points[m]).longitude, Cartographic.fromCartesian(points[m]).latitude];
      const point1 = [Cartographic.fromCartesian(points[n]).longitude, Cartographic.fromCartesian(points[n]).latitude];
      if (m === 0) {
        lerp_pos.push(new Cartographic(point0[0], point0[1]));
        bottom_pos.push(Cartesian3.fromRadians(point0[0], point0[1], heightDiff));
        no_height_top.push(Cartesian3.fromRadians(point0[0], point0[1], 0));
      }
      for (let p = 1; p <= this._options.splitNum; p++) {
        const o = Cesium.Math.lerp(point0[0], point1[0], p / this._options.splitNum);
        const g = Cesium.Math.lerp(point0[1], point1[1], p / this._options.splitNum);

        if (m !== pointLength - 1 || p !== this._options.splitNum) {
          lerp_pos.push(new Cartographic(o, g));
          bottom_pos.push(Cartesian3.fromRadians(o, g, heightDiff));
          no_height_top.push(Cartesian3.fromRadians(o, g, 0));
        }
      }
    }
    this.wallData = {
      lerp_pos, // 地形坐标点包括高度
      bottom_pos, // 相对应的地底坐标点
      no_height_top, // 地形坐标点不包括高度
    };
  }

  // 开始创建底面和侧面
  createWell(wallData: wallDataProp) {
    if (this._viewer.terrainProvider._layers) {
      // 创建底面
      this.createBottomSurface(this.wallData.bottom_pos);
      // Cesium.sampleTerrainMostDetailed可以获取地形点所对应的精确地形高度
      Cesium.sampleTerrainMostDetailed(this._viewer.terrainProvider, this.wallData.lerp_pos).then((pos) => {
        const positionList = [];
        for (let index = 0; index < pos.length; index++) {
          const element = pos[index];
          const curPos = Cartesian3.fromRadians(element.longitude, element.latitude, element.height);
          // 所有地形点
          positionList.push(curPos);
        }
        // 创建侧面对象
        this.createWellWall(wallData.bottom_pos, positionList);
      });
    }
    else {
      this.createBottomSurface(wallData.bottom_pos);
      this.createWellWall(wallData.bottom_pos, wallData.no_height_top);
    }
  }

  // 坐标转换，转出经纬度格式
  ellipsoidToDegree(position: Cartesian3) {
    const cartesian3 = new Cartesian3(position.x, position.y, position.z);
    const cartographic = this._viewer.scene.globe.ellipsoid.cartesianToCartographic(cartesian3);
    return {
      longitude: Cesium.Math.toDegrees(cartographic.longitude),
      latitude: Cesium.Math.toDegrees(cartographic.latitude),
      altitude: cartographic.height,
    };
  }

  // 创建地形开挖的底面对象
  createBottomSurface(points: Cartesian3[]) {
    if (points.length) {
      const minHeight = this.getMinHeight(points) || 0;
      const positions: number[] = [];
      for (let i = 0; i < points.length; i++) {
        const curPoint = this.ellipsoidToDegree(points[i]);
        positions.push(curPoint.longitude, curPoint.latitude, minHeight);
      }
      const polygon = new Cesium.PolygonGeometry({
        polygonHierarchy: new Cesium.PolygonHierarchy(Cartesian3.fromDegreesArrayHeights(positions)),
        perPositionHeight: true,
      });
      const material = new Cesium.Material({
        fabric: {
          type: 'Image',
          uniforms: {
            image: this._options.bottomImg,
          },
        },
      });
      const appearance = new Cesium.MaterialAppearance({
        translucent: false,
        flat: true,
        material,
      });
      this.bottomSurface = new Cesium.Primitive({
        geometryInstances: new Cesium.GeometryInstance({
          geometry: Cesium.PolygonGeometry.createGeometry(polygon),
        }),
        appearance,
        asynchronous: false,
      });
      this._viewer.scene.primitives.add(this.bottomSurface);
    }
  }

  // 创建地形开挖的侧面墙对象
  createWellWall(bottomPos: Cartesian3[], positionList: Cartesian3[]) {
    const minHeight = this.getMinHeight(bottomPos) || 0;
    const maxHeights = [];
    const minHeights = [];
    for (let i = 0; i < positionList.length; i++) {
      maxHeights.push(this.ellipsoidToDegree(positionList[i]).altitude);
      minHeights.push(minHeight);
    }
    const wall = new Cesium.WallGeometry({
      positions: positionList,
      maximumHeights: maxHeights,
      minimumHeights: minHeights,
    });
    const geometry = Cesium.WallGeometry.createGeometry(wall);
    const material = new Cesium.Material({
      fabric: {
        type: 'DiffuseMap',
        uniforms: {
          image: this._options.wallImg,
          repeat: new Cesium.Cartesian2(1, 1),
        },
      },
    });
    const appearance = new Cesium.MaterialAppearance({
      translucent: false,
      flat: true,
      material,
    });
    this.wellWall && this._viewer.scene.primitives.remove(this.wellWall);
    this.wellWall = new Cesium.Primitive({
      geometryInstances: new Cesium.GeometryInstance({
        geometry,
      }),
      appearance,
      asynchronous: false,
    });
    this._viewer.scene.primitives.add(this.wellWall);
  }

  // 获取地形开挖最低点高程值
  getMinHeight(points: Cartesian3[]) {
    let minHeight = 5000000;
    let minPoint = null;
    for (let i = 0; i < points.length; i++) {
      const height = points[i].z;
      if (height < minHeight) {
        minHeight = height;
        minPoint = this.ellipsoidToDegree(points[i]);
      }
    }
    return minPoint?.altitude;
  }

  switchExcavate(show: boolean) {
    if (show) {
      this._viewer.scene.globe.material = null;
      this.wellWall && (this.wellWall.show = true);
      this.bottomSurface && (this.bottomSurface.show = true);
    }
    else {
      this._viewer.scene.globe.material = null;
      this.wellWall && (this.wellWall.show = false);
      this.bottomSurface && (this.bottomSurface.show = false);
    }
  };

  // 挖掘深度变化
  updateExcavateDepth(height: number) {
    this._viewer.scene.primitives.remove(this.bottomSurface);
    this._viewer.scene.primitives.remove(this.wellWall);
    const lerp_pos = this.wallData.lerp_pos;
    const posList: Cartesian3[] = [];
    for (let n = 0; n < lerp_pos.length; n++) {
      posList.push(Cartesian3.fromRadians(lerp_pos[n].longitude, lerp_pos[n].latitude, this.excavateMinHeight - height));
    }
    this.wallData.bottom_pos = posList;
    this.createWell(this.wallData);
  };
}
export default ExcavationTool;
