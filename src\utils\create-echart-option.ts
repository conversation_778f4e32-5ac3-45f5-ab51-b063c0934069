import type { EChartsOption } from 'echarts';
import { TinyColor } from '@ctrl/tinycolor';

import * as echarts from 'echarts';

export function createEchartOption(option?: EChartsOption): EChartsOption | undefined {
  return option;
}

export function createEchartLinearGradient(color: any, x = 0, y = 0, x2 = 0, y2 = 1): echarts.graphic.LinearGradient {
  return new echarts.graphic.LinearGradient(x, y, x2, y2, [
    {
      offset: 0,
      color: new TinyColor(color).setAlpha(0.4).toHex8String(),
    },
    {
      offset: 0.9,
      color: new TinyColor(color).setAlpha(0.1).toHex8String(),
    },
  ]);
}
