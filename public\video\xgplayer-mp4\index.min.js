!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("core-js/modules/es.json.stringify.js"),require("core-js/modules/es.object.assign.js"),require("core-js/modules/es.object.keys.js"),require("core-js/modules/es.object.to-string.js"),require("core-js/modules/es.function.name.js"),require("core-js/modules/es.array.concat.js"),require("xgplayer"),require("core-js/modules/es.array.map.js"),require("core-js/modules/es.array.iterator.js"),require("core-js/modules/es.typed-array.uint8-array.js"),require("core-js/modules/esnext.typed-array.at.js"),require("core-js/modules/es.typed-array.copy-within.js"),require("core-js/modules/es.typed-array.every.js"),require("core-js/modules/es.typed-array.fill.js"),require("core-js/modules/es.typed-array.filter.js"),require("core-js/modules/es.typed-array.find.js"),require("core-js/modules/es.typed-array.find-index.js"),require("core-js/modules/esnext.typed-array.find-last.js"),require("core-js/modules/esnext.typed-array.find-last-index.js"),require("core-js/modules/es.typed-array.for-each.js"),require("core-js/modules/es.typed-array.includes.js"),require("core-js/modules/es.typed-array.index-of.js"),require("core-js/modules/es.typed-array.iterator.js"),require("core-js/modules/es.typed-array.join.js"),require("core-js/modules/es.typed-array.last-index-of.js"),require("core-js/modules/es.typed-array.map.js"),require("core-js/modules/es.typed-array.reduce.js"),require("core-js/modules/es.typed-array.reduce-right.js"),require("core-js/modules/es.typed-array.reverse.js"),require("core-js/modules/es.typed-array.set.js"),require("core-js/modules/es.typed-array.slice.js"),require("core-js/modules/es.typed-array.some.js"),require("core-js/modules/es.typed-array.sort.js"),require("core-js/modules/es.typed-array.subarray.js"),require("core-js/modules/es.typed-array.to-locale-string.js"),require("core-js/modules/es.typed-array.to-string.js"),require("core-js/modules/es.array.find.js"),require("core-js/modules/es.number.constructor.js"),require("core-js/modules/es.array.filter.js"),require("core-js/modules/es.array.slice.js"),require("core-js/modules/es.symbol.js"),require("core-js/modules/es.symbol.iterator.js"),require("core-js/modules/es.string.iterator.js"),require("core-js/modules/web.dom-collections.iterator.js"),require("core-js/modules/es.symbol.description.js"),require("core-js/modules/web.dom-collections.for-each.js"),require("core-js/modules/es.array.includes.js"),require("core-js/modules/es.string.includes.js"),require("core-js/modules/es.number.is-finite.js"),require("core-js/modules/es.array.splice.js"),require("core-js/modules/es.regexp.to-string.js"),require("core-js/modules/es.array.join.js"),require("core-js/modules/es.regexp.exec.js"),require("core-js/modules/es.regexp.test.js"),require("core-js/modules/es.regexp.flags.js"),require("core-js/modules/es.string.pad-start.js"),require("core-js/modules/es.array.from.js"),require("core-js/modules/es.array-buffer.constructor.js"),require("core-js/modules/es.typed-array.int8-array.js"),require("core-js/modules/es.typed-array.uint8-clamped-array.js"),require("core-js/modules/es.typed-array.int16-array.js"),require("core-js/modules/es.typed-array.uint16-array.js"),require("core-js/modules/es.typed-array.int32-array.js"),require("core-js/modules/es.typed-array.uint32-array.js"),require("core-js/modules/es.typed-array.float32-array.js"),require("core-js/modules/es.typed-array.float64-array.js"),require("core-js/modules/es.promise.js"),require("core-js/modules/web.url.js"),require("core-js/modules/web.url-search-params.js"),require("core-js/modules/es.promise.finally.js"),require("core-js/modules/es.string.replace.js"),require("core-js/modules/es.number.is-nan.js"),require("core-js/modules/es.object.get-prototype-of.js"),require("core-js/modules/es.string.trim.js"),require("core-js/modules/es.set.js")):"function"==typeof define&&define.amd?define(["core-js/modules/es.json.stringify.js","core-js/modules/es.object.assign.js","core-js/modules/es.object.keys.js","core-js/modules/es.object.to-string.js","core-js/modules/es.function.name.js","core-js/modules/es.array.concat.js","xgplayer","core-js/modules/es.array.map.js","core-js/modules/es.array.iterator.js","core-js/modules/es.typed-array.uint8-array.js","core-js/modules/esnext.typed-array.at.js","core-js/modules/es.typed-array.copy-within.js","core-js/modules/es.typed-array.every.js","core-js/modules/es.typed-array.fill.js","core-js/modules/es.typed-array.filter.js","core-js/modules/es.typed-array.find.js","core-js/modules/es.typed-array.find-index.js","core-js/modules/esnext.typed-array.find-last.js","core-js/modules/esnext.typed-array.find-last-index.js","core-js/modules/es.typed-array.for-each.js","core-js/modules/es.typed-array.includes.js","core-js/modules/es.typed-array.index-of.js","core-js/modules/es.typed-array.iterator.js","core-js/modules/es.typed-array.join.js","core-js/modules/es.typed-array.last-index-of.js","core-js/modules/es.typed-array.map.js","core-js/modules/es.typed-array.reduce.js","core-js/modules/es.typed-array.reduce-right.js","core-js/modules/es.typed-array.reverse.js","core-js/modules/es.typed-array.set.js","core-js/modules/es.typed-array.slice.js","core-js/modules/es.typed-array.some.js","core-js/modules/es.typed-array.sort.js","core-js/modules/es.typed-array.subarray.js","core-js/modules/es.typed-array.to-locale-string.js","core-js/modules/es.typed-array.to-string.js","core-js/modules/es.array.find.js","core-js/modules/es.number.constructor.js","core-js/modules/es.array.filter.js","core-js/modules/es.array.slice.js","core-js/modules/es.symbol.js","core-js/modules/es.symbol.iterator.js","core-js/modules/es.string.iterator.js","core-js/modules/web.dom-collections.iterator.js","core-js/modules/es.symbol.description.js","core-js/modules/web.dom-collections.for-each.js","core-js/modules/es.array.includes.js","core-js/modules/es.string.includes.js","core-js/modules/es.number.is-finite.js","core-js/modules/es.array.splice.js","core-js/modules/es.regexp.to-string.js","core-js/modules/es.array.join.js","core-js/modules/es.regexp.exec.js","core-js/modules/es.regexp.test.js","core-js/modules/es.regexp.flags.js","core-js/modules/es.string.pad-start.js","core-js/modules/es.array.from.js","core-js/modules/es.array-buffer.constructor.js","core-js/modules/es.typed-array.int8-array.js","core-js/modules/es.typed-array.uint8-clamped-array.js","core-js/modules/es.typed-array.int16-array.js","core-js/modules/es.typed-array.uint16-array.js","core-js/modules/es.typed-array.int32-array.js","core-js/modules/es.typed-array.uint32-array.js","core-js/modules/es.typed-array.float32-array.js","core-js/modules/es.typed-array.float64-array.js","core-js/modules/es.promise.js","core-js/modules/web.url.js","core-js/modules/web.url-search-params.js","core-js/modules/es.promise.finally.js","core-js/modules/es.string.replace.js","core-js/modules/es.number.is-nan.js","core-js/modules/es.object.get-prototype-of.js","core-js/modules/es.string.trim.js","core-js/modules/es.set.js"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).Mp4Plugin=t(null,null,null,null,null,null,e.Player)}(this,(function(e,t,i,n,s,r,a){"use strict";function l(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function o(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?l(Object(i),!0).forEach((function(t){y(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function d(){d=function(){return e};var e={},t=Object.prototype,i=t.hasOwnProperty,n=Object.defineProperty||function(e,t,i){e[t]=i.value},s="function"==typeof Symbol?Symbol:{},r=s.iterator||"@@iterator",a=s.asyncIterator||"@@asyncIterator",l=s.toStringTag||"@@toStringTag";function o(e,t,i){return Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{o({},"")}catch(Y){o=function(e,t,i){return e[t]=i}}function c(e,t,i,s){var r=t&&t.prototype instanceof m?t:m,a=Object.create(r.prototype),l=new S(s||[]);return n(a,"_invoke",{value:W(e,i,l)}),a}function u(e,t,i){try{return{type:"normal",arg:e.call(t,i)}}catch(Y){return{type:"throw",arg:Y}}}e.wrap=c;var h={};function m(){}function p(){}function b(){}var y={};o(y,r,(function(){return this}));var Z=Object.getPrototypeOf,f=Z&&Z(Z(V([])));f&&f!==t&&i.call(f,r)&&(y=f);var X=b.prototype=m.prototype=Object.create(y);function L(e){["next","throw","return"].forEach((function(t){o(e,t,(function(e){return this._invoke(t,e)}))}))}function v(e,t){function s(n,r,a,l){var o=u(e[n],e,r);if("throw"!==o.type){var d=o.arg,c=d.value;return c&&"object"==typeof c&&i.call(c,"__await")?t.resolve(c.__await).then((function(e){s("next",e,a,l)}),(function(e){s("throw",e,a,l)})):t.resolve(c).then((function(e){d.value=e,a(d)}),(function(e){return s("throw",e,a,l)}))}l(o.arg)}var r;n(this,"_invoke",{value:function(e,i){function n(){return new t((function(t,n){s(e,i,t,n)}))}return r=r?r.then(n,n):n()}})}function W(e,t,i){var n="suspendedStart";return function(s,r){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===s)throw r;return{value:void 0,done:!0}}for(i.method=s,i.arg=r;;){var a=i.delegate;if(a){var l=G(a,i);if(l){if(l===h)continue;return l}}if("next"===i.method)i.sent=i._sent=i.arg;else if("throw"===i.method){if("suspendedStart"===n)throw n="completed",i.arg;i.dispatchException(i.arg)}else"return"===i.method&&i.abrupt("return",i.arg);n="executing";var o=u(e,t,i);if("normal"===o.type){if(n=i.done?"completed":"suspendedYield",o.arg===h)continue;return{value:o.arg,done:i.done}}"throw"===o.type&&(n="completed",i.method="throw",i.arg=o.arg)}}}function G(e,t){var i=t.method,n=e.iterator[i];if(void 0===n)return t.delegate=null,"throw"===i&&e.iterator.return&&(t.method="return",t.arg=void 0,G(e,t),"throw"===t.method)||"return"!==i&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+i+"' method")),h;var s=u(n,e.iterator,t.arg);if("throw"===s.type)return t.method="throw",t.arg=s.arg,t.delegate=null,h;var r=s.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,h):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,h)}function x(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function g(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(x,this),this.reset(!0)}function V(e){if(e){var t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,s=function t(){for(;++n<e.length;)if(i.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return s.next=s}}return{next:k}}function k(){return{value:void 0,done:!0}}return p.prototype=b,n(X,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:p,configurable:!0}),p.displayName=o(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,o(e,l,"GeneratorFunction")),e.prototype=Object.create(X),e},e.awrap=function(e){return{__await:e}},L(v.prototype),o(v.prototype,a,(function(){return this})),e.AsyncIterator=v,e.async=function(t,i,n,s,r){void 0===r&&(r=Promise);var a=new v(c(t,i,n,s),r);return e.isGeneratorFunction(i)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},L(X),o(X,l,"Generator"),o(X,r,(function(){return this})),o(X,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),i=[];for(var n in t)i.push(n);return i.reverse(),function e(){for(;i.length;){var n=i.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=V,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(g),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(i,n){return a.type="throw",a.arg=e,t.next=i,n&&(t.method="next",t.arg=void 0),!!n}for(var s=this.tryEntries.length-1;s>=0;--s){var r=this.tryEntries[s],a=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var l=i.call(r,"catchLoc"),o=i.call(r,"finallyLoc");if(l&&o){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(l){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!o)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n];if(s.tryLoc<=this.prev&&i.call(s,"finallyLoc")&&this.prev<s.finallyLoc){var r=s;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var a=r?r.completion:{};return a.type=e,a.arg=t,r?(this.method="next",this.next=r.finallyLoc,h):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.finallyLoc===e)return this.complete(i.completion,i.afterLoc),g(i),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var i=this.tryEntries[t];if(i.tryLoc===e){var n=i.completion;if("throw"===n.type){var s=n.arg;g(i)}return s}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,i){return this.delegate={iterator:V(e),resultName:t,nextLoc:i},"next"===this.method&&(this.arg=void 0),h}},e}function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e,t,i,n,s,r,a){try{var l=e[r](a),o=l.value}catch(d){return void i(d)}l.done?t(o):Promise.resolve(o).then(n,s)}function h(e){return function(){var t=this,i=arguments;return new Promise((function(n,s){var r=e.apply(t,i);function a(e){u(r,n,s,a,l,"next",e)}function l(e){u(r,n,s,a,l,"throw",e)}a(void 0)}))}}function m(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,M(n.key),n)}}function b(e,t,i){return t&&p(e.prototype,t),i&&p(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function y(e,t,i){return(t=M(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function Z(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&X(e,t)}function f(e){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function X(e,t){return(X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function L(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function v(e,t,i){return(v=L()?Reflect.construct.bind():function(e,t,i){var n=[null];n.push.apply(n,t);var s=new(Function.bind.apply(e,n));return i&&X(s,i.prototype),s}).apply(null,arguments)}function W(e){var t="function"==typeof Map?new Map:void 0;return W=function(e){if(null===e||(i=e,-1===Function.toString.call(i).indexOf("[native code]")))return e;var i;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return v(e,arguments,f(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),X(n,e)},W(e)}function G(e,t){if(null==e)return{};var i,n,s=function(e,t){if(null==e)return{};var i,n,s={},r=Object.keys(e);for(n=0;n<r.length;n++)i=r[n],t.indexOf(i)>=0||(s[i]=e[i]);return s}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(n=0;n<r.length;n++)i=r[n],t.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(e,i)&&(s[i]=e[i])}return s}function x(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function g(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return x(e)}function S(e){var t=L();return function(){var i,n=f(e);if(t){var s=f(this).constructor;i=Reflect.construct(n,arguments,s)}else i=n.apply(this,arguments);return g(this,i)}}function V(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=f(e)););return e}function k(){return k="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var n=V(e,t);if(n){var s=Object.getOwnPropertyDescriptor(n,t);return s.get?s.get.call(arguments.length<3?e:i):s.value}},k.apply(this,arguments)}function Y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var n,s,r,a,l=[],o=!0,d=!1;try{if(r=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;o=!1}else for(;!(o=(n=r.call(i)).done)&&(l.push(n.value),l.length!==t);o=!0);}catch(c){d=!0,s=c}finally{try{if(!o&&null!=i.return&&(a=i.return(),Object(a)!==a))return}finally{if(d)throw s}}return l}}(e,t)||w(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(e){return function(e){if(Array.isArray(e))return R(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||w(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(e,t){if(e){if("string"==typeof e)return R(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?R(e,t):void 0}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function M(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}var K={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,i="~";function n(){}function s(e,t,i){this.fn=e,this.context=t,this.once=i||!1}function r(e,t,n,r,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var l=new s(n,r||e,a),o=i?i+t:t;return e._events[o]?e._events[o].fn?e._events[o]=[e._events[o],l]:e._events[o].push(l):(e._events[o]=l,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(i=!1)),l.prototype.eventNames=function(){var e,n,s=[];if(0===this._eventsCount)return s;for(n in e=this._events)t.call(e,n)&&s.push(i?n.slice(1):n);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(e)):s},l.prototype.listeners=function(e){var t=i?i+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var s=0,r=n.length,a=new Array(r);s<r;s++)a[s]=n[s].fn;return a},l.prototype.listenerCount=function(e){var t=i?i+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,s,r,a){var l=i?i+e:e;if(!this._events[l])return!1;var o,d,c=this._events[l],u=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),u){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,s),!0;case 5:return c.fn.call(c.context,t,n,s,r),!0;case 6:return c.fn.call(c.context,t,n,s,r,a),!0}for(d=1,o=new Array(u-1);d<u;d++)o[d-1]=arguments[d];c.fn.apply(c.context,o)}else{var h,m=c.length;for(d=0;d<m;d++)switch(c[d].once&&this.removeListener(e,c[d].fn,void 0,!0),u){case 1:c[d].fn.call(c[d].context);break;case 2:c[d].fn.call(c[d].context,t);break;case 3:c[d].fn.call(c[d].context,t,n);break;case 4:c[d].fn.call(c[d].context,t,n,s);break;default:if(!o)for(h=1,o=new Array(u-1);h<u;h++)o[h-1]=arguments[h];c[d].fn.apply(c[d].context,o)}}return!0},l.prototype.on=function(e,t,i){return r(this,e,t,i,!1)},l.prototype.once=function(e,t,i){return r(this,e,t,i,!0)},l.prototype.removeListener=function(e,t,n,s){var r=i?i+e:e;if(!this._events[r])return this;if(!t)return a(this,r),this;var l=this._events[r];if(l.fn)l.fn!==t||s&&!l.once||n&&l.context!==n||a(this,r);else{for(var o=0,d=[],c=l.length;o<c;o++)(l[o].fn!==t||s&&!l[o].once||n&&l[o].context!==n)&&d.push(l[o]);d.length?this._events[r]=1===d.length?d[0]:d:a(this,r)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=i?i+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=i,l.EventEmitter=l,e.exports=l}(K);var I,H=K.exports,C={};Object.defineProperty(C,"__esModule",{value:!0}),C.default=function(e){for(var t=0,i=arguments.length,n=Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];var r=!0,a=!1,l=void 0;try{for(var o,d=n[Symbol.iterator]();!(r=(o=d.next()).done);r=!0){var c=o.value;t+=c.length}}catch(X){a=!0,l=X}finally{try{!r&&d.return&&d.return()}finally{if(a)throw l}}var u=new e(t),h=0,m=!0,p=!1,b=void 0;try{for(var y,Z=n[Symbol.iterator]();!(m=(y=Z.next()).done);m=!0){var f=y.value;u.set(f,h),h+=f.length}}catch(X){p=!0,b=X}finally{try{!m&&Z.return&&Z.return()}finally{if(p)throw b}}return u};var N=((I=C)&&I.__esModule?I:{default:I}).default,F="video",z="audio",j="metadata",U="avc",J="hevc",P="aac",D="g7110a",E="g7110m",_="LARGE_AV_SHIFT",B="LARGE_VIDEO_GAP",Q="LARGE_VIDEO_GAP_BETWEEN_CHUNK",O="LARGE_AUDIO_GAP",A="AUDIO_FILLED",q="AUDIO_DROPPED",$=function(){function e(){m(this,e),y(this,"id",1),y(this,"type",F),y(this,"codecType",U),y(this,"pid",-1),y(this,"hvcC",void 0),y(this,"codec",""),y(this,"timescale",0),y(this,"formatTimescale",0),y(this,"sequenceNumber",0),y(this,"baseMediaDecodeTime",0),y(this,"baseDts",0),y(this,"duration",0),y(this,"warnings",[]),y(this,"samples",[]),y(this,"pps",[]),y(this,"sps",[]),y(this,"vps",[]),y(this,"fpsNum",0),y(this,"fpsDen",0),y(this,"sarRatio",[]),y(this,"width",0),y(this,"height",0),y(this,"nalUnitSize",4),y(this,"present",!1),y(this,"isVideoEncryption",!1),y(this,"isAudioEncryption",!1),y(this,"isVideo",!0),y(this,"kid",null),y(this,"pssh",null),y(this,"ext",void 0)}return b(e,[{key:"reset",value:function(){this.sequenceNumber=this.width=this.height=this.fpsDen=this.fpsNum=this.duration=this.baseMediaDecodeTime=this.timescale=0,this.codec="",this.present=!1,this.pid=-1,this.pps=[],this.sps=[],this.vps=[],this.sarRatio=[],this.samples=[],this.warnings=[],this.hvcC=null}},{key:"exist",value:function(){return!!(this.pps.length&&this.sps.length&&this.codec)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isVideoEncryption}}]),e}(),ee=function(){function e(){m(this,e),y(this,"id",2),y(this,"type",z),y(this,"codecType",P),y(this,"pid",-1),y(this,"codec",""),y(this,"sequenceNumber",0),y(this,"sampleDuration",0),y(this,"timescale",0),y(this,"formatTimescale",0),y(this,"baseMediaDecodeTime",0),y(this,"duration",0),y(this,"warnings",[]),y(this,"samples",[]),y(this,"baseDts",0),y(this,"sampleSize",16),y(this,"sampleRate",0),y(this,"channelCount",0),y(this,"objectType",0),y(this,"sampleRateIndex",0),y(this,"config",[]),y(this,"present",!1),y(this,"isVideoEncryption",!1),y(this,"isAudioEncryption",!1),y(this,"kid",null),y(this,"ext",void 0)}return b(e,[{key:"reset",value:function(){this.sequenceNumber=0,this.timescale=0,this.sampleDuration=0,this.sampleRate=0,this.channelCount=0,this.baseMediaDecodeTime=0,this.present=!1,this.pid=-1,this.codec="",this.samples=[],this.config=[],this.warnings=[]}},{key:"exist",value:function(){return!!(this.sampleRate&&this.channelCount&&this.codec&&this.codecType===P)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isAudioEncryption}}]),e}(),te=function(){function e(t,i,n){m(this,e),y(this,"flag",{}),y(this,"keyframe",!1),y(this,"gopId",0),y(this,"duration",0),y(this,"size",0),y(this,"units",[]),y(this,"chromaFormat",420),this.originPts=this.pts=t,this.originDts=this.dts=i,n&&(this.units=n)}return b(e,[{key:"cts",get:function(){return this.pts-this.dts}},{key:"setToKeyframe",value:function(){this.keyframe=!0,this.flag.dependsOn=2,this.flag.isNonSyncSample=0}}]),e}(),ie=b((function e(t,i,n,s){m(this,e),y(this,"duration",1024),y(this,"flag",{dependsOn:2,isNonSyncSample:0}),y(this,"keyframe",!0),this.originPts=this.pts=this.dts=t,this.data=i,this.size=i.byteLength,this.sampleOffset=s,n&&(this.duration=n)})),ne=b((function e(t,i){m(this,e),y(this,"time",0),this.data=t,this.originPts=this.pts=i})),se=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i)}(ne),re=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i)}(ne),ae=function(){function e(){m(this,e),y(this,"id",3),y(this,"type",j),y(this,"timescale",0),y(this,"flvScriptSamples",[]),y(this,"seiSamples",[])}return b(e,[{key:"exist",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length||!this.timescale)}},{key:"reset",value:function(){this.timescale=0,this.flvScriptSamples=[],this.seiSamples=[]}},{key:"hasSample",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length)}}]),e}(),le=function(){function e(t){if(m(this,e),y(this,"_bytesAvailable",void 0),y(this,"_bitsAvailable",0),y(this,"_word",0),!t)throw new Error("ExpGolomb data params is required");this._data=t,this._bytesAvailable=t.byteLength,this._bytesAvailable&&this._loadWord()}return b(e,[{key:"_loadWord",value:function(){var e=this._data.byteLength-this._bytesAvailable,t=Math.min(4,this._bytesAvailable);if(0===t)throw new Error("No bytes available");var i=new Uint8Array(4);i.set(this._data.subarray(e,e+t)),this._word=new DataView(i.buffer).getUint32(0),this._bitsAvailable=8*t,this._bytesAvailable-=t}},{key:"skipBits",value:function(e){if(this._bitsAvailable>e)this._word<<=e,this._bitsAvailable-=e;else{e-=this._bitsAvailable;var t=Math.floor(e/8);e-=8*t,this._bytesAvailable-=t,this._loadWord(),this._word<<=e,this._bitsAvailable-=e}}},{key:"readBits",value:function(e){if(e>32)throw new Error("Cannot read more than 32 bits");var t=Math.min(this._bitsAvailable,e),i=this._word>>>32-t;return this._bitsAvailable-=t,this._bitsAvailable>0?this._word<<=t:this._bytesAvailable>0&&this._loadWord(),(t=e-t)>0&&this._bitsAvailable?i<<t|this.readBits(t):i}},{key:"skipLZ",value:function(){var e;for(e=0;e<this._bitsAvailable;++e)if(0!=(this._word&2147483648>>>e))return this._word<<=e,this._bitsAvailable-=e,e;return this._loadWord(),e+this.skipLZ()}},{key:"skipUEG",value:function(){this.skipBits(1+this.skipLZ())}},{key:"readUEG",value:function(){var e=this.skipLZ();return this.readBits(e+1)-1}},{key:"readEG",value:function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}},{key:"readBool",value:function(){return 1===this.readBits(1)}},{key:"readUByte",value:function(){return this.readBits(8)}},{key:"skipScalingList",value:function(e){for(var t=8,i=8,n=0;n<e;n++)0!==i&&(i=(t+this.readEG()+256)%256),t=0===i?t:i}}]),e}(),oe=function(){function e(t){m(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]")}return b(e,[{key:"warn",value:function(){var t;if(!e.disabled){for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];(t=console).warn.apply(t,[this._prefix].concat(n))}}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();y(oe,"disabled",!0);var de=function(){function e(){m(this,e)}return b(e,null,[{key:"decode",value:function(t){for(var i=[],n=t,s=0,r=t.length;s<r;)if(n[s]<128)i.push(String.fromCharCode(n[s])),++s;else{if(n[s]<192);else if(n[s]<224){if(e._checkContinuation(n,s,1)){var a=(31&n[s])<<6|63&n[s+1];if(a>=128){i.push(String.fromCharCode(65535&a)),s+=2;continue}}}else if(n[s]<240){if(e._checkContinuation(n,s,2)){var l=(15&n[s])<<12|(63&n[s+1])<<6|63&n[s+2];if(l>=2048&&55296!=(63488&l)){i.push(String.fromCharCode(65535&l)),s+=3;continue}}}else if(n[s]<248&&e._checkContinuation(n,s,3)){var o=(7&n[s])<<18|(63&n[s+1])<<12|(63&n[s+2])<<6|63&n[s+3];if(o>65536&&o<1114112){o-=65536,i.push(String.fromCharCode(o>>>10|55296)),i.push(String.fromCharCode(1023&o|56320)),s+=4;continue}}i.push(String.fromCharCode(65533)),++s}return i.join("")}},{key:"_checkContinuation",value:function(e,t,i){var n=e;if(t+i<n.length){for(;i--;)if(128!=(192&n[++t]))return!1;return!0}return!1}}]),e}(),ce="undefined"!=typeof window,ue=ce&&navigator.userAgent.toLocaleLowerCase(),he=ce&&/^((?!chrome|android).)*safari/.test(ue),me=ce&&ue.includes("firefox"),pe=ce&&ue.includes("android");function be(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];t=t.filter(Boolean);var n=new Uint8Array(t.reduce((function(e,t){return e+t.byteLength}),0)),s=0;return t.forEach((function(e){n.set(e,s),s+=e.byteLength})),n}var ye=Math.pow(2,32);function Ze(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<8)+(e[t+1]||0)}function fe(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function Xe(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return fe(e,t)*ye+fe(e,t+4)}function Le(e){for(var t,i="avc1.",n=0;n<3;n++)(t=e[n].toString(16)).length<2&&(t="0".concat(t)),i+=t;return i}function ve(e){var t="";if(e.forEach((function(e){t+=function(e){return("0"+Number(e).toString(16)).slice(-2).toUpperCase()}(e)})),t.length<=32)for(var i=32-t.length,n=0;n<i;n++)t+="0";return t}function We(e){if(!Array.isArray(e)){for(var t=[],i="",n=0;n<e.length;n++)n%2&&(i=e[n-1]+e[n],t.push(parseInt(i,16)),i="");return t}return e.map((function(e){return parseInt(e,16)}))}var Ge=function(){function e(){m(this,e)}return b(e,null,[{key:"parseAnnexB",value:function(e){for(var t=e.length,i=2,n=0;null!==e[i]&&void 0!==e[i]&&1!==e[i];)i++;if((n=++i+2)>=t)return[];for(var s=[];n<t;)switch(e[n]){case 0:if(0!==e[n-1]){n+=2;break}if(0!==e[n-2]){n++;break}i!==n-2&&s.push(e.subarray(i,n-2));do{n++}while(1!==e[n]&&n<t);n=(i=n+1)+2;break;case 1:if(0!==e[n-1]||0!==e[n-2]){n+=3;break}i!==n-2&&s.push(e.subarray(i,n-2)),n=(i=n+1)+2;break;default:n+=3}return i<t&&s.push(e.subarray(i)),s}},{key:"parseAvcC",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(!(e.length<4)){for(var i,n=e.length,s=[],r=0;r+t<n;)if(i=fe(e,r),3===t&&(i>>>=8),r+=t,i){if(r+i>n)break;s.push(e.subarray(r,r+i)),r+=i}return s}}},{key:"parseSEI",value:function(e,t){for(var i=e.length,n=t?2:1,s=0,r=0,a="";255===e[n];)s+=255,n++;for(s+=e[n++];255===e[n];)r+=255,n++;if(r+=e[n++],5===s&&i>n+16)for(var l=0;l<16;l++)a+=e[n].toString(16),n++;return{payload:e.subarray(n),type:s,size:r,uuid:a}}},{key:"removeEPB",value:function(e){for(var t=e.byteLength,i=[],n=1;n<t-2;)0===e[n]&&0===e[n+1]&&3===e[n+2]?(i.push(n+2),n+=2):n++;if(!i.length)return e;var s=t-i.length,r=new Uint8Array(s),a=0;for(n=0;n<s;a++,n++)a===i[0]&&(a++,i.shift()),r[n]=e[a];return r}}]),e}(),xe=function(){function e(){m(this,e)}return b(e,null,[{key:"parseAVCDecoderConfigurationRecord",value:function(t){if(!(t.length<7)){for(var i,n,s=1+(3&t[4]),r=[],a=[],l=6,o=31&t[5],d=0;d<o;d++)if(n=t[l]<<8|t[l+1],l+=2,n){var c=t.subarray(l,l+n);l+=n,r.push(c),i||(i=e.parseSPS(Ge.removeEPB(c)))}var u,h=t[l];l++;for(var m=0;m<h;m++)u=t[l]<<8|t[l+1],l+=2,u&&(a.push(t.subarray(l,l+u)),l+=u);return{sps:i,spsArr:r,ppsArr:a,nalUnitSize:s}}}},{key:"parseSPS",value:function(e){var t=new le(e);t.readUByte();var i=t.readUByte(),n=t.readUByte(),s=t.readUByte();t.skipUEG();var r=420;if(100===i||110===i||122===i||244===i||44===i||83===i||86===i||118===i||128===i||138===i||144===i){var a=t.readUEG();if(a<=3&&(r=[0,420,422,444][a]),3===a&&t.skipBits(1),t.skipUEG(),t.skipUEG(),t.skipBits(1),t.readBool())for(var l=3!==a?8:12,o=0;o<l;o++)t.readBool()&&(o<6?t.skipScalingList(16):t.skipScalingList(64))}t.skipUEG();var d=t.readUEG();if(0===d)t.readUEG();else if(1===d){t.skipBits(1),t.skipUEG(),t.skipUEG();for(var c=t.readUEG(),u=0;u<c;u++)t.skipUEG()}t.skipUEG(),t.skipBits(1);var h=t.readUEG(),m=t.readUEG(),p=t.readBits(1);0===p&&t.skipBits(1),t.skipBits(1);var b,y,Z,f,X,L=0,v=0,W=0,G=0;if(t.readBool()&&(L=t.readUEG(),v=t.readUEG(),W=t.readUEG(),G=t.readUEG()),t.readBool()){if(t.readBool())switch(t.readUByte()){case 1:b=[1,1];break;case 2:b=[12,11];break;case 3:b=[10,11];break;case 4:b=[16,11];break;case 5:b=[40,33];break;case 6:b=[24,11];break;case 7:b=[20,11];break;case 8:b=[32,11];break;case 9:b=[80,33];break;case 10:b=[18,11];break;case 11:b=[15,11];break;case 12:b=[64,33];break;case 13:b=[160,99];break;case 14:b=[4,3];break;case 15:b=[3,2];break;case 16:b=[2,1];break;case 255:b=[t.readUByte()<<8|t.readUByte(),t.readUByte()<<8|t.readUByte()]}if(t.readBool()&&t.readBool(),t.readBool()&&(t.readBits(4),t.readBool()&&t.readBits(24)),t.readBool()&&(t.readUEG(),t.readUEG()),t.readBool()){var x=t.readBits(32),g=t.readBits(32);y=t.readBool(),X=(Z=g)/(f=2*x)}}return{codec:Le(e.subarray(1,4)),profileIdc:i,profileCompatibility:n,levelIdc:s,chromaFormat:r,width:Math.ceil(16*(h+1)-2*(L+v)),height:(2-p)*(m+1)*16-(p?2:4)*(W+G),sarRatio:b,fpsNum:Z,fpsDen:f,fps:X,fixedFrame:y}}}]),e}(),ge=function(){function e(){m(this,e)}return b(e,null,[{key:"getRateIndexByRate",value:function(t){return e.FREQ.indexOf(t)}},{key:"parseADTS",value:function(t,i){for(var n=t.length,s=0;s+2<n&&(255!==t[s]||240!=(246&t[s+1]));)s++;if(!(s>=n)){var r=s,a=[],l=(60&t[s+2])>>>2,o=e.FREQ[l];if(!o)throw new Error("Invalid sampling index: ".concat(l));for(var d,c,u=1+((192&t[s+2])>>>6),h=(1&t[s+2])<<2|(192&t[s+3])>>>6,m=e._getConfig(l,h,u),p=m.config,b=m.codec,y=0,Z=e.getFrameDuration(o);s+7<n;)if(255===t[s]&&240==(246&t[s+1])){if(n-s<(c=(3&t[s+3])<<11|t[s+4]<<3|(224&t[s+5])>>5))break;d=2*(1&~t[s+1]),a.push({pts:i+y*Z,data:t.subarray(s+7+d,s+c)}),y++,s+=c}else s++;return{skip:r,remaining:s>=n?void 0:t.subarray(s),frames:a,samplingFrequencyIndex:l,sampleRate:o,objectType:u,channelCount:h,codec:b,config:p,originCodec:"mp4a.40.".concat(u)}}}},{key:"parseAudioSpecificConfig",value:function(t){if(t.length){var i=t[0]>>>3,n=(7&t[0])<<1|t[1]>>>7,s=(120&t[1])>>>3,r=e.FREQ[n];if(r){var a=e._getConfig(n,s,i);return{samplingFrequencyIndex:n,sampleRate:r,objectType:i,channelCount:s,config:a.config,codec:a.codec,originCodec:"mp4a.40.".concat(i)}}}}},{key:"getFrameDuration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4;return 1024*t/e}},{key:"_getConfig",value:function(e,t,i){var n,s,r=[];return me?e>=6?(n=5,s=e-3):(n=2,s=e):pe?(n=2,s=e):(n=2===i||5===i?i:5,s=e,e>=6?s=e-3:1===t&&(n=2,s=e)),r[0]=n<<3,r[0]|=(14&e)>>1,r[1]=(1&e)<<7,r[1]|=t<<3,5===n&&(r[1]|=(14&s)>>1,r[2]=(1&s)<<7,r[2]|=8,r[3]=0),{config:r,codec:"mp4a.40.".concat(n)}}},{key:"getSilentFrame",value:function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}}}]),e}();y(ge,"FREQ",[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350]);var Se=function(){function e(){m(this,e)}return b(e,null,[{key:"parseHEVCDecoderConfigurationRecord",value:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(t.length<23)){i=i||{};for(var n,s,r,a,l,o=1+(3&t[21]),d=[],c=[],u=[],h=23,m=t[22],p=0;p<m;p++){r=63&t[h],a=t[h+1]<<8|t[h+2],h+=3;for(var b=0;b<a;b++)if(l=t[h]<<8|t[h+1],h+=2,l){switch(r){case 32:var y=t.subarray(h,h+l);n||(n=e.parseVPS(Ge.removeEPB(y),i)),u.push(y);break;case 33:var Z=t.subarray(h,h+l);s||(s=e.parseSPS(Ge.removeEPB(Z),i)),d.push(Z);break;case 34:c.push(t.subarray(h,h+l))}h+=l}}return{hvcC:i,sps:s,spsArr:d,ppsArr:c,vpsArr:u,nalUnitSize:o}}}},{key:"parseVPS",value:function(t,i){i=i||{};var n=new le(t);n.readUByte(),n.readUByte(),n.readBits(12);var s=n.readBits(3);return i.numTemporalLayers=Math.max(i.numTemporalLayers||0,s+1),n.readBits(17),e._parseProfileTierLevel(n,s,i),i}},{key:"parseSPS",value:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};i=i||{};var n=new le(t);n.readUByte(),n.readUByte(),n.readBits(4);var s=n.readBits(3);i.numTemporalLayers=Math.max(s+1,i.numTemporalLayers||0),i.temporalIdNested=n.readBits(1),e._parseProfileTierLevel(n,s,i),n.readUEG();var r=i.chromaFormatIdc=n.readUEG(),a=420;r<=3&&(a=[0,420,422,444][r]);var l=0;3===r&&(l=n.readBits(1));var o,d,c,u,h=n.readUEG(),m=n.readUEG(),p=n.readBits(1);if(1===p&&(o=n.readUEG(),d=n.readUEG(),c=n.readUEG(),u=n.readUEG()),i.bitDepthLumaMinus8=n.readUEG(),i.bitDepthChromaMinus8=n.readUEG(),1===p){var b=1!==r&&2!==r||0!==l?1:2,y=1===r&&0===l?2:1;h-=b*(d+o),m-=y*(u+c)}return{codec:"hev1.1.6.L93.B0",width:h,height:m,chromaFormat:a,hvcC:i}}},{key:"_parseProfileTierLevel",value:function(e,t,i){var n=i.generalTierFlag||0;i.generalProfileSpace=e.readBits(2),i.generalTierFlag=Math.max(e.readBits(1),n),i.generalProfileIdc=Math.max(e.readBits(5),i.generalProfileIdc||0),i.generalProfileCompatibilityFlags=e.readBits(32),i.generalConstraintIndicatorFlags=[e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8)];var s=e.readBits(8);n<i.generalTierFlag?i.generalLevelIdc=s:i.generalLevelIdc=Math.max(s,i.generalLevelIdc||0);for(var r=[],a=[],l=0;l<t;l++)r[l]=e.readBits(1),a[l]=e.readBits(1);t>0&&e.readBits(2*(8-t));for(var o=0;o<t;o++)0!==r[o]&&(e.readBits(2),e.readBits(1),e.readBits(5),e.readBits(16),e.readBits(16),e.readBits(4),e.readBits(16),e.readBits(16),e.readBits(12)),0!==a[o]&&e.readBits(8)}}]),e}(),Ve=1e3,ke=5e3,Ye=function(){function e(t,i,n){m(this,e),this.videoTrack=t,this.audioTrack=i,this.metadataTrack=n,this._baseDts=-1,this._baseDtsInited=!1,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastVideoDuration=0,this._keyFrameInNextChunk=!1,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0}return b(e,[{key:"fix",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t=Math.round(1e3*t);var s=this.videoTrack,r=this.audioTrack;!i&&n||(this._videoLastSample=null,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0),i&&!n&&(this._baseDtsInited=!1),this._baseDtsInited||this._calculateBaseDts(r,s),!n&&t&&(this._audioNextPts=this._videoNextDts=t);var a=this._baseDtsInited&&(this._videoTimestampBreak||!this.videoTrack.exist())&&(this._audioTimestampBreak||!this.audioTrack.exist());if(a&&this._resetBaseDtsWhenStreamBreaked(),this._fixAudio(r),this._keyFrameInNextChunk=!1,this._fixVideo(s),this.metadataTrack.exist()){var l=this.metadataTrack.timescale;this.metadataTrack.seiSamples.forEach((function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/l})),this.metadataTrack.flvScriptSamples.forEach((function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/l}))}s.samples.length&&(s.baseMediaDecodeTime=s.samples[0].dts),r.samples.length&&(r.baseMediaDecodeTime=r.samples[0].pts*r.timescale/1e3)}},{key:"_fixVideo",value:function(e){var t=this,i=e.samples;if(i.length){var n;if(i.forEach((function(e){e.dts-=t._baseDts,e.pts-=t._baseDts,e.keyframe&&(t._keyFrameInNextChunk=!0)})),e.fpsNum&&e.fpsDen)n=e.timescale*(e.fpsDen/e.fpsNum);else if(e.length>1){var s=e.samples[0],r=e.samples[i.length-1];n=Math.floor((r.dts-s.dts)/(i.length-1))}else n=this._lastVideoDuration||40;var a=i.pop();if(this._videoLastSample&&i.unshift(this._videoLastSample),this._videoLastSample=a,i.length){if(void 0===this._videoNextDts){var l=i[0];this._videoNextDts=l.dts}var o=i.length,d=0,c=i[0],u=this._videoNextDts-c.dts;if(Math.abs(u)>200){var h;if(Math.abs(c.dts-this._lastVideoExceptionChunkFirstDtsDot)>5e3)this._lastVideoExceptionChunkFirstDtsDot=c.dts,e.warnings.push({type:Q,nextDts:this._videoNextDts,firstSampleDts:c.dts,nextSampleDts:null===(h=i[1])||void 0===h?void 0:h.dts,sampleDuration:u});this._videoTimestampBreak>=5?(this._videoNextDts=c.dts,this._videoTimestampBreak=0):(c.dts+=u,c.pts+=u,this.audioTrack.exist()||(this._videoTimestampBreak=1))}for(var m=0;m<o;m++){var p=i[m].dts,b=i[m+1];((d=m<o-1?b.dts-p:a?a.dts-p:n)>1e3||d<0)&&(this._videoTimestampBreak++,Math.abs(p-this._lastVideoExceptionLargeGapDot)>5e3&&(this._lastVideoExceptionLargeGapDot=p,e.warnings.push({type:B,time:p/e.timescale,dts:p,originDts:i[m].originDts,nextDts:this._videoNextDts,sampleDuration:d,refSampleDuration:n})),d=n),i[m].duration=d,this._videoNextDts+=d,this._lastVideoDuration=d}}}}},{key:"_fixAudio",value:function(e){var t=this,i=e.samples;i.length&&(i.forEach((function(e){e.dts=e.pts-=t._baseDts})),this._doFixAudioInternal(e,i,1e3))}},{key:"_calculateBaseDts",value:function(e,t){var i=e.samples,n=t.samples;if(!i.length&&!n.length)return!1;var s=1/0,r=1/0;i.length&&(e.baseDts=s=i[0].pts),n.length&&(t.baseDts=r=n[0].dts),this._baseDts=Math.min(s,r);var a=r-s;return Number.isFinite(a)&&Math.abs(a)>500&&t.warnings.push({type:_,videoBaseDts:r,audioBasePts:s,baseDts:this._baseDts,delta:a}),this._baseDtsInited=!0,!0}},{key:"_resetBaseDtsWhenStreamBreaked",value:function(){this._calculateBaseDts(this.audioTrack,this.videoTrack)&&(this.audioTrack.exist()?this.videoTrack.exist()?this._baseDts-=Math.min(this._audioNextPts,this._videoNextDts):this._baseDts-=this._audioNextPts:this._baseDts-=this._videoNextDts,this._videoTimestampBreak=0,this._audioTimestampBreak=0)}},{key:"_doFixAudioInternal",value:function(e,t,i){e.sampleDuration||(e.sampleDuration=e.codecType===P?ge.getFrameDuration(e.timescale,i):this._getG711Duration(e));var n=e.sampleDuration,s=e.codecType===P?1024:n*e.timescale/1e3;if(void 0===this._audioNextPts){var r=t[0];this._audioNextPts=r.pts}for(var a=0;a<t.length;a++){var l=this._audioNextPts,o=t[a],d=o.pts-l;if(0===a&&this._audioTimestampBreak>=5&&this._keyFrameInNextChunk&&(l=this._audioNextPts=o.dts,d=0,this._audioTimestampBreak=0),!this._audioTimestampBreak&&d>=3*n&&d<=Ve&&!he){var c=this._getSilentFrame(e)||t[0].data.subarray(),u=Math.floor(d/n);Math.abs(o.pts-this._lastAudioExceptionGapDot)>ke&&(this._lastAudioExceptionGapDot=o.pts,e.warnings.push({type:A,pts:o.pts,originPts:o.originPts,count:u,nextPts:l,refSampleDuration:n}));for(var h=0;h<u;h++){var m=new ie(Math.floor(this._audioNextPts+n)-Math.floor(this._audioNextPts),c,s);m.originPts=Math.floor(this._baseDts+l),t.splice(a,0,m),this._audioNextPts+=n,a++}a--}else d<=-3*n&&d>=-1e3?(Math.abs(o.pts-this._lastAudioExceptionOverlapDot)>ke&&(this._lastAudioExceptionOverlapDot=o.pts,e.warnings.push({type:q,pts:o.pts,originPts:o.originPts,nextPts:l,refSampleDuration:n})),t.splice(a,1),a--):(Math.abs(d)>Ve&&(this._audioTimestampBreak++,Math.abs(o.pts-this._lastAudioExceptionLargeGapDot)>ke&&(this._lastAudioExceptionLargeGapDot=o.pts,e.warnings.push({type:O,time:o.pts/1e3,pts:o.pts,originPts:o.originPts,nextPts:l,sampleDuration:d,refSampleDuration:n}))),o.dts=o.pts=l,o.duration=s,this._audioNextPts+=n)}}},{key:"_getG711Duration",value:function(e){var t=e.sampleSize,i=e.channelCount,n=e.sampleRate,s=e.samples[0];if(s)return 2*s.data.byteLength/i/(t/8)/n*1e3}},{key:"_getSilentFrame",value:function(e){return e.codecType===P?ge.getSilentFrame(e.codec,e.channelCount):new Uint8Array(8*e.sampleDuration*e.channelCount)}}]),e}(),Te=function(){function e(){m(this,e)}return b(e,null,[{key:"parse",value:function(t){if(!(t.length<3)){var i={},n=e._parseValue(new DataView(t.buffer,t.byteOffset,t.byteLength)),s=e._parseValue(new DataView(t.buffer,t.byteOffset+n.size,t.byteLength-n.size));return i[n.data]=s.data,i}}},{key:"_parseValue",value:function(t){var i,n=t.byteLength,s=1,r=!1;switch(t.getUint8(0)){case 0:i=t.getFloat64(1),s+=8;break;case 1:i=!!t.getUint8(1),s+=1;break;case 2:var a=e._parseString(new DataView(t.buffer,t.byteOffset+s,t.byteLength-s));i=a.data,s+=a.size;break;case 3:i={};var l=0;for(9==(16777215&t.getUint32(n-4))&&(l=3);s<n-4;){var o=e._parseObject(new DataView(t.buffer,t.byteOffset+s,t.byteLength-s-l)),d=o.size,c=o.data;if(o.isEnd)break;i[c.name]=c.value,s+=d}if(s<=n-3)9===(16777215&t.getUint32(s-1))&&(s+=3);break;case 8:i={},s+=4;var u=0;for(9==(16777215&t.getUint32(n-4))&&(u=3);s<n-8;){var h=e._parseObject(new DataView(t.buffer,t.byteOffset+s,t.byteLength-s-u)),m=h.size,p=h.data;if(h.isEnd)break;i[p.name]=p.value,s+=m}if(s<=n-3)9===(16777215&t.getUint32(s-1))&&(s+=3);break;case 9:i=void 0,s=1,r=!0;break;case 10:i=[];var b=t.getUint32(1);s+=4;for(var y=0;y<b;y++){var Z=e._parseValue(new DataView(t.buffer,t.byteOffset+s,t.byteLength-s)),f=Z.data,X=Z.size;i.push(f),s+=X}break;case 11:var L=t.getFloat64(s)+6e4*t.getInt16(s+8);i=new Date(L),s+=10;break;case 12:var v=t.getUint32(1);s+=4,i="",v>0&&(i=de.decode(new Uint8Array(t.buffer,t.byteOffset+s,v))),s+=v;break;default:s=n}return{data:i,size:s,isEnd:r}}},{key:"_parseString",value:function(e){var t=e.getUint16(0),i="";return t>0&&(i=de.decode(new Uint8Array(e.buffer,e.byteOffset+2,t))),{data:i,size:2+t}}},{key:"_parseObject",value:function(t){if(!(t.byteLength<3)){var i=e._parseString(t),n=e._parseValue(new DataView(t.buffer,t.byteOffset+i.size,t.byteLength-i.size));return{data:{name:i.data,value:n.data},size:i.size+n.size,isEnd:n.isEnd}}}}]),e}(),we=new oe("FlvDemuxer"),Re=function(){function e(t,i,n){m(this,e),y(this,"_headerParsed",!1),y(this,"_remainingData",null),y(this,"_gopId",0),y(this,"_needAddMetaBeforeKeyFrameNal",!0),this.videoTrack=t||new $,this.audioTrack=i||new ee,this.metadataTrack=n||new ae,this._fixer=new Ye(this.videoTrack,this.audioTrack,this.metadataTrack)}return b(e,[{key:"demux",value:function(t){var i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],s=this.audioTrack,r=this.videoTrack,a=this.metadataTrack;if(!i&&n||(this._remainingData=null,this._headerParsed=!1),i?(r.reset(),s.reset(),a.reset()):(r.samples=[],s.samples=[],a.seiSamples=[],a.flvScriptSamples=[],r.warnings=[],s.warnings=[],this._remainingData&&(t=be(this._remainingData,t),this._remainingData=null)),!t.length)return{videoTrack:r,audioTrack:s,metadataTrack:a};var l=0;if(!this._headerParsed){if(!e.probe(t))throw new Error("Invalid flv file");s.present=(4&t[4])>>>2!=0,r.present=0!=(1&t[4]),this._headerParsed=!0,l=fe(t,5)+4}for(var o,d,c,u,h,m=t.length;l+15<m&&(o=t[l],!(l+15+(d=t[l+1]<<16|t[l+2]<<8|t[l+3])>m));)c=(t[l+7]<<24>>>0)+(t[l+4]<<16)+(t[l+5]<<8)+t[l+6],l+=11,u=t.subarray(l,l+d),8===o?this._parseAudio(u,c):9===o?this._parseVideo(u,c):18===o?this._parseScript(u,c):we.warn("Invalid tag type: ".concat(o)),(h=fe(t,l+=d))!==11+d&&we.warn("Invalid PrevTagSize ".concat(h," (").concat(11+d,")")),l+=4;return l<m&&(this._remainingData=t.subarray(l)),s.formatTimescale=r.formatTimescale=r.timescale=a.timescale=1e3,s.timescale=s.sampleRate||0,!s.exist()&&s.hasSample()&&s.reset(),!r.exist()&&r.hasSample()&&r.reset(),{videoTrack:r,audioTrack:s,metadataTrack:a}}},{key:"fix",value:function(e,t,i){return this._fixer.fix(e,t,i),{videoTrack:this.videoTrack,audioTrack:this.audioTrack,metadataTrack:this.metadataTrack}}},{key:"demuxAndFix",value:function(e,t,i,n){return this.demux(e,t,i),this.fix(n,t,i)}},{key:"_parseAudio",value:function(t,i){if(t.length){var n=(240&t[0])>>>4,s=this.audioTrack;if(10!==n&&7!==n&&8!==n)return we.warn("Unsupported sound format: ".concat(n)),void s.reset();if(10!==n){var r=(12&t[0])>>2,a=(2&t[0])>>1,l=1&t[0];s.sampleRate=e.AUDIO_RATE[r],s.sampleSize=a?16:8,s.channelCount=l+1}10===n?this._parseAac(t,i):this._parseG711(t,i,n)}}},{key:"_parseG711",value:function(e,t,i){var n=this.audioTrack;n.codecType=7===i?D:E,n.sampleRate=8e3,n.codec=n.codecType,n.samples.push(new ie(t,e.subarray(1)))}},{key:"_parseAac",value:function(e,t){var i=this.audioTrack;if(i.codecType=P,0===e[1]){var n=ge.parseAudioSpecificConfig(e.subarray(2));n?(i.codec=n.codec,i.channelCount=n.channelCount,i.sampleRate=n.sampleRate,i.config=n.config,i.objectType=n.objectType,i.sampleRateIndex=n.samplingFrequencyIndex):(i.reset(),we.warn("Cannot parse AudioSpecificConfig",e))}else if(1===e[1]){if(null==t)return;i.samples.push(new ie(t,e.subarray(2)))}else we.warn("Unknown AACPacketType: ".concat(e[1]))}},{key:"_parseVideo",value:function(e,t){var i=this;if(!(e.length<6)){var n=(240&e[0])>>>4,s=15&e[0],r=this.videoTrack;if(7!==s&&12!==s)return r.reset(),void we.warn("Unsupported codecId: ".concat(s));var a=12===s;r.codecType=a?J:U;var l=e[1],o=(e[2]<<16|e[3]<<8|e[4])<<8>>8;if(0===l){var d=e.subarray(5),c=a?Se.parseHEVCDecoderConfigurationRecord(d):xe.parseAVCDecoderConfigurationRecord(d);if(c){var u=c.hvcC,h=c.sps,m=c.ppsArr,p=c.spsArr,b=c.vpsArr,y=c.nalUnitSize;u&&(r.hvcC=r.hvcC||u),h&&(r.codec=h.codec,r.width=h.width,r.height=h.height,r.sarRatio=h.sarRatio,r.fpsNum=h.fpsNum,r.fpsDen=h.fpsDen),p.length&&(r.sps=p),m.length&&(r.pps=m),b&&b.length&&(r.vps=b),y&&(r.nalUnitSize=y)}else we.warn("Cannot parse ".concat(a?"HEVC":"AVC","DecoderConfigurationRecord"),e)}else if(1===l){var Z=Ge.parseAvcC(e.subarray(5),r.nalUnitSize);if((Z=this._checkAddMetaNalToUnits(a,Z,r))&&Z.length){var f=new te(t+o,t,Z);1===n&&f.setToKeyframe(),r.samples.push(f),Z.forEach((function(e){var n=a?e[0]>>>1&63:31&e[0];switch(n){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!a&&5!==n||a&&5===n)break;f.setToKeyframe();break;case 6:case 39:case 40:if(!a&&6!==n||a&&6===n)break;i.metadataTrack.seiSamples.push(new re(Ge.parseSEI(Ge.removeEPB(e),a),t+o))}})),f.keyframe&&this._gopId++,f.gopId=this._gopId}else we.warn("Cannot parse NALUs",e)}else 2===l||we.warn("Unknown AVCPacketType: ".concat(l))}}},{key:"_checkAddMetaNalToUnits",value:function(e,t,i){return e&&this._needAddMetaBeforeKeyFrameNal?t.map((function(e){return e[0]>>>1&63})).includes(32)?(this._needAddMetaBeforeKeyFrameNal=!1,t):(t.unshift(i.pps[0]),t.unshift(i.sps[0]),t.unshift(i.vps[0]),t.filter(Boolean)):(this._needAddMetaBeforeKeyFrameNal=!1,t)}},{key:"_parseScript",value:function(e,t){this.metadataTrack.flvScriptSamples.push(new se(Te.parse(e),t))}}],[{key:"probe",value:function(e){return 70===e[0]&&76===e[1]&&86===e[2]&&1===e[3]&&fe(e,5)>=9}}]),e}();y(Re,"AUDIO_RATE",[5500,11e3,22e3,44e3]),new oe("TsDemuxer");var Me=function(){function e(){m(this,e)}return b(e,null,[{key:"findBox",value:function(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=[];if(!t)return s;for(var r=0,a="",l=0;t.length>7;){if(r=fe(t),a=String.fromCharCode.apply(null,t.subarray(4,8)),l=8,1===r?(r=Xe(t,8),l+=8):r||(r=t.length),!i[0]||a===i[0]){var o=t.subarray(0,r);if(!(i.length<2))return e.findBox(o.subarray(l),i.slice(1),n+l);s.push({start:n,size:r,headerSize:l,type:a,data:o})}n+=r,t=t.subarray(r)}return s}},{key:"tfhd",value:function(e){return Ce(e,!0,(function(e,t){e.trackId=fe(t);var i=4,n=1&e.flags,s=2&e.flags,r=8&e.flags,a=16&e.flags,l=32&e.flags;n&&(i+=4,e.baseDataOffset=fe(t,i),i+=4),s&&(e.sampleDescriptionIndex=fe(t,i),i+=4),r&&(e.defaultSampleDuration=fe(t,i),i+=4),a&&(e.defaultSampleSize=fe(t,i),i+=4),l&&(e.defaultSampleFlags=fe(t,i))}))}},{key:"sidx",value:function(e){return Ce(e,!0,(function(e,t){var i=0;e.reference_ID=fe(t,i),i+=4,e.timescale=fe(t,i),i+=4,0===e.version?(e.earliest_presentation_time=fe(t,i),i+=4,e.first_offset=fe(t,i),i+=4):(e.earliest_presentation_time=Xe(t,i),i+=8,e.first_offset=Xe(t,i),i+=8),i+=2,e.references=[];var n=Ze(t,i);i+=2;for(var s=0;s<n;s++){var r={};e.references.push(r);var a=fe(t,i);i+=4,r.reference_type=a>>31&1,r.referenced_size=2147483647&a,r.subsegment_duration=fe(t,i),a=fe(t,i+=4),i+=4,r.starts_with_SAP=a>>31&1,r.SAP_type=a>>28&7,r.SAP_delta_time=268435455&a}}))}},{key:"moov",value:function(t){return Ce(t,!1,(function(t,i,n){t.mvhd=e.mvhd(e.findBox(i,["mvhd"],n)[0]),t.trak=e.findBox(i,["trak"],n).map((function(t){return e.trak(t)})),t.pssh=e.pssh(e.findBox(i,["pssh"],n)[0])}))}},{key:"mvhd",value:function(e){return Ce(e,!0,(function(e,t){var i=0;1===e.version?(e.timescale=fe(t,16),e.duration=Xe(t,20),i+=28):(e.timescale=fe(t,8),e.duration=fe(t,12),i+=16),e.nextTrackId=fe(t,i+76)}))}},{key:"trak",value:function(t){return Ce(t,!1,(function(t,i,n){t.tkhd=e.tkhd(e.findBox(i,["tkhd"],n)[0]),t.mdia=e.mdia(e.findBox(i,["mdia"],n)[0])}))}},{key:"tkhd",value:function(e){return Ce(e,!0,(function(e,t){var i=0;1===e.version?(e.trackId=fe(t,16),e.duration=Xe(t,24),i+=32):(e.trackId=fe(t,8),e.duration=fe(t,16),i+=20),e.width=fe(t,i+52),e.height=fe(t,i+56)}))}},{key:"mdia",value:function(t){return Ce(t,!1,(function(t,i,n){t.mdhd=e.mdhd(e.findBox(i,["mdhd"],n)[0]),t.hdlr=e.hdlr(e.findBox(i,["hdlr"],n)[0]),t.minf=e.minf(e.findBox(i,["minf"],n)[0])}))}},{key:"mdhd",value:function(e){return Ce(e,!0,(function(e,t){var i=0;1===e.version?(e.timescale=fe(t,16),e.duration=Xe(t,20),i+=28):(e.timescale=fe(t,8),e.duration=fe(t,12),i+=16);var n=Ze(t,i);e.language=String.fromCharCode(96+(n>>10&31),96+(n>>5&31),96+(31&n))}))}},{key:"hdlr",value:function(e){return Ce(e,!0,(function(e,t){0===e.version&&(e.handlerType=String.fromCharCode.apply(null,t.subarray(4,8)))}))}},{key:"minf",value:function(t){return Ce(t,!1,(function(t,i,n){t.vmhd=e.vmhd(e.findBox(i,["vmhd"],n)[0]),t.smhd=e.smhd(e.findBox(i,["smhd"],n)[0]),t.stbl=e.stbl(e.findBox(i,["stbl"],n)[0])}))}},{key:"vmhd",value:function(e){return Ce(e,!0,(function(e,t){e.graphicsmode=Ze(t),e.opcolor=[Ze(t,2),Ze(t,4),Ze(t,6)]}))}},{key:"smhd",value:function(e){return Ce(e,!0,(function(e,t){e.balance=Ze(t)}))}},{key:"stbl",value:function(t){return Ce(t,!1,(function(t,i,n){var s,r,a;t.stsd=e.stsd(e.findBox(i,["stsd"],n)[0]),t.stts=e.stts(e.findBox(i,["stts"],n)[0]),t.ctts=e.ctts(e.findBox(i,["ctts"],n)[0]),t.stsc=e.stsc(e.findBox(i,["stsc"],n)[0]),t.stsz=e.stsz(e.findBox(i,["stsz"],n)[0]),t.stco=e.stco(e.findBox(i,["stco"],n)[0]),t.stco||(t.co64=e.co64(e.findBox(i,["co64"],n)[0]),t.stco=t.co64);var l=null===(s=t.stsd.entries[0])||void 0===s||null===(r=s.sinf)||void 0===r||null===(a=r.schi)||void 0===a?void 0:a.tenc.default_IV_size;t.stss=e.stss(e.findBox(i,["stss"],n)[0]),t.senc=e.senc(e.findBox(i,["senc"],n)[0],l)}))}},{key:"senc",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return Ce(e,!0,(function(e,i){var n=0,s=fe(i,n);n+=4,e.samples=[];for(var r=0;r<s;r++){for(var a={InitializationVector:[]},l=0;l<t;l++)a.InitializationVector[l]=i[n+l];if(n+=t,2&e.flags){a.subsamples=[];var o=Ze(i,n);n+=2;for(var d=0;d<o;d++){var c={};c.BytesOfClearData=Ze(i,n),n+=2,c.BytesOfProtectedData=fe(i,n),n+=4,a.subsamples.push(c)}}e.samples.push(a)}}))}},{key:"pssh",value:function(e){return Ce(e,!0,(function(e,t){for(var i=[],n=[],s=0,r=0;r<16;r++)n.push(Fe(t[s+r]));if(s+=16,e.version>0){var a=fe(t,s);s+=4;for(var l=0;l<(""+a).length;l++)for(var o=0;o<16;o++){var d=t[s];s+=1,i.push(Fe(d))}}var c=fe(t,s);e.data_size=c,s+=4,e.kid=i,e.system_id=n,e.buffer=t}))}},{key:"stsd",value:function(t){return Ce(t,!0,(function(t,i,n){t.entryCount=fe(i),t.entries=e.findBox(i.subarray(4),[],n+4).map((function(t){switch(t.type){case"avc1":case"avc2":case"avc3":case"avc4":return e.avc1(t);case"hvc1":case"hev1":return e.hvc1(t);case"mp4a":return e.mp4a(t);case"alaw":case"ulaw":return e.alaw(t);case"enca":return Ce(t,!1,(function(t,i,n){t.channelCount=Ze(i,16),t.samplesize=Ze(i,18),t.sampleRate=fe(i,24)/65536,i=i.subarray(28),t.sinf=e.sinf(e.findBox(i,["sinf"],n)[0]),t.esds=e.esds(e.findBox(i,["esds"],n)[0])}));case"encv":return Ce(t,!1,(function(t,i,n){t.width=Ze(i,24),t.height=Ze(i,26),t.horizresolution=fe(i,28),t.vertresolution=fe(i,32),i=i.subarray(78),t.sinf=e.sinf(e.findBox(i,["sinf"],n)[0]),t.avcC=e.avcC(e.findBox(i,["avcC"],n)[0]),t.hvcC=e.hvcC(e.findBox(i,["hvcC"],n)[0]),t.pasp=e.pasp(e.findBox(i,["pasp"],n)[0])}))}})).filter(Boolean)}))}},{key:"tenc",value:function(e){return Ce(e,!1,(function(e,t){var i=6;e.default_IsEncrypted=t[i],i+=1,e.default_IV_size=t[i],i+=1,e.default_KID=[];for(var n=0;n<16;n++)e.default_KID.push(Fe(t[i])),i+=1}))}},{key:"schi",value:function(t){return Ce(t,!1,(function(t,i,n){t.tenc=e.tenc(e.findBox(i,["tenc"],n)[0])}))}},{key:"sinf",value:function(t){return Ce(t,!1,(function(t,i,n){t.schi=e.schi(e.findBox(i,["schi"],n)[0]),t.frma=e.frma(e.findBox(i,["frma"],n)[0])}))}},{key:"frma",value:function(e){return Ce(e,!1,(function(e,t){e.data_format="";for(var i=0;i<4;i++)e.data_format+=String.fromCharCode(t[i])}))}},{key:"avc1",value:function(t){return Ce(t,!1,(function(t,i,n){var s=Ie(t,i),r=i.subarray(s);n+=s,t.avcC=e.avcC(e.findBox(r,["avcC"],n)[0]),t.pasp=e.pasp(e.findBox(r,["pasp"],n)[0])}))}},{key:"avcC",value:function(e){return Ce(e,!1,(function(e,t){e.configurationVersion=t[0],e.AVCProfileIndication=t[1],e.profileCompatibility=t[2],e.AVCLevelIndication=t[3],e.codec=Le([t[1],t[2],t[3]]),e.lengthSizeMinusOne=3&t[4],e.spsLength=31&t[5],e.sps=[];for(var i=6,n=0;n<e.spsLength;n++){var s=Ze(t,i);i+=2,e.sps.push(t.subarray(i,i+s)),i+=s}e.ppsLength=t[i],i+=1,e.pps=[];for(var r=0;r<e.ppsLength;r++){var a=Ze(t,i);i+=2,e.pps.push(t.subarray(i,i+=a)),i+=a}}))}},{key:"hvc1",value:function(t){return Ce(t,!1,(function(t,i,n){var s=Ie(t,i),r=i.subarray(s);n+=s,t.hvcC=e.hvcC(e.findBox(r,["hvcC"],n)[0]),t.pasp=e.pasp(e.findBox(r,["pasp"],n)[0])}))}},{key:"hvcC",value:function(e){return Ce(e,!1,(function(t,i){t.data=e.data,t.codec="hev1.1.6.L93.B0",t.configurationVersion=i[0];var n=i[1];t.generalProfileSpace=n>>6,t.generalTierFlag=(32&n)>>5,t.generalProfileIdc=31&n,t.generalProfileCompatibility=fe(i,2),t.generalConstraintIndicatorFlags=i.subarray(6,12),t.generalLevelIdc=i[12],t.avgFrameRate=Ze(i,19),t.numOfArrays=i[22],t.vps=[],t.sps=[],t.pps=[];for(var s=23,r=0,a=0,l=0,o=0;o<t.numOfArrays;o++){r=63&i[s],a=Ze(i,s+1),s+=3;for(var d,c=[],u=0;u<a;u++)l=Ze(i,s),s+=2,c.push(i.subarray(s,s+l)),s+=l;if(32===r)(d=t.vps).push.apply(d,c);else if(33===r){var h;(h=t.sps).push.apply(h,c)}else if(34===r){var m;(m=t.pps).push.apply(m,c)}}}))}},{key:"pasp",value:function(e){return Ce(e,!1,(function(e,t){e.hSpacing=fe(t),e.vSpacing=fe(t,4)}))}},{key:"mp4a",value:function(t){return Ce(t,!1,(function(t,i,n){var s=He(t,i);t.esds=e.esds(e.findBox(i.subarray(s),["esds"],n+s)[0])}))}},{key:"esds",value:function(e){return Ce(e,!0,(function(e,t){e.codec="mp4a.";for(var i=0,n=0,s=0,r=0;t.length;){for(r=t[i=0],n=t[i+1],i+=2;128&n;)s=(127&n)<<7,n=t[i],i+=1;if(s+=127&n,3===r)t=t.subarray(i+3);else{if(4!==r){if(5===r){var a=e.config=t.subarray(i,i+s),l=(248&a[0])>>3;return 31===l&&a.length>=2&&(l=32+((7&a[0])<<3)+((224&a[1])>>5)),e.objectType=l,e.codec+=l.toString(16),void("."===e.codec[e.codec.length-1]&&(e.codec=e.codec.substring(0,e.codec.length-1)))}return void("."===e.codec[e.codec.length-1]&&(e.codec=e.codec.substring(0,e.codec.length-1)))}e.codec+=(t[i].toString(16)+".").padStart(3,"0"),t=t.subarray(i+13)}}}))}},{key:"alaw",value:function(e){return Ce(e,!1,(function(e,t){He(e,t)}))}},{key:"stts",value:function(e){return Ce(e,!0,(function(e,t){for(var i=fe(t),n=[],s=4,r=0;r<i;r++)n.push({count:fe(t,s),delta:fe(t,s+4)}),s+=8;e.entryCount=i,e.entries=n}))}},{key:"ctts",value:function(e){return Ce(e,!0,(function(e,t){var i=fe(t),n=[],s=4;if(1===e.version)for(var r=0;r<i;r++)n.push({count:fe(t,s),offset:fe(t,s+4)}),s+=8;else for(var a=0;a<i;a++)n.push({count:fe(t,s),offset:-(1+~fe(t,s+4))}),s+=8;e.entryCount=i,e.entries=n}))}},{key:"stsc",value:function(e){return Ce(e,!0,(function(e,t){for(var i=fe(t),n=[],s=4,r=0;r<i;r++)n.push({firstChunk:fe(t,s),samplesPerChunk:fe(t,s+4),sampleDescriptionIndex:fe(t,s+8)}),s+=12;e.entryCount=i,e.entries=n}))}},{key:"stsz",value:function(e){return Ce(e,!0,(function(e,t){var i=fe(t),n=fe(t,4),s=[];if(!i)for(var r=8,a=0;a<n;a++)s.push(fe(t,r)),r+=4;e.sampleSize=i,e.sampleCount=n,e.entrySizes=s}))}},{key:"stco",value:function(e){return Ce(e,!0,(function(e,t){for(var i=fe(t),n=[],s=4,r=0;r<i;r++)n.push(fe(t,s)),s+=4;e.entryCount=i,e.entries=n}))}},{key:"co64",value:function(e){return Ce(e,!0,(function(e,t){for(var i=fe(t),n=[],s=4,r=0;r<i;r++)n.push(Xe(t,s)),s+=8;e.entryCount=i,e.entries=n}))}},{key:"stss",value:function(e){return Ce(e,!0,(function(e,t){for(var i=fe(t),n=[],s=4,r=0;r<i;r++)n.push(fe(t,s)),s+=4;e.entryCount=i,e.entries=n}))}},{key:"moof",value:function(t){return Ce(t,!1,(function(t,i,n){t.mfhd=e.mfhd(e.findBox(i,["mfhd"],n)[0]),t.traf=e.findBox(i,["traf"],n).map((function(t){return e.traf(t)}))}))}},{key:"mfhd",value:function(e){return Ce(e,!0,(function(e,t){e.sequenceNumber=fe(t)}))}},{key:"traf",value:function(t){return Ce(t,!1,(function(t,i,n){t.tfhd=e.tfhd(e.findBox(i,["tfhd"],n)[0]),t.tfdt=e.tfdt(e.findBox(i,["tfdt"],n)[0]),t.trun=e.trun(e.findBox(i,["trun"],n)[0])}))}},{key:"trun",value:function(e){return Ce(e,!0,(function(e,t){var i=e.version,n=e.flags,s=t.length,r=e.sampleCount=fe(t),a=4;if(s>a&&1&n&&(e.dataOffset=-(1+~fe(t,a)),a+=4),s>a&&4&n&&(e.firstSampleFlags=fe(t,a),a+=4),e.samples=[],s>a)for(var l,o=0;o<r;o++)l={},256&n&&(l.duration=fe(t,a),a+=4),512&n&&(l.size=fe(t,a),a+=4),1024&n&&(l.flags=fe(t,a),a+=4),2048&n&&(l.cts=i?-(1+~fe(t,a+4)):fe(t,a),a+=4),e.samples.push(l)}))}},{key:"tfdt",value:function(e){return Ce(e,!0,(function(e,t){1===e.version?e.baseMediaDecodeTime=Xe(t):e.baseMediaDecodeTime=fe(t)}))}},{key:"probe",value:function(t){return!!e.findBox(t,["ftyp"])}},{key:"parseSampleFlags",value:function(e){return{isLeading:(12&e[0])>>>2,dependsOn:3&e[0],isDependedOn:(192&e[1])>>>6,hasRedundancy:(48&e[1])>>>4,paddingValue:(14&e[1])>>>1,isNonSyncSample:1&e[1],degradationPriority:e[2]<<8|e[3]}}},{key:"moovToTrack",value:function(e,t,i){var n,s,r=e.trak;if(r&&r.length){var a=r.find((function(e){var t,i;return"vide"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)})),l=r.find((function(e){var t,i;return"soun"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)}));if(a&&t){var o,d,c,u,h,m,p,b=t,y=null===(o=a.tkhd)||void 0===o?void 0:o.trackId;null!=y&&(b.id=a.tkhd.trackId),b.tkhdDuration=a.tkhd.duration,b.mvhdDurtion=e.mvhd.duration,b.mvhdTimecale=e.mvhd.timescale,b.timescale=b.formatTimescale=a.mdia.mdhd.timescale,b.duration=a.mdia.mdhd.duration||b.mvhdDurtion/b.mvhdTimecale*b.timescale;var Z,f,X,L,v,W,G,x,g=a.mdia.minf.stbl.stsd.entries[0];if(b.width=g.width,b.height=g.height,g.pasp&&(b.sarRatio=[g.pasp.hSpacing,g.pasp.vSpacing]),g.hvcC)b.codecType=J,b.codec=g.hvcC.codec,b.vps=g.hvcC.vps,b.sps=g.hvcC.sps,b.pps=g.hvcC.pps,b.hvcC=g.hvcC.data;else{if(!g.avcC)throw new Error("unknown video stsd entry");b.codec=g.avcC.codec,b.sps=g.avcC.sps,b.pps=g.avcC.pps}if(b.present=!0,b.ext={},b.ext.stss=null===(d=a.mdia)||void 0===d||null===(c=d.minf)||void 0===c||null===(u=c.stbl)||void 0===u?void 0:u.stss,b.ext.ctts=null===(h=a.mdia)||void 0===h||null===(m=h.minf)||void 0===m||null===(p=m.stbl)||void 0===p?void 0:p.ctts,g&&"encv"===g.type)b.isVideoEncryption=!0,g.default_KID=null===(Z=g.sinf)||void 0===Z||null===(f=Z.schi)||void 0===f?void 0:f.tenc.default_KID,g.default_IsEncrypted=null===(X=g.sinf)||void 0===X||null===(L=X.schi)||void 0===L?void 0:L.tenc.default_IsEncrypted,g.default_IV_size=null===(v=g.sinf)||void 0===v||null===(W=v.schi)||void 0===W?void 0:W.tenc.default_IV_size,b.videoSenc=a.mdia.minf.stbl.senc&&a.mdia.minf.stbl.senc.samples,g.data_format=null===(G=g.sinf)||void 0===G||null===(x=G.frma)||void 0===x?void 0:x.data_format,b.useEME=e.useEME,b.kidValue=e.kidValue,b.pssh=e.pssh,b.encv=g}if(l&&i){var S,V,k,Y,T,w,R,M,K,I=i,H=null===(S=l.tkhd)||void 0===S?void 0:S.trackId;null!=H&&(I.id=l.tkhd.trackId),I.tkhdDuration=l.tkhd.duration,I.mvhdDurtion=e.mvhd.duration,I.mvhdTimecale=e.mvhd.timescale,I.timescale=I.formatTimescale=l.mdia.mdhd.timescale,I.duration=l.mdia.mdhd.duration||I.mvhdDurtion/I.mvhdTimecale*I.timescale;var C,N,F,z,j,U,P,_,B=l.mdia.minf.stbl.stsd.entries[0];switch(I.sampleSize=B.sampleSize,I.sampleRate=B.sampleRate,I.channelCount=B.channelCount,I.present=!0,B.type){case"alaw":I.codecType=I.codec=D,I.sampleRate=8e3;break;case"ulaw":I.codecType=I.codec=E,I.sampleRate=8e3;break;default:I.sampleDuration=ge.getFrameDuration(I.sampleRate,I.timescale),I.sampleRateIndex=ge.getRateIndexByRate(I.sampleRate),I.objectType=(null===(n=B.esds)||void 0===n?void 0:n.objectType)||2,B.esds&&(I.config=Array.from(B.esds.config)),I.codec=(null===(s=B.esds)||void 0===s?void 0:s.codec)||"mp4a.40.2"}if(I.sampleDuration=ge.getFrameDuration(I.sampleRate,I.timescale),I.objectType=(null===(V=B.esds)||void 0===V?void 0:V.objectType)||2,B.esds&&(B.esds.config?I.config=Array.from(B.esds.config):console.warn("esds config is null")),I.codec=(null===(k=B.esds)||void 0===k?void 0:k.codec)||"mp4a.40.2",I.sampleRateIndex=ge.getRateIndexByRate(I.sampleRate),I.ext={},I.ext.stss=null===(Y=l.mdia)||void 0===Y||null===(T=Y.minf)||void 0===T||null===(w=T.stbl)||void 0===w?void 0:w.stss,I.ext.ctts=null===(R=l.mdia)||void 0===R||null===(M=R.minf)||void 0===M||null===(K=M.stbl)||void 0===K?void 0:K.ctts,I.present=!0,B&&"enca"===B.type)I.isAudioEncryption=!0,B.data_format=null===(C=B.sinf)||void 0===C||null===(N=C.frma)||void 0===N?void 0:N.data_format,B.default_KID=null===(F=B.sinf)||void 0===F||null===(z=F.schi)||void 0===z?void 0:z.tenc.default_KID,B.default_IsEncrypted=null===(j=B.sinf)||void 0===j||null===(U=j.schi)||void 0===U?void 0:U.tenc.default_IsEncrypted,B.default_IV_size=null===(P=B.sinf)||void 0===P||null===(_=P.schi)||void 0===_?void 0:_.tenc.default_IV_size,I.audioSenc=l.mdia.minf.stbl.senc&&l.mdia.minf.stbl.senc.samples,I.useEME=e.useEME,I.kidValue=e.kidValue,I.enca=B}if(i&&(i.isVideoEncryption=!!t&&t.isVideoEncryption),t&&(t.isAudioEncryption=!!i&&i.isAudioEncryption),null!=t&&t.encv||null!=i&&i.enca){var Q,O,A=null==t||null===(Q=t.encv)||void 0===Q?void 0:Q.default_KID,q=null==i||null===(O=i.enca)||void 0===O?void 0:O.default_KID,$=A||q?(A||q).join(""):null;t&&(t.kid=$),i&&(i.kid=$)}return t&&(t.flags=3841),i&&(i.flags=1793),{videoTrack:t,audioTrack:i}}}},{key:"evaluateDefaultDuration",value:function(e,t,i){var n,s=null==t||null===(n=t.samples)||void 0===n?void 0:n.length;return s?1024*s/t.timescale*e.timescale/i:1024}},{key:"moofToSamples",value:function(t,i,n){var s={};return t.mfhd&&(i&&(i.sequenceNumber=t.mfhd.sequenceNumber),n&&(n.sequenceNumber=t.mfhd.sequenceNumber)),t.traf.forEach((function(t){var r=t.tfhd,a=t.tfdt,l=t.trun;if(r&&l){a&&(i&&i.id===r.trackId&&(i.baseMediaDecodeTime=a.baseMediaDecodeTime),n&&n.id===r.trackId&&(n.baseMediaDecodeTime=a.baseMediaDecodeTime));var o=r.defaultSampleSize||0,d=r.defaultSampleDuration||e.evaluateDefaultDuration(i,n,l.samples.length||l.sampleCount),c=l.dataOffset||0,u=0,h=-1;if(!l.samples.length&&l.sampleCount){s[r.trackId]=[];for(var m=0;m<l.sampleCount;m++)s[r.trackId].push({offset:c,dts:u,duration:d,size:o}),u+=d,c+=o}else s[r.trackId]=l.samples.map((function(e,t){return(e={offset:c,dts:u,pts:u+(e.cts||0),duration:e.duration||d,size:e.size||o,gopId:h,keyframe:0===t||null!==e.flags&&void 0!==e.flags&&(65536&e.flags)>>>0!=65536}).keyframe&&(h++,e.gopId=h),u+=e.duration,c+=e.size,e}))}})),s}},{key:"moovToSamples",value:function(e){var t=e.trak;if(t&&t.length){var i=t.find((function(e){var t,i;return"vide"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)})),n=t.find((function(e){var t,i;return"soun"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)}));if(i||n){var s,r;if(i){var a,l,o=null===(a=i.mdia)||void 0===a||null===(l=a.minf)||void 0===l?void 0:l.stbl;if(!o)return;var d=o.stts,c=o.stsc,u=o.stsz,h=o.stco,m=o.stss,p=o.ctts;if(!(d&&c&&u&&h&&m))return;s=Ke(d,c,u,h,p,m)}if(n){var b,y,Z,f=null===(b=n.mdia)||void 0===b||null===(y=b.minf)||void 0===y?void 0:y.stbl;if(!f)return;var X=null===(Z=n.mdia.mdhd)||void 0===Z?void 0:Z.timescale,L=f.stts,v=f.stsc,W=f.stsz,G=f.stco;if(!(X&&L&&v&&W&&G))return;r=Ke(L,v,W,G)}return{videoSamples:s,audioSamples:r}}}}}]),e}();function Ke(e,t,i,n,s,r){var a,l,o,d=[],c=null==s?void 0:s.entries,u=t.entries,h=n.entries,m=i.entrySizes,p=null==r?void 0:r.entries;p&&(a={},p.forEach((function(e){a[e-1]=!0}))),c&&(l=[],c.forEach((function(e){for(var t=e.count,i=e.offset,n=0;n<t;n++)l.push(i)})));var b=-1,y=0,Z=0,f=0,X=0,L=0,v=u[0].samplesPerChunk,W=u[1]?u[1].firstChunk-1:1/0;return e.entries.forEach((function(e){for(var t=e.count,n=e.delta,s=0;s<t;s++)o={dts:y,duration:n,size:m[Z]||i.sampleSize,offset:h[f]+L,index:Z},p&&(o.keyframe=a[Z],o.keyframe&&b++,o.gopId=b),l&&Z<l.length&&(o.pts=o.dts+l[Z]),d.push(o),y+=n,++Z<v?L+=o.size:(f++,L=0,f>=W&&(X++,W=u[X+1]?u[X+1].firstChunk-1:1/0),v+=u[X].samplesPerChunk)})),d}function Ie(e,t){return e.dataReferenceIndex=Ze(t,6),e.width=Ze(t,24),e.height=Ze(t,26),e.horizresolution=fe(t,28),e.vertresolution=fe(t,32),e.frameCount=Ze(t,40),e.depth=Ze(t,74),78}function He(e,t){return e.dataReferenceIndex=Ze(t,6),e.channelCount=Ze(t,16),e.sampleSize=Ze(t,18),e.sampleRate=fe(t,24)/65536,28}function Ce(e,t,i){if(e){if(e.size!==e.data.length)throw new Error("box ".concat(e.type," size !== data.length"));var n={start:e.start,size:e.size,headerSize:e.headerSize,type:e.type};return t&&(n.version=e.data[e.headerSize],n.flags=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<16)+(e[t+1]<<8)+(e[t+2]||0)}(e.data,e.headerSize+1),n.headerSize+=4),i(n,e.data.subarray(n.headerSize),n.start+n.headerSize),n}}var Ne=function(e,t,i){for(var n=String(i),s=t>>0,r=Math.ceil(s/n.length),a=[],l=String(e);r--;)a.push(n);return a.join("").substring(0,s-l.length)+l},Fe=function(){for(var e=[],t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];return i.forEach((function(t){e.push(Ne(Number(t).toString(16),2,0))})),e[0]};function ze(e){for(var t=0,i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];n.forEach((function(e){t+=e.length}));var r=new e(t),a=0;return n.forEach((function(e){r.set(e,a),a+=e.length})),r}var je=function(){function e(){m(this,e),this.buffer=new Uint8Array(0)}return b(e,[{key:"write",value:function(){for(var e=this,t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];i.forEach((function(t){t?e.buffer=ze(Uint8Array,e.buffer,t):window.console.warn(t)}))}}],[{key:"writeUint16",value:function(e){return new Uint8Array([e>>8&255,255&e])}},{key:"writeUint32",value:function(e){return new Uint8Array([e>>24,e>>16&255,e>>8&255,255&e])}}]),e}(),Ue=Math.pow(2,32)-1,Je=function(){function e(){m(this,e)}return b(e,null,[{key:"box",value:function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];var s=8+(i=i.filter(Boolean)).reduce((function(e,t){return e+t.byteLength}),0),r=new Uint8Array(s);r[0]=s>>24&255,r[1]=s>>16&255,r[2]=s>>8&255,r[3]=255&s,r.set(e,4);var a=8;return i.forEach((function(e){r.set(e,a),a+=e.byteLength})),r}},{key:"ftyp",value:function(t){return t.find((function(e){return e.type===F&&e.codecType===J}))?e.FTYPHEV1:e.FTYPAVC1}},{key:"initSegment",value:function(t){return be(e.ftyp(t),e.moov(t))}},{key:"pssh",value:function(t){var i=new Uint8Array([1,0,0,0].concat([16,119,239,236,192,178,77,2,172,227,60,30,82,226,251,75],[0,0,0,1],We(t.kid),[0,0,0,0]));return e.box(e.types.pssh,i)}},{key:"moov",value:function(t){if(t[0].useEME&&(t[0].encv||t[0].enca)){t[0].pssh||(t[0].pssh={kid:t[0].kid});var i=this.pssh(t[0].pssh);return e.box.apply(e,[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale),e.mvex(t)].concat(T(t.map((function(t){return e.trak(t)}))),[i]))}return e.box.apply(e,[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale)].concat(T(t.map((function(t){return e.trak(t)}))),[e.mvex(t)]))}},{key:"mvhd",value:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4,n=e.box(e.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,i>>24&255,i>>16&255,i>>8&255,255&i,t>>24&255,t>>16&255,t>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]));return n}},{key:"trak",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.tkhdDuration||0,t.width,t.height),e.mdia(t))}},{key:"tkhd",value:function(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=e.box(e.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>24&255,t>>16&255,t>>8&255,255&t,0,0,0,0,i>>24&255,i>>16&255,i>>8&255,255&i,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>8&255,255&n,0,0,s>>8&255,255&s,0,0]));return r}},{key:"mdia",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minf(t))}},{key:"mdhd",value:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4,n=e.box(e.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,i>>24&255,i>>16&255,i>>8&255,255&i,t>>24&255,t>>16&255,t>>8&255,255&t,85,196,0,0]));return n}},{key:"hdlr",value:function(t){return e.box(e.types.hdlr,e.HDLR_TYPES[t])}},{key:"minf",value:function(t){return e.box(e.types.minf,t.type===F?e.VMHD:e.SMHD,e.DINF,e.stbl(t))}},{key:"stbl",value:function(t){var i=[];return t&&t.ext&&t.ext.stss&&i.push(e.stss(t.ext.stss.entries)),e.box(e.types.stbl,e.stsd(t),e.STTS,i[0],e.STSC,e.STSZ,e.STCO)}},{key:"stsd",value:function(t){var i;return i="audio"===t.type?t.useEME&&t.enca?e.enca(t):e.mp4a(t):t.useEME&&t.encv?e.encv(t):e.avc1hev1(t),e.box(e.types.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),i)}},{key:"enca",value:function(t){var i=t.enca.channelCount,n=t.enca.sampleRate,s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>8&255,255&n,0,0]),r=e.esds(t.config),a=e.sinf(t.enca);return e.box(e.types.enca,s,r,a)}},{key:"encv",value:function(t){var i,n,s=t.sps.length>0?t.sps[0]:[],r=t.pps.length>0?t.pps[0]:[],a=t.width,l=t.height,o=t.sarRatio[0],d=t.sarRatio[1],c=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,a>>8&255,255&a,l>>8&255,255&l,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),u=new Uint8Array((i=(n=[1,s[1],s[2],s[3],255,225,s.length>>>8&255,255&s.length]).concat.apply(n,T(s)).concat([1,r.length>>>8&255,255&r.length])).concat.apply(i,T(r))),h=new Uint8Array([0,0,88,57,0,15,200,192,0,4,86,72]),m=e.sinf(t.encv),p=new Uint8Array([o>>24,o>>16&255,o>>8&255,255&o,d>>24,d>>16&255,d>>8&255,255&d]);return e.box(e.types.encv,c,e.box(e.types.avcC,u),e.box(e.types.btrt,h),m,e.box(e.types.pasp,p))}},{key:"schi",value:function(t){var i=new Uint8Array([]),n=e.tenc(t);return e.box(e.types.schi,i,n)}},{key:"tenc",value:function(t){var i=new Uint8Array([0,0,0,0,0,0,255&t.default_IsEncrypted,255&t.default_IV_size].concat(We(t.default_KID)));return e.box(e.types.tenc,i)}},{key:"sinf",value:function(t){var i=new Uint8Array([]),n=new Uint8Array([t.data_format.charCodeAt(0),t.data_format.charCodeAt(1),t.data_format.charCodeAt(2),t.data_format.charCodeAt(3)]),s=new Uint8Array([0,0,0,0,99,101,110,99,0,1,0,0]),r=e.schi(t);return e.box(e.types.sinf,i,e.box(e.types.frma,n),e.box(e.types.schm,s),r)}},{key:"avc1hev1",value:function(t){var i=t.codecType===J,n=i?e.types.hvc1:e.types.avc1,s=i?e.hvcC(t):e.avcC(t),r=[new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t.width>>8&255,255&t.width,t.height>>8&255,255&t.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),s];return i?r.push(e.box(e.types.fiel,new Uint8Array([1,0]))):t.sarRatio&&t.sarRatio.length>1&&r.push(e.pasp(t.sarRatio)),e.box.apply(e,[n].concat(r))}},{key:"avcC",value:function(t){var i,n,s,r=[],a=[];return t.sps.forEach((function(e){s=e.byteLength,r.push(s>>>8&255),r.push(255&s),r.push.apply(r,T(e))})),t.pps.forEach((function(e){s=e.byteLength,a.push(s>>>8&255),a.push(255&s),a.push.apply(a,T(e))})),e.box(e.types.avcC,new Uint8Array((i=(n=[1,r[3],r[4],r[5],255,224|t.sps.length]).concat.apply(n,r).concat([t.pps.length])).concat.apply(i,a)))}},{key:"hvcC",value:function(t){var i=t.hvcC;if(i instanceof ArrayBuffer||i instanceof Uint8Array)return i;var n,s=t.vps,r=t.sps,a=t.pps;if(i){var l=i.generalProfileCompatibilityFlags,o=i.generalConstraintIndicatorFlags,d=(s.length&&1)+(r.length&&1)+(a.length&&1);n=[1,i.generalProfileSpace<<6|i.generalTierFlag<<5|i.generalProfileIdc,l>>>24,l>>>16,l>>>8,l,o[0],o[1],o[2],o[3],o[4],o[5],i.generalLevelIdc,240,0,252,252|i.chromaFormatIdc,248|i.bitDepthLumaMinus8,248|i.bitDepthChromaMinus8,0,0,i.numTemporalLayers<<3|i.temporalIdNested<<2|3,d];var c=function(e){var t;n.push(e.length>>8,e.length),(t=n).push.apply(t,T(e))};s.length&&(n.push(160,0,s.length),s.forEach(c)),r.length&&(n.push(161,0,r.length),r.forEach(c)),a.length&&(n.push(162,0,a.length),a.forEach(c))}else n=[1,1,96,0,0,0,144,0,0,0,0,0,93,240,0,252,253,248,248,0,0,15,3,160,0,1,0,24,64,1,12,1,255,255,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,153,152,9,161,0,1,0,45,66,1,1,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,160,2,128,128,45,22,89,153,164,147,43,154,128,128,128,130,0,0,3,0,2,0,0,3,0,50,16,162,0,1,0,7,68,1,193,114,180,98,64];return e.box(e.types.hvcC,new Uint8Array(n))}},{key:"pasp",value:function(t){var i=Y(t,2),n=i[0],s=i[1];return e.box(e.types.pasp,new Uint8Array([n>>24,n>>16&255,n>>8&255,255&n,s>>24,s>>16&255,s>>8&255,255&s]))}},{key:"mp4a",value:function(t){return e.box(e.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,t.sampleRate>>8&255,255&t.sampleRate,0,0]),t.config.length?e.esds(t.config):void 0)}},{key:"esds",value:function(t){var i=t.length;return e.box(e.types.esds,new Uint8Array([0,0,0,0,3,23+i,0,0,0,4,15+i,64,21,0,6,0,0,0,218,192,0,0,218,192,5].concat([i]).concat(t).concat([6,1,2])))}},{key:"mvex",value:function(t){return e.box.apply(e,[e.types.mvex].concat(T(t.map((function(t){return e.trex(t.id)})))))}},{key:"trex",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}},{key:"trex1",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,2,0,0,0,0,0,0,1,0,0]))}},{key:"trex2",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,4,0,0,0,0,0,2,0,0,0]))}},{key:"moof",value:function(t){return e.box.apply(e,[e.types.moof,e.mfhd(t[0].samples?t[0].samples[0].gopId:0)].concat(T(t.map((function(t){return e.traf(t)})))))}},{key:"mfhd",value:function(t){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"traf",value:function(t){var i=e.tfhd(t.id),n=e.tfdt(t,t.baseMediaDecodeTime),s=0;if(t.isVideo&&t.videoSenc&&t.videoSenc.forEach((function(e){s+=8,e.subsamples&&e.subsamples.length&&(s+=2,s+=6*e.subsamples.length)})),t.videoSencLength=s,t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)){if(t.isVideoEncryption){if(t.isVideo){var r=e.saiz(t),a=e.saio(t),l=e.trun1(t),o=e.senc(t);return e.box(e.types.traf,i,n,r,a,l,o)}if(t.isAudioEncryption){var d=e.sbgp(),c=e.saiz(t),u=e.saio(t),h=e.senc(t),m=e.trun1(t);return e.box(e.types.traf,i,n,d,c,u,h,m)}var p=e.sbgp(),b=e.trun1(t);return e.box(e.types.traf,i,n,p,b)}if(t.isVideo){var y=e.trun1(t);return e.box(e.types.traf,i,n,y)}var Z=e.sbgp(),f=e.saiz(t),X=e.saio(t),L=e.senc(t),v=e.trun1(t);return e.box(e.types.traf,i,n,Z,f,X,L,v)}var W=e.sdtp(t);return e.box(e.types.traf,i,n,W,e.trun(t.samples,W.byteLength+76))}},{key:"sdtp",value:function(t){var i=new je;return t.samples.forEach((function(e){i.write(new Uint8Array(t.isVideo?[e.keyframe?32:16]:[16]))})),e.box(e.types.sdtp,this.extension(0,0),i.buffer)}},{key:"trun1",value:function(t){var i=new je,n=je.writeUint32(t.samples.length),s=null;if(t.isVideo){var r=t.videoSencLength;s=je.writeUint32(16*t.samples.length+r+149),!t.isVideoEncryption&&t.isAudioEncryption&&(s=je.writeUint32(16*t.samples.length+92))}else{var a=12*t.samples.length+124;t.isAudioEncryption&&(a=12*t.samples.length+8*t.audioSenc.length+177),s=je.writeUint32(a)}return t.samples.forEach((function(e){i.write(je.writeUint32(e.duration)),i.write(je.writeUint32(e.size)),i.write(je.writeUint32(e.keyframe?33554432:65536)),t.isVideo&&i.write(je.writeUint32(e.cts?e.cts:0))})),e.box(e.types.trun,this.extension(0,t.flags),n,s,i.buffer)}},{key:"senc",value:function(t){var i=new je,n=t.samples.length,s=t.isVideo?16:8,r=t.isVideo?2:0,a=[],l=0;return t.isVideo?(a=t.videoSenc,l=t.videoSencLength):a=t.audioSenc,l=l||s*n,i.write(je.writeUint32(16+l),e.types.senc,this.extension(0,r)),i.write(je.writeUint32(n)),a.forEach((function(e){for(var t=0;t<e.InitializationVector.length;t++)i.write(new Uint8Array([e.InitializationVector[t]]));e.subsamples&&e.subsamples.length&&(i.write(je.writeUint16(e.subsamples.length)),e.subsamples.forEach((function(e){i.write(je.writeUint16(e.BytesOfClearData)),i.write(je.writeUint32(e.BytesOfProtectedData))})))})),i.buffer}},{key:"saio",value:function(t){var i=12*t.samples.length+141;!t.isVideo&&t.isAudioEncryption&&(i=149);var n=new Uint8Array([1,0,0,0,0,0,0,1,0,0,0,0,i>>24&255,i>>16&255,i>>8&255,255&i]);return e.box(e.types.saio,n)}},{key:"saiz",value:function(t){var i=t.samples.length,n=new Uint8Array([0,0,0,0,16,i>>24&255,i>>16&255,i>>8&255,255&i]);return e.box(e.types.saiz,n)}},{key:"sbgp",value:function(){var t=new Uint8Array([114,111,108,108,0,0,0,1,0,0,1,25,0,0,0,1]);return e.box(e.types.sbgp,this.extension(0,0),t)}},{key:"extension",value:function(e,t){return new Uint8Array([e,t>>16&255,t>>8&255,255&t])}},{key:"tfhd",value:function(t){return e.box(e.types.tfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"tfdt",value:function(t,i){var n=Math.floor(i/(Ue+1)),s=Math.floor(i%(Ue+1));return t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)?e.box(e.types.tfdt,new Uint8Array([0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s])):e.box(e.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,s>>24,s>>16&255,s>>8&255,255&s]))}},{key:"trun",value:function(t,i){var n=t.length,s=12+16*n;i+=8+s;var r=new Uint8Array(s);r.set([0,0,15,1,n>>>24&255,n>>>16&255,n>>>8&255,255&n,i>>>24&255,i>>>16&255,i>>>8&255,255&i],0);for(var a=0;a<n;a++){var l=t[a],o=l.duration,d=l.size,c=l.flag,u=void 0===c?{}:c,h=l.cts,m=void 0===h?0:h;r.set([o>>>24&255,o>>>16&255,o>>>8&255,255&o,d>>>24&255,d>>>16&255,d>>>8&255,255&d,u.isLeading<<2|(null===u.dependsOn||void 0===u.dependsOn?1:u.dependsOn),u.isDependedOn<<6|u.hasRedundancy<<4|u.paddingValue<<1|(null===u.isNonSyncSample||void 0===u.isNonSyncSample?1:u.isNonSyncSample),61440&u.degradationPriority,15&u.degradationPriority,m>>>24&255,m>>>16&255,m>>>8&255,255&m],12+16*a)}return e.box(e.types.trun,r)}},{key:"moovMP4",value:function(t){return e.box.apply(e,[e.types.moov,e.mvhd(t[0].duration,t[0].timescale)].concat(T(t.map((function(t){return e.trackMP4(t)})))))}},{key:"trackMP4",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.duration,t.width,t.height),e.mdiaMP4(t))}},{key:"mdiaMP4",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minfMP4(t))}},{key:"minfMP4",value:function(t){return e.box(e.types.minf,t.type===F?e.VMHD:e.SMHD,e.DINF,e.stblMP4(t))}},{key:"stblMP4",value:function(t){var i=t.ext,n=[e.stsd(t),e.stts(i.stts),e.stsc(i.stsc),e.stsz(i.stsz),e.stco(i.stco)];return i.stss.length&&n.push(e.stss(i.stss)),i.ctts.length&&n.push(e.ctts(i.ctts)),e.box.apply(e,[e.types.stbl].concat(n))}},{key:"stts",value:function(t){var i=t.length,n=new Uint8Array(8*i),s=0;return t.forEach((function(e){var t=e.value,i=e.count;n.set([i>>24,i>>16&255,i>>8&255,255&i,t>>24,t>>16&255,t>>8&255,255&t],s),s+=8})),e.box(e.types.stts,be(new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i]),n))}},{key:"stsc",value:function(t){var i=t.length,n=new Uint8Array(12*i),s=0;return t.forEach((function(e){var t=e.firstChunk,i=e.samplesPerChunk,r=e.sampleDescIndex;n.set([t>>24,t>>16&255,t>>8&255,255&t,i>>24,i>>16&255,i>>8&255,255&i,r>>24,r>>16&255,r>>8&255,255&r],s),s+=12})),e.box(e.types.stsc,be(new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i]),n))}},{key:"stsz",value:function(t){var i=t.length,n=new Uint8Array(4*i),s=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],s),s+=4})),e.box(e.types.stsz,be(new Uint8Array([0,0,0,0,0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i]),n))}},{key:"stco",value:function(t){var i=t.length,n=new Uint8Array(4*i),s=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],s),s+=4})),e.box(e.types.stco,be(new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i]),n))}},{key:"stss",value:function(t){var i=t.length,n=new Uint8Array(4*i),s=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],s),s+=4})),e.box(e.types.stss,be(new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i]),n))}},{key:"ctts",value:function(t){var i=t.length,n=new Uint8Array(8*i),s=0;return t.forEach((function(e){var t=e.value,i=e.count;n.set([i>>24,i>>16&255,i>>8&255,255&i,t>>24,t>>16&255,t>>8&255,255&t],s),s+=8})),e.box(e.types.ctts,be(new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i]),n))}},{key:"styp",value:function(){return e.box(e.types.styp,new Uint8Array([109,115,100,104,0,0,0,0,109,115,100,104,109,115,105,120]))}},{key:"sidx",value:function(t){var i=t.timescale,n=t.samples[0].duration,s=n*t.samples.length,r=t.samples[0].sampleOffset*n,a=8;t.samples.forEach((function(e){a+=e.size}));var l=0;if(t.isVideo){var o,d=0;t.videoSenc&&(o=t.videoSenc),t.isVideo&&o.forEach((function(e){d+=8,e.subsamples&&e.subsamples.length&&(d+=2,d+=6*e.subsamples.length)})),t.videoSencLength=d,l=a+141+16*t.samples.length+d,t.useEME&&t.isAudioEncryption&&!t.isVideoEncryption&&(l=a+16*t.samples.length+84)}else l=a+116+12*t.samples.length,t.useEME&&t.isAudioEncryption&&(l=a+169+12*t.samples.length+8*t.audioSenc.length);var c=new Uint8Array([0,0,0,0,0,0,0,255&t.id,i>>24&255,i>>16&255,i>>8&255,255&i,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,0,0,0,1,0,l>>16&255,l>>8&255,255&l,s>>24&255,s>>16&255,s>>8&255,255&s,144,0,0,0]);return e.box(e.types.sidx,c)}},{key:"mdat",value:function(t){return e.box(e.types.mdat,t)}}]),e}();y(Je,"types",["avc1","avcC","hvc1","hvcC","dinf","dref","esds","ftyp","hdlr","mdat","mdhd","mdia","mfhd","minf","moof","moov","mp4a","mvex","mvhd","pasp","stbl","stco","stsc","stsd","stsz","stts","tfdt","tfhd","traf","trak","trex","tkhd","vmhd","smhd","ctts","stss","styp","pssh","sidx","sbgp","saiz","saio","senc","trun","encv","enca","sinf","btrt","frma","tenc","schm","schi","mehd","fiel","sdtp"].reduce((function(e,t){return e[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)],e}),Object.create(null))),y(Je,"HDLR_TYPES",{video:new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),audio:new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0])}),y(Je,"FTYPAVC1",Je.box(Je.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]))),y(Je,"FTYPHEV1",Je.box(Je.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,104,101,118,49]))),y(Je,"DINF",Je.box(Je.types.dinf,Je.box(Je.types.dref,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1])))),y(Je,"VMHD",Je.box(Je.types.vmhd,new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]))),y(Je,"SMHD",Je.box(Je.types.smhd,new Uint8Array([0,0,0,0,0,0,0,0]))),y(Je,"StblTable",new Uint8Array([0,0,0,0,0,0,0,0])),y(Je,"STTS",Je.box(Je.types.stts,Je.StblTable)),y(Je,"STSC",Je.box(Je.types.stsc,Je.StblTable)),y(Je,"STSZ",Je.box(Je.types.stsz,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]))),y(Je,"STCO",Je.box(Je.types.stco,Je.StblTable));var Pe=function(){function e(t,i){m(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),e.disabled=i}return b(e,[{key:"debug",value:function(){var t;if(!e.disabled){for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];(t=console).debug.apply(t,[this._prefix].concat(n))}}},{key:"log",value:function(){var t;if(!e.disabled){for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];(t=console).log.apply(t,[this._prefix].concat(n))}}},{key:"warn",value:function(){var t;if(!e.disabled){for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];(t=console).warn.apply(t,[this._prefix].concat(n))}}},{key:"error",value:function(){var t;if(!e.disabled){for(var i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];(t=console).error.apply(t,[this._prefix].concat(n))}}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();y(Pe,"disabled",!0);for(var De=function(){function e(t,i,n){m(this,e),this.videoTrack=t,this.audioTrack=i;var s=/Chrome\/([^.]+)/.exec(navigator.userAgent);this.forceFirstIDR=s&&Number(s[1])<50,this.log=new Pe("FMP4Remuxer",!n||!n.openLog||!n.openLog)}return b(e,[{key:"remux",value:function(){var e,t,i,n,s,r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=this.videoTrack,o=this.audioTrack,d=l.exist(),c=o.exist(),u=[];return r&&(a&&a.initMerge?(d&&u.push(this.videoTrack),c&&u.push(this.audioTrack),i=Je.initSegment(u)):(d&&(e=Je.initSegment([this.videoTrack])),c&&(t=Je.initSegment([this.audioTrack])))),d&&l.hasSample()&&(n=this._remuxVideo()),c&&o.hasSample()&&(s=this._remuxAudio()),l.samples=[],o.samples=[],{initSegment:i,videoInitSegment:e,audioInitSegment:t,videoSegment:n,audioSegment:s}}},{key:"_remuxVideo",value:function(){var e=this.videoTrack;this.forceFirstIDR&&(e.samples[0].flag={dependsOn:2,isNonSyncSample:0});var t=e.samples,i=0;t.forEach((function(e){i+=e.units.reduce((function(e,t){return e+t.byteLength}),0),i+=4*e.units.length}));for(var n,s=new Uint8Array(i),r=new DataView(s.buffer),a=function(e,i){i=t[l];var a=0;i.units.forEach((function(t){r.setUint32(e,t.byteLength),e+=4,s.set(t,e),e+=t.byteLength,a+=4+t.byteLength})),i.size=a,d=e,n=i},l=0,o=t.length,d=0;l<o;l++)a(d,n);var c=Je.mdat(s);return be(Je.moof([e]),c)}},{key:"_remuxAudio",value:function(){var e=this.audioTrack,t=new Uint8Array(e.samples.reduce((function(e,t){return e+t.size}),0));e.samples.reduce((function(e,i){return t.set(i.data,e),e+i.size}),0);var i=Je.mdat(t);return be(Je.moof([e]),i)}},{key:"reset",value:function(){this.videoTrack.reset(),this.audioTrack.reset()}}]),e}(),Ee=function(){function e(){m(this,e)}return b(e,[{key:"mixIn",value:function(e){return Object.assign(this,e)}},{key:"clone",value:function(){var e=new this.constructor;return Object.assign(e,this),e}}],[{key:"create",value:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return v(this,t)}}]),e}(),_e=function(e){Z(i,e);var t=S(i);function i(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4*n.length;m(this,i),e=t.call(this);var r=n;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){for(var a=r.byteLength,l=[],o=0;o<a;o+=1)l[o>>>2]|=r[o]<<24-o%4*8;e.words=l,e.sigBytes=a}else e.words=n,e.sigBytes=s;return e}return b(i,[{key:"toString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Be;return e.stringify(this)}},{key:"concat",value:function(e){var t=this.words,i=e.words,n=this.sigBytes,s=e.sigBytes;if(this.clamp(),n%4)for(var r=0;r<s;r+=1){var a=i[r>>>2]>>>24-r%4*8&255;t[n+r>>>2]|=a<<24-(n+r)%4*8}else for(var l=0;l<s;l+=4)t[n+l>>>2]=i[l>>>2];return this.sigBytes+=s,this}},{key:"clamp",value:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=Math.ceil(t/4)}},{key:"clone",value:function(){var e=k(f(i.prototype),"clone",this).call(this);return e.words=this.words.slice(0),e}}],[{key:"random",value:function(e){for(var t,n=[],s=function(e){var t=e,i=987654321,n=4294967295;return function(){var e=((i=36969*(65535&i)+(i>>16)&n)<<16)+(t=18e3*(65535&t)+(t>>16)&n)&n;return e/=4294967296,(e+=.5)*(Math.random()>.5?1:-1)}},r=0;r<e;r+=4){var a=s(4294967296*(t||Math.random()));t=987654071*a(),n.push(4294967296*a()|0)}return new i(n,e)}}]),i}(Ee),Be={stringify:function(e){for(var t=e.words,i=e.sigBytes,n=[],s=0;s<i;s+=1){var r=t[s>>>2]>>>24-s%4*8&255;n.push((r>>>4).toString(16)),n.push((15&r).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,i=[],n=0;n<t;n+=2)i[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new _e(i,t/2)}},Qe=function(e){for(var t=e.length,i=[],n=0;n<t;n+=1)i[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new _e(i,t)},Oe=function(e){return Qe(unescape(encodeURIComponent(e)))},Ae=function(e){Z(i,e);var t=S(i);function i(){var e;return m(this,i),(e=t.call(this))._minBufferSize=0,e}return b(i,[{key:"reset",value:function(){this._data=new _e,this._nDataBytes=0}},{key:"_append",value:function(e){var t=e;"string"==typeof t&&(t=Oe(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}},{key:"_process",value:function(e){var t,i=this._data,n=this.blockSize,s=i.words,r=i.sigBytes,a=r/(4*n),l=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*n,o=Math.min(4*l,r);if(l){for(var d=0;d<l;d+=n)this._doProcessBlock(s,d);t=s.splice(0,l),i.sigBytes-=o}return new _e(t,o)}},{key:"clone",value:function(){var e=k(f(i.prototype),"clone",this).call(this);return e._data=this._data.clone(),e}}]),i}(Ee),qe=function(e){Z(i,e);var t=S(i);function i(e){var n;return m(this,i),(n=t.call(this)).blockSize=16,n.cfg=Object.assign(new Ee,e),n.reset(),n}return b(i,[{key:"reset",value:function(){k(f(i.prototype),"reset",this).call(this),this._doReset()}},{key:"update",value:function(e){return this._append(e),this._process(),this}},{key:"finalize",value:function(e){return e&&this._append(e),this._doFinalize()}}],[{key:"_createHelper",value:function(e){return function(t,i){return new e(i).finalize(t)}}},{key:"_createHmacHelper",value:function(e){return function(t,i){return new $e(e,i).finalize(t)}}}]),i}(Ae),$e=function(e){Z(i,e);var t=S(i);function i(e,n){var s;m(this,i),s=t.call(this);var r=new e;s._hasher=r;var a=n;"string"==typeof a&&(a=Oe(a));var l=r.blockSize,o=4*l;a.sigBytes>o&&(a=r.finalize(n)),a.clamp();var d=a.clone();s._oKey=d;var c=a.clone();s._iKey=c;for(var u=d.words,h=c.words,p=0;p<l;p+=1)u[p]^=1549556828,h[p]^=909522486;return d.sigBytes=o,c.sigBytes=o,s.reset(),s}return b(i,[{key:"reset",value:function(){var e=this._hasher;e.reset(),e.update(this._iKey)}},{key:"update",value:function(e){return this._hasher.update(e),this}},{key:"finalize",value:function(e){var t=this._hasher,i=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(i))}}]),i}(Ee),et={stringify:function(e){var t=e.words,i=e.sigBytes,n=this._map;e.clamp();for(var s=[],r=0;r<i;r+=3)for(var a=(t[r>>>2]>>>24-r%4*8&255)<<16|(t[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|t[r+2>>>2]>>>24-(r+2)%4*8&255,l=0;l<4&&r+.75*l<i;l+=1)s.push(n.charAt(a>>>6*(3-l)&63));var o=n.charAt(64);if(o)for(;s.length%4;)s.push(o);return s.join("")},parse:function(e){var t=e.length,i=this._map,n=this._reverseMap;if(!n){this._reverseMap=[],n=this._reverseMap;for(var s=0;s<i.length;s+=1)n[i.charCodeAt(s)]=s}var r=i.charAt(64);if(r){var a=e.indexOf(r);-1!==a&&(t=a)}return function(e,t,i){for(var n=[],s=0,r=0;r<t;r+=1)if(r%4){var a=i[e.charCodeAt(r-1)]<<r%4*2|i[e.charCodeAt(r)]>>>6-r%4*2;n[s>>>2]|=a<<24-s%4*8,s+=1}return _e.create(n,s)}(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},tt=[],it=0;it<64;it+=1)tt[it]=4294967296*Math.abs(Math.sin(it+1))|0;var nt=function(e,t,i,n,s,r,a){var l=e+(t&i|~t&n)+s+a;return(l<<r|l>>>32-r)+t},st=function(e,t,i,n,s,r,a){var l=e+(t&n|i&~n)+s+a;return(l<<r|l>>>32-r)+t},rt=function(e,t,i,n,s,r,a){var l=e+(t^i^n)+s+a;return(l<<r|l>>>32-r)+t},at=function(e,t,i,n,s,r,a){var l=e+(i^(t|~n))+s+a;return(l<<r|l>>>32-r)+t},lt=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i,[{key:"_doReset",value:function(){this._hash=new _e([1732584193,4023233417,2562383102,271733878])}},{key:"_doProcessBlock",value:function(e,t){for(var i=e,n=0;n<16;n+=1){var s=t+n,r=e[s];i[s]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var a=this._hash.words,l=i[t+0],o=i[t+1],d=i[t+2],c=i[t+3],u=i[t+4],h=i[t+5],m=i[t+6],p=i[t+7],b=i[t+8],y=i[t+9],Z=i[t+10],f=i[t+11],X=i[t+12],L=i[t+13],v=i[t+14],W=i[t+15],G=a[0],x=a[1],g=a[2],S=a[3];G=nt(G,x,g,S,l,7,tt[0]),S=nt(S,G,x,g,o,12,tt[1]),g=nt(g,S,G,x,d,17,tt[2]),x=nt(x,g,S,G,c,22,tt[3]),G=nt(G,x,g,S,u,7,tt[4]),S=nt(S,G,x,g,h,12,tt[5]),g=nt(g,S,G,x,m,17,tt[6]),x=nt(x,g,S,G,p,22,tt[7]),G=nt(G,x,g,S,b,7,tt[8]),S=nt(S,G,x,g,y,12,tt[9]),g=nt(g,S,G,x,Z,17,tt[10]),x=nt(x,g,S,G,f,22,tt[11]),G=nt(G,x,g,S,X,7,tt[12]),S=nt(S,G,x,g,L,12,tt[13]),g=nt(g,S,G,x,v,17,tt[14]),x=nt(x,g,S,G,W,22,tt[15]),G=st(G,x,g,S,o,5,tt[16]),S=st(S,G,x,g,m,9,tt[17]),g=st(g,S,G,x,f,14,tt[18]),x=st(x,g,S,G,l,20,tt[19]),G=st(G,x,g,S,h,5,tt[20]),S=st(S,G,x,g,Z,9,tt[21]),g=st(g,S,G,x,W,14,tt[22]),x=st(x,g,S,G,u,20,tt[23]),G=st(G,x,g,S,y,5,tt[24]),S=st(S,G,x,g,v,9,tt[25]),g=st(g,S,G,x,c,14,tt[26]),x=st(x,g,S,G,b,20,tt[27]),G=st(G,x,g,S,L,5,tt[28]),S=st(S,G,x,g,d,9,tt[29]),g=st(g,S,G,x,p,14,tt[30]),x=st(x,g,S,G,X,20,tt[31]),G=rt(G,x,g,S,h,4,tt[32]),S=rt(S,G,x,g,b,11,tt[33]),g=rt(g,S,G,x,f,16,tt[34]),x=rt(x,g,S,G,v,23,tt[35]),G=rt(G,x,g,S,o,4,tt[36]),S=rt(S,G,x,g,u,11,tt[37]),g=rt(g,S,G,x,p,16,tt[38]),x=rt(x,g,S,G,Z,23,tt[39]),G=rt(G,x,g,S,L,4,tt[40]),S=rt(S,G,x,g,l,11,tt[41]),g=rt(g,S,G,x,c,16,tt[42]),x=rt(x,g,S,G,m,23,tt[43]),G=rt(G,x,g,S,y,4,tt[44]),S=rt(S,G,x,g,X,11,tt[45]),g=rt(g,S,G,x,W,16,tt[46]),x=rt(x,g,S,G,d,23,tt[47]),G=at(G,x,g,S,l,6,tt[48]),S=at(S,G,x,g,p,10,tt[49]),g=at(g,S,G,x,v,15,tt[50]),x=at(x,g,S,G,h,21,tt[51]),G=at(G,x,g,S,X,6,tt[52]),S=at(S,G,x,g,c,10,tt[53]),g=at(g,S,G,x,Z,15,tt[54]),x=at(x,g,S,G,o,21,tt[55]),G=at(G,x,g,S,b,6,tt[56]),S=at(S,G,x,g,W,10,tt[57]),g=at(g,S,G,x,m,15,tt[58]),x=at(x,g,S,G,L,21,tt[59]),G=at(G,x,g,S,u,6,tt[60]),S=at(S,G,x,g,f,10,tt[61]),g=at(g,S,G,x,d,15,tt[62]),x=at(x,g,S,G,y,21,tt[63]),a[0]=a[0]+G|0,a[1]=a[1]+x|0,a[2]=a[2]+g|0,a[3]=a[3]+S|0}},{key:"_doFinalize",value:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32;var s=Math.floor(i/4294967296),r=i;t[15+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var a=this._hash,l=a.words,o=0;o<4;o+=1){var d=l[o];l[o]=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8)}return a}},{key:"clone",value:function(){var e=k(f(i.prototype),"clone",this).call(this);return e._hash=this._hash.clone(),e}}]),i}(qe);qe._createHelper(lt),qe._createHmacHelper(lt);var ot=function(e){Z(i,e);var t=S(i);function i(e){var n;return m(this,i),(n=t.call(this)).cfg=Object.assign(new Ee,{keySize:4,hasher:lt,iterations:1},e),n}return b(i,[{key:"compute",value:function(e,t){for(var i,n=this.cfg,s=n.hasher.create(),r=_e.create(),a=r.words,l=n.keySize,o=n.iterations;a.length<l;){i&&s.update(i),i=s.update(e).finalize(t),s.reset();for(var d=1;d<o;d+=1)i=s.finalize(i),s.reset();r.concat(i)}return r.sigBytes=4*l,r}}]),i}(Ee),dt=function(e){Z(i,e);var t=S(i);function i(e,n,s){var r;return m(this,i),(r=t.call(this)).cfg=Object.assign(new Ee,s),r._xformMode=e,r._key=n,r.reset(),r}return b(i,[{key:"reset",value:function(){k(f(i.prototype),"reset",this).call(this),this._doReset()}},{key:"process",value:function(e){return this._append(e),this._process()}},{key:"finalize",value:function(e){return e&&this._append(e),this._doFinalize()}}],[{key:"createEncryptor",value:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}},{key:"createDecryptor",value:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}},{key:"_createHelper",value:function(e){var t=function(e){return"string"==typeof e?Xt:Zt};return{encrypt:function(i,n,s){return t(n).encrypt(e,i,n,s)},decrypt:function(i,n,s){return t(n).decrypt(e,i,n,s)}}}}]),i}(Ae);dt._ENC_XFORM_MODE=1,dt._DEC_XFORM_MODE=2,dt.keySize=4,dt.ivSize=4;var ct=function(e){Z(i,e);var t=S(i);function i(e,n){var s;return m(this,i),(s=t.call(this))._cipher=e,s._iv=n,s}return b(i,null,[{key:"createEncryptor",value:function(e,t){return this.Encryptor.create(e,t)}},{key:"createDecryptor",value:function(e,t){return this.Decryptor.create(e,t)}}]),i}(Ee);function ut(e,t,i){var n,s=e,r=this._iv;r?(n=r,this._iv=void 0):n=this._prevBlock;for(var a=0;a<i;a+=1)s[t+a]^=n[a]}var ht=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i)}(ct);ht.Encryptor=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i,[{key:"processBlock",value:function(e,t){var i=this._cipher,n=i.blockSize;ut.call(this,e,t,n),i.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}}]),i}(ht),ht.Decryptor=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i,[{key:"processBlock",value:function(e,t){var i=this._cipher,n=i.blockSize,s=e.slice(t,t+n);i.decryptBlock(e,t),ut.call(this,e,t,n),this._prevBlock=s}}]),i}(ht);var mt={pad:function(e,t){for(var i=4*t,n=i-e.sigBytes%i,s=n<<24|n<<16|n<<8|n,r=[],a=0;a<n;a+=4)r.push(s);var l=_e.create(r,n);e.concat(l)},unpad:function(e){var t=e,i=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=i}},pt=function(e){Z(i,e);var t=S(i);function i(e,n,s){var r;return m(this,i),(r=t.call(this,e,n,Object.assign({mode:ht,padding:mt},s))).blockSize=4,r}return b(i,[{key:"reset",value:function(){var e;k(f(i.prototype),"reset",this).call(this);var t=this.cfg,n=t.iv,s=t.mode;this._xformMode===this.constructor._ENC_XFORM_MODE?e=s.createEncryptor:(e=s.createDecryptor,this._minBufferSize=1),this._mode=e.call(s,this,n&&n.words),this._mode.__creator=e}},{key:"_doProcessBlock",value:function(e,t){this._mode.processBlock(e,t)}},{key:"_doFinalize",value:function(){var e,t=this.cfg.padding;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}}]),i}(dt),bt=function(e){Z(i,e);var t=S(i);function i(e){var n;return m(this,i),(n=t.call(this)).mixIn(e),n}return b(i,[{key:"toString",value:function(e){return(e||this.formatter).stringify(this)}}]),i}(Ee),yt={stringify:function(e){var t=e.ciphertext,i=e.salt;return(i?_e.create([1398893684,1701076831]).concat(i).concat(t):t).toString(et)},parse:function(e){var t,i=et.parse(e),n=i.words;return 1398893684===n[0]&&1701076831===n[1]&&(t=_e.create(n.slice(2,4)),n.splice(0,4),i.sigBytes-=16),bt.create({ciphertext:i,salt:t})}},Zt=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i,null,[{key:"encrypt",value:function(e,t,i,n){var s=Object.assign(new Ee,this.cfg,n),r=e.createEncryptor(i,s),a=r.finalize(t),l=r.cfg;return bt.create({ciphertext:a,key:i,iv:l.iv,algorithm:e,mode:l.mode,padding:l.padding,blockSize:r.blockSize,formatter:s.format})}},{key:"decrypt",value:function(e,t,i,n){var s=t,r=Object.assign(new Ee,this.cfg,n);return s=this._parse(s,r.format),e.createDecryptor(i,r).finalize(s.ciphertext)}},{key:"_parse",value:function(e,t){return"string"==typeof e?t.parse(e,this):e}}]),i}(Ee);Zt.cfg=Object.assign(new Ee,{format:yt});var ft={execute:function(e,t,i,n){var s=n;s||(s=_e.random(8));var r=ot.create({keySize:t+i}).compute(e,s),a=_e.create(r.words.slice(t),4*i);return r.sigBytes=4*t,bt.create({key:r,iv:a,salt:s})}},Xt=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i,null,[{key:"encrypt",value:function(e,t,i,n){var s=Object.assign(new Ee,this.cfg,n),r=s.kdf.execute(i,e.keySize,e.ivSize);s.iv=r.iv;var a=Zt.encrypt.call(this,e,t,r.key,s);return a.mixIn(r),a}},{key:"decrypt",value:function(e,t,i,n){var s=t,r=Object.assign(new Ee,this.cfg,n);s=this._parse(s,r.format);var a=r.kdf.execute(i,e.keySize,e.ivSize,s.salt);return r.iv=a.iv,Zt.decrypt.call(this,e,s,a.key,r)}}]),i}(Zt);Xt.cfg=Object.assign(Zt.cfg,{kdf:ft});for(var Lt=[],vt=[],Wt=[],Gt=[],xt=[],gt=[],St=[],Vt=[],kt=[],Yt=[],Tt=[],wt=0;wt<256;wt+=1)Tt[wt]=wt<128?wt<<1:wt<<1^283;for(var Rt=0,Mt=0,Kt=0;Kt<256;Kt+=1){var It=Mt^Mt<<1^Mt<<2^Mt<<3^Mt<<4;It=It>>>8^255&It^99,Lt[Rt]=It,vt[It]=Rt;var Ht=Tt[Rt],Ct=Tt[Ht],Nt=Tt[Ct],Ft=257*Tt[It]^16843008*It;Wt[Rt]=Ft<<24|Ft>>>8,Gt[Rt]=Ft<<16|Ft>>>16,xt[Rt]=Ft<<8|Ft>>>24,gt[Rt]=Ft,Ft=16843009*Nt^65537*Ct^257*Ht^16843008*Rt,St[It]=Ft<<24|Ft>>>8,Vt[It]=Ft<<16|Ft>>>16,kt[It]=Ft<<8|Ft>>>24,Yt[It]=Ft,Rt?(Rt=Ht^Tt[Tt[Tt[Nt^Ht]]],Mt^=Tt[Tt[Mt]]):Rt=Mt=1}var zt=[0,1,2,4,8,16,32,64,128,27,54],jt=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i,[{key:"_doReset",value:function(){var e;if(!this._nRounds||this._keyPriorReset!==this._key){this._keyPriorReset=this._key;var t=this._keyPriorReset,i=t.words,n=t.sigBytes/4;this._nRounds=n+6;var s=4*(this._nRounds+1);this._keySchedule=[];for(var r=this._keySchedule,a=0;a<s;a+=1)a<n?r[a]=i[a]:(e=r[a-1],a%n?n>6&&a%n==4&&(e=Lt[e>>>24]<<24|Lt[e>>>16&255]<<16|Lt[e>>>8&255]<<8|Lt[255&e]):(e=Lt[(e=e<<8|e>>>24)>>>24]<<24|Lt[e>>>16&255]<<16|Lt[e>>>8&255]<<8|Lt[255&e],e^=zt[a/n|0]<<24),r[a]=r[a-n]^e);this._invKeySchedule=[];for(var l=this._invKeySchedule,o=0;o<s;o+=1){var d=s-o;e=o%4?r[d]:r[d-4],l[o]=o<4||d<=4?e:St[Lt[e>>>24]]^Vt[Lt[e>>>16&255]]^kt[Lt[e>>>8&255]]^Yt[Lt[255&e]]}}}},{key:"encryptBlock",value:function(e,t){this._doCryptBlock(e,t,this._keySchedule,Wt,Gt,xt,gt,Lt)}},{key:"decryptBlock",value:function(e,t){var i=e,n=i[t+1];i[t+1]=i[t+3],i[t+3]=n,this._doCryptBlock(i,t,this._invKeySchedule,St,Vt,kt,Yt,vt),n=i[t+1],i[t+1]=i[t+3],i[t+3]=n}},{key:"_doCryptBlock",value:function(e,t,i,n,s,r,a,l){for(var o=e,d=this._nRounds,c=o[t]^i[0],u=o[t+1]^i[1],h=o[t+2]^i[2],m=o[t+3]^i[3],p=4,b=1;b<d;b+=1){var y=n[c>>>24]^s[u>>>16&255]^r[h>>>8&255]^a[255&m]^i[p];p+=1;var Z=n[u>>>24]^s[h>>>16&255]^r[m>>>8&255]^a[255&c]^i[p];p+=1;var f=n[h>>>24]^s[m>>>16&255]^r[c>>>8&255]^a[255&u]^i[p];p+=1;var X=n[m>>>24]^s[c>>>16&255]^r[u>>>8&255]^a[255&h]^i[p];p+=1,c=y,u=Z,h=f,m=X}var L=(l[c>>>24]<<24|l[u>>>16&255]<<16|l[h>>>8&255]<<8|l[255&m])^i[p];p+=1;var v=(l[u>>>24]<<24|l[h>>>16&255]<<16|l[m>>>8&255]<<8|l[255&c])^i[p];p+=1;var W=(l[h>>>24]<<24|l[m>>>16&255]<<16|l[c>>>8&255]<<8|l[255&u])^i[p];p+=1;var G=(l[m>>>24]<<24|l[c>>>16&255]<<16|l[u>>>8&255]<<8|l[255&h])^i[p];p+=1,o[t]=L,o[t+1]=v,o[t+2]=W,o[t+3]=G}}]),i}(pt);jt.keySize=8;var Ut=pt._createHelper(jt),Jt=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i)}(ct);Jt.Encryptor=function(e){Z(i,e);var t=S(i);function i(){return m(this,i),t.apply(this,arguments)}return b(i,[{key:"processBlock",value:function(e,t){var i=e,n=this._cipher,s=n.blockSize,r=this._iv,a=this._counter;r&&(this._counter=r.slice(0),a=this._counter,this._iv=void 0);var l=a.slice(0);n.encryptBlock(l,0),a[s-1]=a[s-1]+1|0;for(var o=0;o<s;o+=1)i[t+o]^=l[o]}}]),i}(Jt),Jt.Decryptor=Jt.Encryptor;var Pt={pad:function(){},unpad:function(){}},Dt={decryptWordArray:function(e,t,i){var n=Be.parse(t),s=Be.parse(ve(i)),r=_e.create(new Uint8Array(e)),a=Ut.decrypt(bt.create({ciphertext:r}),n,{iv:s,mode:Jt,padding:Pt});return Dt.wordArrayToUint8Array(a)},wordArrayToUint8Array:function(e){for(var t=e.sigBytes,i=e.words,n=new Uint8Array(t),s=0,r=0;s!==t;){var a=i[r++];if(n[s++]=(4278190080&a)>>>24,s===t)break;if(n[s++]=(16711680&a)>>>16,s===t)break;if(n[s++]=(65280&a)>>>8,s===t)break;n[s++]=255&a}return n},decoderAESCTRData:function(e,t,i){if(e.videoSenc){var n=e.kidValue,s=e.videoSenc;e.samples.forEach((function(t,r){var a=s[r],l=t.data,o=[],d=[],c=a.InitializationVector;if(a.subsamples&&a.subsamples.length)a.subsamples.forEach((function(e){var t=e.BytesOfClearData+e.BytesOfProtectedData,i=l.slice(0,t);o.push(i.slice(0,e.BytesOfClearData)),d.push(i.slice(e.BytesOfClearData)),l=l.slice(t)}));else{var u=t.size;o.push(l.slice(0,0)),d.push(l.slice(0,u)),l=l.slice(u)}var h=new je;h.write.apply(h,d);var m=i?i(h.buffer,n,c):Dt.decryptWordArray(h.buffer,n,c),p=new je;o.forEach((function(e,t){var i=d[t].length,n=m.slice(0,i);p.write(e),p.write(n),m=m.slice(i)})),e.samples[r].data=p.buffer}))}if(t.audioSenc){var r=t.kidValue,a=t.audioSenc;t.samples.forEach((function(e,n){var s=a[n],l=i?i(e.data,r,s.InitializationVector):Dt.decryptWordArray(e.data,r,s.InitializationVector);t.samples[n].data=l}))}}},Et=function(){function e(t,i,n,s){var r=this;m(this,e),y(this,"_videoSamples",[]),y(this,"_audioSamples",[]),y(this,"_lastRemainBuffer",[]),y(this,"_lastRemainBufferStartPos",0),this.videoTrack=new $,this.audioTrack=new ee,this.metadataTrack=n||new ae,this.log=new Pe("MP4Demuxer",!s||!s.openLog||!s.openLog),t&&t.forEach((function(e){var t;(t=r._videoSamples).push.apply(t,T(e.frames))})),i&&i.forEach((function(e){var t;(t=r._audioSamples).push.apply(t,T(e.frames))}))}return b(e,[{key:"parseSamples",value:function(e){if(!e)throw new Error("moov is required");if(this.videoTrack.codec||this.audioTrack.codec||(Me.moovToTrack(e,this.videoTrack,this.audioTrack),this.videoSenc=this.videoTrack.videoSenc,this.audioSenc=this.audioTrack.audioSenc),!this._audioSamples.length&&!this._videoSamples.length){var t=Me.moovToSamples(e);if(!t)throw new Error("cannot parse samples from moov box");this._videoSamples=t.videoSamples||[],this._audioSamples=t.audioSamples||[]}}},{key:"demux",value:function(e,t,i,n,s){this.parseSamples(s);var r,a,l,o=this.videoTrack,d=this.audioTrack;if(o.samples=[],d.samples=[],i){for(var c,u=0,h=i[0],m=i[1];h<=m;h++){if(!(r=this._videoSamples[h]))throw new Error("cannot found video frame #".concat(h));l=r.offset-t,a=e.subarray(l,l+r.size),(c=new te(r.pts||r.dts,r.dts)).duration=r.duration,c.gopId=r.gopId,r.keyframe&&c.setToKeyframe();for(var p=0,b=a.length-1;p<b;)u=fe(a,p),p+=4,c.units.push(a.subarray(p,p+u)),p+=u;o.samples.push(c)}o.baseMediaDecodeTime=o.samples[0].dts}if(n){for(var y=n[0],Z=n[1];y<=Z;y++){if(!(r=this._audioSamples[y]))throw new Error("cannot found video frame #".concat(y));l=r.offset-t,a=e.subarray(l,l+r.size),d.samples.push(new ie(r.dts,a,r.duration))}d.baseMediaDecodeTime=d.samples[0].dts}return{videoTrack:o,audioTrack:d,metadataTrack:this.metadataTrack}}},{key:"demuxPart",value:function(e,t,i,n,s,r,a,l){if(this.parseSamples(s),this.videoTrack.useEME=r,this.audioTrack.useEME=r,this._lastRemainBuffer&&this._lastRemainBuffer.byteLength>0&&t>this._lastRemainBufferStartPos&&t<=this._lastRemainBufferStartPos+this._lastRemainBuffer.byteLength)for(var o=0;o<20;)try{var d=this._lastRemainBuffer.subarray(0,t-this._lastRemainBufferStartPos),c=new Uint8Array(e.byteLength+d.byteLength);c.set(d,0),c.set(new Uint8Array(e),d.byteLength),e=c,t-=d.byteLength,this._lastRemainBuffer=null,this._lastRemainBufferStartPos=0;break}catch(V){if(!(o<20))throw new Error("new Uint8Array error:,"+V.errorMessage);o++}var u,h,m,p=this.videoTrack,b=this.audioTrack;p.samples=[],b.samples=[],p.videoSenc=null,b.audioSenc=null;var y=0,Z=0;if(this._videoSamples.length>0&&i.length>0){for(var f,X=e.byteLength+t,L=i[0];L<=i[1];L++){if(!(u=this._videoSamples[L]))throw new Error("cannot found video frame #".concat(L));u.offset>=t&&u.offset+u.size<=X&&(y=(m=u.offset-t)+u.size,h=e.subarray(m,y),(f=new te(u.pts||u.dts,u.dts)).duration=u.duration,f.gopId=u.gopId,f.sampleOffset=u.index,u.keyframe&&f.setToKeyframe(),f.data=h,f.size=u.size,p.samples.push(f))}p.samples.length>0&&(p.gopId=p.samples[0].gopId,p.baseMediaDecodeTime=p.samples[0].dts,p.startPts=p.samples[0].pts/p.timescale,p.endPts=p.samples[p.samples.length-1].pts/p.timescale,this.videoSenc&&(p.videoSenc=this.videoSenc.slice(p.samples[0].sampleOffset,p.samples[0].sampleOffset+p.samples.length),p.kidValue=a))}if(this._audioSamples.length>0&&n.length>0){for(var v=n[0];v<=n[1];v++){if(!(u=this._audioSamples[v]))throw new Error("cannot found video frame #".concat(v));u.offset>=t&&u.offset+u.size<=e.byteLength+t&&(Z=(m=u.offset-t)+u.size,h=e.subarray(m,Z),b.samples.push(new ie(u.dts,h,u.duration,u.index)))}b.samples.length>0&&(b.gopId=b.samples[0].gopId||p.gopId,b.baseMediaDecodeTime=b.samples[0].dts,b.startPts=b.samples[0].pts/b.timescale,b.endPts=b.samples[b.samples.length-1].pts/b.timescale,this.audioSenc&&(b.audioSenc=this.audioSenc.slice(b.samples[0].sampleOffset,b.samples[0].sampleOffset+b.samples.length),b.kidValue=a))}this.decoderData(p,b,l);for(var W=0,G=0;G<p.samples.length;G++)for(var x=0,g=p.samples[G].data,S=g.length-1;x<S;)W=fe(g,x),x+=4,p.samples[G].units.push(g.subarray(x,x+W)),x+=W;return this._lastRemainBuffer=e.subarray(Math.max(y,Z)),this._lastRemainBuffer.byteLength>0?this._lastRemainBufferStartPos=t+e.byteLength-this._lastRemainBuffer.byteLength:this._lastRemainBufferStartPos=0,{videoTrack:p,audioTrack:b,metadataTrack:this.metadataTrack}}},{key:"reset",value:function(){this._videoSamples=[],this._audioSamples=[],this._lastRemainBuffer=null,this._lastRemainBufferStartPos=0,this.videoTrack.reset(),this.audioTrack.reset(),this.metadataTrack.reset()}},{key:"decoderData",value:function(e,t,i){e.useEME||t.useEME||Dt.decoderAESCTRData(e,t,i)}}],[{key:"probe",value:function(e){return Me.probe(e)}}]),e}(),_t="3.0.8",Bt=-499971,Qt=-499986,Ot=-499987,At=-499990,qt=-499791,$t={network:1003,format:1005,runtime:1002,other:9999,demux:1006,remux:1007},ei=b((function e(t,i,n){m(this,e);var s=0,r=0;return n&&n.range&&n.range.length>1&&(s=n.range[0],r=n.range[1]),{errorCode:i,errorType:t,errorTypeCode:$t[t],errorMessage:(null==n?void 0:n.httpText)||(null==n?void 0:n.message),url:null==n?void 0:n.url,httpCode:i,version:_t,rangeStart:s,rangeEnd:r,ext:n,mediaError:{code:i,message:(null==n?void 0:n.httpText)||(null==n?void 0:n.message)}}})),ti=b((function e(t,i,n){return m(this,e),{errorCode:i,errorType:t,errorTypeCode:$t[t],version:_t,errorMessage:n.msg,ext:n,mediaError:{code:i,message:n.msg}}})),ii={};function ni(){var e,t,i=new Promise((function(i,n){e=i,t=n}));return i.used=!1,i.resolve=function(){return i.used=!0,e.apply(void 0,arguments)},i.reject=function(){return i.used=!0,t.apply(void 0,arguments)},i}function si(){try{return parseInt(performance.now(),10)}catch(e){return(new Date).getTime()}}ii.nowTime=function(){try{return parseInt(performance.now(),10)}catch(e){return(new Date).getTime()}},ii.concatData=function(e,t){var i=[],n=0;e&&i.push(e),t&&i.push(t),i.every((function(e){return e&&(n+=e.byteLength),!0}));var s=new Uint8Array(n),r=0;return i.every((function(e){return e&&(s.set(e,r),r+=e.byteLength),!0})),s};var ri,ai=function(){function e(){m(this,e)}return b(e,null,[{key:"start",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6||1===e.length&&e.start(0)<0?0:e.start(0):0}},{key:"end",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6?0:e.end(e.length-1):0}},{key:"get",value:function(e){if(e)try{return e.buffered}catch(t){}}},{key:"buffers",value:function(e,t){if(!e||!e.length)return[];for(var i=[],n=0,s=e.length;n<s;n++){var r=i.length;if(r&&t){var a=i[r-1],l=a[1];if(e.start(n)-l<=t){var o=e.end(n);o>l&&(a[1]=o)}else i.push([e.start(n),e.end(n)])}else i.push([e.start(n),e.end(n)])}return i}},{key:"totalLength",value:function(e){return e&&e.length?e.reduce((function(e,t){return e+(t[1]-t[0])}),0):0}},{key:"info",value:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!t||!t.length)return{start:0,end:0,buffers:[]};for(var s=0,r=0,a=0,l=0,o=0,d=0,c=0,u=e.buffers(t,n),h=0,m=u.length;h<m;h++){var p=u[h];if(i+n>=p[0]&&i<=p[1])s=p[0],r=p[1],a=h;else{if(i+n<p[0]){l=p[0],o=p[1];break}i+n>p[1]&&(d=p[0],c=p[1])}}return{start:s,end:r,index:a,buffers:u,nextStart:l,nextEnd:o,prevStart:d,prevEnd:c,currentTime:i,behind:i-s,remaining:r?r-i:0,length:e.totalLength&&e.totalLength(u)}}}]),e}(),li="network",oi="network_timeout",di="network_forbidden",ci="network_notfound",ui="network_range_not_satisfiable",hi="demux",mi="remux",pi="media",bi="drm",yi="other",Zi="runtime",fi={FLV:"FLV",HLS:"HLS",MP4:"MP4",FMP4:"FMP4",MSE_ADD_SB:"MSE_ADD_SB",MSE_APPEND_BUFFER:"MSE_APPEND_BUFFER",MSE_OTHER:"MSE_OTHER",MSE_FULL:"MSE_FULL",OPTION:"OPTION",DASH:"DASH",LICENSE:"LICENSE",CUSTOM_LICENSE:"CUSTOM_LICENSE",MSE_HIJACK:"MSE_HIJACK",EME_HIJACK:"EME_HIJACK",SIDX:"SIDX",NO_CANPLAY_ERROR:"NO_CANPLAY_ERROR",BUFFERBREAK_ERROR:"BUFFERBREAK_ERROR",WAITING_TIMEOUT_ERROR:"WAITING_TIMEOUT_ERROR",MEDIA_ERR_ABORTED:"MEDIA_ERR_ABORTED",MEDIA_ERR_NETWORK:"MEDIA_ERR_NETWORK",MEDIA_ERR_DECODE:"MEDIA_ERR_DECODE",MEDIA_ERR_SRC_NOT_SUPPORTED:"MEDIA_ERR_SRC_NOT_SUPPORTED",MEDIA_ERR_CODEC_NOT_SUPPORTED:"MEDIA_ERR_CODEC_NOT_SUPPORTED",MEDIA_ERR_URL_EMPTY:"MEDIA_ERR_URL_EMPTY"},Xi=(y(ri={},"manifest",{HLS:1100,DASH:1200}),y(ri,li,2100),y(ri,oi,2101),y(ri,di,2103),y(ri,ci,2104),y(ri,ui,2116),y(ri,hi,{FLV:3100,HLS:3200,MP4:3300,FMP4:3400,SIDX:3410}),y(ri,mi,{FMP4:4100,MP4:4200}),y(ri,pi,{MEDIA_ERR_ABORTED:5101,MEDIA_ERR_NETWORK:5102,MEDIA_ERR_DECODE:5103,MEDIA_ERR_SRC_NOT_SUPPORTED:5104,MEDIA_ERR_CODEC_NOT_SUPPORTED:5105,MEDIA_ERR_URL_EMPTY:5106,MSE_ADD_SB:5200,MSE_APPEND_BUFFER:5201,MSE_OTHER:5202,MSE_FULL:5203,MSE_HIJACK:5204,EME_HIJACK:5301}),y(ri,bi,{LICENSE:7100,CUSTOM_LICENSE:7200}),y(ri,yi,8e3),y(ri,Zi,{NO_CANPLAY_ERROR:9001,BUFFERBREAK_ERROR:9002,WAITING_TIMEOUT_ERROR:9003}),ri),Li=function(e){Z(i,e);var t=S(i);function i(e,n,s,r,a){var l;return m(this,i),(l=t.call(this,a||(null==s?void 0:s.message))).errorType=e===oi?li:e,l.originError=s,l.ext=r,l.errorCode=Xi[e][n]||Xi[e],l.errorMessage=l.message,l.errorCode||(l.errorType=yi,l.errorCode=Xi[l.errorType]),l}return b(i,null,[{key:"create",value:function(e,t,n,s,r){return e instanceof i?e:(e instanceof Error&&(n=e,e=""),e||(e=yi),new i(e,t,n,s,r))}},{key:"network",value:function(e){var t;return new i(null!=e&&e.isTimeout?oi:li,null,e instanceof Error?e:null,{url:null==e?void 0:e.url,response:null==e?void 0:e.response,httpCode:null==e||null===(t=e.response)||void 0===t?void 0:t.status})}}]),i}(W(Error)),vi="undefined"!=typeof window,Wi={DEBUG:1,LOG:2,WARN:3,ERROR:4},Gi=["Boolean","Number","String","Undefined","Null","Date","Object"],xi=function(){function e(t,i){m(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),this.logCacheLevel=(null==i?void 0:i.logCacheLevel)||3,this.logMaxSize=(null==i?void 0:i.logMaxSize)||204800,this.logSize=0,this.logTextArray=[]}return b(e,[{key:"debug",value:function(){for(var t,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];this.logCache.apply(this,[Wi.DEBUG].concat(n)),e.disabled||(t=console).debug.apply(t,[this._prefix,gi()].concat(n))}},{key:"log",value:function(){for(var t,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];this.logCache.apply(this,[Wi.LOG].concat(n)),e.disabled||(t=console).log.apply(t,[this._prefix,gi()].concat(n))}},{key:"warn",value:function(){for(var t,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];this.logCache.apply(this,[Wi.WARN].concat(n)),e.disabled||(t=console).warn.apply(t,[this._prefix,gi()].concat(n))}},{key:"error",value:function(){for(var t,i=arguments.length,n=new Array(i),s=0;s<i;s++)n[s]=arguments[s];this.logCache.apply(this,[Wi.ERROR].concat(n)),e.disabled||(t=console).error.apply(t,[this._prefix,gi()].concat(n))}},{key:"logCache",value:function(e){if(!(e<this.logCacheLevel)){var t="";try{for(var i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];var r=n.map((function(e){return Vi(e)}));t=this._prefix+gi()+JSON.stringify(r)}catch(l){return}if(e>=this.logCacheLevel&&(this.logSize+=t.length,this.logTextArray.push(t)),this.logSize>this.logMaxSize){var a=this.logTextArray.shift();this.logSize-=a.length}}}},{key:"getLogCache",value:function(){var e=this.logTextArray.join("\n");return this.reset(),e}},{key:"reset",value:function(){this.logTextArray=[],this.logSize=0}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}},{key:"setLogLevel",value:function(e){this.logCacheLevel=e}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}]),e}();function gi(){return(new Date).toLocaleString()}function Si(e){if("object"!==c(e))return e;var t=Object.prototype.toString.call(e).slice(8,-1);switch(t){case"Array":case"Uint8Array":case"ArrayBuffer":return t+"["+e.length+"]";case"Object":return"{}";default:return t}}function Vi(e,t,i){i||(i=1),t||(t=2);var n={};if(!e||"object"!==c(e))return e;var s=Object.prototype.toString.call(e).slice(8,-1);if(!Gi.includes(s))return s;if(!(i>t)){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(i===t?n[r]=Si(e[r]):"object"===c(e[r])?n[r]=Vi(e[r],t,i+1):n[r]=e[r]);return n}}y(xi,"disabled",!0);var ki=function(){try{return vi?window.MediaSource:null}catch(e){}}(),Yi="appendBuffer",Ti="removeBuffer",wi="updateDuration",Ri=function(){function e(t,i){var n=this;m(this,e),y(this,"media",null),y(this,"mediaSource",null),y(this,"_openPromise",ni()),y(this,"_queue",Object.create(null)),y(this,"_sourceBuffer",Object.create(null)),y(this,"_mseFullFlag",{}),y(this,"_st",0),y(this,"_opst",0),y(this,"_logger",null),y(this,"_config",null),y(this,"_url",null),y(this,"_onSBUpdateEnd",(function(e){var t=n._queue[e];if(t){var i=t[0];if((null==i?void 0:i.opName)!==wi&&t.shift(),i){var s=si()-n._opst;n._logger.debug("UpdateEnd",i.opName,s,i.context),i.promise.resolve({name:i.opName,context:i.context,costtime:s}),n._startQueue(e)}}})),y(this,"_onSBUpdateError",(function(e,t){var i=n._queue[e];if(i){var s=i[0];s&&(n._logger.error("UpdateError",e,s.opName,s.context),s.promise.reject(new Li(pi,fi.MSE_APPEND_BUFFER,t)))}})),this._config=Object.assign(e.getDefaultConfig(),i),t&&this.bindMedia(t),this._logger=new xi("MSE"),this._config.openLog&&xi.enable()}var t,i,n;return b(e,[{key:"isOpened",get:function(){var e;return"open"===(null===(e=this.mediaSource)||void 0===e?void 0:e.readyState)}},{key:"url",get:function(){return this._url}},{key:"duration",get:function(){var e;return(null===(e=this.mediaSource)||void 0===e?void 0:e.duration)||-1}},{key:"isEnded",get:function(){return!!this.mediaSource&&"ended"===this.mediaSource.readyState}},{key:"isFull",value:function(t){return t?this._mseFullFlag[t]:this._mseFullFlag[e.VIDEO]}},{key:"updateDuration",value:function(e){var t=this,i=this.mediaSource&&this.mediaSource.duration>e;if(this.mediaSource&&this.mediaSource.duration>e){var n=0;if(Object.keys(this._sourceBuffer).forEach((function(e){try{n=Math.max(t.bufferEnd(e)||0,n)}catch(i){}})),e<n)return Promise.resolve()}return this._enqueueBlockingOp((function(){t.isEnded?t._logger.debug("[debug mse] setDuration ended"):t.mediaSource&&(t.mediaSource.duration=e,t._logger.debug("[debug mse] setDuration"))}),wi,{isReduceDuration:i})}},{key:"open",value:function(){var e=this;if(this._openPromise.used&&!this.isOpened&&this.mediaSource){var t=this.mediaSource;t.addEventListener("sourceopen",(function i(){var n=si()-e._st;e._logger.debug("MSE OPEN",n),t.removeEventListener("sourceopen",i),e._openPromise.resolve({costtime:n})})),this._openPromise=ni()}return this._openPromise}},{key:"bindMedia",value:(n=h(d().mark((function e(t){var i,n,s=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.mediaSource&&!this.media){e.next=3;break}return e.next=3,this.unbindMedia();case 3:if(t&&ki){e.next=5;break}throw new Error("Param media or MediaSource does not exist");case 5:return this.media=t,i=this.mediaSource=new ki,this._st=si(),n=function e(){var n=si()-s._st;s._logger.debug("MSE OPEN"),i.removeEventListener("sourceopen",e),URL.revokeObjectURL(t.src),s._openPromise.resolve({costtime:n})},i.addEventListener("sourceopen",n),this._url=URL.createObjectURL(i),t.src=this._url,e.abrupt("return",this._openPromise);case 13:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"unbindMedia",value:(i=h(d().mark((function e(){var t,i,n,s=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._openPromise.used||this._openPromise.resolve(),t=this.mediaSource){if(Object.keys(this._queue).forEach((function(e){var t=s._queue[e];t&&t.forEach((function(e){var t,i;return null===(t=e.promise)||void 0===t||null===(i=t.resolve)||void 0===i?void 0:i.call(t)}))})),i=!!this.media&&this.media.readyState>=1,n="open"===t.readyState,i&&n)try{t.endOfStream()}catch(r){}Object.keys(this._sourceBuffer).forEach((function(e){try{t.removeSourceBuffer(s._sourceBuffer[e])}catch(r){}}))}if(this.media){this.media.removeAttribute("src");try{this.media.load()}catch(r){}this.media=null}this.mediaSource=null,this._openPromise=ni(),this._queue=Object.create(null),this._sourceBuffer=Object.create(null);case 8:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"createSource",value:function(e,t){if(!this._sourceBuffer[e]&&this.mediaSource){var i;try{i=this._sourceBuffer[e]=this.mediaSource.addSourceBuffer(t)}catch(n){throw new Li(pi,fi.MSE_ADD_SB,n)}i.mimeType=t,i.addEventListener("updateend",this._onSBUpdateEnd.bind(this,e)),i.addEventListener("error",this._onSBUpdateError.bind(this,e))}}},{key:"changeType",value:function(e,t){var i=this,n=this._sourceBuffer[e];return this.mediaSource&&n&&n.mimeType!==t?"function"!=typeof n.changeType?Promise.reject():this._enqueueOp(e,(function(){n.changeType(t),n.mimeType=t,i._onSBUpdateEnd(e)}),"changeType",{mimeType:t}):Promise.resolve()}},{key:"createOrChangeSource",value:function(e,t){return this.createSource(e,t),this.changeType(e,t)}},{key:"append",value:function(e,t,i){var n=this;return t&&t.byteLength&&this._sourceBuffer[e]?this._enqueueOp(e,(function(){var s;n.mediaSource&&!n.media.error&&(n._logger.debug("MSE APPEND START",i),n._opst=si(),null===(s=n._sourceBuffer[e])||void 0===s||s.appendBuffer(t))}),Yi,i):Promise.resolve()}},{key:"remove",value:function(e,t,i,n){var s=this,r=!1;return this._mseFullFlag[e]&&(r=!0),this._enqueueOp(e,(function(){if(s.mediaSource&&!s.media.error){var r=s._sourceBuffer[e];t>=i||!r?s._onSBUpdateEnd(e):(s._opst=si(),s._logger.debug("MSE REMOVE START",e,t,i,n),r.remove(t,i))}}),Ti,n,r)}},{key:"clearBuffer",value:function(e,t){var i,n=this;return Object.keys(this._sourceBuffer).forEach((function(s){i=n._enqueueOp(s,(function(){if(n.mediaSource&&!n.media.error){var i=n._sourceBuffer[s];n._logger.debug("MSE clearBuffer START",s,e,t),i.remove(e,t)}}),Ti)})),i||Promise.resolve()}},{key:"clearAllBuffer",value:function(){var e,t=this;return Object.keys(this._sourceBuffer).forEach((function(i){e=t._enqueueOp(i,(function(){if(t.mediaSource&&!t.media.error){var e=t._sourceBuffer[i];t._logger.debug("MSE clearAllBuffer START",i),e.remove(0,ai.end(ai.get(e)))}}))})),e}},{key:"clearOpQueues",value:function(e,t){var i;this._logger.debug("MSE clearOpQueue START");var n=this._queue[e];if(t&&n)this._queue[e]=[];else if(n&&n[e]&&!(n.length<5)){var s=[];n.forEach((function(e){e.context&&e.context.isinit&&s.push(e)})),this._queue[e]=n.slice(0,2),s.length>0&&(i=this._queue[e]).push.apply(i,s)}}},{key:"endOfStream",value:function(e){var t=this;return this.mediaSource&&"open"===this.mediaSource.readyState?this._enqueueBlockingOp((function(){var i=t.mediaSource;i&&"open"===i.readyState&&(t._logger.debug("MSE endOfStream START"),e?i.endOfStream(e):i.endOfStream())}),"endOfStream"):Promise.resolve()}},{key:"setLiveSeekableRange",value:function(e,t){var i=this.mediaSource;e<0||t<e||null==i||!i.setLiveSeekableRange||"open"!==i.readyState||i.setLiveSeekableRange(e,t)}},{key:"getSourceBuffer",value:function(e){return this._sourceBuffer[e]}},{key:"buffered",value:function(e){return ai.get(this._sourceBuffer[e])}},{key:"bufferStart",value:function(e){return ai.start(this.buffered(e))}},{key:"bufferEnd",value:function(e){return ai.end(this.buffered(e))}},{key:"_enqueueOp",value:function(e,t,i,n,s){var r=this;if(!this.mediaSource)return Promise.resolve();var a=this._queue[e]=this._queue[e]||[],l={exec:t,promise:ni(),opName:i,context:n};return s?(a.splice(0,0,l),this._mseFullFlag[e]=!1,this._startQueue(e)):a.push(l),this.isOpened||this.isEnded?1===a.length&&this._startQueue(e):this._openPromise.then((function(){1===a.length&&r._startQueue(e)})),l.promise}},{key:"_enqueueBlockingOp",value:(t=h(d().mark((function e(t,i,n){var s,r,a=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mediaSource){e.next=2;break}return e.abrupt("return",Promise.resolve());case 2:if((s=Object.keys(this._sourceBuffer)).length){e.next=5;break}return e.abrupt("return",t());case 5:return r=[],s.forEach((function(e){var t=a._queue[e],s=ni();r.push(s),t.push({exec:function(){s.resolve()},promise:s,opName:i,context:n}),1===t.length&&a._startQueue(e)})),e.abrupt("return",Promise.all(r).then((function(){try{return t()}finally{s.forEach((function(e){var t=a._queue[e],i=a._sourceBuffer[e];null==t||t.shift(),i&&i.updating||a._startQueue(e)}))}})));case 8:case"end":return e.stop()}}),e,this)}))),function(e,i,n){return t.apply(this,arguments)})},{key:"_startQueue",value:function(e){var t=this._queue[e];if(t){var i=t[0];if(i&&!this._mseFullFlag[e])try{i.exec()}catch(n){n&&n.message&&n.message.indexOf("SourceBuffer is full")>=0?(this._mseFullFlag[e]=!0,this._logger.error("[MSE error],  context,",i.context," ,name,",i.opName,",err,SourceBuffer is full"),i.promise.reject(new Li(pi,fi.MSE_FULL,n))):(this._logger.error(n),i.promise.reject(new Li(pi,fi.MSE_OTHER,n)),t.shift(),this._startQueue(e))}}}},{key:"setTimeoffset",value:function(e,t,i){var n=this;return this._enqueueOp(e,(function(){t<0&&(t+=.001),n._sourceBuffer[e].timestampOffset=t,n._onSBUpdateEnd(e)}),"setTimeoffset",i)}},{key:"abort",value:function(e,t){var i=this;return this.isOpened?this._enqueueOp(e,(function(){i._sourceBuffer[e].abort(),i._onSBUpdateEnd(e)}),"abort",t):Promise.resolve()}}],[{key:"getDefaultConfig",value:function(){return{openLog:!1}}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if(!ki)return!1;try{return ki.isTypeSupported(e)}catch(t){return this._logger.error(e,t),!1}}}]),e}();y(Ri,"VIDEO","video"),y(Ri,"AUDIO","audio");var Mi="fetch",Ki="xhr",Ii="ws",Hi="arraybuffer",Ci="text",Ni="json",Fi=function(e){Z(i,e);var t=S(i);function i(e,n,s,r){var a;return m(this,i),y(x(a=t.call(this,r)),"retryCount",0),y(x(a),"isTimeout",!1),y(x(a),"loaderType",Mi),y(x(a),"startTime",0),y(x(a),"endTime",0),y(x(a),"options",{}),a.url=e,a.request=n,a.response=s,a}return b(i)}(W(Error)),zi=Object.prototype.toString;function ji(e){if("[object Object]"!==zi.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function Ui(e){if(e&&null!==e[0]&&void 0!==e[0]&&(0!==e[0]||null!==e[1]&&void 0!==e[1])){var t="bytes="+e[0]+"-";return e[1]&&(t+=e[1]),t}}function Ji(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Pi(e,t){if(e){if(!t)return e;var i,n=Object.keys(t).map((function(e){if(null!=(i=t[e]))return Array.isArray(i)?e+="[]":i=[i],i.map((function(t){var i;return i=t,"[object Date]"===zi.call(i)?t=t.toISOString():function(e){return null!==e&&"object"===c(e)}(t)&&(t=JSON.stringify(t)),"".concat(Ji(e),"=").concat(Ji(t))})).join("&")})).filter(Boolean).join("&");if(n){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}}function Di(e,t,i,n,s,r,a,l,o,d,c){return s=null!=s?parseFloat(s):null,n=parseInt(n||"0",10),Number.isNaN(n)&&(n=0),{data:e,done:t,options:{range:o,vid:d,index:l,contentLength:n,age:s,startTime:r,firstByteTime:a,endTime:Date.now(),priOptions:c},response:i}}function Ei(e,t){return Math.round(8*e*1e3/t/1024)}var _i="real_time_speed",Bi=2097152,Qi=function(e){Z(n,e);var t,i=S(n);function n(){var e;return m(this,n),y(x(e=i.call(this)),"_abortController",null),y(x(e),"_timeoutTimer",null),y(x(e),"_reader",null),y(x(e),"_response",null),y(x(e),"_aborted",!1),y(x(e),"_index",-1),y(x(e),"_range",null),y(x(e),"_receivedLength",0),y(x(e),"_running",!1),y(x(e),"_logger",null),y(x(e),"_vid",""),y(x(e),"_onProcessMinLen",0),y(x(e),"_onCancel",null),y(x(e),"_priOptions",null),e}return b(n,[{key:"load",value:function(e){var t,i=this,n=e.url,s=e.vid,r=e.timeout,a=e.responseType,l=e.onProgress,o=e.index,c=e.onTimeout,u=e.onCancel,m=e.range,p=e.transformResponse,b=e.request,y=e.params,Z=e.logger,f=e.method,X=e.headers,L=e.body,v=e.mode,W=e.credentials,G=e.cache,x=e.redirect,g=e.referrer,S=e.referrerPolicy,V=e.onProcessMinLen,k=e.priOptions;this._logger=Z,this._aborted=!1,this._onProcessMinLen=V,this._onCancel=u,this._abortController="undefined"!=typeof AbortController&&new AbortController,this._running=!0,this._index=o,this._range=m||[0,0],this._vid=s||n,this._priOptions=k||{};var Y={method:f,headers:X,body:L,mode:v,credentials:W,cache:G,redirect:x,referrer:g,referrerPolicy:S,signal:null===(t=this._abortController)||void 0===t?void 0:t.signal},T=!1;clearTimeout(this._timeoutTimer),n=Pi(n,y);var w=Ui(m);w&&(X=b?b.headers:Y.headers=Y.headers||(Headers?new Headers:{}),Headers&&X instanceof Headers?X.append("Range",w):X.Range=w),r&&(this._timeoutTimer=setTimeout((function(){if(T=!0,i.cancel(),c){var e=new Fi(n,Y,null,"timeout");e.isTimeout=!0,c(e,{index:i._index,range:i._range,vid:i._vid,priOptions:i._priOptions})}}),r));var R=Date.now();return this._logger.debug("[fetch load start], index,",o,",range,",m),new Promise((function(e,t){fetch(b||n,b?void 0:Y).then(function(){var s=h(d().mark((function s(r){var c,u,h,b;return d().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:if(clearTimeout(i._timeoutTimer),i._response=r,!i._aborted&&i._running){s.next=4;break}return s.abrupt("return");case 4:if(p&&(r=p(r,n)||r),r.ok){s.next=7;break}throw new Fi(n,Y,r,"bad network response");case 7:if(c=Date.now(),a!==Ci){s.next=15;break}return s.next=11,r.text();case 11:u=s.sent,i._running=!1,s.next=37;break;case 15:if(a!==Ni){s.next=22;break}return s.next=18,r.json();case 18:u=s.sent,i._running=!1,s.next=37;break;case 22:if(!l){s.next=29;break}return i.resolve=e,i.reject=t,i._loadChunk(r,l,R,c),s.abrupt("return");case 29:return s.next=31,r.arrayBuffer();case 31:u=s.sent,u=new Uint8Array(u),i._running=!1,h=Date.now()-R,b=Ei(u.byteLength,h),i.emit(_i,{speed:b,len:u.byteLength,time:h,vid:i._vid,index:i._index,range:i._range,priOptions:i._priOptions});case 37:i._logger.debug("[fetch load end], index,",o,",range,",m),e(Di(u,!0,r,r.headers.get("Content-Length"),r.headers.get("age"),R,c,o,m,i._vid,i._priOptions));case 39:case"end":return s.stop()}}),s)})));return function(e){return s.apply(this,arguments)}}()).catch((function(e){var s;clearTimeout(i._timeoutTimer),i._running=!1,i._aborted&&!T||((e=e instanceof Fi?e:new Fi(n,Y,null,null===(s=e)||void 0===s?void 0:s.message)).startTime=R,e.endTime=Date.now(),e.isTimeout=T,e.options={index:i._index,range:i._range,vid:i._vid,priOptions:i._priOptions},t(e))}))}))}},{key:"cancel",value:(t=h(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._aborted){e.next=2;break}return e.abrupt("return");case 2:if(this._aborted=!0,this._running=!1,!this._response){e.next=14;break}if(e.prev=5,!this._reader){e.next=9;break}return e.next=9,this._reader.cancel();case 9:e.next=13;break;case 11:e.prev=11,e.t0=e.catch(5);case 13:this._response=this._reader=null;case 14:if(this._abortController){try{this._abortController.abort()}catch(t){}this._abortController=null}this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions});case 16:case"end":return e.stop()}}),e,this,[[5,11]])}))),function(){return t.apply(this,arguments)})},{key:"_loadChunk",value:function(e,t,i,n){var s=this;if(!e.body||!e.body.getReader){this._running=!1;var r=new Fi(e.url,"",e,"onProgress of bad response.body.getReader");return r.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},void this.reject(r)}this._onProcessMinLen>0&&(this._cache=new Uint8Array(Bi),this._writeIdx=0);var a,l,o,c=this._reader=e.body.getReader(),u=function(){var r=h(d().mark((function r(){var h,m,p,b,y,Z,f,X;return d().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return l=Date.now(),r.prev=1,r.next=4,c.read();case 4:a=r.sent,console.log(a),o=Date.now(),r.next=14;break;case 9:return r.prev=9,r.t0=r.catch(1),o=Date.now(),s._aborted||(s._running=!1,r.t0.options={index:s._index,range:s._range,vid:s._vid,priOptions:s._priOptions},s.reject(r.t0)),r.abrupt("return");case 14:if(m=(null===(h=s._range)||void 0===h?void 0:h.length)>0?s._range[0]:0,p=m+s._receivedLength,!s._aborted){r.next=20;break}return s._running=!1,t(void 0,!1,{range:[p,p],vid:s._vid,index:s._index,startTime:l,endTime:o,st:i,firstByteTime:n,priOptions:s._priOptions},e),r.abrupt("return");case 20:b=a.value?a.value.byteLength:0,s._receivedLength+=b,s._logger.debug("【fetchLoader,onProgress call】,task,",s._range,", start,",p,", end,",m+s._receivedLength,", done,",a.done),s._onProcessMinLen>0?s._writeIdx+b>=s._onProcessMinLen||a.done?((y=new Uint8Array(s._writeIdx+b)).set(s._cache.slice(0,s._writeIdx),0),b>0&&y.set(a.value,s._writeIdx),s._writeIdx=0,s._logger.debug("【fetchLoader,onProgress enough】,done,",a.done,",len,",y.byteLength,", writeIdx,",s._writeIdx)):b>0&&s._writeIdx+b<Bi?(s._cache.set(a.value,s._writeIdx),s._writeIdx+=b,s._logger.debug("【fetchLoader,onProgress cache】,len,",b,", writeIdx,",s._writeIdx)):b>0&&(Z=new Uint8Array(s._writeIdx+b+2048),s._logger.debug("【fetchLoader,onProgress extra start】,size,",s._writeIdx+b+2048,", datalen,",b,", writeIdx,",s._writeIdx),Z.set(s._cache.slice(0,s._writeIdx),0),b>0&&Z.set(a.value,s._writeIdx),s._writeIdx+=b,delete s._cache,s._cache=Z,s._logger.debug("【fetchLoader,onProgress extra end】,len,",b,", writeIdx,",s._writeIdx)):y=a.value,(y&&y.byteLength>0||a.done)&&(console.log(y,"fetch"),t(y,a.done,{range:[s._range[0]+s._receivedLength-(y?y.byteLength:0),s._range[0]+s._receivedLength],vid:s._vid,index:s._index,startTime:l,endTime:o,st:i,firstByteTime:n,priOptions:s._priOptions},e)),a.done?(f=Date.now()-i,X=Ei(s._receivedLength,f),s.emit(_i,{speed:X,len:s._receivedLength,time:f,vid:s._vid,index:s._index,range:s._range,priOptions:s._priOptions}),s._running=!1,s._logger.debug("[fetchLoader onProgress end],task,",s._range,",done,",a.done),s.resolve(Di(a,!0,e,e.headers.get("Content-Length"),e.headers.get("age"),i,n,s._index,s._range,s._vid,s._priOptions))):u();case 26:case"end":return r.stop()}}),r,null,[[1,9]])})));return function(){return r.apply(this,arguments)}}();u()}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}}],[{key:"isSupported",value:function(){return!("undefined"==typeof fetch)}}]),n}(H),Oi=2097152,Ai=function(e){Z(n,e);var t,i=S(n);function n(){var e;return m(this,n),y(x(e=i.call(this)),"_socket",null),y(x(e),"_timeoutTimer",null),y(x(e),"_response",null),y(x(e),"_aborted",!1),y(x(e),"_index",-1),y(x(e),"_range",null),y(x(e),"_receivedLength",0),y(x(e),"_running",!1),y(x(e),"_logger",null),y(x(e),"_vid",""),y(x(e),"_onProcessMinLen",0),y(x(e),"_onCancel",null),y(x(e),"_priOptions",null),y(x(e),"_startTime",null),y(x(e),"_endTime",null),e}return b(n,[{key:"load",value:function(e){var t=this,i=e.url,n=e.vid,s=e.timeout,r=e.responseType,a=e.onProgress,l=e.index,o=e.onTimeout,d=e.onCancel,c=e.range;e.transformResponse,e.request;var u=e.params,h=e.logger;e.method,e.headers,e.body,e.mode,e.credentials,e.cache,e.redirect,e.referrer,e.referrerPolicy;var m=e.onProcessMinLen,p=e.priOptions;this._logger=h,this._aborted=!1,this._onProcessMinLen=m,this._onCancel=d,this._running=!0,this._index=l,this._range=c||[0,0],this._vid=n||i,this._priOptions=p||{};var b=!1;clearTimeout(this._timeoutTimer),i=Pi(i,u),s&&(this._timeoutTimer=setTimeout((function(){if(b=!0,t.cancel(),o){var e=new Fi(i,null,"timeout");e.isTimeout=!0,o(e,{index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions})}}),s));var y=Date.now();return new Promise((function(e,n){t._socket=new WebSocket(i),t._socket.binaryType=r,t._logger.debug("[websocket load start], index,",l,",ws,",t._socket),t._socket.onopen=function(){if(clearTimeout(t._timeoutTimer),!t._aborted&&t._running){t._logger.debug("[websocket connected],ws",t._socket),t._startTime=Date.now();var i=Date.now();t._socket.onmessage=function(s){if(t._startTime=t._endTime||t._startTime,t._endTime=Date.now(),!t._aborted&&t._running){var o;if(r===Ci)o=s.data,t._running=!1;else if(r===Ni)o=JSON.parse(s.data),t._running=!1;else{if(a)return t.resolve=e,t.reject=n,void t._loadChunk(new Uint8Array(s.data),a,y,i);o=new Uint8Array(s.data),t._running=!1;var d=Date.now()-y,u=Ei(o.byteLength,d);t.emit(_i,{speed:u,len:o.byteLength,time:d,vid:t._vid,index:t._index,range:t._range,priOptions:t._priOptions})}t._logger.debug("[websocket load end], index,",l,",range,",c),e(Di(o,!0,null,null,null,y,i,l,c,t._vid,t._priOptions))}}}},t._socket.onclose=function(e){t._endTime=null,t._startTime=null},t._socket.onerror=function(e){var s;t._endTime=Date.now(),clearTimeout(t._timeoutTimer),t._running=!1,t._aborted&&!b||((e=new Fi(i,null,null===(s=e)||void 0===s?void 0:s.message)).startTime=y,e.endTime=Date.now(),e.isTimeout=b,e.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},n(e))}}))}},{key:"cancel",value:(t=h(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._aborted){e.next=2;break}return e.abrupt("return");case 2:if(this._aborted=!0,this._running=!1,this._socket){try{this._socket.close()}catch(t){}this._socket=null}this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions});case 6:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"_loadChunk",value:function(e,t,i,n,s){var r;this._onProcessMinLen>0&&(this._cache=new Uint8Array(Oi),this._writeIdx=0);var a=(null===(r=this._range)||void 0===r?void 0:r.length)>0?this._range[0]:0,l=a+this._receivedLength;if(this._aborted)return this._running=!1,void t(void 0,!1,{range:[l,l],vid:this._vid,index:this._index,startTime:this._startTime,endTime:this._endTime,st:i,firstByteTime:n,priOptions:this._priOptions});var o=e.byteLength;this._receivedLength+=o;var d,c=[2,3].indexOf(this._socket.readyState)>0;if(this._logger.debug("【WsLoader,onProgress call】,task,",this._range,", start,",l,", end,",a+this._receivedLength,", done,",c),this._onProcessMinLen>0){if(this._writeIdx+o>=this._onProcessMinLen||c)(d=new Uint8Array(this._writeIdx+o)).set(this._cache.slice(0,this._writeIdx),0),o>0&&d.set(e,this._writeIdx),this._writeIdx=0,this._logger.debug("【WsLoader,onProgress enough】,done,",c,",len,",d.byteLength,", writeIdx,",this._writeIdx);else if(o>0&&this._writeIdx+o<Oi)this._cache.set(e,this._writeIdx),this._writeIdx+=o,this._logger.debug("【WsLoader,onProgress cache】,len,",o,", writeIdx,",this._writeIdx);else if(o>0){var u=new Uint8Array(this._writeIdx+o+2048);this._logger.debug("【WsLoader,onProgress extra start】,size,",this._writeIdx+o+2048,", datalen,",o,", writeIdx,",this._writeIdx),u.set(this._cache.slice(0,this._writeIdx),0),o>0&&u.set(e,this._writeIdx),this._writeIdx+=o,delete this._cache,this._cache=u,this._logger.debug("【WsLoader,onProgress extra end】,len,",o,", writeIdx,",this._writeIdx)}}else d=e;if((d&&d.byteLength>0||c)&&t(d,c,{range:[this._range[0]+this._receivedLength-(d?d.byteLength:0),this._range[0]+this._receivedLength],vid:this._vid,index:this._index,startTime:this._startTime,endTime:this._endTime,st:i,firstByteTime:n,priOptions:this._priOptions},null),c){var h=Date.now()-i,m=Ei(this._receivedLength,h);this.emit(_i,{speed:m,len:this._receivedLength,time:h,vid:this._vid,index:this._index,range:this._range,priOptions:this._priOptions}),this._running=!1,this._logger.debug("[WsLoader onProgress end],task,",this._range,",done,",!0),this.resolve(Di(d,!0,null,null,null,i,n,this._index,this._range,this._vid,this._priOptions))}}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}}],[{key:"isSupported",value:function(){return!("undefined"==typeof WebSocket)}}]),n}(H);var qi=function(e){Z(i,e);var t=S(i);function i(){var e;return m(this,i),y(x(e=t.call(this)),"_xhr",null),y(x(e),"_aborted",!1),y(x(e),"_timeoutTimer",null),y(x(e),"_range",null),y(x(e),"_receivedLength",0),y(x(e),"_url",null),y(x(e),"_onProgress",null),y(x(e),"_index",-1),y(x(e),"_headers",null),y(x(e),"_currentChunkSizeKB",384),y(x(e),"_timeout",null),y(x(e),"_xhr",null),y(x(e),"_withCredentials",null),y(x(e),"_startTime",-1),y(x(e),"_loadCompleteResolve",null),y(x(e),"_loadCompleteReject",null),y(x(e),"_runing",!1),y(x(e),"_logger",!1),y(x(e),"_vid",""),y(x(e),"_responseType",void 0),y(x(e),"_credentials",void 0),y(x(e),"_method",void 0),y(x(e),"_transformResponse",void 0),y(x(e),"_firstRtt",void 0),y(x(e),"_onCancel",null),y(x(e),"_priOptions",null),e}return b(i,[{key:"load",value:function(e){var t=this;clearTimeout(this._timeoutTimer),this._logger=e.logger,this._range=e.range,this._onProgress=e.onProgress,this._index=e.index,this._headers=e.headers,this._withCredentials="include"===e.credentials||"same-origin"===e.credentials,this._body=e.body||null,e.method&&(this._method=e.method),this._timeout=e.timeout||null,this._runing=!0,this._vid=e.vid||e.url,this._responseType=e.responseType,this._firstRtt=-1,this._onTimeout=e.onTimeout,this._onCancel=e.onCancel,this._request=e.request,this._priOptions=e.priOptions||{},this._logger.debug("【xhrLoader task】, range",this._range),this._url=Pi(e.url,e.params);var i=Date.now();return new Promise((function(e,i){t._loadCompleteResolve=e,t._loadCompleteReject=i,t._startLoad()})).catch((function(e){if(clearTimeout(t._timeoutTimer),t._runing=!1,!t._aborted)throw(e=e instanceof Fi?e:new Fi(t._url,t._request)).startTime=i,e.endTime=Date.now(),e.options={index:t._index,vid:t._vid,priOptions:t._priOptions},e}))}},{key:"_startLoad",value:function(){var e=null;if(this._responseType===Hi&&this._range&&this._range.length>1)if(this._onProgress){this._firstRtt=-1;var t=1024*this._currentChunkSizeKB,i=this._range[0]+this._receivedLength,n=this._range[1];t<this._range[1]-i&&(n=i+t),e=[i,n],this._logger.debug("[xhr_loader->],tast :",this._range,", SubRange, ",e)}else e=this._range,this._logger.debug("[xhr_loader->],tast :",this._range,", allRange, ",e);this._internalOpen(e)}},{key:"_internalOpen",value:function(e){var t=this;try{this._startTime=Date.now();var i=this._xhr=new XMLHttpRequest;i.open(this._method||"GET",this._url,!0),i.responseType=this._responseType,this._timeout&&(i.timeout=this._timeout),i.withCredentials=this._withCredentials,i.onload=this._onLoad.bind(this),i.onreadystatechange=this._onReadyStatechange.bind(this),i.onerror=function(e){var i,n,s;t._running=!1;var r=new Fi(t._url,t._request,null==e||null===(i=e.currentTarget)||void 0===i?void 0:i.response,"xhr.onerror.status:"+(null==e||null===(n=e.currentTarget)||void 0===n?void 0:n.status)+",statusText,"+(null==e||null===(s=e.currentTarget)||void 0===s?void 0:s.statusText));r.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(r)},i.ontimeout=function(e){t.cancel();var i=new Fi(t._url,t._request,{status:408},"timeout");t._onTimeout&&(i.isTimeout=!0,t._onTimeout(i,{index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions})),i.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(i)};var n=this._headers||{},s=Ui(e);s&&(n.Range=s),n&&Object.keys(n).forEach((function(e){i.setRequestHeader(e,n[e])})),this._logger.debug("[xhr.send->] tast,",this._range,",load sub range, ",e),i.send(this._body)}catch(r){r.options={index:this._index,range:e,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(r)}}},{key:"_onReadyStatechange",value:function(e){2===e.target.readyState&&this._firstRtt<0&&(this._firstRtt=Date.now())}},{key:"_onLoad",value:function(e){var t,i=e.target.status;if(i<200||i>299){var n=new Fi(this._url,null,o(o({},e.target.response),{},{status:i}),"bad response,status:"+i);return n.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(n)}var s,r=null,a=!1,l=(null===(t=this._range)||void 0===t?void 0:t.length)>0?this._range[0]:0;if(this._responseType===Hi){var d,c=new Uint8Array(e.target.response);if(s=l+this._receivedLength,c&&c.byteLength>0){this._receivedLength+=c.byteLength;var u=Date.now()-this._startTime,h=Ei(this._receivedLength,u);this.emit(_i,{speed:h,len:this._receivedLength,time:u,vid:this._vid,index:this._index,range:[s,l+this._receivedLength],priOptions:this._priOptions})}r=c,a=!((null===(d=this._range)||void 0===d?void 0:d.length)>1&&this._range[1]&&this._receivedLength<this._range[1]-this._range[0]),this._logger.debug("[xhr load done->], tast :",this._range,", start",s,"end ",l+this._receivedLength,",dataLen,",c?c.byteLength:0,",receivedLength",this._receivedLength,",index,",this._index,", done,",a)}else a=!0,r=e.target.response;var m={ok:i>=200&&i<300,status:i,statusText:this._xhr.statusText,url:this._xhr.responseURL,headers:this._getHeaders(this._xhr),body:this._xhr.response};this._transformResponse&&(m=this._transformResponse(m,this._url)||m),this._onProgress&&this._onProgress(r,a,{index:this._index,vid:this._vid,range:[s,l+this._receivedLength],startTime:this._startTime,endTime:Date.now(),priOptions:this._priOptions},m),a?(this._runing=!1,this._loadCompleteResolve&&this._loadCompleteResolve(Di(this._onProgress?null:r,a,m,m.headers["content-length"],m.headers.age,this._startTime,this._firstRtt,this._index,this._range,this._vid,this._priOptions))):this._startLoad()}},{key:"cancel",value:function(){if(!this._aborted)return this._aborted=!0,this._runing=!1,k(f(i.prototype),"removeAllListeners",this).call(this),this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions}),this._xhr?this._xhr.abort():void 0}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}},{key:"_getHeaders",value:function(e){var t,i={},n=function(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=w(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,s=function(){};return{s:s,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return a=e.done,e},e:function(e){l=!0,r=e},f:function(){try{a||null==i.return||i.return()}finally{if(l)throw r}}}}(e.getAllResponseHeaders().trim().split("\r\n"));try{for(n.s();!(t=n.n()).done;){var s=t.value.split(": ");i[s[0].toLowerCase()]=s.slice(1).join(": ")}}catch(r){n.e(r)}finally{n.f()}return i}}],[{key:"isSupported",value:function(){return"undefined"!=typeof XMLHttpRequest}}]),i}(H),$i=["retry","retryDelay","onRetryError","transformError"],en=function(){function e(t,i){m(this,e),this.promise=ni(),this.alive=!!i.onProgress,!i.logger&&(i.logger=new xi("Loader")),this._loaderType=t,this._loader=t===Mi&&window.fetch?new Qi:t===Ii&&window.WebSocket?new Ai:new qi,this._config=i,this._retryCount=0,this._retryTimer=null,this._canceled=!1,this._retryCheckFunc=i.retryCheckFunc,this._logger=i.logger}var t;return b(e,[{key:"exec",value:function(){var e=this,t=this._config,i=t.retry,n=t.retryDelay,s=t.onRetryError,r=t.transformError,a=G(t,$i),l=function(){var t=h(d().mark((function t(){var o,c,u;return d().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e._loader.load(a);case 3:o=t.sent,e.promise.resolve(o),t.next=27;break;case 7:if(t.prev=7,t.t0=t.catch(0),e._loader.running=!1,e._logger.debug("[task request catch err]",t.t0),!e._canceled){t.next=13;break}return t.abrupt("return");case 13:if(t.t0.loaderType=e._loaderType,t.t0.retryCount=e._retryCount,c=t.t0,r&&(c=r(c)||c),s&&e._retryCount>0&&s(c,e._retryCount,{index:a.index,vid:a.vid,range:a.range,priOptions:a.priOptions}),e._retryCount++,u=!0,e._retryCheckFunc&&(u=e._retryCheckFunc(t.t0)),!(u&&e._retryCount<=i)){t.next=26;break}return clearTimeout(e._retryTimer),e._logger.debug("[task request setTimeout],retry",e._retryCount,",retry range,",a.range),e._retryTimer=setTimeout(l,n),t.abrupt("return");case 26:e.promise.reject(c);case 27:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(){return t.apply(this,arguments)}}();return l(),this.promise}},{key:"cancel",value:(t=h(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return clearTimeout(this._retryTimer),this._canceled=!0,this._loader.running=!1,e.abrupt("return",this._loader.cancel());case 4:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"running",get:function(){return this._loader&&this._loader.running}},{key:"loader",get:function(){return this._loader}}]),e}();function tn(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];if((t=t.filter(Boolean)).length<2)return t[0];var n=new Uint8Array(t.reduce((function(e,t){return e+t.byteLength}),0)),s=0;return t.forEach((function(e){n.set(e,s),s+=e.byteLength})),n}function nn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new Promise((function(t){return setTimeout(t,e)}))}var sn=function(e){Z(n,e);var t,i=S(n);function n(e){var t;return m(this,n),y(x(t=i.call(this,e)),"type",Mi),y(x(t),"_queue",[]),y(x(t),"_alive",[]),y(x(t),"_currentTask",null),y(x(t),"_config",void 0),t._config=function(e){return o({loaderType:Mi,retry:0,retryDelay:0,timeout:0,request:null,onTimeout:void 0,onProgress:void 0,onRetryError:void 0,transformRequest:void 0,transformResponse:void 0,transformError:void 0,responseType:Ci,range:void 0,url:"",params:void 0,method:"GET",headers:{},body:void 0,mode:void 0,credentials:void 0,cache:void 0,redirect:void 0,referrer:void 0,referrerPolicy:void 0,integrity:void 0,onProcessMinLen:0},e)}(e),t._config.loaderType!==Ki&&(Qi.isSupported()||Ai.isSupported())||(t.type=Ki),t.log=e.logger,t}return b(n,[{key:"isFetch",value:function(){return this.type===Mi}},{key:"isWs",value:function(){return this.type===Ii}},{key:"isWebSocketURL",value:function(e){return/wss?:\/\/(.+?)/.test(e)}},{key:"load",value:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!1;"string"!=typeof e&&e?(i=e,n=!!this.isWebSocketURL(i.url)):(i.url=e||i.url||this._config.url,n=!!this.isWebSocketURL(i.url)),(this._config.loaderType===Ii||n)&&Ai.isSupported()&&(this.type=Ii,this._config.loaderType=i.loaderType=Ii),(i=Object.assign({},this._config,i)).params&&(i.params=Object.assign({},i.params)),i.headers&&ji(i.headers)&&(i.headers=Object.assign({},i.headers)),i.body&&ji(i.body)&&(i.body=Object.assign({},i.body)),i.transformRequest&&(i=i.transformRequest(i)||i),i.logger=this.log;var s=new en(this.type,i);return s.loader.on(_i,(function(e){t.emit(_i,e)})),this._queue.push(s),1!==this._queue.length||this._currentTask&&this._currentTask.running||this._processTask(),s.promise}},{key:"cancel",value:(t=h(d().mark((function e(){var t;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this._queue.map((function(e){return e.cancel()})).concat(this._alive.map((function(e){return e.cancel()}))),this._currentTask&&t.push(this._currentTask.cancel()),this._queue=[],this._alive=[],e.next=6,Promise.all(t);case 6:return e.next=8,nn();case 8:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"_processTask",value:function(){var e=this;if(this._currentTask=this._queue.shift(),this._currentTask){this._currentTask.alive&&this._alive.push(this._currentTask);var t=this._currentTask.exec().catch((function(e){}));t&&"function"==typeof t.finally&&t.finally((function(){var t,i;null!==(t=e._currentTask)&&void 0!==t&&t.alive&&(null===(i=e._alive)||void 0===i?void 0:i.length)>0&&(e._alive=e._alive.filter((function(t){return t&&t!==e._currentTask}))),e._processTask()}))}}}],[{key:"isFetchSupport",value:function(){return Qi.isSupported()}},{key:"isWsSupport",value:function(){return Ai.isSupported()}}]),n}(H);var rn=b((function e(t,i){m(this,e),this.type="file",this.message=t,this.data=i})),an=function(){function e(){m(this,e),y(this,"_data",Object.create(null))}return b(e,[{key:"set",value:function(e,t){this._data[e]=t}},{key:"get",value:function(e){return this._data[e]}},{key:"clear",value:function(){this._data=Object.create(null)}}]),e}();function ln(e,t){var i=e.trak;if(i&&i.length){var n=i.find((function(e){var t,i;return"vide"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)})),s=i.find((function(e){var t,i;return"soun"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)}));if(n||s){var r,a=[],l=[];if(n){var o,d,c,u=null===(o=n.mdia)||void 0===o||null===(d=o.minf)||void 0===d?void 0:d.stbl;if(!u)return;var h=null===(c=n.mdia.mdhd)||void 0===c?void 0:c.timescale,m=u.stts,p=u.stsc,b=u.stsz,y=u.stco,Z=u.stss,f=u.ctts;if(!(h&&m&&p&&b&&y&&Z))return;r=(a=on(t,h,m,p,b,y,Z,f)).map((function(e){return e.duration}))}if(s){var X,L,v,W=null===(X=s.mdia)||void 0===X||null===(L=X.minf)||void 0===L?void 0:L.stbl;if(!W)return;var G=null===(v=s.mdia.mdhd)||void 0===v?void 0:v.timescale,x=W.stts,g=W.stsc,S=W.stsz,V=W.stco;if(!(G&&x&&g&&S&&V))return;l=on(t,G,x,g,S,V,null,null,r)}return{videoSegments:a,audioSegments:l}}}}function on(e,t,i,n,s,r,a,l,o){var d,c,u,h,m=[],p=[],b=[],y=n.entries,Z=r.entries,f=s.entrySizes,X=null==a?void 0:a.entries,L=null==l?void 0:l.entries;L&&(d=[],L.forEach((function(e){for(var t=e.count,i=e.offset,n=0;n<t;n++)d.push(i)}))),X&&(c={},X.forEach((function(e){c[e-1]=!0})));var v=0,W=0,G=0,x=0,g=0,S=y[0].samplesPerChunk,V=y[1]?y[1].firstChunk-1:1/0,k=0,Y=-1;i.entries.forEach((function(e){var t=e.count,i=e.delta;h=i;for(var n=0;n<t;n++)u={dts:k,startTime:v,duration:h,size:f[W]||s.sampleSize,offset:Z[G]+g,index:W},X&&(u.keyframe=c[W],u.keyframe?(Y++,p.push([u]),b.push(u.duration)):(p[p.length-1].push(u),b[p.length-1]+=u.duration),u.gopId=Y),d&&W<d.length&&(u.pts=k+d[W]),0===W&&(u.pts=0),m.push(u),v+=h,k+=i,++W<S?g+=u.size:(G++,g=0,G>=V&&(x++,V=y[x+1]?y[x+1].firstChunk-1:1/0),S+=y[x].samplesPerChunk)}));var w=m.length;if(w&&(!a||m[0].keyframe)){var R,M=[],K=[],I=0,H=0,C=function(e){var i;R=K[K.length-1],M.push({index:M.length,startTime:(null===(i=M[M.length-1])||void 0===i?void 0:i.endTime)||K[0].startTime/t,endTime:(R.startTime+R.duration)/t,duration:e,range:[K[0].offset,R.offset+R.size],frames:K}),I=0,K=[]};if(a)for(var N=e*t,F=0,z=p.length;F<z;F++){var j;I+=b[F],(j=K).push.apply(j,T(p[F])),F+1<z?(0===F||I>N)&&C(I/t):C(I/t)}else for(var U=(o=o||[])[0]||e,J=0;J<w;J++){K.push(m[J]);var P=(I+=m[J].duration)/t;(J+1>=w||P+H>=U)&&(H+=P-U,C(P),U=o[M.length]||e)}return M}}function dn(e){var t="",i="",n=0,s=0,r=0,a=0,l=0,o=0,d=0;e.mvhd&&(l=e.mvhd.duration/e.mvhd.timescale);var c=e.trak;if(c){var u,h,m,p,b,y,Z,f,X,L,v,W,G,x,g,S,V,k=c.find((function(e){var t,i;return"vide"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)})),Y=c.find((function(e){var t,i;return"soun"===(null===(t=e.mdia)||void 0===t||null===(i=t.hdlr)||void 0===i?void 0:i.handlerType)})),T=null;if(k)if(T=null===(h=k.mdia)||void 0===h||null===(m=h.minf)||void 0===m||null===(p=m.stbl)||void 0===p?void 0:p.stsd.entries[0])if(n=T.width,s=T.height,o=null===(b=k.mdia)||void 0===b||null===(y=b.mdhd)||void 0===y?void 0:y.timescale,t=null===(Z=T.avcC||T.hvcC)||void 0===Z?void 0:Z.codec,"encv"===T.type)u=null===(f=T.sinf)||void 0===f||null===(X=f.schi)||void 0===X?void 0:X.tenc.default_KID;if(Y)if(T=null===(L=Y.mdia)||void 0===L||null===(v=L.minf)||void 0===v||null===(W=v.stbl)||void 0===W?void 0:W.stsd.entries[0])if(r=T.channelCount,a=T.sampleRate,i=null===(G=T.esds)||void 0===G?void 0:G.codec,d=null===(x=Y.mdia)||void 0===x||null===(g=x.mdhd)||void 0===g?void 0:g.timescale,"enca"===T.type)u=u||(null===(S=T.sinf)||void 0===S||null===(V=S.schi)||void 0===V?void 0:V.tenc.default_KID);return{kid:u?u.join(""):null,videoCodec:t,audioCodec:i,width:n,height:s,videoTimescale:o,audioChannelCount:r,audioSampleRate:a,duration:l,audioTimescale:d,moov:e}}}function cn(e){return"number"==typeof e&&!Number.isNaN(e)}var un=["vid","cache"],hn=function(e){Z(L,e);var t,i,n,s,r,a,l,c,u,p,f,X=S(L);function L(e){var t;m(this,L),y(x(t=X.call(this)),"vid",""),y(x(t),"url",""),y(x(t),"meta",{}),y(x(t),"downloadInfo",[]),y(x(t),"videoSegments",[]),y(x(t),"audioSegments",[]),y(x(t),"cache",null),y(x(t),"_currentSegmentIndex",-1),y(x(t),"_currentLoadingSegmentIndex",-1),y(x(t),"buffer",void 0),y(x(t),"_error",void 0),y(x(t),"_transformError",(function(e){return e})),t._config=o({vid:"",moovEnd:8e4,segmentDuration:2,maxDownloadInfoSize:30,responseType:"arraybuffer",cache:null},e);var i=t._config,n=i.vid,s=i.cache,r=G(i,un);return t.cache=s||new an,t.vid=n||r.url,t.url=r.url,r.transformError=t._transformError,t.logger=new xi("MP4Loader_"+t.vid),e.openLog&&xi.enable(),r.logger=t.logger,t._loader=new sn(r),t._loader.on(_i,(function(e){t.emit(_i,e)})),t}return b(L,[{key:"isMetaLoaded",get:function(){return this.videoSegments.length||this.audioSegments.length}},{key:"setCurrentSegment",value:function(e){cn(e)&&(this._currentSegmentIndex=e)}},{key:"isLastSegment",value:function(e){var t,i;return!!cn(e)&&e>=((null===(t=this.videoSegments[this.videoSegments.length-1])||void 0===t?void 0:t.index)||(null===(i=this.audioSegments[this.audioSegments.length-1])||void 0===i?void 0:i.index)||0)}},{key:"isSegmentLoading",value:function(e){return e===this._currentLoadingSegmentIndex}},{key:"changeUrl",value:(f=h(d().mark((function e(t){var i,n,s,r=arguments;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=r.length>1&&void 0!==r[1]?r[1]:t,n=r.length>2?r[2]:void 0,s=r.length>3?r[3]:void 0,e.next=5,this.reset(s);case 5:t&&(this.url=t),i&&(this.vid=i),n&&(this._config.moovEnd=n);case 8:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"getOrLoadMeta",value:(p=h(d().mark((function e(t){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isMetaLoaded){e.next=3;break}return e.next=3,this.loadMeta(t);case 3:return e.abrupt("return",this.meta);case 4:case"end":return e.stop()}}),e,this)}))),function(e){return p.apply(this,arguments)})},{key:"loadMetaProcess",value:(u=h(d().mark((function e(t,i,n,s){var r,a,l,c,u=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=Y(i,2),a=r[0],l=r[1],this._error=!1,this.logger.debug("[loadMetaProcess start], range,",[a,l]),c=function(){var e=h(d().mark((function e(i,r,o){var c,h,m,p,b,y,Z,f;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(u.meta&&null!=o&&o.range&&o.range.length>0&&o.range[1]>=l&&(r=!0,u.logger.debug("[loadMetaProcess],data done,setstate true,[",a,l,"]")),r&&null!=o&&o.range&&o.range.length>0&&o.range[1]<l&&(r=!1,u.logger.debug("[loadMetaProcess],data not done,setstate false,[",a,l,"]")),u.logger.debug("[loadMetaProcess],task,[",a,l,"], range,",o.range,",dataLen,",i?i.byteLength:void 0,", state,",r,",err,",u._error),!u._error&&i&&i.byteLength>0&&n(i,r,o),!u.meta.moov&&!u._error){e.next=6;break}return e.abrupt("return");case 6:if(!(i&&i.byteLength>0)){e.next=44;break}if(u.buffer=tn(u.buffer,i),c=Me.findBox(u.buffer,["moov"])[0]){e.next=23;break}if(h=Me.findBox(u.buffer,["mdat"])[0],!r){e.next=23;break}if(h){e.next=18;break}return u._error=!0,n(null,r,o,{err:"cannot find moov or mdat box"}),e.abrupt("return");case 18:return m=h.start+h.size,e.next=21,u.loadData([m,""],t,s);case 21:(p=e.sent)&&(c=Me.findBox(p.data,["moov"])[0]);case 23:if(!(c&&r&&c.size>c.data.length)){e.next=27;break}return u.logger.debug("[loadMetaProcess],moov not all, range,",o.range[1],",dataLen,",u.buffer.byteLength,", state,",r),e.next=27,u.loadMetaProcess(t,[o.range[1],c.start+c.size-1],n);case 27:if(!(c&&c.size<=c.data.length)||u.meta.moov){e.next=44;break}if(b=Me.moov(c)){e.next=33;break}return u._error=!0,n(null,r,o,{err:"cannot parse moov box"}),e.abrupt("return");case 33:if(y=ln(b,u._config.segmentDuration)){e.next=38;break}return u._error=!0,n(null,r,o,{err:"cannot parse segments"}),e.abrupt("return");case 38:u.meta=dn(b),Z=y.videoSegments,f=y.audioSegments,u.videoSegments=Z,u.audioSegments=f,u.logger.debug("[loadMetaProcess] moov ok"),n(void 0,r,{meta:{meta:u.meta,videoSegments:Z,audioSegments:f}});case 44:case"end":return e.stop()}}),e)})));return function(t,i,n){return e.apply(this,arguments)}}(),e.next=6,this.loadData([a,l||this._config.moovEnd],t,o({onProgress:c},s));case 6:case"end":return e.stop()}}),e,this)}))),function(e,t,i,n){return u.apply(this,arguments)})},{key:"loadMeta",value:(c=h(d().mark((function e(t,i,n){var s,r,a,l,o,c,u,h,m;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s=[],this.logger.debug("[loadMeta start]"),e.next=4,this.loadData([0,i||this._config.moovEnd],t,n);case 4:if(r=e.sent){e.next=7;break}return e.abrupt("return");case 7:if(s.push(r),a=Me.findBox(r.data,["moov"])[0]){e.next=23;break}if(l=Me.findBox(r.data,["mdat"])[0]){e.next=13;break}throw new rn("cannot find moov or mdat box",r.data);case 13:return o=l.start+l.size,e.next=16,this.loadData([o],t,n);case 16:if(r=e.sent){e.next=19;break}return e.abrupt("return");case 19:if(s.push(r),a=Me.findBox(r.data,["moov"],o)[0]){e.next=23;break}throw new rn("cannot find moov box",r.data);case 23:if(!(a.size>a.data.length)){e.next=31;break}return e.next=26,this.loadData([r.data.length,a.start+a.size-1],t,n);case 26:if(r=e.sent){e.next=29;break}return e.abrupt("return");case 29:s.push(r),a.data=tn(a.data,r.data);case 31:if(c=Me.moov(a)){e.next=34;break}throw new rn("cannot parse moov box",a.data);case 34:if(u=ln(c,this._config.segmentDuration)){e.next=37;break}throw new rn("cannot parse segments",a.data);case 37:return this.meta=dn(c),h=u.videoSegments,m=u.audioSegments,this.videoSegments=h,this.audioSegments=m,this.logger.debug("[load moov end!!!!!]"),e.abrupt("return",{meta:this.meta,videoSegments:h,audioSegments:m,responses:s});case 43:case"end":return e.stop()}}),e,this)}))),function(e,t,i){return c.apply(this,arguments)})},{key:"loadCacheMeta",value:function(e,t){var i=ln(e.moov,this._config.segmentDuration),n=i.videoSegments,s=i.audioSegments;this.videoSegments=n,this.audioSegments=s,this._currentSegmentIndex=t,this.meta=e}},{key:"getSegmentByTime",value:function(e){var t,i;return this.videoSegments.length?(t=this.videoSegments.find((function(t){return t.startTime<=e&&t.endTime>e})))&&(i=this.audioSegments[t.index]):i=this.audioSegments.find((function(t){return t.startTime<=e&&t.endTime>e})),{video:t,audio:i}}},{key:"loadSegmentByTime",value:(l=h(d().mark((function e(t,i){var n,s,r,a,l,o=arguments;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=!(o.length>2&&void 0!==o[2])||o[2],s=o.length>3&&void 0!==o[3]?o[3]:{},this.isMetaLoaded){e.next=5;break}return e.next=5,this.loadMeta(i);case 5:return r=this.getSegmentByTime(t),a=r.video,l=r.audio,e.abrupt("return",this._loadSegment(a,l,i,n,s));case 7:case"end":return e.stop()}}),e,this)}))),function(e,t){return l.apply(this,arguments)})},{key:"loadNextSegment",value:(a=h(d().mark((function e(t){var i,n,s,r,a=arguments;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=!(a.length>1&&void 0!==a[1])||a[1],n=a.length>2&&void 0!==a[2]?a[2]:{},this.isMetaLoaded){e.next=5;break}return e.next=5,this.loadMeta();case 5:return s=this.videoSegments[this._currentSegmentIndex+1],r=this.audioSegments[this._currentSegmentIndex+1],e.abrupt("return",this._loadSegment(s,r,t,i,n));case 8:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)})},{key:"preload",value:(r=h(d().mark((function e(t){var i,n,s,r,a,l,o,c=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.isMetaLoaded){e.next=3;break}return e.next=3,this.loadMeta(!0);case 3:if(t&&!(t<0)){e.next=5;break}return e.abrupt("return");case 5:if(i=this.getSegmentByTime(t),n=i.video,s=i.audio,r=Math.max((null==n?void 0:n.index)||0,(null==s?void 0:s.index)||0)){e.next=9;break}return e.abrupt("return");case 9:return a=this.videoSegments.slice(0,r),l=this.audioSegments.slice(0,r),o=function(){var e=h(d().mark((function e(t){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t>r)){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,c._loadSegment(a[t],l[t],!0,!1);case 4:return e.next=6,o(t+1);case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),e.next=14,o(0);case 14:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)})},{key:"cancel",value:function(){return this._loader.cancel()}},{key:"reset",value:(s=h(d().mark((function e(){var t=arguments;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.length>0&&void 0!==t[0]&&t[0]){e.next=5;break}return this.logger.debug("[MP4loader reset func call loader.cancel]"),e.next=5,this._loader.cancel();case 5:this.vid=this.url="",this.meta={},this.downloadInfo=[],this.videoSegments=[],this.audioSegments=[],this._currentSegmentIndex=-1,this._currentLoadingSegmentIndex=-1;case 12:case"end":return e.stop()}}),e,this)}))),function(){return s.apply(this,arguments)})},{key:"destroy",value:(n=h(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.reset();case 2:this.cache.clear();case 3:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"_loadSegment",value:(i=h(d().mark((function e(t,i,n,s,r){var a,l;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t||i){e.next=2;break}return e.abrupt("return");case 2:return a=(null==t?void 0:t.index)||(null==i?void 0:i.index)||0,this._currentLoadingSegmentIndex=a,e.prev=4,e.next=7,this.loadData([Math.min((null==t?void 0:t.range[0])||1/0,(null==i?void 0:i.range[0])||1/0),Math.max((null==t?void 0:t.range[1])||0,(null==i?void 0:i.range[1])||0)],n,r);case 7:l=e.sent;case 8:return e.prev=8,this._currentLoadingSegmentIndex=-1,e.finish(8);case 11:if(l){e.next=13;break}return e.abrupt("return");case 13:return s&&(this._currentSegmentIndex=a),l.video=t,l.audio=i,e.abrupt("return",l);case 17:case"end":return e.stop()}}),e,this,[[4,,8,11]])}))),function(e,t,n,s,r){return i.apply(this,arguments)})},{key:"loadData",value:(t=h(d().mark((function e(t,i,n){var s,r,a,l;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s=this._getCacheKey(t),e.next=3,this.cache.get(s);case 3:if(r=e.sent){e.next=11;break}return l=n&&n.url?n.url:this.url,e.next=8,this._loader.load(l,o({range:t,vid:this.vid},n));case 8:a=e.sent,e.next=12;break;case 11:a={data:r,state:!0,options:{fromCache:!0,range:t,vid:this.vid}};case 12:if(a){e.next=14;break}return e.abrupt("return");case 14:return r||(a.data&&this.downloadInfo.push({startTime:a.startTime,endTime:a.endTime,size:a.data.byteLength,range:t}),this.downloadInfo&&this.downloadInfo.length>this._config.maxDownloadInfoSize&&(this.downloadInfo=this.downloadInfo.slice(-this._config.maxDownloadInfoSize))),e.abrupt("return",a);case 17:case"end":return e.stop()}}),e,this)}))),function(e,i,n){return t.apply(this,arguments)})},{key:"_getCacheKey",value:function(e){return(this.vid||this.url)+":"+e}}]),L}(H),mn=function(){try{return"undefined"!=typeof localStorage&&"setItem"in window.localStorage&&!!window.localStorage.setItem}catch(e){return!1}}()&&!function(){var e="_localstorage_support_test";try{return window.localStorage.setItem(e,!0),window.localStorage.removeItem(e),!1}catch(t){return!0}}(),pn=bn();function bn(){if(mn)try{return!!window.localStorage.getItem("playerlog")}catch(e){return!1}return!1}function yn(e){for(var t,i=arguments.length,n=new Array(i>1?i-1:0),s=1;s<i;s++)n[s-1]=arguments[s];pn&&console.log&&(t=console).log.apply(t,["[logger]",ii.nowTime(),e].concat(n))}!function(){if(mn)try{return!!window.localStorage.getItem("playertestlog")}catch(e){return!1}}();var Zn="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",fn="undefined"!=typeof window&&window.Blob&&new Blob([atob(Zn)],{type:"text/javascript;charset=utf-8"});function Xn(){var e=fn&&(window.URL||window.webkitURL).createObjectURL(fn);try{return e?new Worker(e,{}):new Worker("data:application/javascript;base64,"+Zn,{type:"module"})}finally{e&&(window.URL||window.webkitURL).revokeObjectURL(e)}}var Ln=function(e){Z(i,e);var t=S(i);function i(e){var n;return m(this,i),(n=t.call(this)).openlog=e.openLog,n.codecType=e.codecType,n.supportHevc=e.supportHevc,n.worker=new Xn,n.worker.onmessage=function(e){n.emit(e.data.method,e.data)},n.worker.postMessage({method:"init",id:e.id||0,args:{openlog:n.openlog,supportHevc:n.supportHevc,codecType:n.codecType}}),n}return b(i,[{key:"transmux",value:function(e,t,i,n,s,r,a,l,o){var d=t.buffer;this.worker&&this.worker.postMessage({method:"transmux",id:e,buffer:d,args:{start:i,videoIdx:n,audioIdx:s,moov:r,useEME:a,kidValue:l,context:o}},[d])}},{key:"reset",value:function(){this.worker&&this.worker.postMessage({method:"reset"})}},{key:"destroy",value:function(){this.worker&&this.worker.terminate()}}]),i}(H),vn="error",Wn="metaReady",Gn="moov_request_Progress",xn="update_load_fragmentIdx",gn=function(e){Z(f,e);var t,i,n,s,r,a,l,c,u,p=S(f);function f(e,t){var i;if(m(this,f),y(x(i=p.call(this)),"onprogressDataArrive",function(){var e=h(d().mark((function e(t,n,s){var r;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t&&t.byteLength>0&&((r=i.timeRange[s.index].range)&&s.range[1]>=r[1]&&!n&&(n=!0),i.log("[mp4.loadFragment onprogressDataArrive ] receive data, >>> index,",s.index,",range,",JSON.stringify(s.range),", dataLen,",t.byteLength),i._mux(t,s.range[0],s.index,n)),n&&(i.timeRange[s.index].downloaded=!0,i.bufferLoadedPos=-1,i.log("[FragLoadDowned],fragmentIdx,",s.index,",rangeEnd,",s.range[1]));case 2:case"end":return e.stop()}}),e)})));return function(t,i,n){return e.apply(this,arguments)}}()),i.url=e,i.options=f.getDefaultConfig(),Object.keys(t).map((function(e){void 0!==t[e]&&null!==t[e]&&(i.options[e]=t[e])})),i.timeRange=[],i.CHUNK_SIZE=i.options.chunkSize,i.bufferLoaded=new Uint8Array(0),i.bufferLoadedPos=0,i.meta=null,i.videoTrak=null,i.audioTrak=null,i.canDownload=!0,i._loadSuccessCallBack=null,i._isPending=!1,i._metaLoading=!1,i.MP4Loader=new hn(o(o({segmentDuration:i.options.segmentDuration,url:e,vid:t.vid,retry:i.options.retryCount,retryDelay:i.options.retryDelay,timeout:i.options.timeout},t.reqOptions),{},{openLog:bn()})),i.MP4Demuxer=null,i.FMP4Remuxer=null,i._needInitSegment=!0,i._switchBitRate=!1,i.enableWorker=i.options.enableWorker,i.enableWorker&&"undefined"!=typeof Worker)try{i.workerSequence=0,i.transmuxerWorkerControl=new Ln({id:i.workerSequence,codecType:t.codecType,supportHevc:i.options.supportHevc,openLog:bn()}),i.transmuxerWorkerControl.on("transmux",(function(e){var t=e.args;i.log("[transmuxerworker end] ,range, ",JSON.stringify(t.range),",dataLen,",t.buffer.byteLength,t.context),i._loadSuccessCallBack&&i._loadSuccessCallBack(t)}))}catch(n){i.log("Error in worker:",n),i.enableWorker=!1,i.transmuxerWorkerControl=null}return i.enableWorker||(i.MP4Demuxer=null,i.FMP4Remuxer=null),i.seekTime=-1,i.changeBitRateTime=-1,i}return b(f,[{key:"changeBitRate",value:(u=h(d().mark((function e(t){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.url=t.url,e.next=3,this.MP4Loader.changeUrl(this.url,this.options.vid+Date.now(),this.CHUNK_SIZE);case 3:this._switchBitRate=!0,this.log("[switchBitrate] changeUrl, bitRate,",this.url);case 5:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"log",value:function(e){for(var t=this.options,i=t&&t.vid?"[MP4] ".concat(t.vid," ").concat(e):"[MP4] ".concat(e),n=arguments.length,s=new Array(n>1?n-1:0),r=1;r<n;r++)s[r-1]=arguments[r];yn.apply(void 0,[i].concat(s))}},{key:"errorHandler",value:(c=h(d().mark((function e(t,i){var n,s,r,a;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=t.response,s=t.message,r=this.options?this.options.vid:"",a=null,this._isPending=!0,n?(a=new ei("network",n.status,{httpText:n.httpText,message:s,url:n.url}),this.emit(vn,a)):(console.log("[MP4] [".concat(r,"] errorHandler,"),t),a=t,this.emit(vn,a));case 6:case"end":return e.stop()}}),e,this)}))),function(e,t){return c.apply(this,arguments)})},{key:"init",value:(l=h(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.url){e.next=3;break}return e.next=3,this.MP4Loader.changeUrl(this.url,this.options.vid+Date.now(),this.CHUNK_SIZE);case 3:return e.next=5,this.getMetaInfo();case 5:case"end":return e.stop()}}),e,this)}))),function(){return l.apply(this,arguments)})},{key:"getTimeRange",value:function(){for(var e=[],t=null,i=0;this.videoTrak&&i<this.videoTrak.length;i++)t={startTime:this.videoTrak[i].startTime,endTime:this.videoTrak[i].endTime,downloaded:!1,isLoading:!1},e.push(t);if(this.audioTrak&&this.audioTrak.length>e.length)for(var n=e.length;n<this.audioTrak.length;n++)t={startTime:Math.max(this.audioTrak[n].startTime,t?t.endTime:0),endTime:Math.max(this.audioTrak[n].endTime,t?t.endTime:0),downloaded:!1,isLoading:!1},e.push(t);return e}},{key:"getMetaInfo",value:(a=h(d().mark((function e(){var t,i,n,s=this,r=arguments;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=!(r.length>0&&void 0!==r[0])||r[0],e.prev=1,this._metaLoading=!0,this.log("getMetaInfo start"),this.bufferLoaded=new Uint8Array(0),i=0,n=function(){var e=h(d().mark((function e(n,r,a){var l;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:s.log("getMetaInfo onProgressHandle, dataLen,",n?n.byteLength:-1,", state,",r,",range,",JSON.stringify(a.range)),n&&a.range[0]===i&&(s.bufferLoaded=N(Uint8Array,s.bufferLoaded,new Uint8Array(n)),i+=n.byteLength),a.meta&&!s.meta&&(l=a.meta,s.videoTrak=l.videoSegments,s.audioTrak=l.audioSegments,s.timeRange=s.getTimeRange(),s.meta=o(o({},l.meta),{},{ext:{videoTrak:s.videoTrak,audioTrak:s.audioTrak}}),l.bufferLoaded=s.bufferLoaded,s.log("meta reach"),t&&s.emit(Wn,s.meta)),s.meta&&r&&(s.log("[getMetaInfo req end]"),s._metaLoading=!1),s.meta&&(n||r)&&(s.log("emit moov_req_progress"),s.emit(Gn));case 5:case"end":return e.stop()}}),e)})));return function(t,i,n){return e.apply(this,arguments)}}(),e.next=9,this.MP4Loader.loadMetaProcess(this.MP4Loader.cache,[0,this.CHUNK_SIZE],n);case 9:e.next=15;break;case 11:e.prev=11,e.t0=e.catch(1),console.error("[MP4] trigger errorHandler getMetaInfo",null===e.t0||void 0===e.t0?void 0:e.t0.message),this.loadError(e.t0,"getMetaInfo");case 15:case"end":return e.stop()}}),e,this,[[1,11]])}))),function(){return a.apply(this,arguments)})},{key:"getFragmentIdx",value:function(e){var t,i;if(this.videoTrak.length){if(t=this.videoTrak.find((function(t){return t.startTime<=e&&t.endTime>e})),i=this.audioTrak.find((function(t){return t.startTime<=e&&t.endTime>e})),t&&i)return Math.min(t.index,i.index);if(t||i)return t?t.index:i.index;var n=Number.MAX_VALUE;return this.videoTrak&&this.videoTrak.length>0&&(n=this.videoTrak.length-1),this.audioTrak&&this.audioTrak.length>0&&(n=Math.min(this.audioTrak.length-1,n)),n}return(i=this.audioTrak.find((function(t){return t.startTime<=e&&t.endTime>e})))?i.index:0}},{key:"_checkHasMeta",value:(r=h(d().mark((function e(){var t;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.log(" loadMeta start"),this._metaLoading=!0,e.next=4,this.MP4Loader.loadMeta(this.MP4Loader.cache,Math.round(this.CHUNK_SIZE/2));case 4:return t=e.sent,this._metaLoading=!1,this.videoTrak=t.videoSegments,this.audioTrak=t.audioSegments,this.meta=o(o({},t.meta),{},{ext:{videoTrak:this.videoTrak,audioTrak:this.audioTrak}}),this.timeRange=this.getTimeRange(),this.bufferLoaded=new Uint8Array(0),t.bufferLoaded=this.bufferLoaded,e.abrupt("return",!0);case 13:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"resetFragmentLoadState",value:function(e){for(var t=0;t<this.timeRange.length;t++)t<e?(this.timeRange[t].downloaded=!0,this.timeRange[t].isLoading=!0):(this.timeRange[t].downloaded=!1,this.timeRange[t].isLoading=!1)}},{key:"getFragRange",value:function(e){var t,i,n,s,r=null;this.videoTrak&&(r=e<this.videoTrak.length?this.videoTrak[e]:this.videoTrak[this.videoTrak.length-1]);var a=null;this.audioTrak&&(a=e<this.audioTrak.length?this.audioTrak[e]:this.audioTrak[this.audioTrak.length-1]);var l=0,o=0;this.videoTrak&&e>=this.videoTrak.length-1&&(l=-1),this.audioTrak&&e>=this.audioTrak.length-1&&(o=-1);var d=[Math.min((null===(t=r)||void 0===t?void 0:t.range[0])||1/0,(null===(i=a)||void 0===i?void 0:i.range[0])||1/0),Math.max((null===(n=r)||void 0===n?void 0:n.range[1])+l||0,(null===(s=a)||void 0===s?void 0:s.range[1])+o||0)];return e<this.timeRange.length&&(this.timeRange[e].range=d),d}},{key:"load",value:(s=h(d().mark((function e(t,i){var n,s,r;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._loadSuccessCallBack=i,!this._switchBitRate||this._metaLoading){e.next=12;break}return this.log("[switchBitrate], switch bitRate start load, time,",t>=this.videoTrak.length?this.audioTrak[t].startTime:this.videoTrak[t].startTime,", fragIndex,",t),e.next=5,this._checkHasMeta();case 5:n=e.sent,this.changeBitRateTime>0&&(this.timeRange[t].startTime<=this.changeBitRateTime&&this.changeBitRateTime<this.timeRange[t].endTime?this.log("[switchBitrate], not need update load fragIndex",t,",stTime,",this.changeBitRateTime):(t=this.getFragmentIdx(this.changeBitRateTime),this.log("[switchBitrate], need update load fragIndex",t,",stTime,",this.changeBitRateTime,",newBitrateTimeRange,",this.timeRange[t].startTime,"-",this.timeRange[t].endTime),this.emit(xn,t)),this.seekTime=this.changeBitRateTime,this.changeBitRateTime=-1),this.resetFragmentLoadState(t),this.log("[switchBitrate], reset timerange state,",t),this._needInitSegment=!0,this.resetTansmuxer(),n&&(this._switchBitRate=!1);case 12:if(!this._switchBitRate||!this._metaLoading){e.next=14;break}return e.abrupt("return");case 14:s=this.getFragRange(t),this.log("loadFragment,",t,",range,",JSON.stringify(s)),this.seekTime>0?(r=this.getSubRange(t,this.seekTime,s),this.loadFragment(t,r),this.seekTime=-1):this.loadFragment(t,s);case 17:case"end":return e.stop()}}),e,this)}))),function(e,t){return s.apply(this,arguments)})},{key:"getSubRange",value:function(e,t,i){var n=i[0],s=i[0],r=1,a=!1;if(this.log(">>>>>getSubRange time,",t,JSON.stringify(i)),this.videoTrak){var l=e<this.videoTrak.length?this.videoTrak[e]:this.videoTrak[this.videoTrak.length-1],o=l.frames.filter(Sn),d=this.meta.videoTimescale,c=o[0].startTime/d;this.log(">>>>>getSubRange video, startTime,",l.startTime,",endTime,",l.endTime);for(var u=0;u<o.length;u++)this.log(">>>>>getSubRange video keyFrameList, startTime,",o[u].startTime/d,",range,",o[u].offset);for(;r<o.length;r++){var h=o[r].startTime/d;if(c<=t&&t<h&&i[0]<o[r-1].offset){n=o[r-1].offset,a=!0,this.log(">>>>>getSubRange video end, startTime,",c,",endTime,",h,",startRange,",n,", keyFrameIndex,",r-1);break}c=h}!a&&c<=t&&t<l.endTime+.8&&(n=o[r-1].offset,this.log(">>>>>getSubRange video last, startTime,",c,",endTime,",l.endTime,",startRange,",n))}if(r=1,this.audioTrak){var m=e<this.audioTrak.length?this.audioTrak[e]:this.audioTrak[this.audioTrak.length-1],p=m.frames,b=this.meta.audioTimescale;r=Math.floor((t*b-p[0].startTime)/m.frames[0].duration);for(var y=(r=Math.min(p.length-1,r))>0?p[r-1].startTime/b:p[0].startTime/b;r>=0&&r<p.length;)if(r>0&&y>t)y=p[r-=1].startTime/b;else{var Z=(p[r].startTime+p[r].duration)/b;if(y<=t&&t<Z&&i[0]<p[r].offset){s=p[r].offset,a=!0,this.log(">>>>>getSubRange audio end, startTime,",y,",endTime,",Z,",startRange,",s,", index,",r);break}y=Z,r++}}var f=[Math.min(s,n),i[1]];return this.log(">>>>>getSubRange finalRange ",JSON.stringify(f),",oldRange,",JSON.stringify(i)),f}},{key:"_mux",value:function(e,t,i,n){var s=this.getSamplesRange(i,"video"),r=this.getSamplesRange(i,"audio"),a=[t,t+e.byteLength];if(this.transmuxerWorkerControl){var l={range:a,state:n,fragIndex:i};this.log("[transmuxerworker start] ,range, ",JSON.stringify(a),",dataLen,",e.byteLength,l),this.transmuxerWorkerControl.transmux(this.workerSequence,e,t,s,r,this.meta.moov,this.useEME,this.kidValue,l)}else try{this.MP4Demuxer||(this.MP4Demuxer=new Et(this.videoTrak,this.audioTrak,null,{openLog:bn()}));var o,d=this.MP4Demuxer.demuxPart(e,t,s,r,this.meta.moov,this.useEME,this.kidValue);this.FMP4Remuxer||this.checkCodecH265()&&!this.options.supportHevc||(this.FMP4Remuxer=new De(this.MP4Demuxer.videoTrack,this.MP4Demuxer.audioTrack,{openLog:bn()})),this.log("[mux], videoTimeRange,",d.videoTrack?[d.videoTrack.startPts,d.videoTrack.endPts]:null,",audioTimeRange,",d.audioTrack?[d.audioTrack.startPts,d.audioTrack.endPts]:null);var c=[Math.min(d.videoTrack.startPts,d.audioTrack.startPts),Math.max(d.videoTrack.endPts,d.audioTrack.endPts)];if(this.FMP4Remuxer){var u=this.FMP4Remuxer.remux(this._needInitSegment,{initMerge:!0,range:a});u.initSegment&&(this._needInitSegment=!1),o={buffer:ii.concatData(u.audioSegment,u.videoSegment),range:a,state:n,context:{range:a,fragIndex:i,timeRange:c},initSeg:u.initSegment}}else o={videoTrack:d.videoTrack,audioTrack:d.audioTrack,buffer:null,range:a,state:n,context:{range:a,fragIndex:i,timeRange:c}};this._loadSuccessCallBack&&this._loadSuccessCallBack(o)}catch(m){console.error("mux err:",m);var h=new ti($t.remux,Qt,{msg:JSON.stringify(m)});this.errorHandler(h,"mux",{fragIndex:i,range:[t,t+e.byteLength]})}}},{key:"getSamplesRange",value:function(e,t){var i=[];switch(t){case"video":if(this.videoTrak&&e<this.videoTrak.length){var n=this.videoTrak[e].frames;i.push(n[0].index),i.push(n[n.length-1].index)}break;case"audio":if(this.audioTrak&&e<this.audioTrak.length){var s=this.audioTrak[e].frames;i.push(s[0].index),i.push(s[s.length-1].index)}break;default:console.warn("[getSamplesRange] type ",t," is invalid")}return i}},{key:"loadFragment",value:(n=h(d().mark((function e(t,i){var n,s,r,a;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._isPending&&i!==[0,0]&&!this.timeRange[t].isLoading){e.next=2;break}return e.abrupt("return");case 2:if(this.log("[MP4.loadFragment] ,fragIndex,",t,",range ",i,",len ,",i[1]-i[0],", bufferLoaded_Len,",this.bufferLoaded.byteLength),!(i.length>=2&&i[1]&&i[1]>0&&i[1]<=this.bufferLoaded.byteLength)){e.next=13;break}this.timeRange[t].isLoading=!0,n=Math.max(i[0],this.bufferLoadedPos),s=new Uint8Array(this.bufferLoaded.slice(n,i[1])),this.log("[mp4.loadFragment] has all data: ",n,i[1]),this.timeRange[t].downloaded=!0,this.bufferLoadedPos=-1,this._mux(s,n,t,!0),e.next=34;break;case 13:if(!(i.length>=2&&i[0]&&i[0]<=this.bufferLoaded.byteLength)){e.next=29;break}if(this.timeRange[t].isLoading){e.next=27;break}if(r=Math.max(i[0],this.bufferLoadedPos),!((a=new Uint8Array(this.bufferLoaded.slice(r,i[1]))).byteLength>0)){e.next=22;break}return this.bufferLoadedPos=r+a.byteLength,this.log("[mp4.loadFragment] has part data: ",r,r+a.byteLength),this._mux(a,r,t,i[1]<=this.bufferLoadedPos),e.abrupt("return");case 22:if(this._metaLoading||this.timeRange[t].isLoading){e.next=27;break}return this.log("[mp4.loadFragment] ready to load part data >>> ",this.bufferLoaded.byteLength,i[1]),this.timeRange[t].isLoading=!0,e.next=27,this.startLoad([this.bufferLoaded.byteLength,i[1]],t);case 27:e.next=34;break;case 29:if(this._metaLoading&&!(i[0]>=this.CHUNK_SIZE)||this.timeRange[t].isLoading){e.next=34;break}return this.timeRange[t].isLoading=!0,this.log("[mp4.loadFragment],ready to load all data ,segmentIdx, ",t,",range >>> ",JSON.stringify(i)),e.next=34,this.startLoad(i,t);case 34:case"end":return e.stop()}}),e,this)}))),function(e,t){return n.apply(this,arguments)})},{key:"startLoad",value:(i=h(d().mark((function e(t,i){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,this.MP4Loader.loadData(t,this.MP4Loader.cache,{index:i,onProgress:this.onprogressDataArrive,onProcessMinLen:this.options.onProcessMinLen});case 3:e.next=9;break;case 5:e.prev=5,e.t0=e.catch(0),console.error("[MP4] trigger errorHandler getMetaInfo",null===e.t0||void 0===e.t0?void 0:e.t0.message),this.loadError(e.t0,"loadFragment",{range:t,fragIndex:i});case 9:case"end":return e.stop()}}),e,this,[[0,5]])}))),function(e,t){return i.apply(this,arguments)})},{key:"loadError",value:function(e,t,i){var n;!e.response&&(e.response={}),e.isTimeout?e.response.status="timeout":null!=e&&null!==(n=e.response)&&void 0!==n&&n.status||(e.response.status="networkError"),this.errorHandler(e,t,i)}},{key:"cancelLoading",value:(t=h(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.MP4Loader;case 2:if(e.t0=e.sent,!e.t0){e.next=5;break}this.MP4Loader.cancel();case 5:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"update",value:function(e){this.url=e}},{key:"checkCodecH265",value:function(){return this.meta&&(this.meta.videoCodec.indexOf("hvc1")>-1||this.meta.videoCodec.indexOf("hev1")>-1)}},{key:"destroy",value:function(){this.hasDestroyed||(this.resetTansmuxer(),this.transmuxerWorkerControl&&this.transmuxerWorkerControl.destroy(),this._isPending=!1,this._metaLoading=!1,this.bufferLoadedPos=0,this.bufferLoaded=new Uint8Array(0),this.MP4Loader&&(this.MP4Loader.cancel(),this.MP4Loader.destroy()),this.hasDestroyed=!0)}},{key:"resetTansmuxer",value:function(){this.MP4Demuxer&&this.MP4Demuxer.reset(),this.MP4Demuxer=null,this.FMP4Remuxer&&this.FMP4Remuxer.reset(),this.FMP4Remuxer=null,this.transmuxerWorkerControl&&this.transmuxerWorkerControl.reset()}}],[{key:"getDefaultConfig",value:function(){return{segmentDuration:5,onProcessMinLen:1024,chunkSize:8e5,retryCount:3,retryDelay:1e3,timeout:3e3,enableWorker:!1,playerId:"",vid:"",ext:{}}}}]),f}(H);function Sn(e){if(e.keyframe)return e}var Vn=function(){function e(t){m(this,e),this.onTick_=t,this.cancelPending_=null}return b(e,[{key:"tickAfter",value:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.stop();var n=!0,s=null;this.cancelPending_=function(){window.clearTimeout(s),n=!1};var r=function(){n&&(t.onTick_(),i&&i())};return s=window.setTimeout(r,1e3*e),this}},{key:"tickEvery",value:function(e){var t=this;this.tickAfter(e,(function(){t.tickEvery(e)}))}},{key:"stop",value:function(){this.cancelPending_&&(this.cancelPending_(),this.cancelPending_=null)}}]),e}(),kn=null,Yn=null,Tn=null,wn=function(e){Z(s,e);var t,i,n=S(s);function s(e){var t;return m(this,s),y(x(t=n.call(this,e)),"_onMp4DataCallBack",(function(){t._isMseInit&&t._onTimeUpdate()})),y(x(t),"_onMp4MetaReady",(function(e){var i=x(t).config,n=t.mp4.checkCodecH265();try{if(n&&!t.config.supportHevc){var s="browser not support HEVC",r=new a.Errors(t.player,{errorType:"runtime",errorTypeCode:$t.runtime,errorCode:Ot,errorMessage:s,vid:i.vid,mediaError:{code:Ot,message:s}});t._errorHandler(r)}else t._initMse(e)}catch(d){var l=new a.Errors(t.player,{errorType:"runtime",errorTypeCode:$t.runtime,errorCode:Bt,errorMessage:null==d?void 0:d.message,vid:i.vid,mediaError:{code:At,message:null==d?void 0:d.message}});t._errorHandler(l)}finally{var o=t.config.url;t.mse&&(o=t.mse.url,t._proxyPlayer()),t.player._startInit.call(t.player,o)}t._loadData()})),y(x(t),"_onMp4Error",(function(e){var i=t.playerConfig.vid;console.error("[Index] _onMp4Error",i,e),t._errorHandler(e)})),y(x(t),"_loadDataSuccess",(function(e){if(!t.isDestroy&&t.mse){t.log("[loadFragment] _loadDataSuccess ",JSON.stringify(e.context.range),",dataLen,",e.buffer?e.buffer.byteLength:0,e.state);try{e.initSeg&&(t._appendInitSeg(e.initSeg),(!e.buffer||e.buffer.byteLength<1)&&(t.log("no data, must load data"),t._onTimeUpdate()));var i=e.buffer,n=e.state,s=e.context;if(t.mse&&n&&(!i||i.byteLength<=0)&&s.fragIndex===t.mp4.timeRange.length-1){var r=t.player.buffered;r&&r.length>0&&(t.bufferEndTime=r.end(r.length-1)),t._isEnded(),t.log("loaded ended !!!==>>>",JSON.stringify(s.range),", fragIndex,",s.fragIndex,", bufferEndTime,",t.bufferEndTime,",meta_duration,",t.mp4.meta.duration)}if(t.mse&&n&&s.fragIndex===t.mp4.timeRange.length-1&&(!i||i.byteLength<=0)){var l=t.player.buffered;l&&l.length>0&&(t.bufferEndTime=l.end(l.length-1)),t._isEnded(),t.log("load ended !!!==>>>",t.playerConfig.vid,JSON.stringify(s.range),", fragIndex,",s.fragIndex,", bufferEndTime,",t.bufferEndTime,",meta_duration,",t.mp4.meta.duration)}i&&t.mse&&i&&i.byteLength>0&&t._appendBuffer(Ri.VIDEO,i,s,n)}catch(d){t.log("appendBuffer error",d);var o=new a.Errors(t.player,{errorType:"runtime",errorTypeCode:$t.runtime,errorCode:Bt,vid:t.player.config.vid,errorMessage:d.message,mediaError:{code:Bt,message:d.message}});t._errorHandler(o)}null!=e&&e.state&&t._onTimeUpdate()}})),y(x(t),"_onWaiting",(function(){var e=x(t),i=e.player,n=e.config;clearTimeout(t._waitInBufferTimer),t._waitInBufferTimer=null;var s=i.currentTime;console.log("[>>>>onWaiting],currentTime, ",s,ii.nowTime());var r=i.bufferedPoint;r.end>0&&r.end-i.currentTime>=2&&t._waitAdjustTimeCnt<n.waitJampBufferMaxCnt&&(t._waitInBufferTimer=setTimeout((function(){t._waitAdjustTimeCnt++,i.currentTime=i.currentTime+.5,t.log("[waitInBufferTimeout], waitAdjustTimeCnt,",t._waitAdjustTimeCnt,",curtime,",s,ii.nowTime())}),n.waitingInBufferTimeOut))})),y(x(t),"_onSeeking",h(d().mark((function e(){var i,n,s,r,a,l,o,c,u,h,m;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=x(t),n=i.player,s=i.mp4,r=n.currentTime,t.log("[seekTime], curTime,",r,",buffer,",n.buffered2.bufferedList),s&&s.meta){e.next=5;break}return e.abrupt("return");case 5:if(t.endofstream=!1,s.bufferLoadedPos=-1,s._metaLoading=!1,a=n.bufferedPoint,l=!1,o=0,!(a.end>0)){e.next=25;break}if(l=!0,!(s.meta.duration-a.end<.5)){e.next=17;break}return t._startProgress(),t.log("[seeking in buffered range], buffer end,",a.end,", duration,",s.meta.duration),e.abrupt("return");case 17:if(o=s.getFragmentIdx(a.end),t._curLoadSegmentIdx!==o){e.next=22;break}return t._startProgress(),t.log("[seeking in buffered range], seek fragIndex is current load segmentIdx",o),e.abrupt("return");case 22:s.seekTime=a.end,o<0&&(o=t._curLoadSegmentIdx),t.log("[seeking in buffered range], seekTime ",r,",bufferRange,",a.start,"-",a.end,", fragIndex,",o);case 25:return l||(s.seekTime=r,(o=s.getFragmentIdx(r))<0&&(o=t._curLoadSegmentIdx),t.log("[seekTime out buffer range], curTime,",r,", Idx,",o),c=x(t),(u=c.mse)&&u.isFull()&&(h=n.buffered2.bufferedList,m=h[h.length-1],u.clearOpQueues(Ri.VIDEO),t._checkRemoveSourceBuffer([m.start,m.end],n.currentTime,!0,!0))),e.next=28,s.cancelLoading();case 28:s.resetFragmentLoadState(o),t._curLoadSegmentIdx=o,t._onTimeUpdate(),t._startProgress(),t._isEnded();case 33:case"end":return e.stop()}}),e)})))),y(x(t),"changeDefineCanPlay",(function(e,i,n,s){var r=x(t).player;r.ended?r.currentTime=0:(t.log("[oldChangeDefinition],this._changeDefState,",t._changeDefState),r.currentTime=t._changeDefState?t._changeDefState.currentTime:e,(t._changeDefState?t._changeDefState.paused:i)?r.pause():r.play(),t._changeDefState=null,r.emit(a.Events.AFTER_DEFINITION_CHANGE,{from:n,to:s}))})),y(x(t),"changeDefinition",function(){var e=h(d().mark((function e(i,n){var s,r,l,o,c,u,h;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(s=x(t),r=s.player,l=s.config,o=s.mp4,n||(n=r.curDefinition),t._MSEError=!1,!l.witchBitRateWay){e.next=6;break}return t.oldChangeDefinition(i,n),e.abrupt("return");case 6:if(r.emit(a.Events.DEFINITION_CHANGE,{from:n,to:i}),c=r.currentTime,(u=o.getFragmentIdx(c))<0&&(u=t._curLoadSegmentIdx),t.log("switchBitrate:point,fragIndex,",u,",startTime,",o.timeRange[u].startTime,",currentTime,",r.currentTime),!o){e.next=16;break}return t.mp4.changeBitRateTime=c,e.next=15,o.cancelLoading();case 15:o._metaLoading&&(o._metaLoading=!1);case 16:return t._removeBuffeEndTime=o.timeRange[u].startTime,t._isChangeDefinition=!0,(h=r.getBufferedRange(r.buffered))[1]>0&&h[1]-r.currentTime>5&&(t.mse.clearOpQueues(Ri.VIDEO),t.mse.remove(Ri.VIDEO,r.currentTime+5,h[1])),t.log("switchBitrate: resetFragmentLoadState,",u),o.resetFragmentLoadState(u),t._curLoadSegmentIdx=u,e.next=25,t.mp4.changeBitRate(i);case 25:t._onTimeUpdate(),r.emit("RESOLUTION_UPDATE",i);case 27:case"end":return e.stop()}}),e)})));return function(t,i){return e.apply(this,arguments)}}()),y(x(t),"_replayHook",(function(){var e;return null===(e=t.player)||void 0===e||e.play(),!1})),y(x(t),"_retryHook",(function(){return t.beforePlayerInit(),!1})),t.mp4=null,t.mse=null,t._waitAdjustTimeCnt=0,t._lastCheckTime=ii.nowTime(),t._removeBuffeEndTime=0,t}return b(s,[{key:"afterCreate",value:function(){var e=this;window.__mp4player=this;try{a.BasePlugin.defineGetterOrSetter(this.player,{__url:{get:function(){try{return e.mse?e.mse.url:e.config.url}catch(t){return null}}}})}catch(t){}}},{key:"beforePlayerInit",value:function(){var e=this.config;void 0===e.supportHevc&&null!==a.Sniffer&&void 0!==a.Sniffer&&a.Sniffer.isHevcSupported&&a.Sniffer.isHevcSupported()&&(e.supportHevc=!0),this.initMp4(),this.attachEvents(),this._startProgress()}},{key:"attachEvents",value:function(){this.off(a.Events.SEEKING,this._onSeeking),this.on(a.Events.SEEKING,this._onSeeking),this.on(a.Events.WAITING,this._onWaiting),this.off(a.Events.URL_CHANGE,this.switchURL),this.on(a.Events.URL_CHANGE,this.switchURL)}},{key:"detachEvents",value:function(){this.off(a.Events.SEEKING,this._onSeeking),this.off(a.Events.WAITING,this._onWaiting),this.off(a.Events.URL_CHANGE,this.switchURL)}},{key:"initMp4",value:function(){var e=this,t=this.player;t.config.vid||(t.config.vid=Date.now()),this.mp4&&(this.mp4.off(Wn,this._onMp4MetaReady),this.mp4.off(vn,this._onMp4Error),this.mp4.off(Gn,this._onMp4DataCallBack),this.mp4.destroy()),this.mp4=new gn(t.config.url,o(o({},this.config),{},{vid:t.config.vid})),this.mp4.on(Wn,this._onMp4MetaReady),this.mp4.on(vn,this._onMp4Error),this.mp4.on(Gn,this._onMp4DataCallBack),this.mp4.on(xn,(function(t){e._curLoadSegmentIdx=t,e.log("[update curLoadSegmentIdx]",t)})),this.mp4.init()}},{key:"_proxyPlayer",value:function(){var e=this;"function"==typeof this.player.playNext&&(kn=this.player.playNext),this.player.playNext=function(){e.playNext.apply(e,arguments)},Tn=this.player.switchURL,Yn=this.player.changeDefinition,this.player.switchURL=this.switchURL.bind(this),this.player.changeDefinition=this.changeDefinition.bind(this),this.player.removeHooks("replay",this._replayHook),this.player.removeHooks("retry",this._retryHook)}},{key:"setConfig",value:function(e){this.config=Object.assign(this.config,e)}},{key:"playNext",value:function(e){var t=this.player;this._defInited=!1,t.resetState(),t._currentTime=0,t._duration=0,t.isPlaying=!1,this._MSEError=!1,t.pause(),this._reset(),t.setConfig(e),this.log("[Index] playNext",e),t.play(),this.emit("playnext")}},{key:"_errorHandler",value:function(e){var t=this.player,i=this.config;t&&(console.log("final error !!!!, ",i.vid,e),this.emit("error",e))}},{key:"_initMse",value:(i=h(d().mark((function e(t){var i,n,s,r,a,l=this;return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.mse){e.next=4;break}return e.next=3,this.mse.unbindMedia();case 3:this.mse=null;case 4:i=this.mp4&&this.mp4.checkCodecH265(),n=!!t.videoCodec,s=!!t.audioCodec,r=n&&s?i?'video/mp4; codecs="hev1.1.6.L93.B0, mp4a.40.5"':'video/mp4; codecs="avc1.64001E, mp4a.40.5"':n?i?'video/mp4; codecs="hev1.1.6.L93.B0"':'video/mp4; codecs="avc1.64001E"':'video/mp4; codecs="mp4a.40.5"',a=y({},Ri.VIDEO,{mimeType:"video/mp4",codec:r}),this.mse=new Ri,this.mse.bindMedia(this.player.video).then((function(){var e=Object.keys(a);try{for(var t=0;t<e.length;t++){var i=e[t];l.mse.createSource(i,a[i].codec)}}catch(n){console.error("MSE error: ",n),l._errorHandler(n)}l._isMseInit=!0,l._onTimeUpdate()}));case 12:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)})},{key:"_onTimeUpdate",value:function(){var e=this,t=this.mse,i=this.mp4,n=this.player,s=this.config;if(i){var r=i.timeRange,a=n.getBufferedRange(n.buffered2);if(t&&i&&i.canDownload){ii.nowTime()-this._lastCheckTime>1e3&&(this._lastCheckTime=ii.nowTime(),this._loadStuckCheck(),this._checkRemoveSourceBuffer(a,n.currentTime));var l=n.paused?n.currentTime+s.minBufferLength:n.currentTime+s.maxBufferLength;a[1]-l<0&&r.every((function(t,i){return!!t.downloaded||(!e._isChangeDefinition&&t.endTime-t.startTime>1&&e._isInBuffer(t)?(t.downloaded=!0,t.isLoading=!0,e.log("onTimeUpdate, ".concat(i," download segment, has buffer"),t.startTime,t.endTime),!0):void(t.startTime-n.currentTime<s.maxBufferLength&&(e._curLoadSegmentIdx=i,e.log("[onTimeUpdate],load index==>>>, ",i,",IdxTimeRange, ",t.startTime,"-",t.endTime,",buffEnd, ",a[1],",playCurTime,",n.currentTime,", bufferLen,",a[1]-n.currentTime,",bufferRangeList,",e.player.buffered2?e.player.buffered2.bufferedList:null),e._loadData())))})),this._isEnded()}this.checkRemoveOldBitrateBuffer()}}},{key:"checkRemoveOldBitrateBuffer",value:function(){var e=this.mse,t=this.player;e&&this._removeBuffeEndTime>0&&t.currentTime>this._removeBuffeEndTime+1&&(this.log("remove old bitrate buffer",this._removeBuffeEndTime),e.remove(Ri.VIDEO,0,this._removeBuffeEndTime-1),this._removeBuffeEndTime=0)}},{key:"_isInBuffer",value:function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=!1,n=this.player.video.buffered,s=0;s<n.length;s++){var r=n.start(s)-t,a=n.end(s)+t;if(r<=e.startTime&&e.endTime<=a){i=!0;break}}return i}},{key:"_loadData",value:(t=h(d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mp4&&this._isMseInit){e.next=3;break}return this.log("loadData, player.mp4 null",this._isMseInit),e.abrupt("return");case 3:return e.prev=3,e.next=6,this.mp4.load(this._curLoadSegmentIdx,this._loadDataSuccess);case 6:e.next=11;break;case 8:e.prev=8,e.t0=e.catch(3),console.error("[Index] _loadData error",this.playerConfig.vid,e.t0);case 11:case"end":return e.stop()}}),e,this,[[3,8]])}))),function(){return t.apply(this,arguments)})},{key:"_appendInitSeg",value:function(e){var t=this;this.mp4&&this.mse&&this.mse.append(Ri.VIDEO,e,{vid:this.playerConfig.vid,range:null,dataLen:e.byteLength,isinit:!0}).then((function(e){t.log("appendInitSeg end ==>>>",e.context?e.context:null,", costTime,",e.costtime)}))}},{key:"_appendBuffer",value:function(e,t){var i=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0,r=this.mse,a=this.config;r.append(e,t,{vid:a.vid,fragIndex:n.fragIndex,range:n.range,dataLen:t.byteLength,state:s}).then((function(e){if(i.log("player appendBuffer end ==>>>",e.context?e.context:null,", costTime,",e.costtime,", opt,",e.name,",bufferRange,",i.player.getBufferedRange()),i.mse&&n.state&&n.fragIndex===i.mp4.timeRange.length-1){var t=i.player.buffered;t&&t.length>0&&(i.bufferEndTime=t.end(t.length-1)),i._isEnded(),i.log("loaded ended !!!==>>>",n.range,", fragIndex,",n.fragIndex,", bufferEndTime,",i.bufferEndTime,",meta_duration,",i.mp4.meta.duration)}})).catch((function(e){if(console.log("[MSE error]",e),e&&null!=r&&r.isFull()){var t=i.player.getBufferedRange(i.player.buffered2);i._checkRemoveSourceBuffer(t,i.player.currentTime,!0)}}))}},{key:"_checkRemoveSourceBuffer",value:function(e,t,i){var n=this,s=this.mse,r=this.mp4,a=this.player;if(s&&r&&a&&(i&&(clearTimeout(this._removeBufferTimer),this._removeBufferTimer=null),e||(e=a.getBufferedRange(a.buffered2)),t||(t=a.currentTime),!(!i&&ii.nowTime()-this._checkRemoveBufferLastTime<=this.config.removeBufferLen||this.endofstream)&&(this._checkRemoveBufferLastTime=ii.nowTime(),e&&e[0]>=0&&(t-e[0]>this.config.removeBufferLen||s.isFull())))){var l=e[1],o=r.getFragmentIdx(l);if(o>=0&&r.timeRange[o].startTime<t){var d=Math.floor(Math.min(r.timeRange[o].startTime,e[1]));e[0]<d?(this.log("[checkremoveSourceBuffer], remove range==>>>",e[0],d),s.remove(Ri.VIDEO,e[0],d)):s.isFull()&&!this._removeBufferTimer&&(this._removeBufferTimer=setTimeout((function(){n._checkRemoveSourceBuffer(null,null,!0)}),1e4))}}}},{key:"_isEnded",value:function(){var e=this.player,t=this.mp4,i=e.bufferedPoint,n=i?i.end:0;return!this.endofstream&&this.mse&&t.meta.duration-e.currentTime<.5&&(this.log("[check player isEnded],deal mse.endOfStream, currentTime,",e.currentTime,", bufferend,",n,", duration,",t.meta.duration),this.endofstream=!0,this.mse.endOfStream()),!!(t&&t.meta&&t.meta.duration-e.currentTime<.5)&&(this._stopProgress(),this.log("[check player isEnded],stopProgress and endOfStream,currentTime, ",e.currentTime,", bufferend,",n,", duration,",t.meta.duration),this.mse&&this.mse.endOfStream(),!0)}},{key:"switchURL",value:function(e){this.changeDefinition(e)}},{key:"oldChangeDefinition",value:function(e,t){var i=this,n=this.config,s=this.player;this.log("[oldChangeDefinition],currentTime,",s.currentTime,",from,",t,",to,",e);var r=s.currentTime,a=s.paused;this._changeDefState||(this._changeDefState={currentTime:r,paused:a},this.log("[oldChangeDefinition],currentTime,",s.currentTime,",pause,",a)),s.config.url=e.url,n.focusUserDefinition=!0,s.currentTime=0,s.pause(),this._reset(),this._isMseInit=!1,this._changeDefineCanPlay&&s.off("canplay",this._changeDefineCanPlay),this._changeDefineCanPlay=function(){i.changeDefineCanPlay(r,a,t,e),i._changeDefineCanPlay=null},s.once("canplay",this._changeDefineCanPlay),this.player.video.load(),this.initMp4()}},{key:"_loadStuckCheck",value:function(){var e=this,t=this.config,i=this.player;t.disableBufferBreakCheck||(i.currentTime-(this._lastCurrentTime||0)>.1||i.paused?1!==this._bufferBreakFlag&&2!==this._bufferBreakFlag||(this.log("视频没有卡死,重置卡死标记"),this._bufferBreakFlag=0,clearTimeout(this._bufferBreakTimer),this._bufferBreakFlag=null):this._bufferBreakFlag||(this._bufferBreakFlag=1,this.log("卡死计时开始! 持续".concat(t.waitingTimeOut,"毫秒则确认卡死")),this._bufferBreakTimer=setTimeout((function(){e.isDestroy||(1===e._bufferBreakFlag&&(e._bufferBreakFlag=2,e.log("确认卡死!!!"),e._errorHandler(new a.Errors(e.player,{errorType:"runtime",errorTypeCode:$t.runtime,errorCode:qt,errorMessage:"wait_timeout",vid:t.vid}))),e._bufferBreakTimer=null)}),t.waitingTimeOut)),this._lastCurrentTime=i.currentTime)}},{key:"isDestroy",get:function(){return!this.player}},{key:"_stopProgress",value:function(){this._hasStartProgress=!1,this._requestTimer&&(this._requestTimer.stop(),this._requestTimer=null),this._bufferBreakTimer&&(clearTimeout(this._bufferBreakTimer),this._bufferBreakTimer=null,this._bufferBreakFlag=void 0)}},{key:"_startProgress",value:function(){var e=this;this._hasStartProgress||(this._stopProgress(),this._requestTimer=new Vn((function(){e._requestTimer&&e._onTimeUpdate()})),this._requestTimer.tickEvery(this.config.tickInSeconds),this._hasStartProgress=!0)}},{key:"log",value:function(e){for(var t=this.playerConfig,i=t&&t.vid?"[Index]".concat(t.vid," ").concat(e):"[Index] ".concat(e),n=arguments.length,s=new Array(n>1?n-1:0),r=1;r<n;r++)s[r-1]=arguments[r];yn.apply(void 0,[i].concat(s))}},{key:"_reset",value:function(){this._isMseInit=!1,this.endofstream=!1,this._curLoadSegmentIdx=0,this._removeBuffeEndTime=0,this._isChangeDefinition=!1,this._stopProgress(),this.mp4&&(this.mp4.off(Wn,this._onMp4MetaReady),this.mp4.off(vn,this._onMp4Error),this.mp4.off(Gn,this._onMp4DataCallBack),this.mp4.destroy(),this.mp4=null),this.mse&&(this.mse.unbindMedia(),this.mse=null),this._unloadVideo()}},{key:"_unloadVideo",value:function(){var e=this.player;try{this.log("unloadVideo src ".concat(e.video.src)),e.video&&e.video.src&&(e.video.removeAttribute("src"),e.video.load())}catch(t){this.log("unloadVideo error",t)}}},{key:"destroy",value:function(){var e=this.player;e.removeHooks("replay",this._replayHook),e.removeHooks("retry",this._retryHook),this.detachEvents(),this._reset(),this.player.playNext=kn,this.player._startInit=null,this.player.changeDefinition=Yn,this.player.switchURL=Tn,this._bufferBreakTimer&&clearInterval(this._bufferBreakTimer),this._removeBufferTimer&&(clearTimeout(this._removeBufferTimer),this._removeBufferTimer=null),this._waitInBufferTimer&&(clearTimeout(this._waitInBufferTimer),this._waitInBufferTimer=null)}}],[{key:"pluginName",get:function(){return"mp4Plugin"}},{key:"defaultConfig",get:function(){return{maxBufferLength:40,minBufferLength:5,disableBufferBreakCheck:!1,waitingTimeOut:15e3,waitingInBufferTimeOut:5e3,waitJampBufferMaxCnt:3,tickInSeconds:.1,reqOptions:null}}},{key:"version",get:function(){return"3.0.8"}}]),s}(a.BasePlugin);return wn}));
//# sourceMappingURL=index.min.js.map
