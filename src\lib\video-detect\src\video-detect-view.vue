<!-- 视频监测播放器 -->
<script lang="ts" setup>
import flvjs from 'flv.js';

defineOptions({ name: 'VideoDetectView' });

const props = withDefaults(
  defineProps<{
    source?: flvjs.MediaDataSource;
    config?: flvjs.Config;
    controls?: boolean;
    autoplay?: boolean;
  }>(),
  {
    autoplay: true,
  },
);

const playerRef = shallowRef<HTMLMediaElement>();

const player = shallowRef<flvjs.Player>();
watch([playerRef, () => props.source], ([playerRef, source]) => {
  player.value?.destroy();
  if (!playerRef)
    return;

  if (source) {
    player.value = flvjs.createPlayer(source, props.config);
    player.value.attachMediaElement(playerRef!);
    if (props.autoplay) {
      try {
        player.value.load();
        player.value.play();
      }
      catch (error) {}
    }
  }
});

onUnmounted(() => player.value?.destroy());
</script>

<template>
  <video
    ref="playerRef"
    class="video-detect-view"
    controls
    :autoplay="autoplay"
    disablepictureinpicture
  />
</template>
