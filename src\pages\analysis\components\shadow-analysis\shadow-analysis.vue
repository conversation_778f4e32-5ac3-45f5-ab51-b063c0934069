<!-- 阴影分析 -->
<script lang="ts" setup>
import { useCzViewer } from '@x3d/vue-hooks';
import dayjs from 'dayjs';

defineOptions({ name: 'ShadowAnalysis' });
const viewer = useCzViewer();
const date = ref(dayjs().format('YYYY-MM-DD 06:00:00'));
const startUp = ref<boolean>(true);
const hour = ref(6);
const duration = ref(0);
let calcTime = 0;
const { pause, resume, isActive } = useIntervalFn(() => {
  calcTime += (duration.value + 1) * 1000 * 60;
  // 改变时间设置光照效果
  const timestamp = dayjs(date.value).valueOf() + calcTime;
  const datetime = new Date(timestamp);
  const utc = Cesium.JulianDate.fromDate(datetime);
  hour.value = datetime.getHours();
  if (hour.value === 18) {
    calcTime -= 3600000 * 12;
  }
  // 北京时间
  viewer.value.clockViewModel.currentTime = Cesium.JulianDate.addHours(
    utc,
    0,
    new Cesium.JulianDate(),
  );
}, 100);
watch(hour, (cur, his) => {
  if (!isActive.value) {
    calcTime += (cur - his) * 3600000;
    const timestamp = dayjs(date.value).valueOf() + calcTime;
    const datetime = new Date(timestamp);
    const utc = Cesium.JulianDate.fromDate(datetime);
    // 北京时间
    viewer.value.clockViewModel.currentTime = Cesium.JulianDate.addHours(
      utc,
      0,
      new Cesium.JulianDate(),
    );
  }
});
function openShadowAnalysis() {
  viewer.value.scene.globe.enableLighting = true;
  viewer.value.shadows = true;
  viewer.value.terrainShadows = Cesium.ShadowMode.RECEIVE_ONLY;
  viewer.value.shadowMap.darkness = 0.5; // 阴影透明度--越大越透明
  resume();
}
function closeShadowAnalysis() {
  viewer.value.scene.globe.enableLighting = false;
  viewer.value.shadows = false;
  viewer.value.terrainShadows = Cesium.ShadowMode.DISABLED;
  pause();
}
watchImmediate(startUp, (bool) => {
  bool ? openShadowAnalysis() : closeShadowAnalysis();
});
onUnmounted(() => {
  closeShadowAnalysis();
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="阴影分析"
    class="h-400px w-400px"
  >
    <el-form class="shadow-analysis mt-24px h-full pr-40px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="开启阴影" :label-width="$vh(80)">
            <el-switch v-model="startUp" :active-value="true" :inactive-value="false" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-button
            v-if="startUp"
            type="primary"
            class="h-25px!"
            @click="isActive ? pause() : resume(), (hour = 6)"
          >
            {{ isActive ? "暂停" : "播放" }}
          </el-button>
        </el-col>
      </el-row>
      <el-form-item label="日期" :label-width="$vh(80)">
        <el-date-picker
          v-model="date"
          value-format="YYYY-MM-DD 06:00:00"
          type="date"
          placeholder="选择日期"
        />
      </el-form-item>
      <el-form-item label="速率" :label-width="$vh(80)">
        <el-slider
          v-model="duration"
          :min="0"
          :max="59"
          :step="10"
          :marks="{ 0: '10min/s', 30: '5h/s', 59: '10h/s' }"
        />
      </el-form-item>
      <el-form-item label="时间" :label-width="$vh(80)" mt="40px">
        <el-slider
          v-model="hour"
          :min="6"
          :max="18"
          :step="1"
          :marks="{ 6: '6时', 9: '9时', 12: '12时', 15: '15时', 18: '18时' }"
        />
      </el-form-item>
    </el-form>
  </drag-card>
</template>
