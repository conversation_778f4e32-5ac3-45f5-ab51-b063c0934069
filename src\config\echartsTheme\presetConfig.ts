import Color from 'color';
import * as echarts from 'echarts';

import theme from './theme.json';

// 渐变色
function linearGradient(color: string, x = 0, y = 0, x2 = 0, y2 = 1) {
  const _color = Color(color);
  return new echarts.graphic.LinearGradient(x, y, x2, y2, [
    {
      offset: 0,
      color: _color.alpha(1).toString(),
    },
    {
      offset: 0.9,
      color: _color.alpha(0).toString(),
    },
  ]);
}

/** 折线图 series 预设 */
export const LINE_SERIES_OPTIONS: echarts.LineSeriesOption[] = theme.color.map((color) => {
  return {
    areaStyle: {
      color: linearGradient(color),
    },
  };
});

/** 柱状图 series 预设 */
export const BAR_SERIES_OPTIONS: echarts.BarSeriesOption[] = theme.color.map((color) => {
  const _color = Color(color);
  return {
    barMaxWidth: 20,
    itemStyle: {
      borderRadius: 5,
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: _color.toString(),
        },
        {
          offset: 1,
          color: _color
            .red(_color.red() / 3)
            .green(_color.green() / 3)
            .blue(_color.blue() / 2)
            .lighten(0.5)
            .toString(),
        },
      ]),
    },
  };
});
