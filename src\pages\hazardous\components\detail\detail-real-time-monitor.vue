<!-- 实时监测模块 -->
<script lang="ts" setup>
import type {
  CompanyEquipDTO,
  WeiXianYuanXinXiDTO,
} from '@/genapi/production';
import {
  productionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost,
  productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet,
} from '@/genapi/production';
import { computedLoading } from '@/hooks/computed-loading';
import { HAZARD_RANK_ENUM } from '../../enum';

import EquipmentIndicator from './equipment-indicator.vue';

;

defineOptions({ name: 'DetailRealTimeMonitor' });

const props = defineProps<{
  companyCode: string;
}>();

// 危险源列表
const [sources] = computedLoading(async () => {
  if (!props.companyCode)
    return;
  const { data } = await productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet({
    path: { companyCode: props.companyCode! },
  });
  return data;
});

// 危险源按危险源等级进行分类 key:等级  value: WeiXianYuanXinXiDTO[]
const sourcesCats = computed(() => {
  const record: Record<string, WeiXianYuanXinXiDTO[]> = {};
  sources.value?.forEach((source) => {
    if (source.hazardRank) {
      record[source.hazardRank] ??= [];
      record[source.hazardRank].push(source);
    }
  });
  return record;
});

/** 通过危险源编码获取设备列表 */
async function getEquip(hazardCode: string) {
  const { data } = await productionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost({
    path: { hazardCode },
  });
  return data;
}

// 设备列表
const [equips] = computedLoading(async () => {
  if (!sources.value?.length)
    return;
  const promises = sources.value.map(({ hazardCode }) => getEquip(hazardCode!));
  const datas = await Promise.all(promises);
  return datas?.flat();
});

const equipTypeMap = {
  G0: '储罐',
  P0: '装置',
  Q0: '可燃/有毒气体',
  K0: '仓库',
};

  type HazardCode = string;
  type EquipType = string;
  // 设备根据所属危险源进行分类
const equipsCats = computed(() => {
  const record: Record<HazardCode, Record<EquipType, CompanyEquipDTO[]>> = {};
  equips.value?.forEach((equip) => {
    if (equip?.hazardCode && equip.equipType) {
      record[equip.hazardCode] ??= {};
      record[equip.hazardCode][equip.equipType] ??= [];
      record[equip.hazardCode][equip.equipType].push(equip);
    }
  });
  return record;
});

const levelActive = ref<string[]>();
watchEffect(() => {
  levelActive.value = [Object.keys(sourcesCats.value)?.[0]];
});
</script>

<template>
  <el-scrollbar class="detail-real-time-monitor" wrap-class="px-20px">
    <el-collapse v-model="levelActive">
      <el-collapse-item
        v-for="level in Object.keys(sourcesCats)"
        :key="level"
        class="level-collapse-item"
        :title="
          `${HAZARD_RANK_ENUM[level]}${+level !== 9 ? '重大危险源' : ''}`
            + ` (${sourcesCats[level].length}个)`
        "
        :name="level"
      >
        <el-collapse>
          <el-collapse-item
            v-for="source in sourcesCats[level]"
            :key="source.hazardCode"
            class="source-collapse-item"
            :name="source.hazardCode"
          >
            <template #title>
              <div class="source-header">
                <span class="name">{{ source.hazardName }}</span>
                <span class="item">
                  储罐数：{{ equipsCats[source.hazardCode!]?.G0?.length ?? 0 }}
                </span>
                <span class="item">
                  装置数：{{ equipsCats[source.hazardCode!]?.P0?.length ?? 0 }}
                </span>
                <span class="item">
                  气体检测点：{{ equipsCats[source.hazardCode!]?.Q0?.length ?? 0 }}
                </span>
                <span class="item">
                  仓库数：{{ equipsCats[source.hazardCode!]?.K0?.length ?? 0 }}
                </span>
                <span class="item">R值:{{ source.rvalue }}</span>
              </div>
            </template>
            <template v-for="(name, equipType,) in equipTypeMap" :key="equipType">
              <div v-if="equipsCats[source.hazardCode!]?.[equipType]?.length" class="">
                <header-title2 class="pb-10px pt-15px">
                  {{ name }}
                </header-title2>
                <div class="flex flex-wrap">
                  <div
                    v-for="item in equipsCats[source.hazardCode!]?.[equipType]"
                    :key="item.equipCode"
                    class="w-33.3%"
                  >
                    <EquipmentIndicator :equip="item" />
                  </div>
                </div>
              </div>
            </template>
          </el-collapse-item>
        </el-collapse>
      </el-collapse-item>
    </el-collapse>
  </el-scrollbar>
</template>

<style scoped lang="scss">
  .detail-real-time-monitor {
  height: 100%;
}

.el-collapse {
  --el-collapse-header-font-size: 18px;
}

.el-collapse,
.el-collapse-item,
:deep().el-collapse-item__wrap,
:deep().el-collapse-item__header {
  border: 0;
}

.level-collapse-item {
  margin-bottom: 2px;

  :deep() > div > .el-collapse-item__header {
    height: 36px;
    padding-right: 6px;
    padding-left: 32px;
    margin-bottom: 2px;
    font-size: 16px;
    font-weight: bold;
    background: #08357d;

    &::before {
      position: absolute;
      left: 16px;
      width: 2px;
      height: 16px;
      content: '';
      background: #fff;
    }
  }
}

.source-collapse-item {
  margin-bottom: 2px;

  :deep() > div > .el-collapse-item__header {
    height: 36px;
    padding-right: 6px;
    padding-left: 32px;
    margin-bottom: 2px;
    font-size: 15px;
    font-weight: bold;
    background: rgb(#292b2e, 30%);
  }
}

.source-header {
  display: flex;
  width: 100%;

  .name {
    min-width: 170px;
  }

  .item {
    min-width: 110px;
    padding-right: 20px;
  }
}
</style>
