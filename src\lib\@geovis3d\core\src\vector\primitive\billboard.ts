import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.Billboard} 拓展用法与 {@link Cesium.Billboard} 基本一致。
 *
 * `GcBillboard.event`鼠标事件监听
 */
export class GcBillboard extends Cesium.Billboard {
  constructor() {
    super();
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
