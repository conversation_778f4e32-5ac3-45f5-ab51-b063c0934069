import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.CzmlDataSource} 拓展用法与 {@link Cesium.CzmlDataSource} 基本一致。
 *
 * `GcCzmlDataSource.event`鼠标事件监听
 */
export class GcCzmlDataSource extends Cesium.CzmlDataSource {
  constructor(name?: string) {
    super(name);

    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
