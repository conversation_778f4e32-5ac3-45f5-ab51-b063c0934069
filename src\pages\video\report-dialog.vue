<!-- 报告弹窗 -->
<script lang="ts" setup>
import statePNG from './assets/state.png';
import { DEMO_LIST } from './demoList';

export interface ReportDialogProps {
  modelValue?: boolean;
}

export interface ReportDialogEmits {
  (event: 'update:modelValue', data?: boolean): void;
}

defineOptions({ name: 'ReportDialog' });
const props = defineProps<ReportDialogProps>();
const emit = defineEmits<ReportDialogEmits>();
const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-dialog v-model="model" title="报告" append-to-body>
    <el-scrollbar h="560px!">
      <header-title1>火险等级排名及报告</header-title1>
      <div flex="~ justify-center">
        <el-table :data="DEMO_LIST" w="90%!">
          <el-table-column label="乡镇名称" prop="name" />
          <el-table-column label="火险指数" prop="index" />
          <el-table-column label="火险等级" prop="level" />
          <el-table-column label="火险气象条件" prop="weather" />
        </el-table>
      </div>
      <header-title1>火线等级态势图</header-title1>
      <div flex="~ justify-center">
        <img :src="statePNG" w="90%!">
      </div>
      <header-title1>预警实时信息</header-title1>
      <el-empty description="暂无预警" />
    </el-scrollbar>
  </el-dialog>
</template>
