import type { GcEntity } from './entity';

import * as Cesium from 'cesium';

import { GcParticleSystem } from '../primitive/particle-system';

import { createEntityEffect } from './entity';

/**
 * ParticleGraphics构造参数
 *
 * 见:
 * {@link Cesium.ParticleSystem}
 */
export interface ParticleGraphicsConstructorOptions {
  show?: Cesium.Property | boolean;
  emitter?: Cesium.Property | Cesium.ParticleEmitter;
  modelMatrix?: Cesium.Property | Cesium.Matrix4;
  emitterModelMatrix?: Cesium.Property | Cesium.Matrix4;
  emissionRate?: Cesium.Property | number;
  bursts?: Cesium.Property | Cesium.ParticleBurst[];
  loop?: Cesium.Property | boolean;
  scale?: Cesium.Property | number;
  startScale?: Cesium.Property | number;
  endScale?: Cesium.Property | number;
  color?: Cesium.Property | Cesium.Color;
  startColor?: Cesium.Property | Cesium.Color;
  endColor?: Cesium.Property | Cesium.Color;
  image?: Cesium.Property | string | HTMLCanvasElement;
  imageSize?: Cesium.Property | Cesium.Cartesian2;
  minimumImageSize?: Cesium.Property | Cesium.Cartesian2;
  maximumImageSize?: Cesium.Property | Cesium.Cartesian2;
  sizeInMeters?: Cesium.Property | boolean;
  speed?: Cesium.Property | number;
  minimumSpeed?: Cesium.Property | number;
  maximumSpeed?: Cesium.Property | number;
  lifetime?: Cesium.Property | number;
  particleLife?: Cesium.Property | number;
  minimumParticleLife?: Cesium.Property | number;
  maximumParticleLife?: Cesium.Property | number;
  mass?: Cesium.Property | number;
  minimumMass?: Cesium.Property | number;
  maximumMass?: Cesium.Property | number;
  updateCallback?: (p: Cesium.Particle, dt: number) => void;
}

const propertyNames: (keyof ParticleGraphicsConstructorOptions)[] = [
  'show',
  'emitter',
  'modelMatrix',
  'emitterModelMatrix',
  'emissionRate',
  'bursts',
  'loop',
  'scale',
  'startScale',
  'endScale',
  'color',
  'startColor',
  'endColor',
  'image',
  'imageSize',
  'minimumImageSize',
  'maximumImageSize',
  'sizeInMeters',
  'speed',
  'minimumSpeed',
  'maximumSpeed',
  'lifetime',
  'particleLife',
  'minimumParticleLife',
  'maximumParticleLife',
  'mass',
  'minimumMass',
  'maximumMass',
  'updateCallback',
];

/**
 * 将粒子系统`ParticleSystem`与`Entity`结合
 *
 * 优势在于不需要进行一大堆entity矩阵转换对粒子系统进行定位(内部已实现)，只需要直接将想要的粒子参数输入即可。
 * 与 {@link Cesium.ParticleSystem} 初始化几乎一致。
 *
 * 值得注意的是：
 * {@link Cesium.ParticleSystem.updateCallback} 被 {@link ParticleGraphics.updateEvent} 代替
 */
export class ParticleGraphics {
  constructor(options: ParticleGraphicsConstructorOptions) {
    this._system = new GcParticleSystem({
      updateCallback: (particle, dt) => {
        this._updateEvent.raiseEvent(this, particle, dt);
      },
    });

    propertyNames.forEach((key) => {
      const privateKey = `_${key}`;
      const item: any = options?.[key];
      if (item !== undefined) {
        const property = item.getValue ? item : new Cesium.ConstantProperty(options[key]);
        this[privateKey] = property;
        this._system[key] = property.getValue();
      }
      // get set回调
      Object.defineProperty(this, key, {
        get: () => this[privateKey],
        set: (property) => {
          if (this[privateKey]?.getValue() !== property?.getValue()) {
            const prev = this[privateKey];
            this[privateKey] = property;
            this.definitionChanged.raiseEvent(this, key, property, prev);
            property.definitionChanged.addEventListener((_: any, value: any) => {
              this._system[key] = value;
            });
          }
        },
      });
    });

    this._definitionChanged.addEventListener((_graphics, key, value) => {
      this._system[key] = value?.getValue();
    });
  }

  /**
   * @internal
   * 承载的粒子系统
   */
  readonly _system: GcParticleSystem;

  /**
   * @internal
   */
  private readonly _definitionChanged = new Cesium.Event();

  /**
   * 属性发生变化时触发
   */
  get definitionChanged(): Cesium.Event<
    (self: ParticleGraphics, field: string, value: any, prevValue: any) => void
  > {
    return this._definitionChanged;
  }

  /**
   * @internal
   */
  private readonly _updateEvent = new Cesium.Event();

  /**
   * 用于在每个时间步长修改粒子属性的函数。这可以包括力修改， 颜色、大小等。
   * 见 {@link Cesium.ParticleSystem.updateCallback }
   *
   * @example
   * function applyGravity(particle, dt) {
   *    const position = particle.position;
   *    const gravityVector = Cesium.Cartesian3.normalize(position, new Cesium.Cartesian3());
   *    Cesium.Cartesian3.multiplyByScalar(gravityVector, GRAVITATIONAL_CONSTANT * dt, gravityVector);
   *    particle.velocity = Cesium.Cartesian3.add(particle.velocity, gravityVector, particle.velocity);
   * }
   *
   * graphic.updateCallbackEvent.addEventListener((self, particle, dt)=>applyGravity(particle，dt))
   *
   * @param particle - 正在更新的粒子。
   * @param dt - 自上次更新以来的时间(秒)。
   */
  get updateEvent(): Cesium.Event<
    (self: ParticleGraphics, particle: Cesium.Particle, dt: number) => void
  > {
    return this._updateEvent;
  }

  show?: Cesium.Property;

  emitter?: Cesium.Property;

  modelMatrix?: Cesium.Property;

  emitterModelMatrix?: Cesium.Property;

  emissionRate?: Cesium.Property;

  bursts?: Cesium.Property;

  loop?: Cesium.Property;

  scale?: Cesium.Property;

  startScale?: Cesium.Property;

  endScale?: Cesium.Property;

  color?: Cesium.Property;

  startColor?: Cesium.Property;

  endColor?: Cesium.Property;

  image?: Cesium.Property;

  imageSize?: Cesium.Property;

  minimumImageSize?: Cesium.Property;

  maximumImageSize?: Cesium.Property;

  sizeInMeters?: Cesium.Property;

  speed?: Cesium.Property;

  minimumSpeed?: Cesium.Property;

  maximumSpeed?: Cesium.Property;

  lifetime?: Cesium.Property;

  particleLife?: Cesium.Property;

  minimumParticleLife?: Cesium.Property;

  maximumParticleLife?: Cesium.Property;

  mass?: Cesium.Property;

  minimumMass?: Cesium.Property;

  maximumMass?: Cesium.Property;
}

/**
 * 将`ParticleGraphics`添加到entity中
 * @param graphics
 * @param entity
 */
export function entityParticleGraphicsEffect(entity: GcEntity) {
  createEntityEffect(entity, (onCleanup) => {
    if (!entity.particle) {
      return;
    }
    const scene = entity.scene;
    if (!scene) {
      return;
    }
    const system: GcParticleSystem = scene.primitives.add(entity.particle._system);
    // 粒子点击时，触发entity点击
    system.event?.on?.('LEFT_CLICK', ({ context }) => {
      entity.event.emit('LEFT_CLICK', [{ context, pick: { id: entity } }]);
    });

    let hpr = new Cesium.HeadingPitchRoll();
    const trs = new Cesium.TranslationRotationScale();
    // 粒子与entity.position的绑定
    const preUpdateDestroy = scene.preUpdate.addEventListener((_, time) => {
      const modelMatrix = entity.computeModelMatrix(time, new Cesium.Matrix4());
      modelMatrix && (system.modelMatrix = modelMatrix);
      hpr = Cesium.HeadingPitchRoll.fromDegrees(0, 0, 0, hpr);
      trs.translation = Cesium.Cartesian3.fromElements(0, 0, 0, trs.translation);
      trs.rotation = Cesium.Quaternion.fromHeadingPitchRoll(hpr, trs.rotation);
      system.emitterModelMatrix = Cesium.Matrix4.fromTranslationRotationScale(
        trs,
        system.emitterModelMatrix,
      );
    });
    onCleanup(() => {
      preUpdateDestroy?.();
      scene.primitives?.remove(system);
    });
  });
}
