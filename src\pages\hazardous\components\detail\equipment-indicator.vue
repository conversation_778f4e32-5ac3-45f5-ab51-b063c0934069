<!-- 设备指标小组件 -->
<script lang="ts" setup>
import type {
} from '@/genapi/production';
import {
  productionHazardousChemicalsGetTankEquipByCodeEquipCodeUsingPost,
  productionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet,
} from '@/genapi/production';

defineOptions({ name: 'EquipmentIndicator' });
const props = defineProps<{
  equip: any;
}>();

const tabIndex = ref(0);

const images = import.meta.glob('../../assets/equipment/*.png', { eager: true, query: '?url' });

// 设备指标列表
const data = computedAsync(async () => {
  if (!props?.equip?.equipCode)
    return;
  const { data } = await productionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet({
    path: { equipCode: props.equip?.equipCode ?? '' },
  });
  return data;
});

const detail = computedAsync(async () => {
  if (!props?.equip?.equipCode)
    return;
  const { data } = await productionHazardousChemicalsGetTankEquipByCodeEquipCodeUsingPost({
    path: { equipCode: props.equip?.equipCode ?? '' },
  });
  return data;
});

function getImage() {
  const keyword = props.equip.equipType === 'Q0'
    ? data.value?.[0]?.targetType
    : props.equip.equipType === 'G0'
      ? `G${props.equip.tankType}`
      : props.equip.equipType;
  const key = Object.keys(images).find(item => item.includes(keyword));
  if (!key) {
    return;
  }
  return (images as any)[key]?.default;
}
</script>

<template>
  <div class="equipment-indicator">
    <div class="tabs">
      <div class="tab-item" :class="{ active: tabIndex === 0 }" @click="tabIndex = 0">
        设备概览
      </div>
      <div v-if="detail" class="tab-item" :class="{ active: tabIndex === 1 }" @click="tabIndex = 1">
        详细信息
      </div>
    </div>

    <!-- 概览 -->
    <div v-if="tabIndex === 0" class="overview">
      <div class="overview-left">
        <img
          :src="getImage()"
          alt=""
        >
        <span>{{ equip.equipName }}</span>
        <span>({{ equip.equipDescribe }})</span>
      </div>
      <el-scrollbar class="overview-right">
        <el-popover
          v-for="target in data"
          :key="target.targetType"
          placement="top-start"
          :width="$vh(400)"
          trigger="hover"
        >
          <template #reference>
            <div class="target-button">
              <span>{{ target.targetName }}:</span>
              <span>{{ target?.targetRealInfo?.lastValue }}{{ target.unit }}</span>
            </div>
          </template>
          <el-descriptions size="small" :column="2" border>
            <el-descriptions-item label="实时值">
              {{ target.targetRealInfo?.lastValue }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="量程">
              {{ target?.rangeDown ?? '--' }}~{{ target?.rangeUp ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="上上限">
              {{ target.thresholdUp ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="上限">
              {{ target.thresholdUp2 ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="下限">
              {{ target.thresholdDown2 ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="下下限">
              {{ target.thresholdDown ?? '--' }}{{ target.unit }}
            </el-descriptions-item>

            <el-descriptions-item label="采集时间">
              {{ target.targetRealInfo?.lastCollectTime }}
            </el-descriptions-item>
            <el-descriptions-item label="最后报警时间">
              {{ target.targetRealInfo?.lastAlarmStarted }}
            </el-descriptions-item>
          </el-descriptions>
        </el-popover>
      </el-scrollbar>
    </div>
    <!-- 详细信息 -->
    <el-scrollbar v-else class="flex-1">
      <el-descriptions :column="2" size="small" border>
        <el-descriptions-item label="罐类型">
          {{ detail?.tankType ?? '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="罐的容积">
          {{ detail?.reserves ?? '--' }}{{ detail?.mediumStatus === '液态' ? '吨' : 'm³' }}
        </el-descriptions-item>
        <el-descriptions-item label="压力类型">
          {{ detail?.pressureType ?? '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="设计压力">
          {{ detail?.pressureDesing ?? '--' }}MPa
        </el-descriptions-item>
        <el-descriptions-item label="最高工作压力">
          {{ detail?.pressureMax ?? '--' }}MPa
        </el-descriptions-item>
        <el-descriptions-item label="温度类型">
          {{ detail?.temperatureType ?? '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="设计温度高限">
          {{ detail?.tempratureMax ?? '--' }}℃
        </el-descriptions-item>
        <el-descriptions-item label="设计温度低限">
          {{ detail?.tempratureMin ?? '--' }}℃
        </el-descriptions-item>
        <el-descriptions-item label="存储介质">
          {{ detail?.medium ?? '--' }}
        </el-descriptions-item>
        <el-descriptions-item label="介质形态">
          {{ detail?.mediumStatus ?? '--' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-scrollbar>
  </div>
</template>

<style scoped lang="scss">
  .equipment-indicator {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: calc(100% - 12px);
  height: 194px;
  margin-right: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  background: rgb(26 53 131 / 60%);
  border-radius: 6px;
  box-shadow: inset 0 1px 20px 0 rgb(18 142 232 / 35%);

  .tabs {
    display: flex;
    height: 28px;

    > .tab-item {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-color: #021b54;
      border-radius: 6px 6px 0 0;

      &.active {
        background-color: transparent;
      }
    }
  }

  .overview {
    display: flex;
    flex: 1;
    padding: 12px;

    .overview-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 160px;

      img {
        width: 100px;
        height: 100px;
      }
    }

    .overview-right {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: stretch;

      .target-button {
        display: flex;
        align-items: center;
        padding: 6px 16px;
        margin-bottom: 10px;
        line-height: 1;
        background: #163d7b;
        border: 1px solid rgb(55 93 168 / 100%);
        border-radius: 1px;
      }
    }
  }
}
</style>
