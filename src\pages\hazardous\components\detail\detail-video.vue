<!-- 视频监控 -->
<script lang="ts" setup>
import type { VideoTreeNode } from '@/lib/video-detect';

import type FlvJs from 'flv.js';
import { computedLoading } from '@/hooks/computed-loading';
import { DEVideoDetect, VideoDetectView } from '@/lib/video-detect';

defineOptions({ name: 'DetailVideo' });
const props = defineProps<{
  companyName: string;
}>();
const detect = new DEVideoDetect(`${import.meta.env.VITE_VISUAL_SYSTEM_PATH}/wpCamera/hikvisionWp/forward`);

async function recursion(params?: VideoTreeNode<Record<string, any>>): Promise<VideoTreeNode<Record<string, any>>[]> {
  if (params?.isLeaf) {
    return [];
  }
  const nodes = await detect.getNodes(params);
  const res = await Promise.all(nodes.map(e => recursion(e)));
  return [...nodes, ...res].flat();
}

// 当前企业的channel列表
const [channels] = computedLoading(async () => {
  const res = await recursion();
  const company = res.find(e => e.name.includes(props?.companyName));
  if (company) {
    return await detect.getNodes(company);
  }
});

watch(channels, (channels) => {
  if (channels?.length) {
    checks.value = [channels?.[0]?.code];
  }
});
const checks = ref<string[]>([]);

  type Videos = Record<string, FlvJs.MediaDataSource & { name: string }>;

const videos = reactive<Videos>({});

watch(checks, async (checks) => {
  Object.keys(videos).forEach((key) => {
    if (!checks.includes(key)) {
      delete videos[key];
    }
  });
  checks.forEach(async (code) => {
    if (!Object.keys(videos).includes(code)) {
      const data = channels.value?.find(e => e.code == code)!;
      const instance = detect.getInstance(data!);
      const rtsp = await instance.realTimeRTSP();
      const options = [
        'param=-an',
        'param=-c:v',
        'param=copy',
        'param=-f',
        'param=flv',
        // 'param=-buffer_size',
        // 'param=102400',
        'ffmpeg=true',
      ].join('&&&');

      const ssl = window.location.protocol !== 'http:';
      const protocol = ssl ? 'wss://' : 'ws://';
      const url = `${protocol}${window.location.host}${import.meta.env.VITE_VISUAL_SYSTEM_LIVE_PATH}?url=${rtsp}&&&${options}`;
      videos[code] = {
        url,
        type: 'flv',
        name: data.name,
      };
    }
  });
});

// 计算栅格 style
const style = computed(() => {
  const length = Object.keys(videos)?.length ?? 0;
  let style: any = null;
  let index = 2;
  while (!style) {
    if (length <= index * index) {
      style = {
        'grid-template-rows': new Array(index)
          .fill('')
          .map(() => `${100 / index}%`)
          .join(' '),
        'grid-template-columns': new Array(index)
          .fill('')
          .map(() => `${100 / index}%`)
          .join(' '),
      };
      break;
    }
    else {
      index++;
    }
  }
  return style;
});
</script>

<template>
  <div v-if="channels?.length" class="detail-video" wrap-class="px-20px">
    <el-scrollbar class="w-200px">
      <el-checkbox-group v-model="checks">
        <el-checkbox
          v-for="item in channels"
          :key="item.code"
          :label="item.code"
          :disabled="!item.onLine"
        >
          {{ item.name }}
        </el-checkbox>
      </el-checkbox-group>
    </el-scrollbar>

    <div class="video-wrap" :style="style">
      <div v-for="code in Object.keys(videos)" :key="code" class="video-box">
        <VideoDetectView :source="videos[code]" class="video" />
        <span class="name">{{ videos[code].name }}</span>
      </div>
    </div>
  </div>
  <div v-else class="h-100% flex items-center justify-center">
    <el-empty description="该企业暂无数据" />
  </div>
</template>

<style scoped lang="scss">
  .detail-video {
  display: flex;
  height: 100%;
  overflow: hidden;

  .el-checkbox-group {
    display: flex;
    flex-direction: column;

    .el-checkbox {
      padding: 20px 10px;
      margin-right: 0;

      &.is-checked {
        color: #fff !important;
        background: rgb(#292b2e, 30%);
      }
    }
  }

  .video-wrap {
    display: grid;
    flex: 1;
    overflow: hidden;
    background: rgb(#292b2e, 30%);
  }

  .video-box {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    overflow: hidden;
    background: rgb(1 4 10 / 60%);

    .video {
      flex: 1;
      align-items: stretch;
      padding: 5px;
      overflow: hidden;
      object-fit: cover;
    }

    .name {
      position: absolute;
      bottom: 5px;
      left: 10px;
      height: 30px;
    }
  }
}
</style>
