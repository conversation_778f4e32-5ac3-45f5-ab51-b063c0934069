<!-- BoundingRectangleAttribute -->
<script lang="ts" setup>
import type { BoundingRectangleSerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'BoundingRectangleAttribute' });

const props = defineProps<{
  modelValue?: BoundingRectangleSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: BoundingRectangleSerializateJSON): void;
}>();

const model = ref<BoundingRectangleSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div>
    <el-form-item :label="label" />
    <NumberAttribute v-model="model.x" :min="0" label="X" />
    <NumberAttribute v-model="model.y" :min="0" label="Y" />
    <NumberAttribute v-model="model.width" :min="0" label="宽" />
    <NumberAttribute v-model="model.height" :min="0" label="高" />
  </div>
</template>
