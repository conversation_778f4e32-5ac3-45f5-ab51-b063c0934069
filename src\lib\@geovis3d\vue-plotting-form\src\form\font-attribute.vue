<!-- FontAttribute -->
<script lang="ts" setup>
import type { FontSerializateJSON } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

import StringAttribute from './string-attribute.vue';

defineOptions({ name: 'FontAttribute' });

const props = defineProps<{
  modelValue?: FontSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: FontSerializateJSON): void;
}>();

const model = ref<FontSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <NumberAttribute v-model="model.size" label="size" />
  <StringAttribute v-model="model.family" label="family" />
  <BooleanAttribute v-model="model.bold" label="bold" />
  <BooleanAttribute v-model="model.italic" label="italic" />
</template>
