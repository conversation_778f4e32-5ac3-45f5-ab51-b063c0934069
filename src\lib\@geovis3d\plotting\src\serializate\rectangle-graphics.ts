import type { ColorSerializateJSON } from './color';

import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type {
  ClassificationTypeSerializateJSON,
  HeightReferenceSerializateJSON,
  ShadowModeSerializateJSON,
} from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import type { RectangleSerializateJSON } from './rectangle';
import * as Cesium from 'cesium';
import { ColorSerializate } from './color';

import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { RectangleSerializate } from './rectangle';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface RectangleGraphicsSerializateJSON {
  show?: boolean;
  coordinates?: RectangleSerializateJSON;
  height?: number;
  heightReference?: HeightReferenceSerializateJSON;
  extrudedHeight?: number;
  extrudedHeightReference?: HeightReferenceSerializateJSON;
  rotation?: number;
  stRotation?: number;
  granularity?: number;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  classificationType?: ClassificationTypeSerializateJSON;
  zIndex?: number;
}

export type RectangleGraphicsKey = keyof RectangleGraphicsSerializateJSON;

export class RectangleGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.RectangleGraphics,
    omit?: RectangleGraphicsKey[],
    time?: Cesium.JulianDate,
  ): RectangleGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      coordinates: RectangleSerializate.toJSON(getValue('coordinates')),
      height: getValue('height'),
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      extrudedHeight: getValue('extrudedHeight'),
      extrudedHeightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('extrudedHeightReference'))
        ?? 'NONE',
      rotation: getValue('rotation'),
      stRotation: getValue('stRotation') ?? 0,
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType:
        EnumSerializate.toJSON(Cesium.ClassificationType, getValue('classificationType')) ?? 'BOTH',
      zIndex: getValue('zIndex'),
    };
  }

  static fromJSON(
    json?: RectangleGraphicsSerializateJSON,
    omit?: RectangleGraphicsKey[],
  ): Cesium.RectangleGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.RectangleGraphics({
      show: getValue('show') ?? true,
      coordinates: RectangleSerializate.fromJSON(getValue('coordinates')),
      height: getValue('height'),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      extrudedHeight: getValue('extrudedHeight'),
      extrudedHeightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('extrudedHeightReference'),
      ),
      rotation: getValue('rotation'),
      stRotation: getValue('stRotation') ?? 0,
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType: EnumSerializate.fromJSON(
        Cesium.ClassificationType,
        getValue('classificationType'),
      ),
      zIndex: getValue('zIndex'),
    });
  }
}
