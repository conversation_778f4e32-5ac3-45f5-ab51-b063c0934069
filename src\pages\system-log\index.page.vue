<script lang="ts" setup>
import { Back } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import Details from './details.vue';

defineOptions({ name: 'SystemLogIndexPage' });
definePage({
  meta: {
    title: '系统日志',
    sort: 7,
    hidden: true,
  },
});

const currentTab = ref('details');
provide('currentTab', currentTab);
const router = useRouter();
function back() {
  router.push('/');
}
</script>

<template>
  <div class="system-log-container">
    <div class="system-log-header">
      <div @click="back">
        <el-icon size="20">
          <Back />
        </el-icon>
        <span>返回主页</span>
      </div>
    </div>
    <div class="system-log-main">
      <Details />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.system-log-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100vw;
  height: 100vh;
  pointer-events: all;
  background: #1c1d1e;

  .system-log-header {
    display: flex;
    align-items: center;
    height: 64px;
    padding-left: 40px;
    font-size: 24px;
    background: var(--el-bg-color); // #292b2e;

    > div {
      display: flex;
      align-items: center;
      height: 100%;
      cursor: pointer;
      transition: all 0.3s;

      > span {
        margin: 0 15px;
      }

      &:hover {
        color: #4584ff;
      }
    }
  }

  .system-log-main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 64px);
  }
}
</style>
