<script lang="ts" setup>
import type { LegendConfig } from '../assets/types';

export interface LayerFormLegendProps {
  modelValue?: LegendConfig;
}

export interface LayerFormLegendEmits {
  (event: 'update:modelValue', data?: LegendConfig): void;
}

defineOptions({ name: 'LayerFormLegend' });
const props = defineProps<LayerFormLegendProps>();
const emit = defineEmits<LayerFormLegendEmits>();

const model = useVModel(
  props,
  'modelValue',
  emit,
  {
    deep: true,
    defaultValue: { list: [] },
    clone: true,
    passive: true,
  },
);

function addLegend() {
  model.value!.list = [
    {
      symbol: Cesium.createGuid(),
    },
    ...model.value!.list,
  ];
}

async function removeLegend(data: any) {
  await ElMessageBox.confirm('确定移除该图例？', '提示');
  const index = model.value!.list.findIndex((e: any) => e.symbol === data.symbol);
  if (index !== -1) {
    model.value!.list.splice(index, 1);
  }
}
</script>

<template>
  <header-title2 my="16px" content="图例">
    <template #extra>
      <div flex="~">
        <el-switch v-model="model!.visible" inactive-text="显示" mr="20px" />
        <el-button size="small" @click="addLegend()">
          新增图例
        </el-button>
      </div>
    </template>
  </header-title2>
  <div class="min-h-60px">
    <div v-for="item in model!.list" :key="item.symbol" flex="~ items-center justify-between" class="my-5px ml-35px mr-20px" un-children="my-5px!">
      <el-input
        v-model="item.name"
        w="120px!"
        placeholder="请输入"
      />
      <el-color-picker v-model="item.color" un-children="size-38px!" />
      <div flex="1" />
      <el-button text @click="removeLegend(item)">
        <el-icon class="i-custom:delete" />
      </el-button>
    </div>
  </div>
</template>
