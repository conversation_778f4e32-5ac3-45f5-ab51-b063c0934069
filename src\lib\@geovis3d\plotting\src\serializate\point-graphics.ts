import type { ColorSerializateJSON } from './color';

import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type { HeightReferenceSerializateJSON } from './enum';
import type { NearFarScalarSerializateJSON } from './near-far-scalar';
import * as Cesium from 'cesium';
import { ColorSerializate } from './color';

import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { NearFarScalarSerializate } from './near-far-scalar';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PointGraphicsSerializateJSON {
  show?: boolean;
  pixelSize?: number;
  heightReference?: HeightReferenceSerializateJSON;
  color?: ColorSerializateJSON;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  scaleByDistance?: NearFarScalarSerializateJSON;
  translucencyByDistance?: NearFarScalarSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  disableDepthTestDistance?: number;
}

export type PointGraphicsKey = keyof PointGraphicsSerializateJSON;

export class PointGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.PointGraphics,
    omit?: PointGraphicsKey[],
    time?: Cesium.JulianDate,
  ): PointGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      pixelSize: getValue('pixelSize'),
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      color: ColorSerializate.toJSON(getValue('color')),
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      scaleByDistance: NearFarScalarSerializate.toJSON(getValue('scaleByDistance')),
      translucencyByDistance: NearFarScalarSerializate.toJSON(getValue('translucencyByDistance')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      disableDepthTestDistance: getValue('disableDepthTestDistance'),
    };
  }

  static fromJSON(
    json?: PointGraphicsSerializateJSON,
    omit?: PointGraphicsKey[],
  ): Cesium.PointGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.PointGraphics({
      show: getValue('show') ?? true,
      pixelSize: getValue('pixelSize'),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      color: ColorSerializate.fromJSON(getValue('color')),
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      scaleByDistance: NearFarScalarSerializate.fromJSON(getValue('scaleByDistance')),
      translucencyByDistance: NearFarScalarSerializate.fromJSON(getValue('translucencyByDistance')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      disableDepthTestDistance: getValue('disableDepthTestDistance'),
    });
  }
}
