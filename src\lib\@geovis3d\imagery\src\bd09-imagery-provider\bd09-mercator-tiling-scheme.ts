import { bd09ToWgs84, cartographicToBd09 } from '@/lib/@geovis3d/coordinate';
import * as Cesium from 'cesium';

/**
 * BD09坐标系影像图层
 */
export class Bd09MercatorTilingScheme extends Cesium.WebMercatorTilingScheme {
  constructor(options?: {
    ellipsoid?: Cesium.Ellipsoid;
    numberOfLevelZeroTilesX?: number;
    numberOfLevelZeroTilesY?: number;
    rectangleSouthwestInMeters?: Cesium.Cartesian2;
    rectangleNortheastInMeters?: Cesium.Cartesian2;
  }) {
    super(options);
    const projection = new Cesium.WebMercatorProjection();
    this.projection.project = function (cartographic, result) {
      const bd09 = cartographicToBd09(cartographic) as [number, number, number];
      const cg = Cesium.Cartographic.fromDegrees(...bd09);
      result = projection.project(cg);
      return result;
    };
    this.projection.unproject = function (cartesian, result) {
      const cartographic = projection.unproject(cartesian);
      const wgs84 = bd09ToWgs84([
        Cesium.Math.toDegrees(cartographic.longitude),
        Cesium.Math.toDegrees(cartographic.latitude),
      ]);
      result = new Cesium.Cartographic(
        Cesium.Math.toRadians(wgs84[0]),
        Cesium.Math.toRadians(wgs84[1]),
      );
      return result;
    };
  }
}
