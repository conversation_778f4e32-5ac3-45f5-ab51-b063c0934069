import type { InjectionKey } from 'vue';

interface CompanyActive {
  /** 选中的企业 */
  detailCompanyCode?: string;

  /** 二维的企业详情 */
  twoCompanyCode?: string;

  /** 三维的企业详情 */
  threeCompanyCode?: string;
}

const COMPANY_ACTIVE_KEY: InjectionKey<CompanyActive> = Symbol();

/**
 * 危化品企业交互相关
 */
export function useCompanyActiveProvide() {
  const option = reactive<CompanyActive>({});

  // 2d/3d互斥
  watch(
    () => option.twoCompanyCode,
    code => code && (option.threeCompanyCode = undefined),
  );
  watch(
    () => option.threeCompanyCode,
    code => code && (option.twoCompanyCode = undefined),
  );
  // 2/3d选中时 详情关闭
  watch(
    () => option.twoCompanyCode || option.threeCompanyCode,
    code => code && (option.detailCompanyCode = undefined),
  );

  provide(COMPANY_ACTIVE_KEY, option);
  return option;
}

/**
 * 危化品企业交互相关
 */
export function useCompanyActiveInject() {
  return inject(COMPANY_ACTIVE_KEY)!;
}
