import dayjs from 'dayjs';

export const useTimelinePlayer = createGlobalState(() => {
  const duration = ref(1000);

  const length = ref(24);

  /**
   * 当前选择的日期
   */
  const date = shallowRef(new Date());

  /**
   * 当前选择的小时数
   */
  const selectHour = ref(dayjs().get('hour'));

  /**
   * 当前选择的时间
   */
  const selectTime = shallowRef(new Date());

  /**
   * 将当前小时数减一
   */
  function prev() {
    if (selectHour.value > 0) {
      selectHour.value--;
    }
  }

  /**
   * 将当前小时数加一
   */
  function next() {
    selectHour.value++;
  }

  const { pause: pausePlay, resume, isActive: isPlaying } = useIntervalFn(() => {
    next();
    if (selectHour.value === length.value - 1) {
      pausePlay();
    }
  }, duration, {
    immediate: false,
  });

  function resumePlay() {
    if (selectHour.value === length.value - 1) {
      selectHour.value = 0;
    }
    resume();
  }

  /**
   * 重置成当天的当前小时
   */
  const reset = () => {
    pausePlay();
    date.value = dayjs(dayjs().format('YYYY-MM-DD 00:00:00')).toDate();
    selectHour.value = dayjs().get('hour');
    selectTime.value = dayjs(dayjs().format('YYYY-MM-DD HH:00:00')).toDate();
  };
  reset();

  return {
    duration,
    length,
    date,
    selectHour,
    reset,
    prev,
    next,
    isPlaying: readonly(isPlaying),
    resumePlay,
    pausePlay,
  };
});
