<!-- ScreenAttribute -->
<script lang="ts" setup>
import type { ScreenSerializateJSON } from '@/lib/@geovis3d/plotting';
import { useShallowBinding } from './hooks';

defineOptions({ name: 'ScreenAttribute' });

const props = defineProps<{
  modelValue?: ScreenSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: ScreenSerializateJSON): void;
}>();

const model = ref<ScreenSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div class="screen-attribute">
    ScreenAttribute
  </div>
</template>
