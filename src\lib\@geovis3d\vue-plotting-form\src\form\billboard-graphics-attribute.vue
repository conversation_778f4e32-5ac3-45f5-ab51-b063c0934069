<!-- BillboardGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { BillboardGraphicsKey, BillboardGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { BillboardGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import BoundingRectangleAttribute from './bounding-rectangle-attribute.vue';
import Cartesian2Attribute from './cartesian2-attribute.vue';
import Cartesian3Attribute from './cartesian3-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import HorizontalOriginAttribute from './horizontal-origin-attribute.vue';
import NearFarScalarAttribute from './near-far-scalar-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import PinBuilderAttribute from './pin-builder-attribute.vue';
import StringAttribute from './string-attribute.vue';
import VerticalOriginAttribute from './vertical-origin-attribute.vue';

defineOptions({ name: 'BillboardGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: BillboardGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.BillboardGraphics, BillboardGraphicsSerializateJSON>({
  graphic: () => props.entity?.billboard,
  omit: props.omit,
  toJSON: (graphics, omit) => BillboardGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => BillboardGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="billboard"
    graphics-field="show"
    label="可见"
  />
  <template v-if="!hide?.includes('image')">
    <PinBuilderAttribute
      v-if="Array.isArray(model.image)"
      v-model="model.image"
      graphics="billboard"
      graphics-field="image"
    />
    <StringAttribute
      v-else
      v-model="model.image"
      graphics="billboard"
      graphics-field="image"
      label="图像地址"
    />
  </template>

  <NumberAttribute
    v-if="!hide?.includes('scale')"
    v-model="model.scale"
    graphics="billboard"
    graphics-field="scale"
    :precision="2"
    label="缩放"
  />
  <Cartesian2Attribute
    v-if="!hide?.includes('pixelOffset')"
    v-model="model.pixelOffset"
    graphics="billboard"
    graphics-field="pixelOffset"
    label="像素偏移"
  />
  <Cartesian3Attribute
    v-if="!hide?.includes('eyeOffset')"
    v-model="model.eyeOffset"
    graphics="billboard"
    graphics-field="eyeOffset"
    label="视角偏移"
  />
  <HorizontalOriginAttribute
    v-if="!hide?.includes('horizontalOrigin')"
    v-model="model.horizontalOrigin"
    graphics="billboard"
    graphics-field="horizontalOrigin"
    label="水平参照"
  />
  <VerticalOriginAttribute
    v-if="!hide?.includes('verticalOrigin')"
    v-model="model.verticalOrigin"
    graphics="billboard"
    graphics-field="verticalOrigin"
    label="垂直参照"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="billboard"
    graphics-field="heightReference"
    label="高度参照"
  />
  <ColorAttribute
    v-if="!hide?.includes('color')"
    v-model="model.color"
    graphics="billboard"
    graphics-field="color"
    label="颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('rotation')"
    v-model="model.rotation"
    graphics="billboard"
    graphics-field="rotation"
    label="旋转"
  />
  <Cartesian3Attribute
    v-if="!hide?.includes('alignedAxis')"
    v-model="model.alignedAxis"
    graphics="billboard"
    graphics-field="alignedAxis"
    label="对齐轴"
  />
  <BooleanAttribute
    v-if="!hide?.includes('sizeInMeters')"
    v-model="model.sizeInMeters"
    graphics="billboard"
    graphics-field="sizeInMeters"
    label="以米为单位"
  />
  <NumberAttribute
    v-if="!hide?.includes('width')"
    v-model="model.width"
    graphics="billboard"
    graphics-field="width"
    label="宽"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('height')"
    v-model="model.height"
    graphics="billboard"
    graphics-field="height"
    label="高"
    :precision="2"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('scaleByDistance')"
    v-model="model.scaleByDistance"
    graphics="billboard"
    graphics-field="scaleByDistance"
    label="按距离缩放"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('translucencyByDistance')"
    v-model="model.translucencyByDistance"
    graphics="billboard"
    graphics-field="translucencyByDistance"
    label="按距离透明"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('pixelOffsetScaleByDistance')"
    v-model="model.pixelOffsetScaleByDistance"
    graphics="billboard"
    graphics-field="pixelOffsetScaleByDistance"
    label="像素偏移比例"
  />
  <BoundingRectangleAttribute
    v-if="!hide?.includes('imageSubRegion')"
    v-model="model.imageSubRegion"
    graphics="billboard"
    graphics-field="imageSubRegion"
    label="图片显示区域"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="billboard"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <NumberAttribute
    v-if="!hide?.includes('disableDepthTestDistance')"
    v-model="model.disableDepthTestDistance"
    graphics="billboard"
    graphics-field="disableDepthTestDistance"
    label="禁用深度检测距离"
    :precision="2"
  />
</template>
