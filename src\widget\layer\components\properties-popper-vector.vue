<!-- 矢量数据属性弹窗 -->
<script lang="ts" setup>
import { canvasCoordToCartesian } from '@x3d/all';
import { use<PERSON>z<PERSON>iewer } from '@x3d/vue-hooks';

defineOptions({ name: 'PropertiesPopperVector' });

const viewer = useCzViewer();

const position = shallowRef<Cesium.Cartesian3>();
const property = ref<Record<string, any>>();

watchEffect((onCleanup) => {
  const remove = viewer.value.scene.x3d.vectorEvent.on('LEFT_CLICK', ({ pick, context, scene }) => {
    property.value = undefined;
    position.value = undefined;
    const ids: string[] = pick?.content?.batchTable?.getPropertyIds() ?? [];
    if (ids.length) {
      property.value = {};
      ids.forEach((id) => {
        property.value![id] = pick?.getProperty?.(id, 'name');
      });
      if (Object.values(property.value)?.filter(e => !!e)?.length) {
        position.value = canvasCoordToCartesian(context?.position, scene);
      }
    }
  });
  onCleanup(() => remove());
});
</script>

<template>
  <located-popper1
    v-if="position && property"
    :position="position"
    show-close
    header="属性信息"
    class="w-500px!"
    @close="position = undefined, property = undefined"
  >
    <el-scrollbar h="250px!" wrap-class="p-10px">
      <el-descriptions :column="1" border>
        <el-descriptions-item v-for="(value, key) in property" :key="key" :label="key">
          <span> {{ value }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-scrollbar>
  </located-popper1>
</template>
