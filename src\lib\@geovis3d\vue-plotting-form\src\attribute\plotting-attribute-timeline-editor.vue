<!-- 时间线编辑组件 -->
<script lang="ts" setup>
import type { PlottingDataSource } from '@/lib/@geovis3d/core';
import type { TimelineRow } from 'animation-timeline-js';
import { getKeys } from '@/lib/@geovis3d/core';
import { refAutoReset, useMounted, watchArray } from '@vueuse/core';
import { Timeline } from 'animation-timeline-js';
import dayjs from 'dayjs';

import Duration from 'dayjs/plugin/duration';
import { ref, shallowRef, watch, watchEffect } from 'vue';
import InputNumber from '../components/input-number.vue';
import { usePlottingDataSourceClockHelper } from './use-plotting-data-source-clock-helper';
import { usePlottingDataSourceHelper } from './use-plotting-data-source-helper';

export interface TimelineItem {
  label: string;
  row: TimelineRow;
}

export interface TimelineEditProps {
  dataSource: PlottingDataSource;
}

defineOptions({ name: 'PlottingAttributeTimelineEditor' });

const props = defineProps<TimelineEditProps>();

dayjs.extend(Duration);

const { active, plottings, dataSource, sampledPlottings } = usePlottingDataSourceHelper(
  () => props.dataSource,
);

const isMounted = useMounted();

const elRef = shallowRef<HTMLDivElement>();
const containerRef = shallowRef<HTMLElement>();
const timeline = shallowRef<Timeline>();

watchEffect((onCleanup) => {
  if (isMounted.value) {
    timeline.value = new Timeline({
      id: elRef.value,
      stepVal: 1000,
      snapStep: 1,
      zoomMax: 1000000000,
    });
    timeline.value._formatUnitsText = (ms) => {
      const duration = dayjs.duration(ms, 'milliseconds');
      let str = duration.format('HH:mm:ss');
      const day = Math.floor(duration.asDays());
      if (day) {
        str = `${Math.floor(duration.asDays())}d ${str}`;
      }
      return str;
    };
    timeline.value.onScroll((item) => {
      containerRef.value!.style.minHeight = `${item.scrollHeight}px`;
      containerRef.value!.parentElement!.scrollTop = item.scrollTop;
    });
  }
  onCleanup(() => timeline.value?.dispose());
});

/**
 * 当采样点在自身部分发生变化时触发
 */
const sampledsChanged = shallowRef<symbol>();

watchEffect((onCleanup) => {
  const cleanups: VoidFunction[] = [];
  plottings.value?.forEach((entity) => {
    const stop = entity.plotting.sampleds.definitionChanged.addEventListener(() => {
      sampledsChanged.value = Symbol('sampledsChanged');
    });
    cleanups.push(stop);
  });
  onCleanup(() => cleanups.forEach(fn => fn()));
});

type PlottingId = string;

interface Intermediary {
  id: string;
  duration: number;
  suspend?: number;
}

// 拥有采集点的标绘entity

// 中介变量
const intermediary = ref<Record<PlottingId, Intermediary[]>>({});

watchArray(
  sampledPlottings,
  (_newList, _prevList, _added, removed) => {
    removed?.forEach(entity => delete intermediary.value[entity.id]);
  },
  {
    immediate: true,
  },
);
// 标绘变量=》中介变量
watchEffect((onCleanup) => {
  const cleanups: VoidFunction[] = [];
  onCleanup(() => cleanups.forEach(fn => fn()));
  sampledPlottings.value.forEach((entity) => {
    const sampleds = entity.plotting.sampleds;
    const setValue = () => {
      intermediary.value[entity.id] = sampleds.getValue().map(({ id, duration }) => ({
        id,
        duration,
      }));
    };
    setValue();
    const stop = sampleds.definitionChanged.addEventListener(setValue);
    cleanups.push(stop);
  });
});
const keyframeChanged = refAutoReset(false, 16);

// 中介变量=》时间轴
watchEffect(() => {
  if (keyframeChanged.value) {
    return;
  }
  const models = getKeys(intermediary.value).map((id) => {
    const item = intermediary.value[id];
    let count = 0;
    const keyframes = item.map((sampled) => {
      count += sampled.duration;
      return {
        val: count * 1000,
      };
    });

    return {
      id,
      keyframes,
    };
  });
  timeline.value?.setModel({ rows: models });
});

watchEffect(() => {
  timeline.value?.onKeyframeChanged((res) => {
    keyframeChanged.value = true;
    const { row } = res.target;
    const entity = sampledPlottings.value.find(e => e.id === row!.id)!;
    const keyframes = row?.keyframes?.sort((a, b) => a.val - b.val);
    keyframes?.forEach((keyframe, index) => {
      const duration = index === 0 ? keyframe.val : keyframe.val - keyframes![index - 1].val;
      entity.plotting.sampleds.setByIndex(index, undefined, duration / 1000);
    });
  });
});

const { playing, duration, multipler } = usePlottingDataSourceClockHelper(dataSource);

watchEffect(() => {
  timeline.value?.onTimeChanged((res) => {
    duration.value = res.val / 1000;
  });
});

watch(
  [timeline, duration],
  ([timeline, duration]) => {
    timeline?.setTime(duration * 1000);
  },
  {
    immediate: true,
  },
);

watchEffect(() => {
  sampledPlottings.value.forEach((entity) => {
    entity.plotting.playing = playing.value;
  });
});
</script>

<template>
  <div class="timeline-editor">
    <div class="timeline-editor--header">
      <div>动画编辑器</div>
      <el-form size="small" inline>
        <el-form-item>
          <el-button
            :disabled="!active"
            @click="active!.plotting.sampling = true"
          >
            添加采样点
          </el-button>
        </el-form-item>
        <el-form-item>
          <InputNumber
            v-model="multipler"
            s
            :step="1"
            :precision="1"
            :min="0.1"
            controls-position="right"
            unit="X"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="playing = !playing">
            <template #icon>
              <el-icon v-if="playing" class="i-material-symbols:pause-rounded" />
              <el-icon v-else class="i-material-symbols:play-arrow" />
            </template>
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="timeline-editor--content">
      <div class="timeline-editor--list">
        <div class="timeline-editor--list-header">
          动画列表
        </div>
        <div class="timeline-editor--list-container">
          <div
            ref="containerRef"
            class="timeline-editor--list-content"
            @wheel="timeline?._handleWheelEvent"
          >
            <div
              v-for="(item, index) in sampledPlottings"
              :key="index"
              class="timeline-editor--list-label"
              :style="{ height: '24px', marginBottom: '2px' }"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div ref="elRef" class="timeline-editor--progress" />
      <div class="timeline-editor--attribute">
        <div class="timeline-editor--attribute-header">
          属性设置
        </div>
        <ElScrollbar> <span /> </ElScrollbar>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.timeline-editor {
  position: fixed !important;
  bottom: 40px;
  left: 50%;
  display: flex;
  flex-direction: column;
  width: 60%;
  height: 240px;
  color: #fff;
  background: rgba($color: #000, $alpha: 80%);
  transform: translateX(-50%);

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgb(232 232 232 / 20%);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-button {
    background-color: transparent;
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  .timeline-editor--header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    height: 46px;
    padding: 0 20px;
    background: #000;

    .el-form {
      flex: 1;
      margin-left: 40px;
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }

  .timeline-editor--content {
    display: flex;
    flex: 1;
    overflow: hidden;

    .timeline-editor--list,
    .timeline-editor--attribute {
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      width: 160px;
    }

    .timeline-editor--list {
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      width: 160px;
      border-right: 1px solid #aaa;
    }

    .timeline-editor--progress {
      flex: 1;
      overflow: hidden;
      border-right: 1px solid #aaa;
    }
  }
}

.timeline-editor--list-container {
  overflow: hidden;
}

.timeline-editor--list-label {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background-color: rgb(255 255 255 / 10%);
}

.timeline-editor--list-header,
.timeline-editor--attribute-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  height: 30px;
  font-size: 14px;
  font-weight: bold;
  background-color: rgb(255 255 255 / 20%);
}
</style>
