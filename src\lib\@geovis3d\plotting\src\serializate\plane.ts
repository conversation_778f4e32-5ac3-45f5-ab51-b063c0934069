import type { Cartesian3SerializateJSON } from './cartesian3';

import * as Cesium from 'cesium';
import { Cartesian3Serializate } from './cartesian3';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PlaneSerializateJSON {
  normal: Cartesian3SerializateJSON;
  distance: number;
}

export type PlaneKey = keyof PlaneSerializateJSON;

export class PlaneSerializate {
  private constructor() {}

  static toJSON(data?: Cesium.Plane, time?: Cesium.JulianDate): PlaneSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      normal: Cartesian3Serializate.toJSON(getValue('normal'))!,
      distance: getValue('distance')!,
    };
  }

  static fromJSON(json?: PlaneSerializateJSON): Cesium.Plane | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json);
    return new Cesium.Plane(
      Cartesian3Serializate.fromJSON(getValue('normal'))!,
      getValue('distance')!,
    );
  }
}
