import type { Cartesian3SerializateJSON } from './cartesian3';

import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type { HeightReferenceSerializateJSON, ShadowModeSerializateJSON } from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { Cartesian3Serializate } from './cartesian3';

import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface BoxGraphicsSerializateJSON {
  show?: boolean;
  dimensions?: Cartesian3SerializateJSON;
  heightReference?: HeightReferenceSerializateJSON;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
}

export type BoxGraphicsKey = keyof BoxGraphicsSerializateJSON;

export class BoxGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.BoxGraphics,
    omit?: BoxGraphicsKey[],
    time?: Cesium.JulianDate,
  ): BoxGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }

    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      dimensions: Cartesian3Serializate.toJSON(getValue('dimensions')),
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
    };
  }

  static fromJSON(
    json?: BoxGraphicsSerializateJSON,
    omit?: BoxGraphicsKey[],
  ): Cesium.BoxGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.BoxGraphics({
      show: getValue('show') ?? true,
      dimensions: Cartesian3Serializate.fromJSON(getValue('dimensions')),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
    });
  }
}
