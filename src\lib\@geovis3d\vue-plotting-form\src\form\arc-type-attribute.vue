<!-- ArcTypeAttribute -->
<script lang="ts" setup>
import type { ArcTypeSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'ArcTypeAttribute' });

const props = defineProps<{
  modelValue?: ArcTypeSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: ArcTypeSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const options = [
  {
    label: '无',
    value: 'NONE',
  },
  {
    label: '测地线',
    value: 'GEODESIC',
  },
  {
    label: '斜坡道',
    value: 'RHUMB',
  },
];
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="model" :options="options" :clearable="false" />
  </el-form-item>
</template>
