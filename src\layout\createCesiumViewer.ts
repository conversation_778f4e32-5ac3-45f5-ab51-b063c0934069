import type { MaybeComputedElementRef } from '@vueuse/core';
import type { Ref } from 'vue';
import { X3d } from '@x3d/all';

import { createCzViewer } from '@x3d/vue-hooks';
import * as Cesium from 'cesium';

import 'cesium/Build/Cesium/Widgets/widgets.css';

export function createCesiumViewer(element: MaybeComputedElementRef): Ref<Cesium.Viewer> {
  // 默认相机视角，此位置提供给camera.flyHome调用
  Cesium.Camera.DEFAULT_VIEW_RECTANGLE = new Cesium.Rectangle(
    -3.141592653589793,
    -1.5707963267948966,
    3.141592653589793,
    1.5707963267948966,
  );

  const viewer = createCzViewer(element, {
    baseLayer: false, // 底图
    // baseLayer: new Cesium.ImageryLayer(createImageryProviders()[0].provider),
    baseLayerPicker: false, // 图层选择器
    fullscreenButton: false, // 全屏按钮
    geocoder: false, // 右上角查询搜索
    infoBox: false, // 信息框
    homeButton: false, // home按钮
    sceneModePicker: false, // 3d 2d选择器
    selectionIndicator: false, //
    animation: false, // 左下角仪表
    timeline: false, // 时间轴
    navigationHelpButton: false, // 右上角帮助按钮
    shouldAnimate: true, // 允许动画

  });

  watch(viewer, (viewer) => {
    if (viewer) {
      X3d.register(viewer);
      // @ts-expect-error globalThis
      globalThis.viewer = viewer;

      // 取消双击事件 ，双击的话，会视角直接切到该实体，且无法拖拽
      viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
      );
      // 关闭地面大气层
      viewer.scene.globe.showGroundAtmosphere = false;
      // 关闭天空大气层
      viewer.scene.skyAtmosphere.show = false;
    }
  });
  return viewer;
}
