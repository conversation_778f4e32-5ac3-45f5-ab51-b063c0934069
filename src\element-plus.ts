import 'element-plus/theme-chalk/dark/css-vars.css';

Object.assign(ElPagination.props, {
  layout: {
    type: String,
    default: 'prev, pager, next',
  },
  background: {
    type: Boolean,
    default: true,
  },
});

Object.assign(ElDialog.props, {
  appendToBody: {
    type: Boolean,
    default: true,
  },
  closeOnClickModal: {
    type: Boolean,
    default: false,
  },
  closeOnPressEscape: {
    type: Boolean,
    default: false,
  },
  destroyOnClose: {
    type: Boolean,
    default: true,
  },
});
