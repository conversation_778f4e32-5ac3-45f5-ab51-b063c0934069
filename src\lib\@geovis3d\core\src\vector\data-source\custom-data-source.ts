import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.CustomDataSource} 拓展用法与 {@link Cesium.CustomDataSource} 基本一致。
 *
 * `GcCustomDataSource.event`鼠标事件监听
 */
export class GcCustomDataSource extends Cesium.CustomDataSource {
  constructor(name?: string) {
    super(name);

    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
