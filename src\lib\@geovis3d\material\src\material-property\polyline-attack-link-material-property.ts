import * as Cesium from 'cesium';

import PolylineAttackLinkPNG from './assets/polyline-attack-link.png';
import { setCesiumMaterialCache } from './material-cache';
import PolylineAttackLink from './shaders/polyline-attack-link.glsl?raw';

export interface PolylineAttackLinkMaterialUniforms {
  color?: Cesium.Color;
  time?: number;
  image?: string;
}

const uniforms: PolylineAttackLinkMaterialUniforms = {
  color: Cesium.Color.RED,
  time: 0,
  image: PolylineAttackLinkPNG,
};

const PolylineAttackLinkMaterialType = 'PolylineAttackLinkMaterialType';

export class PolylineAttackLinkMaterial extends Cesium.Material {
  constructor(options?: PolylineAttackLinkMaterialUniforms) {
    super({
      fabric: {
        type: PolylineAttackLinkMaterialType,
        source: PolylineAttackLink,
        uniforms: {
          color: options?.color ?? uniforms.color,
          time: options?.time ?? uniforms.time,
          image: options?.image ?? uniforms.image,
        },
      },
    });
  }
}

/**
 * 攻击特效线
 */
export class PolylineAttackLinkMaterialProperty implements Cesium.MaterialProperty {
  constructor(options?: PolylineAttackLinkMaterialUniforms) {
    this._color = Cesium.defaultValue(options?.color, uniforms.color);
    // this._time = Cesium.defaultValue(options?.time, uniforms.time);
    this._time = performance.now();

    this._image = Cesium.defaultValue(options?.image, uniforms.image);
  }

  private _time: number;

  get time() {
    return this._time;
  }

  set time(value: number) {
    if (this._time !== value) {
      this._time = value;
    }
  }

  private _color: Cesium.Color;

  get color() {
    return this._color;
  }

  set color(value: Cesium.Color) {
    if (this._color !== value) {
      this._color = value;
    }
  }

  private _image: string;

  get image() {
    return this._image;
  }

  set image(value: string) {
    if (this._image !== value) {
      this._image = value;
    }
  }

  static get MaterialType() {
    return PolylineAttackLinkMaterialType;
  }

  getType(_time?: Cesium.JulianDate) {
    return PolylineAttackLinkMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: PolylineAttackLinkMaterialUniforms) {
    result ??= {};
    result.color = this.color;
    result.image = this.image;
    result.time = (performance.now() - this._time) / 1000;

    return result;
  }

  equals(other?: PolylineAttackLinkMaterialProperty) {
    return (
      this === other
      || (other instanceof PolylineAttackLinkMaterialProperty
        && this.color == other?.color
        && this.time == other?.time
        && this.image == other?.image)
    );
  }
}
setCesiumMaterialCache(PolylineAttackLinkMaterialType, {
  fabric: {
    type: PolylineAttackLinkMaterialType,
    uniforms,
    source: PolylineAttackLink,
  },
  translucent: () => true,
});
