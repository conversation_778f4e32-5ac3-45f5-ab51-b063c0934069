<!-- 挖填方分析 -->
<script lang="ts" setup>
import { cartesianToWgs84, CzPlotEntity } from '@x3d/all';
import { useCzDataSource, useCzEntities, useCzViewer } from '@x3d/vue-hooks';
import {
  CallbackProperty,
  ConstantPositionProperty,
  CustomDataSource,
  PolygonGraphics,
  PolygonHierarchy,
} from 'cesium';

import ExcavationTool from './excavation';

defineOptions({ name: 'SurfaceExcavation' });
// 深度检测关闭
const emits = defineEmits(['close']);
const viewer = useCzViewer();
const depthTestAgainstTerrain = viewer.value.scene.globe.depthTestAgainstTerrain;
viewer.value.scene.globe.depthTestAgainstTerrain = true;
const datasource = shallowRef(new CustomDataSource());
const { dataSource } = useCzDataSource(datasource);
const points = ref<number[][]>([]);
const digDepth = ref(10);
const plotEntity = shallowRef<CzPlotEntity>();
const bottomSurface = new ExcavationTool(viewer.value, {
  splitNum: 300,
  height: digDepth.value,
});
watch(digDepth, () => {
  bottomSurface.updateExcavateDepth(digDepth.value);
});
function start() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      manualTerminate: entity => entity.record.positions.getLength() > 2,
      control: { visible: true },
      interval: { visible: 'loop' },
      delete: { visible: false },
      effect(entity, onCleanup) {
        entity.polygon ??= new PolygonGraphics({
          material: Cesium.Color.fromCssColorString('#5474FC').withAlpha(0.3),
        });
        entity.polyline ??= new Cesium.PolylineGraphics({
          width: 5.0,
          material: new Cesium.PolylineGlowMaterialProperty({
            color: Cesium.Color.WHEAT,
          }),
          depthFailMaterial: new Cesium.PolylineGlowMaterialProperty({}),
          clampToGround: true,
        });

        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);
        if (coords.length >= 3) {
          const hierarchy = new PolygonHierarchy(positions);
          entity.polygon.hierarchy = new CallbackProperty(() => hierarchy, false);
          entity.polyline.positions = new Cesium.CallbackProperty(
            () => [...positions, positions[0]],
            false,
          );
          entity.position = new ConstantPositionProperty(record.positions.getCenter()!);
          points.value = coords;
          bottomSurface.updateData(positions);
          onCleanup(() => {
            bottomSurface?.clear();
          });
        }
        else {
          entity.polygon.hierarchy = undefined;
          entity.position = undefined;
        }
      },
    },
  });
}
useCzEntities(() => [plotEntity.value]);
function clear() {
  datasource.value = new CustomDataSource();
  bottomSurface?.clear();
  plotEntity.value = undefined;
}
onUnmounted(() => {
  viewer.value.scene.globe.depthTestAgainstTerrain = depthTestAgainstTerrain;
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="挖填方分析"
    class="h-258px w-400px"

    @close="emits('close')"
  >
    <el-form mt="24px" mx="25px">
      <el-form-item label="深度">
        <el-input-number
          v-model="digDepth"
          type="number"
          :min="0"
          w="120px"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button type="primary" @click="start()">
          绘制开挖区域
        </el-button>
        <el-button class="plain-#FF6363 px-26px!" @click="clear()">
          清除
        </el-button>
      </div>
    </template>
  </drag-card>
</template>

<style lang="scss" scope>
.surface-excavation {
  display: inline-block;
}
</style>
