import type { MaterialPropertySerializateController } from './material-property';

import { ChemicalMaterialProperty } from '@/lib/@geovis3d/material';

export interface ChemicalMaterialPropertySerializateJSON {
  // color?: string;
}

export default <
  MaterialPropertySerializateController<
    'ChemicalMaterialProperty',
    ChemicalMaterialProperty,
    ChemicalMaterialPropertySerializateJSON
  >
>{
  type: 'ChemicalMaterialProperty',
  hit: (property) => {
    return property instanceof ChemicalMaterialProperty;
  },
  toJSON(_property, _time) {
    // time ??= Cesium.JulianDate.now();
    // const color = property?.getValue(time)?.color;
    return {
      type: 'ChemicalMaterialProperty',
      params: {
        // color: ColorSerializate.toJSON(color),
      },
    };
  },
  fromJSON(_json) {
    // const color = json?.params?.color;
    // return new ChemicalMaterialProperty(ColorSerializate.fromJSON(color));
    return new ChemicalMaterialProperty();
  },
};
