<!-- 标绘entity的属性列表 -->
<script lang="ts" setup>
import type { PlottingDataSource } from '@/lib/@geovis3d/core';

import { ref } from 'vue';
import EntityNameEditor from './entity-name-editor.vue';
import { usePlottingDataSourceHelper } from './use-plotting-data-source-helper';

export interface PlottingAttributeListProps {
  dataSource: PlottingDataSource;
}

defineOptions({ name: 'PlottingAttributeList' });

const props = defineProps<PlottingAttributeListProps>();

const { plottings, setActive, flyTo, remove } = usePlottingDataSourceHelper(() => props.dataSource);

const editorIndex = ref<number>();
</script>

<template>
  <el-scrollbar class="plotting-attribute-list px-20px">
    <div
      v-for="(entity, index) in plottings"
      :key="entity.id"
      class="plotting-attribute-list--item"
      flex="~"
      @click="setActive(entity)"
    >
      <EntityNameEditor
        :entity="entity"
        :editable="editorIndex === index"
        @update:editable="editorIndex = $event ? index : undefined"
      />
      <el-icon
        flex="shrink-0"
        class="i-material-symbols:edit"
        text="20px!"
        title="编辑名称"
        @click.stop="editorIndex = index"
      />
      <el-icon
        class="i-material-symbols:moved-location-rounded"
        text="20px!"
        flex="shrink-0"
        title="前往定位"
        @click.stop="flyTo(entity)"
      />
      <el-icon
        class="i-material-symbols:delete"
        text="20px!"
        flex="shrink-0"
        title="删除标绘"
        @click.stop="remove(entity)"
      />
    </div>
  </el-scrollbar>
</template>

<style scoped lang="scss">
.plotting-attribute-list {
  width: 100%;

  .plotting-attribute-list--item {
    display: flex;
    align-items: center;
    height: 34px;
    padding: 0 10px;
    margin-bottom: 10px;
    font-size: 14px;
    cursor: pointer;
    background: rgb(28 29 30);

    &.active,
    &:hover {
      background: rgb(53 54 54 / 80%);
    }

    .icon {
      display: none;
      padding: 0 5px;
      font-size: 1rem;
      cursor: pointer;
    }

    &:hover {
      .icon {
        display: block;
      }
    }
  }
}
</style>
