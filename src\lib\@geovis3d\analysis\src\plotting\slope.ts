import type { GcEntityConstructorOptions } from '@/lib/@geovis3d/core';
import { PlottingEntity } from '@/lib/@geovis3d/core';
import * as Cesium from 'cesium';

import { slope } from '../function/slope';

export class AnalysisSlopeEntity extends PlottingEntity {
  constructor(options?: GcEntityConstructorOptions) {
    super({
      rectangle: {
        material: Cesium.Color.RED.withAlpha(0.1),
      },
      ...options,
      plotting: {
        options: {
          forceTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
          center: { visible: false },
          control: { visible: false },
          delete: { visible: true },

          destroy: (entity) => {
            entity._slopes?.forEach((e) => {
              entity.entityCollection?.remove(e);
            });
          },
          update(entity) {
            const positions = entity.plotting.coordinates.getPositions();
            const mousePosition = entity.plotting.mousePosition;
            mousePosition && positions.push(mousePosition.clone());
            const cache
              = positions.length < 2
                ? undefined
                : Cesium.Rectangle.fromCartesianArray(positions ?? []);
            entity.rectangle!.coordinates = new Cesium.CallbackProperty(() => cache, false);
            if (entity.plotting.coordinates.getPositions().length > 1) {
              slope(entity.scene!, cache!).then((entities) => {
                entity._slopes?.forEach((e) => {
                  entity.entityCollection?.remove(e);
                });
                entity._slopes = entities;
                entity._slopes.forEach((e) => {
                  entity.entityCollection?.add(e);
                });
              });
            }
          },
        },
      },
    });
  }
}
