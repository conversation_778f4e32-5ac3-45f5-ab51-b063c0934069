import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.GroundPolylinePrimitive} 构造函数参数
 */
export type GroundPolylinePrimitiveConstructorOptions = ConstructorParameters<
  typeof Cesium.GroundPolylinePrimitive
>[0];

/**
 * {@link Cesium.GroundPolylinePrimitive} 拓展用法与 {@link Cesium.GroundPolylinePrimitive} 基本一致。
 *
 * `GcGroundPolylinePrimitive.event`鼠标事件监听
 */
export class GcGroundPolylinePrimitive extends Cesium.GroundPolylinePrimitive {
  constructor(options?: GroundPolylinePrimitiveConstructorOptions) {
    super(options);

    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
