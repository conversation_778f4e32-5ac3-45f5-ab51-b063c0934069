import type { MaterialPropertySerializateController } from './material-property';
import { PolylineTrailLinkMaterialProperty } from '@/lib/@geovis3d/material';

import * as Cesium from 'cesium';

import { ColorSerializate } from '../color';

export interface PolylineTrailLinkMaterialPropertySerializateJSON {
  color?: string;
  time?: number;
}

/**
 * PolylineTrailLinkMaterialProperty序列化加入缓存
 * @internal
 */
export default <
  MaterialPropertySerializateController<
    'PolylineTrailLinkMaterialProperty',
    PolylineTrailLinkMaterialProperty,
    PolylineTrailLinkMaterialPropertySerializateJSON
  >
>{
  type: 'PolylineTrailLinkMaterialProperty',
  hit: (property) => {
    return property instanceof PolylineTrailLinkMaterialProperty;
  },
  toJSON(property, time) {
    time ??= Cesium.JulianDate.now();
    const data = property?.getValue(time) ?? {};
    return {
      type: 'PolylineTrailLinkMaterialProperty',
      params: {
        color: ColorSerializate.toJSON(data.color),
        time: data.time,
      },
    };
  },
  fromJSON(json) {
    const { color, time } = json?.params ?? {};
    return new PolylineTrailLinkMaterialProperty({
      color: ColorSerializate.fromJSON(color),
      time,
    });
  },
};
