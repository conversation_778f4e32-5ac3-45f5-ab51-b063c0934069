import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.GroundPrimitive} 构造函数参数
 */
export type GroundPrimitiveConstructorOptions = ConstructorParameters<
  typeof Cesium.GroundPrimitive
>[0];

/**
 * {@link Cesium.GroundPrimitive} 拓展用法与 {@link Cesium.GroundPrimitive} 基本一致。
 *
 * `GcGroundPrimitive.event`鼠标事件监听
 */
export class GcGroundPrimitive extends Cesium.GroundPrimitive {
  constructor(options?: GroundPrimitiveConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
