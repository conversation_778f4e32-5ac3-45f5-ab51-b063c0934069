import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.KmlDataSource} 拓展用法与 {@link Cesium.KmlDataSource} 基本一致。
 * `GcKmlDataSource.event`鼠标事件监听
 */
export class GcKmlDataSource extends Cesium.KmlDataSource {
  constructor() {
    super();
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
