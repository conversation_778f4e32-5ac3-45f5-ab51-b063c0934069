!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("core-js/modules/es.array.find.js"),require("core-js/modules/es.object.to-string.js"),require("core-js/modules/es.array.concat.js"),require("core-js/modules/web.dom-collections.for-each.js"),require("core-js/modules/es.object.keys.js"),require("core-js/modules/es.array.slice.js"),require("core-js/modules/es.array.join.js"),require("core-js/modules/es.array.splice.js"),require("core-js/modules/es.promise.js"),require("core-js/modules/es.function.name.js"),require("core-js/modules/es.number.is-nan.js"),require("core-js/modules/es.number.constructor.js"),require("core-js/modules/es.array.map.js"),require("core-js/modules/es.array.iterator.js"),require("core-js/modules/web.dom-collections.iterator.js"),require("core-js/modules/es.array.filter.js"),require("core-js/modules/es.object.assign.js"),require("core-js/modules/es.symbol.js"),require("core-js/modules/es.regexp.exec.js"),require("core-js/modules/es.string.match.js"),require("core-js/modules/es.regexp.constructor.js"),require("core-js/modules/es.regexp.sticky.js"),require("core-js/modules/es.regexp.to-string.js"),require("core-js/modules/es.string.split.js"),require("core-js/modules/es.string.replace.js"),require("core-js/modules/es.string.trim.js"),require("core-js/modules/es.regexp.test.js"),require("core-js/modules/es.array.sort.js"),require("core-js/modules/es.string.iterator.js"),require("core-js/modules/es.number.to-fixed.js"),require("core-js/modules/es.string.repeat.js"),require("core-js/modules/es.array.find-index.js"),require("core-js/modules/es.typed-array.uint8-array.js"),require("core-js/modules/esnext.typed-array.at.js"),require("core-js/modules/es.typed-array.copy-within.js"),require("core-js/modules/es.typed-array.every.js"),require("core-js/modules/es.typed-array.fill.js"),require("core-js/modules/es.typed-array.filter.js"),require("core-js/modules/es.typed-array.find.js"),require("core-js/modules/es.typed-array.find-index.js"),require("core-js/modules/esnext.typed-array.find-last.js"),require("core-js/modules/esnext.typed-array.find-last-index.js"),require("core-js/modules/es.typed-array.for-each.js"),require("core-js/modules/es.typed-array.includes.js"),require("core-js/modules/es.typed-array.index-of.js"),require("core-js/modules/es.typed-array.iterator.js"),require("core-js/modules/es.typed-array.join.js"),require("core-js/modules/es.typed-array.last-index-of.js"),require("core-js/modules/es.typed-array.map.js"),require("core-js/modules/es.typed-array.reduce.js"),require("core-js/modules/es.typed-array.reduce-right.js"),require("core-js/modules/es.typed-array.reverse.js"),require("core-js/modules/es.typed-array.set.js"),require("core-js/modules/es.typed-array.slice.js"),require("core-js/modules/es.typed-array.some.js"),require("core-js/modules/es.typed-array.sort.js"),require("core-js/modules/es.typed-array.subarray.js"),require("core-js/modules/es.typed-array.to-locale-string.js"),require("core-js/modules/es.typed-array.to-string.js"),require("core-js/modules/web.url.js"),require("core-js/modules/web.url-search-params.js"),require("core-js/modules/es.json.stringify.js")):"function"==typeof define&&define.amd?define(["core-js/modules/es.array.find.js","core-js/modules/es.object.to-string.js","core-js/modules/es.array.concat.js","core-js/modules/web.dom-collections.for-each.js","core-js/modules/es.object.keys.js","core-js/modules/es.array.slice.js","core-js/modules/es.array.join.js","core-js/modules/es.array.splice.js","core-js/modules/es.promise.js","core-js/modules/es.function.name.js","core-js/modules/es.number.is-nan.js","core-js/modules/es.number.constructor.js","core-js/modules/es.array.map.js","core-js/modules/es.array.iterator.js","core-js/modules/web.dom-collections.iterator.js","core-js/modules/es.array.filter.js","core-js/modules/es.object.assign.js","core-js/modules/es.symbol.js","core-js/modules/es.regexp.exec.js","core-js/modules/es.string.match.js","core-js/modules/es.regexp.constructor.js","core-js/modules/es.regexp.sticky.js","core-js/modules/es.regexp.to-string.js","core-js/modules/es.string.split.js","core-js/modules/es.string.replace.js","core-js/modules/es.string.trim.js","core-js/modules/es.regexp.test.js","core-js/modules/es.array.sort.js","core-js/modules/es.string.iterator.js","core-js/modules/es.number.to-fixed.js","core-js/modules/es.string.repeat.js","core-js/modules/es.array.find-index.js","core-js/modules/es.typed-array.uint8-array.js","core-js/modules/esnext.typed-array.at.js","core-js/modules/es.typed-array.copy-within.js","core-js/modules/es.typed-array.every.js","core-js/modules/es.typed-array.fill.js","core-js/modules/es.typed-array.filter.js","core-js/modules/es.typed-array.find.js","core-js/modules/es.typed-array.find-index.js","core-js/modules/esnext.typed-array.find-last.js","core-js/modules/esnext.typed-array.find-last-index.js","core-js/modules/es.typed-array.for-each.js","core-js/modules/es.typed-array.includes.js","core-js/modules/es.typed-array.index-of.js","core-js/modules/es.typed-array.iterator.js","core-js/modules/es.typed-array.join.js","core-js/modules/es.typed-array.last-index-of.js","core-js/modules/es.typed-array.map.js","core-js/modules/es.typed-array.reduce.js","core-js/modules/es.typed-array.reduce-right.js","core-js/modules/es.typed-array.reverse.js","core-js/modules/es.typed-array.set.js","core-js/modules/es.typed-array.slice.js","core-js/modules/es.typed-array.some.js","core-js/modules/es.typed-array.sort.js","core-js/modules/es.typed-array.subarray.js","core-js/modules/es.typed-array.to-locale-string.js","core-js/modules/es.typed-array.to-string.js","core-js/modules/web.url.js","core-js/modules/web.url-search-params.js","core-js/modules/es.json.stringify.js"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).Player=t()}(this,(function(){"use strict";function e(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function t(t){for(var i=1;i<arguments.length;i++){var n=null!=arguments[i]?arguments[i]:{};i%2?e(Object(n),!0).forEach((function(e){s(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,y(n.key),n)}}function r(e,t,i){return t&&o(e.prototype,t),i&&o(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,i){return(t=y(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}function l(e){return(l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function c(e,t){return(c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return u(e)}function d(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=l(e);if(t){var o=l(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return h(this,i)}}function f(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=l(e)););return e}function p(){return p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,i){var n=f(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:i):o.value}},p.apply(this,arguments)}function g(e){return function(e){if(Array.isArray(e))return v(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return v(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);"Object"===i&&e.constructor&&(i=e.constructor.name);if("Map"===i||"Set"===i)return Array.from(e);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return v(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function v(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}function y(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var m={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,i="~";function n(){}function o(e,t,i){this.fn=e,this.context=t,this.once=i||!1}function r(e,t,n,r,s){if("function"!=typeof n)throw new TypeError("The listener must be a function");var a=new o(n,r||e,s),l=i?i+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],a]:e._events[l].push(a):(e._events[l]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function a(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(i=!1)),a.prototype.eventNames=function(){var e,n,o=[];if(0===this._eventsCount)return o;for(n in e=this._events)t.call(e,n)&&o.push(i?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(e)):o},a.prototype.listeners=function(e){var t=i?i+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,r=n.length,s=new Array(r);o<r;o++)s[o]=n[o].fn;return s},a.prototype.listenerCount=function(e){var t=i?i+e:e,n=this._events[t];return n?n.fn?1:n.length:0},a.prototype.emit=function(e,t,n,o,r,s){var a=i?i+e:e;if(!this._events[a])return!1;var l,c,u=this._events[a],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,o),!0;case 5:return u.fn.call(u.context,t,n,o,r),!0;case 6:return u.fn.call(u.context,t,n,o,r,s),!0}for(c=1,l=new Array(h-1);c<h;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var d,f=u.length;for(c=0;c<f;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,o);break;default:if(!l)for(d=1,l=new Array(h-1);d<h;d++)l[d-1]=arguments[d];u[c].fn.apply(u[c].context,l)}}return!0},a.prototype.on=function(e,t,i){return r(this,e,t,i,!1)},a.prototype.once=function(e,t,i){return r(this,e,t,i,!0)},a.prototype.removeListener=function(e,t,n,o){var r=i?i+e:e;if(!this._events[r])return this;if(!t)return s(this,r),this;var a=this._events[r];if(a.fn)a.fn!==t||o&&!a.once||n&&a.context!==n||s(this,r);else{for(var l=0,c=[],u=a.length;l<u;l++)(a[l].fn!==t||o&&!a[l].once||n&&a[l].context!==n)&&c.push(a[l]);c.length?this._events[r]=1===c.length?c[0]:c:s(this,r)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=i?i+e:e,this._events[t]&&s(this,t)):(this._events=new n,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=i,a.EventEmitter=a,e.exports=a}(m);var k=m.exports,b="undefined"!=typeof window&&window.location&&window.location.href.indexOf("xgplayerdebugger=1")>-1,C={info:"color: #525252; background-color: #90ee90;",error:"color: #525252; background-color: red;",warn:"color: #525252; background-color: yellow; "},_="%c[xgplayer]",w={config:{debug:b?3:0},logInfo:function(e){for(var t,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];this.config.debug>=3&&(t=console).log.apply(t,[_,C.info,e].concat(n))},logWarn:function(e){for(var t,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];this.config.debug>=1&&(t=console).warn.apply(t,[_,C.warn,e].concat(n))},logError:function(e){var t;if(!(this.config.debug<1)){for(var i=this.config.debug>=2?"trace":"error",n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];(t=console)[i].apply(t,[_,C.error,e].concat(o))}}};var T=function(){function e(t){n(this,e),this.bufferedList=t}return r(e,[{key:"start",value:function(e){return this.bufferedList[e].start}},{key:"end",value:function(e){return this.bufferedList[e].end}},{key:"length",get:function(){return this.bufferedList.length}}]),e}(),x={};function S(e,t){for(var i=0,n=t.length;i<n;i++)if(e.indexOf(t[i])>-1)return!0;return!1}function E(e){var t=i(e);return null!==e&&("object"===t||"function"===t)}function P(e,t,i){var n,o,r,s,a,l,c=0,u=!1,h=!1,d=!0,f=!t&&0!==t&&"function"==typeof window.requestAnimationFrame;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var i=n,r=o;return n=o=void 0,c=t,s=e.apply(r,i)}function g(e,t){return f?(window.cancelAnimationFrame(a),window.requestAnimationFrame(e)):setTimeout(e,t)}function v(e){return c=e,a=g(m,t),u?p(e):s}function y(e){var i=e-l;return void 0===l||i>=t||i<0||h&&e-c>=r}function m(){var e=Date.now();if(y(e))return k(e);a=g(m,function(e){var i=e-c,n=t-(e-l);return h?Math.min(n,r-i):n}(e))}function k(e){return a=void 0,d&&n?p(e):(n=o=void 0,s)}function b(){for(var e=Date.now(),i=y(e),r=arguments.length,c=new Array(r),u=0;u<r;u++)c[u]=arguments[u];if(n=c,o=this,l=e,i){if(void 0===a)return v(l);if(h)return a=g(m,t),p(l)}return void 0===a&&(a=g(m,t)),s}return t=+t||0,E(i)&&(u=!!i.leading,r=(h="maxWait"in i)?Math.max(+i.maxWait||0,t):r,d="trailing"in i?!!i.trailing:d),b.cancel=function(){void 0!==a&&function(e){if(f)return window.cancelAnimationFrame(e);clearTimeout(e)}(a),c=0,n=l=o=a=void 0},b.flush=function(){return void 0===a?s:k(Date.now())},b.pending=function(){return void 0!==a},b}x.createDom=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"div",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",o=document.createElement(e);return o.className=n,o.innerHTML=t,Object.keys(i).forEach((function(t){var n=t,r=i[t];"video"===e||"audio"===e||"live-video"===e?r&&o.setAttribute(n,r):o.setAttribute(n,r)})),o},x.createDomFromHtml=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{var n=document.createElement("div");n.innerHTML=e;var o=n.children;return n=null,o.length>0?(o=o[0],i&&x.addClass(o,i),t&&Object.keys(t).forEach((function(e){o.setAttribute(e,t[e])})),o):null}catch(r){return w.logError("util.createDomFromHtml",r),null}},x.hasClass=function(e,t){if(!e||!t)return!1;try{return Array.prototype.some.call(e.classList,(function(e){return e===t}))}catch(o){var n=e.className&&"object"===i(e.className)?e.getAttribute("class"):e.className;return n&&!!n.match(new RegExp("(\\s|^)"+t+"(\\s|$)"))}},x.addClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.add(t)}))}catch(n){x.hasClass(e,t)||(e.className&&"object"===i(e.className)?e.setAttribute("class",e.getAttribute("class")+" "+t):e.className+=" "+t)}},x.removeClass=function(e,t){if(e&&t)try{t.replace(/(^\s+|\s+$)/g,"").split(/\s+/g).forEach((function(t){t&&e.classList.remove(t)}))}catch(n){x.hasClass(e,t)&&t.split(/\s+/g).forEach((function(t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className&&"object"===i(e.className)?e.setAttribute("class",e.getAttribute("class").replace(n," ")):e.className=e.className.replace(n," ")}))}},x.toggleClass=function(e,t){e&&t.split(/\s+/g).forEach((function(t){x.hasClass(e,t)?x.removeClass(e,t):x.addClass(e,t)}))},x.classNames=function(){for(var e=arguments,t=[],i=function(i){"String"===x.typeOf(e[i])?t.push(e[i]):"Object"===x.typeOf(e[i])&&Object.keys(e[i]).map((function(n){e[i][n]&&t.push(n)}))},n=0;n<arguments.length;n++)i(n);return t.join(" ")},x.findDom=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,i=arguments.length>1?arguments[1]:void 0;try{e=t.querySelector(i)}catch(n){w.logError("util.findDom",n),0===i.indexOf("#")&&(e=t.getElementById(i.slice(1)))}return e},x.getCss=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,!1)[t]},x.padStart=function(e,t,i){for(var n=String(i),o=t>>0,r=Math.ceil(o/n.length),s=[],a=String(e);r--;)s.push(n);return s.join("").substring(0,o-a.length)+a},x.format=function(e){if(window.isNaN(e))return"";e=Math.round(e);var t=x.padStart(Math.floor(e/3600),2,0),i=x.padStart(Math.floor((e-3600*t)/60),2,0),n=x.padStart(Math.floor(e-3600*t-60*i),2,0);return("00"===t?[i,n]:[t,i,n]).join(":")},x.event=function(e){if(e.touches){var t=e.touches[0]||e.changedTouches[0];e.clientX=t.clientX||0,e.clientY=t.clientY||0,e.offsetX=t.pageX-t.target.offsetLeft,e.offsetY=t.pageY-t.target.offsetTop}e._target=e.target||e.srcElement},x.typeOf=function(e){return Object.prototype.toString.call(e).match(/([^\s.*]+)(?=]$)/g)[0]},x.deepCopy=function(e,t){if("Object"===x.typeOf(t)&&"Object"===x.typeOf(e))return Object.keys(t).forEach((function(i){"Object"!==x.typeOf(t[i])||t[i]instanceof Node?"Array"===x.typeOf(t[i])?e[i]="Array"===x.typeOf(e[i])?e[i].concat(t[i]):t[i]:e[i]=t[i]:void 0===e[i]||void 0===e[i]?e[i]=t[i]:x.deepCopy(e[i],t[i])})),e},x.deepMerge=function(e,t){return Object.keys(t).map((function(i){var n;"Array"===x.typeOf(t[i])&&"Array"===x.typeOf(e[i])?"Array"===x.typeOf(e[i])&&(n=e[i]).push.apply(n,g(t[i])):x.typeOf(e[i])!==x.typeOf(t[i])||null===e[i]||"Object"!==x.typeOf(e[i])||t[i]instanceof window.Node?null!==t[i]&&(e[i]=t[i]):x.deepMerge(e[i],t[i])})),e},x.getBgImage=function(e){var t=(e.currentStyle||window.getComputedStyle(e,null)).backgroundImage;if(!t||"none"===t)return"";var i=document.createElement("a");return i.href=t.replace(/url\("|"\)/g,""),i.href},x.copyDom=function(e){if(e&&1===e.nodeType){var t=document.createElement(e.tagName);return Array.prototype.forEach.call(e.attributes,(function(e){t.setAttribute(e.name,e.value)})),e.innerHTML&&(t.innerHTML=e.innerHTML),t}return""},x.setInterval=function(e,t,i,n){e._interval[t]||(e._interval[t]=window.setInterval(i.bind(e),n))},x.clearInterval=function(e,t){clearInterval(e._interval[t]),e._interval[t]=null},x.setTimeout=function(e,t,i){e._timers||(e._timers=[]);var n=setTimeout((function(){t(),x.clearTimeout(e,n)}),i);return e._timers.push(n),n},x.clearTimeout=function(e,t){var i=e._timers;if("Array"===x.typeOf(i)){for(var n=0;n<i.length;n++)if(i[n]===t){i.splice(n,1),clearTimeout(t);break}}else clearTimeout(t)},x.clearAllTimers=function(e){var t=e._timers;"Array"===x.typeOf(t)&&(t.map((function(e){clearTimeout(e)})),e._timerIds=[])},x.createImgBtn=function(e,t,i,n){var o,r,s,a=x.createDom("xg-".concat(e),"",{},"xgplayer-".concat(e,"-img"));(a.style.backgroundImage='url("'.concat(t,'")'),i&&n)&&(["px","rem","em","pt","dp","vw","vh","vm","%"].every((function(e){return!(i.indexOf(e)>-1&&n.indexOf(e)>-1)||(o=parseFloat(i.slice(0,i.indexOf(e)).trim()),r=parseFloat(n.slice(0,n.indexOf(e)).trim()),s=e,!1)})),a.style.width="".concat(o).concat(s),a.style.height="".concat(r).concat(s),a.style.backgroundSize="".concat(o).concat(s," ").concat(r).concat(s),a.style.margin="start"===e?"-".concat(r/2).concat(s," auto auto -").concat(o/2).concat(s):"auto 5px auto 5px");return a},x.Hex2RGBA=function(e,t){var i=[];if(/^\#[0-9A-F]{3}$/i.test(e)){var n="#";e.replace(/[0-9A-F]/gi,(function(e){n+=e+e})),e=n}return/^#[0-9A-F]{6}$/i.test(e)?(e.replace(/[0-9A-F]{2}/gi,(function(e){i.push(parseInt(e,16))})),"rgba(".concat(i.join(","),", ").concat(t,")")):"rgba(255, 255, 255, 0.1)"},x.getFullScreenEl=function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement},x.checkIsFunction=function(e){return e&&"function"==typeof e},x.checkIsObject=function(e){return null!==e&&"object"===i(e)},x.hide=function(e){e.style.display="none"},x.show=function(e,t){e.style.display=t||"block"},x.isUndefined=function(e){if(null==e)return!0},x.isNotNull=function(e){return!(null==e)},x.setStyleFromCsstext=function(e,t){t&&("String"===x.typeOf(t)?t.replace(/\s+/g,"").split(";").map((function(t){if(t){var i=t.split(":");i.length>1&&(e.style[i[0]]=i[1])}})):Object.keys(t).map((function(i){e.style[i]=t[i]})))},x.filterStyleFromText=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["width","height","top","left","bottom","right","position","z-index","padding","margin","transform"],i=e.style.cssText;if(!i)return{};var n=i.replace(/\s+/g,"").split(";"),o={},r={};return n.map((function(e){if(e){var i=e.split(":");i.length>1&&(S(i[0],t)?o[i[0]]=i[1]:r[i[0]]=i[1])}})),e.setAttribute("style",""),Object.keys(r).map((function(t){e.style[t]=r[t]})),o},x.getStyleFromCsstext=function(e){var t=e.style.cssText;if(!t)return{};var i=t.replace(/\s+/g,"").split(";"),n={};return i.map((function(e){if(e){var t=e.split(":");t.length>1&&(n[t[0]]=t[1])}})),n},x.preloadImg=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(){},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){var n=new window.Image;n.onload=function(e){n=null,t&&t(e)},n.onerror=function(e){n=null,i&&i(e)},n.src=e}},x.stopPropagation=function(e){e&&e.stopPropagation()},x.scrollTop=function(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0},x.scrollLeft=function(){return window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0},x.checkTouchSupport=function(){return"ontouchstart"in window},x.getBuffered2=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5,i=[],n=0;n<e.length;n++)i.push({start:e.start(n)<.5?0:e.start(n),end:e.end(n)});i.sort((function(e,t){var i=e.start-t.start;return i||t.end-e.end}));var o=[];if(t)for(var r=0;r<i.length;r++){var s=o.length;if(s){var a=o[s-1].end;i[r].start-a<t?i[r].end>a&&(o[s-1].end=i[r].end):o.push(i[r])}else o.push(i[r])}else o=i;return new T(o)},x.getEventPos=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e.touches&&e.touches.length>0&&(e=e.touches[0]),{x:e.x/t,y:e.y/t,clientX:e.clientX/t,clientY:e.clientY/t,offsetX:e.offsetX/t,offsetY:e.offsetY/t,pageX:e.pageX/t,pageY:e.pageY/t}},x.requestAnimationFrame=function(e){var t=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(t)return t(e)},x.getHostFromUrl=function(e){if("String"!==x.typeOf(e))return"";var t=e.split("/"),i="";return t.length>3&&t[2]&&(i=t[2]),i},x.cancelAnimationFrame=function(e){var t=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.cancelRequestAnimationFrame;t&&t(e)},x.isMSE=function(e){return!!(e&&e instanceof HTMLMediaElement)&&(/^blob/.test(e.currentSrc)||/^blob/.test(e.src))},x.isBlob=function(e){return"string"==typeof e&&/^blob/.test(e)},x.generateSessionId=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=(new Date).getTime();try{e=parseInt(e)}catch(n){e=0}t+=e,window.performance&&"function"==typeof window.performance.now&&(t+=parseInt(window.performance.now()));var i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var i=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"===e?i:3&i|8).toString(16)}));return i},x.createEvent=function(e){var t;return"function"==typeof window.Event?t=new Event(e):(t=document.createEvent("Event")).initEvent(e,!0,!0),t},x.adjustTimeByDuration=function(e,t,i){return t&&e&&(e>t||i&&e<t)?t:e},x.createPositionBar=function(e,t){var i=x.createDom("xg-bar","",{"data-index":-1},e);return t.appendChild(i),i},x.getTransformStyle=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{x:0,y:0,scale:1,rotate:0},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i={scale:"".concat(e.scale||1),translate:"".concat(e.x||0,"%, ").concat(e.y||0,"%"),rotate:"".concat(e.rotate||0,"deg")},n=Object.keys(i);return n.forEach((function(e){var n=new RegExp("".concat(e,"\\([^\\(]+\\)"),"g"),o="".concat(e,"(").concat(i[e],")");n.test(t)?(n.lastIndex=-1,t=t.replace(n,o)):t+="".concat(o," ")})),t},x.convertDeg=function(e){return Math.abs(e)<=1?360*e:e%360},x.getIndexByTime=function(e,t){var i=t.length,n=-1;if(i<1)return n;if(e<=t[0].end||i<2)n=0;else if(e>t[i-1].end)n=i-1;else for(var o=1;o<i;o++)if(e>t[o-1].end&&e<=t[o].end){n=o;break}return n},x.getOffsetCurrentTime=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,n=-1;if((n=i>=0&&i<t.length?i:x.getIndexByTime(e,t))<0)return-1;var o=t.length,r=t[n],s=r.start,a=r.end,l=r.cTime,c=r.offset;return e<s?l:e>=s&&e<=a?e-c:e>a&&n>=o-1?a:-1},x.getCurrentTimeByOffset=function(e,t){var i=-1;if(!t||t.length<0)return e;for(var n=0;n<t.length;n++)if(e<=t[n].duration){i=n;break}if(-1!==i){var o=t[i].start;return i-1<0?o+e:o+(e-t[i-1].duration)}return e};var I=/(Android)\s([\d.]+)/,L=/(Version)\/([\d.]+)/,A=["avc1.42E01E, mp4a.40.2","avc1.58A01E, mp4a.40.2","avc1.4D401E, mp4a.40.2","avc1.64001E, mp4a.40.2","avc1.42E01E","mp4v.20.8","avc1.42E01E, mp4a.40.2","avc1.58A01E, mp4a.40.2","avc1.4D401E, mp4a.40.2","avc1.64001E, mp4a.40.2","mp4v.20.8, mp4a.40.2","mp4v.20.240, mp4a.40.2"],O={get device(){return O.os.isPc?"pc":"mobile"},get browser(){if("undefined"==typeof navigator)return"";var e=navigator.userAgent.toLowerCase(),t={ie:/rv:([\d.]+)\) like gecko/,firefox:/firefox\/([\d.]+)/,chrome:/chrome\/([\d.]+)/,opera:/opera.([\d.]+)/,safari:/version\/([\d.]+).*safari/};return[].concat(Object.keys(t).filter((function(i){return t[i].test(e)})))[0]},get os(){if("undefined"==typeof navigator)return{};var e=navigator.userAgent,t=/(?:Windows Phone)/.test(e),i=/(?:SymbianOS)/.test(e)||t,n=/(?:Android)/.test(e),o=/(?:Firefox)/.test(e),r=/(?:iPad|PlayBook)/.test(e)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1,s=r||n&&!/(?:Mobile)/.test(e)||o&&/(?:Tablet)/.test(e),a=/(?:iPhone)/.test(e)&&!s;return{isTablet:s,isPhone:a,isIpad:r,isIos:a||r,isAndroid:n,isPc:!(a||n||i||s),isSymbian:i,isWindowsPhone:t,isFireFox:o}},get osVersion(){if("undefined"==typeof navigator)return 0;var e=navigator.userAgent,t="",i=(t=/(?:iPhone)|(?:iPad|PlayBook)/.test(e)?L:I)?t.exec(e):[];if(i&&i.length>=3){var n=i[2].split(".");return n.length>0?parseInt(n[0]):0}return 0},get isWeixin(){if("undefined"==typeof navigator)return!1;return!!/(micromessenger)\/([\d.]+)/.exec(navigator.userAgent.toLocaleLowerCase())},isSupportMP4:function(){var e={isSupport:!1,mime:""};if("undefined"==typeof document)return e;if(this.supportResult)return this.supportResult;var t=document.createElement("video");return"function"==typeof t.canPlayType&&A.map((function(i){"probably"===t.canPlayType('video/mp4; codecs="'.concat(i,'"'))&&(e.isSupport=!0,e.mime+="||".concat(i))})),this.supportResult=e,t=null,e},isMSESupport:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if("undefined"==typeof MediaSource||!MediaSource)return!1;try{return MediaSource.isTypeSupported(e)}catch(t){return this._logger.error(e,t),!1}},isHevcSupported:function(){return!("undefined"==typeof MediaSource||!MediaSource.isTypeSupported)&&(MediaSource.isTypeSupported('video/mp4;codecs="hev1.1.6.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.2.4.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.3.E.L120.90"')||MediaSource.isTypeSupported('video/mp4;codecs="hev1.4.10.L120.90"'))},probeConfigSupported:function(e){var t={supported:!1,smooth:!1,powerEfficient:!1};if(!e||"undefined"==typeof navigator)return Promise.resolve(t);if(navigator.mediaCapabilities&&navigator.mediaCapabilities.decodingInfo)return navigator.mediaCapabilities.decodingInfo(e);var i=e.video||{},n=e.audio||{};try{var o=MediaSource.isTypeSupported(i.contentType),r=MediaSource.isTypeSupported(n.contentType);return Promise.resolve({supported:o&&r,smooth:!1,powerEfficient:!1})}catch(s){return Promise.resolve(t)}}},D="3.0.16",R={1:"media",2:"media",3:"media",4:"media",5:"media",6:"media"},M={1:5101,2:5102,3:5103,4:5104,5:5105,6:5106},j=r((function e(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{errorType:"",errorCode:0,errorMessage:"",originError:"",ext:{},mediaError:null};n(this,e);var o=t&&t.i18n?t.i18n.ERROR_TYPES:null;if(t.media){var r=i.mediaError?i.mediaError:t.media.error||{},s=t.duration,a=t.currentTime,l=t.ended,c=t.src,u=t.currentSrc,h=t.media,d=h.readyState,f=h.networkState,p=i.errorCode||r.code;M[p]&&(p=M[p]);var g={playerVersion:D,currentTime:a,duration:s,ended:l,readyState:d,networkState:f,src:c||u,errorType:i.errorType,errorCode:p,message:i.errorMessage||r.message,mediaError:r,originError:i.originError?i.originError.stack:"",host:x.getHostFromUrl(c||u)};return i.ext&&Object.keys(i.ext).map((function(e){g[e]=i.ext[e]})),g}if(arguments.length>1){for(var v={playerVersion:D,domain:document.domain},y=["errorType","currentTime","duration","networkState","readyState","src","currentSrc","ended","errd","errorCode","mediaError"],m=0;m<arguments.length;m++)v[y[m]]=arguments[m];return v.ex=o?(o[arguments[0]]||{}).msg:"",v}})),N="play",F="playing",H="ended",B="pause",U="error",V="seeking",W="seeked",G="timeupdate",z="waiting",K="canplay",q="durationchange",Y="volumechange",X="loadeddata",Z="ratechange",J="progress",$="loadstart",Q="emptied",ee="focus",te="blur",ie="ready",ne="urlNull",oe="autoplay_started",re="autoplay_was_prevented",se="complete",ae="replay",le="destroy",ce="urlchange",ue="download_speed_change",he="leaveplayer",de="enterplayer",fe="loading",pe="fullscreen_change",ge="cssFullscreen_change",ve="mini_state_change",ye="definition_change",me="after_definition_change",ke="video_resize",be="pip_change",Ce="rotate",_e="screenShot",we="playnext",Te="shortcut",xe="xglog",Se="user_action",Ee="reset",Pe="source_error",Ie="source_success",Le=["play","playing","ended","pause","error","seeking","seeked","timeupdate","waiting","canplay","canplaythrough","durationchange","volumechange","loadeddata","loadedmetadata","ratechange","progress","loadstart","emptied","stalled","suspend","abort","lowdecode"],Ae={STATS_INFO:"stats_info",STATS_DOWNLOAD:"stats_download",STATS_RESET:"stats_reset"},Oe="fps_stuck",De=Object.freeze(Object.defineProperty({__proto__:null,PLAY:N,PLAYING:F,ENDED:H,PAUSE:B,ERROR:U,SEEKING:V,SEEKED:W,TIME_UPDATE:G,WAITING:z,CANPLAY:K,CANPLAY_THROUGH:"canplaythrough",DURATION_CHANGE:q,VOLUME_CHANGE:Y,LOADED_DATA:X,LOADED_METADATA:"loadedmetadata",RATE_CHANGE:Z,PROGRESS:J,LOAD_START:$,EMPTIED:Q,STALLED:"stalled",SUSPEND:"suspend",ABORT:"abort",BUFFER_CHANGE:"bufferedChange",PLAYER_FOCUS:ee,PLAYER_BLUR:te,READY:ie,URL_NULL:ne,AUTOPLAY_STARTED:oe,AUTOPLAY_PREVENTED:re,COMPLETE:se,REPLAY:ae,DESTROY:le,URL_CHANGE:ce,DOWNLOAD_SPEED_CHANGE:ue,LEAVE_PLAYER:he,ENTER_PLAYER:de,LOADING:fe,FULLSCREEN_CHANGE:pe,CSS_FULLSCREEN_CHANGE:ge,MINI_STATE_CHANGE:ve,DEFINITION_CHANGE:ye,BEFORE_DEFINITION_CHANGE:"before_definition_change",AFTER_DEFINITION_CHANGE:me,SEI_PARSED:"SEI_PARSED",RETRY:"retry",VIDEO_RESIZE:ke,PIP_CHANGE:be,ROTATE:Ce,SCREEN_SHOT:_e,PLAYNEXT:we,SHORTCUT:Te,XGLOG:xe,USER_ACTION:Se,RESET:Ee,SOURCE_ERROR:Pe,SOURCE_SUCCESS:Ie,SWITCH_SUBTITLE:"switch_subtitle",VIDEO_EVENTS:Le,STATS_EVENTS:Ae,FPS_STUCK:Oe},Symbol.toStringTag,{value:"Module"}));function Re(e,t){this&&this.emit&&("error"===e?this.errorHandler(e,t.error):this.emit(e,t))}function Me(e,t){return function(i,n){var o={player:t,eventName:e,originalEvent:i,detail:i.detail||{},timeStamp:i.timeStamp,currentTime:t.currentTime,duration:t.duration,paused:t.paused,ended:t.ended,isInternalOp:!!t._internalOp[i.type],muted:t.muted,volume:t.volume,host:x.getHostFromUrl(t.currentSrc),vtype:t.vtype};if(t.removeInnerOP(i.type),"timeupdate"===e&&(t._currentTime=t.media&&t.media.currentTime),"ratechange"===e){var r=t.media?t.media.playbackRate:0;if(r&&t._rate===r)return;t._rate=t.media&&t.media.playbackRate}if("durationchange"===e&&(t._duration=t.media.duration),"volumechange"===e&&(o.isMutedChange=t._lastMuted!==t.muted,t._lastMuted=t.muted),"error"===e&&(o.error=n||t.video.error),t.mediaEventMiddleware[e]){var s=Re.bind(t,e,o);try{t.mediaEventMiddleware[e].call(t,o,s)}catch(a){throw Re.call(t,e,o),a}}else Re.call(t,e,o)}}var je=function(e){a(i,e);var t=d(i);function i(e){var o;n(this,i),(o=t.call(this,e))._hasStart=!1,o._currentTime=0,o._duration=0,o._internalOp={},o._lastMuted=!1,o.vtype="MP4",o._rate=-1,o.mediaConfig=Object.assign({},{controls:!1,autoplay:e.autoplay,playsinline:e.playsinline,"x5-playsinline":e.playsinline,"webkit-playsinline":e.playsinline,"x5-video-player-fullscreen":e["x5-video-player-fullscreen"]||e.x5VideoPlayerFullscreen,"x5-video-orientation":e["x5-video-orientation"]||e.x5VideoOrientation,airplay:e.airplay,"webkit-airplay":e.airplay,tabindex:0|e.tabindex,mediaType:e.mediaType||"video","data-index":-1},e.videoConfig,e.videoAttributes);var r=e["x5-video-player-type"]||e.x5VideoPlayerType;return O.isWeixin&&O.os.isAndroid&&r&&(o.mediaConfig["x5-video-player-type"]=r,delete o.mediaConfig.playsinline,delete o.mediaConfig["webkit-playsinline"],delete o.mediaConfig["x5-playsinline"]),e.loop&&(o.mediaConfig.loop="loop"),e.autoplayMuted&&!Object.prototype.hasOwnProperty.call(o.mediaConfig,"muted")&&(o.mediaConfig.muted=!0),o.media=x.createDom(o.mediaConfig.mediaType,"",o.mediaConfig,""),e.defaultPlaybackRate&&(o.media.defaultPlaybackRate=o.media.playbackRate=e.defaultPlaybackRate),"Number"===x.typeOf(e.volume)&&(o.volume=e.volume),e.autoplayMuted&&(o.media.muted=!0,o._lastMuted=!0),e.autoplay&&(o.media.autoplay=!0),o._interval={},o.mediaEventMiddleware={},o.attachVideoEvents(),o}return r(i,[{key:"setEventsMiddleware",value:function(e){var t=this;Object.keys(e).map((function(i){t.mediaEventMiddleware[i]=e[i]}))}},{key:"removeEventsMiddleware",value:function(e){var t=this;Object.keys(e).map((function(e){delete t.mediaEventMiddleware[e]}))}},{key:"attachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers||(this._evHandlers=Le.map((function(t){var i="on".concat(t.charAt(0).toUpperCase()).concat(t.slice(1));return"function"==typeof e[i]&&e.on(t,e[i]),s({},t,Me(t,e))}))),this._evHandlers.forEach((function(e){var i=Object.keys(e)[0];t.addEventListener(i,e[i],!1)}))}},{key:"detachVideoEvents",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.media;this._evHandlers.forEach((function(e){var i=Object.keys(e)[0];t.removeEventListener(i,e[i],!1)})),this._evHandlers.forEach((function(t){var i=Object.keys(t)[0],n="on".concat(i.charAt(0).toUpperCase()).concat(i.slice(1));"function"==typeof e[n]&&e.off(i,e[n])})),this._evHandlers=null}},{key:"_attachSourceEvents",value:function(e,t){var i=this;e.removeAttribute("src"),e.load(),t.forEach((function(e,t){i.media.appendChild(x.createDom("source","",{src:"".concat(e.src),type:"".concat(e.type||""),"data-index":t+1}))}));var n=e.children;if(n){this._videoSourceCount=n.length,this._videoSourceIndex=n.length,this._vLoadeddata=function(e){i.emit(Ie,{src:e.target.currentSrc,host:x.getHostFromUrl(e.target.currentSrc)})};for(var o=null,r=0;r<this._evHandlers.length;r++)if("error"===Object.keys(this._evHandlers[r])[0]){o=this._evHandlers[r];break}!this._sourceError&&(this._sourceError=function(e){var t=parseInt(e.target.getAttribute("data-index"),10);if(i._videoSourceIndex--,0===i._videoSourceIndex||t>=i._videoSourceCount){var n={code:4,message:"sources_load_error"};o?o.error(e,n):i.errorHandler("error",n)}var r=R[4];i.emit(Pe,new j(i,{errorType:r,errorCode:4,errorMessage:"sources_load_error",mediaError:{code:4,message:"sources_load_error"},src:e.target.src}))});for(var s=0;s<n.length;s++)n[s].addEventListener("error",this._sourceError);e.addEventListener("loadeddata",this._vLoadeddata)}}},{key:"_detachSourceEvents",value:function(e){var t=e.children;if(t&&0!==t.length&&this._sourceError){for(var i=0;i<t.length;i++)t[i].removeEventListener("error",this._sourceError);for(;t.length>0;)e.removeChild(t[0]);this._vLoadeddata&&e.removeEventListener("loadeddata",this._vLoadeddata)}}},{key:"errorHandler",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.media&&(this.media.error||t)){var i=this.media.error||t,n=i.code?R[i.code]:"other";i.message;this.media.currentSrc||(i={code:6,message:"empty_src"}),this.emit(e,new j(this,{errorType:n,errorCode:i.code,errorMessage:i.message||"",mediaError:i}))}}},{key:"destroy",value:function(){for(var e in this.media&&(this.media.pause&&(this.media.pause(),this.media.muted=!0),this.media.removeAttribute("src"),this.media.load()),this._currentTime=0,this._duration=0,this.mediaConfig=null,this._interval)Object.prototype.hasOwnProperty.call(this._interval,e)&&(clearInterval(this._interval[e]),this._interval[e]=null);this.detachVideoEvents(),this.media=null,this.mediaEventMiddleware={},this.removeAllListeners()}},{key:"video",get:function(){return this.media},set:function(e){this.media=e}},{key:"play",value:function(){return this.media?this.media.play():null}},{key:"pause",value:function(){this.media&&this.media.pause()}},{key:"load",value:function(){this.media&&this.media.load()}},{key:"canPlayType",value:function(e){return!!this.media&&this.media.canPlayType(e)}},{key:"getBufferedRange",value:function(e){var t=[0,0];if(!this.media)return t;e||(e=this.media.buffered);var i=this.media.currentTime;if(e)for(var n=0,o=e.length;n<o&&(t[0]=e.start(n),t[1]=e.end(n),!(t[0]<=i&&i<=t[1]));n++);return t[0]-i<=0&&i-t[1]<=0?t:[0,0]}},{key:"autoplay",get:function(){return!!this.media&&this.media.autoplay},set:function(e){this.media&&(this.media.autoplay=e)}},{key:"buffered",get:function(){return this.media?this.media.buffered:null}},{key:"buffered2",get:function(){return this.media&&this.media.buffered?x.getBuffered2(this.media.buffered):null}},{key:"bufferedPoint",get:function(){var e={start:0,end:0};if(!this.media)return e;var t=this.media.buffered;if(!t||0===t.length)return e;for(var i=0;i<t.length;i++)if((t.start(i)<=this.currentTime||t.start(i)<.1)&&t.end(i)>=this.currentTime)return{start:t.start(i),end:t.end(i)};return e}},{key:"crossOrigin",get:function(){return this.media?this.media.crossOrigin:""},set:function(e){this.media&&(this.media.crossOrigin=e)}},{key:"currentSrc",get:function(){return this.media?this.media.currentSrc:""},set:function(e){this.media&&(this.media.currentSrc=e)}},{key:"currentTime",get:function(){return this.media?void 0!==this.media.currentTime?this.media.currentTime:this._currentTime:0},set:function(e){this.media&&(this.media.currentTime=e)}},{key:"defaultMuted",get:function(){return!!this.media&&this.media.defaultMuted},set:function(e){this.media&&(this.media.defaultMuted=e)}},{key:"duration",get:function(){return this._duration}},{key:"ended",get:function(){return!!this.media&&this.media.ended}},{key:"error",get:function(){return this.media.error}},{key:"errorNote",get:function(){if(!this.media.error)return"";return["MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED"][this.media.error.code-1]}},{key:"loop",get:function(){return!!this.media&&this.media.loop},set:function(e){this.media&&(this.media.loop=e)}},{key:"muted",get:function(){return!!this.media&&this.media.muted},set:function(e){this.media&&this.media.muted!==e&&(this._lastMuted=this.media.muted,this.media.muted=e)}},{key:"networkState",get:function(){return this.media.networkState}},{key:"paused",get:function(){return!this.media||this.media.paused}},{key:"playbackRate",get:function(){return this.media?this.media.playbackRate:0},set:function(e){this.media&&e!==1/0&&(this.media.defaultPlaybackRate=e,this.media.playbackRate=e)}},{key:"played",get:function(){return this.media?this.media.played:null}},{key:"preload",get:function(){return!!this.media&&this.media.preload},set:function(e){this.media&&(this.media.preload=e)}},{key:"readyState",get:function(){return this.media.readyState}},{key:"seekable",get:function(){return!!this.media&&this.media.seekable}},{key:"seeking",get:function(){return!!this.media&&this.media.seeking}},{key:"src",get:function(){return this.media?this.media.src:""},set:function(e){this.media&&(this.emit(ce,e),this.emit(z),this._currentTime=0,this._duration=0,x.isMSE(this.media)?this.onWaiting():(this._detachSourceEvents(this.media),"Array"===x.typeOf(e)?this._attachSourceEvents(this.media,e):e?this.media.src=e:this.media.removeAttribute("src"),this.load()))}},{key:"volume",get:function(){return this.media?this.media.volume:0},set:function(e){e!==1/0&&this.media&&(this.media.volume=e)}},{key:"aspectRatio",get:function(){return this.media?this.media.videoWidth/this.media.videoHeight:0}},{key:"addInnerOP",value:function(e){this._internalOp[e]=!0}},{key:"removeInnerOP",value:function(e){delete this._internalOp[e]}},{key:"emit",value:function(e,t){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=p(l(i.prototype),"emit",this)).call.apply(n,[this,e,t].concat(r))}},{key:"on",value:function(e,t){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=p(l(i.prototype),"on",this)).call.apply(n,[this,e,t].concat(r))}},{key:"once",value:function(e,t){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=p(l(i.prototype),"once",this)).call.apply(n,[this,e,t].concat(r))}},{key:"off",value:function(e,t){for(var n,o=arguments.length,r=new Array(o>2?o-2:0),s=2;s<o;s++)r[s-2]=arguments[s];(n=p(l(i.prototype),"off",this)).call.apply(n,[this,e,t].concat(r))}},{key:"offAll",value:function(){p(l(i.prototype),"removeAllListeners",this).call(this)}}]),i}(k),Ne=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{name:"xgplayer",version:1,db:null,ojstore:{name:"xg-m4a",keypath:"vid"}};n(this,e),this.indexedDB=window.indexedDB||window.webkitindexedDB,this.IDBKeyRange=window.IDBKeyRange||window.webkitIDBKeyRange,this.myDB=t}return r(e,[{key:"openDB",value:function(e){var t=this,i=this,n=this.myDB.version||1,o=i.indexedDB.open(i.myDB.name,n);o.onerror=function(e){},o.onsuccess=function(n){t.myDB.db=n.target.result,e.call(i)},o.onupgradeneeded=function(e){var t=e.target.result;e.target.transaction,t.objectStoreNames.contains(i.myDB.ojstore.name)||t.createObjectStore(i.myDB.ojstore.name,{keyPath:i.myDB.ojstore.keypath})}}},{key:"deletedb",value:function(){this.indexedDB.deleteDatabase(this.myDB.name)}},{key:"closeDB",value:function(){this.myDB.db.close()}},{key:"addData",value:function(e,t){for(var i,n=this.myDB.db.transaction(e,"readwrite").objectStore(e),o=0;o<t.length;o++)(i=n.add(t[o])).onerror=function(){},i.onsuccess=function(){}}},{key:"putData",value:function(e,t){for(var i,n=this.myDB.db.transaction(e,"readwrite").objectStore(e),o=0;o<t.length;o++)(i=n.put(t[o])).onerror=function(){},i.onsuccess=function(){}}},{key:"getDataByKey",value:function(e,t,i){var n=this,o=this.myDB.db.transaction(e,"readwrite").objectStore(e).get(t);o.onerror=function(){i.call(n,null)},o.onsuccess=function(e){var t=e.target.result;i.call(n,t)}}},{key:"deleteData",value:function(e,t){this.myDB.db.transaction(e,"readwrite").objectStore(e).delete(t)}},{key:"clearData",value:function(e){this.myDB.db.transaction(e,"readwrite").objectStore(e).clear()}}]),e}(),Fe=["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],He=["requestFullscreen","webkitRequestFullscreen","mozRequestFullScreen","msRequestFullscreen"],Be=["exitFullscreen","webkitExitFullscreen","mozCancelFullScreen","msExitFullscreen"],Ue="data-xgplayerid";function Ve(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];var s=t.call.apply(t,[e].concat(o));i&&"function"==typeof i&&(s&&s.then?s.then((function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];i.call.apply(i,[e].concat(n))})):i.call.apply(i,[e].concat(o)))}function We(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{pre:null,next:null};return this.__hooks||(this.__hooks={}),!this.__hooks[e]&&(this.__hooks[e]=null),function(){var n=arguments,o=this;if(i.pre)try{var r;(r=i.pre).call.apply(r,[this].concat(Array.prototype.slice.call(arguments)))}catch(l){throw l.message="[pluginName: ".concat(this.pluginName,":").concat(e,":pre error] >> ").concat(l.message),l}if(this.__hooks&&this.__hooks[e])try{var s,a=(s=this.__hooks[e]).call.apply(s,[this,this].concat(Array.prototype.slice.call(arguments)));a?a.then?a.then((function(e){!1!==e&&Ve.apply(void 0,[o,t,i.next].concat(g(n)))})).catch((function(e){throw e})):Ve.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments))):void 0===a&&Ve.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments)))}catch(l){throw l.message="[pluginName: ".concat(this.pluginName,":").concat(e,"] >> ").concat(l.message),l}else Ve.apply(void 0,[this,t,i.next].concat(Array.prototype.slice.call(arguments)))}.bind(this)}function Ge(e,t){var i=this.__hooks;if(i)return i.hasOwnProperty(e)?(i&&(i[e]=t),!0):(console.warn("has no supported hook which name [".concat(e,"]")),!1)}function ze(e,t){var i=this.__hooks;i&&delete i[e]}function Ke(e){if(this.plugins&&this.plugins[e.toLowerCase()]){for(var t=this.plugins[e.toLowerCase()],i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return t.useHooks&&t.useHooks.apply(t,n)}}function qe(e){if(this.plugins&&this.plugins[e.toLowerCase()]){var t=this.plugins[e.toLowerCase()];if(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];return t.removeHooks&&t.removeHooks.apply(t,n)}}}function Ye(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];e.__hooks={},t&&t.map((function(t){e.__hooks[t]=null})),Object.defineProperty(e,"hooks",{get:function(){return e.__hooks&&Object.keys(e.__hooks).map((function(t){if(e.__hooks[t])return t}))}})}function Xe(e){e.__hooks=null}function Ze(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];if(!e.__hooks||!e.__hooks[t])return i.call.apply(i,[e,e].concat(o));var s,a=(s=e.__hooks[t]).call.apply(s,[e,e].concat(o));if(a&&a.then)a.then((function(t){return!1===t?null:i.call.apply(i,[e,e].concat(o))})).catch((function(e){console.warn("[runHooks]".concat(t," reject"),e.message)}));else if(!1!==a)return i.call.apply(i,[e,e].concat(o))}function Je(e,t){w.logError("[".concat(e,"] event or callback cant be undefined or null when call ").concat(t))}var $e=function(){function e(t){n(this,e),x.checkIsFunction(this.beforeCreate)&&this.beforeCreate(t),Ye(this),this.__args=t,this.__events={},this.__onceEvents={},this.config=t.config||{},this.player=null,this.playerConfig={},this.pluginName="",this.__init(t)}return r(e,[{key:"beforeCreate",value:function(e){}},{key:"afterCreate",value:function(){}},{key:"beforePlayerInit",value:function(){}},{key:"onPluginsReady",value:function(){}},{key:"afterPlayerInit",value:function(){}},{key:"destroy",value:function(){}},{key:"__init",value:function(e){this.player=e.player,this.playerConfig=e.player&&e.player.config,this.pluginName=e.pluginName?e.pluginName.toLowerCase():this.constructor.pluginName.toLowerCase(),this.logger=e.player&&e.player.logger}},{key:"updateLang",value:function(e){e||(e=this.lang)}},{key:"lang",get:function(){return this.player.lang}},{key:"i18n",get:function(){return this.player.i18n}},{key:"i18nKeys",get:function(){return this.player.i18nKeys}},{key:"domEventType",get:function(){var e=x.checkTouchSupport()?"touch":"mouse";return!this.playerConfig||"touch"!==this.playerConfig.domEventType&&"mouse"!==this.playerConfig.domEventType||(e=this.playerConfig.domEventType),e}},{key:"on",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(this.__events[e]=t,this.player.on(e,t)):Array.isArray(e)&&e.forEach((function(e){i.__events[e]=t,i.player.on(e,t)})):Je(this.pluginName,"plugin.on(event, callback)")}},{key:"once",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(this.__onceEvents[e]=t,this.player.once(e,t)):Array.isArray(e)&&e.forEach((function(n){i.__onceEvents[n]=t,i.player.once(e,t)})):Je(this.pluginName,"plugin.once(event, callback)")}},{key:"off",value:function(e,t){var i=this;e&&t&&this.player?"string"==typeof e?(delete this.__events[e],this.player.off(e,t)):Array.isArray(e)&&e.forEach((function(n){delete i.__events[e],i.player.off(n,t)})):Je(this.pluginName,"plugin.off(event, callback)")}},{key:"offAll",value:function(){var e=this;["__events","__onceEvents"].forEach((function(t){Object.keys(e[t]).forEach((function(i){e[t][i]&&e.off(i,e[t][i]),i&&delete e[t][i]}))})),this.__events={},this.__onceEvents={}}},{key:"emit",value:function(e){var t;if(this.player){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];(t=this.player).emit.apply(t,[e].concat(n))}}},{key:"emitUserAction",value:function(e,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.player){var o=t(t({},n),{},{pluginName:this.pluginName});this.player.emitUserAction(e,i,o)}}},{key:"hook",value:function(e,t){return We.call.apply(We,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(e,t){for(var i=arguments.length,n=new Array(i>2?i-2:0),o=2;o<i;o++)n[o-2]=arguments[o];return Ge.call.apply(Ge,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(e,t){for(var i=arguments.length,n=new Array(i>2?i-2:0),o=2;o<i;o++)n[o-2]=arguments[o];return ze.call.apply(ze,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(this.player)return i&&(t.pluginName=i),this.player.registerPlugin({plugin:e,options:t})}},{key:"getPlugin",value:function(e){return this.player?this.player.getPlugin(e):null}},{key:"__destroy",value:function(){var e=this,t=this.player,i=this.pluginName;this.offAll(),x.clearAllTimers(this),x.checkIsFunction(this.destroy)&&this.destroy(),["player","playerConfig","pluginName","logger","__args","__hooks"].map((function(t){e[t]=null})),t.unRegisterPlugin(i),Xe(this)}}],[{key:"defineGetterOrSetter",value:function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&Object.defineProperty(e,i,t[i])}},{key:"defaultConfig",get:function(){return{}}},{key:"pluginName",get:function(){return"pluginName"}}]),e}();if("undefined"!=typeof Element&&!Element.prototype.matches){var Qe=Element.prototype;Qe.matches=Qe.matchesSelector||Qe.mozMatchesSelector||Qe.msMatchesSelector||Qe.oMatchesSelector||Qe.webkitMatchesSelector}var et=function(e,t){for(;e&&9!==e.nodeType;){if("function"==typeof e.matches&&e.matches(t))return e;e=e.parentNode}};function tt(e,t,i,n,o){var r=it.apply(this,arguments);return e.addEventListener(i,r,o),{destroy:function(){e.removeEventListener(i,r,o)}}}function it(e,t,i,n){return function(i){i.delegateTarget=et(i.target,t),i.delegateTarget&&n.call(e,i)}}var nt=function(e,t,i,n,o){return"function"==typeof e.addEventListener?tt.apply(null,arguments):"function"==typeof i?tt.bind(null,document).apply(null,arguments):("string"==typeof e&&(e=document.querySelectorAll(e)),Array.prototype.map.call(e,(function(e){return tt(e,t,i,n,o)})))},ot={CONTROLS:"controls",ROOT:"root"},rt={ROOT:"root",ROOT_LEFT:"rootLeft",ROOT_RIGHT:"rootRight",ROOT_TOP:"rootTop",CONTROLS_LEFT:"controlsLeft",CONTROLS_RIGTH:"controlsRight",CONTROLS_RIGHT:"controlsRight",CONTROLS_CENTER:"controlsCenter",CONTROLS:"controls"};function st(e){return!!e&&(e.indexOf&&/^(?:http|data:|\/)/.test(e))}function at(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",r=null;if(e instanceof window.Element)return x.addClass(e,i),Object.keys(n).map((function(t){e.setAttribute(t,n[t])})),e;if(st(e)||st(e.url))return n.src=st(e)?e:e.url||"",r=x.createDom(e.tag||"img","",n,"xg-img ".concat(i));if("function"==typeof e)try{return(r=e())instanceof window.Element?(x.addClass(r,i),Object.keys(n).map((function(e){r.setAttribute(e,n[e])})),r):(w.logWarn("warn>>icons.".concat(t," in config of plugin named [").concat(o,"] is a function mast return an Element Object")),null)}catch(s){return w.logError("Plugin named [".concat(o,"]:createIcon"),s),null}return"string"==typeof e?x.createDomFromHtml(e,n,i):(w.logWarn("warn>>icons.".concat(t," in config of plugin named [").concat(o,"] is invalid")),null)}function lt(e,t){var n=t.config.icons||t.playerConfig.icons;Object.keys(e).map((function(o){var r=e[o],s=r&&r.class?r.class:"",a=r&&r.attr?r.attr:{},l=null;n&&n[o]&&(s=function(e,t){return"object"===i(e)&&e.class&&"string"==typeof e.class?"".concat(t," ").concat(e.class):t}(n[o],s),a=function(e,t){return"object"===i(e)&&e.attr&&"object"===i(e.attr)&&Object.keys(e.attr).map((function(i){t[i]=e.attr[i]})),t}(n[o],a),l=at(n[o],o,s,a,t.pluginName)),!l&&r&&(l=at(r.icon?r.icon:r,a,s,{},t.pluginName)),t.icons[o]=l}))}var ct=function(e){a(o,e);var t=d(o);function o(){var e,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n(this,o),(e=t.call(this,i)).__delegates=[],e}return r(o,[{key:"__init",value:function(e){if(p(l(o.prototype),"__init",this).call(this,e),e.root){var t=e.root,i=null;this.icons={},this.root=null,this.parent=null,lt(this.registerIcons()||{},this),this.langText={};var n,r,s=this.registerLanguageTexts()||{};n=s,r=this,Object.keys(n).map((function(e){Object.defineProperty(r.langText,e,{get:function(){var t=r.lang,i=r.i18n;return i[e]?i[e]:n[e]&&n[e][t]||""}})}));var a="";try{a=this.render()}catch(h){throw w.logError("Plugin:".concat(this.pluginName,":render"),h),new Error("Plugin:".concat(this.pluginName,":render:").concat(h.message))}if(a)(i=o.insert(a,t,e.index)).setAttribute("data-index",e.index);else{if(!e.tag)return;(i=x.createDom(e.tag,"",e.attr,e.name)).setAttribute("data-index",e.index),t.appendChild(i)}this.root=i,this.parent=t;var c=this.config.attr||{},u=this.config.style||{};this.setAttr(c),this.setStyle(u),this.config.index&&this.root.setAttribute("data-index",this.config.index),this.__registerChildren()}}},{key:"__registerChildren",value:function(){var e=this;if(this.root){this._children=[];var t=this.children();t&&"object"===i(t)&&Object.keys(t).length>0&&Object.keys(t).map((function(n){var o,r,s=n,a=t[s],l={root:e.root};"function"==typeof a?(o=e.config[s]||{},r=a):"object"===i(a)&&"function"==typeof a.plugin&&(o=a.options?x.deepCopy(e.config[s]||{},a.options):e.config[s]||{},r=a.plugin),l.config=o,void 0!==o.index&&(l.index=o.index),o.root&&(l.root=o.root),e.registerPlugin(r,l,s)}))}}},{key:"updateLang",value:function(e){e||(e=this.lang);var t=this.root,i=this.i18n,n=this.langText;t&&function e(t,i){for(var n=0;n<t.children.length;n++)t.children[n].children.length>0?e(t.children[n],i):i(t.children[n])}(t,(function(t){var o=t.getAttribute&&t.getAttribute("lang-key");if(o){var r=i[o.toUpperCase()]||n[o];r&&(t.innerHTML="function"==typeof r?r(e):r)}}))}},{key:"lang",get:function(){return this.player.lang}},{key:"changeLangTextKey",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=this.i18n||{},n=this.langText;e.setAttribute&&e.setAttribute("lang-key",t);var o=i[t]||n[t]||"";o&&(e.innerHTML=o)}},{key:"plugins",value:function(){return this._children}},{key:"children",value:function(){return{}}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";t.root=t.root||this.root;var n=p(l(o.prototype),"registerPlugin",this).call(this,e,t,i);return this._children.push(n),n}},{key:"registerIcons",value:function(){return{}}},{key:"registerLanguageTexts",value:function(){return{}}},{key:"find",value:function(e){if(this.root)return this.root.querySelector(e)}},{key:"bind",value:function(e,t,i){var n=this;if(arguments.length<3&&"function"==typeof t)Array.isArray(e)?e.forEach((function(e){n.bindEL(e,t)})):this.bindEL(e,t);else{var r=o.delegate.call(this,this.root,e,t,i);this.__delegates=this.__delegates.concat(r)}}},{key:"unbind",value:function(e,t){var i=this;if(arguments.length<3&&"function"==typeof t)Array.isArray(e)?e.forEach((function(e){i.unbindEL(e,t)})):this.unbindEL(e,t);else for(var n="".concat(e,"_").concat(t),o=0;o<this.__delegates.length;o++)if(this.__delegates[o].key===n){this.__delegates[o].destroy(),this.__delegates.splice(o,1);break}}},{key:"setStyle",value:function(e,t){var i=this;if(this.root)return"String"===x.typeOf(e)?this.root.style[e]=t:void("Object"===x.typeOf(e)&&Object.keys(e).map((function(t){i.root.style[t]=e[t]})))}},{key:"setAttr",value:function(e,t){var i=this;if(this.root)return"String"===x.typeOf(e)?this.root.setAttribute(e,t):void("Object"===x.typeOf(e)&&Object.keys(e).map((function(t){i.root.setAttribute(t,e[t])})))}},{key:"setHtml",value:function(e,t){this.root&&(this.root.innerHTML=e,"function"==typeof t&&t())}},{key:"bindEL",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.addEventListener(e,t,i)}},{key:"unbindEL",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.root&&"on".concat(e)in this.root&&"function"==typeof t&&this.root.removeEventListener(e,t,i)}},{key:"show",value:function(e){this.root&&(this.root.style.display=void 0!==e?e:"block","none"===window.getComputedStyle(this.root,null).getPropertyValue("display")&&(this.root.style.display="block"))}},{key:"hide",value:function(){this.root&&(this.root.style.display="none")}},{key:"appendChild",value:function(e,t){if(!this.root)return null;if(arguments.length<2&&arguments[0]instanceof window.Element)return this.root.appendChild(arguments[0]);if(!(t&&t instanceof window.Element))return null;try{return"string"==typeof e?this.find(e).appendChild(t):e.appendChild(t)}catch(i){return w.logError("Plugin:appendChild",i),null}}},{key:"render",value:function(){return""}},{key:"destroy",value:function(){}},{key:"__destroy",value:function(){var e=this,t=this.player;this.__delegates.map((function(e){e.destroy()})),this.__delegates=[],this._children instanceof Array&&(this._children.map((function(e){t.unRegisterPlugin(e.pluginName)})),this._children=null),this.root&&(this.root.hasOwnProperty("remove")?this.root.remove():this.root.parentNode&&this.root.parentNode.removeChild(this.root)),p(l(o.prototype),"__destroy",this).call(this),this.icons={},["root","parent"].map((function(t){e[t]=null}))}}],[{key:"insert",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=t.children.length,o=Number(i),r=e instanceof window.Node;if(n){for(var s=0,a=null,l="";s<n;s++){a=t.children[s];var c=Number(a.getAttribute("data-index"));if(c>=o){l="beforebegin";break}c<o&&(l="afterend")}return r?"afterend"===l?t.appendChild(e):t.insertBefore(e,a):a.insertAdjacentHTML(l,e),"afterend"===l?t.children[t.children.length-1]:t.children[s]}return r?t.appendChild(e):t.insertAdjacentHTML("beforeend",e),t.children[t.children.length-1]}},{key:"defaultConfig",get:function(){return{}}},{key:"delegate",value:function(e,t,i,n){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],r=[];if(e instanceof window.Node&&"function"==typeof n)if(Array.isArray(i))i.forEach((function(i){var s=nt(e,t,i,n,o);s.key="".concat(t,"_").concat(i),r.push(s)}));else{var s=nt(e,t,i,n,o);s.key="".concat(t,"_").concat(i),r.push(s)}return r}},{key:"ROOT_TYPES",get:function(){return ot}},{key:"POSITIONS",get:function(){return rt}}]),o}($e),ut=function(){function e(){var t=this;if(n(this,e),s(this,"__trigger",(function(e){var i=(new Date).getTime();t.timeStamp=i;for(var n=0;n<e.length;n++)t.__runHandler(e[n].target)})),this.__handlers=[],this.timeStamp=0,this.observer=null,window.ResizeObserver)try{this.observer=new window.ResizeObserver(function(e,t,i){var n=!0,o=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return E(i)&&(n="leading"in i?!!i.leading:n,o="trailing"in i?!!i.trailing:o),P(e,t,{leading:n,trailing:o,maxWait:t})}(this.__trigger,100,{trailing:!0})),this.timeStamp=(new Date).getTime()}catch(i){console.error(i)}}return r(e,[{key:"addObserver",value:function(e,t){if(this.observer){this.observer&&this.observer.observe(e);for(var i=e.getAttribute(Ue),n=this.__handlers,o=-1,r=0;r<n.length;r++)n[r]&&e===n[r].target&&(o=r);o>-1?this.__handlers[o].handler=t:this.__handlers.push({target:e,handler:t,playerId:i})}}},{key:"unObserver",value:function(e){var t=-1;this.__handlers.map((function(i,n){e===i.target&&(t=n)}));try{this.observer&&this.observer.unobserve(e)}catch(i){}this.observer&&this.observer.unobserve(e),t>-1&&this.__handlers.splice(t,1)}},{key:"destroyObserver",value:function(){this.observer&&this.observer.disconnect(),this.observer=null,this.__handlers=null}},{key:"__runHandler",value:function(e){for(var t=this.__handlers,i=0;i<t.length;i++)if(t[i]&&e===t[i].target){try{t[i].handler(e)}catch(n){console.error(n)}return!0}return!1}}]),e}(),ht=null;var dt={pluginGroup:{},init:function(e){var t,i,n=e._pluginInfoId;n||(n=(new Date).getTime(),e._pluginInfoId=n),!e.config.closeResizeObserver&&(t=e.root,i=function(){e.resize()},ht||(ht=new ut),ht.addObserver(t,i)),this.pluginGroup[n]={_originalOptions:e.config||{},_plugins:{}}},formatPluginInfo:function(e,t){var i=null,n=null;return e.plugin&&"function"==typeof e.plugin?(i=e.plugin,n=e.options):(i=e,n={}),t&&(n.config=t||{}),{PLUFGIN:i,options:n}},checkPluginIfExits:function(e,t){for(var i=0;i<t.length;i++)if(e.toLowerCase()===t[i].pluginName.toLowerCase())return!0;return!1},getRootByConfig:function(e,t){for(var i=Object.keys(t),n=null,o=0;o<i.length;o++)if(e.toLowerCase()===i[o].toLowerCase()){n=t[i[o]];break}return"Object"===x.typeOf(n)?{root:n.root,position:n.position}:{}},lazyRegister:function(e,t){var i=this,n=t.timeout||1500;return Promise.race([t.loader().then((function(t){var n;n=t&&t.__esModule?t.default:t,i.register(e,n,t.options)})),new Promise((function(e,t){setTimeout((function(){t(new Error("timeout"))}),n)}))])},register:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e&&t&&"function"==typeof t&&void 0!==t.prototype){var n=e._pluginInfoId;if(n&&this.pluginGroup[n]){this.pluginGroup[n]._plugins||(this.pluginGroup[n]._plugins={});var o=this.pluginGroup[n]._plugins,r=this.pluginGroup[n]._originalOptions;i.player=e;var s=i.pluginName||t.pluginName;if(!s)throw new Error("The property pluginName is necessary");if(!t.isSupported||t.isSupported(e.config.mediaType,e.config.codecType)){i.config||(i.config={});for(var a=Object.keys(r),l=0;l<a.length;l++)if(s.toLowerCase()===a[l].toLowerCase()){var c=r[a[l]];"Object"===x.typeOf(c)?i.config=Object.assign({},i.config,r[a[l]]):"Boolean"===x.typeOf(c)&&(i.config.disable=!c);break}t.defaultConfig&&Object.keys(t.defaultConfig).forEach((function(e){void 0===i.config[e]&&(i.config[e]=t.defaultConfig[e])})),i.root?"string"==typeof i.root&&(i.root=e[i.root]):i.root=e.root,i.index=i.config.index||0;try{o[s.toLowerCase()]&&(this.unRegister(n,s.toLowerCase()),console.warn("the is one plugin with same pluginName [".concat(s,"] exist, destroy the old instance")));var u=new t(i);return o[s.toLowerCase()]=u,o[s.toLowerCase()].func=t,u&&"function"==typeof u.afterCreate&&u.afterCreate(),u}catch(h){throw console.error(h),h}}else console.warn("not supported plugin [".concat(s,"]"))}}},unRegister:function(e,t){e._pluginInfoId&&(e=e._pluginInfoId),t=t.toLowerCase();try{var i=this.pluginGroup[e]._plugins[t];i&&(i.pluginName&&i.__destroy(),delete this.pluginGroup[e]._plugins[t])}catch(n){console.error("[unRegister:".concat(t,"] cgid:[").concat(e,"] error"),n)}},deletePlugin:function(e,t){var i=e._pluginInfoId;i&&this.pluginGroup[i]&&this.pluginGroup[i]._plugins&&delete this.pluginGroup[i]._plugins[t]},getPlugins:function(e){var t=e._pluginInfoId;return t&&this.pluginGroup[t]?this.pluginGroup[t]._plugins:{}},findPlugin:function(e,t){var i=e._pluginInfoId;if(!i||!this.pluginGroup[i])return null;var n=t.toLowerCase();return this.pluginGroup[i]._plugins[n]},beforeInit:function(e){var t=this;function i(e){return e&&e.then?e:new Promise((function(e){e()}))}return new Promise((function(n){if(t.pluginGroup)return(e._loadingPlugins&&e._loadingPlugins.length?Promise.all(e._loadingPlugins):Promise.resolve()).then((function(){var o=e._pluginInfoId;if(t.pluginGroup[o]){var r=t.pluginGroup[o]._plugins,s=[];Object.keys(r).forEach((function(e){if(r[e]&&r[e].beforePlayerInit)try{var t=r[e].beforePlayerInit();s.push(i(t))}catch(n){throw s.push(i(null)),n}})),Promise.all([].concat(s)).then((function(){n()})).catch((function(e){console.error(e),n()}))}else n()}))}))},afterInit:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var i=this.pluginGroup[t]._plugins;Object.keys(i).forEach((function(e){i[e]&&i[e].afterPlayerInit&&i[e].afterPlayerInit()}))}},setLang:function(e,t){var i=t._pluginInfoId;if(i&&this.pluginGroup[i]){var n=this.pluginGroup[i]._plugins;Object.keys(n).forEach((function(t){if(n[t].updateLang)n[t].updateLang(e);else try{n[t].lang=e}catch(i){console.warn("".concat(t," setLang"))}}))}},reRender:function(e){var t=this,i=e._pluginInfoId;if(i&&this.pluginGroup[i]){var n=[],o=this.pluginGroup[i]._plugins;Object.keys(o).forEach((function(e){"controls"!==e&&o[e]&&(n.push({plugin:o[e].func,options:o[e].__args}),t.unRegister(i,e))})),n.forEach((function(i){t.register(e,i.plugin,i.options)}))}},onPluginsReady:function(e){var t=e._pluginInfoId;if(t&&this.pluginGroup[t]){var i=this.pluginGroup[t]._plugins||{};Object.keys(i).forEach((function(e){i[e].onPluginsReady&&"function"==typeof i[e].onPluginsReady&&i[e].onPluginsReady()}))}},destroy:function(e){var t=e._pluginInfoId;if(this.pluginGroup[t]){var i,n;i=e.root,ht.unObserver(i,n);for(var o=this.pluginGroup[t]._plugins,r=0,s=Object.keys(o);r<s.length;r++){var a=s[r];this.unRegister(t,a)}delete this.pluginGroup[t],delete e._pluginInfoId}}},ft={DEFAULT:"xgplayer",DEFAULT_SKIN:"xgplayer-skin-default",ENTER:"xgplayer-is-enter",PAUSED:"xgplayer-pause",PLAYING:"xgplayer-playing",ENDED:"xgplayer-ended",CANPLAY:"xgplayer-canplay",LOADING:"xgplayer-isloading",ERROR:"xgplayer-is-error",REPLAY:"xgplayer-replay",NO_START:"xgplayer-nostart",ACTIVE:"xgplayer-active",INACTIVE:"xgplayer-inactive",FULLSCREEN:"xgplayer-is-fullscreen",CSS_FULLSCREEN:"xgplayer-is-cssfullscreen",ROTATE_FULLSCREEN:"xgplayer-rotate-fullscreen",PARENT_ROTATE_FULLSCREEN:"xgplayer-rotate-parent",PARENT_FULLSCREEN:"xgplayer-fullscreen-parent",INNER_FULLSCREEN:"xgplayer-fullscreen-inner",NO_CONTROLS:"no-controls",FLEX_CONTROLS:"flex-controls",CONTROLS_FOLLOW:"controls-follow",CONTROLS_AUTOHIDE:"controls-autohide",TOP_BAR_AUTOHIDE:"top-bar-autohide",NOT_ALLOW_AUTOPLAY:"not-allow-autoplay",SEEKING:"seeking",PC:"xgplayer-pc",MOBILE:"xgplayer-mobile",MINI:"xgplayer-mini"};function pt(){return{id:"",el:null,url:"",domEventType:"default",nullUrlStart:!1,width:600,height:337.5,fluid:!1,fitVideoSize:"fixed",videoFillMode:"auto",volume:.6,autoplay:!1,autoplayMuted:!1,loop:!1,isLive:!1,zoom:1,videoInit:!0,poster:"",isMobileSimulateMode:!1,defaultPlaybackRate:1,execBeforePluginsCall:null,allowSeekAfterEnded:!0,enableContextmenu:!0,closeVideoClick:!1,closeVideoDblclick:!1,closePlayerBlur:!1,closeDelayBlur:!1,leavePlayerTime:3e3,closePlayVideoFocus:!1,closePauseVideoFocus:!1,closeFocusVideoFocus:!0,closeControlsBlur:!0,topBarAutoHide:!0,videoAttributes:{},startTime:0,seekedStatus:"play",miniprogress:!1,disableSwipeHandler:function(){},enableSwipeHandler:function(){},preProcessUrl:null,ignores:[],whitelist:[],inactive:3e3,lang:(e=(document.documentElement.getAttribute("lang")||navigator.language||"zh-cn").toLocaleLowerCase(),"zh-cn"===e&&(e="zh"),e),controls:!0,marginControls:!1,fullscreenTarget:null,screenShot:!1,rotate:!1,pip:!1,download:!1,mini:!1,cssFullscreen:!0,keyShortcut:!0,presets:[],plugins:[],playbackRate:1,definition:{list:[]},playsinline:!0,customDuration:0,timeOffset:0,icons:{},i18n:[],tabindex:0,thumbnail:null,videoConfig:{},isHideTips:!1,minWaitDelay:200,commonStyle:{progressColor:"",playedColor:"",cachedColor:"",sliderBtnStyle:{},volumeColor:""}};var e}var gt=1,vt=2,yt=3,mt=4,kt=5,bt=6,Ct=7,_t=["ERROR","INITIAL","READY","ATTACHING","ATTACHED","NOTALLOW","RUNNING","ENDED","DESTROYED"],wt=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"onMouseEnter",(function(t){var i=u(e),n=i.player;i.playerConfig.closeControlsBlur&&n.focus({autoHide:!1})})),s(u(e),"onMouseLeave",(function(t){u(e).player.focus()})),e}return r(i,[{key:"beforeCreate",value:function(e){e.config.mode||"mobile"!==O.device||(e.config.mode="flex"),e.player.config.marginControls&&(e.config.autoHide=!1)}},{key:"afterCreate",value:function(){var e=this,t=this.config,i=t.disable,n=t.height,o=t.mode;if(!i){"flex"===o&&this.player.addClass(ft.FLEX_CONTROLS);var r={height:"".concat(n,"px")};Object.keys(r).map((function(t){e.root.style[t]=r[t]})),this.left=this.find("xg-left-grid"),this.center=this.find("xg-center-grid"),this.right=this.find("xg-right-grid"),this.innerRoot=this.find("xg-inner-controls"),this.on(ve,(function(t){t?x.addClass(e.root,"mini-controls"):x.removeClass(e.root,"mini-controls")}));var s=this.playerConfig.isMobileSimulateMode;"mobile"!==O.device&&"mobile"!==s&&(this.bind("mouseenter",this.onMouseEnter),this.bind("mouseleave",this.onMouseLeave))}}},{key:"focus",value:function(){this.player.focus({autoHide:!1})}},{key:"focusAwhile",value:function(){this.player.focus({autoHide:!0})}},{key:"blur",value:function(){this.player.blur({ignorePaused:!0})}},{key:"recoverAutoHide",value:function(){this.config.autoHide&&x.addClass(this.root,ft.CONTROLS_AUTOHIDE)}},{key:"pauseAutoHide",value:function(){x.removeClass(this.root,ft.CONTROLS_AUTOHIDE)}},{key:"show",value:function(e){this.root.style.display="",this.player.focus()}},{key:"hide",value:function(){this.root.style.display="none"}},{key:"mode",get:function(){return this.config.mode}},{key:"registerPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;if(this.root){var o=e.defaultConfig||{};if(!t.root){var r=t.position?t.position:t.config&&t.config.position?t.config.position:o.position;switch(r){case rt.CONTROLS_LEFT:t.root=this.left;break;case rt.CONTROLS_RIGHT:t.root=this.right;break;case rt.CONTROLS_CENTER:t.root=this.center;break;case rt.CONTROLS:t.root=this.root;break;default:t.root=this.left}return p(l(i.prototype),"registerPlugin",this).call(this,e,t,n)}}}},{key:"destroy",value:function(){"mobile"!==O.device&&(this.unbind("mouseenter",this.onMouseEnter),this.unbind("mouseleave",this.onMouseLeave))}},{key:"render",value:function(){var e=this.config,t=e.mode,i=e.autoHide,n=e.initShow;if(!e.disable){var o=x.classNames({"xgplayer-controls":!0},{"flex-controls":"flex"===t},{"bottom-controls":"bottom"===t},s({},ft.CONTROLS_AUTOHIDE,i),{"xgplayer-controls-initshow":n||!i});return'<xg-controls class="'.concat(o,'" unselectable="on">\n    <xg-inner-controls class="xg-inner-controls xg-pos">\n      <xg-left-grid class="xg-left-grid">\n      </xg-left-grid>\n      <xg-center-grid class="xg-center-grid"></xg-center-grid>\n      <xg-right-grid class="xg-right-grid">\n      </xg-right-grid>\n    </xg-inner-controls>\n    </xg-controls>')}}}],[{key:"pluginName",get:function(){return"controls"}},{key:"defaultConfig",get:function(){return{disable:!1,autoHide:!0,mode:"",initShow:!1}}}]),i}(ct),Tt={lang:{},langKeys:[],textKeys:[]};function xt(e,t){return Object.keys(t).forEach((function(i){var n,o=x.typeOf(t[i]),r=x.typeOf(e[i]);"Array"===o?("Array"!==r&&(e[i]=[]),(n=e[i]).push.apply(n,g(t[i]))):"Object"===o?("Object"!==r&&(e[i]={}),xt(e[i],t[i])):e[i]=t[i]})),e}function St(){Object.keys(Tt.lang.en).map((function(e){Tt.textKeys[e]=e}))}function Et(e,t){var i=e.LANG;if(t||(t=Tt),t.lang){var n=e.TEXT||{};"zh"===i&&(i="zh-cn"),t.lang[i]?xt(t.lang[i],n):(t.langKeys.push(i),t.lang[i]=n),St()}}Et({LANG:"en",TEXT:{ERROR_TYPES:{network:{code:1,msg:"video download error"},mse:{code:2,msg:"stream append error"},parse:{code:3,msg:"parsing error"},format:{code:4,msg:"wrong format"},decoder:{code:5,msg:"decoding error"},runtime:{code:6,msg:"grammatical errors"},timeout:{code:7,msg:"play timeout"},other:{code:8,msg:"other errors"}},HAVE_NOTHING:"There is no information on whether audio/video is ready",HAVE_METADATA:"Audio/video metadata is ready ",HAVE_CURRENT_DATA:"Data about the current play location is available, but there is not enough data to play the next frame/millisecond",HAVE_FUTURE_DATA:"Current and at least one frame of data is available",HAVE_ENOUGH_DATA:"The available data is sufficient to start playing",NETWORK_EMPTY:"Audio/video has not been initialized",NETWORK_IDLE:"Audio/video is active and has been selected for resources, but no network is used",NETWORK_LOADING:"The browser is downloading the data",NETWORK_NO_SOURCE:"No audio/video source was found",MEDIA_ERR_ABORTED:"The fetch process is aborted by the user",MEDIA_ERR_NETWORK:"An error occurred while downloading",MEDIA_ERR_DECODE:"An error occurred while decoding",MEDIA_ERR_SRC_NOT_SUPPORTED:"Audio/video is not supported",REPLAY:"Replay",ERROR:"Network is offline",PLAY_TIPS:"Play",PAUSE_TIPS:"Pause",PLAYNEXT_TIPS:"Play next",DOWNLOAD_TIPS:"Download",ROTATE_TIPS:"Rotate",RELOAD_TIPS:"Reload",FULLSCREEN_TIPS:"Fullscreen",EXITFULLSCREEN_TIPS:"Exit fullscreen",CSSFULLSCREEN_TIPS:"Cssfullscreen",EXITCSSFULLSCREEN_TIPS:"Exit cssfullscreen",TEXTTRACK:"Caption",PIP:"PIP",SCREENSHOT:"Screenshot",LIVE:"LIVE",OFF:"Off",OPEN:"Open",MINI_DRAG:"Click and hold to drag",MINISCREEN:"Miniscreen",REFRESH_TIPS:"Please Try",REFRESH:"Refresh",FORWARD:"forward",LIVE_TIP:"Live"}});var Pt={get textKeys(){return Tt.textKeys},get langKeys(){return Tt.langKeys},get lang(){var e={};return Tt.langKeys.map((function(t){e[t]=Tt.lang[t]})),Tt.lang["zh-cn"]&&(e.zh=Tt.lang["zh-cn"]||{}),e},extend:function(e,t){var i=[];if(t||(t=Tt),t.lang){i="Array"!==x.typeOf(e)?Object.keys(e).map((function(t){return{LANG:"zh"===t?"zh-cn":t,TEXT:e[t]}})):e;var n=t.lang;i.map((function(e){"zh"===e.LANG&&(e.LANG="zh-cn"),n[e.LANG]?xt(n[e.LANG]||{},e.TEXT||{}):Et(e,t)})),St()}},use:Et,init:function(e){var t,i={lang:{},langKeys:[],textKeys:{},pId:e};return xt(i.lang,Tt.lang),(t=i.langKeys).push.apply(t,g(Tt.langKeys)),xt(i.textKeys,Tt.textKeys),i}},It={},Lt=null,At=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"add",value:function(e){e&&(It[e.playerId]=e,1===Object.keys(It).length&&this.setActive(e.playerId,!0))}},{key:"remove",value:function(e){e&&(e.isUserActive,delete It[e.playerId])}},{key:"_iterate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(var i in It)if(Object.prototype.hasOwnProperty.call(It,i)){var n=It[i];if(t){if(e(n))break}else e(n)}}},{key:"forEach",value:function(e){this._iterate(e)}},{key:"find",value:function(e){var t=null;return this._iterate((function(i){var n=e(i);return n&&(t=i),n}),!0),t}},{key:"findAll",value:function(e){var t=[];return this._iterate((function(i){e(i)&&t.push(i)})),t}},{key:"setActive",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(It[e])return t?this.forEach((function(t){e===t.playerId?(t.isUserActive=!0,t.isInstNext=!1):t.isUserActive=!1})):It[e].isUserActive=t,e}},{key:"getActiveId",value:function(){for(var e=Object.keys(It),t=0;t<e.length;t++){var i=It[e[t]];if(i&&i.isUserActive)return e[t]}return null}},{key:"setNext",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(It[e])return t?this.forEach((function(t){e===t.playerId?(t.isUserActive=!1,t.isInstNext=!0):t.isInstNext=!1})):It[e].isInstNext=t,e}}],[{key:"getInstance",value:function(){return Lt||(Lt=new i),Lt}}]),i}(m.exports.EventEmitter);var Ot=["play","pause","replay","retry"],Dt=0,Rt=0,Mt=null,jt=function(e){a(o,e);var i=d(o);function o(e){var t;n(this,o);var r,a=x.deepMerge(pt(),e);s(u(t=i.call(this,a)),"canPlayFunc",(function(){if(t.config){var e=t.config,i=e.autoplay,n=e.defaultPlaybackRate;w.logInfo("player","canPlayFunc, startTime",t.__startTime),t.__startTime>0&&t.duration>0&&(t.currentTime=t.__startTime>t.duration?t.duration:t.__startTime,t.__startTime=-1),t.playbackRate=n,(i||t._useAutoplay)&&t.mediaPlay(),t.off(K,t.canPlayFunc),t.removeClass(ft.ENTER)}})),s(u(t),"onFullscreenChange",(function(e,i){var n=function(){x.setTimeout(u(t),(function(){t.resize()}),100)},o=x.getFullScreenEl();t._fullActionFrom?t._fullActionFrom="":t.emit(Se,{eventType:"system",action:"switch_fullscreen",pluginName:"player",currentTime:t.currentTime,duration:t.duration,props:[{prop:"fullscreen",from:!0,to:!1}]});var r=function(e,t,i){if(e){var n=e.getAttribute(i);return!(!n||n!==t||"VIDEO"!==e.tagName&&"AUDIO"!==e.tagName)}}(o,t.playerId,Ue);if(i||o&&(o===t._fullscreenEl||r))n(),!t.config.closeFocusVideoFocus&&t.media.focus(),t.fullscreen=!0,t.changeFullStyle(t.root,o,ft.FULLSCREEN),t.emit(pe,!0,t._fullScreenOffset),t.cssfullscreen&&t.exitCssFullscreen();else if(t.fullscreen){n();var s=u(t),a=s._fullScreenOffset;s.config.needFullscreenScroll?(window.scrollTo(a.left,a.top),x.setTimeout(u(t),(function(){t.fullscreen=!1,t._fullScreenOffset=null}),100)):(!t.config.closeFocusVideoFocus&&t.media.focus(),t.fullscreen=!1,t._fullScreenOffset=null),t.cssfullscreen?t.removeClass(ft.FULLSCREEN):t.recoverFullStyle(t.root,t._fullscreenEl,ft.FULLSCREEN),t._fullscreenEl=null,t.emit(pe,!1)}})),s(u(t),"_onWebkitbeginfullscreen",(function(e){t._fullscreenEl=t.media,t.onFullscreenChange(e,!0)})),s(u(t),"_onWebkitendfullscreen",(function(e){t.onFullscreenChange(e,!1)})),Ye(u(t),Ot),t.config=a,t._pluginInfoId=x.generateSessionId(),(r=u(t)).logInfo=w.logInfo.bind(r),r.logWarn=w.logWarn.bind(r),r.logError=w.logError.bind(r);var l=t.constructor.defaultPreset;if(t.config.presets.length){var c=t.config.presets.indexOf("default");c>=0&&l&&(t.config.presets[c]=l)}else l&&t.config.presets.push(l);if(t.userTimer=null,t.waitTimer=null,t.handleSource=!0,t._state=gt,t.isError=!1,t._hasStart=!1,t.isSeeking=!1,t.isCanplay=!1,t._useAutoplay=!1,t.__startTime=-1,t.rotateDeg=0,t.isActive=!1,t.fullscreen=!1,t.cssfullscreen=!1,t.isRotateFullscreen=!1,t._fullscreenEl=null,t.timeSegments=[],t._cssfullscreenEl=null,t.curDefinition=null,t._orgCss="",t._fullScreenOffset=null,t._videoHeight=0,t._videoWidth=0,t.videoPos={pi:1,scale:0,rotate:-1,x:0,y:0,h:-1,w:-1,vy:0,vx:0},t.sizeInfo={width:0,height:0,left:0,top:0},t._accPlayed={t:0,acc:0,loopAcc:0},t._offsetInfo={currentTime:-1,duration:0},t.innerContainer=null,t.controls=null,t.topBar=null,t.root=null,t.__i18n=Pt.init(t._pluginInfoId),O.os.isAndroid&&O.osVersion>0&&O.osVersion<6&&(t.config.autoplay=!1),t.database=new Ne,t.isUserActive=!1,t._onceSeekCanplay=null,t._isPauseBeforeSeek=0,t.innerStates={isActiveLocked:!1},t.instManager=Mt,!t._initDOM())return console.error(new Error("can't find the dom which id is ".concat(t.config.id," or this.config.el does not exist"))),h(t);var d=t.config,f=d.definition,p=void 0===f?{}:f;if(!d.url&&p.list&&p.list.length>0){var g=p.list.find((function(e){return e.definition&&e.definition===p.defaultDefinition}));g||(p.defaultDefinition=p.list[0].definition,g=p.list[0]),t.config.url=g.url,t.curDefinition=g}return t._bindEvents(),t._registerPresets(),t._registerPlugins(),dt.onPluginsReady(u(t)),t.getInitDefinition(),t.setState(vt),x.setTimeout(u(t),(function(){t.emit(ie)}),0),t.onReady&&t.onReady(),(t.config.videoInit||t.config.autoplay)&&(!t.hasStart||t.state<mt)&&t.start(),t}return r(o,[{key:"_initDOM",value:function(){var e,t=this;if(this.root=this.config.id?document.getElementById(this.config.id):null,!this.root){var i=this.config.el;if(!i||1!==i.nodeType)return this.emit(U,new j("use",this.config.vid,{line:32,handle:"Constructor",msg:"container id can't be empty"})),console.error("this.confg.id or this.config.el can't be empty"),!1;this.root=i}var n=function(e){for(var t=Object.keys(It),i=0;i<t.length;i++){var n=It[t[i]];if(n.root===e)return n}return null}(this.root);n&&(w.logWarn("The is an Player instance already exists in this.root, destroy it and reinitialize"),n.destroy()),this.root.setAttribute(Ue,this.playerId),null===(e=Mt)||void 0===e||e.add(this),dt.init(this),this._initBaseDoms();var o=this.constructor.XgVideoProxy;if(o&&this.mediaConfig.mediaType===o.mediaType){var r=this.innerContainer||this.root;this.detachVideoEvents(this.media);var s=new o(r,this.config,this.mediaConfig);this.attachVideoEvents(s),this.media=s}if(this.media.setAttribute(Ue,this.playerId),this.config.controls){var a=this.config.controls.root||null,l=dt.register(this,wt,{root:a});this.controls=l}var c="mobile"===this.config.isMobileSimulateMode?"mobile":O.device;if(this.addClass("".concat(ft.DEFAULT," ").concat(ft.INACTIVE," xgplayer-").concat(c," ").concat(this.config.controls?"":ft.NO_CONTROLS)),this.config.autoplay?this.addClass(ft.ENTER):this.addClass(ft.NO_START),this.config.fluid){var u=this.config,h=u.width,d=u.height;"number"==typeof h&&"number"==typeof d||(h=600,d=337.5);var f={width:"100%",height:"0","max-width":"100%","padding-top":"".concat(100*d/h,"%")};Object.keys(f).forEach((function(e){t.root.style[e]=f[e]}))}else["width","height"].forEach((function(e){t.config[e]&&("number"!=typeof t.config[e]?t.root.style[e]=t.config[e]:t.root.style[e]="".concat(t.config[e],"px"))}));var p=this.root.getBoundingClientRect(),g=p.width,v=p.height,y=p.left,m=p.top;return this.sizeInfo.width=g,this.sizeInfo.height=v,this.sizeInfo.left=y,this.sizeInfo.top=m,!0}},{key:"_initBaseDoms",value:function(){this.topBar=null,this.leftBar=null,this.rightBar=null,this.config.marginControls&&(this.innerContainer=x.createDom("xg-video-container","",{"data-index":-1},"xg-video-container"),this.root.appendChild(this.innerContainer))}},{key:"_bindEvents",value:function(){var e=this;["focus","blur"].forEach((function(t){e.on(t,e["on"+t.charAt(0).toUpperCase()+t.slice(1)])})),Fe.forEach((function(t){document&&document.addEventListener(t,e.onFullscreenChange)})),O.os.isIos&&(this.media.addEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.addEventListener("webkitendfullscreen",this._onWebkitendfullscreen)),this.once(X,this.resize),this.playFunc=function(){e.config.closeFocusVideoFocus||e.media.focus()},this.once(N,this.playFunc)}},{key:"_unbindEvents",value:function(){var e=this;this.root.removeEventListener("mousemove",this.mousemoveFunc),Fe.forEach((function(t){document.removeEventListener(t,e.onFullscreenChange)})),this.playFunc&&this.off(N,this.playFunc),this.off(K,this.canPlayFunc),this.media.removeEventListener("webkitbeginfullscreen",this._onWebkitbeginfullscreen),this.media.removeEventListener("webkitendfullscreen",this._onWebkitendfullscreen)}},{key:"_startInit",value:function(e){var t=this;if(this.media&&(e&&""!==e&&("Array"!==x.typeOf(e)||0!==e.length)||(e="",this.emit(ne),w.logWarn("config.url is null, please get url and run player._startInit(url)"),!this.config.nullUrlStart))){this.handleSource&&(this._detachSourceEvents(this.media),"Array"===x.typeOf(e)&&e.length>0?this._attachSourceEvents(this.media,e):this.media.src&&this.media.src===e?e||this.media.removeAttribute("src"):this.media.src=e),"Number"===x.typeOf(this.config.volume)&&(this.volume=this.config.volume);var i=this.innerContainer?this.innerContainer:this.root;this.media instanceof window.Element&&!i.contains(this.media)&&i.insertBefore(this.media,i.firstChild);var n=this.media.readyState;w.logInfo("_startInit readyState",n),this.config.autoplay&&(!x.isMSE(this.media)&&this.load(),(O.os.isIpad||O.os.isPhone)&&this.mediaPlay());var o=this.config.startTime;this.__startTime=o>0?o:-1,this.config.startTime=0,n>=2&&this.duration>0?this.canPlayFunc():this.on(K,this.canPlayFunc),(!this.hasStart||this.state<mt)&&dt.afterInit(this),this.hasStart=!0,this.setState(mt),x.setTimeout(this,(function(){t.emit(se)}),0)}}},{key:"_registerPlugins",value:function(){var e=this,t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this._loadingPlugins=[];var i=this.config.ignores||[],n=this.config.plugins||[],o=this.config.i18n||[];t&&Pt.extend(o,this.__i18n);var r=i.join("||").toLowerCase().split("||"),s=this.plugins;n.forEach((function(i){try{var n=i.plugin?i.plugin.pluginName:i.pluginName;if(n&&r.indexOf(n.toLowerCase())>-1)return null;if(!t&&s[n.toLowerCase()])return;if(i.lazy&&i.loader){var o=dt.lazyRegister(e,i);return void(i.forceBeforeInit&&(o.then((function(){e._loadingPlugins.splice(e._loadingPlugins.indexOf(o),1)})).catch((function(t){w.logError("_registerPlugins:loadingPlugin",t),e._loadingPlugins.splice(e._loadingPlugins.indexOf(o),1)})),e._loadingPlugins.push(o)))}return e.registerPlugin(i)}catch(a){w.logError("_registerPlugins:",a)}}))}},{key:"_registerPresets",value:function(){var e=this;this.config.presets.forEach((function(t){!function(e,t){var i,n,o=t.preset&&t.options?new t.preset(t.options,e.config):new t({},e.config),r=o.plugins,s=void 0===r?[]:r,a=o.ignores,l=void 0===a?[]:a,c=o.icons,u=void 0===c?{}:c,h=o.i18n,d=void 0===h?[]:h;e.config.plugins||(e.config.plugins=[]),e.config.ignores||(e.config.ignores=[]),(i=e.config.plugins).push.apply(i,g(s)),(n=e.config.ignores).push.apply(n,g(l)),Object.keys(u).map((function(t){e.config.icons[t]||(e.config.icons[t]=u[t])}));var f=e.config.i18n||[];d.push.apply(d,g(f)),e.config.i18n=d}(e,t)}))}},{key:"_getRootByPosition",value:function(e){var t=null;switch(e){case rt.ROOT_RIGHT:this.rightBar||(this.rightBar=x.createPositionBar("xg-right-bar",this.root)),t=this.rightBar;break;case rt.ROOT_LEFT:this.leftBar||(this.leftBar=x.createPositionBar("xg-left-bar",this.root)),t=this.leftBar;break;case rt.ROOT_TOP:this.topBar||(this.topBar=x.createPositionBar("xg-top-bar",this.root),this.config.topBarAutoHide&&x.addClass(this.topBar,ft.TOP_BAR_AUTOHIDE)),t=this.topBar;break;default:t=this.innerContainer||this.root}return t}},{key:"registerPlugin",value:function(e,t){var i=dt.formatPluginInfo(e,t),n=i.PLUFGIN,o=i.options,r=this.config.plugins;!dt.checkPluginIfExits(n.pluginName,r)&&r.push(n);var s=dt.getRootByConfig(n.pluginName,this.config);s.root&&(o.root=s.root),s.position&&(o.position=s.position);var a=o.position?o.position:o.config&&o.config.position||n.defaultConfig&&n.defaultConfig.position;return!o.root&&"string"==typeof a&&a.indexOf("controls")>-1?this.controls&&this.controls.registerPlugin(n,o,n.pluginName):(o.root||(o.root=this._getRootByPosition(a)),dt.register(this,n,o))}},{key:"deregister",value:function(e){"string"==typeof e?dt.unRegister(this,e):e instanceof $e&&dt.unRegister(this,e.pluginName)}},{key:"unRegisterPlugin",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.deregister(e),t&&this.removePluginFromConfig(e)}},{key:"removePluginFromConfig",value:function(e){var t;if("string"==typeof e?t=e:e instanceof $e&&(t=e.pluginName),t)for(var i=this.config.plugins.length-1;i>-1;i--){if(this.config.plugins[i].pluginName.toLowerCase()===t.toLowerCase()){this.config.plugins.splice(i,1);break}}}},{key:"plugins",get:function(){return dt.getPlugins(this)}},{key:"getPlugin",value:function(e){var t=dt.findPlugin(this,e);return t&&t.pluginName?t:null}},{key:"addClass",value:function(e){this.root&&(x.hasClass(this.root,e)||x.addClass(this.root,e))}},{key:"removeClass",value:function(e){this.root&&x.removeClass(this.root,e)}},{key:"hasClass",value:function(e){if(this.root)return x.hasClass(this.root,e)}},{key:"setAttribute",value:function(e,t){this.root&&this.root.setAttribute(e,t)}},{key:"removeAttribute",value:function(e,t){this.root&&this.root.removeAttribute(e,t)}},{key:"start",value:function(e){var t=this;if(!(this.state>yt))return e||this.config.url||this.getInitDefinition(),this.hasStart=!0,this.setState(yt),this._registerPlugins(!1),dt.beforeInit(this).then((function(){if(t.config){e||(e=t.url||t.config.url);var i=t._preProcessUrl(e);return t._startInit(i.url)}})).catch((function(e){throw e.fileName="player",e.lineNumber="236",w.logError("start:beforeInit:",e),e}))}},{key:"switchURL",value:function(e,t){var i=this,n=e;"Object"===x.typeOf(e)&&(n=e.url),n=this._preProcessUrl(n).url;var o=this.currentTime;this.__startTime=o;var r=this.paused&&!this.isError;return this.src=n,new Promise((function(e,t){var o=function(e){i.off("timeupdate",s),i.off("canplay",s),t(e)},s=function(){i.duration>0&&i.__startTime>0&&(i.currentTime=i.__startTime,i.__startTime=-1),r&&i.pause(),i.off("error",o),e(!0)};i.once("error",o),n?(O.os.isAndroid?i.once("timeupdate",s):i.once("canplay",s),i.play()):i.errorHandler("error",{code:6,message:"empty_src"})}))}},{key:"videoPlay",value:function(){this.mediaPlay()}},{key:"mediaPlay",value:function(){var e=this;if(!this.hasStart&&this.state<mt)return this.removeClass(ft.NO_START),this.addClass(ft.ENTER),this.start(),void(this._useAutoplay=!0);this.state<bt&&(this.removeClass(ft.NO_START),!this.isCanplay&&this.addClass(ft.ENTER));var t=p(l(o.prototype),"play",this).call(this);return void 0!==t&&t&&t.then?t.then((function(){e.removeClass(ft.NOT_ALLOW_AUTOPLAY),e.addClass(ft.PLAYING),e.state<bt&&(w.logInfo(">>>>playPromise.then"),e.setState(bt),e.emit(oe))})).catch((function(t){if(w.logWarn(">>>>playPromise.catch",t.name),e.media&&e.media.error)return e.onError(),void e.removeClass(ft.ENTER);"NotAllowedError"===t.name&&(e._errorTimer=x.setTimeout(e,(function(){e._errorTimer=null,e.emit(re),e.addClass(ft.NOT_ALLOW_AUTOPLAY),e.removeClass(ft.ENTER),e.pause(),e.setState(kt)}),0))})):(w.logWarn("video.play not return promise"),this.state<bt&&(this.setState(bt),this.removeClass(ft.NOT_ALLOW_AUTOPLAY),this.removeClass(ft.NO_START),this.removeClass(ft.ENTER),this.addClass(ft.PLAYING),this.emit(oe))),t}},{key:"mediaPause",value:function(){p(l(o.prototype),"pause",this).call(this)}},{key:"videoPause",value:function(){p(l(o.prototype),"pause",this).call(this)}},{key:"play",value:function(){var e=this;return this.removeClass(ft.PAUSED),Ze(this,"play",(function(){return e.mediaPlay()}))}},{key:"pause",value:function(){var e=this;Ze(this,"pause",(function(){p(l(o.prototype),"pause",e).call(e)}))}},{key:"seek",value:function(e,t){var i=this;if(this.media&&!Number.isNaN(Number(e))&&this.hasStart){var n=this.config,o=n.isSeekedPlay,r=n.seekedStatus,s=t||(o?"play":r);e=e<0?0:e>this.duration?parseInt(this.duration,10):e,!this._isPauseBeforeSeek&&(this._isPauseBeforeSeek=this.paused?2:1),this._onceSeekCanplay&&this.off(W,this._onceSeekCanplay),this._onceSeekCanplay=function(){switch(i.removeClass(ft.ENTER),i.isSeeking=!1,s){case"play":i.play();break;case"pause":i.pause();break;default:i._isPauseBeforeSeek>1||i.paused?i.pause():i.play()}i._isPauseBeforeSeek=0,i._onceSeekCanplay=null},this.once(W,this._onceSeekCanplay),this.state<bt?(this.removeClass(ft.NO_START),this.currentTime=e,this.play()):this.currentTime=e}}},{key:"getInitDefinition",value:function(){var e=this,t=this.config,i=t.definition;!t.url&&i&&i.list&&i.list.length>0&&i.defaultDefinition&&i.list.map((function(t){t.definition===i.defaultDefinition&&(e.config.url=t.url,e.curDefinition=t)}))}},{key:"changeDefinition",value:function(e,i){var n=this,o=this.config.definition;if(Array.isArray(null==o?void 0:o.list)&&o.list.forEach((function(t){(null==e?void 0:e.definition)===t.definition&&(n.curDefinition=t)})),null!=e&&e.bitrate&&"number"!=typeof e.bitrate&&(e.bitrate=parseInt(e.bitrate,10)||0),this.emit(ye,{from:i,to:e}),this.hasStart){var r=this.switchURL(e.url,t({seamless:!1!==o.seamless&&"undefined"!=typeof MediaSource&&"function"==typeof MediaSource.isTypeSupported},e));r&&r.then?r.then((function(){n.emit(me,{from:i,to:e})})):this.emit(me,{from:i,to:e})}else this.config.url=e.url}},{key:"reload",value:function(){this.load(),this.reloadFunc=function(){this.play()},this.once(X,this.reloadFunc)}},{key:"resetState",value:function(){var e=this,t=[ft.NOT_ALLOW_AUTOPLAY,ft.PLAYING,ft.NO_START,ft.PAUSED,ft.REPLAY,ft.ENTER,ft.ENDED,ft.ERROR,ft.LOADING];this.hasStart=!1,this.isError=!1,this._useAutoplay=!1,this.mediaPause(),this._accPlayed.acc=0,this._accPlayed.t=0,this._accPlayed.loopAcc=0,t.forEach((function(t){e.removeClass(t)})),this.addClass(ft.NO_START),this.emit(Ee)}},{key:"reset",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=arguments.length>1?arguments[1]:void 0;this.resetState();var n=this.plugins;if(n&&(t.map((function(t){e.deregister(t)})),i)){var o=pt();Object.keys(this.config).keys((function(t){"undefined"===e.config[t]||"plugins"!==t&&"presets"!==t&&"el"!==t&&"id"!==t||(e.config[t]=o[t])}))}}},{key:"destroy",value:function(){var e,t=this,i=this.innerContainer,n=this.root,r=this.media;if(n&&r){if(this.hasStart=!1,this._useAutoplay=!1,n.removeAttribute(Ue),this.updateAcc("destroy"),this._unbindEvents(),this._detachSourceEvents(this.media),x.clearAllTimers(this),this.emit(le),null===(e=Mt)||void 0===e||e.remove(this),dt.destroy(this),Xe(this),p(l(o.prototype),"destroy",this).call(this),this.fullscreen&&this._fullscreenEl===this.root&&this.exitFullscreen(),i)for(var s=i.children,a=0;a<s.length;a++)i.removeChild(s[a]);!i&&r instanceof window.Node&&n.contains(r)&&n.removeChild(r),["topBar","leftBar","rightBar","innerContainer"].map((function(e){t[e]&&n.removeChild(t[e]),t[e]=null}));var c=n.className.split(" ");c.length>0?n.className=c.filter((function(e){return e.indexOf("xgplayer")<0})).join(" "):n.className="",this.removeAttribute("data-xgfill"),["isSeeking","isCanplay","isActive","cssfullscreen","fullscreen"].forEach((function(e){t[e]=!1}))}}},{key:"replay",value:function(){var e=this;this.removeClass(ft.ENDED),this.currentTime=0,this.isSeeking=!1,Ze(this,"replay",(function(){e.once(W,(function(){var t=e.mediaPlay();t&&t.catch&&t.catch((function(e){console.log(e)}))})),e.emit(ae),e.onPlay()}))}},{key:"retry",value:function(){var e=this;this.removeClass(ft.ERROR),this.addClass(ft.LOADING),Ze(this,"retry",(function(){var t=e.currentTime,i=e.config.url,n=x.isMSE(e.media)?{url:i}:e._preProcessUrl(i);e.src=n.url,!e.config.isLive&&(e.currentTime=t),e.once(K,(function(){e.mediaPlay()}))}))}},{key:"changeFullStyle",value:function(e,t,i,n){e&&(n||(n=ft.PARENT_FULLSCREEN),this._orgCss||(this._orgCss=x.filterStyleFromText(e)),x.addClass(e,i),t&&t!==e&&!this._orgPCss&&(this._orgPCss=x.filterStyleFromText(t),x.addClass(t,n),t.setAttribute(Ue,this.playerId)))}},{key:"recoverFullStyle",value:function(e,t,i,n){n||(n=ft.PARENT_FULLSCREEN),this._orgCss&&(x.setStyleFromCsstext(e,this._orgCss),this._orgCss=""),x.removeClass(e,i),t&&t!==e&&this._orgPCss&&(x.setStyleFromCsstext(t,this._orgPCss),this._orgPCss="",x.removeClass(t,n),t.removeAttribute(Ue))}},{key:"getFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget,t=this.root,i=this.media;e||(e=t),this._fullScreenOffset={top:x.scrollTop(),left:x.scrollLeft()},this._fullscreenEl=e,this._fullActionFrom="get";var n=x.getFullScreenEl();if(n===this._fullscreenEl)return this.onFullscreenChange(),Promise.resolve();try{for(var o=0;o<He.length;o++){var r=He[o];if(e[r]){var s="webkitRequestFullscreen"===r?e.webkitRequestFullscreen(window.Element.ALLOW_KEYBOARD_INPUT):e[r]();return s&&s.then?s:Promise.resolve()}}return i.fullscreenEnabled||i.webkitSupportsFullscreen?(i.webkitEnterFullscreen(),Promise.resolve()):Promise.reject(new Error("call getFullscreen fail"))}catch(a){return Promise.reject(new Error("call getFullscreen fail"))}}},{key:"exitFullscreen",value:function(e){if(this.isRotateFullscreen&&this.exitRotateFullscreen(),this._fullscreenEl||x.getFullScreenEl()){this.root;var t=this.media;this._fullActionFrom="exit";try{for(var i=0;i<Be.length;i++){var n=Be[i];if(document[n]){var o=document[n]();return o&&o.then?o:Promise.resolve()}}return t&&t.webkitSupportsFullscreen?(t.webkitExitFullScreen(),Promise.resolve()):Promise.reject(new Error("call exitFullscreen fail"))}catch(r){return Promise.reject(new Error("call exitFullscreen fail"))}}}},{key:"getCssFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.isRotateFullscreen?this.exitRotateFullscreen():this.fullscreen&&this.exitFullscreen();var t=e?"".concat(ft.INNER_FULLSCREEN," ").concat(ft.CSS_FULLSCREEN):ft.CSS_FULLSCREEN;this.changeFullStyle(this.root,e,t);var i=this.config.fullscreen,n=void 0===i?{}:i,o=!0===n.useCssFullscreen||"function"==typeof n.useCssFullscreen&&n.useCssFullscreen();o&&(this.fullscreen=!0,this.emit(pe,!0)),this._cssfullscreenEl=e,this.cssfullscreen=!0,this.emit(ge,!0)}},{key:"exitCssFullscreen",value:function(){var e=this._cssfullscreenEl?"".concat(ft.INNER_FULLSCREEN," ").concat(ft.CSS_FULLSCREEN):ft.CSS_FULLSCREEN;if(this.fullscreen){var t=this.config.fullscreen,i=void 0===t?{}:t;!0===i.useCssFullscreen||"function"==typeof i.useCssFullscreen&&i.useCssFullscreen()?(this.recoverFullStyle(this.root,this._cssfullscreenEl,e),this.fullscreen=!1,this.emit(pe,!1)):this.removeClass(e)}else this.recoverFullStyle(this.root,this._cssfullscreenEl,e);this._cssfullscreenEl=null,this.cssfullscreen=!1,this.emit(ge,!1)}},{key:"getRotateFullscreen",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.fullscreenTarget;this.cssfullscreen&&this.exitCssFullscreen(e);var t=e?"".concat(ft.INNER_FULLSCREEN," ").concat(ft.ROTATE_FULLSCREEN):ft.ROTATE_FULLSCREEN;this._fullscreenEl=e||this.root,this.changeFullStyle(this.root,e,t,ft.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!0,this.fullscreen=!0,this.setRotateDeg(90),this._rootStyle=this.root.getAttribute("style"),this.root.style.width="".concat(window.innerHeight,"px"),this.emit(pe,!0)}},{key:"exitRotateFullscreen",value:function(e){var t=this._fullscreenEl!==this.root?"".concat(ft.INNER_FULLSCREEN," ").concat(ft.ROTATE_FULLSCREEN):ft.ROTATE_FULLSCREEN;this.recoverFullStyle(this.root,this._fullscreenEl,t,ft.PARENT_ROTATE_FULLSCREEN),this.isRotateFullscreen=!1,this.fullscreen=!1,this.setRotateDeg(0),this.emit(pe,!1),this._rootStyle&&(this.root.style.style=this._rootStyle,this._rootStyle=!1)}},{key:"setRotateDeg",value:function(e){90===window.orientation||-90===window.orientation?this.rotateDeg=0:this.rotateDeg=e}},{key:"focus",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!this.config.closeDelayBlur,delay:this.config.inactive};this.isActive?this.onFocus(e):this.emit(ee,t({paused:this.paused,ended:this.ended},e))}},{key:"blur",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{ignorePaused:!1};this.isActive?this.emit(te,t({paused:this.paused,ended:this.ended},e)):this.onBlur(e)}},{key:"onFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{autoHide:!0,delay:3e3},i=this.innerStates;if(this.isActive=!0,this.removeClass(ft.INACTIVE),this.userTimer&&(x.clearTimeout(this,this.userTimer),this.userTimer=null),void 0!==t.isLock&&(i.isActiveLocked=t.isLock),!1===t.autoHide||!0===t.isLock||i.isActiveLocked)this.userTimer&&(x.clearTimeout(this,this.userTimer),this.userTimer=null);else{var n=t&&t.delay?t.delay:this.config.inactive;this.userTimer=x.setTimeout(this,(function(){e.userTimer=null,e.blur()}),n)}}},{key:"onBlur",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.ignorePaused,i=void 0!==t&&t;if(this.isActive&&!this.innerStates.isActiveLocked){var n=this.config.closePauseVideoFocus;this.isActive=!1,(i||n||!this.paused&&!this.ended)&&this.addClass(ft.INACTIVE)}}},{key:"onEmptied",value:function(){this.updateAcc("emptied")}},{key:"onCanplay",value:function(){this.removeClass(ft.ENTER),this.removeClass(ft.ERROR),this.removeClass(ft.LOADING),this.isCanplay=!0,this.waitTimer&&x.clearTimeout(this,this.waitTimer)}},{key:"onDurationchange",value:function(){this.__startTime>0&&this.duration>0&&(this.currentTime=this.__startTime,this.__startTime=-1)}},{key:"onLoadeddata",value:function(){this.isError=!1,this.isSeeking=!1}},{key:"onLoadstart",value:function(){this.removeClass(ft.ERROR),this.isCanplay=!1}},{key:"onPlay",value:function(){this.state===Ct&&this.setState(bt),this.removeClass(ft.PAUSED),this.ended&&this.removeClass(ft.ENDED),!this.config.closePlayVideoFocus&&this.focus()}},{key:"onPause",value:function(){this.addClass(ft.PAUSED),this.updateAcc("pause"),this.config.closePauseVideoFocus||(this.userTimer&&(x.clearTimeout(this,this.userTimer),this.userTimer=null),this.focus())}},{key:"onEnded",value:function(){this.updateAcc("ended"),this.addClass(ft.ENDED),this.setState(Ct)}},{key:"onError",value:function(){this.isError=!0,this.updateAcc("error"),this.removeClass(ft.NOT_ALLOW_AUTOPLAY),this.removeClass(ft.NO_START),this.removeClass(ft.ENTER),this.removeClass(ft.LOADING),this.addClass(ft.ERROR)}},{key:"onSeeking",value:function(){this.isSeeking||this.updateAcc("seeking"),this.isSeeking=!0,this.addClass(ft.SEEKING)}},{key:"onSeeked",value:function(){this.isSeeking=!1,this.waitTimer&&x.clearTimeout(this,this.waitTimer),this.removeClass(ft.LOADING),this.removeClass(ft.SEEKING)}},{key:"onWaiting",value:function(){var e=this;this.waitTimer&&x.clearTimeout(this,this.waitTimer),this.updateAcc("waiting"),this.waitTimer=x.setTimeout(this,(function(){e.addClass(ft.LOADING),e.emit(fe),x.clearTimeout(e,e.waitTimer),e.waitTimer=null}),this.config.minWaitDelay)}},{key:"onPlaying",value:function(){var e=this;this.isError=!1,[ft.NO_START,ft.PAUSED,ft.ENDED,ft.ERROR,ft.REPLAY,ft.LOADING].forEach((function(t){e.removeClass(t)})),this._accPlayed.t||this.paused||this.ended||(this._accPlayed.t=(new Date).getTime())}},{key:"onTimeupdate",value:function(){!this._videoHeight&&this.media.videoHeight&&this.resize(),(this.waitTimer||this.hasClass(ft.LOADING))&&this.media.readyState>2&&(this.removeClass(ft.LOADING),x.clearTimeout(this,this.waitTimer),this.waitTimer=null),!this.paused&&this.state<bt&&this.duration&&(this.setState(bt),this.emit(oe)),this._accPlayed.t||this.paused||this.ended||(this._accPlayed.t=(new Date).getTime())}},{key:"onVolumechange",value:function(){"Number"===x.typeOf(this.config.volume)&&(this.config.volume=this.volume)}},{key:"onRatechange",value:function(){this.config.defaultPlaybackRate=this.playbackRate}},{key:"emitUserAction",value:function(e,i,n){if(this.media&&i&&e){var o="String"===x.typeOf(e)?e:e.type||"";n.props&&"Array"!==x.typeOf(n.props)&&(n.props=[n.props]),this.emit(Se,t({eventType:o,action:i,currentTime:this.currentTime,duration:this.duration,ended:this.ended,event:e},n))}}},{key:"updateAcc",value:function(e){if(this._accPlayed.t){var t=(new Date).getTime()-this._accPlayed.t;this._accPlayed.acc+=t,this._accPlayed.t=0,("ended"===e||this.ended)&&(this._accPlayed.loopAcc=this._accPlayed.acc)}}},{key:"checkBuffer",value:function(e){var t=this.media.buffered;if(!t||0===t.length||!this.duration)return!0;for(var i=e||this.media.currentTime||.2,n=t.length,o=0;o<n;o++)if(t.start(o)<=i&&t.end(o)>i)return!0;return!1}},{key:"resizePosition",value:function(){var e=this.videoPos,t=e.vy,i=e.vx,n=e.h,o=e.w,r=this.videoPos.rotate;if(!(r<0&&n<0&&o<0)){var s=this.videoPos._pi;if(!s&&this.media.videoHeight&&(s=this.media.videoWidth/this.media.videoHeight*100),s){this.videoPos.pi=s;var a={rotate:r=r<0?0:r},l=0,c=0,u=1,h=Math.abs(r/90),d=this.root,f=this.innerContainer,p=d.offsetWidth,g=f?f.offsetHeight:d.offsetHeight,v=g,y=p;if(h%2==0)u=n>0?100/n:o>0?100/o:1,a.scale=u,l=t>0?(100-n)/2-t:0,a.y=2===h?0-l:l,c=i>0?(100-o)/2-i:0,a.x=2===h?0-c:c,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(v,"px");else if(h%2==1){v=p;var m=g-p;c=-m/2/(y=g)*100,a.x=3===h?c+t/2:c-t/2,l=m/2/v*100,a.y=3===h?l+i/2:l-i/2,a.scale=u,this.media.style.width="".concat(y,"px"),this.media.style.height="".concat(v,"px")}var k=x.getTransformStyle(a,this.media.style.transform||this.media.style.webkitTransform);this.media.style.transform=k,this.media.style.webkitTransform=k}}}},{key:"position",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{h:0,y:0,x:0,w:0};if(this.media&&e&&e.h){var t=this.videoPos;t.h=100*e.h||0,t.w=100*e.w||0,t.vx=100*e.x||0,t.vy=100*e.y||0,this.resizePosition()}}},{key:"setConfig",value:function(e){var t=this;e&&Object.keys(e).map((function(i){if("plugins"!==i){t.config[i]=e[i];var n=t.plugins[i.toLowerCase()];n&&"Function"===x.typeOf(n.setConfig)&&n.setConfig(e[i])}}))}},{key:"playNext",value:function(e){var t=this;this.resetState(),this.setConfig(e),this._currentTime=0,this._duration=0,Ze(this,"playnext",(function(){t.start(),t.emit(we,e)}))}},{key:"resize",value:function(){var e=this;if(this.media){var t=this.root.getBoundingClientRect();this.sizeInfo.width=t.width,this.sizeInfo.height=t.height,this.sizeInfo.left=t.left,this.sizeInfo.top=t.top;var i=this.media,n=i.videoWidth,o=i.videoHeight,r=this.config,s=r.fitVideoSize,a=r.videoFillMode;if("fill"!==a&&"cover"!==a&&"contain"!==a||this.setAttribute("data-xgfill",a),o&&n){this._videoHeight=o,this._videoWidth=n;var l=this.controls&&this.innerContainer?this.controls.root.getBoundingClientRect().height:0,c=t.width,u=t.height-l,h=parseInt(n/o*1e3,10),d=parseInt(c/u*1e3,10),f=c,p=u,g={};"auto"===s&&d>h||"fixWidth"===s?(p=c/h*1e3,this.config.fluid?g.paddingTop="".concat(100*p/f,"%"):g.height="".concat(p+l,"px")):("auto"===s&&d<h||"fixHeight"===s)&&(f=h*u/1e3,g.width="".concat(f,"px")),this.fullscreen||this.cssfullscreen||Object.keys(g).forEach((function(t){e.root.style[t]=g[t]})),("fillHeight"===a&&d<h||"fillWidth"===a&&d>h)&&this.setAttribute("data-xgfill","cover");var v={videoScale:h,vWidth:f,vHeight:p,cWidth:f,cHeight:p+l};this.resizePosition(),this.emit(ke,v)}}}},{key:"updateObjectPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.media.updateObjectPosition?this.media.updateObjectPosition(e,t):this.media.style.objectPosition="".concat(100*e,"% ").concat(100*t,"%")}},{key:"setState",value:function(e){w.logInfo("setState","state from:".concat(_t[this.state]," to:").concat(_t[e])),this._state=e}},{key:"_preProcessUrl",value:function(e,t){var i=this.config.preProcessUrl;return x.isBlob(e)||"function"!=typeof i?{url:e}:i(e,t)}},{key:"state",get:function(){return this._state}},{key:"isFullscreen",get:function(){return this.fullscreen}},{key:"isCssfullScreen",get:function(){return this.cssfullscreen}},{key:"hasStart",get:function(){return this._hasStart},set:function(e){"boolean"==typeof e&&(this._hasStart=e,!1===e&&this.setState(vt),this.emit("hasstart"))}},{key:"isPlaying",get:function(){return this._state===bt||this._state===Ct},set:function(e){e?this.setState(bt):this._state>=bt&&this.setState(mt)}},{key:"definitionList",get:function(){return this.config&&this.config.definition&&this.config.definition.list||[]},set:function(e){var t=this,i=this.config.definition,n=null,o=null;i.list=e,this.emit("resourceReady",e),e.forEach((function(e){var r;(null===(r=t.curDefinition)||void 0===r?void 0:r.definition)===e.definition&&(n=e),i.defaultDefinition===e.definition&&(o=e)})),!o&&e.length>0&&(o=e[0]),n?this.changeDefinition(n):o&&this.changeDefinition(o)}},{key:"videoFrameInfo",get:function(){var e={total:0,dropped:0,corrupted:0,droppedRate:0,droppedDuration:0};if(!this.media||!this.media.getVideoPlaybackQuality)return e;var t=this.media.getVideoPlaybackQuality();return e.dropped=t.droppedVideoFrames||0,e.total=t.totalVideoFrames||0,e.corrupted=t.corruptedVideoFrames||0,e.total>0&&(e.droppedRate=e.dropped/e.total*100,e.droppedDuration=parseInt(this.cumulateTime/e.total*e.dropped,0)),e}},{key:"lang",get:function(){return this.config.lang},set:function(e){0!==Pt.langKeys.filter((function(t){return t===e})).length||"zh"===e?(this.config.lang=e,dt.setLang(e,this)):console.error("Sorry, set lang fail, because the language [".concat(e,"] is not supported now, list of all supported languages is [").concat(Pt.langKeys.join(),"] "))}},{key:"i18n",get:function(){var e=this.config.lang;return"zh"===e&&(e="zh-cn"),this.__i18n.lang[e]||this.__i18n.lang.en}},{key:"i18nKeys",get:function(){return this.__i18n.textKeys||{}}},{key:"version",get:function(){return D}},{key:"playerId",get:function(){return this._pluginInfoId}},{key:"url",get:function(){return this.__url||this.config.url},set:function(e){this.__url=e}},{key:"poster",get:function(){return this.plugins.poster?this.plugins.poster.config.poster:this.config.poster},set:function(e){this.plugins.poster&&this.plugins.poster.update(e)}},{key:"readyState",get:function(){return p(l(o.prototype),"readyState",this)}},{key:"error",get:function(){var e=p(l(o.prototype),"error",this);return this.i18n[e]||e}},{key:"networkState",get:function(){return p(l(o.prototype),"networkState",this)}},{key:"fullscreenChanging",get:function(){return!(null===this._fullScreenOffset)}},{key:"cumulateTime",get:function(){var e=this._accPlayed,t=e.acc,i=e.t;return i?(new Date).getTime()-i+t:t}},{key:"zoom",get:function(){return this.config.zoom},set:function(e){this.config.zoom=e}},{key:"videoRotateDeg",get:function(){return this.videoPos.rotate},set:function(e){(e=x.convertDeg(e))%90==0&&e!==this.videoPos.rotate&&(this.videoPos.rotate=e,this.resizePosition())}},{key:"avgSpeed",get:function(){return Rt},set:function(e){Rt=e}},{key:"realTimeSpeed",get:function(){return Dt},set:function(e){Dt=e}},{key:"offsetCurrentTime",get:function(){return this._offsetInfo.currentTime||0},set:function(e){this._offsetInfo.currentTime=e}},{key:"offsetDuration",get:function(){return this._offsetInfo.duration||0},set:function(e){this._offsetInfo.duration=e||0}},{key:"hook",value:function(e,t){return We.call.apply(We,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"useHooks",value:function(e,t){return Ge.call.apply(Ge,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removeHooks",value:function(e,t){return ze.call.apply(ze,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"usePluginHooks",value:function(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];return Ke.call.apply(Ke,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"removePluginHooks",value:function(e,t,i){for(var n=arguments.length,o=new Array(n>3?n-3:0),r=3;r<n;r++)o[r-3]=arguments[r];return qe.call.apply(qe,[this].concat(Array.prototype.slice.call(arguments)))}},{key:"setUserActive",value:function(e,t){var i;"boolean"==typeof t&&t!==this.muted&&(this.addInnerOP("volumechange"),x.typeOf(t)===Boolean&&(this.muted=t)),null===(i=Mt)||void 0===i||i.setActive(this.playerId,e)}}],[{key:"debugger",get:function(){return w.config.debug},set:function(e){w.config.debug=e}},{key:"instManager",get:function(){return Mt},set:function(e){Mt=e}},{key:"getCurrentUserActivePlayerId",value:function(){var e;return null===(e=Mt)||void 0===e?void 0:e.getActiveId()}},{key:"setCurrentUserActive",value:function(e,t){var i;null===(i=Mt)||void 0===i||i.setActive(e,t)}},{key:"isHevcSupported",value:function(){return O.isHevcSupported()}},{key:"probeConfigSupported",value:function(e){return O.probeConfigSupported(e)}},{key:"install",value:function(e,t){o.plugins||(o.plugins={}),o.plugins[e]||(o.plugins[e]=t)}},{key:"use",value:function(e,t){o.plugins||(o.plugins={}),o.plugins[e]=t}}]),o}(je);s(jt,"defaultPreset",null),s(jt,"XgVideoProxy",null),jt.instManager=At.getInstance();function Nt(){return(new Date).getTime()}var Ft="loadstart",Ht="loadeddata",Bt="firstFrame",Ut="waitingStart",Vt="waitingEnd",Wt="seekStart",Gt="seekEnd",zt=function(e){a(o,e);var i=d(o);function o(){var e;n(this,o);for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return s(u(e=i.call.apply(i,[this].concat(r))),"_onTimeupdate",(function(){e._state.isTimeUpdate=!0,e._state.autoplayStart&&(w.logInfo("[xgLogger]".concat(e.player.playerId," _onTimeupdate")),e._sendFF("onTimeupdate"))})),s(u(e),"_onAutoplayStart",(function(){w.logInfo("[xgLogger]".concat(e.player.playerId," _onAutoplayStart")),e._state.autoplayStart=!0,e.vt&&e._sendFF("onAutoplayStart")})),s(u(e),"_onReset",(function(){e._state={autoplayStart:!1,isFFLoading:!1,isTimeUpdate:!1,isFFSend:!1,isLs:!1},e.vt=0,e.pt=0,e.fvt=0,e.newPointTime=Nt(),e.loadedCostTime=0,e.startCostTime=0,e._isSeeking=!1,e.seekingStart=0,e.waitingStart=0,e.fixedWaitingStart=0,e._isWaiting=!1,e._waitTimer&&x.clearTimeout(u(e),e._waitTimer),e._waittTimer&&x.clearTimeout(u(e),e._waittTimer),e._waitTimer=null,e._waittTimer=null,e._waitType=0})),s(u(e),"_onSeeking",(function(){e.seekingStart||(e.suspendWaitingStatus("seek"),e.seekingStart=Nt(),e.emitLog(Wt,{start:Nt()}))})),s(u(e),"_onSeeked",(function(){e.suspendSeekingStatus("seeked")})),s(u(e),"_onWaitingLoadStart",(function(){e._isWaiting||e.vt||(e._isWaiting=!0,e.waitingStart=Nt(),e.fixedWaitingStart=Nt(),e._waitType=1,e.emitLog(Ut,{fixedStart:e.fixedWaitingStart,start:e.waitingStart,type:1,endType:"loadstart"}))})),s(u(e),"_onWaiting",(function(){!e._isWaiting&&e.vt&&(e._isWaiting=!0,e.vt?e.seekingStart?e._waitType=2:e._waitType=0:e._waitType=1,e.fixedWaitingStart=Nt(),e._waitTimer=x.setTimeout(u(e),(function(){e._isWaiting&&(e.waitingStart=Nt(),x.clearTimeout(u(e),e._waitTimer),e._waitTimer=null,e._startWaitTimeout(),e.emitLog(Ut,{fixedStart:e.fixedWaitingStart,start:e.waitingStart,type:e._waitType,endType:2===e._waitType?"seek":"playing"}))}),200))})),s(u(e),"_onError",(function(){e.suspendSeekingStatus("error"),e.suspendWaitingStatus("error")})),s(u(e),"_onPlaying",(function(){e._isWaiting&&e.suspendWaitingStatus("playing")})),e}return r(o,[{key:"afterCreate",value:function(){var e=this;this._onReset(),this._waitType="firstFrame",this._initOnceEvents(),this.newPointTime=Nt(),this.loadedCostTime=0,this.startCostTime=0,this.on($,(function(){var t=e._state,i=t.autoplayStart,n=t.isFFSend;e.startCostTime=Nt()-e.newPointTime,w.logInfo("[xgLogger]".concat(e.player.playerId," LOAD_START"),"autoplayStart:".concat(i," isFFSend:").concat(n," startCostTime:").concat(e.startCostTime," newPointTime").concat(e.newPointTime)),n||(!t.isLs&&e.emitLog(Ft,{}),t.isLs=!0,t.isTimeUpdate=!1,t.isFFLoading=!0,e.pt=Nt(),e.vt=0,e.fvt=0,e._initOnceEvents(),e._onWaitingLoadStart())})),this.on(X,(function(){e.vt=Nt(),e.fvt=e.vt-e.pt,e.loadedCostTime=e.vt-e.newPointTime;var t=e._state,i=t.isTimeUpdate,n=t.isFFSend,o=t.autoplayStart;w.logInfo("[xgLogger]".concat(e.player.playerId," LOADED_DATA"),"fvt:".concat(e.fvt," isTimeUpdate:").concat(e._state.isTimeUpdate," loadedCostTime:").concat(e.loadedCostTime)),(i||o)&&e._sendFF("loadedData"),n||e.emitLog(Ht,{}),e.suspendWaitingStatus("loadeddata")})),this.on(V,this._onSeeking),this.on(W,this._onSeeked),this.on(le,(function(){e.endState("destroy")})),this.on(ce,(function(){e.endState("urlChange"),w.logInfo("[xgLogger]".concat(e.player.playerId," URL_CHANGE")),e._state.isFFSend&&e._onReset()})),this.on([F,K],this._onPlaying),this.on(z,this._onWaiting),this.on(U,this._onError),this.on(Ee,(function(){w.logInfo("[xgLogger]".concat(e.player.playerId," RESET")),e.endState("reset"),e._initOnceEvents(),e._onReset()}))}},{key:"_initOnceEvents",value:function(){this.off(oe,this._onAutoplayStart),this.off(G,this._onTimeupdate),this.once(oe,this._onAutoplayStart),this.once(G,this._onTimeupdate)}},{key:"_sendFF",value:function(e){this.s=Nt();var t=this._state,i=t.isFFLoading,n=t.isFFSend;w.logInfo("[xgLogger]".concat(this.player.playerId," _sendFF"),"".concat(e," fvt:").concat(this.fvt," isFFLoading:").concat(i," !isFFSend:").concat(!n)),this.vt>0&&i&&!n&&(w.logInfo("[xgLogger]".concat(this.player.playerId," emitLog_firstFrame"),e),this._state.isFFLoading=!1,this._state.isFFSend=!0,this.emitLog(Bt,{fvt:this.fvt,costTime:this.fvt,vt:this.vt,startCostTime:this.startCostTime,loadedCostTime:this.loadedCostTime}))}},{key:"_startWaitTimeout",value:function(){var e=this;this._waittTimer&&x.clearTimeout(this,this._waittTimer),this._waittTimer=x.setTimeout(this,(function(){e.suspendWaitingStatus("timeout"),x.clearTimeout(e,e._waittTimer),e._waittTimer=null}),this.config.waitTimeout)}},{key:"endState",value:function(e){this.suspendWaitingStatus(e),this.suspendSeekingStatus(e)}},{key:"suspendSeekingStatus",value:function(e){if(this.seekingStart){var t=Nt(),i=t-this.seekingStart;this.seekingStart=0,this.emitLog(Gt,{end:t,costTime:i,endType:e})}}},{key:"suspendWaitingStatus",value:function(e){if(this._waitTimer&&(x.clearTimeout(this,this._waitTimer),this._waitTimer=null),this._waittTimer&&(x.clearTimeout(this,this._waittTimer),this._waittTimer=null),this._isWaiting=!1,this.waitingStart){var t=Nt(),i=t-this.waitingStart,n=t-this.fixedWaitingStart,o=this.config.waitTimeout;this._isWaiting=!1,this.waitingStart=0,this.fixedWaitingStart=0,this.emitLog(Vt,{fixedCostTime:n>o?o:n,costTime:i>o?o:i,type:"loadeddata"===e?1:this._waitType,endType:2===this._waitType?"seek":e})}}},{key:"emitLog",value:function(e,i){var n=this.player;this.emit(xe,t({t:Nt(),host:x.getHostFromUrl(n.currentSrc),vtype:n.vtype,eventType:e,currentTime:this.player.currentTime,readyState:n.video.readyState,networkState:n.video.networkState},i))}}],[{key:"pluginName",get:function(){return"xgLogger"}},{key:"defaultConfig",get:function(){return{waitTimeout:1e4}}}]),o}(ct);function Kt(){return(new DOMParser).parseFromString('<svg class="xgplayer-replay-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 78 78" width="78" height="78">\n  <path fill="#fff" transform="translate(20, 20)" d="M8.22708362,13.8757234 L11.2677371,12.6472196 C11.7798067,12.4403301 12.3626381,12.6877273 12.5695276,13.1997969 L12.9441342,14.1269807 C13.1510237,14.6390502 12.9036264,15.2218816 12.3915569,15.4287712 L6.8284538,17.6764107 L5.90126995,18.0510173 C5.38920044,18.2579068 4.80636901,18.0105096 4.5994795,17.49844 L1.97723335,11.0081531 C1.77034384,10.4960836 2.0177411,9.91325213 2.52981061,9.70636262 L3.45699446,9.33175602 C3.96906396,9.12486652 4.5518954,9.37226378 4.75878491,9.88433329 L5.67885163,12.1615783 C7.99551726,6.6766934 13.3983951,3 19.5,3 C27.7842712,3 34.5,9.71572875 34.5,18 C34.5,26.2842712 27.7842712,33 19.5,33 C15.4573596,33 11.6658607,31.3912946 8.87004692,28.5831991 C8.28554571,27.9961303 8.28762719,27.0463851 8.87469603,26.4618839 C9.46176488,25.8773827 10.4115101,25.8794641 10.9960113,26.466533 C13.2344327,28.7147875 16.263503,30 19.5,30 C26.127417,30 31.5,24.627417 31.5,18 C31.5,11.372583 26.127417,6 19.5,6 C14.4183772,6 9.94214483,9.18783811 8.22708362,13.8757234 Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var qt=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"registerIcons",value:function(){return{replay:Kt}}},{key:"afterCreate",value:function(){var e=this;ct.insert(this.icons.replay,this.root,0),this.__handleReplay=this.hook("replayClick",(function(){e.player.replay()}),{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(".xgplayer-replay",["click","touchend"],this.__handleReplay),this.on(H,(function(){if(e.playerConfig.loop||x.addClass(e.player.root,"replay"),!e.config.disable){e.show();var t=e.root.querySelector("path");if(t){var i=window.getComputedStyle(t).getPropertyValue("transform");if("string"==typeof i&&i.indexOf("none")>-1)return null;t.setAttribute("transform",i)}}})),this.on(N,(function(){e.hide()}))}},{key:"handleReplay",value:function(e){e.preventDefault(),e.stopPropagation(),this.player.replay(),x.removeClass(this.player.root,"replay")}},{key:"show",value:function(e){this.config.disable||(this.root.style.display="flex")}},{key:"enable",value:function(){this.config.disable=!1}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){this.unbind(".xgplayer-replay",["click","touchend"],this.__handleReplay)}},{key:"render",value:function(){return'<xg-replay class="xgplayer-replay">\n      <xg-replay-txt class="xgplayer-replay-txt" lang-key="'.concat(this.i18nKeys.REPLAY,'">').concat(this.i18n.REPLAY,"</xg-replay-txt>\n    </xg-replay>")}}],[{key:"pluginName",get:function(){return"replay"}},{key:"defaultConfig",get:function(){return{disable:!1}}}]),i}(ct),Yt=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"isEndedShow",get:function(){return this.config.isEndedShow},set:function(e){this.config.isEndedShow=e}},{key:"hide",value:function(){x.addClass(this.root,"hide")}},{key:"show",value:function(e){x.removeClass(this.root,"hide")}},{key:"beforeCreate",value:function(e){"string"==typeof e.player.config.poster&&(e.config.poster=e.player.config.poster)}},{key:"afterCreate",value:function(){var e=this;this.on(H,(function(){e.isEndedShow&&x.removeClass(e.root,"hide")})),this.config.hideCanplay?(this.once(G,(function(){e.onTimeUpdate()})),this.on(ce,(function(){x.removeClass(e.root,"hide"),x.addClass(e.root,"xg-showplay"),e.once(G,(function(){e.onTimeUpdate()}))}))):this.on(N,(function(){x.addClass(e.root,"hide")}))}},{key:"onTimeUpdate",value:function(){var e=this;this.player.currentTime?x.removeClass(this.root,"xg-showplay"):this.once(G,(function(){e.onTimeUpdate()}))}},{key:"update",value:function(e){e&&(this.config.poster=e,this.root.style.backgroundImage="url(".concat(e,")"))}},{key:"getBgSize",value:function(e){var t="";switch(e){case"cover":t="cover";break;case"contain":t="contain";break;case"fixHeight":t="auto 100%";break;default:t=""}return t?"background-size: ".concat(t,";"):""}},{key:"render",value:function(){var e=this.config,t=e.poster,i=e.hideCanplay,n=e.fillMode,o=e.notHidden,r=this.getBgSize(n),s=t?"background-image:url(".concat(t,");").concat(r):r;return'<xg-poster class="xgplayer-poster '.concat(o?"xg-not-hidden":i?"xg-showplay":"",'" style="').concat(s,'">\n    </xg-poster>')}}],[{key:"pluginName",get:function(){return"poster"}},{key:"defaultConfig",get:function(){return{isEndedShow:!0,hideCanplay:!1,notHidden:!1,poster:"",fillMode:"fixWidth"}}}]),i}(ct);function Xt(){return(new DOMParser).parseFromString('<svg class="play" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Zt(){return(new DOMParser).parseFromString('<svg class="pause" xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="3 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598,214h170v596h-170v-596zM256 810v-596h170v596h-170z"></path>\n</svg>\n',"image/svg+xml").firstChild}var Jt={};function $t(e){e?window.clearTimeout(e):Object.keys(Jt).map((function(e){window.clearTimeout(Jt[e].id),delete Jt[e]}))}var Qt=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),s(u(o=t.call(this,e)),"onPlayerReset",(function(){o.autoPlayStart=!1;var e="auto"===o.config.mode?"auto-hide":"hide";o.setAttr("data-state","play"),x.removeClass(o.root,e),o.show()})),s(u(o),"onAutoplayStart",(function(){if(!o.autoPlayStart){var e="auto"===o.config.mode?"auto-hide":"hide";x.addClass(o.root,e),o.autoPlayStart=!0,o.onPlayPause("play")}})),o.autoPlayStart=!1,o}return r(i,[{key:"afterCreate",value:function(){var e=this,t=this.player,i=this.playerConfig;this.initIcons(),this.once(ie,(function(){i&&(i.lang&&"en"===i.lang?x.addClass(t.root,"lang-is-en"):"jp"===i.lang&&x.addClass(t.root,"lang-is-jp"))})),this.on(oe,this.onAutoplayStart),i.autoplay||this.show(),this.on(re,(function(){var t="auto"===e.config.mode?"auto-hide":"hide";e.setAttr("data-state","play"),x.removeClass(e.root,t),e.show()})),this.on(N,(function(){e.onPlayPause("play")})),this.on(B,(function(){e.onPlayPause("pause")})),this.on(Ee,(function(){e.onPlayerReset()})),this.clickHandler=this.hook("startClick",this.switchPausePlay,{pre:function(t){t.cancelable&&t.preventDefault(),t.stopPropagation();var i=e.player.paused;e.emitUserAction(t,"switch_play_pause",{props:"paused",from:i,to:!i})}}),this.bind(["click","touchend"],this.clickHandler)}},{key:"registerIcons",value:function(){return{startPlay:{icon:Xt,class:"xg-icon-play"},startPause:{icon:Zt,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild("xg-start-inner",e.startPlay),this.appendChild("xg-start-inner",e.startPause)}},{key:"hide",value:function(){x.addClass(this.root,"hide")}},{key:"show",value:function(e){x.removeClass(this.root,"hide")}},{key:"focusHide",value:function(){x.addClass(this.root,"focus-hide")}},{key:"recover",value:function(){x.removeClass(this.root,"focus-hide")}},{key:"switchStatus",value:function(e){e?this.setAttr("data-state",this.player.paused?"pause":"play"):this.setAttr("data-state",this.player.paused?"play":"pause")}},{key:"animate",value:function(e){var t=this;this._animateId=function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{start:null,end:null};return Jt[e]&&window.clearTimeout(Jt[e].id),Jt[e]={},i.start&&i.start(),Jt[e].id=window.setTimeout((function(){i.end&&i.end(),window.clearTimeout(Jt[e].id),delete Jt[e]}),t),Jt[e].id}("pauseplay",400,{start:function(){x.addClass(t.root,"interact"),t.show(),t.switchStatus(!0)},end:function(){x.removeClass(t.root,"interact"),!e&&t.hide(),t._animateId=null}})}},{key:"endAnimate",value:function(){x.removeClass(this.root,"interact"),$t(this._animateId),this._animateId=null}},{key:"switchPausePlay",value:function(e){var t=this.player;(e.cancelable&&e.preventDefault(),e.stopPropagation(),t.state<vt)||(this.player.paused||t.state!==bt?t.play():t.pause())}},{key:"onPlayPause",value:function(e){var t=this.config,i=this.player;if(i&&!(i.state<bt)&&this.autoPlayStart){if("show"===t.mode)return this.switchStatus(),void this.show();if("auto"!==t.mode){if(t.isShowPause&&i.paused&&!i.ended||t.isShowEnd&&i.ended)return this.switchStatus(),this.show(),void this.endAnimate();if(t.disableAnimate)return this.switchStatus(),void this.hide();if("play"===e)this.autoPlayStart?this.animate():this.hide();else{if(!this.autoPlayStart||i.ended)return;this.animate()}}else this.switchStatus()}}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.clickHandler),$t(this._animateId)}},{key:"render",value:function(){var e=this.playerConfig.autoplay?"auto"===this.config.mode?"auto-hide":"hide":"";return'\n    <xg-start class="xgplayer-start '.concat(e,'">\n    <xg-start-inner></xg-start-inner>\n    </xg-start>')}}],[{key:"pluginName",get:function(){return"start"}},{key:"defaultConfig",get:function(){return{isShowPause:!1,isShowEnd:!1,disableAnimate:!1,mode:"hide"}}}]),i}(ct),ei=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"render",value:function(){var e=this.config.innerHtml,t=x.createDom("xg-enter","",{},"xgplayer-enter");if(e&&e instanceof window.HTMLElement)t.appendChild(e);else if(e&&"string"==typeof e)t.innerHTML=e;else{for(var i="",n=1;n<=12;n++)i+='<div class="xgplayer-enter-bar'.concat(n,'"></div>');t.innerHTML='<div class="xgplayer-enter-spinner">'.concat(i,"</div>")}return t}}],[{key:"pluginName",get:function(){return"enter"}},{key:"defaultConfig",get:function(){return{innerHtml:"",logo:""}}}]),i}(ct);function ti(e,t,i){try{return' <div class="xg-tips '.concat(i?"hide":" ",'" lang-key="').concat(e.i18nKeys[t],'">\n    ').concat(e.i18n[t],"\n    </div>")}catch(n){return'<div class="xg-tips hide"></div>'}}var ii=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"afterCreate",value:function(){this.getMini=this.getMini.bind(this),this.exitMini=this.exitMini.bind(this),this.bind("click",this.getMini)}},{key:"getMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"exitMini",value:function(){this.config.onClick&&this.config.onClick()}},{key:"destroy",value:function(){this.unbind(["click","touchend"],this.getMini)}},{key:"render",value:function(){var e="MINISCREEN";return'\n      <xg-icon class="xgplayer-miniicon">\n      <div class="xgplayer-icon btn-text"><span class="icon-text" lang-key="'.concat(this.i18nKeys[e],'">').concat(this.i18n[e],"</span></div>\n      </xg-icon>")}}],[{key:"pluginName",get:function(){return"miniscreenIcon"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:10}}}]),i}(ct);function ni(e){var t=parseFloat(e);return-1===e.indexOf("%")&&!Number.isNaN(t)&&t}var oi=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],ri=oi.length;function si(e){if("string"==typeof e&&(e=document.querySelector(e)),e&&"object"===i(e)&&e.nodeType){var t=function(e){return window.getComputedStyle(e)}(e);if("none"===t.display)return function(){for(var e={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},t=0;t<ri;t++)e[oi[t]]=0;return e}();var n={};n.width=e.offsetWidth,n.height=e.offsetHeight;for(var o=n.isBorderBox="border-box"===t.boxSizing,r=0;r<ri;r++){var s=oi[r],a=t[s],l=parseFloat(a);n[s]=Number.isNaN(l)?0:l}var c=n.paddingLeft+n.paddingRight,u=n.paddingTop+n.paddingBottom,h=n.marginLeft+n.marginRight,d=n.marginTop+n.marginBottom,f=n.borderLeftWidth+n.borderRightWidth,p=n.borderTopWidth+n.borderBottomWidth,g=o,v=ni(t.width);!1!==v&&(n.width=v+(g?0:c+f));var y=ni(t.height);return!1!==y&&(n.height=y+(g?0:u+p)),n.innerWidth=n.width-(c+f),n.innerHeight=n.height-(u+p),n.outerWidth=n.width+h,n.outerHeight=n.height+d,n}}function ai(e,t){for(var i=0;i<e.length;i++){var n=e[i];if(n.identifier===t)return n}}var li="dragStart",ci="dragMove",ui="dragEnded",hi={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]},di=function(e){a(i,e);var t=d(i);function i(e){var o,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n(this,i),(o=t.call(this)).isEnabled=!0,o.isDragging=!1,o.isDown=!1,o.position={},o.downPoint={},o.dragPoint={x:0,y:0},o.startPos={x:0,y:0},o._root=e instanceof Element?e:document.querySelector(e),o._handlerDom=r.handle instanceof Element?r.handle:document.querySelector(r.handle),o._root&&o._handlerDom?(o._bindStartEvent(),o):h(o)}return r(i,[{key:"_bindStartEvent",value:function(){var e=this;"ontouchstart"in window?this._startKey="touchstart":this._startKey="mousedown",this["on".concat(this._startKey)]=this["on".concat(this._startKey)].bind(this),this._handlerDom.addEventListener(this._startKey,this["on".concat(this._startKey)]),hi[this._startKey].map((function(t){e["on".concat(t)]=e["on".concat(t)].bind(e)}))}},{key:"_unbindStartEvent",value:function(){this._handlerDom.removeEventListener(this._startKey,this["on".concat(this._startKey)])}},{key:"_bindPostStartEvents",value:function(e){var t=this;if(e){var i=hi[this._startKey];i.map((function(e){window.addEventListener(e,t["on".concat(e)])})),this._boundPointerEvents=i}}},{key:"_unbindPostStartEvents",value:function(){var e=this;this._boundPointerEvents&&(this._boundPointerEvents.map((function(t){window.removeEventListener(t,e["on".concat(t)])})),delete this._boundPointerEvents)}},{key:"enable",value:function(){this.isEnabled=!0}},{key:"disable",value:function(){this.isEnabled=!1,this.isDragging&&this.onUp()}},{key:"onDocUp",value:function(e){this.onUp()}},{key:"animate",value:function(){var e=this;this.isDragging&&(this.positionDrag(),window.requestAnimationFrame((function(){e.animate()})))}},{key:"positionDrag",value:function(){var e="translate3d(".concat(this.dragPoint.x,"px, ").concat(this.dragPoint.y,"px, 0)");this._root.style.transform=e,this._root.style.webKitTransform=e}},{key:"setLeftTop",value:function(){this._root.style.left=this.position.x+"px",this._root.style.top=this.position.y+"px"}},{key:"onmousedown",value:function(e){this.dragStart(e,e)}},{key:"onmousemove",value:function(e){this.dragMove(e,e)}},{key:"onmouseup",value:function(e){this.dragEnd(e,e)}},{key:"ontouchstart",value:function(e){var t=e.changedTouches[0];this.dragStart(e,t),this.touchIdentifier=void 0!==t.pointerId?t.pointerId:t.identifier,e.preventDefault()}},{key:"ontouchmove",value:function(e){var t=ai(e.changedTouches,this.touchIdentifier);t&&this.dragMove(e,t)}},{key:"ontouchend",value:function(e){var t=ai(e.changedTouches,this.touchIdentifier);t&&this.dragEnd(e,t),e.preventDefault()}},{key:"ontouchcancel",value:function(e){var t=ai(e.changedTouches,this.touchIdentifier);t&&this.dragCancel(e,t)}},{key:"dragStart",value:function(e,t){if(this._root&&!this.isDown&&this.isEnabled){this.downPoint=t,this.dragPoint.x=0,this.dragPoint.y=0,this._getPosition();var i=si(this._root);this.startPos.x=this.position.x,this.startPos.y=this.position.y,this.startPos.maxY=window.innerHeight-i.height,this.startPos.maxX=window.innerWidth-i.width,this.setLeftTop(),this.isDown=!0,this._bindPostStartEvents(e)}}},{key:"dragRealStart",value:function(e,t){this.isDragging=!0,this.animate(),this.emit(li,this.startPos)}},{key:"dragEnd",value:function(e,t){this._root&&(this._unbindPostStartEvents(),this.isDragging&&(this._root.style.transform="",this.setLeftTop(),this.emit(ui)),this.presetInfo())}},{key:"_dragPointerMove",value:function(e,t){var i={x:t.pageX-this.downPoint.pageX,y:t.pageY-this.downPoint.pageY};return!this.isDragging&&this.hasDragStarted(i)&&this.dragRealStart(e,t),i}},{key:"dragMove",value:function(e,t){if(e=e||window.event,this.isDown){var i=this.startPos,n=i.x,o=i.y,r=this._dragPointerMove(e,t),s=r.x,a=r.y;s=this.checkContain("x",s,n),a=this.checkContain("y",a,o),this.position.x=n+s,this.position.y=o+a,this.dragPoint.x=s,this.dragPoint.y=a,this.emit(ci,this.position)}}},{key:"dragCancel",value:function(e,t){this.dragEnd(e,t)}},{key:"presetInfo",value:function(){this.isDragging=!1,this.startPos={x:0,y:0},this.dragPoint={x:0,y:0},this.isDown=!1}},{key:"destroy",value:function(){this._unbindStartEvent(),this._unbindPostStartEvents(),this.isDragging&&this.dragEnd(),this.removeAllListeners(),this._handlerDom=null}},{key:"hasDragStarted",value:function(e){return Math.abs(e.x)>3||Math.abs(e.y)>3}},{key:"checkContain",value:function(e,t,i){return t+i<0?0-i:"x"===e&&t+i>this.startPos.maxX?this.startPos.maxX-i:"y"===e&&t+i>this.startPos.maxY?this.startPos.maxY-i:t}},{key:"_getPosition",value:function(){var e=window.getComputedStyle(this._root),t=this._getPositionCoord(e.left,"width"),i=this._getPositionCoord(e.top,"height");this.position.x=Number.isNaN(t)?0:t,this.position.y=Number.isNaN(i)?0:i,this._addTransformPosition(e)}},{key:"_addTransformPosition",value:function(e){var t=e.transform;if(0===t.indexOf("matrix")){var i=t.split(","),n=0===t.indexOf("matrix3d")?12:4,o=parseInt(i[n],10),r=parseInt(i[n+1],10);this.position.x+=o,this.position.y+=r}}},{key:"_getPositionCoord",value:function(e,t){if(-1!==e.indexOf("%")){var i=si(this._root.parentNode);return i?parseFloat(e)/100*i[t]:0}return parseInt(e,10)}}]),i}(k),fi=function(e){a(i,e);var t=d(i);function i(e){var o;n(this,i),s(u(o=t.call(this,e)),"onCancelClick",(function(e){o.exitMini(),o.isClose=!0})),s(u(o),"onCenterClick",(function(e){var t=u(o).player;t.paused?t.play():t.pause()})),s(u(o),"onScroll",(function(e){if(!(!window.scrollY&&0!==window.scrollY||Math.abs(window.scrollY-o.pos.scrollY)<50)){var t=parseInt(x.getCss(o.player.root,"height"));t+=o.config.scrollTop,o.pos.scrollY=window.scrollY,window.scrollY>t+5?!o.isMini&&!o.isClose&&o.getMini():window.scrollY<=t&&(o.isMini&&o.exitMini(),o.isClose=!1)}})),o.isMini=!1,o.isClose=!1;var r=u(o).config;return o.pos={left:r.left<0?window.innerWidth-r.width-20:r.left,top:r.top<0?window.innerHeight-r.height-20:r.top,height:o.config.height,width:o.config.width,scrollY:window.scrollY||0},o.lastStyle=null,o}return r(i,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.mini&&(e.config.isShowIcon=e.player.config.mini)}},{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.on(B,(function(){e.setAttr("data-state","pause")})),this.on(N,(function(){e.setAttr("data-state","play")}))}},{key:"onPluginsReady",value:function(){var e=this,t=this.player;if(!this.config.disable){if(this.config.isShowIcon){var i={config:{onClick:function(){e.getMini()}}};t.controls.registerPlugin(ii,i,ii.pluginName)}var n=x.checkTouchSupport()?"touchend":"click";this.bind(".mini-cancel-btn",n,this.onCancelClick),this.bind(".play-icon",n,this.onCenterClick),this.config.disableDrag||(this._draggabilly=new di(this.player.root,{handle:this.root})),this.config.isScrollSwitch&&window.addEventListener("scroll",this.onScroll)}}},{key:"registerIcons",value:function(){return{play:{icon:Xt,class:"xg-icon-play"},pause:{icon:Zt,class:"xg-icon-pause"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".play-icon",e.play),this.appendChild(".play-icon",e.pause)}},{key:"getMini",value:function(){var e=this;if(!this.isMini){var t=this.player,i=this.playerConfig,n=this.config.target||this.player.root;this.lastStyle={},x.addClass(t.root,"xgplayer-mini"),["width","height","top","left"].map((function(t){e.lastStyle[t]=n.style[t],n.style[t]="".concat(e.pos[t],"px")})),i.fluid&&(n.style["padding-top"]=""),this.emit(ve,!0),t.isMini=this.isMini=!0}}},{key:"exitMini",value:function(){var e=this;if(!this.isMini)return!1;var t=this.player,i=this.playerConfig,n=this.config.target||this.player.root;x.removeClass(t.root,"xgplayer-mini"),this.lastStyle&&Object.keys(this.lastStyle).map((function(t){n.style[t]=e.lastStyle[t]})),this.lastStyle=null,i.fluid&&(t.root.style.width="100%",t.root.style.height="0",t.root.style["padding-top"]="".concat(100*i.height/i.width,"%")),this.emit(ve,!1),this.isMini=t.isMini=!1}},{key:"destroy",value:function(){window.removeEventListener("scroll",this.onScroll);var e=x.checkTouchSupport()?"touchend":"click";this.unbind(".mini-cancel-btn",e,this.onCancelClick),this.unbind(".play-icon",e,this.onCenterClick),this._draggabilly&&this._draggabilly.destroy(),this._draggabilly=null,this.exitMini()}},{key:"render",value:function(){if(!this.config.disable)return'\n      <xg-mini-layer class="xg-mini-layer">\n      <xg-mini-header class="xgplayer-mini-header">\n      '.concat(ti(this,"MINI_DRAG",this.playerConfig.isHideTips),'\n      </xg-mini-header>\n      <div class="mini-cancel-btn">\n        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">\n          <path fill="#fff" fill-rule="evenodd" d="M3.99 3.49a1 1 0 0 1 1.414 0L10 8.085l4.596-4.595a1 1 0 1 1 1.414 1.414L11.414 9.5l4.596 4.596a1 1 0 0 1 .084 1.32l-.084.094a1 1 0 0 1-1.414 0L10 10.914 5.404 15.51a1 1 0 0 1-1.414-1.414L8.585 9.5 3.99 4.904a1 1 0 0 1-.084-1.32z"></path>\n        </svg>\n      </div>\n      <div class="play-icon">\n      </div>\n      </xg-mini-layer>')}}],[{key:"pluginName",get:function(){return"miniscreen"}},{key:"defaultConfig",get:function(){return{index:10,disable:!1,width:320,height:180,left:-1,top:-1,isShowIcon:!1,isScrollSwitch:!1,scrollTop:0,disableDrag:!1}}}]),i}(ct),pi={mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",mousemove:"onMouseMove"},gi=["videoClick","videoDbClick"],vi=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"onMouseMove",(function(t){var i=u(e),n=i.player,o=i.playerConfig;n.isActive||(n.focus({autoHide:!o.closeDelayBlur}),!o.closeFocusVideoFocus&&n.media.focus())})),s(u(e),"onMouseEnter",(function(t){var i=u(e),n=i.playerConfig,o=i.player;!n.closeFocusVideoFocus&&o.media.focus(),n.closeDelayBlur?o.focus({autoHide:!1}):o.focus(),e.emit(de)})),s(u(e),"onMouseLeave",(function(t){var i=e.playerConfig,n=i.closePlayerBlur,o=i.leavePlayerTime,r=i.closeDelayBlur;n||r||(o?e.player.focus({autoHide:!0,delay:o}):e.player.blur({ignorePaused:!0})),e.emit(he)})),s(u(e),"onVideoClick",(function(t){var i=u(e),n=i.player,o=i.playerConfig;t.target&&o.closeVideoClick||t.target!==n.root&&t.target!==n.media&&t.target!==n.innerContainer&&t.target!==n.media.__canvas||(t.preventDefault(),o.closeVideoStopPropagation||t.stopPropagation(),e._clickCount++,e.clickTimer&&(clearTimeout(e.clickTimer),e.clickTimer=null),e.clickTimer=setTimeout((function(){e._clickCount&&(e._clickCount--,Ze(u(e),gi[0],(function(t,i){e.switchPlayPause(i.e)}),{e:t,paused:n.paused}),clearTimeout(e.clickTimer),e.clickTimer=null)}),300))})),s(u(e),"onVideoDblClick",(function(t){var i=u(e),n=i.player,o=i.playerConfig;o.closeVideoDblclick||!t.target||t.target!==n.media&&t.target!==n.media.__canvas||(!o.closeVideoClick&&e._clickCount<2?e._clickCount=0:(e._clickCount=0,e.clickTimer&&(clearTimeout(e.clickTimer),e.clickTimer=null),t.preventDefault(),t.stopPropagation(),Ze(u(e),gi[1],(function(t,i){e.emitUserAction(i.e,"switch_fullscreen",{props:"fullscreen",from:n.fullscreen,to:!n.fullscreen}),n.fullscreen?n.exitFullscreen():n.getFullscreen()}),{e:t,fullscreen:n.fullscreen})))})),e}return r(i,[{key:"afterCreate",value:function(){var e=this;this._clickCount=0,gi.map((function(t){e.__hooks[t]=null})),"mobile"===this.playerConfig.isMobileSimulateMode||"mobile"===O.device&&!O.os.isIpad||this.initEvents()}},{key:"initEvents",value:function(){var e=this,t=this.player,i=t.media,n=t.root,o=this.playerConfig.enableContextmenu;n&&n.addEventListener("click",this.onVideoClick,!1),n&&n.addEventListener("dblclick",this.onVideoDblClick,!1),Object.keys(pi).map((function(t){n.addEventListener(t,e[pi[t]],!1)})),!o&&i&&i.addEventListener("contextmenu",this.onContextmenu,!1)}},{key:"switchPlayPause",value:function(e){var t=this.player;this.emitUserAction(e,"switch_play_pause",{props:"paused",from:t.paused,to:!t.paused}),t.ended?t.duration!==1/0&&t.duration>0&&t.replay():t.paused?t.play():t.pause()}},{key:"onContextmenu",value:function(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.stopPropagation?e.stopPropagation():(e.returnValue=!1,e.cancelBubble=!0)}},{key:"destroy",value:function(){var e=this,t=this.player,i=t.video,n=t.root;this.clickTimer&&clearTimeout(this.clickTimer),n.removeEventListener("click",this.onVideoClick,!1),n.removeEventListener("dblclick",this.onVideoDblClick,!1),i.removeEventListener("contextmenu",this.onContextmenu,!1),Object.keys(pi).map((function(t){n.removeEventListener(t,e[pi[t]],!1)}))}}],[{key:"pluginName",get:function(){return"pc"}},{key:"defaultConfig",get:function(){return{}}}]),i}($e),yi="press",mi="pressend",ki="doubleclick",bi="click",Ci="touchmove",_i="touchstart",wi="touchend",Ti={start:"touchstart",end:"touchend",move:"touchmove",cancel:"touchcancel"},xi={start:"mousedown",end:"mouseup",move:"mousemove",cancel:"mouseleave"};function Si(e){return e&&e.length>0?e[e.length-1]:null}var Ei=function(){function e(t){var i=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{eventType:"touch"};n(this,e),s(this,"onTouchStart",(function(e){var t=i._pos,n=i.root,o=Si(e.touches);t.x=o?parseInt(o.pageX,10):e.pageX,t.y=o?parseInt(o.pageX,10):e.pageX,t.start=!0,i.__setPress(e),n.addEventListener(i.events.end,i.onTouchEnd),n.addEventListener(i.events.cancel,i.onTouchCancel),n.addEventListener(i.events.move,i.onTouchMove),i.trigger(_i,e)})),s(this,"onTouchCancel",(function(e){i.onTouchEnd(e)})),s(this,"onTouchEnd",(function(e){var t=i._pos,n=i.root;i.__clearPress(),n.removeEventListener(i.events.cancel,i.onTouchCancel),n.removeEventListener(i.events.end,i.onTouchEnd),n.removeEventListener(i.events.move,i.onTouchMove),e.moving=t.moving,e.press=t.press,t.press&&i.trigger(mi,e),i.trigger(wi,e),!t.press&&!t.moving&&i.__setDb(e),t.press=!1,t.start=!1,t.moving=!1})),s(this,"onTouchMove",(function(e){var t=i._pos,n=i.config,o=Si(e.touches),r=o?parseInt(o.pageX,10):e.pageX,s=o?parseInt(o.pageY,10):e.pageX,a=r-t.x,l=s-t.y;Math.abs(l)<n.miniStep&&Math.abs(a)<n.miniStep||(i.__clearPress(),t.press&&i.trigger(mi,e),t.press=!1,t.moving=!0,i.trigger(Ci,e))})),this._pos={moving:!1,start:!1,x:0,y:0},this.config={pressDelay:600,dbClickDelay:200,disablePress:!1,disableDbClick:!1,miniStep:2,needPreventDefault:!0},Object.keys(o).map((function(e){i.config[e]=o[e]})),this.root=t,this.events="mouse"===o.eventType?xi:Ti,this.pressIntrvalId=null,this.dbIntrvalId=null,this.__handlers={},this._initEvent()}return r(e,[{key:"_initEvent",value:function(){this.root.addEventListener(this.events.start,this.onTouchStart)}},{key:"__setPress",value:function(e){var t=this,i=this.config;this.pressIntrvalId&&this.__clearPress(),this.pressIntrvalId=setTimeout((function(){t.trigger(yi,e),t._pos.press=!0,t.__clearPress()}),i.pressDelay)}},{key:"__clearPress",value:function(){window.clearTimeout(this.pressIntrvalId),this.pressIntrvalId=null}},{key:"__setDb",value:function(e){var t=this,i=this.config;if(this.dbIntrvalId)return this.__clearDb(),void this.trigger(ki,e);this.dbIntrvalId=setTimeout((function(){t.__clearDb(),t._pos.start||t._pos.press||t._pos.moving||t.trigger(bi,e)}),i.dbClickDelay)}},{key:"__clearDb",value:function(){clearTimeout(this.dbIntrvalId),this.dbIntrvalId=null}},{key:"on",value:function(e,t){this.__handlers[e]||(this.__handlers[e]=[]),this.__handlers[e].push(t)}},{key:"off",value:function(e,t){if(this.__handlers[e]){for(var i=this.__handlers[e],n=-1,o=0;o<i.length;o++)if(i[o]===t){n=o;break}n>=0&&this.__handlers[e].splice(n,1)}}},{key:"trigger",value:function(e,t){this.__handlers[e]&&this.__handlers[e].map((function(i){try{i(t)}catch(n){console.error("trigger>>:".concat(e),n)}}))}},{key:"destroy",value:function(){var e=this,t={touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"};Object.keys(t).forEach((function(i){e.root.removeEventListener(i,e[t[i]])}))}}]),e}();function Pi(){return(new DOMParser).parseFromString('<svg width="20" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg"\n  xmlns:xlink="http://www.w3.org/1999/xlink">\n  <path opacity="0.54"\n    d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z"\n    fill="white" />\n  <path transform="translate(5 0)" d="M7.5 3.63397C8.16667 4.01887 8.16667 4.98113 7.5 5.36603L1.5 8.83013C0.833334 9.21503 0 8.7339 0 7.9641L0 1.0359C0 0.266098 0.833333 -0.215027 1.5 0.169873L7.5 3.63397Z" fill="white"/>\n</svg>',"image/svg+xml").firstChild}var Ii="auto",Li="seeking",Ai="playbackrate",Oi=["videoClick","videoDbClick"],Di=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),s(u(o=t.call(this,e)),"onTouchStart",(function(e){var t=u(o),i=t.player,n=t.config,r=t.pos,s=t.playerConfig,a=o.getTouche(e);if(a&&!n.disableGesture&&o.duration>0&&!i.ended){r.isStart=!0,x.checkIsFunction(s.disableSwipeHandler)&&s.disableSwipeHandler(),o.find(".xg-dur").innerHTML=x.format(o.duration);var l=o.root.getBoundingClientRect();90===i.rotateDeg?(r.top=l.left,r.left=l.top,r.width=l.height,r.height=l.width):(r.top=l.top,r.left=l.left,r.width=l.width,r.height=l.height);var c=parseInt(a.pageX-r.left,10),h=parseInt(a.pageY-r.top,10);r.x=90===i.rotateDeg?h:c,r.y=90===i.rotateDeg?c:h,r.scopeL=n.scopeL*r.width,r.scopeR=(1-n.scopeR)*r.width,r.scopeM1=r.width*(1-n.scopeM)/2,r.scopeM2=r.width-r.scopeM1}})),s(u(o),"onTouchMove",(function(e){var t=o.getTouche(e),i=u(o),n=i.pos,r=i.config,s=i.player;if(t&&!r.disableGesture&&o.duration&&n.isStart){var a=r.miniMoveStep,l=r.hideControlsActive,c=parseInt(t.pageX-n.left,10),h=parseInt(t.pageY-n.top,10),d=90===s.rotateDeg?h:c,f=90===s.rotateDeg?c:h;if(Math.abs(d-n.x)>a||Math.abs(f-n.y)>a){var p=d-n.x,g=f-n.y,v=n.scope;if(-1===v&&(0===(v=o.checkScope(d,f,p,g,n))&&(l?s.blur():s.focus({autoHide:!1}),!n.time&&(n.time=parseInt(1e3*s.currentTime,10)+1e3*o.timeOffset)),n.scope=v),-1===v||v>0&&!r.gestureY||0===v&&!r.gestureX)return;e.cancelable&&e.preventDefault(),o.executeMove(p,g,v,n.width,n.height),n.x=d,n.y=f}}})),s(u(o),"onTouchEnd",(function(e){var t=u(o),i=t.player,n=t.pos,r=t.playerConfig;if(n.isStart){n.scope>-1&&e.cancelable&&e.preventDefault();var s=o.config,a=s.disableGesture,l=s.gestureX;!a&&l?(o.endLastMove(n.scope),setTimeout((function(){i.getPlugin("progress")&&i.getPlugin("progress").resetSeekState()}),10)):n.time=0,n.scope=-1,o.resetPos(),x.checkIsFunction(r.enableSwipeHandler)&&r.enableSwipeHandler(),o.changeAction(Ii)}})),s(u(o),"onRootTouchMove",(function(e){!o.config.disableGesture&&o.config.gestureX&&o.checkIsRootTarget(e)&&(e.stopPropagation(),o.pos.isStart?o.onTouchMove(e):o.onTouchStart(e))})),s(u(o),"onRootTouchEnd",(function(e){o.pos.isStart&&o.checkIsRootTarget(e)&&(e.stopPropagation(),o.onTouchEnd(e))})),o.pos={isStart:!1,x:0,y:0,time:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1},o.timer=null,o}return r(i,[{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"registerIcons",value:function(){return{seekTipIcon:{icon:Pi,class:"xg-seek-pre"}}}},{key:"afterCreate",value:function(){var e=this;Oi.map((function(t){e.__hooks[t]=null}));var t=this.playerConfig,i=this.config,n=this.player;!0===t.closeVideoDblclick&&(i.closedbClick=!0),this.resetPos(),x.isUndefined(t.disableGesture)||(i.disableGesture=!!t.disableGesture),this.appendChild(".xg-seek-icon",this.icons.seekTipIcon),this.xgMask=x.createDom("xg-mask","",{},"xgmask"),n.root.appendChild(this.xgMask),this.initCustomStyle(),this.registerThumbnail();var o="mouse"===this.domEventType?"mouse":"touch";this.touch=new Ei(this.root,{eventType:o,needPreventDefault:!this.config.disableGesture}),this.root.addEventListener("contextmenu",(function(e){e.preventDefault()})),n.root.addEventListener("touchmove",this.onRootTouchMove,!0),n.root.addEventListener("touchend",this.onRootTouchEnd,!0),this.on(q,(function(){var t=e.player,i=e.config;1e3*t.duration<i.moveDuration&&(i.moveDuration=1e3*t.duration)})),this.on([K,H],(function(){var t=e.pos,i=t.time;!t.isStart&&i>0&&(e.pos.time=0)}));var r={touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd",press:"onPress",pressend:"onPressEnd",click:"onClick",doubleclick:"onDbClick"};if(Object.keys(r).map((function(t){e.touch.on(t,(function(i){e[r[t]](i)}))})),!i.disableActive){var s=n.plugins.progress;s&&(s.addCallBack("dragmove",(function(t){e.activeSeekNote(t.currentTime,t.forward)})),s.addCallBack("dragend",(function(){e.changeAction(Ii)})))}}},{key:"registerThumbnail",value:function(){var e=this.player.plugins.thumbnail;if(e&&e.usable){this.thumbnail=e.createThumbnail(null,"mobile-thumbnail");var t=this.find(".time-preview");t.insertBefore(this.thumbnail,t.children[0])}}},{key:"initCustomStyle",value:function(){var e=(this.playerConfig||{}).commonStyle,t=e.playedColor,i=e.progressColor,n=e.timePreviewStyle,o=e.curTimeColor,r=e.durationColor;if(t&&(this.find(".xg-curbar").style.backgroundColor=t),i&&(this.find(".xg-bar").style.backgroundColor=i),n){var s=this.find(".time-preview");Object.keys(n).forEach((function(e){s.style[e]=n[e]}))}var a=o||t,l=r;a&&(this.find(".xg-cur").style.color=a),l&&(this.find(".xg-dur").style.color=l),this.config.disableTimeProgress&&x.addClass(this.find(".xg-timebar"),"hide")}},{key:"resetPos",value:function(){var e=this;this.pos?(this.pos.isStart=!1,this.pos.scope=-1,["x","y","width","height","scopeL","scopeR","scopeM1","scopeM2"].map((function(t){e.pos[t]=0}))):this.pos={isStart:!1,x:0,y:0,volume:0,rate:1,light:0,width:0,height:0,scopeL:0,scopeR:0,scopeM1:0,scopeM2:0,scope:-1,time:0}}},{key:"changeAction",value:function(e){var t=this.player;this.root.setAttribute("data-xg-action",e);var i=t.plugins.start;i&&i.recover()}},{key:"getTouche",value:function(e){this.player.rotateDeg;var t=e.touches&&e.touches.length>0?e.touches[e.touches.length-1]:e;return{pageX:t.pageX,pageY:t.pageY}}},{key:"checkScope",value:function(e,t,i,n,o){var r=o.width,s=-1;if(e<0||e>r)return s;var a=0===n?Math.abs(i):Math.abs(i/n);return Math.abs(i)>0&&a>=1.73&&e>o.scopeM1&&e<o.scopeM2?s=0:(0===Math.abs(i)||a<=.57)&&(s=e<o.scopeL?1:e>o.scopeR?2:3),s}},{key:"executeMove",value:function(e,t,i,n,o){switch(i){case 0:this.updateTime(e/n*this.config.scopeM);break;case 1:this.updateBrightness(t/o);break;case 2:O.os.isIos||this.updateVolume(t/o)}}},{key:"endLastMove",value:function(e){var t=this,i=this.pos,n=this.player,o=this.config,r=(i.time-this.timeOffset)/1e3;if(0===e)n.seek(Number(r).toFixed(1)),o.hideControlsEnd?n.blur():n.focus(),this.timer=setTimeout((function(){t.pos.time=0}),500);this.changeAction(Ii)}},{key:"checkIsRootTarget",value:function(e){var t=this.player.plugins||{};return(!t.progress||!t.progress.root.contains(e.target))&&(t.start&&t.start.root.contains(e.target)||t.controls&&t.controls.root.contains(e.target))}},{key:"sendUseAction",value:function(e){var t=this.player.paused;this.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t,to:!t})}},{key:"clickHandler",value:function(e){var t=this.player,i=this.config,n=this.playerConfig;t.state<bt?n.closeVideoClick||(this.sendUseAction(x.createEvent("click")),t.play()):!i.closedbClick||n.closeVideoClick?t.isActive?t.blur():t.focus():n.closeVideoClick||((t.isActive||i.focusVideoClick)&&(this.sendUseAction(x.createEvent("click")),this.switchPlayPause()),t.focus())}},{key:"dbClickHandler",value:function(e){var t=this.config,i=this.player;!t.closedbClick&&i.state>=bt&&(this.sendUseAction(x.createEvent("dblclick")),this.switchPlayPause())}},{key:"onClick",value:function(e){var t=this,i=this.player;Ze(this,Oi[0],(function(e,i){t.clickHandler(i.e)}),{e:e,paused:i.paused})}},{key:"onDbClick",value:function(e){var t=this,i=this.player;Ze(this,Oi[1],(function(e,i){t.dbClickHandler(i.e)}),{e:e,paused:i.paused})}},{key:"onPress",value:function(e){var t=this.pos,i=this.config,n=this.player;i.disablePress||(t.rate=this.player.playbackRate,this.emitUserAction("press","change_rate",{prop:"playbackRate",from:n.playbackRate,to:i.pressRate}),n.playbackRate=i.pressRate,this.changeAction(Ai))}},{key:"onPressEnd",value:function(e){var t=this.pos,i=this.config,n=this.player;i.disablePress||(this.emitUserAction("pressend","change_rate",{prop:"playbackRate",from:n.playbackRate,to:t.rate}),n.playbackRate=t.rate,t.rate=1,this.changeAction(Ii))}},{key:"updateTime",value:function(e){var t=this.player,i=this.config,n=this.player.duration;e=Number(e.toFixed(4));var o=parseInt(e*i.moveDuration,10)+this.timeOffset;o=(o+=this.pos.time)<0?0:o>1e3*n?1e3*n-200:o,t.getPlugin("time")&&t.getPlugin("time").updateTime(o/1e3),t.getPlugin("progress")&&t.getPlugin("progress").updatePercent(o/1e3/this.duration,!0),this.activeSeekNote(o/1e3,e>0),i.isTouchingSeek&&t.seek(Number((o-this.timeOffset)/1e3).toFixed(1)),this.pos.time=o}},{key:"updateVolume",value:function(e){this.player.rotateDeg&&(e=-e);var t=this.player,i=this.pos;if(e=parseInt(100*e,10),i.volume+=e,!(Math.abs(i.volume)<10)){var n=parseInt(10*t.volume,10)-parseInt(i.volume/10,10);n=n>10?10:n<1?0:n,t.volume=n/10,i.volume=0}}},{key:"updateBrightness",value:function(e){this.player.rotateDeg&&(e=-e);var t=this.pos,i=this.config,n=this.xgMask,o=t.light+.8*e;o=o>i.maxDarkness?i.maxDarkness:o<0?0:o,n&&(n.style.backgroundColor="rgba(0,0,0,".concat(o,")")),t.light=o}},{key:"activeSeekNote",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.player,n=this.config,o=!(this.duration!==1/0&&this.duration>0);if(e&&"number"==typeof e&&!o&&!n.disableActive){e<0?e=0:e>i.duration&&(e=i.duration-.2),this.changeAction(Li);var r=i.plugins.start;r&&r.focusHide(),this.find(".xg-dur").innerHTML=x.format(this.duration),this.find(".xg-cur").innerHTML=x.format(e),this.find(".xg-curbar").style.width="".concat(e/this.duration*100,"%"),t?x.removeClass(this.find(".xg-seek-show"),"xg-back"):x.addClass(this.find(".xg-seek-show"),"xg-back"),this.updateThumbnails(e)}}},{key:"updateThumbnails",value:function(e){var t=this.player.plugins.thumbnail;t&&t.usable&&this.thumbnail&&t.update(this.thumbnail,e,160,90)}},{key:"switchPlayPause",value:function(){var e=this.player;if(e.state<mt)return!1;e.ended||(e.paused?e.play():e.pause())}},{key:"disableGesture",value:function(){this.config.disableGesture=!1}},{key:"enableGesture",value:function(){this.config.disableGesture=!0}},{key:"destroy",value:function(){var e=this.player;this.timer&&clearTimeout(this.timer),this.thumbnail=null,e.root.removeChild(this.xgMask),this.xgMask=null,this.touch&&this.touch.destroy(),this.touch=null,e.root.removeEventListener("touchmove",this.onRootTouchMove,!0),e.root.removeEventListener("touchend",this.onRootTouchEnd,!0)}},{key:"render",value:function(){var e="normal"!==this.config.gradient?"gradient ".concat(this.config.gradient):"gradient";return'\n     <xg-trigger class="trigger">\n     <div class="'.concat(e,'"></div>\n        <div class="time-preview">\n            <div class="xg-seek-show ').concat(this.config.disableSeekIcon?" hide-seek-icon":"",'">\n              <i class="xg-seek-icon"></i>\n              <span class="xg-cur">00:00</span>\n              <span class="xg-separator">/</span>\n              <span class="xg-dur">00:00</span>\n            </div>\n              <div class="xg-bar xg-timebar">\n                <div class="xg-curbar"></div>\n              </div>\n        </div>\n        <div class="xg-playbackrate xg-top-note">\n            <span><i>').concat(this.config.pressRate,"X</i>").concat(this.i18n.FORWARD,"</span>\n        </div>\n     </xg-trigger>\n    ")}}],[{key:"pluginName",get:function(){return"mobile"}},{key:"defaultConfig",get:function(){return{index:0,disableGesture:!1,gestureX:!0,gestureY:!0,gradient:"normal",isTouchingSeek:!1,miniMoveStep:5,miniYPer:5,scopeL:.25,scopeR:.25,scopeM:.9,pressRate:2,darkness:!0,maxDarkness:.8,disableActive:!1,disableTimeProgress:!1,hideControlsActive:!1,hideControlsEnd:!1,moveDuration:36e4,closedbClick:!1,disablePress:!0,disableSeekIcon:!1,focusVideoClick:!1}}}]),i}(ct);function Ri(e){var t=e.tagName;return!("INPUT"!==t&&"TEXTAREA"!==t&&!e.isContentEditable)}var Mi=function(e){a(o,e);var i=d(o);function o(){var e;n(this,o);for(var t=arguments.length,r=new Array(t),a=0;a<t;a++)r[a]=arguments[a];return s(u(e=i.call.apply(i,[this].concat(r))),"onBodyKeyDown",(function(t){if(e.player){var i=t||window.event,n=i.keyCode,o=u(e),r=o._keyState,s=o.player,a=e.config,l=a.disable,c=a.disableBodyTrigger,h=a.isIgnoreUserActive;l||c||!s.isUserActive&&!h||Ri(i.target)||!e.checkIsVisible()||i.metaKey||i.altKey||i.ctrlKey?r.isBodyKeyDown=!1:(t.repeat||r.isKeyDown||((i.target===document.body||e.config.isGlobalTrigger&&!Ri(i.target))&&e.checkCode(n,!0)&&(r.isBodyKeyDown=!0),document.addEventListener("keyup",e.onBodyKeyUp)),r.isBodyKeyDown&&e.handleKeyDown(i))}})),s(u(e),"onBodyKeyUp",(function(t){e.player&&(document.removeEventListener("keyup",e.onBodyKeyUp),e.handleKeyUp(t))})),s(u(e),"onKeydown",(function(t){if(e.player){var i=t||window.event,n=u(e)._keyState;if(!i.repeat){if(e.config.disable||e.config.disableRootTrigger||i.metaKey||i.altKey||i.ctrlKey)return;!i||37!==i.keyCode&&!e.checkCode(i.keyCode)||i.target!==e.player.root&&i.target!==e.player.video&&i.target!==e.player.controls.el||(n.isKeyDown=!0),e.player.root.addEventListener("keyup",e.onKeyup)}n.isKeyDown&&e.handleKeyDown(i)}})),s(u(e),"onKeyup",(function(t){e.player&&(e.player.root.removeEventListener("keyup",e.onKeyup),e.handleKeyUp(t))})),e}return r(o,[{key:"mergekeyCodeMap",value:function(){var e=this,t=this.config.keyCodeMap;t&&Object.keys(t).map((function(i){e.keyCodeMap[i]?["keyCode","action","disable","pressAction","disablePress","isBodyTarget"].map((function(n){t[i][n]&&(e.keyCodeMap[i][n]=t[i][n])})):e.keyCodeMap[i]=t[i]}))}},{key:"afterCreate",value:function(){this.config.disable=!this.playerConfig.keyShortcut;var e="function"==typeof this.config.seekStep?this.config.seekStep(this.player):this.config.seekStep;e&&"number"==typeof e&&(this.seekStep=e),this.keyCodeMap={space:{keyCode:32,action:"playPause",disable:!1,disablePress:!1,noBodyTarget:!1},up:{keyCode:38,action:"upVolume",disable:!1,disablePress:!1,noBodyTarget:!0},down:{keyCode:40,action:"downVolume",disable:!1,disablePress:!1,noBodyTarget:!0},left:{keyCode:37,action:"seekBack",disablePress:!1,disable:!1},right:{keyCode:39,action:"seek",pressAction:"changePlaybackRate",disablePress:!1,disable:!1},esc:{keyCode:27,action:"exitFullscreen",disablePress:!0,disable:!1}},this.mergekeyCodeMap(),this._keyState={isKeyDown:!1,isBodyKeyDown:!1,isPress:!1,tt:0,playbackRate:0},this.player.root.addEventListener("keydown",this.onKeydown),document.addEventListener("keydown",this.onBodyKeyDown)}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.config[i]=e[i]}))}},{key:"checkIsVisible",value:function(){if(!this.config.checkVisible)return!0;var e=this.player.root.getBoundingClientRect(),t=e.height,i=e.top,n=e.bottom,o=window.innerHeight;return!(i<0&&i<0-.9*t||n>0&&n-o>.9*t)}},{key:"checkCode",value:function(e,t){var i=this,n=!1;return Object.keys(this.keyCodeMap).map((function(o){i.keyCodeMap[o]&&e===i.keyCodeMap[o].keyCode&&!i.keyCodeMap[o].disable&&(n=!t||t&&!i.keyCodeMap[o].noBodyTarget)})),n}},{key:"downVolume",value:function(e){var t=this.player;if(!(t.volume<=0)){var i=parseFloat((t.volume-.1).toFixed(1)),n={volume:{from:t.volume,to:i}};this.emitUserAction(e,"change_volume",{props:n}),t.volume=i>=0?i:0}}},{key:"upVolume",value:function(e){var t=this.player;if(!(t.volume>=1)){var i=parseFloat((t.volume+.1).toFixed(1)),n={volume:{from:t.volume,to:i}};this.emitUserAction(e,"change_volume",{props:n}),t.volume=i<=1?i:1}}},{key:"seek",value:function(e){var t=this.player,i=t.currentTime,n=t.offsetCurrentTime,o=t.duration,r=t.offsetDuration,s=t.timeSegments,a=n>-1?n:i,l=r||o,c=e.repeat&&this.seekStep>=4?parseInt(this.seekStep/2,10):this.seekStep;a+c<=l?a+=c:a=l;var u=x.getCurrentTimeByOffset(a,s),h={currentTime:{from:i,to:u}};this.emitUserAction(e,"seek",{props:h}),this.player.currentTime=u}},{key:"seekBack",value:function(e){var t=this.player,i=t.currentTime,n=t.offsetCurrentTime,o=t.timeSegments,r=(n>-1?n:i)-(e.repeat?parseInt(this.seekStep/2,10):this.seekStep);r<0&&(r=0);var s={currentTime:{from:i,to:r=x.getCurrentTimeByOffset(r,o)}};this.emitUserAction(e,"seek",{props:s}),this.player.currentTime=r}},{key:"changePlaybackRate",value:function(e){var t=this._keyState,i=this.config,n=this.player;0===t.playbackRate&&(t.playbackRate=n.playbackRate,n.playbackRate=i.playbackRate)}},{key:"playPause",value:function(e){var t=this.player;t&&(this.emitUserAction(e,"switch_play_pause"),t.paused?t.play():t.pause())}},{key:"exitFullscreen",value:function(e){var t=this.player,i=t.fullscreen,n=t.cssfullscreen;i&&(this.emitUserAction("keyup","switch_fullscreen",{prop:"fullscreen",from:i,to:!i}),t.exitFullscreen()),n&&(this.emitUserAction("keyup","switch_css_fullscreen",{prop:"cssfullscreen",from:n,to:!n}),t.exitCssFullscreen())}},{key:"handleKeyDown",value:function(e){var t=this._keyState;if(e.repeat){t.isPress=!0;var i=Date.now();if(i-t.tt<200)return;t.tt=i}this.handleKeyCode(e.keyCode,e,t.isPress)}},{key:"handleKeyUp",value:function(e){var t=this._keyState;t.playbackRate>0&&(this.player.playbackRate=t.playbackRate,t.playbackRate=0),t.isKeyDown=!1,t.isPress=!1,t.tt=0}},{key:"handleKeyCode",value:function(e,i,n){for(var o,r=Object.keys(this.keyCodeMap),s=0;s<r.length;s++){var a=this.keyCodeMap[r[s]],l=a.action,c=a.keyCode,u=a.disable,h=a.pressAction,d=a.disablePress;if(c===e){if(!(u||n&&d)){var f=n&&h||l;"function"==typeof f?l(i,this.player,n):"string"==typeof f&&"function"==typeof this[f]&&this[f](i,this.player,n),this.emit(Te,t({key:r[s],target:i.target,isPress:n},this.keyCodeMap[r[s]]))}(o=i).preventDefault(),o.returnValue=!1,i.stopPropagation();break}}}},{key:"destroy",value:function(){this.player.root.removeEventListener("keydown",this.onKeydown),document.removeEventListener("keydown",this.onBodyKeyDown),this.player.root.removeEventListener("keyup",this.onKeyup),document.removeEventListener("keyup",this.onBodyKeyUp)}},{key:"disable",value:function(){this.config.disable=!0}},{key:"enable",value:function(){this.config.disable=!1}}],[{key:"pluginName",get:function(){return"keyboard"}},{key:"defaultConfig",get:function(){return{seekStep:10,checkVisible:!1,disableBodyTrigger:!1,disableRootTrigger:!1,isGlobalTrigger:!0,keyCodeMap:{},disable:!1,playbackRate:2,isIgnoreUserActive:!0}}}]),o}($e);function ji(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="-5 -5 110 110">\n  <path d="M100,50A50,50,0,1,1,50,0" stroke-width="5" stroke="#ddd" stroke-dasharray="236" fill="none"></path>\n</svg>\n',"image/svg+xml").firstChild}var Ni=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"registerIcons",value:function(){return{loadingIcon:ji}}},{key:"afterCreate",value:function(){this.appendChild("xg-loading-inner",this.icons.loadingIcon)}},{key:"render",value:function(){return'\n    <xg-loading class="xgplayer-loading">\n      <xg-loading-inner></xg-loading-inner>\n    </xg-loading>'}}],[{key:"pluginName",get:function(){return"loading"}},{key:"defaultConfig",get:function(){return{position:rt.ROOT}}}]),i}(ct),Fi=[{tag:"xg-cache",className:"xgplayer-progress-cache",styleKey:"cachedColor"},{tag:"xg-played",className:"xgplayer-progress-played",styleKey:"playedColor"}],Hi=function(){function e(t){n(this,e),this.fragments=t.fragments||[],0===this.fragments.length&&this.fragments.push({percent:1}),this._callBack=t.actionCallback,this.fragConfig={fragFocusClass:t.fragFocusClass||"inner-focus-point",fragAutoFocus:!!t.fragAutoFocus,fragClass:t.fragClass||""},this.style=t.style||{playedColor:"",cachedColor:"",progressColor:""},this.duration=0,this.cachedIndex=0,this.playedIndex=0,this.focusIndex=-1}return r(e,[{key:"updateDuration",value:function(e){var t=this;this.duration=e;var i=0,n=this.fragments;this.fragments=n.map((function(e){return e.start=parseInt(i,10),e.end=parseInt(i+e.percent*t.duration,10),e.duration=parseInt(e.percent*t.duration,10),i+=e.percent*t.duration,e}))}},{key:"updateProgress",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"played",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{newIndex:0,curIndex:0,millisecond:0},i=this.progressList,n=this.fragments;if(!(i.length<1)){var o=t.newIndex,r=t.curIndex,s=t.millisecond;o!==r&&i.map((function(t,i){i<o?t[e].style.width="100%":i>o&&(t[e].style.width=0)}));var a=n[o],l=0===s?0:(s-a.start)/a.duration;i[o][e].style.width=l<0?0:"".concat(100*l,"%")}}},{key:"updateFocus",value:function(e){if(this.fragConfig.fragAutoFocus&&!(this.fragments.length<2))if(e){var t=this.findIndex(1e3*e.currentTime,this.focusIndex);if(t>=0&&t!==this.focusIndex){this.focusIndex>-1&&this.unHightLight(this.focusIndex),this.setHightLight(t);var i={index:t,preIndex:this.focusIndex,fragment:this.fragments[this.focusIndex]};this.focusIndex=t,this._callBack&&this._callBack(i)}}else if(this.focusIndex>-1){this.unHightLight(this.focusIndex);var n={index:-1,preIndex:this.focusIndex,fragment:null};this._callBack&&this._callBack(n),this.focusIndex=-1}}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{cached:0,played:0},t=arguments.length>1?arguments[1]:void 0;if(!this.duration||parseInt(1e3*t,10)!==this.duration){if(!t&&0!==t)return;this.updateDuration(parseInt(1e3*t,10))}var i=this.playedIndex,n=this.cachedIndex;if("Undefined"!==x.typeOf(e.played)){var o=this.findIndex(1e3*e.played,i);if(o<0)return;this.updateProgress("played",{newIndex:o,curIndex:i,millisecond:parseInt(1e3*e.played,10)}),this.playedIndex=o}if("Undefined"!==x.typeOf(e.cached)){var r=this.findIndex(1e3*e.cached,n);if(r<0)return;this.updateProgress("cached",{newIndex:r,curIndex:n,millisecond:parseInt(1e3*e.cached,10)}),this.cachedIndex=r}}},{key:"findIndex",value:function(e,t){var i=this.fragments;if(!i||0===i.length)return-1;if(1===i.length)return 0;if(t>-1&&t<i.length&&e>i[t].start&&e<i[t].end)return t;if(e>i[i.length-1].start)return i.length-1;for(var n=0;n<i.length;n++)if(e>i[n].start&&e<=i[n].end){t=n;break}return t}},{key:"findHightLight",value:function(){for(var e=this.root.children,t=0;t<e.length;t++)if(x.hasClass(e[t],this.fragConfig.fragFocusClass))return{dom:e[t],pos:e[t].getBoundingClientRect()}}},{key:"findFragment",value:function(e){var t=this.root.children;return e<0||e>=t.length?null:{dom:t[e],pos:t[e].getBoundingClientRect()}}},{key:"unHightLight",value:function(){for(var e=this.root.children,t=0;t<e.length;t++)x.removeClass(e[t],this.fragConfig.fragFocusClass)}},{key:"setHightLight",value:function(e){var t=this.root.children;if(e<t.length)return x.addClass(t[e],this.fragConfig.fragFocusClass),{dom:t[e],pos:t[e].getBoundingClientRect()}}},{key:"destroy",value:function(){this.progressList=null,this.fragments=null,this.root.innerHTML=""}},{key:"reset",value:function(e){var t=this;if(Object.keys(this.fragConfig).forEach((function(i){void 0!==e[i]&&(t.fragConfig[i]=e[i])})),e.fragments){if(this.fragments=0===e.fragments.length?[{percent:1}]:e.fragments,this.updateDuration(this.duration),this.playedIndex=0,this.cachedIndex=0,this.root)for(var i=this.root.children;i.length>0;)this.root.removeChild(i[0]);this.render()}}},{key:"render",value:function(){var e=this,t=this.style.progressColor;if(this.root||(this.root=x.createDom("xg-inners","",{},"progress-list")),this.fragments){var i=this.fragConfig,n=i.fragClass,o=i.fragFocusClass;this.progressList=this.fragments.map((function(i){var r=x.createDom("xg-inner","",{style:t?"background:".concat(t,"; flex: ").concat(i.percent):"flex: ".concat(i.percent)},"".concat(i.isFocus?o:""," xgplayer-progress-inner ").concat(n));return e.root.appendChild(r),Fi.forEach((function(t){r.appendChild(x.createDom(t.tag,"",{style:t.styleKey?"background: ".concat(e.style[t.styleKey],"; width:0;"):"width:0;"},t.className))})),{cached:r.children[0],played:r.children[1]}}))}return this.root}}]),e}(),Bi={POINT:"inner-focus-point",HIGHLIGHT:"inner-focus-highlight"},Ui=function(e){a(l,e);var o=d(l);function l(e){var t;return n(this,l),s(u(t=o.call(this,e)),"onMoveOnly",(function(e,i){var n=u(t),o=n.pos,r=n.config,s=n.player,a=i;if(e){x.event(e);var l=x.getEventPos(e,s.zoom),c=90===s.rotateDeg?l.clientY:l.clientX;if(o.moving&&Math.abs(o.x-c)<r.miniMoveStep)return;o.moving=!0,o.x=c,a=t.computeTime(e,c)}t.triggerCallbacks("dragmove",a,e),t._updateInnerFocus(a)})),s(u(t),"onBodyClick",(function(e){t.pos.isLocked&&(t.pos.isLocked=!1,e.preventDefault(),e.stopPropagation())})),s(u(t),"_mouseDownHandler",(function(e,i){t._state.time=i.currentTime,t.updateWidth(i.currentTime,i.seekTime,i.percent,0),t._updateInnerFocus(i)})),s(u(t),"_mouseUpHandler",(function(e,i){u(t).pos.moving&&t.updateWidth(i.currentTime,i.seekTime,i.percent,2)})),s(u(t),"_mouseMoveHandler",(function(e,i){var n=u(t),o=n._state,r=n.pos,s=n.config,a=n.player;o.time<i.currentTime?i.forward=!0:i.forward=!1,o.time=i.currentTime,r.isDown&&!r.moving&&(r.moving=!0,s.isPauseMoving&&a.pause(),t.triggerCallbacks("dragstart",i,e),t.emitUserAction("drag","dragstart",i)),t.updateWidth(i.currentTime,i.seekTime,i.percent,1),t.triggerCallbacks("dragmove",i,e),t._updateInnerFocus(i)})),s(u(t),"onMouseDown",(function(e){var i=u(t),n=i._state,o=i.player,r=i.pos,s=i.config,a=i.playerConfig,l=x.getEventPos(e,o.zoom),c=90===o.rotateDeg?l.clientY:l.clientX;if(!(o.isMini||s.closeMoveSeek||!a.allowSeekAfterEnded&&o.ended)){if(o.duration||o.isPlaying){e.stopPropagation(),t.focus(),x.checkIsFunction(a.disableSwipeHandler)&&a.disableSwipeHandler(),x.checkIsFunction(s.onMoveStart)&&s.onMoveStart(),x.event(e),r.x=c,r.isDown=!0,r.moving=!1,n.prePlayTime=o.currentTime,o.focus({autoHide:!1}),t.isProgressMoving=!0,x.addClass(t.progressBtn,"active");var h=t.computeTime(e,c);return h.prePlayTime=n.prePlayTime,t._mouseDownHandlerHook(e,h),"touchstart"===e.type?(t.root.addEventListener("touchmove",t.onMouseMove),t.root.addEventListener("touchend",t.onMouseUp)):(t.unbind("mousemove",t.onMoveOnly),document.addEventListener("mousemove",t.onMouseMove,!1),document.addEventListener("mouseup",t.onMouseUp,!1)),!0}o.play()}})),s(u(t),"onMouseUp",(function(e){var i=u(t),n=i.player,o=i.config,r=i.pos,s=i.playerConfig,a=i._state;e.stopPropagation(),e.preventDefault(),x.checkIsFunction(s.enableSwipeHandler)&&s.enableSwipeHandler(),x.checkIsFunction(o.onMoveEnd)&&o.onMoveEnd(),x.event(e),x.removeClass(t.progressBtn,"active");var l=t.computeTime(e,r.x);l.prePlayTime=a.prePlayTime,r.moving?(t.triggerCallbacks("dragend",l,e),t.emitUserAction("drag","dragend",l)):(t.triggerCallbacks("click",l,e),t.emitUserAction("click","click",l)),t._mouseUpHandlerHook(e,l),r.moving=!1,r.isDown=!1,r.x=0,r.y=0,r.isLocked=!0,a.prePlayTime=0,a.time=0,"touchend"===e.type?(t.root.removeEventListener("touchmove",t.onMouseMove),t.root.removeEventListener("touchend",t.onMouseUp),t.blur()):(document.removeEventListener("mousemove",t.onMouseMove,!1),document.removeEventListener("mouseup",t.onMouseUp,!1),r.isEnter?"mobile"!==s.isMobileSimulateMode&&t.bind("mousemove",t.onMoveOnly):t.onMouseLeave(e)),x.setTimeout(u(t),(function(){t.resetSeekState()}),10),n.focus()})),s(u(t),"onMouseMove",(function(e){var i=u(t),n=i._state,o=i.pos,r=i.player,s=i.config;x.checkTouchSupport()&&e.preventDefault(),x.event(e);var a=x.getEventPos(e,r.zoom),l=90===r.rotateDeg?a.clientY:a.clientX,c=Math.abs(o.x-l);if(!(o.moving&&c<s.miniMoveStep||!o.moving&&c<s.miniStartStep)){o.x=l;var h=t.computeTime(e,l);h.prePlayTime=n.prePlayTime,t._mouseMoveHandlerHook(e,h)}})),s(u(t),"onMouseOut",(function(e){t.triggerCallbacks("mouseout",null,e)})),s(u(t),"onMouseOver",(function(e){t.triggerCallbacks("mouseover",null,e)})),s(u(t),"onMouseEnter",(function(e){var i=u(t),n=i.player,o=i.pos;if(!(o.isDown||o.isEnter||n.isMini||!n.config.allowSeekAfterEnded&&n.ended)){o.isEnter=!0,t.bind("mousemove",t.onMoveOnly),t.bind("mouseleave",t.onMouseLeave),x.event(e);var r=x.getEventPos(e,n.zoom),s=90===n.rotateDeg?r.clientY:r.clientX,a=t.computeTime(e,s);t.triggerCallbacks("mouseenter",a,e),t.focus()}})),s(u(t),"onMouseLeave",(function(e){t.triggerCallbacks("mouseleave",null,e),t.unlock(),t._updateInnerFocus(null)})),s(u(t),"onVideoResize",(function(){var e=t.pos,i=e.x,n=e.isDown;if(e.isEnter&&!n){var o=t.computeTime(null,i);t.onMoveOnly(null,o)}})),t.useable=!1,t.isProgressMoving=!1,t.__dragCallBacks=[],t._state={now:-1,direc:0,time:0,prePlayTime:-1},t._disableBlur=!1,t}return r(l,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"duration",get:function(){return this.playerConfig.customDuration||this.player.duration}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i+this.timeOffset}},{key:"changeState",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.useable=e}},{key:"show",value:function(e){this.root&&(this.root.style.display="flex")}},{key:"_initInner",value:function(){var e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};i&&0!==i.length||(i=[{percent:1}]);var o=t(t({fragments:i},n),{},{actionCallback:function(t){e.emitUserAction("fragment_focus","fragment_focus",t)}});this.innerList?this.innerList.reset(o):(this.innerList=new Hi(o),this.outer.insertBefore(this.innerList.render(),this.outer.children[0]),["findHightLight","unHightLight","setHightLight","findFragment"].map((function(t){e[t]=e.innerList[t].bind(e.innerList)})))}},{key:"_updateInnerFocus",value:function(e){this.innerList&&this.innerList.updateFocus(e)}},{key:"afterCreate",value:function(){var e=this;if(!this.config.disable&&!this.playerConfig.isLive){this.pos={x:0,y:0,moving:!1,isDown:!1,isEnter:!1,isLocked:!1},this.outer=this.find("xg-outer");var t=this.config,i=t.fragFocusClass,n=t.fragAutoFocus,o=t.fragClass;this._initInner(this.config.fragments,{fragFocusClass:i,fragAutoFocus:n,fragClass:o,style:this.playerConfig.commonStyle||{}}),"mobile"===O.device&&(this.config.isDragingSeek=!1,this.isMobile=!0),this.progressBtn=this.find(".xgplayer-progress-btn"),this.on(q,(function(){e.onMouseLeave()})),this.on(G,(function(){e.onTimeupdate()})),this.on(W,(function(){e.onTimeupdate(),e.onCacheUpdate()})),this.on(J,(function(){e.onCacheUpdate()})),this.on(H,(function(){e.onCacheUpdate(!0),e.onTimeupdate(!0),e._state.now=0})),this.on(Q,(function(){e.onReset()})),this.on(ke,(function(){e.onVideoResize()})),this.bindDomEvents(),this.initCustomStyle()}}},{key:"setConfig",value:function(e){var t=this,i=null;Object.keys(e).forEach((function(n){t.config[n]=e[n],"fragments"===n&&(i=e[n])})),i&&this._initInner(i,e)}},{key:"initCustomStyle",value:function(){var e=(this.playerConfig||{}).commonStyle.sliderBtnStyle,t=this.progressBtn;e&&("string"==typeof e?t.style.boxShadow=e:"object"===i(e)&&Object.keys(e).map((function(i){t.style[i]=e[i]})))}},{key:"triggerCallbacks",value:function(e,t,i){this.__dragCallBacks.length>0&&this.__dragCallBacks.map((function(n){if(n&&n.handler&&n.type===e)try{n.handler(t,i)}catch(o){console.error("[XGPLAYER][triggerCallbacks] ".concat(n," error"),o)}}))}},{key:"addCallBack",value:function(e,t){t&&"function"==typeof t&&this.__dragCallBacks.push({type:e,handler:t})}},{key:"removeCallBack",value:function(e,t){var i=this.__dragCallBacks,n=-1;i.map((function(i,o){i&&i.type===e&&i.handler===t&&(n=o)})),n>-1&&i.splice(n,1)}},{key:"unlock",value:function(){var e=this.player,t=this.pos;t.isEnter=!1,t.isLocked=!1,e.isMini||(this.unbind("mousemove",this.onMoveOnly),t.isDown?this.unbind("mouseleave",this.onMouseLeave):this.blur())}},{key:"bindDomEvents",value:function(){var e=this.player,t=e.controls,i=e.config;this._mouseDownHandlerHook=this.hook("dragstart",this._mouseDownHandler),this._mouseUpHandlerHook=this.hook("dragend",this._mouseUpHandler),this._mouseMoveHandlerHook=this.hook("drag",this._mouseMoveHandler),"touch"!==this.domEventType&&"compatible"!==this.domEventType||(this.root.addEventListener("touchstart",this.onMouseDown),t&&(t.root&&t.root.addEventListener("touchmove",x.stopPropagation),t.center&&t.center.addEventListener("touchend",x.stopPropagation))),"mouse"!==this.domEventType&&"compatible"!==this.domEventType||(this.bind("mousedown",this.onMouseDown),"mobile"!==i.isMobileSimulateMode&&this.bind("mouseenter",this.onMouseEnter),this.bind("mouseover",this.onMouseOver),this.bind("mouseout",this.onMouseOut),this.player.root.addEventListener("click",this.onBodyClick,!0))}},{key:"focus",value:function(){this.player.controls.pauseAutoHide(),x.addClass(this.root,"active")}},{key:"blur",value:function(){this._disableBlur||(this.player.controls.recoverAutoHide(),x.removeClass(this.root,"active"))}},{key:"disableBlur",value:function(){this._disableBlur=!0}},{key:"enableBlur",value:function(){this._disableBlur=!1}},{key:"updateWidth",value:function(e,t,i,n){var o=this.config,r=this.player;if(!o.isCloseClickSeek||0!==n){var s=t=t>=r.duration?r.duration-o.endedDiff:Number(t).toFixed(1);this.updatePercent(i),this.updateTime(e),(1!==n||o.isDragingSeek&&"audio"!==r.config.mediaType)&&(this._state.now=s,this._state.direc=s>r.currentTime?0:1,r.seek(s))}}},{key:"computeTime",value:function(e,t){var i,n,o=this.player,r=this.root.getBoundingClientRect(),s=r.width,a=r.height,l=r.top,c=r.left,u=t;90===o.rotateDeg?(i=a,n=l):(i=s,n=c);var h=u-n,d=(h=h>i?i:h<0?0:h)/i;d=d<0?0:d>1?1:d;var f=parseInt(d*this.offsetDuration*1e3,10)/1e3;return{percent:d,currentTime:f,seekTime:x.getCurrentTimeByOffset(f,o.timeSegments),offset:h,width:i,left:n,e:e}}},{key:"updateTime",value:function(e){var t=this.player,i=this.duration;e>i?e=i:e<0&&(e=0);var n=t.plugins.time;n&&n.updateTime(e)}},{key:"resetSeekState",value:function(){this.isProgressMoving=!1;var e=this.player.plugins.time;e&&e.resetActive()}},{key:"updatePercent",value:function(e,t){if(this.isProgressMoving=!0,!this.config.disable){e=e>1?1:e<0?0:e,this.progressBtn.style.left="".concat(100*e,"%"),this.innerList.update({played:e*this.offsetDuration},this.offsetDuration);var i=this.player.plugins.miniprogress;i&&i.update({played:e*this.offsetDuration},this.offsetDuration)}}},{key:"onTimeupdate",value:function(e){var t=this.player,i=this._state,n=this.offsetDuration;if(!(t.isSeeking&&t.media.seeking||this.isProgressMoving)&&t.hasStart){if(i.now>-1){var o=parseInt(1e3*i.now,10)-parseInt(1e3*t.currentTime,10);if(0===i.direc&&o>300||1===i.direc&&o>-300)return void(i.now=-1);i.now=-1}var r=this.currentTime;r=x.adjustTimeByDuration(r,n,e),this.innerList.update({played:r},n),this.progressBtn.style.left="".concat(r/n*100,"%")}}},{key:"onCacheUpdate",value:function(e){var t=this.player,i=this.duration;if(t){var n=t.bufferedPoint.end;n=x.adjustTimeByDuration(n,i,e),this.innerList.update({cached:n},i)}}},{key:"onReset",value:function(){this.innerList.update({played:0,cached:0},0),this.progressBtn.style.left="0%"}},{key:"destroy",value:function(){var e=this.player,t=e.controls;this.thumbnailPlugin=null,this.innerList.destroy(),this.innerList=null;var i=this.domEventType;"touch"!==i&&"compatible"!==i||(this.root.removeEventListener("touchstart",this.onMouseDown),this.root.removeEventListener("touchmove",this.onMouseMove),this.root.removeEventListener("touchend",this.onMouseUp),t&&(t.root&&t.root.removeEventListener("touchmove",x.stopPropagation),t.center&&t.center.removeEventListener("touchend",x.stopPropagation))),"mouse"!==i&&"compatible"!==i||(this.unbind("mousedown",this.onMouseDown),this.unbind("mouseenter",this.onMouseEnter),this.unbind("mousemove",this.onMoveOnly),this.unbind("mouseleave",this.onMouseLeave),document.removeEventListener("mousemove",this.onMouseMove,!1),document.removeEventListener("mouseup",this.onMouseUp,!1),e.root.removeEventListener("click",this.onBodyClick,!0))}},{key:"render",value:function(){if(!this.config.disable&&!this.playerConfig.isLive){var e=this.player.controls?this.player.controls.config.mode:"";return'\n    <xg-progress class="xgplayer-progress '.concat("bottom"===e?"xgplayer-progress-bottom":"",'">\n      <xg-outer class="xgplayer-progress-outer">\n        <xg-progress-btn class="xgplayer-progress-btn"></xg-progress-btn>\n      </xg-outer>\n    </xg-progress>\n    ')}}}],[{key:"pluginName",get:function(){return"progress"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_CENTER,index:0,disable:!1,isDragingSeek:!0,closeMoveSeek:!1,isPauseMoving:!1,isCloseClickSeek:!1,fragments:[{percent:1}],fragFocusClass:Bi.POINT,fragClass:"",fragAutoFocus:!1,miniMoveStep:5,miniStartStep:2,onMoveStart:function(){},onMoveEnd:function(){},endedDiff:.2}}},{key:"FRAGMENT_FOCUS_CLASS",get:function(){return Bi}}]),l}(ct),Vi=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"_onMouseenter",(function(t){e.emit("icon_mouseenter",{pluginName:e.pluginName})})),s(u(e),"_onMouseLeave",(function(t){e.emit("icon_mouseleave",{pluginName:e.pluginName})})),e}return r(i,[{key:"afterCreate",value:function(){this.bind("mouseenter",this._onMouseenter),this.bind("mouseleave",this._onMouseLeave)}},{key:"destroy",value:function(){this.unbind("mouseenter",this._onMouseenter),this.unbind("mouseleave",this._onMouseLeave)}}]),i}(ct),Wi=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"afterCreate",value:function(){var e=this;p(l(i.prototype),"afterCreate",this).call(this);var t=this.player;this.config.disable||(this.initIcons(),this.btnClick=this.btnClick.bind(this),this.bind(["touchend","click"],this.btnClick),this.on([B,U,Q],(function(){e.animate(t.paused)})),this.on(N,(function(){e.animate(t.paused)})),this.animate(!0))}},{key:"registerIcons",value:function(){return{play:{icon:Xt,class:"xg-icon-play"},pause:{icon:Zt,class:"xg-icon-pause"}}}},{key:"btnClick",value:function(e){e.preventDefault(),e.stopPropagation();var t=this.player;return this.emitUserAction(e,"switch_play_pause",{prop:"paused",from:t.paused,to:!t.paused}),t.ended?t.replay():t.paused?(t.play(),this.animate(!1)):(t.pause(),this.animate(!0)),!1}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.play),this.appendChild(".xgplayer-icon",e.pause)}},{key:"animate",value:function(e){if(this.player){var t=this.i18nKeys,i=this.find(".xg-tips");e?(this.setAttr("data-state","pause"),i&&this.changeLangTextKey(i,t.PLAY_TIPS)):(this.setAttr("data-state","play"),i&&this.changeLangTextKey(i,t.PAUSE_TIPS))}}},{key:"destroy",value:function(){p(l(i.prototype),"destroy",this).call(this),this.unbind(["touchend","click"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-play">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(ti(this,"PLAY_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"play"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_LEFT,index:0,disable:!1}}}]),i}(Vi);function Gi(){return(new DOMParser).parseFromString('<svg width="32px" height="40px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n        <path d="M11.2374369,14 L17.6187184,7.61871843 C17.9604272,7.27700968 17.9604272,6.72299032 17.6187184,6.38128157 C17.2770097,6.03957281 16.7229903,6.03957281 16.3812816,6.38128157 L9.38128157,13.3812816 C9.03957281,13.7229903 9.03957281,14.2770097 9.38128157,14.6187184 L16.3812816,21.6187184 C16.7229903,21.9604272 17.2770097,21.9604272 17.6187184,21.6187184 C17.9604272,21.2770097 17.9604272,20.7229903 17.6187184,20.3812816 L11.2374369,14 L11.2374369,14 Z" fill="#FFFFFF"></path>\n    </g>\n</svg>',"image/svg+xml").firstChild}var zi=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"afterCreate",value:function(){var e=this;this.initIcons(),this.onClick=function(t){t.preventDefault(),t.stopPropagation(),e.config.onClick(t)},this.bind(["click","touchend"],this.onClick)}},{key:"registerIcons",value:function(){return{screenBack:{icon:Gi,class:"xg-fullscreen-back"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(this.root,e.screenBack)}},{key:"show",value:function(){x.addClass(this.root,"show")}},{key:"hide",value:function(){x.removeClass(this.root,"show")}},{key:"render",value:function(){return'<xg-icon class="xgplayer-back">\n    </xg-icon>'}}],[{key:"pluginName",get:function(){return"topbackicon"}},{key:"defaultConfig",get:function(){return{position:rt.ROOT_TOP,index:0}}}]),i}(ct);function Ki(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M598 214h212v212h-84v-128h-128v-84zM726 726v-128h84v212h-212v-84h128zM214 426v-212h212v84h-128v128h-84zM298 598v128h128v84h-212v-212h84z"></path>\n</svg>\n',"image/svg+xml").firstChild}function qi(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="2 -4 28 40">\n  <path fill="#fff" transform="scale(0.0320625 0.0320625)" d="M682 342h128v84h-212v-212h84v128zM598 810v-212h212v84h-128v128h-84zM342 342v-128h84v212h-212v-84h128zM214 682v-84h212v212h-84v-128h-128z"></path>\n</svg>\n',"image/svg+xml").firstChild}var Yi=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"_onOrientationChange",(function(t){e.player.fullscreen&&e.config.rotateFullscreen&&(90===window.orientation||-90===window.orientation?e.player.setRotateDeg(0):e.player.setRotateDeg(90))})),e}return r(i,[{key:"afterCreate",value:function(){var e=this;p(l(i.prototype),"afterCreate",this).call(this);var t=this.config,n=this.playerConfig;if(!t.disable){t.target&&(this.playerConfig.fullscreenTarget=this.config.target);var o=x.getFullScreenEl();n.fullscreenTarget===o&&this.player.getFullscreen().catch((function(e){})),this.initIcons(),this.handleFullscreen=this.hook("fullscreenChange",this.toggleFullScreen,{pre:function(t){var i=e.player.fullscreen;e.emitUserAction(t,"switch_fullscreen",{prop:"fullscreen",from:i,to:!i})}}),this.bind(".xgplayer-fullscreen",["touchend","click"],this.handleFullscreen),this.on(pe,(function(t){var i=e.find(".xg-tips");i&&e.changeLangTextKey(i,t?e.i18nKeys.EXITFULLSCREEN_TIPS:e.i18nKeys.FULLSCREEN_TIPS),e.animate(t)})),this.config.needBackIcon&&(this.topBackIcon=this.player.registerPlugin({plugin:zi,options:{config:{onClick:function(t){e.handleFullscreen(t)}}}})),"mobile"===O.device&&window.addEventListener("orientationchange",this._onOrientationChange)}}},{key:"registerIcons",value:function(){return{fullscreen:{icon:Ki,class:"xg-get-fullscreen"},exitFullscreen:{icon:qi,class:"xg-exit-fullscreen"}}}},{key:"destroy",value:function(){p(l(i.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon","mobile"===O.device?"touchend":"click",this.handleFullscreen),"mobile"===O.device&&window.removeEventListener("orientationchange",this._onOrientationChange)}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.fullscreen),this.appendChild(".xgplayer-icon",e.exitFullscreen)}},{key:"toggleFullScreen",value:function(e){e&&(e.preventDefault(),e.stopPropagation());var t=this.player,i=this.config;!0===i.useCssFullscreen||"function"==typeof i.useCssFullscreen&&i.useCssFullscreen()?(t.fullscreen?t.exitCssFullscreen():t.getCssFullscreen(),this.animate(t.fullscreen)):i.rotateFullscreen?(t.fullscreen?t.exitRotateFullscreen():t.getRotateFullscreen(),this.animate(t.fullscreen)):i.switchCallback&&"function"==typeof i.switchCallback?i.switchCallback(t.fullscreen):t.fullscreen?(t.exitFullscreen(),i.useScreenOrientation&&this.unlockScreen()):(t.getFullscreen().catch((function(e){})),i.useScreenOrientation&&t.aspectRatio>1&&this.lockScreen(i.lockOrientationType))}},{key:"animate",value:function(e){e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.topBackIcon&&(e?(this.topBackIcon.show(),this.hide()):(this.topBackIcon.hide(),this.show()))}},{key:"render",value:function(){if(!this.config.disable){return'<xg-icon class="xgplayer-fullscreen">\n    <div class="xgplayer-icon">\n    </div>\n    '.concat(ti(this,"FULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}},{key:"lockScreen",value:function(e){try{screen.orientation.lock(e).catch((function(e){}))}catch(t){}}},{key:"unlockScreen",value:function(){try{screen.orientation.unlock().catch((function(e){}))}catch(e){}}}],[{key:"pluginName",get:function(){return"fullscreen"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:0,useCssFullscreen:!1,rotateFullscreen:!1,useScreenOrientation:!1,lockOrientationType:"landscape",switchCallback:null,target:null,disable:!1,needBackIcon:!1}}}]),i}(Vi),Xi=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),(o=t.call(this,e)).isActiving=!1,o}return r(i,[{key:"duration",get:function(){var e=this.player,t=e.offsetDuration,i=e.duration;return this.playerConfig.customDuration||t||i}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i}},{key:"timeOffset",get:function(){return this.playerConfig.timeOffset||0}},{key:"afterCreate",value:function(){var e=this,t=this.player.controls.config.mode;this.mode="flex"===t?"flex":"normal",this.config.disable||("flex"===this.mode&&(this.createCenterTime(),this.root.style.display="none"),this.durationDom=this.find(".time-duration"),this.timeDom=this.find(".time-current"),this.on([q,W,G],(function(t){"durationchange"===t.eventName&&(e.isActiving=!1),e.onTimeUpdate()})),this.on(H,(function(){e.onTimeUpdate(!0)})),this.on(Q,(function(){e.onReset()})))}},{key:"show",value:function(e){if("flex"===this.mode)return this.centerCurDom&&(this.centerCurDom.style.display="block"),void(this.centerDurDom&&(this.centerDurDom.style.display="block"));this.root.style.display="block"}},{key:"hide",value:function(){if("flex"===this.mode)return this.centerCurDom&&(this.centerCurDom.style.display="none"),void(this.centerDurDom&&(this.centerDurDom.style.display="none"));this.root.style.display="none"}},{key:"onTimeUpdate",value:function(e){var t=this.player,i=this.config,n=this.duration;if(!i.disable&&!this.isActiving&&t.hasStart){var o=this.currentTime+this.timeOffset;o=x.adjustTimeByDuration(o,n,e),"flex"===this.mode?(this.centerCurDom.innerHTML=this.minWidthTime(x.format(o)),n!==1/0&&n>0&&(this.centerDurDom.innerHTML=x.format(n))):(this.timeDom.innerHTML=this.minWidthTime(x.format(o)),n!==1/0&&n>0&&(this.durationDom.innerHTML=x.format(n)))}}},{key:"onReset",value:function(){"flex"===this.mode?(this.centerCurDom.innerHTML=this.minWidthTime(x.format(0)),this.centerDurDom.innerHTML=x.format(0)):(this.timeDom.innerHTML=this.minWidthTime(x.format(0)),this.durationDom.innerHTML=x.format(0))}},{key:"createCenterTime",value:function(){var e=this.player;if(e.controls&&e.controls.center){var t=e.controls.center;this.centerCurDom=x.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-left"),this.centerDurDom=x.createDom("xg-icon","00:00",{},"xgplayer-time xg-time-right"),t.children.length>0?t.insertBefore(this.centerCurDom,t.children[0]):t.appendChild(this.centerCurDom),t.appendChild(this.centerDurDom)}}},{key:"afterPlayerInit",value:function(){var e=this.config;this.duration===1/0||this.playerConfig.isLive?(x.hide(this.durationDom),x.hide(this.timeDom),x.hide(this.find(".time-separator")),x.show(this.find(".time-live-tag"))):x.hide(this.find(".time-live-tag")),e.hide?this.hide():this.show()}},{key:"changeLiveState",value:function(e){e?(x.hide(this.durationDom),x.hide(this.timeDom),x.hide(this.find(".time-separator")),x.show(this.find(".time-live-tag"))):(x.hide(this.find(".time-live-tag")),x.show(this.find(".time-separator")),x.show(this.durationDom),x.show(this.timeDom))}},{key:"updateTime",value:function(e){this.isActiving=!0,!e&&0!==e||e>this.duration||("flex"!==this.mode?this.timeDom.innerHTML=this.minWidthTime(x.format(e)):this.centerCurDom.innerHTML=this.minWidthTime(x.format(e)))}},{key:"minWidthTime",value:function(e){return e.split(":").map((function(e){return'<span class="time-min-width">'.concat(e,"</span>")})).join(":")}},{key:"resetActive",value:function(){var e=this,t=this.player,i=function(){e.isActiving=!1};this.off(W,i),t.isSeeking&&t.media.seeking?this.once(W,i):this.isActiving=!1}},{key:"destroy",value:function(){var e=this.player.controls.center;this.centerCurDom&&e.removeChild(this.centerCurDom),this.centerCurDom=null,this.centerDurDom&&e.removeChild(this.centerDurDom),this.centerDurDom=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-time">\n    <span class="time-current">00:00</span>\n    <span class="time-separator">/</span>\n    <span class="time-duration">00:00</span>\n    <span class="time-live-tag">'.concat(this.i18n.LIVE_TIP,"</span>\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"time"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_LEFT,index:2,disable:!1}}}]),i}(ct),Zi=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"_onDurationChange",(function(){e.updateSegments();var t=e.player,i=t.currentTime,n=t.timeSegments;if(e._checkIfEnabled(n)){var o=x.getIndexByTime(i,n),r=x.getOffsetCurrentTime(i,n,o);e.player.offsetCurrentTime=r,e.changeIndex(o,n)}})),s(u(e),"_onLoadedData",(function(){var t=e.player.timeSegments;if(e._checkIfEnabled(t)){var i=x.getOffsetCurrentTime(0,t);e.player.offsetCurrentTime=i,e.changeIndex(0,t),e.curPos.start>0&&(e.player.currentTime=e.curPos.start)}})),s(u(e),"_onTimeupdate",(function(){var t=e.player,i=t.currentTime,n=t.timeSegments;if(e._checkIfEnabled(n)){var o=n.length;e.lastCurrentTime=i;var r=x.getIndexByTime(i,n);r!==e.curIndex&&e.changeIndex(r,n);var s=x.getOffsetCurrentTime(i,n,r);if(e.player.offsetCurrentTime=s,e.curPos){var a=e.curPos,l=a.start,c=a.end;i<l?e.player.currentTime=l:i>c&&r>=o-1&&e.player.pause()}}})),s(u(e),"_onSeeking",(function(){var t=e.player,i=t.currentTime,n=t.timeSegments;if(e._checkIfEnabled(n))if(i<n[0].start)e.player.currentTime=n[0].start;else if(i>n[n.length-1].end)e.player.currentTime=n[n.length-1].end;else{var o=x.getIndexByTime(i,n);if(o>=0){var r=e.getSeekTime(i,e.lastCurrentTime,o,n);r>=0&&(e.player.currentTime=r)}}})),s(u(e),"_onPlay",(function(){var t=e.player,i=t.currentTime,n=t.timeSegments;e._checkIfEnabled(n)&&i>=n[n.length-1].end&&(e.player.currentTime=n[0].start)})),e}return r(i,[{key:"afterCreate",value:function(){this.curIndex=-1,this.curPos=null,this.lastCurrentTime=0,this.updateSegments(),this.on(q,this._onDurationChange),this.on(X,this._onLoadedData),this.on(G,this._onTimeupdate),this.on(V,this._onSeeking),this.on(N,this._onPlay)}},{key:"setConfig",value:function(e){var t=this;if(e){var i=Object.keys(e);i.length<1||(i.forEach((function(i){t.config[i]=e[i]})),this.updateSegments())}}},{key:"updateSegments",value:function(){var e=this.config,t=e.disable,i=e.segments,n=this.player;if(t||!i||0===i.length)n.timeSegments=[],n.offsetDuration=0,n.offsetCurrentTime=-1;else{var o=this.formatTimeSegments(i,n.duration);n.timeSegments=o,n.offsetDuration=o.length>0?o[o.length-1].duration:0}}},{key:"formatTimeSegments",value:function(e,t){var i=[];return e?(e.sort((function(e,t){return e.start-t.start})),e.forEach((function(e,n){var o={};if(o.start=e.start<0?0:e.start,o.end=t>0&&e.end>t?t:e.end,!(t>0&&o.start>t)){i.push(o);var r=o.end-o.start;if(0===n)o.offset=e.start,o.cTime=0,o.segDuration=r,o.duration=r;else{var s=i[n-1];o.offset=s.offset+(o.start-s.end),o.cTime=s.duration+s.cTime,o.segDuration=r,o.duration=s.duration+r}}})),i):[]}},{key:"getSeekTime",value:function(e,t,i,n){var o=-1,r=n[i],s=r.start,a=r.end;if(e>=s&&e<=a)return o;var l=e-t;if(l<0&&e<s){var c=t>s?t-s:0;return o=i-1>=0?n[i-1].end+l+c:0}return-1}},{key:"_checkIfEnabled",value:function(e){return!(!e||e.length<1)}},{key:"changeIndex",value:function(e,t){this.curIndex=e,e>=0&&t.length>0?this.curPos=t[e]:this.curPos=null}}],[{key:"pluginName",get:function(){return"TimeSegmentsControls"}},{key:"defaultConfig",get:function(){return{disable:!0,segments:[]}}}]),i}($e);function Ji(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M940.632 837.632l-72.192-72.192c65.114-64.745 105.412-154.386 105.412-253.44s-40.299-188.695-105.396-253.424l-0.016-0.016 72.192-72.192c83.639 83.197 135.401 198.37 135.401 325.632s-51.762 242.434-135.381 325.612l-0.020 0.020zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function $i(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021zM795.648 693.248l-72.704-72.704c27.756-27.789 44.921-66.162 44.921-108.544s-17.165-80.755-44.922-108.546l0.002 0.002 72.704-72.704c46.713 46.235 75.639 110.363 75.639 181.248s-28.926 135.013-75.617 181.227l-0.021 0.021z"></path>\n</svg>\n',"image/svg+xml").firstChild}function Qi(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="28" height="40" viewBox="0 -10 28 40">\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M358.4 358.4h-204.8v307.2h204.8l256 256v-819.2l-256 256z"></path>\n  <path fill="#fff" transform="scale(0.0220625 0.0220625)" d="M920.4 439.808l-108.544-109.056-72.704 72.704 109.568 108.544-109.056 108.544 72.704 72.704 108.032-109.568 108.544 109.056 72.704-72.704-109.568-108.032 109.056-108.544-72.704-72.704-108.032 109.568z"></path>\n</svg>\n',"image/svg+xml").firstChild}var en=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"onBarMousedown",(function(t){var i=u(e).player,n=e.find(".xgplayer-bar");x.event(t);var o=n.getBoundingClientRect(),r=x.getEventPos(t,i.zoom),s=o.height-(r.clientY-o.top);if(r.h=s,r.barH=o.height,e.pos=r,!(s<-2))return e.updateVolumePos(s,t),document.addEventListener("mouseup",e.onBarMouseUp),e._d.isStart=!0,!1})),s(u(e),"onBarMouseMove",(function(t){var i=u(e)._d;if(i.isStart){var n=u(e),o=n.pos,r=n.player;t.preventDefault(),t.stopPropagation(),x.event(t);var s=x.getEventPos(t,r.zoom);i.isMoving=!0;var a=o.h-s.clientY+o.clientY;a>o.barH||e.updateVolumePos(a,t)}})),s(u(e),"onBarMouseUp",(function(t){x.event(t),document.removeEventListener("mouseup",e.onBarMouseUp);var i=u(e)._d;i.isStart=!1,i.isMoving=!1})),s(u(e),"onMouseenter",(function(t){e._d.isActive=!0,e.focus(),e.emit("icon_mouseenter",{pluginName:e.pluginName})})),s(u(e),"onMouseleave",(function(t){e._d.isActive=!1,e.unFocus(100,!1,t),e.emit("icon_mouseleave",{pluginName:e.pluginName})})),s(u(e),"onVolumeChange",(function(t){if(e.player){var i=e.player,n=i.muted,o=i.volume;e._d.isMoving||(e.find(".xgplayer-drag").style.height=n||0===o?"4px":"".concat(100*o,"%"),e.config.showValueLabel&&e.updateVolumeValue()),e.animate(n,o)}})),e}return r(i,[{key:"registerIcons",value:function(){return{volumeSmall:{icon:$i,class:"xg-volume-small"},volumeLarge:{icon:Ji,class:"xg-volume"},volumeMuted:{icon:Qi,class:"xg-volume-mute"}}}},{key:"afterCreate",value:function(){var e=this;if(this._timerId=null,this._d={isStart:!1,isMoving:!1,isActive:!1},!this.config.disable){this.initIcons();var t=this.playerConfig,i=t.commonStyle,n=t.volume;i.volumeColor&&(this.find(".xgplayer-drag").style.backgroundColor=i.volumeColor),this.changeMutedHandler=this.hook("mutedChange",(function(t){e.changeMuted(t)}),{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this._onMouseenterHandler=this.hook("mouseenter",this.onMouseenter),this._onMouseleaveHandler=this.hook("mouseleave",this.onMouseleave),"mobile"!==O.device&&"mobile"!==this.playerConfig.isMobileSimulateMode&&(this.bind("mouseenter",this._onMouseenterHandler),this.bind(["blur","mouseleave"],this._onMouseleaveHandler),this.bind(".xgplayer-slider","mousedown",this.onBarMousedown),this.bind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.bind(".xgplayer-slider","mouseup",this.onBarMouseUp)),this.bind(".xgplayer-icon",["touchend","click"],this.changeMutedHandler),this.on(Y,this.onVolumeChange),this.once(X,this.onVolumeChange),"Number"!==x.typeOf(n)&&(this.player.volume=this.config.default),this.onVolumeChange()}}},{key:"updateVolumePos",value:function(e,t){var i=this.player,n=this.find(".xgplayer-drag"),o=this.find(".xgplayer-bar");if(o&&n){var r=parseInt(e/o.getBoundingClientRect().height*1e3,10);n.style.height="".concat(e,"px");var s=Math.max(Math.min(r/1e3,1),0),a={volume:{from:i.volume,to:s}};i.muted&&(a.muted={from:!0,to:!1}),this.emitUserAction(t,"change_volume",{muted:i.muted,volume:i.volume,props:a}),i.volume=Math.max(Math.min(r/1e3,1),0),i.muted&&(i.muted=!1),this.config.showValueLabel&&this.updateVolumeValue()}}},{key:"updateVolumeValue",value:function(){var e=this.player,t=e.volume,i=e.muted,n=this.find(".xgplayer-value-label"),o=Math.max(Math.min(t,1),0);n.innerText=i?0:Math.ceil(100*o)}},{key:"focus",value:function(){this.player.focus({autoHide:!1}),this._timerId&&(x.clearTimeout(this,this._timerId),this._timerId=null),x.addClass(this.root,"slide-show")}},{key:"unFocus",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=arguments.length>2?arguments[2]:void 0,o=this._d,r=this.player;o.isActive||(this._timerId&&(x.clearTimeout(this,this._timerId),this._timerId=null),this._timerId=x.setTimeout(this,(function(){o.isActive||(i?r.blur():r.focus(),x.removeClass(e.root,"slide-show"),o.isStart&&e.onBarMouseUp(n)),e._timerId=null}),t))}},{key:"changeMuted",value:function(e){e&&e.stopPropagation();var t=this.player;this._d.isStart&&this.onBarMouseUp(e),this.emitUserAction(e,"change_muted",{muted:t.muted,volume:t.volume,props:{muted:{from:t.muted,to:!t.muted}}}),t.volume>0&&(t.muted=!t.muted),t.volume<.01&&(t.volume=this.config.miniVolume)}},{key:"animate",value:function(e,t){e||0===t?this.setAttr("data-state","mute"):t<.5&&this.icons.volumeSmall?this.setAttr("data-state","small"):this.setAttr("data-state","normal")}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.volumeSmall),this.appendChild(".xgplayer-icon",e.volumeLarge),this.appendChild(".xgplayer-icon",e.volumeMuted)}},{key:"destroy",value:function(){this._timerId&&(x.clearTimeout(this,this._timerId),this._timerId=null),this.unbind("mouseenter",this.onMouseenter),this.unbind(["blur","mouseleave"],this.onMouseleave),this.unbind(".xgplayer-slider","mousedown",this.onBarMousedown),this.unbind(".xgplayer-slider","mousemove",this.onBarMouseMove),this.unbind(".xgplayer-slider","mouseup",this.onBarMouseUp),document.removeEventListener("mouseup",this.onBarMouseUp),this.unbind(".xgplayer-icon","mobile"===O.device?"touchend":"click",this.changeMutedHandler)}},{key:"render",value:function(){if(!this.config.disable){var e=this.config.default||this.player.volume,t=this.config.showValueLabel;return'\n    <xg-icon class="xgplayer-volume" data-state="normal">\n      <div class="xgplayer-icon">\n      </div>\n      <xg-slider class="xgplayer-slider">\n        '.concat(t?'<div class="xgplayer-value-label">'.concat(100*e,"</div>"):"",'\n        <div class="xgplayer-bar">\n          <xg-drag class="xgplayer-drag" style="height: ').concat(100*e,'%"></xg-drag>\n        </div>\n      </xg-slider>\n    </xg-icon>')}}}],[{key:"pluginName",get:function(){return"volume"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:1,disable:!1,showValueLabel:!1,default:.6,miniVolume:.2}}}]),i}(ct);function tn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="-4 -6 40 40" fill="none">\n  <g clip-path="url(#clip0)">\n    <path transform="scale(1.5 1.5)" d="M11.6665 9.16663H4.1665C2.78579 9.16663 1.6665 10.2859 1.6665 11.6666V15.8333C1.6665 17.214 2.78579 18.3333 4.1665 18.3333H11.6665C13.0472 18.3333 14.1665 17.214 14.1665 15.8333V11.6666C14.1665 10.2859 13.0472 9.16663 11.6665 9.16663Z" fill="white"/>\n    <path transform="scale(1.5 1.5)" fill-rule="evenodd" clip-rule="evenodd" d="M3.88148 4.06298C3.75371 4.21005 3.67667 4.40231 3.67749 4.61242C3.67847 4.87253 3.79852 5.10435 3.98581 5.25646L6.99111 8.05895C7.32771 8.37283 7.85502 8.35443 8.16891 8.01782C8.48279 7.68122 8.46437 7.15391 8.12778 6.84003L6.62061 5.43457L9.8198 5.4224C9.82848 5.42239 9.8372 5.42221 9.84591 5.4219C10.9714 5.38233 12.0885 5.6285 13.0931 6.13744C14.0976 6.64635 14.957 7.40148 15.5908 8.33234C16.2246 9.2632 16.6122 10.3394 16.7177 11.4606C16.823 12.5819 16.6427 13.7115 16.1934 14.7442C16.0098 15.1661 16.203 15.6571 16.6251 15.8408C17.0471 16.0243 17.5381 15.8311 17.7216 15.4091C18.2833 14.1183 18.5087 12.7063 18.3771 11.3047C18.2453 9.90318 17.7607 8.55792 16.9684 7.39433C16.1761 6.23073 15.1021 5.28683 13.8463 4.65065C12.5946 4.01651 11.203 3.70872 9.80072 3.75583L6.43415 3.76862L7.96326 2.12885C8.27715 1.79225 8.25872 1.26494 7.92213 0.951061C7.58553 0.63718 7.05822 0.655585 6.74433 0.99219L3.90268 4.0395C3.89545 4.04724 3.88841 4.05509 3.88154 4.06303L3.88148 4.06298Z" fill="white"/>\n  </g>\n  <defs>\n    <clipPath id="clip0">\n      <rect width="40" height="40" fill="white"/>\n    </clipPath>\n  </defs>\n</svg>\n',"image/svg+xml").firstChild}var nn=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),(o=t.call(this,e)).rotateDeg=o.config.rotateDeg||0,o}return r(i,[{key:"afterCreate",value:function(){var e=this;if(!this.config.disable){p(l(i.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.rotate),this.onBtnClick=this.onBtnClick.bind(this),this.bind(".xgplayer-icon",["click","touchend"],this.onBtnClick),this.on(ke,(function(){e.rotateDeg&&e.config.innerRotate&&x.setTimeout(e,(function(){e.updateRotateDeg(e.rotateDeg,e.config.innerRotate)}),100)}));var t=this.player.root;this.rootWidth=t.style.width||t.offsetWidth||t.clientWidth,this.rootHeight=t.style.height||t.offsetHeight||t.clientHeight,this.rotateDeg&&this.updateRotateDeg(this.rotateDeg,this.config.innerRotate)}}},{key:"destroy",value:function(){p(l(i.prototype),"destroy",this).call(this),this.unbind(".xgplayer-icon",["click","touchend"],this.onBtnClick)}},{key:"onBtnClick",value:function(e){e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"rotate"),this.rotate(this.config.clockwise,this.config.innerRotate,1)}},{key:"updateRotateDeg",value:function(e,t){if(e||(e=0),t)this.player.videoRotateDeg=e;else{var i=this.player,n=this.rootWidth,o=this.rootHeight,r=i.root,s=i.innerContainer,a=i.media,l=r.offsetWidth,c=s&&t?s.offsetHeight:r.offsetHeight,u=n,h=o,d=0,f=0;.75!==e&&.25!==e||(u="".concat(c,"px"),h="".concat(l,"px"),d=-(c-l)/2,f=-(l-c)/2);var p="translate(".concat(d,"px,").concat(f,"px) rotate(").concat(e,"turn)"),g={transformOrigin:"center center",transform:p,webKitTransform:p,height:h,width:u},v=t?a:r,y=t?i.getPlugin("poster"):null;Object.keys(g).map((function(e){v.style[e]=g[e],y&&y.root&&(y.root.style[e]=g[e])}))}}},{key:"rotate",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.player;this.rotateDeg||(this.rotateDeg=0);var o=e?1:-1;this.rotateDeg=(this.rotateDeg+1+.25*o*i)%1,this.updateRotateDeg(this.rotateDeg,t),n.emit(Ce,360*this.rotateDeg)}},{key:"registerIcons",value:function(){return{rotate:tn}}},{key:"render",value:function(){if(!this.config.disable)return'\n    <xg-icon class="xgplayer-rotate">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(ti(this,"ROTATE_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"rotate"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:6,innerRotate:!0,clockwise:!1,rotateDeg:0,disable:!1}}}]),i}(Vi);function on(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}function rn(){return(new DOMParser).parseFromString('<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M16.5 4.3H3.5C3.38954 4.3 3.3 4.38954 3.3 4.5V15.5C3.3 15.6105 3.38954 15.7 3.5 15.7H8.50005L8.50006 17.5H3.5C2.39543 17.5 1.5 16.6046 1.5 15.5V4.5C1.5 3.39543 2.39543 2.5 3.5 2.5H16.5C17.6046 2.5 18.5 3.39543 18.5 4.5V8.5H16.7V4.5C16.7 4.38954 16.6105 4.3 16.5 4.3ZM12 11.5C11.4477 11.5 11 11.9477 11 12.5L11 16.5C11 17.0523 11.4478 17.5 12 17.5H17.5C18.0523 17.5 18.5 17.0523 18.5 16.5L18.5 12.5C18.5 11.9477 18.0523 11.5 17.5 11.5H12Z"\n    fill="white" />\n  <path fill-rule="evenodd" clip-rule="evenodd"\n    d="M9.4998 7.7C9.77595 7.7 9.9998 7.47614 9.9998 7.2V6.5C9.9998 6.22386 9.77595 6 9.4998 6H5.5402L5.52754 6.00016H5.5C5.22386 6.00016 5 6.22401 5 6.50016V10.4598C5 10.7359 5.22386 10.9598 5.5 10.9598H6.2C6.47614 10.9598 6.7 10.7359 6.7 10.4598V8.83005L8.76983 10.9386C8.96327 11.1357 9.27984 11.1386 9.47691 10.9451L9.97645 10.4548C10.1735 10.2613 10.1764 9.94476 9.983 9.7477L7.97289 7.7H9.4998Z"\n    fill="white" />\n</svg>',"image/svg+xml").firstChild}var sn="picture-in-picture",an="inline",ln="fullscreen",cn=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"switchPIP",(function(t){if(!e.isPIPAvailable())return!1;t.stopPropagation&&t.stopPropagation(),e.isPip?(e.exitPIP(),e.emitUserAction(t,"change_pip",{props:"pip",from:!0,to:!1}),e.setAttr("data-state","normal")):4===e.player.media.readyState&&(e.requestPIP(),e.emitUserAction(t,"change_pip",{props:"pip",from:!1,to:!0}),e.setAttr("data-state","pip"))})),e}return r(i,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.pip&&(e.config.showIcon=e.player.config.pip)}},{key:"afterCreate",value:function(){var e=this;this.isPIPAvailable()&&(p(l(i.prototype),"afterCreate",this).call(this),this.pMode=an,this.initPipEvents(),this.config.showIcon&&this.initIcons(),this.once(se,(function(){e.config.showIcon&&(x.removeClass(e.find(".xgplayer-icon"),"xg-icon-disable"),e.bind("click",e.switchPIP))})))}},{key:"registerIcons",value:function(){return{pipIcon:{icon:on,class:"xg-get-pip"},pipIconExit:{icon:rn,class:"xg-exit-pip"}}}},{key:"initIcons",value:function(){var e=this.icons;this.appendChild(".xgplayer-icon",e.pipIcon),this.appendChild(".xgplayer-icon",e.pipIconExit)}},{key:"initPipEvents",value:function(){var e=this,t=this.player;this.leavePIPCallback=function(){var i=t.paused;x.setTimeout(e,(function(){!i&&t.mediaPlay()}),0),!i&&t.mediaPlay(),e.setAttr("data-state","normal"),e.pipWindow=null,t.emit(be,!1)},this.enterPIPCallback=function(i){t.emit(be,!0),null!=i&&i.pictureInPictureWindow&&(e.pipWindow=i.pictureInPictureWindow),e.setAttr("data-state","pip")},this.onWebkitpresentationmodechanged=function(i){var n=t.media.webkitPresentationMode;e.pMode===ln&&n!==ln&&t.onFullscreenChange(null,!1),e.pMode=n,n===sn?e.enterPIPCallback(i):n===an&&e.leavePIPCallback(i)},t.media&&(t.media.addEventListener("enterpictureinpicture",this.enterPIPCallback),t.media.addEventListener("leavepictureinpicture",this.leavePIPCallback),i.checkWebkitSetPresentationMode(t.media)&&t.media.addEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged))}},{key:"copyStyleIntoPiPWindow",value:function(e){var t=g(document.styleSheets).map((function(t){try{return g(t.cssRules).map((function(e){return e.cssText})).join("")}catch(n){var i=document.createElement("link");i.rel="stylesheet",i.type=t.type,i.media=t.media,i.href=t.href,e.document.head.appendChild(i)}return""})).filter(Boolean).join("\n"),i=document.createElement("style");i.textContent=t,e.document.head.appendChild(i)}},{key:"requestPIP",value:function(){var e=this,t=this.player,n=this.playerConfig,o=this.config;if(this.isPIPAvailable()&&!this.isPip)try{var r=n.poster;if(r&&(t.media.poster="String"===x.typeOf(r)?r:r.poster),o.preferDocument&&this.isDocPIPAvailable()){var s={};if(o.width&&o.height)s.width=o.width,s.height=o.height;else{var a=t.root.getBoundingClientRect();s.width=a.width,s.height=a.height}documentPictureInPicture.requestWindow(s).then((function(i){var n=o.docPiPNode,r=o.docPiPStyle;e.enterPIPCallback();var s=n||t.root,a=s.parentElement,l=s.previousSibling,c=s.nextSibling;e.copyStyleIntoPiPWindow(i);var u=document.createElement("style");if(u.append("body{padding:0; margin:0;}"),r){var h="";"string"==typeof r?h=r:"function"==typeof r&&(h=r.call(o)),h&&u.append(h)}else s===t.root&&u.append("\n              .xgplayer{width: 100%!important; height: 100%!important;}\n            ");i.document.head.append(u),i.document.body.append(s),i.addEventListener("pagehide",(function(t){a&&(c?a.insertBefore(s,c):l?a.insertBefore(s,l.nextSibling):a.appendChild(s)),e.leavePIPCallback()}),{once:!0})}))}else i.checkWebkitSetPresentationMode(t.media)?t.media.webkitSetPresentationMode("picture-in-picture"):t.media.requestPictureInPicture();return!0}catch(l){return console.error("requestPiP",l),!1}}},{key:"exitPIP",value:function(){var e=this.player;try{var t;if(this.isPIPAvailable()&&this.isPip)this.isDocPIPAvailable()&&null!==(t=documentPictureInPicture)&&void 0!==t&&t.window?documentPictureInPicture.window.close():i.checkWebkitSetPresentationMode(e.media)?e.media.webkitSetPresentationMode("inline"):document.exitPictureInPicture();return!0}catch(n){return console.error("exitPIP",n),!1}}},{key:"isPip",get:function(){var e,t=this.player;return!(!this.isDocPIPAvailable()||null===(e=documentPictureInPicture)||void 0===e||!e.window)||document.pictureInPictureElement&&document.pictureInPictureElement===t.media||t.media.webkitPresentationMode===sn}},{key:"isPIPAvailable",value:function(){var e=this.player.media;return("Boolean"!==x.typeOf(document.pictureInPictureEnabled)||document.pictureInPictureEnabled)&&("Boolean"===x.typeOf(e.disablePictureInPicture)&&!e.disablePictureInPicture||e.webkitSupportsPresentationMode&&"Function"===x.typeOf(e.webkitSetPresentationMode))||this.isDocPIPAvailable()}},{key:"isDocPIPAvailable",value:function(){return"documentPictureInPicture"in window&&/^(https|file)/.test(location.protocol)}},{key:"destroy",value:function(){p(l(i.prototype),"destroy",this).call(this);var e=this.player;e.media.removeEventListener("enterpictureinpicture",this.enterPIPCallback),e.media.removeEventListener("leavepictureinpicture",this.leavePIPCallback),i.checkWebkitSetPresentationMode(e.media)&&e.media.removeEventListener("webkitpresentationmodechanged",this.onWebkitpresentationmodechanged),this.exitPIP(),this.unbind("click",this.btnClick)}},{key:"render",value:function(){if(this.config.showIcon&&this.isPIPAvailable())return'<xg-icon class="xgplayer-pip">\n      <div class="xgplayer-icon xg-icon-disable">\n      </div>\n      '.concat(ti(this,"PIP",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"pip"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:6,showIcon:!1,preferDocument:!1,width:void 0,height:void 0,docPiPNode:void 0,docPiPStyle:void 0}}},{key:"checkWebkitSetPresentationMode",value:function(e){return"function"==typeof e.webkitSetPresentationMode}}]),i}(Vi);function un(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="40" viewBox="10 0 24 40">\n  <path transform="scale(0.038 0.028)" d="M800 380v768h-128v-352l-320 320v-704l320 320v-352z"></path>\n</svg>\n',"image/svg+xml").firstChild}var hn=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),s(u(o=t.call(this,e)),"playNext",(function(e){var t=u(o).player;e.preventDefault(),e.stopPropagation(),o.idx+1<o.config.urlList.length?(o.idx++,o.nextHandler(o.config.urlList[o.idx],o.idx),t.emit(we,o.idx+1)):(o.nextHandler(),t.emit(we))})),o.idx=-1,o}return r(i,[{key:"afterCreate",value:function(){this.config.urlList&&0!==this.config.urlList.length&&(this.appendChild(".xgplayer-icon",this.icons.playNext),this.initEvents())}},{key:"registerIcons",value:function(){return{playNext:un}}},{key:"initEvents",value:function(){this.nextHandler=this.hook("nextClick",this.changeSrc);var e="mobile"===O.device?"touchend":"click";this.bind(e,this.playNext),this.show()}},{key:"changeSrc",value:function(e){var t=this.player;e&&(t.pause(),t.currentTime=0,t.switchURL?t.switchURL(e):t.src=e,t.config.url=e,t.play())}},{key:"destroy",value:function(){this.unbind(["touchend","click"],this.playNext)}},{key:"render",value:function(){if(this.config.urlList&&0!==this.config.urlList.length)return'\n     <xg-icon class="xgplayer-playnext">\n      <div class="xgplayer-icon">\n      </div>\n      '.concat(ti(this,"PLAYNEXT_TIPS",this.playerConfig.isHideTips),"\n     </xg-icon>\n    ")}}],[{key:"pluginName",get:function(){return"playNext"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_LEFT,index:1,url:null,urlList:[]}}}]),i}(ct),dn={exports:{}};dn.exports=function e(t,i,n){var o,r,s=window,a="application/octet-stream",l=n||a,c=t,u=!i&&!n&&c,h=document.createElement("a"),d=function(e){return String(e)},f=s.Blob||s.MozBlob||s.WebKitBlob||d,p=i||"download";if(f=f.call?f.bind(s):Blob,"true"===String(this)&&(l=(c=[c,l])[0],c=c[1]),u&&u.length<2048&&(p=u.split("/").pop().split("?")[0],h.href=u,-1!==h.href.indexOf(u))){var g=new XMLHttpRequest;return g.open("GET",u,!0),g.responseType="blob",g.onload=function(t){e(t.target.response,p,a)},setTimeout((function(){g.send()}),0),g}if(/^data:([\w+-]+\/[\w+.-]+)?[,;]/.test(c)){if(!(c.length>2096103.424&&f!==d))return navigator.msSaveBlob?navigator.msSaveBlob(k(c),p):b(c);l=(c=k(c)).type||a}else if(/([\x80-\xff])/.test(c)){for(var v=0,y=new Uint8Array(c.length),m=y.length;v<m;++v)y[v]=c.charCodeAt(v);c=new f([y],{type:l})}function k(e){for(var t=e.split(/[:;,]/),i=t[1],n=("base64"==t[2]?atob:decodeURIComponent)(t.pop()),o=n.length,r=0,s=new Uint8Array(o);r<o;++r)s[r]=n.charCodeAt(r);return new f([s],{type:i})}function b(e,t){if("download"in h)return h.href=e,h.setAttribute("download",p),h.className="download-js-link",h.innerHTML="downloading...",h.style.display="none",document.body.appendChild(h),setTimeout((function(){h.click(),document.body.removeChild(h),!0===t&&setTimeout((function(){s.URL.revokeObjectURL(h.href)}),250)}),66),!0;if(/(Version)\/(\d+)\.(\d+)(?:\.(\d+))?.*Safari\//.test(navigator.userAgent))return/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,a)),window.open(e)||confirm("Displaying New Document\n\nUse Save As... to download, then click back to return to this page.")&&(location.href=e),!0;var i=document.createElement("iframe");document.body.appendChild(i),!t&&/^data:/.test(e)&&(e="data:"+e.replace(/^data:([\w\/\-\+]+)/,a)),i.src=e,setTimeout((function(){document.body.removeChild(i)}),333)}if(o=c instanceof f?c:new f([c],{type:l}),navigator.msSaveBlob)return navigator.msSaveBlob(o,p);if(s.URL)b(s.URL.createObjectURL(o),!0);else{if("string"==typeof o||o.constructor===d)try{return b("data:"+l+";base64,"+s.btoa(o))}catch(C){return b("data:"+l+","+encodeURIComponent(o))}(r=new FileReader).onload=function(e){b(this.result)},r.readAsDataURL(o)}return!0};var fn=dn.exports;function pn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24">\n  <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\n    <g transform="translate(-488.000000, -340.000000)" fill="#FFFFFF">\n      <g id="Group-2">\n        <g id="volme_big-copy" transform="translate(488.000000, 340.000000)">\n          <rect id="Rectangle-18" x="11" y="4" width="2" height="12" rx="1"></rect>\n          <rect id="Rectangle-2" x="3" y="18" width="18" height="2" rx="1"></rect>\n          <rect id="Rectangle-2" transform="translate(4.000000, 17.500000) rotate(90.000000) translate(-4.000000, -17.500000) " x="1.5" y="16.5" width="5" height="2" rx="1"></rect><rect id="Rectangle-2-Copy-3" transform="translate(20.000000, 17.500000) rotate(90.000000) translate(-20.000000, -17.500000) " x="17.5" y="16.5" width="5" height="2" rx="1"></rect>\n          <path d="M9.48791171,8.26502656 L9.48791171,14.2650266 C9.48791171,14.8173113 9.04019646,15.2650266 8.48791171,15.2650266 C7.93562696,15.2650266 7.48791171,14.8173113 7.48791171,14.2650266 L7.48791171,7.26502656 C7.48791171,6.71274181 7.93562696,6.26502656 8.48791171,6.26502656 L15.4879117,6.26502656 C16.0401965,6.26502656 16.4879117,6.71274181 16.4879117,7.26502656 C16.4879117,7.81731131 16.0401965,8.26502656 15.4879117,8.26502656 L9.48791171,8.26502656 Z" id="Combined-Shape" transform="translate(11.987912, 10.765027) scale(1, -1) rotate(45.000000) translate(-11.987912, -10.765027) "></path>\n        </g>\n      </g>\n    </g>\n  </g>\n</svg>\n',"image/svg+xml").firstChild}var gn=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),s(u(o=t.call(this,e)),"download",(function(e){if(!o.isLock){o.emitUserAction(e,"download");var t=o.playerConfig.url,i="";"String"===x.typeOf(t)?i=t:"Array"===x.typeOf(t)&&t.length>0&&(i=t[0].src);var n=o.getAbsoluteURL(i);fn(n),o.isLock=!0,o.timer=window.setTimeout((function(){o.isLock=!1,window.clearTimeout(o.timer),o.timer=null}),300)}})),o.timer=null,o.isLock=!1,o}return r(i,[{key:"afterCreate",value:function(){p(l(i.prototype),"afterCreate",this).call(this),this.config.disable||(this.appendChild(".xgplayer-icon",this.icons.download),this._handler=this.hook("click",this.download,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(["click","touchend"],this._handler))}},{key:"registerIcons",value:function(){return{download:pn}}},{key:"getAbsoluteURL",value:function(e){if(!e.match(/^https?:\/\//)){var t=document.createElement("div");t.innerHTML='<a href="'.concat(e,'">x</a>'),e=t.firstChild.href}return e}},{key:"destroy",value:function(){p(l(i.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.download),window.clearTimeout(this.timer),this.timer=null}},{key:"render",value:function(){if(!this.config.disable)return'<xg-icon class="xgplayer-download">\n   <div class="xgplayer-icon">\n   </div>\n   '.concat(ti(this,"DOWNLOAD_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"download"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:3,disable:!0}}}]),i}(Vi),vn=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.screenShot&&(e.config.disable=!e.player.config.screenShot)}},{key:"afterCreate",value:function(){p(l(i.prototype),"afterCreate",this).call(this),this.appendChild(".xgplayer-icon",this.icons.screenshotIcon);var e=this.config;this.initSize=function(t){e.fitVideo&&(e.width=t.vWidth,e.height=t.vHeight)},this.once(ke,this.initSize)}},{key:"onPluginsReady",value:function(){this.show(),this.onClickBtn=this.onClickBtn.bind(this),this.bind(["click","touchend"],this.onClickBtn)}},{key:"saveScreenShot",value:function(e,t){var i=document.createElement("a");i.href=e,i.download=t;var n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!1,window,0,0,0,0,0,!1,!1,!1,!1,0,null),i.dispatchEvent(n)}},{key:"createCanvas",value:function(e,t){var i=document.createElement("canvas"),n=i.getContext("2d");this.canvasCtx=n,this.canvas=i,i.width=e||this.config.width,i.height=t||this.config.height,n.imageSmoothingEnabled=!0,n.imageSmoothingEnabled&&(n.imageSmoothingQuality="high")}},{key:"onClickBtn",value:function(e){var t=this;e.preventDefault(),e.stopPropagation(),this.emitUserAction(e,"shot");var i=this.config;this.shot(i.width,i.height).then((function(e){t.emit(_e,e),i.saveImg&&t.saveScreenShot(e,i.name+i.format)}))}},{key:"shot",value:function(e,t){var i=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{quality:.92,type:"image/png"},o=this.config,r=this.player,s=n.quality||o.quality,a=n.type||o.type;return new Promise((function(n,l){var c,u=null;if(r.media.canvas)u=r.media.canvas;else{i.canvas?(i.canvas.width=e||o.width,i.canvas.height=t||o.height):i.createCanvas(e,t),u=i.canvas,c=i.canvasCtx;var h,d,f,p,g=r.media.videoWidth/r.media.videoHeight,v=u.width/u.height,y=r.media.videoWidth,m=r.media.videoHeight;g>v?(f=u.width,p=u.width/g,h=0,d=Math.round((u.height-p)/2)):g===v?(f=u.width,p=u.height,h=0,d=0):g<v&&(f=u.height*g,p=u.height,h=Math.round((u.width-f)/2),d=0),c.drawImage(r.media,0,0,y,m,h,d,f,p)}var k=u.toDataURL(a,s).replace(a,"image/octet-stream");n(k=k.replace(/^data:image\/[^;]+/,"data:application/octet-stream"))}))}},{key:"registerIcons",value:function(){return{screenshotIcon:null}}},{key:"destroy",value:function(){p(l(i.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.onClickBtn),this.off(ke,this.initSize)}},{key:"render",value:function(){if(!this.config.disable){var e=this.icons.screenshotIcon?"xgplayer-icon":"xgplayer-icon btn-text",t="SCREENSHOT";return'\n      <xg-icon class="xgplayer-shot">\n      <div class="'.concat(e,'">\n      ').concat(this.icons.screenshotIcon?"":'<span lang-key="'.concat(this.i18nKeys[t],'">').concat(this.i18n[t],"</span>"),"\n      </div>\n    </xg-icon>")}}}],[{key:"pluginName",get:function(){return"screenShot"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:5,quality:.92,type:"image/png",format:".png",width:600,height:337,saveImg:!0,fitVideo:!0,disable:!1,name:"screenshot"}}}]),i}(Vi),yn=function(){function e(t){n(this,e),this.config=t.config,this.parent=t.root,this.root=x.createDom("ul","",{},"xg-options-list xg-list-slide-scroll ".concat(this.config.className)),t.root.appendChild(this.root);var i=this.config.maxHeight;i&&this.setStyle({maxHeight:i}),this.onItemClick=this.onItemClick.bind(this),this.renderItemList();var o="mobile"===O.device?"touchend":"click";this._delegates=ct.delegate.call(this,this.root,"li",o,this.onItemClick)}return r(e,[{key:"renderItemList",value:function(e){var t=this,i=this.config,n=this.root;e?i.data=e:e=i.data,i.style&&Object.keys(i.style).map((function(e){n.style[e]=i[e]})),e.length>0&&(this.attrKeys=Object.keys(e[0])),this.root.innerHTML="",e.map((function(e,i){var n=e.selected?"option-item selected":"option-item";e["data-index"]=i,t.root.appendChild(x.createDom("li","<span>".concat(e.showText,"</span>"),e,n))}))}},{key:"onItemClick",value:function(e){e.delegateTarget||(e.delegateTarget=e.target);var t=e.delegateTarget;if(t&&x.hasClass(t,"selected"))return!1;var i="function"==typeof this.config.onItemClick?this.config.onItemClick:null,n=this.root.querySelector(".selected");x.addClass(t,"selected"),n&&x.removeClass(n,"selected"),i(e,{from:n?this.getAttrObj(n,this.attrKeys):null,to:this.getAttrObj(t,this.attrKeys)})}},{key:"getAttrObj",value:function(e,t){if(!e||!t)return{};var i={};t.map((function(t){i[t]=e.getAttribute(t)}));var n=e.getAttribute("data-index");return n&&(i.index=Number(n)),i}},{key:"show",value:function(){x.removeClass(this.root,"hide"),x.addClass(this.root,"active")}},{key:"hide",value:function(){x.removeClass(this.root,"active"),x.addClass(this.root,"hide")}},{key:"setStyle",value:function(e){var t=this;Object.keys(e).forEach((function(i){t.root.style[i]=e[i]}))}},{key:"destroy",value:function(){this._delegates&&(this._delegates.map((function(e){e.destroy&&e.destroy()})),this._delegates=null),this.root.innerHTML=null,this.parent.removeChild(this.root),this.root=null}}]),e}(),mn="side",kn="middle",bn="click",Cn="hover";var _n="mobile"===O.device,wn=function(e){a(o,e);var t=d(o);function o(e){var i;return n(this,o),s(u(i=t.call(this,e)),"onEnter",(function(e){e.stopPropagation(),i.emit("icon_mouseenter",{pluginName:i.pluginName}),i.switchActiveState(e)})),s(u(i),"switchActiveState",(function(e){e.stopPropagation(),i.config.toggleMode===bn?i.toggle(!i.isActive):i.toggle(!0)})),s(u(i),"onLeave",(function(e){e.stopPropagation(),i.emit("icon_mouseleave",{pluginName:i.pluginName}),i.config.listType!==mn&&i.isActive&&i.toggle(!1)})),s(u(i),"onListEnter",(function(e){i.enterType=2})),s(u(i),"onListLeave",(function(e){i.enterType=0,i.isActive&&i.toggle(!1)})),i.isIcons=!1,i.isActive=!1,i.curValue=null,i.curIndex=0,i}return r(o,[{key:"updateLang",value:function(e){this.renderItemList(this.config.list,this.curIndex)}},{key:"afterCreate",value:function(){var e=this,t=this.config;this.initIcons(),_n&&t.listType!==kn&&(t.listType=mn),t.hidePortrait&&x.addClass(this.root,"portrait"),this.on([ke,pe],(function(){e._resizeList()})),this.once(K,(function(){t.list&&t.list.length>0&&(e.renderItemList(t.list),e.show())})),_n&&this.on(ee,(function(){e.isActive&&(e.optionsList&&e.optionsList.hide(),e.isActive=!1)})),_n?(t.toggleMode=bn,this.activeEvent="touchend"):this.activeEvent=t.toggleMode===bn?"click":"mouseenter",t.toggleMode===bn?this.bind(this.activeEvent,this.switchActiveState):(this.bind(this.activeEvent,this.onEnter),this.bind("mouseleave",this.onLeave)),this.isIcons&&this.bind("click",this.onIconClick)}},{key:"initIcons",value:function(){var e=this,t=this.icons,i=Object.keys(t),n=!1;i.length>0&&(i.forEach((function(i){e.appendChild(".xgplayer-icon",t[i]),!n&&(n=t[i])})),this.isIcons=n),n||(this.appendChild(".xgplayer-icon",x.createDom("span","",{},"icon-text")),x.addClass(this.find(".xgplayer-icon"),"btn-text"))}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||x.addClass(this.root,"show")}},{key:"hide",value:function(){x.removeClass(this.root,"show")}},{key:"getTextByLang",value:function(e,t,n){if(void 0===e)return"";var o=this.config.list;!n&&(n=this.player.lang),t=!t||x.isUndefined(e[t])?"text":t,"number"==typeof e&&(e=o[e]);try{return"object"===i(e[t])?e[t][n]||e[t].en:e[t]}catch(r){return console.warn(r),""}}},{key:"toggle",value:function(e){if(e!==this.isActive){var t=this.player.controls,i=this.config.listType;e?(i===mn?t.blur():t.focus(),this.optionsList&&this.optionsList.show()):(i===mn?t.focus():t.focusAwhile(),this.optionsList&&this.optionsList.hide()),this.isActive=e}}},{key:"onItemClick",value:function(e,t){e.stopPropagation();var i=this.config,n=i.listType,o=i.list;this.curIndex=t.to.index,this.curItem=o[this.curIndex],this.changeCurrentText(),(this.config.isItemClickHide||_n||n===mn)&&this.toggle(!1)}},{key:"onIconClick",value:function(e){}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,t=e[this.curIndex<e.length?this.curIndex:0];t&&(this.find(".icon-text").innerHTML=this.getTextByLang(t,"iconText"))}}},{key:"renderItemList",value:function(e,t){var i=this,n=this.config,o=this.optionsList,r=this.player;if("number"==typeof t&&(this.curIndex=t,this.curItem=n.list[t]),o)return o.renderItemList(e),void this.changeCurrentText();var s,a,l={config:{data:e||[],className:(s=n.listType,a=n.position,s===mn?a===rt.CONTROLS_LEFT?"xg-side-list xg-left-side":"xg-side-list xg-right-side":""),onItemClick:function(e,t){i.onItemClick(e,t)}},root:n.listType===mn?r.innerContainer||r.root:this.root};if(this.config.isShowIcon){var c=this.player.root.getBoundingClientRect().height,u=n.listType===kn?c-50:c;u&&n.heightLimit&&(l.config.maxHeight="".concat(u,"px")),this.optionsList=new yn(l),this.changeCurrentText(),this.show()}this._resizeList()}},{key:"_resizeList",value:function(){if(this.config.heightLimit){var e=this.player.root.getBoundingClientRect().height,t=this.config.listType===kn?e-50:e;this.optionsList&&this.optionsList.setStyle({maxHeight:"".concat(t,"px")})}}},{key:"destroy",value:function(){this.config.toggleMode===bn?this.unbind(this.activeEvent,this.switchActiveState):(this.unbind(this.activeEvent,this.onEnter),this.unbind("mouseleave",this.onLeave)),this.isIcons&&this.unbind("click",this.onIconClick),this.optionsList&&(this.optionsList.destroy(),this.optionsList=null)}},{key:"render",value:function(){if(this.config.isShowIcon)return'<xg-icon class="xg-options-icon '.concat(this.config.className||"",'">\n    <div class="xgplayer-icon">\n    </div>\n   </xg-icon>')}}],[{key:"pluginName",get:function(){return"optionsIcon"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:100,list:[],listType:"middle",listStyle:{},hidePortrait:!0,isShowIcon:!1,isItemClickHide:!0,toggleMode:Cn,heightLimit:!0}}}]),o}(ct),Tn=function(e){a(o,e);var i=d(o);function o(e){var t;return n(this,o),(t=i.call(this,e)).curTime=0,t.isPaused=!0,t}return r(o,[{key:"beforeCreate",value:function(e){var t=e.config.list;Array.isArray(t)&&t.length>0&&(e.config.list=t.map((function(e){return!e.text&&e.name&&(e.text=e.name),e.text||(e.text=e.definition),e})))}},{key:"afterCreate",value:function(){var e=this;p(l(o.prototype),"afterCreate",this).call(this),this.on("resourceReady",(function(t){e.changeDefinitionList(t)})),this.on(ye,(function(t){e.renderItemList(e.config.list,t.to)})),this.player.definitionList.length<2&&this.hide()}},{key:"show",value:function(e){!this.config.list||this.config.list.length<2||x.addClass(this.root,"show")}},{key:"initDefinition",value:function(){var e=this.config,t=e.list,i=e.defaultDefinition;if(t.length>0){var n=null;t.map((function(e){e.definition===i&&(n=e)})),n||(n=t[0]),this.changeDefinition(n)}}},{key:"renderItemList",value:function(){var e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.list||[],n=arguments.length>1?arguments[1]:void 0,r=n&&n.definition?n.definition:this.config.defaultDefinition;n&&i.forEach((function(e){e.selected=!1}));var s=0,a=i.map((function(i,n){var o=t(t({},i),{},{showText:e.getTextByLang(i)||i.definition,selected:!1});return(i.selected||i.definition&&i.definition==r)&&(o.selected=!0,s=n),o}));p(l(o.prototype),"renderItemList",this).call(this,a,s)}},{key:"changeDefinitionList",value:function(e){Array.isArray(e)&&(this.config.list=e.map((function(e){return!e.text&&e.name&&(e.text=e.name),e.text||(e.text=e.definition),e})),this.renderItemList(),this.config.list.length<2?this.hide():this.show())}},{key:"changeDefinition",value:function(e,t){this.player.changeDefinition(e,t)}},{key:"onItemClick",value:function(e,t){var i=this.player.definitionList;p(l(o.prototype),"onItemClick",this).apply(this,arguments),this.emitUserAction(e,"change_definition",{from:t.from,to:t.to});for(var n=0;n<i.length;n++)t.to&&i[n].definition===t.to.definition&&(t.to.url=i[n].url),t.from&&i[n].definition===t.from.definition&&(t.from.url=i[n].url);this.player.changeDefinition(t.to,t.from)}}],[{key:"pluginName",get:function(){return"definition"}},{key:"defaultConfig",get:function(){return t(t({},wn.defaultConfig),{},{position:rt.CONTROLS_RIGHT,index:3,list:[],defaultDefinition:"",disable:!1,hidePortrait:!1,className:"xgplayer-definition",isShowIcon:!0})}}]),o}(wn),xn=function(e){a(o,e);var i=d(o);function o(e){var t;return n(this,o),(t=i.call(this,e)).curRate=1,t}return r(o,[{key:"beforeCreate",value:function(e){var t=e.player.config.playbackRate,i=t?Array.isArray(t)?t:e.config.list:[];Array.isArray(i)&&(e.config.list=i.map((function(e){return"number"==typeof e?e={rate:e,text:"".concat(e,"x")}:!e.text&&e.rate&&(e.text="".concat(e.rate,"x")),e})))}},{key:"afterCreate",value:function(){var e=this;p(l(o.prototype),"afterCreate",this).call(this),this.on(Z,(function(){e.curValue!==e.player.playbackRate&&e.renderItemList()})),this.renderItemList()}},{key:"show",value:function(e){this.config.list&&0!==this.config.list.length&&p(l(o.prototype),"show",this).call(this)}},{key:"onItemClick",value:function(e,t){p(l(o.prototype),"onItemClick",this).apply(this,arguments);var i=e.delegateTarget,n=Number(i.getAttribute("rate"));if(!n||n===this.curValue)return!1;var r={playbackRate:{from:this.player.playbackRate,to:n}};this.emitUserAction(e,"change_rate",{props:r}),this.curValue=n,this.player.playbackRate=n}},{key:"renderItemList",value:function(){var e=this,t=this.player.playbackRate||1;this.curValue=t;var i=-1,n=this.config.list.map((function(n,o){var r={rate:n.rate};return r.rate===t&&(r.selected=!0,i=o),r.showText=e.getTextByLang(n),r}));p(l(o.prototype),"renderItemList",this).call(this,n,i)}},{key:"changeCurrentText",value:function(){if(!this.isIcons){var e=this.config.list,t=e[this.curIndex<e.length?this.curIndex:0],i="";i=!t||this.curIndex<0?"".concat(this.player.playbackRate,"x"):this.getTextByLang(t,"iconText"),this.find(".icon-text").innerHTML=i}}},{key:"destroy",value:function(){p(l(o.prototype),"destroy",this).call(this)}}],[{key:"pluginName",get:function(){return"playbackRate"}},{key:"defaultConfig",get:function(){return t(t({},wn.defaultConfig),{},{position:rt.CONTROLS_RIGHT,index:4,list:[2,1.5,1,.75,.5],className:"xgplayer-playbackrate",isShowIcon:!0,hidePortrait:!1})}}]),o}(wn);function Sn(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" class=\'path_full\' d="M9,10v1a.9.9,0,0,1-1,1,.9.9,0,0,1-1-1V9A.9.9,0,0,1,8,8h2a.9.9,0,0,1,1,1,.9.9,0,0,1-1,1Zm6,4V13a1,1,0,0,1,2,0v2a.9.9,0,0,1-1,1H14a1,1,0,0,1,0-2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}function En(){return(new DOMParser).parseFromString('<svg xmlns="http://www.w3.org/2000/svg" width="31" height="40" viewBox="0 -5 31 40">\n  <path fill="#fff" transform="scale(1.3, 1.3)" d="M9,10V9a.9.9,0,0,1,1-1,.9.9,0,0,1,1,1v2a.9.9,0,0,1-1,1H8a.9.9,0,0,1-1-1,.9.9,0,0,1,1-1Zm6,4v1a1,1,0,0,1-2,0V13a.9.9,0,0,1,1-1h2a1,1,0,0,1,0,2Zm3-7H6V17H18Zm2,0V17a2,2,0,0,1-2,2H6a2,2,0,0,1-2-2V7A2,2,0,0,1,6,5H18A2,2,0,0,1,20,7Z"></path>\n</svg>\n',"image/svg+xml").firstChild}var Pn=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"beforeCreate",value:function(e){"boolean"==typeof e.player.config.cssFullscreen&&(e.config.disable=!e.player.config.cssFullscreen)}},{key:"afterCreate",value:function(){var e=this;p(l(i.prototype),"afterCreate",this).call(this),this.config.disable||(this.config.target&&(this.playerConfig.fullscreenTarget=this.config.target),this.initIcons(),this.on(ge,(function(t){e.animate(t)})),this.btnClick=this.btnClick.bind(this),this.handleCssFullscreen=this.hook("cssFullscreen_change",this.btnClick,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.bind(["click","touchend"],this.handleCssFullscreen))}},{key:"initIcons",value:function(){var e=this.icons,t=this.find(".xgplayer-icon");t.appendChild(e.cssFullscreen),t.appendChild(e.exitCssFullscreen)}},{key:"btnClick",value:function(e){e.preventDefault(),e.stopPropagation();var t=this.player.isCssfullScreen;this.emitUserAction(e,"switch_cssfullscreen",{cssfullscreen:t}),t?this.player.exitCssFullscreen():this.player.getCssFullscreen()}},{key:"animate",value:function(e){this.root&&(e?this.setAttr("data-state","full"):this.setAttr("data-state","normal"),this.switchTips(e))}},{key:"switchTips",value:function(e){var t=this.i18nKeys,i=this.find(".xg-tips");i&&this.changeLangTextKey(i,e?t.EXITCSSFULLSCREEN_TIPS:t.CSSFULLSCREEN_TIPS)}},{key:"registerIcons",value:function(){return{cssFullscreen:{icon:Sn,class:"xg-get-cssfull"},exitCssFullscreen:{icon:En,class:"xg-exit-cssfull"}}}},{key:"destroy",value:function(){p(l(i.prototype),"destroy",this).call(this),this.unbind(["click","touchend"],this.btnClick)}},{key:"render",value:function(){if(!this.config.disable)return"<xg-icon class='xgplayer-cssfullscreen'>\n    <div class=\"xgplayer-icon\">\n    </div>\n    ".concat(ti(this,"CSSFULLSCREEN_TIPS",this.playerConfig.isHideTips),"\n    </xg-icon>")}}],[{key:"pluginName",get:function(){return"cssFullscreen"}},{key:"defaultConfig",get:function(){return{position:rt.CONTROLS_RIGHT,index:1,disable:!1,target:null}}}]),i}(Vi),In=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"afterCreate",value:function(){var e=this;this.clickHandler=this.hook("errorRetry",this.errorRetry,{pre:function(e){e.preventDefault(),e.stopPropagation()}}),this.onError=this.hook("showError",this.handleError),this.bind(".xgplayer-error-refresh","click",this.clickHandler),this.on(U,(function(t){e.onError(t)}))}},{key:"errorRetry",value:function(e){this.emitUserAction(e,"error_retry",{}),this.player.retry()}},{key:"handleError",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.player,i=e.errorType,n=t.errorNote?this.i18n[t.errorNote]:"";if(!n)switch(i){case"decoder":n=this.i18n.MEDIA_ERR_DECODE;break;case"network":n=this.i18n.MEDIA_ERR_NETWORK;break;default:n=this.i18n.MEDIA_ERR_SRC_NOT_SUPPORTED}this.find(".xgplayer-error-text").innerHTML=n,this.find(".xgplayer-error-tips").innerHTML="".concat(this.i18n.REFRESH_TIPS,'<span class="xgplayer-error-refresh">').concat(this.i18n.REFRESH,"</span>")}},{key:"destroy",value:function(){this.unbind(".xgplayer-error-refresh","click",this.clickHandler)}},{key:"render",value:function(){return'<xg-error class="xgplayer-error">\n      <div class="xgplayer-errornote">\n       <span class="xgplayer-error-text"></span>\n       <span class="xgplayer-error-tips"><em class="xgplayer-error-refresh"></em></span>\n      </div>\n    </xg-error>'}}],[{key:"pluginName",get:function(){return"error"}}]),i}(ct),Ln=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"afterCreate",value:function(){var e=this;this.intervalId=0,this.customConfig=null,this.bind(".highlight",["click","touchend"],(function(t){(e.config.onClick||e.customOnClick)&&(t.preventDefault(),t.stopPropagation(),e.customOnClick?e.customOnClick(t):e.config.onClick(t))})),this.player.showPrompt=function(){e.showPrompt.apply(e,arguments)},this.player.hidePrompt=function(){e.hide()}}},{key:"setStyle",value:function(e){var t=this;Object.keys(e).map((function(i){t.root.style[i]=e[i]}))}},{key:"showPrompt",value:function(e){var t=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};if(e){this.customOnClick=n;var o=this.config.interval;this.intervalId&&(clearTimeout(this.intervalId),this.intervalId=null),x.addClass(this.root,"show"),"arrow"===i.mode&&x.addClass(this.root,"arrow"),this.find(".xgplayer-prompt-detail").innerHTML="string"==typeof e?e:"".concat(e.text||"")+"".concat(e.highlight?'<i class="highlight">'.concat(e.highlight,"</i>"):""),i.style&&this.setStyle(i.style);var r="boolean"==typeof i.autoHide?i.autoHide:this.config.autoHide;if(r){var s=i.interval||o;this.intervalId=setTimeout((function(){t.hide()}),s)}}}},{key:"hide",value:function(){x.removeClass(this.root,"show"),x.removeClass(this.root,"arrow"),this.root.removeAttribute("style"),this.customOnClick=null}},{key:"render",value:function(){return'<xg-prompt class="xgplayer-prompt '.concat(ft.CONTROLS_FOLLOW,'">\n    <span class="xgplayer-prompt-detail"></span>\n    </xg-prompt>')}}],[{key:"pluginName",get:function(){return"prompt"}},{key:"defaultConfig",get:function(){return{interval:3e3,style:{},mode:"arrow",autoHide:!0,detail:{text:"",highlight:""},onClick:function(){}}}}]),i}(ct),An={time:0,text:"",id:1,duration:1,color:"#fff",style:{},width:6,height:6};function On(e){Object.keys(An).map((function(t){void 0===e[t]&&(e[t]=An[t])}))}var Dn={_updateDotDom:function(e,t){if(t){var i=this.calcuPosition(e.time,e.duration),n=e.style||{};n.left="".concat(i.left,"%"),n.width="".concat(i.width,"%"),t.setAttribute("data-text",e.text),t.setAttribute("data-time",e.time),i.isMini?x.addClass(t,"mini"):x.removeClass(t,"mini"),Object.keys(n).map((function(e){t.style[e]=n[e]}))}},initDots:function(){var e=this;this._ispots.map((function(t){e.createDot(t,!1)})),this.ispotsInit=!0},createDot:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],i=this.player.plugins.progress;if(i&&(t&&(On(e),this._ispots.push(e)),this.ispotsInit||!t)){var n=this.calcuPosition(e.time,e.duration),o=e.style||{};o.left="".concat(n.left,"%"),o.width="".concat(n.width,"%");var r="xgspot_".concat(e.id," xgplayer-spot");n.isMini&&(r+=" mini");var s=e.template?'<div class="xgplayer-spot-pop">'.concat(e.template,"</div>"):"",a=x.createDom("xg-spot",s,{"data-text":e.text,"data-time":e.time,"data-id":e.id},r);Object.keys(o).map((function(e){a.style[e]=o[e]})),i.outer&&i.outer.appendChild(a),this.positionDot(a,e.id)}},findDot:function(e){if(this.player.plugins.progress){var t=this._ispots.filter((function(t,i){return t.id===e}));return t.length>0?t[0]:null}},updateDot:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.player.plugins.progress;if(i){var n=this.findDot(e.id);if(n&&Object.keys(e).map((function(t){n[t]=e[t]})),this.ispotsInit){var o=i.find('xg-spot[data-id="'.concat(e.id,'"]'));o&&(this._updateDotDom(e,o),t&&this.showDot(e.id))}}},deleteDot:function(e){var t=this._ispots,i=this.player.plugins.progress;if(i){for(var n=[],o=0;o<t.length;o++)t[o].id===e&&n.push(o);for(var r=n.length-1;r>=0;r--)if(t.splice(n[r],1),this.ispotsInit){var s=i.find('xg-spot[data-id="'.concat(e,'"]'));s&&s.parentElement.removeChild(s)}}},deleteAllDots:function(){var e=this.player.plugins.progress;if(e)if(this.ispotsInit){for(var t=e.root.getElementsByTagName("xg-spot"),i=t.length-1;i>=0;i--)e.outer.removeChild(t[i]);this._ispots=[]}else this._ispots=[]},updateAllDots:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=this.player.plugins.progress;if(i)if(this.ispotsInit){this._ispots=[];var n=i.root.getElementsByTagName("xg-spot"),o=n.length;if(o>t.length)for(var r=o-1;r>t.length-1;r--)i.outer.removeChild(n[r]);t.forEach((function(t,i){i<o?(n[i].setAttribute("data-id","".concat(t.id)),e._ispots.push(t),e.updateDot(t)):e.createDot(t)}))}else this._ispots=t},positionDots:function(){var e=this,t=this._ispots,i=this.playerSize,n=this.player.sizeInfo,o=this.player.plugins.progress;o&&n.width!==i.width&&(i.width=n.width,i.left=n.left,t.forEach((function(t){var i=o.find('xg-spot[data-id="'.concat(t.id,'"]'));i&&e.positionDot(i,t.id)})))},positionDot:function(e,t){var i=x.findDom(e,".xgplayer-spot-pop");if(i){var n=this.playerSize,o=e.getBoundingClientRect(),r=i.getBoundingClientRect(),s=o.left-n.left,a=n.width-s-o.width/2;if(s<r.width/2||n.width<r.width){var l=r.width/2-s;i.style.left="".concat(l,"px")}else if(a<r.width/2){var c=a-r.width/2+o.width/2;i.style.left="".concat(c,"px")}else i.style.left="50%"}},updateDuration:function(){var e=this,t=this.player.plugins.progress;t&&this._ispots.forEach((function(i){var n=t.find('xg-spot[data-id="'.concat(i.id,'"]'));e._updateDotDom(i,n)}))},getAllDotsDom:function(){var e=this.player.plugins.progress;return e?e.root.getElementsByTagName("xg-spot"):[]},getDotDom:function(e){var t=this.player.plugins.progress;if(t)return t.find('xg-spot[data-id="'.concat(e,'"]'))}};var Rn={dragmove:"onProgressMove",dragstart:"onProgressDragStart",dragend:"onProgressDragEnd",click:"onProgressClick",mouseover:"onProgressMouseOver",mouseenter:"onProgressMouseOver"},Mn=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),s(u(o=t.call(this,e)),"onMousemove",(function(e){o.config.disable||(x.hasClass(e.target,"xg-spot-content")&&o.config.isHideThumbnailHover?o.player.plugins.progress.onMouseLeave(e):(o._state.f||x.hasClass(e.target,"xg-spot-content"))&&(x.event(e),e.stopPropagation()))})),s(u(o),"onMousedown",(function(e){o.config.disable||(o._state.f||x.hasClass(e.target,"xg-spot-content"))&&(x.event(e),e.stopPropagation())})),s(u(o),"onMouseup",(function(e){if(o.isDrag){var t=o.player.plugins.progress;t&&t.pos&&(t.onMouseUp(e),!t.pos.isEnter&&t.onMouseLeave(e))}})),s(u(o),"onDotMouseLeave",(function(e){if(!o.config.disable){o._curDot.removeEventListener("mouseleave",o.onDotMouseLeave),o.blurDot(e.target),o._curDot=null;var t=o.player.plugins.progress;t&&t.enableBlur(),o.show()}})),s(u(o),"onProgressMouseOver",(function(e,t){if(!o.config.disable&&x.hasClass(t.target,"xgplayer-spot")&&!o._curDot){o._curDot=t.target,o.focusDot(t.target),o._curDot.children.length>0&&o.hide();var i=o.player.plugins.progress;i&&i.disableBlur(),o._curDot.addEventListener("mouseleave",o.onDotMouseLeave)}})),o._ispots=[],o.videoPreview=null,o.videothumbnail=null,o.thumbnail=null,o.timeStr="",o._state={now:0,f:!1},o}return r(i,[{key:"beforeCreate",value:function(e){var t=e.player.plugins.progress;t&&(e.root=t.root)}},{key:"afterCreate",value:function(){var e=this;this._curDot=null,this.handlerSpotClick=this.hook("spotClick",(function(t,i){i.seekTime&&e.player.seek(i.seekTime)})),this.transformTimeHook=this.hook("transformTime",(function(t){e.setTimeContent(x.format(t))})),function(e){var t=e.config,i=e.player;Object.keys(Dn).map((function(t){e[t]=Dn[t].bind(e)}));var n=i.config.progressDot||t.ispots||[];e._ispots=n.map((function(e){return On(e),e})),e.ispotsInit=!1,e.playerSize={left:i.sizeInfo.left,width:i.sizeInfo.width},e.on(q,(function(){e.ispotsInit?e.updateDuration():e.initDots()})),e.on(ke,(function(){e.positionDots()}))}(this),this.on(q,(function(){e.show()})),this.config.disable&&this.disable(),this.extTextRoot=this.find(".xg-spot-ext-text")}},{key:"setConfig",value:function(e){var t=this;e&&Object.keys(e).map((function(i){t.config[i]=e[i]}))}},{key:"onPluginsReady",value:function(){this.player.plugins.progress&&(this.previewLine=this.find(".xg-spot-line"),this.timePoint=this.find(".xgplayer-progress-point"),this.timeText=this.find(".xg-spot-time"),this.tipText=this.find(".spot-inner-text"),this._hasThumnail=!1,this.registerThumbnail(),this.bindEvents())}},{key:"bindEvents",value:function(){var e=this,t=this.player.plugins.progress;if(t&&(Object.keys(Rn).map((function(i){e[Rn[i]]=e[Rn[i]].bind(e),t.addCallBack(i,e[Rn[i]])})),"mobile"!==O.device)){this.bind(".xg-spot-info","mousemove",this.onMousemove),this.bind(".xg-spot-info","mousedown",this.onMousedown),this.bind(".xg-spot-info","mouseup",this.onMouseup);var i=this.hook("previewClick",(function(){}));this.handlerPreviewClick=function(t){t.stopPropagation(),i(parseInt(1e3*e._state.now,10)/1e3,t)},this.bind(".xg-spot-content","mouseup",this.handlerPreviewClick)}}},{key:"onProgressMove",value:function(e,t){!this.config.disable&&this.player.duration&&this.updatePosition(e.offset,e.width,e.currentTime,e.e)}},{key:"onProgressDragStart",value:function(e){!this.config.disable&&this.player.duration&&(this.isDrag=!0,this.videoPreview&&x.addClass(this.videoPreview,"show"))}},{key:"onProgressDragEnd",value:function(e){!this.config.disable&&this.player.duration&&(this.isDrag=!1,this.videoPreview&&x.removeClass(this.videoPreview,"show"))}},{key:"onProgressClick",value:function(e,t){this.config.disable||x.hasClass(t.target,"xgplayer-spot")&&(t.stopPropagation(),t.preventDefault(),["time","id","text"].map((function(i){e[i]=t.target.getAttribute("data-".concat(i))})),e.time&&(e.time=Number(e.time)),this.handlerSpotClick(t,e))}},{key:"updateLinePos",value:function(e,t){var i=this.root,n=this.previewLine,o=this.player,r=this.config,s="flex"===o.controls.mode,a=i.getBoundingClientRect().width;if(a||!this._hasThumnail){var l,c=e-(a=this._hasThumnail&&a<r.width?r.width:a)/2;c<0&&!s?(c=0,l=e-a/2):c>t-a&&!s?(l=c-(t-a),c=t-a):l=0,void 0!==l&&(n.style.transform="translateX(".concat(l.toFixed(2),"px)")),i.style.transform="translateX(".concat(c.toFixed(2),"px) translateZ(0)")}}},{key:"updateTimeText",value:function(e){var t=this.timeText,i=this.timePoint;t.innerHTML=e,!this.thumbnail&&(i.innerHTML=e)}},{key:"updatePosition",value:function(e,t,i,n){var o=this.root,r=this.config,s=this._state;if(o){s.now=i,this.transformTimeHook(i);var a=this.timeStr;n&&n.target&&x.hasClass(n.target,"xgplayer-spot")?(this.showTips(n.target.getAttribute("data-text"),!1,a),this.focusDot(n.target),s.f=!0,r.isFocusDots&&s.f&&(s.now=parseInt(n.target.getAttribute("data-time"),10))):r.defaultText?(s.f=!1,this.showTips(r.defaultText,!0,a)):(s.f=!1,this.hideTips("")),this.updateTimeText(a),this.updateThumbnails(s.now),this.updateLinePos(e,t)}}},{key:"setTimeContent",value:function(e){this.timeStr=e}},{key:"updateThumbnails",value:function(e){var t=this.player,i=this.videoPreview,n=this.config,o=t.plugins.thumbnail;if(o&&o.usable){this.thumbnail&&o.update(this.thumbnail,e,n.width,n.height);var r=i&&i.getBoundingClientRect();this.videothumbnail&&o.update(this.videothumbnail,e,r.width,r.height)}}},{key:"registerThumbnail",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("mobile"!==O.device){var t=this.player,i=this.config,n=t.getPlugin("thumbnail");if(n&&n.setConfig(e),n&&n.usable&&i.isShowThumbnail){x.removeClass(this.root,"short-line no-thumbnail"),"short"===i.mode&&x.addClass(this.root,"short-line"),this._hasThumnail=!0;var o=this.find(".xg-spot-thumbnail");this.thumbnail=n.createThumbnail(o,"progress-thumbnail"),i.isShowCoverPreview&&(this.videoPreview=x.createDom("xg-video-preview","",{},"xgvideo-preview"),t.root.appendChild(this.videoPreview),this.videothumbnail=n.createThumbnail(this.videoPreview,"xgvideo-thumbnail")),this.updateThumbnails(0)}else x.addClass(this.root,"short-line no-thumbnail")}}},{key:"calcuPosition",value:function(e,t){var i=this.player.plugins.progress,n=this.player,o=i.root.getBoundingClientRect().width,r=n.duration/o*6;return e+t>n.duration&&(t=n.duration-e),n.duration,n.duration,{left:e/n.duration*100,width:t/n.duration*100,isMini:t<r}}},{key:"showDot",value:function(e){var t=this.findDot(e);if(t){var i=this.root.getBoundingClientRect().width,n=t.time/this.player.duration*i;this.updatePosition(n,i,t.time)}}},{key:"focusDot",value:function(e,t){e&&(t||(t=e.getAttribute("data-id")),x.addClass(e,"active"),this._activeDotId=t)}},{key:"blurDot",value:function(e){if(!e){var t=this._activeDotId;e=this.getDotDom(t)}e&&(x.removeClass(e,"active"),this._activeDotId=null)}},{key:"showTips",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";x.addClass(this.root,"no-timepoint"),e&&(x.addClass(this.find(".xg-spot-content"),"show-text"),t&&"production"===this.config.mode?(x.addClass(this.root,"product"),this.tipText.textContent=e):(x.removeClass(this.root,"product"),this.tipText.textContent=this._hasThumnail?e:"".concat(i," ").concat(e)))}},{key:"hideTips",value:function(){x.removeClass(this.root,"no-timepoint"),this.tipText.textContent="",x.removeClass(this.find(".xg-spot-content"),"show-text"),x.removeClass(this.root,"product")}},{key:"hide",value:function(){x.addClass(this.root,"hide")}},{key:"show",value:function(e){x.removeClass(this.root,"hide")}},{key:"enable",value:function(){var e=this.config,t=this.playerConfig;this.config.disable=!1,this.show(),!this.thumbnail&&e.isShowThumbnail&&this.registerThumbnail(t.thumbnail||{})}},{key:"disable",value:function(){this.config.disable=!0,this.hide()}},{key:"destroy",value:function(){var e=this,t=this.player.plugins.progress;t&&Object.keys(Rn).map((function(i){t.removeCallBack(i,e[Rn[i]])})),this.videothumbnail=null,this.thumbnail=null,this.videoPreview&&this.player.root.removeChild(this.videoPreview),this.unbind(".xg-spot-info","mousemove",this.onMousemove),this.unbind(".xg-spot-info","mousedown",this.onMousedown),this.unbind(".xg-spot-info","mouseup",this.onMouseup),this.unbind(".xg-spot-content","mouseup",this.handlerPreviewClick)}},{key:"render",value:function(){return"mobile"===O.device||"mobile"===this.playerConfig.isMobileSimulateMode?"":'<div class="xg-spot-info hide '.concat("short"===this.config.mode?"short-line":"",'">\n      <div class="xg-spot-content">\n        <div class="xg-spot-thumbnail">\n          <span class="xg-spot-time"></span>\n        </div>\n        <div class="xg-spot-text"><span class="spot-inner-text"></span></div>\n      </div>\n      <div class="xgplayer-progress-point">00:00</div>\n      <div class="xg-spot-ext-text"></div>\n      <div class="xg-spot-line"></div>\n    </div>')}}],[{key:"pluginName",get:function(){return"progresspreview"}},{key:"defaultConfig",get:function(){return{index:1,miniWidth:6,ispots:[],defaultText:"",isFocusDots:!0,isHideThumbnailHover:!0,isShowThumbnail:!0,isShowCoverPreview:!1,mode:"",disable:!1,width:160,height:90}}}]),i}(ct),jn=function(e){a(i,e);var t=d(i);function i(e){var o;return n(this,i),(o=t.call(this,e)).ratio=1,o.interval=null,o._preloadMark={},o}return r(i,[{key:"afterCreate",value:function(){var e=this;this.usable&&this.initThumbnail(),this.on([q],(function(){var t=e.config,i=t.pic_num,n=t.interval;e.usable&&(e.interval=n>0?n:Math.round(1e3*e.player.duration/i)/1e3)}))}},{key:"setConfig",value:function(e){var t=this;if(e){var i=Object.keys(e);i.length<1||(i.forEach((function(i){t.config[i]=e[i]})),this.usable&&this.initThumbnail())}}},{key:"usable",get:function(){var e=this.config,t=e.urls,i=e.pic_num;return t&&t.length>0&&i>0}},{key:"initThumbnail",value:function(){var e=this.config,t=e.width,i=e.height,n=e.pic_num,o=e.interval;this.ratio=t/i*100,this.interval=o||Math.round(this.player.duration/n),this._preloadMark={}}},{key:"getUrlByIndex",value:function(e){return e>=0&&e<this.config.urls.length?this.config.urls[e]:""}},{key:"preload",value:function(e){var t=this;if(!this._preloadMark[e]){var i=this.config.urls,n=i.length,o=[];e>0&&o.push(e-1),o.push(e),e>0&&e<n-1&&o.push(e+1),o.map((function(e){!t._preloadMark[e]&&e>=0&&e<n&&(t._preloadMark[e]=1,x.preloadImg(i[e],(function(){t._preloadMark[e]=2})))}))}}},{key:"getPosition",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=this.config,o=n.pic_num,r=n.row,s=n.col,a=n.width,l=n.height;this.interval=Math.round(this.player.duration/o);var c=Math.ceil(e/this.interval),u=(c=c>o?o:c)<r*s?0:Math.ceil(c/(r*s))-1,h=c-u*(s*r),d=h>0?Math.ceil(h/s)-1:0,f=h>0?h-d*s-1:0,p=0,g=0;if(t&&i){var v=t/i;v<a/l?p=(g=i)*(a/l):g=(p=t)/(a/l)}else i?t||(p=(g=i||l)*(a/l)):g=(p=t||a)/(a/l);var y=this.getUrlByIndex(u);return{urlIndex:u,rowIndex:d,colIndex:f,url:y,height:g,width:p,style:{backgroundImage:"url(".concat(y,")"),backgroundSize:"".concat(p*s,"px auto"),backgroundPosition:"-".concat(f*p,"px -").concat(d*g,"px"),width:"".concat(p,"px"),height:"".concat(g,"px")}}}},{key:"update",value:function(e,t){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",r=this.config,s=r.pic_num,a=r.urls;if(!(s<=0)&&a&&0!==a.length){var l=this.getPosition(t,i,n);this.preload(l.urlIndex),Object.keys(l.style).map((function(t){e.style[t]=l.style[t]})),Object.keys(o).map((function(t){e.style[t]=o[t]}))}}},{key:"changeConfig",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.setConfig(e)}},{key:"createThumbnail",value:function(e,t){var i=x.createDom("xg-thumbnail","",{},"thumbnail ".concat(t));return e&&e.appendChild(i),i}}],[{key:"pluginName",get:function(){return"thumbnail"}},{key:"defaultConfig",get:function(){return{isShow:!1,urls:[],pic_num:0,col:0,row:0,height:90,width:160,scale:1,className:"",hidePortrait:!1}}}]),i}(ct);function Nn(e){return e?"background:".concat(e,";"):""}var Fn=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"onTimeupdate",(function(){var t=e.player.ended,i=u(e).offsetDuration,n=e.currentTime;n=x.adjustTimeByDuration(n,i,t),e.update({played:n},i)})),e}return r(i,[{key:"offsetDuration",get:function(){return this.playerConfig.customDuration||this.player.offsetDuration||this.player.duration}},{key:"currentTime",get:function(){var e=this.player,t=e.offsetCurrentTime,i=e.currentTime;return t>=0?t:i}},{key:"afterCreate",value:function(){var e=this;this.root&&(this.on(G,this.onTimeupdate),this.on(Q,(function(){e.reset()})))}},{key:"reset",value:function(){this.update({played:0,cached:0},0)}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{cached:0,played:0},t=arguments.length>1?arguments[1]:void 0;t&&this.root&&(e.cached&&(this.find("xg-mini-progress-cache").style.width="".concat(e.cached/t*100,"%")),e.played&&(this.find("xg-mini-progress-played").style.width="".concat(e.played/t*100,"%")))}},{key:"render",value:function(){var e=this.playerConfig,t=e.commonStyle;if(e.miniprogress){var i=this.config,n=i.mode,o=i.height,r={cached:Nn(t.cachedColor),played:Nn(t.playedColor),progress:Nn(t.progressColor),height:o>0&&2!==o?"height: ".concat(o,"px;"):""};return'<xg-mini-progress class="xg-mini-progress '.concat("show"===n?"xg-mini-progress-show":"",'" style="').concat(r.progress," ").concat(r.height,'">\n    <xg-mini-progress-cache class="xg-mini-progress-cache" style="').concat(r.cached,'"></xg-mini-progress-cache>\n    <xg-mini-progress-played class="xg-mini-progress-played" style="').concat(r.played,'"></xg-mini-progress-played>\n    </xg-mini-progress>')}}}],[{key:"pluginName",get:function(){return"MiniProgress"}},{key:"defaultConfig",get:function(){return{mode:"auto",height:2}}}]),i}(ct),Hn="realtime",Bn="firstframe",Un="poster";function Vn(){try{return parseInt(window.performance.now(),10)}catch(e){return(new Date).getTime()}}var Wn=null,Gn=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"onLoadedData",(function(t){e.player&&(e._frameCount=e.config.startFrameCount,e.stop(),e.renderOnTimeupdate(t),e.off(G,e.renderOnTimeupdate),e.on(G,e.renderOnTimeupdate))})),s(u(e),"onVisibilitychange",(function(t){"visible"===document.visibilityState?e._checkIfCanStart()&&e.start():"hidden"===document.visibilityState&&e.stop()})),s(u(e),"renderOnTimeupdate",(function(t){if(e._frameCount>0)e.renderOnce(),e._frameCount--;else{e._isLoaded=!0,e.off(G,e.renderOnTimeupdate);var i=e.config.startInterval;!e.player.paused&&e._checkIfCanStart()&&e.start(0,i)}})),s(u(e),"start",(function(t,i){var n=e.player.video,o=Vn(),r=e.checkVideoIsSupport(n);r&&e.canvasCtx&&(i||(i=e.interval),e.stop(),n.videoWidth&&n.videoHeight&&(e.videoPI=n.videoHeight>0?parseInt(n.videoWidth/n.videoHeight*100,10):0,(e.config.mode===Hn||o-e.preTime>=i)&&(n&&n.videoWidth&&e.update(r,e.videoPI),e.preTime=o)),e.frameId="timer"===e._loopType?x.setTimeout(u(e),e.start,i):x.requestAnimationFrame(e.start))})),s(u(e),"stop",(function(){e.frameId&&("timer"===e._loopType?x.clearTimeout(u(e),e.frameId):x.cancelAnimationFrame(e.frameId),e.frameId=null)})),e}return r(i,[{key:"afterCreate",value:function(){var e=this;!0===this.playerConfig.dynamicBg&&(this.config.disable=!1),i.isSupport||(this.config.disable=!0);var t=this.config,n=t.disable,o=t.mode,r=t.frameRate;n||(this._pos={width:0,height:0,rwidth:0,rheight:0,x:0,y:0,pi:0},this.isStart=!1,this._isLoaded=!1,this.videoPI=0,this.preTime=0,this.interval=parseInt(1e3/r,10),this.canvas=null,this.canvasCtx=null,this._frameCount=0,this._loopType=this.config.mode!==Hn&&this.interval>=1e3?"timer":"animation",this.once(se,(function(){e.player&&(e.init(),e.renderByPoster(),e.player.paused||e.start())})),o!==Un&&(o!==Bn&&(this.on(Q,(function(){e.stop()})),this.on(N,(function(){var t=e.config.startInterval;e._checkIfCanStart()&&e.start(0,t)})),this.on(B,(function(){e.stop()}))),this.on(X,this.onLoadedData),this.on($,(function(){e._isLoaded=!1,e.stop()})),document.addEventListener("visibilitychange",this.onVisibilitychange)))}},{key:"setConfig",value:function(e){var t=this;Object.keys(e).forEach((function(i){"root"===i&&e[i]!==t.config[i]?t.reRender(e[i]):"frameRate"===i?t.interval=parseInt(1e3/e[i],10):"disable"===i&&e[i]&&t.stop(),t.config[i]=e[i]}))}},{key:"init",value:function(e){var t=this.player,n=this.config;this.canvasFilter=i.supportCanvasFilter();try{var o=e||n.root;o||(o=n.isInnerRender&&t.innerContainer||t.root),o.insertAdjacentHTML("afterbegin",'<div class="xgplayer-dynamic-bg" data-index="'.concat(n.index,'"><canvas>\n        </canvas><xgmask></xgmask></div>')),this.root=o.children[0],this.canvas=this.find("canvas"),this.canvasFilter||(this.canvas.style.filter=n.filter,this.canvas.style.webkitFilter=n.filter),this.mask=this.find("xgmask"),n.addMask&&(this.mask.style.background=n.maskBg),this.canvasCtx=this.canvas.getContext("2d")}catch(r){w.logError("plugin:DynamicBg",r)}}},{key:"reRender",value:function(e){if(this.config.disable||this.root){this.stop();var t=this.root?this.root.parentElement:null;if(t!==e&&t.removeChild(this.root),e){this.init(e),this.renderOnce();var i=this.config.startInterval;this._checkIfCanStart()&&this.start(0,i)}else this.root=null}}},{key:"checkVideoIsSupport",value:function(e){if(!e)return null;var t=e&&e instanceof window.HTMLVideoElement?e:e.canvas?e.canvas:e.flyVideo?e.flyVideo:null;if(t&&("safari"!==O.browser||!x.isMSE(t)))return t;var i=t?t.tagName.toLowerCase():"";return"canvas"===i||"img"===i?t:null}},{key:"renderByPoster",value:function(){var e=this.playerConfig.poster;if(e){var t="String"===x.typeOf(e)?e:"String"===x.typeOf(e.poster)?e.poster:null;this.updateImg(t)}}},{key:"_checkIfCanStart",value:function(){var e=this.config.mode;return this._isLoaded&&!this.player.paused&&e!==Bn&&e!==Un}},{key:"renderOnce",value:function(){var e=this.player.video;if(e.videoWidth&&e.videoHeight){this.videoPI=parseInt(e.videoWidth/e.videoHeight*100,10);var t=this.checkVideoIsSupport(e);t&&this.update(t,this.videoPI)}}},{key:"updateImg",value:function(e){var t=this;if(e){var i=this.canvas.getBoundingClientRect(),n=i.width,o=i.height,r=new window.Image;r.onload=function(){if(t.canvas&&!t.frameId&&!t.isStart){t.canvas.height=o,t.canvas.width=n;var e=parseInt(n/o*100,10);t.update(r,e),r=null}},r.src=e}}},{key:"update",value:function(e,t){if(this.canvas&&this.canvasCtx&&t)try{var i=this._pos,n=this.config,o=this.canvas.getBoundingClientRect(),r=o.width,s=o.height;if(r!==i.width||s!==i.height||i.pi!==t){var a=parseInt(r/s*100,10);i.pi=t,i.width!==r&&(i.width=this.canvas.width=r),i.height!==s&&(i.height=this.canvas.height=s);var l=s,c=r;a<t?c=parseInt(s*t/100,10):a>t&&(l=parseInt(100*r/t,10)),i.rwidth=c*n.multiple,i.rheight=l*n.multiple,i.x=(r-i.rwidth)/2,i.y=(s-i.rheight)/2}this.canvasFilter&&(this.canvasCtx.filter=n.filter),this.canvasCtx.drawImage(e,i.x,i.y,i.rwidth,i.rheight)}catch(u){w.logError("plugin:DynamicBg",u)}}},{key:"destroy",value:function(){this.stop(),document.removeEventListener("visibilitychange",this.onVisibilitychange),this.canvasCtx=null,this.canvas=null}},{key:"render",value:function(){return""}}],[{key:"pluginName",get:function(){return"dynamicBg"}},{key:"defaultConfig",get:function(){return{isInnerRender:!1,disable:!0,index:-1,mode:"framerate",frameRate:10,filter:"blur(50px)",startFrameCount:2,startInterval:0,addMask:!0,multiple:1.2,maskBg:"rgba(0,0,0,0.7)"}}},{key:"isSupport",get:function(){return"boolean"==typeof Wn?Wn:Wn=function(){try{return!!document.createElement("canvas").getContext}catch(e){return!1}}()}},{key:"supportCanvasFilter",value:function(){return!("safari"===O.browser||"firefox"===O.browser)}}]),i}(ct),zn="info",Kn=Ae,qn=function(e){a(o,e);var i=d(o);function o(){var e;n(this,o);for(var r=arguments.length,a=new Array(r),l=0;l<r;l++)a[l]=arguments[l];return s(u(e=i.call.apply(i,[this].concat(a))),"_recordUserActions",(function(i){var n=e._getTime(),o=Object.assign({},i,{msg:i.msg||i.action});e._stats.info.push(t(t({type:"userAction"},n),{},{payload:o}))})),s(u(e),"_onReset",(function(){e.reset()})),s(u(e),"_recordInfo",(function(t){e.info(t)})),s(u(e),"_downloadStats",(function(){var t=e.getStats(),i=new Blob([JSON.stringify(t)],{type:"application/json"}),n=window.URL.createObjectURL(i),o=document.createElement("a");o.style.display="none",o.href=n,o.download="player.txt",o.disabled=!1,o.click()})),e}return r(o,[{key:"_getTime",value:function(){return{timestamp:Date.now(),timeFormat:(new Date).toISOString()}}},{key:"afterCreate",value:function(){this.reset(),this.on(Se,this._recordUserActions),this.on(Kn.STATS_INFO,this._recordInfo),this.on(Kn.STATS_DOWNLOAD,this._downloadStats),this.on(Kn.STATS_RESET,this._onReset)}},{key:"destroy",value:function(){this.offAll()}},{key:"downloadStats",value:function(){this._downloadStats()}},{key:"info",value:function(e){e.profile?this._infoProfile(e):this._info(e)}},{key:"_info",value:function(e){var i=this._getTime();this._stats.info.push(t(t({},i),{},{payload:e}))}},{key:"_infoProfile",value:function(e){if(e&&e.startMs){var i=t({cat:"function",dur:Date.now()-e.startMs,name:e.name||e.msg,ph:"X",pid:0,tid:0,ts:e.startMs,profile:!0},e);this._info(i)}else console.warn("infoProfile need object data, include startMs")}},{key:"reset",value:function(){var e;this._stats=(s(e={},zn,[]),s(e,"media",{}),e)}},{key:"getStats",value:function(){for(var e=this.player.media,t=[],i=0;i<e.buffered.length;i++)t.push({start:e.buffered.start(i),end:e.buffered.end(i)});var n={currentTime:e.currentTime,readyState:e.readyState,buffered:t,paused:e.paused,ended:e.ended};return this._stats.media=n,{raw:this._stats,timestat:this._getTimeStats(),profile:this._getProfile()}}},{key:"_getTimeStats",value:function(){return this._stats.info.map((function(e){var t=e.payload.data,i="";try{t instanceof Error?i=t.msg:void 0!==t&&(i=JSON.stringify(t))}catch(n){console.log("err",n)}return"[".concat(e.timeFormat,"] : ").concat(e.payload.msg," ").concat(i," ")}))}},{key:"_getProfile",value:function(){var e={traceEvents:[]};return this._stats.info.forEach((function(t){t.payload.profile&&e.traceEvents.push(t.payload)})),e}}],[{key:"pluginName",get:function(){return"stats"}},{key:"defaultConfig",get:function(){return{}}}]),o}($e),Yn=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"onGapJump",(function(){var t=u(e),n=t.player,o=t.config;if(n.media.readyState!==HTMLMediaElement.HAVE_NOTHING){if(n.media.seeking){if(!e.seekingEventReceived)return}else e.seekingEventReceived=!1;if(!n.media.paused||0===n.media.currentTime||!e.hasPlayed){var r=n.media.buffered,s=o.smallGapLimit||.5,a=o.gapDetectionThreshold||.3,l=n.media.currentTime,c=e._getIndex(r,l,a);if(null!==c&&0!==c){console.log("GapJump  bufferRange ",r.start(c),r.end(c));var h=r.start(c)+.1;if(!(h>n.media.duration)){var d=h-l,f=d<=s;d<i.BROWSER_GAP_TOLERANCE||f&&(!1!==o.useGapJump&&(n.media.currentTime=e.isSafari?h+.1:h),e.player&&e.player.emit("detectGap"),console.log("gapJump gapIndex",c," isGapSamll:",f," currentTime:",n.media.currentTime," jumpSize:",l-n.media.currentTime),.08!==h&&n&&n.emit("log",{type:"oneevent",end_type:"gap",vid:n.config.vid,ext:{video_postion:Math.floor(1e3*h)}}))}}}}})),e}return r(i,[{key:"afterCreate",value:function(){var e=this;!1!==this.config.useGapJump&&(this.hasPlayed=!1,this.seekingEventReceived=!1,this.isSafari=/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),this.on(z,this.onGapJump),this.on(N,(function(){e.hasPlayed=!0})),this.on(V,(function(){e.seekingEventReceived=!0})))}},{key:"_getIndex",value:function(e,t,i){if(!e||!e.length)return null;if(1===e.length&&e.end(0)-e.start(0)<1e-6)return null;for(var n=this._getBuffered(e),o=null,r=0;r<n.length;r++){if(n[r].start>t&&(0===r||n[r-1].end-t<=i)){o=r;break}}return o}},{key:"_getBuffered",value:function(e){if(!e)return[];for(var t=[],i=0;i<e.length;i++)t.push({start:e.start(i),end:e.end(i)});return t}}],[{key:"pluginName",get:function(){return"gapJump"}},{key:"defaultConfig",get:function(){return{useGapJump:!1,smallGapLimit:.5,gapDetectionThreshold:.3}}}]),i}(ct);Yn.BROWSER_GAP_TOLERANCE=.001;var Xn=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"onWaiting",(function(){var t=u(e).config;e.jumpCnt>t.jumpCntMax||e.timer||!1===t.useWaitingTimeoutJump||(e.timer=setTimeout(e.onJump,1e3*t.waitingTime))})),s(u(e),"onJump",(function(){var t=u(e),i=t.player,n=t.config;if(clearTimeout(e.timer),e.timer=null,!(e.jumpCnt>n.jumpCntMax||!1===n.useWaitingTimeoutJump||i.media.paused&&0!==i.media.currentTime&&e.hasPlayed)){e.jumpSize=n.jumpSize*(e.jumpCnt+1),e.jumpCnt===n.jumpSize&&e.jumpSize<6&&(e.jumpSize=6);var o=i.currentTime+e.jumpSize;o>i.media.duration||(console.log("waitintTimeout, currentTime:",i.currentTime,", jumpTo:",o),e.jumpCnt++,i.currentTime=o)}})),e}return r(i,[{key:"afterCreate",value:function(){var e=this,t=this.config,i=t.useWaitingTimeoutJump,n=t.jumpSize;!1!==i&&(this.hasPlayed=!1,this.jumpCnt=0,this.timer=null,this.jumpSize=n,this.on(z,this.onWaiting),this.on([F,K],(function(){clearTimeout(e.timer),e.timer=null,e.jumpSize=e.config.jumpSize})),this.on(N,(function(){e.hasPlayed=!0})))}}],[{key:"pluginName",get:function(){return"waitingTimeoutJump"}},{key:"defaultConfig",get:function(){return{useWaitingTimeoutJump:!1,waitingTime:15,jumpSize:2,jumpCntMax:4}}}]),i}(ct),Zn="cdn",Jn=["cdn"],$n=function(e){a(i,e);var t=d(i);function i(){var e;n(this,i);for(var o=arguments.length,r=new Array(o),a=0;a<o;a++)r[a]=arguments[a];return s(u(e=t.call.apply(t,[this].concat(r))),"getSpeed",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Zn;if(!e.speedListCache||!e.speedListCache[t])return 0;if(e.speedListCache[t].length<=0)return 0;var i=0;return e.speedListCache[t].map((function(e){i+=e})),Math.floor(i/e.speedListCache[t].length)})),s(u(e),"startTimer",(function(){x.isMSE(e.player.video)||(e.initSpeedList(),e.cnt=0,e.timer=setTimeout(e.testSpeed,e.config.testTimeStep))})),s(u(e),"initSpeedList",(function(){e.speedListCache={},Jn.forEach((function(t){e.speedListCache[t]=[]}))})),s(u(e),"_onRealSpeedChange",(function(t){t.speed&&e.appendList(t.speed,t.type||Zn)})),s(u(e),"testSpeed",(function(){if(clearTimeout(e.timer),e.timer=null,e.player&&e.config.openSpeed){var t=e.config,i=t.url,n=t.loadSize,o=t.testCnt,r=t.testTimeStep,s=i+(i.indexOf("?")<0?"?testst=":"&testst=")+Date.now();if(!(e.cnt>=o)){e.cnt++;try{var a=(new Date).getTime(),l=null,c=new XMLHttpRequest;e.xhr=c,c.open("GET",s);var u={},h=Math.floor(10*Math.random());u.Range="bytes="+h+"-"+(n+h),u&&Object.keys(u).forEach((function(e){c.setRequestHeader(e,u[e])})),c.onreadystatechange=function(){if(4===c.readyState){e.xhr=null,l=(new Date).getTime();var t=c.getResponseHeader("Content-Length")/1024*8,i=Math.round(1e3*t/(l-a));e.appendList(i),e.timer=setTimeout(e.testSpeed,r)}},c.send()}catch(d){console.error(d)}}}})),s(u(e),"appendList",(function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Zn;if(e.speedListCache&&e.speedListCache[i]){var n=e.config.saveSpeedMax;e.speedListCache[i].length>=n&&e.speedListCache[i].shift(),e.speedListCache[i].push(t);var o=u(e),r=o.player;r&&(i===Zn?r.realTimeSpeed=t:r[e.getSpeedName("realTime",i)]=t),e.updateSpeed(i)}})),s(u(e),"updateSpeed",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Zn,i=e.getSpeed(t),n=u(e),o=n.player;if(o)if(t===Zn)o.avgSpeed&&i===o.avgSpeed||(o.avgSpeed=i,o.emit(ue,{speed:i,realTimeSpeed:o.realTimeSpeed}));else{var r=e.getSpeedName("avg",t);o[r]&&i===o[r]||(o[r]=i,o.emit(ue,{speed:i,realTimeSpeed:o.realTimeSpeed}))}})),e}return r(i,[{key:"afterCreate",value:function(){var e=this.config,t=e.openSpeed,i=e.addSpeedTypeList;(null==i?void 0:i.length)>0&&Jn.push.apply(Jn,g(i)),this.initSpeedList(),this.on("real_time_speed",this._onRealSpeedChange),this.timer=null,this.cnt=0,this.xhr=null,t&&this.on([X,ae],this.startTimer)}},{key:"getSpeedName",value:function(e,t){return e+"Speed"+t.toUpperCase()}},{key:"openSpeed",get:function(){return this.config.openSpeed},set:function(e){if(this.config.openSpeed=e,!e&&this.timer)return clearTimeout(this.timer),void(this.timer=null);if(this.config.openSpeed){if(this.timer)return;this.timer=setTimeout(this.testSpeed,this.config.testTimeStep)}}},{key:"destroy",value:function(){var e=this;this.off("real_time_speed",this._onRealSpeedChange),this.off([X,ae],this.startTimer),Jn.forEach((function(t){e.speedListCache&&e.speedListCache[t]&&(e.speedListCache[t]=[])})),this.speedListCache&&(this.speedListCache={}),clearTimeout(this.timer),this.timer=null,this.xhr&&4!==this.xhr.readyState&&(this.xhr.cancel&&this.xhr.cancel(),this.xhr=null)}}],[{key:"pluginName",get:function(){return"testspeed"}},{key:"defaultConfig",get:function(){return{openSpeed:!1,testCnt:3,loadSize:204800,testTimeStep:3e3,url:"",saveSpeedMax:5,addSpeedTypeList:[]}}}]),i}(ct),Qn=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i,[{key:"afterCreate",value:function(){var e=this,t=this.player,i=this.config,n=t.media||t.video;(this.timer=null,this._lastDecodedFrames=0,this._currentStuckCount=0,this._lastCheckPoint=null,this._payload=[],i.disabled)||n.getVideoPlaybackQuality&&(this.on(N,(function(){e._startTick()})),this.on(B,(function(){e._stopTick()})),this.on(H,(function(){e._stopTick()})),this.on(Q,(function(){e._stopTick()})))}},{key:"_startTick",value:function(){var e=this;this._stopTick(),this._timer=setTimeout((function(){e._checkDecodeFPS(),e._startTick()}),this.config.tick)}},{key:"_stopTick",value:function(){clearTimeout(this._timer),this._timer=null}},{key:"_checkBuffer",value:function(e,t){for(var i=!1,n=[],o=0;o<t.length;o++){var r=t.start(o),s=t.end(o);if(n.push({start:r,end:s}),r<=e&&e<=s-1){i=!0;break}}return{enoughBuffer:i,buffers:n}}},{key:"_checkStuck",value:function(e,t,i,n){var o=this.player.media||this.player.video,r=document.hidden,s=o.paused,a=o.readyState,l=o.currentTime,c=o.buffered;if(!(r||s||a<4)){var u=this._checkBuffer(l,c),h=u.enoughBuffer,d=u.buffers;h&&(e<=this.config.reportFrame?(this._currentStuckCount++,this._payload.push({currentTime:l,buffers:d,curDecodedFrames:e,totalVideoFrames:t,droppedVideoFrames:i,checkInterval:n}),this._currentStuckCount>=this.config.stuckCount&&(this.emit(Oe,this._payload),this._reset())):this._reset())}}},{key:"_reset",value:function(){this._payload=[],this._currentStuckCount=0}},{key:"_checkDecodeFPS",value:function(){var e=this.player.media||this.player.video;if(e){var t=e.getVideoPlaybackQuality(),i=t.totalVideoFrames,n=t.droppedVideoFrames,o=performance.now();if(i&&this._lastCheckPoint){var r=i-this._lastDecodedFrames,s=o-this._lastCheckPoint;this._checkStuck(r,i,n,s)}this._lastDecodedFrames=i,this._lastCheckPoint=o}}},{key:"destroy",value:function(){this._stopTick()}}],[{key:"pluginName",get:function(){return"FpsDetect"}},{key:"defaultConfig",get:function(){return{disabled:!1,tick:1e3,stuckCount:3,reportFrame:0}}}]),i}(ct);Pt.use({LANG:"zh-cn",TEXT:{ERROR_TYPES:{network:{code:1,msg:"视频下载错误"},mse:{code:2,msg:"流追加错误"},parse:{code:3,msg:"解析错误"},format:{code:4,msg:"格式错误"},decoder:{code:5,msg:"解码错误"},runtime:{code:6,msg:"语法错误"},timeout:{code:7,msg:"播放超时"},other:{code:8,msg:"其他错误"}},HAVE_NOTHING:"没有关于音频/视频是否就绪的信息",HAVE_METADATA:"音频/视频的元数据已就绪",HAVE_CURRENT_DATA:"关于当前播放位置的数据是可用的，但没有足够的数据来播放下一帧/毫秒",HAVE_FUTURE_DATA:"当前及至少下一帧的数据是可用的",HAVE_ENOUGH_DATA:"可用数据足以开始播放",NETWORK_EMPTY:"音频/视频尚未初始化",NETWORK_IDLE:"音频/视频是活动的且已选取资源，但并未使用网络",NETWORK_LOADING:"浏览器正在下载数据",NETWORK_NO_SOURCE:"未找到音频/视频来源",MEDIA_ERR_ABORTED:"取回过程被用户中止",MEDIA_ERR_NETWORK:"网络错误",MEDIA_ERR_DECODE:"解码错误",MEDIA_ERR_SRC_NOT_SUPPORTED:"不支持的音频/视频格式",REPLAY:"重播",ERROR:"网络连接似乎出现了问题",PLAY_TIPS:"播放",PAUSE_TIPS:"暂停",PLAYNEXT_TIPS:"下一集",DOWNLOAD_TIPS:"下载",ROTATE_TIPS:"旋转",RELOAD_TIPS:"重新载入",FULLSCREEN_TIPS:"进入全屏",EXITFULLSCREEN_TIPS:"退出全屏",CSSFULLSCREEN_TIPS:"进入样式全屏",EXITCSSFULLSCREEN_TIPS:"退出样式全屏",TEXTTRACK:"字幕",PIP:"画中画",SCREENSHOT:"截图",LIVE:"正在直播",OFF:"关闭",OPEN:"开启",MINI_DRAG:"点击按住可拖动视频",MINISCREEN:"小屏幕",REFRESH_TIPS:"请试试",REFRESH:"刷新",FORWARD:"快进中",LIVE_TIP:"直播"}});var eo=r((function e(t,i){var o,r,s;n(this,e);var a=i&&"mobile"===i.isMobileSimulateMode,l=i.isLive,c=[].concat(l?[]:[Zi,Ui,Fn,Mn,Xi],[Wi,Yi,nn,hn,Tn,xn,gn,vn,en,cn]),u=[qt,Yt,Qt,Ni,ei,In,Ln,jn,fi];this.plugins=[qn,zt].concat(g(c),u,[Yn,Xn]);var h=a?"mobile":O.device;switch(h){case"pc":(o=this.plugins).push.apply(o,[Mi,vi,Pn,$n,Qn]);break;case"mobile":(r=this.plugins).push.apply(r,[Di]);break;default:(s=this.plugins).push.apply(s,[Mi,vi,Pn])}(O.os.isIpad||"pc"===h)&&this.plugins.push(Gn),O.os.isIpad&&this.plugins.push(vi),this.ignores=[],this.i18n=[]})),to=function(e){a(i,e);var t=d(i);function i(){return n(this,i),t.apply(this,arguments)}return r(i)}(jt);return s(to,"defaultPreset",eo),s(to,"Util",x),s(to,"Sniffer",O),s(to,"Errors",j),s(to,"Events",De),s(to,"Plugin",ct),s(to,"BasePlugin",$e),s(to,"I18N",Pt),s(to,"STATE_CLASS",ft),s(to,"InstManager",At),to}));
//# sourceMappingURL=index.min.js.map
