import type { Ref } from 'vue';

export interface UsePromiseModalOptions {

}

export interface UsePromiseModalRetrun<T, R> {
  isActive: Ref<boolean>;

  /**
   * 打开弹窗，返回一个 Promise
   * @param value
   */
  open: (value?: T) => Promise<R>;

  /**
   * 使open返回的Promise完成
   * @param value
   */
  completed: (value?: R | PromiseLike<R>) => void;

  /**
   * 强制关闭弹窗
   * @param reson 传递给 reject 的参数
   */
  forceClose: (reson?: any) => void;
}

/**
 * 使用 Promise Modal 的自定义 Hook
 *
 * @template T 传递给 effect 函数的参数类型，默认为 undefined
 * @template R Promise Modal 完成后返回的结果类型，默认为 undefined
 * @param effect 异步函数，接受一个参数并返回一个 Promise 或无返回（void）
 * @returns 包含 isActive、open、completed、forceClose 四个方法的对象
 */
export function usePromiseModal<R = void, T = any>(
  effect: (value?: T) => (Promise<void> | void),
): UsePromiseModalRetrun<T, R> {
  const isActive = ref(false);

  let resolve: undefined | ((value: R | PromiseLike<R>) => void);
  let reject: undefined | ((reason?: any) => void);

  async function open(value?: T) {
    await effect?.(value);
    forceClose();
    await nextTick();
    return new Promise<R>((_resolve, _reject) => {
      isActive.value = true;
      resolve = (value: R | PromiseLike<R>) => {
        _resolve?.(value);
        resolve = undefined;
        reject = undefined;
        isActive.value = false;
      };

      reject = (reason: any) => {
        _reject(reason);
        resolve = undefined;
        reject = undefined;
        isActive.value = false;
      };
    });
  }

  function completed(value?: R | PromiseLike<R>) {
    resolve?.(value as any);
  }

  function forceClose(reason?: any) {
    reject?.(reason);
  }

  onUnmounted(() => {
    forceClose();
  });
  whenever(() => !isActive.value, () => forceClose());

  return {
    isActive,
    open,
    completed,
    forceClose,
  };
}
