<script lang="ts" setup>
import { defaultStatic } from '@/lib/geovisearth/defaultStaticConfig';
import GroundSkyBox from '@/lib/geovisearth/GroundSkyBox';
import { useSettingStore } from '@/stores/setting';
import { CzEntity, gcj02ToWgs84 } from '@x3d/all';
import { useCzCamera, useCzDataSources, useCzEntities, useCzScene, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'UserSetting' });
const settingStore = useSettingStore();
const viewer = useCzViewer();
const camera = useCzCamera();
const scene = useCzScene();
let flag = true;
watchEffect(() => {
  // 设置title
  // 设置地图分辨率
  viewer.value.resolutionScale = settingStore.setting?.resolution || 1;
  // 设置光照效果
  viewer.value.scene.globe.enableLighting = settingStore.setting?.light === '1';
  viewer.value.shadows = settingStore.setting?.light === '1';
  // 设置大气效果
  scene.value.skyAtmosphere.show = settingStore.setting?.atmosphere === '1';
  // 设置全局主题色
  updateBackgroundColor(settingStore.setting?.themeColor ? settingStore.setting?.themeColor : '#292b2e'); // 调用时更新背景色
});
function updateBackgroundColor(newColor: string) {
  document.documentElement.style.setProperty('--global-bg-color', newColor);
}

// 设置默认行政区划
useCzDataSources(async () => {
  if (!settingStore.setting?.defaultUnit) {
    return [];
  }
  const geojson = settingStore.geojson;
  const bound = await Cesium.GeoJsonDataSource.load(geojson, {
    fill: Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0)'),
    stroke: Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0)'),
  });
  const [west, south, east, north] = turf.bbox(geojson);
  const rectangle = Cesium.Rectangle.fromDegrees(west - Math.abs(west - east) / 4, south - Math.abs(south - north) / 4, east + Math.abs(west - east) / 4, north + Math.abs(south - north) / 4);
  Cesium.Camera.DEFAULT_VIEW_FACTOR = 0;
  Cesium.Camera.DEFAULT_VIEW_RECTANGLE = rectangle;
  camera.value.flyHome();
  return [bound];
});
// 边界的线宽  线色
const entities = computedAsync(async () => {
  const geojson = settingStore.geojson;

  const entitiesPromises = geojson!.features[0].geometry.coordinates.map(async (item) => {
    const wgs84Pos = ref<any[]>([]);
    item[0].forEach((element: any) => {
      wgs84Pos.value?.push(gcj02ToWgs84(element));
    });
    wgs84Pos.value.flat().filter(value => value !== undefined);
    const entity = new CzEntity({
      polyline: {
        show: true,
        positions: Cesium.Cartesian3.fromDegreesArray(wgs84Pos.value.flat().filter(value => value !== undefined)),
        width: settingStore.setting?.strokeWidth ? Number(settingStore.setting?.strokeWidth) : 1,
        material: Cesium.Color.fromCssColorString(settingStore.setting?.stroke ? settingStore.setting?.stroke : 'rgba(255,255,0,1)').withAlpha(Number(settingStore.setting?.isShowUnitBorder)),
        arcType: Cesium.ArcType.GEODESIC,
        clampToGround: true,
      },
    });
    return entity;
  }) ?? [];
  return await Promise.all(entitiesPromises);
});

useCzEntities(entities);

// 切换远景和近景天空盒子
const handler = useThrottleFn(() => {
  const position = camera.value.positionCartographic;
  const [distantName, nearName] = settingStore.setting?.skyBox?.split('-') ?? [] as [string, string];
  defaultStatic.skyBox.near.find(e => e.name === nearName);
  if (!position.height || !distantName || !nearName)
    return;
  if (position.height >= 100000) {
    if (flag) {
      const sources = defaultStatic.skyBox.distant.find(e => e.name === distantName) as Omit<ISkyBoxSources, 'name'>;
      scene.value.skyBox = new Cesium.SkyBox({ sources });
      scene.value.skyAtmosphere.show = true;
      flag = false;
    }
  }
  else if (position.height < 100000) {
    if (!flag) {
      const sources = defaultStatic.skyBox.near.find(e => e.name === nearName) as Omit<ISkyBoxSources, 'name'>;
      scene.value.skyBox = new GroundSkyBox({ sources }) as Cesium.SkyBox;
      scene.value.skyAtmosphere.show = false;
      flag = true;
    }
  }
}, 1000);

// useCzEventListener(() => viewer.value.camera.changed, handler);
</script>

<template>
  <div />
</template>
