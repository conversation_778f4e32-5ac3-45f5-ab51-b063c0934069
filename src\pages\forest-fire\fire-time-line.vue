<!-- 火情 预警列表 -->
<script lang="ts" setup>
import type { ForestFireVoDuiXiang } from '@/genapi/analysis';

import dayjs from 'dayjs';

const props = defineProps<{ data: ForestFireVoDuiXiang[]; scrollToId?: string }>();
const emits = defineEmits<{ (event: 'clickItem', data: ForestFireVoDuiXiang): void }>();
defineComponent({ name: 'FireTimeLine' });
const timelineItemRef = shallowRef();
const scrollRef = shallowRef();
function handleItem(data) {
  emits('clickItem', data);
}
watch([() => props.scrollToId], async () => {
  await nextTick();
  setTimeout(() => {
    const index = props.data.findIndex(el => el.id === props.scrollToId);
    const { top } = timelineItemRef.value[index].$el.getBoundingClientRect();
    console.log('开始滚动', index, top);
    scrollRef.value.setScrollTop(top - 300);
  }, 3000);
});
</script>

<template>
  <el-scrollbar ref="scrollRef" class="fire-time-line" height="100%">
    <el-timeline pl="100px" pt="15px">
      <el-timeline-item
        v-for="stationData in data"
        :id="stationData?.id || ''"
        :key="stationData.id"
        ref="timelineItemRef"
        :class="{ active: stationData.id === scrollToId }"
        @click="handleItem(stationData)"
      >
        <div class="detail" flex="~" w="calc(100% - 100px)">
          <div font-size="22px" color="#CDD2D7" flex="~ col" align="center">
            <span>{{ dayjs(stationData?.createTime).format("MM-DD") }}</span>
            <span>{{ dayjs(stationData?.createTime).format("HH:mm:ss") }}</span>
          </div>
          <div pl="30px">
            <p font-size="22px" color="#C8EAFF" align="left" py="5px">
              <span mr="20px">{{ stationData?.stationName }}</span>
              <span mr="20px">火情事件编号{{ stationData.nfsid }}</span>
            </p>
            <p font-size="22px" color="#C8EAFF" align="left" py="5px">
              <span mr="20px">经度：{{ stationData.lng }}</span>
              <span mr="20px">纬度：{{ stationData.lat }}</span>
            </p>
            <el-row :gutter="20" m="20px 0" h="100px" overflow="hidden" items-top>
              <el-col :span="8" h="100%">
                <el-image
                  lazy
                  h="100%"
                  b="1px solid #415CA6"
                  :src="stationData.kjgFilePath"
                  :preview-src-list="[stationData?.kjgFilePath, stationData?.rcxFilePath]"
                  fit="cover"
                />
              </el-col>
              <el-col :span="8" h="100%">
                <el-image
                  lazy
                  h="100%"
                  b="1px solid "
                  :src="stationData.rcxFilePath"
                  :preview-src-list="[stationData.rcxFilePath, stationData.kjgFilePath]"
                  fit="cover"
                />
              </el-col>
              <el-col :span="8" h="100%">
                <video
                  :src="stationData.videoFilePath"
                  :poster="stationData.kjgFilePath"
                  preload="none"
                  h="100%"
                  style="object-fit: fill"
                  controls="controls"
                />
              </el-col>
            </el-row>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </el-scrollbar>
</template>

<style scoped lang="scss">
.fire-time-line {
  .el-timeline-item {
    padding-top: 20px;
    padding-right: 15px;
    padding-bottom: 5px;
    cursor: pointer;

    .detail {
      width: calc(100% - 100px);
    }

    &:hover,
    &.active {
      background: rgb(61 100 181 / 30%);
    }
  }
}
</style>
