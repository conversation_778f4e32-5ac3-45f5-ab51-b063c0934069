<!-- PathGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity, ParticleGraphics } from '@/lib/@geovis3d/core';

import type { ParticleGraphicsKey, ParticleGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import { ParticleGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import { useGraphicsBinding } from './hooks';

defineOptions({ name: 'PathGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: ParticleGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<ParticleGraphics, ParticleGraphicsSerializateJSON>({
  graphic: () => props.entity?.particle,
  omit: props.omit,
  toJSON: (graphics, omit) => ParticleGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => ParticleGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <el-form-item label="大小">
    <el-input-number
      v-model.number="model.endScale"
      class="input-mater"
      :min="0"
      :precision="0"
      step-strictly
      controls-position="right"
    />
  </el-form-item>

  <el-form-item label="可见性">
    <el-switch
      v-if="!hide?.includes('show')"
      v-model="model.show"
      graphics="particle"
      graphics-field="show"
    />
  </el-form-item>
  <el-form-item label="数量比率">
    <el-input-number
      v-model.number="model.emissionRate"
      class="input-rate"
      :min="0"
      :precision="0"
      step-strictly
      controls-position="right"
    />
  </el-form-item>
  <el-form-item label="粒子时长">
    <el-input-number
      v-model.number="model.lifetime"
      class="input-rate"
      :min="0"
      :precision="0"
      step-strictly
      controls-position="right"
    />
  </el-form-item>
</template>
