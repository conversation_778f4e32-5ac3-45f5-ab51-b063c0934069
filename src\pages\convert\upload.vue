<script setup lang="ts">
import type { ComponentSize } from 'element-plus';

const props = defineProps<{
  id: string;
}>();

const searchValue = ref('');

const currentPage = ref(4);
const pageSize = ref(100);
const size = ref<ComponentSize>('default');
const background = ref(false);
const disabled = ref(false);

function handleSizeChange(val: number) {
  console.log(`${val} items per page`);
}
function handleCurrentChange(val: number) {
  console.log(`current page: ${val}`);
}

/**
 * 搜索
 */
function handleSearch() {
  console.log('handleSearch');
}
/**
 * 新建转换
 */
function handleCreate() {
  console.log('handleCreate');
}
/**
 * 详情
 * @param row
 */
function handleDetail(row: any) {
  console.log('handleDetail', row);
}
/**
 * 发布
 * @param row
 */
function handlePublish(row: any) {
  console.log('handlePublish', row);
}
/**
 * 删除
 * @param row
 */
function handleDelete(row: any) {
  console.log('handleDelete', row);
}
</script>

<template>
  <div class="convert-title">
    倾斜摄影转换
  </div>
  <div class="search">
    <el-input v-model="searchValue" style="width: 350px" placeholder="请输入" @keyup.enter="handleSearch" />
    <el-button type="primary" @click="handleCreate">
      新建转换
    </el-button>
  </div>
  <div class="table-content">
    <el-table
      :data="[{
                rwmc: '倾斜摄影转换',
                ysj: '源数据格式',
                zhh: '转换后格式',
                zt: '状态',
                cjsj: '创建时间',
              },
              {
                rwmc: '倾斜摄影转换',
                ysj: '源数据格式',
                zhh: '转换后格式',
                zt: '状态',
                cjsj: '创建时间',
              },
              {
                rwmc: '倾斜摄影转换',
                ysj: '源数据格式',
                zhh: '转换后格式',
                zt: '状态',
                cjsj: '创建时间',
              },
      ]"
    >
      <el-table-column prop="rwmc" label="任务名称" />
      <el-table-column prop="ysj" label="源数据格式" />
      <el-table-column prop="zhh" label="转换后格式" />
      <el-table-column prop="zt" label="状态" />
      <el-table-column prop="cjsj" label="创建时间" />
      <el-table-column label="操作">
        <template #default="scope">
          <!-- 详情 发布 删除 -->
          <el-button type="primary" link @click="handleDetail(scope.row)">
            详情
          </el-button>
          <el-button type="primary" link @click="handlePublish(scope.row)">
            发布
          </el-button>
          <el-button type="primary" link @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination />
  </div>
  <div class="pagination">
    <el-pagination
      v-model:current-page="currentPage"
      :page-size="pageSize"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, prev, pager, next"
      :total="1000"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped lang="scss">
.convert-title {
  font-size: 20px;
  font-weight: 600;
  color: #ffffffd9;
}

.search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.table-content {
  margin-top: 20px;
}
</style>
