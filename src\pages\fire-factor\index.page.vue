<script lang="ts" setup>
import TimeSeriesLayerTool from '@/components/time-service-layer/time-service-layer-tool.vue';
import Left from './components/left.vue';
import Right from './components/right.vue';
import StationPopper from './components/station-popper.vue';
import { createFireFactorState } from './state';

defineOptions({ name: 'FireFactorIndexPage' });
definePage({
  meta: {
    title: '火险因子',
  },
});
createFireFactorState();
</script>

<template>
  <layout-scaffold>
    <template #left>
      <Left />
    </template>
    <StationPopper />
    <TimeSeriesLayerTool />
    <template #right>
      <Right />
    </template>
  </layout-scaffold>
</template>
