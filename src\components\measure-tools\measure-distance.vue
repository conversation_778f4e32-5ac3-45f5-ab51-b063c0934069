<!-- 距离测量 -->
<script lang="ts" setup>
import { CzPlotEntity, distance } from '@x3d/all';
import { useCzEntities, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'MeasureDistance' });
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    isActive?: boolean;
  }>(),
  { modelValue: true },
);

const viewer = useCzViewer();
const show = useVModel(props, 'modelValue');
const unit = ref(1);
const result = ref(0);
const plotEntity = shallowRef<CzPlotEntity>();
function initEntity() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      manualTerminate: entity => entity.record.positions.getLength() > 2,
      control: { visible: true },
      interval: { visible: 'loop' },
      // delete: { visible: true },
      effect(entity) {
        entity.polyline ??= new Cesium.PolylineGraphics({
          material: Cesium.Color.fromCssColorString('#FCC650'),
          width: 2,
        });
        const { record, controller } = entity;

        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 2 ? positions : [];
        entity.polyline.positions = new Cesium.CallbackProperty(() => cache, false);
        entity.position = new Cesium.ConstantPositionProperty(
          record.positions.getCenter()!,
        );
        positions.length > 1
        && distance(positions, {
          clampToGround: false,
          scene: viewer.value.scene,
        }).then((e) => {
          result.value = e.count;
          let text: string = '';

          if (e.count / 1000 > 10) {
            text = `${(e.count / 1000).toFixed(2)}km`;
          }
          else {
            text = `${(+e.count).toFixed(2)}m`;
          }
          entity.label = new Cesium.LabelGraphics({
            text,
            font: '16pt Source Han Sans CN',
            pixelOffset: new Cesium.Cartesian2(0, -20),
          });
        });
      },
    },
  });
}
useCzEntities(() => [plotEntity.value]);

initEntity();
defineExpose({ initEntity });
</script>

<template>
  <basic-card
    v-show="show"
    show-close
    title="量测设置"
    @close="show = false"
  >
    <div class="px-24px py-16px">
      <el-form>
        <el-form-item label="直线距离" class="items-center">
          <el-select v-model="unit">
            <el-option label=" 米 (m)" :value="1" />
            <el-option label=" 千米 (km)" :value="1000" />
          </el-select>
        </el-form-item>
      </el-form>
      <div text="#FCC650 14px" mt="12px" lh="24px">
        提示：左键在球上点击选点，单击鼠标右键结束
      </div>
    </div>
    <template #footer>
      <div flex="~ justify-between" class="my--8px w-full font-blod-18px">
        距离：
        <span> {{ (result / unit).toFixed(2) }}{{ unit === 1 ? "m" : "km" }} </span>
      </div>
    </template>
  </basic-card>
</template>

<style lang="scss" scoped>
.el-select {
  :deep() &__wrapper {
    min-height: 44px !important;
  }
}
</style>
