<!-- RectangleAttribute -->
<script lang="ts" setup>
import type { RectangleSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'RectangleAttribute' });

const props = defineProps<{
  modelValue?: RectangleSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: RectangleSerializateJSON): void;
}>();

const model = ref<RectangleSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div>
    <el-form-item :label="label" />
    <NumberAttribute v-model="model.west" label="西" :min="-180" :max="180" />
    <NumberAttribute v-model="model.south" label="南" :min="model.north" :max="180" />
    <NumberAttribute v-model="model.east" label="东" :min="-180" :max="180" />
    <NumberAttribute v-model="model.north" label="北" :min="-180" :max="model.south" />
  </div>
</template>
