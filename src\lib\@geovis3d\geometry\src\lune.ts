import type { Coord, Feature, LineString } from '@turf/turf';

import { getCoord, lineString } from '@turf/turf';
import {
  getArcPoints,
  getAzimuth,
  getCircleCenterOfThreePoints,
  isClockWise,
  mathDistance,
} from './common';

/**
 * 弓形
 */
export function lune(points: Coord[]): Feature<LineString> {
  if (points.length < 3) {
    throw new Error('points.length must >=3');
  }

  let coords = points.map(e => getCoord(e));

  const coord1 = coords[0];
  const coord2 = coords[1];
  const coord3 = coords[2];
  let startAngle;
  let endAngle;

  const center = getCircleCenterOfThreePoints(coord1, coord2, coord3);

  const radius = mathDistance(coord1, center);
  const angle1 = getAzimuth(coord1, center);
  const angle2 = getAzimuth(coord2, center);
  if (isClockWise(coord1, coord2, coord3)) {
    startAngle = angle2;
    endAngle = angle1;
  }
  else {
    startAngle = angle1;
    endAngle = angle2;
  }
  coords = getArcPoints(center, radius, startAngle, endAngle).map(e => e.geometry.coordinates);
  coords.push(coords[0]);
  return lineString(coords);
}
