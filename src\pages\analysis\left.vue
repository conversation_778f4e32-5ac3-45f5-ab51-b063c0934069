<script lang="ts" setup>
import { checked, menuList } from './config';

defineOptions({ name: 'Left' });

const router = useRouter();

const show = ref(true);
function handleItem(key: string, component?: Component) {
  checked.clear();
  checked.set(key, component!);
}
</script>

<template>
  <layout-left-panel p="b-46px t-64px" position="relative" bg="[var(--el-bg-color)]!">
    <header-title1 v-show="show" b-b="1px! solid #fff/10%!">
      分析
      <template #extra>
        <el-button link>
          <el-icon class="i-material-symbols:close" text="20px! #FFF!" @click="router.push({ path: '/home' })" />
        </el-button>
      </template>
    </header-title1>
    <el-scrollbar wrap-class="px-16px py-12px" :w="show ? '275px' : '0'" transition="all 300" h="auto" flex="1">
      <el-menu :default-openeds="menuList.map((e) => e.name)">
        <el-sub-menu v-for="item in menuList" :key="item.name" :index="item.name">
          <template #title>
            <component :is="item.icon" v-if="item.icon" text="20px! #FFF!" />
            {{ item.name }}
          </template>
          <el-row mt="4px" mb="12px">
            <template v-for="child in item.children" :key="child.name">
              <el-col v-if="child.load" :span="12">
                <el-menu-item
                  px="10px!"
                  @click="
                    handleItem(child.name, child.component)
                  "
                >
                  <component :is="child.icon" v-if="child.icon" text="20px! #FFF!" />
                  <span text="14px!">{{ child.name }}</span>
                </el-menu-item>
              </el-col>
            </template>
          </el-row>
        </el-sub-menu>
      </el-menu>
    </el-scrollbar>

    <el-button link class="toggle-btn" @click="show = !show">
      <el-icon
        class="i-material-symbols:chevron-left"
        text="18px! #FFF!"
        transition="all 300"
        :rotate="show ? '0' : '180'"
        inline-block
      />
    </el-button>
  </layout-left-panel>
</template>

<style lang="scss" scoped>
.toggle-btn {
  position: absolute;
  top: 521px;
  left: 100%;
  z-index: 99;
  width: 24px;
  height: 56px;
  background: var(--el-bg-color) !important; // #292b2e ;
  border-radius: 0 4px 4px 0 !important;
}

.el-menu {
  :deep() .el-sub-menu {
    &__title {
      padding: 12px !important;
    }
  }
}
</style>
