import type { Feature } from 'geojson';

import type { ContourDataSourceLoadOptions } from './contour-data-source';
import * as turf from '@turf/turf';

// eslint-disable-next-line no-restricted-globals
self.addEventListener('message', (event) => {
  try {
    const data = contourCalc(event.data);
    // eslint-disable-next-line no-restricted-globals
    self.postMessage(data);
  }
  catch (error) {
    console.error(error);
  }
}, { once: true });

function contourCalc(options: ContourDataSourceLoadOptions) {
  const { interpolate, isobands, surface, simplify } = options;
  const surfaceGeometry = simplify ? turf.simplify(surface, simplify) : surface;

  const points = turf.clone(interpolate.points);

  if (surfaceGeometry) {
    // // 取出边界外接矩形的四个点，向这四个点中添加属性0，以确保后续插值计算中，形成的插值矩形覆盖整个边界
    const bbox = surfaceGeometry?.bbox ?? turf.bbox(surfaceGeometry);
    turf.coordAll(turf.bboxPolygon(bbox)).forEach((position) => {
      const bboxPoint = turf.point(position, { [interpolate.options?.property || 'value']: 0 });
      points.features.push(bboxPoint);
    });
  }
  const pointGrid = turf.interpolate(
    points,
    interpolate.cellSize,
    {
      ...interpolate.options,
      property: interpolate.options?.property || 'value',
      gridType: 'point',
    },
  );

  const bands = turf.isobands(
    pointGrid,
    isobands.breaks,
    {
      zProperty: 'value',
      ...isobands.options,
    },
  );

  if (!surfaceGeometry) {
    return bands;
  }
  else {
    const _surface = turf.flatten(surfaceGeometry);
    // const _bands = turf.polygonSmooth(turf.flatten(bands), { iterations: 5 });
    const _bands = turf.flatten(bands);
    const features = _bands.features.map((bandsFeature) => {
      return _surface.features.map((surfaceFeature) => {
        let intersection: Feature | null = null;
        try {
          intersection = turf.intersect(turf.featureCollection([surfaceFeature, bandsFeature]));
        }
        catch (error) {
          console.warn(error);
          const _bandsFeature = turf.buffer(bandsFeature, 0);
          intersection = turf.intersect(turf.featureCollection([surfaceFeature, _bandsFeature!]));
        }
        if (intersection) {
          intersection.properties = bandsFeature.properties;
        }
        return intersection;
      });
    }).flat().filter(e => !!e) as Feature[];
    return turf.featureCollection(features);
  }
}
