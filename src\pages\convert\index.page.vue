<script lang="ts" setup>
import { Back } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import Create from './create.vue';
import Details from './details.vue';

defineOptions({ name: 'ConvertIndexPage' });
definePage({
  meta: {
    title: '数据转换',
    sort: 6,
  },
});

const currentTab = ref('details');
provide('currentTab', currentTab);
const router = useRouter();
function back() {
  router.push('/');
}
</script>

<template>
  <div class="convert-container">
    <div class="convert-header">
      <div @click="back">
        <el-icon size="20">
          <Back />
        </el-icon>
        <span>返回主页</span>
      </div>
    </div>
    <div class="convert-main">
      <div class="convert-left">
        数据转换
        <p class="mx-10px my20px cursor-pointer indent-10px text-16px color-#ffffffD9">
          倾斜摄影转换
        </p>
      </div>
      <div class="convert-content">
        <template v-if="currentTab === 'details'">
          <Details />
        </template>
        <template v-if="currentTab === 'create'">
          <Create />
        </template>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.convert-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100vw;
  height: 100vh;
  pointer-events: all;
  background: #1c1d1e;

  .convert-header {
    display: flex;
    align-items: center;
    height: 64px;
    padding-left: 40px;
    font-size: 24px;
    background: var(--el-bg-color); // #292b2e;

    > div {
      display: flex;
      align-items: center;
      height: 100%;
      cursor: pointer;
      transition: all 0.3s;

      > span {
        margin: 0 15px;
      }

      &:hover {
        color: #4584ff;
      }
    }
  }

  .convert-main {
    display: flex;
    height: calc(100vh - 64px);

    .convert-left {
      box-sizing: border-box;
      width: 300px;
      padding: 25px;
      border-right: 1px solid #2f3238;
    }

    .convert-content {
      box-sizing: border-box;
      width: calc(100% - 300px);
    }
  }
}
</style>
