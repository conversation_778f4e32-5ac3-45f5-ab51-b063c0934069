import type { GcEntityConstructorOptions } from '../vector';

import type { PlottingControllerConstructorOptions } from './plotting-controller';

import { GcEntity } from '../vector';
import { PlottingController } from './plotting-controller';

/**
 * 标绘entity配置
 */
export interface PlottingEntityConstructorOptions extends GcEntityConstructorOptions {
  /**
   * 标绘控制器
   */
  plotting?: PlottingControllerConstructorOptions;
}

/**
 * 可标绘的entity
 *
 * entity在挂载后开始执行标绘
 */
export class PlottingEntity extends GcEntity {
  constructor(options?: PlottingEntityConstructorOptions) {
    const { plotting, ...superOptions } = options ?? {};
    super(superOptions);
    this._plotting = new PlottingController(this, plotting);
  }

  get owner() {
    return this.entityCollection?.owner;
  }

  /**
   * @internal
   */
  private _plotting: PlottingController;

  /**
   * 标绘控制器
   */
  get plotting(): PlottingController {
    return this._plotting;
  }
}
