<!-- CesiumPlotLayers -->
<script lang="ts" setup>
import type { UploadFile } from 'element-plus';
import type { GlobalComponents } from 'vue';

import type { PlotSimpleLayer } from './types';
import { refThrottled } from '@vueuse/core';
import { computed } from 'vue';

import { usePlotInjectState } from './state';

defineOptions({ name: 'CesiumPlotLayers' });
const {
  deleteLayer,
  editLayer,
  plotLayerList,
  importLayer,
  exportLayer,
} = usePlotInjectState()!;

const keyword = refThrottled(ref(''), 300, true, false);

const list = computed(() => {
  return keyword.value
    ? plotLayerList.value?.filter(e => e.remark?.includes(keyword.value))
    : plotLayerList.value;
});

async function remove(item: PlotSimpleLayer) {
  if (item.id) {
    await ElMessageBox.confirm(`删除后"${item?.remark}"不可恢复!`, '确认删除？', {
      type: 'error',
    });
    await deleteLayer(item.id);
  }
}

const uploadRef = shallowRef<InstanceType<GlobalComponents['ElUpload']>>();

// 导入
async function importJSON(data: UploadFile) {
  try {
    const text = await data.raw?.text();
    importLayer(text ?? '');
  }
  catch {
    ElMessage.error('解析失败');
  }
  uploadRef.value?.clearFiles();
}

const VITE_SHOW_TAMPER = import.meta.env.VITE_SHOW_TAMPER;
</script>

<template>
  <el-input
    v-model="keyword"
    class="my-20px h-42px px-24px"
    clearable
    placeholder="搜索标绘图层"
  />

  <el-scrollbar flex="1">
    <div v-for="item in list" :key="item?.id" class="list-item" position="relative" m="x-25px">
      <!-- 防篡改标识 -->
      <div
        v-if="+VITE_SHOW_TAMPER"
        size="12px"
        rd="6px"
        :bg="!item.macVerify ? 'yellow-6' : 'green-6'"
        m="r-4px"
      />
      <span class="title"> {{ item?.remark }}</span>
      <el-button link title="下载" @click.stop="exportLayer(item.id!)">
        <el-icon class="i-material-symbols:download" text="20px!" />
      </el-button>
      <el-button link title="编辑" @click.stop="editLayer(item.id)">
        <el-icon class="i-material-symbols:edit" text="20px!" />
      </el-button>
      <el-button link title="删除" @click.stop="remove(item)">
        <el-icon class="i-material-symbols:delete" text="20px!" />
      </el-button>
    </div>
  </el-scrollbar>
  <div class="button-group" px-24px>
    <el-button type="primary" @click="editLayer()">
      <el-icon class="i-material-symbols:add-rounded" text="20px!" />
      新建标绘
    </el-button>
    <ElUpload
      ref="uploadRef"
      :limit="1"
      :on-change="importJSON"
      :auto-upload="false"
      :show-file-list="false"
      accept="application/json"
    >
      <template #trigger>
        <el-button type="primary" ml="15px">
          <el-icon class="i-material-symbols:upload-rounded" text="20px!" />
          导入标绘
        </el-button>
      </template>
    </ElUpload>
  </div>
</template>

<style scoped lang="scss">
.button-group {
  display: flex;
  align-items: center;
  justify-content: end;
  margin: 16px 0 0;
}

.el-scrollbar {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 400px;
  overflow: hidden;
}

.list-item {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 8px;
  margin-bottom: 10px;
  background: rgb(28 29 30);
  border-radius: 4px;

  &.active,
  &:hover {
    background: rgb(53 54 54 / 80%);
  }

  .el-checkbox {
    flex: 1;
    color: #fff;
  }

  .title {
    flex: 1;
    font-size: 14px;
    color: #fff;
    cursor: default;
  }

  .icon {
    display: none;
    padding: 2px 4px;
    cursor: pointer;
    background-size: 100% 100%;
  }

  &:hover {
    .icon {
      display: inline-block;
    }
  }
}
</style>
