import type { components, SceneInfoSaveOrUpdateSceneInfoUsingPost } from '@/genapi/cimapi';
import type { ShallowRef } from 'vue-demi';
import type { PlotDetailLayer } from './types';

import { downloadTrigger, PlottingDataSource } from '@/lib/@geovis3d/core';
import { usePlottingDataSourceHelper } from '@/lib/@geovis3d/vue-plotting-form';
import { computedAsync, createInjectionState, promiseTimeout } from '@vueuse/core';
import { useCzDataSourceCollection } from '@x3d/vue-hooks';
import * as Cesium from 'cesium';

import { nextTick, ref, shallowReadonly, shallowRef, watch } from 'vue-demi';
import readDirURL from './assets/readDir.json?url';
import { resolveSource } from './resolve-source';
import treeList from './tree';

export const [usePlotProvideState, usePlotInjectState] = createInjectionState(
  (
    parentElRef: ShallowRef<HTMLElement | undefined>,
    baseURL: string,
    appCode: string,
    assetURL: string,
    viewer?: Cesium.Viewer,
  ) => {
    const dataSourceEffect = useCzDataSourceCollection(viewer?.dataSources);

    // 图层列表
    const plotLayerList = shallowRef<components['schemas']['TbMessageDuiXiang'][]>();
    async function refreshLayerList() {
      const { data } = await cimApi.sceneInfoListPageUsingPost({
        data: {
          query: {},
          current: 1,
          size: 100,
        },
      });
      plotLayerList.value = data?.records ?? [];
    }

    refreshLayerList();

    // 标绘功能树类型列表
    const plotTreeList = computedAsync(async () => {
      const { data } = await axios.get(readDirURL);
      const tree = resolveSource(data!, assetURL);
      return [...treeList, ...tree];
    });

    // 选中的标绘图层ID
    const activeLayerIds = ref<string[]>([]);

    // 当前正在进行中的标绘图层
    const currentLayer = ref<PlotDetailLayer>();

    // 当前正在编辑的DataSource
    const dataSource = shallowRef<PlottingDataSource>();

    // 当前图层变化时，挂载或卸载
    watch(dataSource, async (value, prevValue) => {
      prevValue && dataSourceEffect.remove(prevValue);
      value && await dataSourceEffect.add(value);

      if (value) {
        await promiseTimeout(100);
        viewer?.flyTo(value, {
          duration: 1,
          offset: new Cesium.HeadingPitchRange(0, -90, 0),
        });
      }
    });

    const { loadJSON, toJSON, active, setActive, plottings }
      = usePlottingDataSourceHelper(dataSource);

    // 当前图层数据变化时，解析图层entityJSON数据
    watch(
      currentLayer,
      async (currentLayer) => {
        if (!currentLayer) {
          dataSource.value = undefined;
        }
        else {
          dataSource.value = new PlottingDataSource(currentLayer.remark);
          dataSource.value.id = currentLayer.id;
          await nextTick();
          const json = JSON.parse(currentLayer.value || '{}');

          loadJSON(json);
        }
      },
      {
        deep: false,
      },
    );

    watch(
      () => currentLayer.value?.remark,
      (name) => {
        dataSource.value && (dataSource.value.name = name ?? '');
      },
    );

    // 图层进入编辑,不传id则为新增
    const editLayer = async (id?: string, value?: any) => {
      let detail: PlotDetailLayer = {};
      if (id) {
        const { data } = await cimApi.sceneInfoGetSceneInfoIdUsingGet({ path: { id } });
        detail = data ?? {};
      }

      detail.remark ||= `新建图层 ${(plotLayerList.value?.length ?? 0) + 1}`;
      detail.key ||= Cesium.createGuid().slice(0, 16);
      detail.value ||= value;
      detail.value ||= '{}';
      currentLayer.value = detail;
    };

    // 保存图层
    const saveLayer = async () => {
      const entitiesJSON = toJSON();

      const detail: SceneInfoSaveOrUpdateSceneInfoUsingPost.Body = {
        id: '',
        remark: dataSource.value!.name,
        key: Cesium.createGuid().slice(0, 16),
        value: JSON.stringify(entitiesJSON),
      };
      dataSource.value!.id && (detail.id = dataSource.value!.id);
      // await plotApi.saveOrUpdatePlotLayer(detail);
      await cimApi.sceneInfoSaveOrUpdateSceneInfoUsingPost({ data: detail });
      refreshLayerList();
    };

    // 移除图层
    const deleteLayer = async (id: string) => {
      // await plotApi.deletePlotLayerById(id!);
      await cimApi.sceneInfoDeleteByIdIdUsingGet({ path: { id: id! } });
      await refreshLayerList();
    };

    // 导出图层
    const exportLayer = async (id: string) => {
      const data = await cimApi.sceneInfoExportJsonIdUsingGet({ path: { id: id! } }).then(res => res as PlotDetailLayer);
      downloadTrigger(new Blob([data.value ?? '']), `${data?.remark}.json`);
    };

    // 导入图层
    async function importLayer(text: string) {
      const json: any = JSON.parse(text!);
      editLayer(undefined, JSON.stringify(json));
    }

    return {
      activeLayerIds,
      plotLayerList,
      refreshLayerList,
      plotTreeList,
      currentLayer,
      dataSource,
      plottings,
      active,
      setActive,
      editLayer,
      saveLayer,
      deleteLayer,
      exportLayer,
      importLayer,
      parentElRef: shallowReadonly(parentElRef),
    };
  },
);
