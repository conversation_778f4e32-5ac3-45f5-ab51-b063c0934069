<!-- 底部工具栏 -->
<script lang="ts" setup>
import { useNavigation } from '@/hooks/use-navigation';
import LayerTheme from '@/widget/layer/layer-theme.vue';
import EmergencyResource from '@/widget/resource/emergency-resource.vue';
import CesiumScreenshotTool from '@/widget/screenshot-tool/cesium-screenshot-tool.vue';
import { useCzCamera, useCzScene } from '@x3d/vue-hooks';

defineOptions({ name: 'BottomNav' });
const showCompass = ref(false);
const showAlpha = ref(false);
const showMeasure = ref(false);
const navigation = useNavigation();
const sceneMode = ref(3);
const compassRef = ref<HTMLDivElement>();
const scene = useCzScene();
const camera = useCzCamera();
const { isFullscreen, toggle } = useFullscreen(document.body, {
  autoExit: true,
});
// 放大;
function zoomIn() {
  const minZoom = scene.value.screenSpaceCameraController.minimumZoomDistance;
  const position = camera.value.positionCartographic;
  const moveRate = position.height / 10.0;
  minZoom <= position.height && camera.value.moveForward(moveRate);
}
// 缩小
function zoomOut() {
  const maxZoom = scene.value.screenSpaceCameraController.maximumZoomDistance;
  const position = camera.value.positionCartographic;
  const moveRate = position.height / 10.0;
  maxZoom >= position.height && camera.value.moveBackward(moveRate);
}
watch(showCompass, (bool) => {
  bool && camera.value?.flyTo({
    destination: camera.value.computeViewRectangle() || Cesium.Rectangle.MAX_VALUE,
    orientation: {
      heading: 0,
    },
  });
});
onMounted(() => {
  compassRef.value && navigation.append('compass', compassRef.value);
});

const screenshotToolRef = templateRef('screenshotToolRef');

const VITE_ENV = import.meta.env.VITE_ENV;

const showEmergencyResource = ref(false);
</script>

<template>
  <div class="bottom-nav">
    <div position="fixed" right="24px" class="right-btn">
      <template v-if="VITE_ENV === 'HAINAN'">
        <el-button px="7px!">
          <el-icon
            class="i-tabler:database-share"
            text="24px!"
            @click="showEmergencyResource = true"
          />
        </el-button>
        <EmergencyResource v-if="showEmergencyResource" @close="showEmergencyResource = false" />
      </template>

      <el-button px="7px!">
        <el-icon
          class="i-material-symbols:camera"
          text="24px!"
          @click="screenshotToolRef?.open()"
        />
      </el-button>
      <CesiumScreenshotTool ref="screenshotToolRef" />
      <LayerTheme v-if="scene" v-model="showAlpha">
        <el-button
          class="tile-alpha mx-12px px-2px!"
          title="专题图制作"
          :class="{ active: showAlpha }"
          @click="showAlpha = !showAlpha"
        >
          <el-icon class="i-material-symbols:invert-colors-rounded px-17px! text-24px!" />
        </el-button>
      </LayerTheme>

      <el-button px="7px!" :class="{ active: showMeasure }">
        <el-icon class="i-custom:measure-distance" @click="showMeasure = !showMeasure" />
      </el-button>
      <el-button px="7px!">
        <el-icon
          :class="isFullscreen ? 'i-custom:zoom-screen' : 'i-custom:full-screen'"
          text="24px!"
          @click="toggle"
        />
      </el-button>
      <el-dropdown ml="12px" placement="top" :teleported="false">
        <el-button px="7px!" title="切换场景模式">
          <el-icon v-if="sceneMode === 2" class="i-custom:2d" />
          <el-icon v-if="sceneMode === 2.5" class="i-custom:25d" />
          <el-icon v-if="sceneMode === 3" class="i-custom:3d" />
        </el-button>
        <template #dropdown>
          <div flex="~ col justify-center">
            <el-button
              v-if="sceneMode !== 2"
              class="m-5px! px-7px!"
              @click="(sceneMode = 2), scene.morphTo2D()"
            >
              <el-icon class="i-custom:2d" />
            </el-button>
            <el-button
              v-if="sceneMode !== 2.5"
              class="m-5px! px-7px!"
              @click="(sceneMode = 2.5), scene.morphToColumbusView()"
            >
              <el-icon class="i-custom:25d" />
            </el-button>
            <el-button
              v-if="sceneMode !== 3"
              class="m-5px! px-7px!"
              @click="(sceneMode = 3), scene.morphTo3D()"
            >
              <el-icon class="i-custom:3d" />
            </el-button>
          </div>
        </template>
      </el-dropdown>

      <el-popover
        mx="12px"
        placement="top"
        trigger="click"
        :teleported="false"
        :show-arrow="false"
        :visible="showCompass"
      >
        <template #reference>
          <el-button
            class="compass-btn mx-12px px-2px!"
            title="指南针"
            @click="showCompass = !showCompass"
          >
            <el-icon class="i-custom:compass" text="34px!" />
          </el-button>
        </template>
        <div
          ref="compassRef"
          class="position-relative mx-auto h-85px w-100px overflow-hidden"
        />
      </el-popover>
      <el-button px="7px!" @click="camera.flyHome(1)">
        <el-icon class="i-custom:home" />
      </el-button>
      <el-button-group ml="12px">
        <el-button class="h-30px! b-0! rd-0! p-0!" @click="zoomIn">
          <el-icon class="i-custom:add-circle" mr="8px" />
        </el-button>
        <el-button class="h-30px! b-0! rd-0! p-0!" @click="zoomOut">
          <el-icon class="i-custom:sub-circle" ml="8px" />
        </el-button>
      </el-button-group>
    </div>
  </div>
  <measure-tools v-if="showMeasure" />
</template>

<style scoped lang="scss">
.bottom-nav {
  position: absolute;
  right: 0;
  bottom: 32px;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: end;
  justify-content: space-between;
  padding: 24px;
  font-size: 24px;
  pointer-events: none !important;

  .el-button,
  .el-button-group {
    height: 40px;
    padding: 6px 12px;
    font-size: 24px;
    color: #fff;
    pointer-events: auto;
    background: var(--el-bg-color);
    border-radius: 8px;

    &.active {
      background-color: rgb(29 30 32 / 100%);
      border-color: var(--el-button-active-border-color);
      box-shadow: 0 0 2px var(--el-button-active-border-color);
    }
  }

  .el-dropdown {
    --el-bg-color-overlay: tranparent;
    --el-border-color-light: tranparent;
    --el-box-shadow-light: tranparent;

    pointer-events: auto;
  }

  :deep() .compass-btn + .el-popover {
    --el-popover-bg-color: tranparent;
    --el-popover-border-color: tranparent;
    --el-box-shadow-light: none;
  }
}
</style>
