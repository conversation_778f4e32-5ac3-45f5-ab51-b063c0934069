import type { Enti<PERSON>, Viewer } from 'cesium';
import { cartographicToWgs84 } from '@x3d/core';

import {
  Cartesian3,
  Cartographic,
  Color,
  Math as CzMath,
  PolylineArrowMaterialProperty,
  Rectangle,
  sampleTerrainMostDetailed,
} from 'cesium';

// 绘制小矩形面 四个经纬度的点，z值高度可以忽略 如：113.xx  ,37.xx,0 ,113.xx,37.xx,0
export async function slopeAnalysis(viewer: Viewer, rectangle: Rectangle) {
  const [startx1, starty1] = cartographicToWgs84(Rectangle.southwest(rectangle));
  const [startx3, starty3] = cartographicToWgs84(Rectangle.northeast(rectangle));
  const [startx2, starty2] = cartographicToWgs84(Rectangle.southeast(rectangle));
  const [startx4, starty4] = cartographicToWgs84(Rectangle.northwest(rectangle));

  const startz1 = 0;
  const startz2 = 0;
  const startz3 = 0;
  const startz4 = 0;
  // 高度z全为0
  const count = 30;
  const slopelineposition: Cartographic[] = [];
  const hireacys: Cartesian3[] = [];

  const entities: Entity[] = [];
  for (let j = 0; j < count; j++) {
    for (let i = 0; i < count; i++) {
      const hireacy: Cartesian3[] = [];
      // 分割成小面，切分经纬度
      hireacy.push(
        new Cartesian3(
          startx1
          + ((startx2 - startx1) / count) * i
          + ((startx4 + ((startx3 - startx4) / count) * i - startx1 - ((startx2 - startx1) / count) * i) / count) * j,
          starty1
          + ((starty2 - starty1) / count) * i
          + ((starty4 + ((starty3 - starty4) / count) * i - starty1 - ((starty2 - starty1) / count) * i) / count) * j,
          startz1
          + ((startz2 - startz1) / count) * i
          + ((startz4 + ((startz3 - startz4) / count) * i - startz1 - ((startz2 - startz1) / count) * i) / count) * j,
        ),
      );
      hireacy.push(
        new Cartesian3(
          startx1
          + ((startx2 - startx1) / count) * (i + 1)
          + ((startx4 + ((startx3 - startx4) / count) * (i + 1) - startx1 - ((startx2 - startx1) / count) * (i + 1))
            / count)
          * j,
          starty1
          + ((starty2 - starty1) / count) * (i + 1)
          + ((starty4 + ((starty3 - starty4) / count) * (i + 1) - starty1 - ((starty2 - starty1) / count) * (i + 1))
            / count)
          * j,
          startz1
          + ((startz2 - startz1) / count) * (i + 1)
          + ((startz4 + ((startz3 - startz4) / count) * (i + 1) - startz1 - ((startz2 - startz1) / count) * (i + 1))
            / count)
          * j,
        ),
      );
      hireacy.push(
        new Cartesian3(
          startx4
          + ((startx3 - startx4) / count) * (i + 1)
          - ((startx4 + ((startx3 - startx4) / count) * (i + 1) - startx1 - ((startx2 - startx1) / count) * (i + 1))
            / count)
          * (count - 1 - j),
          starty4
          + ((starty3 - starty4) / count) * (i + 1)
          - ((starty4 + ((starty3 - starty4) / count) * (i + 1) - starty1 - ((starty2 - starty1) / count) * (i + 1))
            / count)
          * (count - 1 - j),
          startz4
          + ((startz3 - startz4) / count) * (i + 1)
          - ((startz4 + ((startz3 - startz4) / count) * (i + 1) - startz1 - ((startz2 - startz1) / count) * (i + 1))
            / count)
          * (count - 1 - j),
        ),
      );
      hireacy.push(
        new Cartesian3(
          startx4
          + ((startx3 - startx4) / count) * i
          - ((startx4 + ((startx3 - startx4) / count) * i - startx1 - ((startx2 - startx1) / count) * i) / count)
          * (count - 1 - j),
          starty4
          + ((starty3 - starty4) / count) * i
          - ((starty4 + ((starty3 - starty4) / count) * i - starty1 - ((starty2 - starty1) / count) * i) / count)
          * (count - 1 - j),
          startz4
          + ((startz3 - startz4) / count) * i
          - ((startz4 + ((startz3 - startz4) / count) * i - startz1 - ((startz2 - startz1) / count) * i) / count)
          * (count - 1 - j),
        ),
      );
      hireacys.push(...hireacy);
      // 取出面的8个点坐标，拿点坐标去求高度值
      slopelineposition.push(Cartographic.fromDegrees(hireacy[0].x, hireacy[0].y));
      slopelineposition.push(
        Cartographic.fromDegrees((hireacy[0].x + hireacy[1].x) / 2, (hireacy[0].y + hireacy[1].y) / 2),
      );
      slopelineposition.push(Cartographic.fromDegrees(hireacy[1].x, hireacy[1].y));
      slopelineposition.push(
        Cartographic.fromDegrees((hireacy[1].x + hireacy[2].x) / 2, (hireacy[1].y + hireacy[2].y) / 2),
      );
      slopelineposition.push(Cartographic.fromDegrees(hireacy[2].x, hireacy[2].y));
      slopelineposition.push(
        Cartographic.fromDegrees((hireacy[2].x + hireacy[3].x) / 2, (hireacy[2].y + hireacy[3].y) / 2),
      );
      slopelineposition.push(Cartographic.fromDegrees(hireacy[3].x, hireacy[3].y));
      slopelineposition.push(
        Cartographic.fromDegrees((hireacy[3].x + hireacy[0].x) / 2, (hireacy[3].y + hireacy[0].y) / 2),
      );
    }
  }
  const updatedPositions = await sampleTerrainMostDetailed(viewer.terrainProvider, slopelineposition);

  // 拿到所有的高度数据
  let m = 0;

  // 提取计算坡度的逻辑为函数
  function calculateSlope(height1: number, height2: number, position1: Cartesian3, position2: Cartesian3): number {
    const heightDifference = Math.abs(height1 - height2);
    const horizontalDistance = Math.abs(Cartesian3.distance(position1, position2));
    const angleRadians = Math.atan(heightDifference / horizontalDistance);
    const angleDegrees = angleRadians * (180 / Math.PI);
    return angleDegrees;
  }

  // 提取生成方向线坐标的逻辑为函数
  function generateLinePosition(slopeIndex: number, updatedPositions: Cartographic[], m: number): number[] {
    const linePosition: number[] = [];
    const [startIdx, endIdx] = [m + slopeIndex, m + slopeIndex + 4];
    if (updatedPositions[startIdx].height > updatedPositions[endIdx].height) {
      linePosition.push(
        CzMath.toDegrees(updatedPositions[startIdx].longitude),
        CzMath.toDegrees(updatedPositions[startIdx].latitude),
        CzMath.toDegrees(updatedPositions[endIdx].longitude),
        CzMath.toDegrees(updatedPositions[endIdx].latitude),
      );
    }
    else {
      linePosition.push(
        CzMath.toDegrees(updatedPositions[endIdx].longitude),
        CzMath.toDegrees(updatedPositions[endIdx].latitude),
        CzMath.toDegrees(updatedPositions[startIdx].longitude),
        CzMath.toDegrees(updatedPositions[startIdx].latitude),
      );
    }
    return linePosition;
  }

  // 添加颜色统计对象
  const colorCounts: Record<string, number> = {
    yellow: 0,
    cyan: 0,
    limegreen: 0,
    red: 0,
    royalblue: 0,
    purple: 0,
    pink: 0,
    orange: 0,
    white: 0,
  };

  for (let k = 0; k < updatedPositions.length / 8; k++) {
    const slopes = [
      calculateSlope(
        updatedPositions[m].height,
        updatedPositions[m + 4].height,
        Cartesian3.fromDegrees(
          updatedPositions[m].longitude,
          updatedPositions[m].latitude,
        ),
        Cartesian3.fromDegrees(
          updatedPositions[m + 4].longitude,
          updatedPositions[m + 4].latitude,
        ),
      ),
      calculateSlope(
        updatedPositions[m + 1].height,
        updatedPositions[m + 5].height,
        Cartesian3.fromDegrees(
          updatedPositions[m + 1].longitude,
          updatedPositions[m + 1].latitude,
        ),
        Cartesian3.fromDegrees(
          updatedPositions[m + 5].longitude,
          updatedPositions[m + 5].latitude,
        ),
      ),
      calculateSlope(
        updatedPositions[m + 2].height,
        updatedPositions[m + 6].height,
        Cartesian3.fromDegrees(
          updatedPositions[m + 2].longitude,
          updatedPositions[m + 2].latitude,
        ),
        Cartesian3.fromDegrees(
          updatedPositions[m + 6].longitude,
          updatedPositions[m + 6].latitude,
        ),
      ),
      calculateSlope(
        updatedPositions[m + 3].height,
        updatedPositions[m + 7].height,
        Cartesian3.fromDegrees(
          updatedPositions[m + 3].longitude,
          updatedPositions[m + 3].latitude,
        ),
        Cartesian3.fromDegrees(
          updatedPositions[m + 7].longitude,
          updatedPositions[m + 7].latitude,
        ),
      ),
    ];

    const maxSlope = Math.max(...slopes);

    const linePosition = generateLinePosition(slopes.indexOf(maxSlope), updatedPositions, m);

    let slopeColor: string;
    if (maxSlope >= 0 && maxSlope < 10) {
      slopeColor = 'yellow';
    }
    else if (maxSlope >= 10 && maxSlope < 20) {
      slopeColor = 'cyan';
    }
    else if (maxSlope >= 20 && maxSlope < 30) {
      slopeColor = 'limegreen';
    }
    else if (maxSlope >= 30 && maxSlope < 40) {
      slopeColor = 'red';
    }
    else if (maxSlope >= 40 && maxSlope < 50) {
      slopeColor = 'royalblue';
    }
    else if (maxSlope >= 50 && maxSlope < 60) {
      slopeColor = 'purple';
    }
    else if (maxSlope >= 60 && maxSlope < 70) {
      slopeColor = 'pink';
    }
    else if (maxSlope >= 70 && maxSlope < 80) {
      slopeColor = 'orange';
    }
    else {
      slopeColor = 'white';
    }

    // 更新颜色统计
    colorCounts[slopeColor] = (colorCounts[slopeColor] || 0) + 1;

    const entity = new Cesium.Entity({
      polyline: {
        clampToGround: true,
        positions: Cartesian3.fromDegreesArray(linePosition),
        material: new PolylineArrowMaterialProperty(Color.fromCssColorString(slopeColor)),
        width: 8,
      },
    });
    entities.push(entity);
    m += 8;
  }

  // 返回结果中添加颜色统计
  return { entities, colorCounts };
}
