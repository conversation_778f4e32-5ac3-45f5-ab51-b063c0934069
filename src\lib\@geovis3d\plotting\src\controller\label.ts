import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';
import { positionUpdate } from './utils/position-update';

/**
 * label 标绘配置
 */
export default <PlottingControllerOptions>{
  type: 'label',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 1,
  location: { visible: true },
  altitude: { visible: true },
  scale: { visible: true },
  scaleCallback(entity, scale) {
    const prev = entity.label!.scale?.getValue(Cesium.JulianDate.now()) ?? 1;
    entity.label!.scale = new Cesium.ConstantProperty(prev * scale);
  },
  update(entity) {
    if (!entity.label) {
      entity.label = new Cesium.LabelGraphics({
        text: '文本',
      });
    }
    positionUpdate(entity);
  },
};
