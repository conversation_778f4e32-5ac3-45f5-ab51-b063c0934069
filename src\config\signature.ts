import JSEncrypt from 'jsencrypt';

export const PUBL<PERSON>_KEY_PEM = `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0V/ByBXeVt87qXPN/xnmEbMXJZYlYgfxdiH7sDoB+6KHEc57Rtr1fm2Jsmkhb7nhUBTSnFBFwCIfOPY8JuMjzamU3/1UqfDJqd+GARtlmhV/9WcFGMSlTUofY0o3WjbpAJxtKXIhDIq0AQ1ndMKqpGoG3dXBvBtbV1FlYfO9CpeztZTJ9QfkkhU5Sc5Ok3Va7+MaZZrqMbslGz57sHx9OEIJXbn7geyYI1TDTZEgK8btBwaZoOHHNaOORTXVpLFWMYrRg0VNsfbuLY2tWvtusaQCnB3jdxIGnLW9VZ/TFidfL19/mzAx97N33Q4y9w6Qxc7C1bLn1whGLQJ3B+iKSQIDAQAB`;
export const PRIVATE_KEY_PEM = `MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDRX8HIFd5W3zupc83/GeYRsxclliViB/F2IfuwOgH7oocRzntG2vV+bYmyaSFvueFQFNKcUEXAIh849jwm4yPNqZTf/VSp8Mmp34YBG2WaFX/1ZwUYxKVNSh9jSjdaNukAnG0pciEMirQBDWd0wqqkagbd1cG8G1tXUWVh870Kl7O1lMn1B+SSFTlJzk6TdVrv4xplmuoxuyUbPnuwfH04QgldufuB7JgjVMNNkSArxu0HBpmg4cc1o45FNdWksVYxitGDRU2x9u4tja1a+26xpAKcHeN3Egactb1Vn9MWJ18vX3+bMDH3s3fdDjL3DpDFzsLVsufXCEYtAncH6IpJAgMBAAECggEAAN8cXpZyUOJBzIUse/D1Qc80O9yeiZ3tP2FwCF4opY3m1tTvmaGBTBltbU5KuVa78JCY0wjygWFvx9W4KMeuEN9+PMBzmpF9OJvMyJkyMkX/1ltFBilelsfFznmZaWK5IMUfhZo+apLy1N5KNyfOr6IeJsykL2hEJRG0EYhPzSKtyqqbciMmTi9zR0lQktC3LFgOH9ghUFpQo2c887Njq0S4R0X0VlJvpZQr1OSIzOF+a/l78vc46/hJ1gLhDmtt39b/u7unXjpAJzPM31LXncQZhzTNPJ/35DSLDNEjCFe7StHc56GbI8o+dIviB5fsoVSQXZEYVJRgXBRgV128EQKBgQDbMP5kkBs6KSPG6OOYrqSjQ7tDunImj9N2l/PteGTq9T2/OreTAEB7NBRuN4h6BmMgzusdq2DBX3I7d09+QOvC5Dvoi3t0Ngq4yqEKnhn9VL5pQDR98fwtPDv/jUjxDtDA1c0TJja/5ZlvsFESdP7ekWSFMWqVIt2KsP90iJ4FSwKBgQD0iLeyxZXqeI/6sjCpAmK+77Hox9LYMyUP/0c4IT/znX2VN2+hTYV6wyD6wBtUfVMvcIAndGeafTijOhXDBvnZzl2TEqOnq/O759kL6XETs39pNnDdDJZ9n+FKZRE0uOubNHPm7PzZ3QCm9r52lT+IfN3DhmJR4gyShFRx2O62OwKBgFj9ABr1wogPUb4cqOJA7Qh3pBxwIo3zUGfScy7S9S3K2QG9JfBhZsxeLXk9xIO9+hrExwGppUN8q5Tm8HgShWT/Yi8cCcf5dMrnQRBddbG66P80GNuVFcuLq9QpmRZ87FWNt49xsUKhRXTpwVBb/WmA362PjXT0zbrT9zJpkLYXAoGAcPlxFTDf8DG/Iy2L+alWgawv38HiEWyfpOJlvvLtntmWGYhBFJ7CGlNoOzDh74jvE63AJ3pKGz/bGN5PC4l/iTckWDx2S5LPynJ0/mHdkiKNrMgwIJAePu/MoBhNeLQudcgn+/plXyoRkRHSMOsHLUGzGxBNAFOVR2PHt8lnj90CgYBAMGzaJCz46qMTuG+zvDfIEnm1Z0LdgSR3iQoH8UjyKwzzT4uOxfIOI/1wbJIHBCBd4A3xsi9kze6J+Up3FoKP4+bQ0d0eOT7YK/cAxVhkM/8lfjNHWcDdKEiS82bMByHmt6fvKUMGnf2aOOStFXyd8Th0DksOY4iyfSFZScHetA==`;
// 对内容进行加密
export function encrypt(text: string): string {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(PUBLIC_KEY_PEM);
  return encryptor.encrypt(text) as string;
}
export function decrypt(text: string): string {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(PUBLIC_KEY_PEM);
  encryptor.setPrivateKey(PRIVATE_KEY_PEM);
  return encryptor.decrypt(text) as string;
}
