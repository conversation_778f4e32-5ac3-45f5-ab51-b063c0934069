import * as turf from '@turf/turf';
import { Bezier } from 'bezier-js';

/**
 * 输入起点、终点、最高点,生成OD贝塞尔曲线
 * @param o 起点
 * @param d 终点
 * @param height  最高点 m
 * @param steps 生成点位的数量
 */
export function bezierFromOD(
  o: [number, number, number],
  d: [number, number, number],
  height: number,
  steps = 10,
): { x: number; y: number; z?: number }[] {
  const mid = turf.midpoint([o[0], o[1]], [d[0], d[1]]).geometry.coordinates;
  const m = [mid[0], mid[1], height];
  return new Bezier(
    { x: o[0], y: o[1], z: o[2] },
    { x: m[0], y: m[1], z: m[2] },
    { x: d[0], y: d[1], z: d[2] },
  ).getLUT(steps);
}
