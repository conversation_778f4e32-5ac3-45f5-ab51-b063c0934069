import * as Cesium from 'cesium';

import { setCesiumMaterialCache } from './material-cache';
import CircleWaveSource from './shaders/circle-wave.glsl?raw';

export interface CircleWaveMaterialPropertyOptions {
  color?: Cesium.Color;
  duration?: number;
  count?: number;
  gradient?: number;
}

/**
 * 原型扩散波
 */
export class CircleWaveMaterialProperty implements Cesium.MaterialProperty {
  constructor(options?: CircleWaveMaterialPropertyOptions) {
    this._color = options?.color ?? Cesium.Color.RED;

    this._duration = options?.duration ?? 5000;
    this._count = options?.count ?? 1;
    if (this.count <= 0) {
      this.count = 1;
    }

    this._gradient = options?.gradient ?? 0.5;
    this._gradient > 1 && (this._gradient = 1);
    this._gradient < 0 && (this._gradient = 0);

    this._time = performance.now();
  }

  static readonly MaterialType = 'CircleWaveMaterial';

  getType(_time?: Cesium.JulianDate) {
    return CircleWaveMaterialProperty.MaterialType;
  }

  private _time: number;

  private _duration: number;

  get duration(): number {
    return this._count;
  }

  set duration(value: number) {
    this._count = value;
  }

  private _count: number;

  get count(): number {
    return this._count;
  }

  set count(value: number) {
    this._count = value;
  }

  private _gradient: number;

  get gradient(): number {
    return this._gradient;
  }

  set gradient(value: number) {
    this._gradient = value;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  private _color: Cesium.Color;

  get color() {
    return this._color;
  }

  set color(value: Cesium.Color) {
    this._color = value;
  }

  getValue(_time?: Cesium.JulianDate, result?: any) {
    result ??= {};
    result.color = this._color;
    result.time = (performance.now() - this._time) / this._duration;
    result.count = this.count;
    result.gradient = 1 + 10 * (1 - this.gradient);
    return result;
  }

  equals(other?: CircleWaveMaterialProperty) {
    return (
      this === other
      || (other instanceof CircleWaveMaterialProperty
        && this._color == other._color
        && this._gradient == other?._gradient
        && this._count == other?._count
        && this._duration == other?._duration)
    );
  }
}

setCesiumMaterialCache(CircleWaveMaterialProperty.MaterialType, {
  fabric: {
    type: CircleWaveMaterialProperty.MaterialType,
    uniforms: {
      color: new Cesium.Color(1, 0, 0, 1),
      time: 1,
      count: 1,
      gradient: 0.5,
    },
    source: CircleWaveSource,
  },
  translucent: () => true,
});
