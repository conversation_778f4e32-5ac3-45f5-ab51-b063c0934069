<!-- Cesium3dTilesetGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type {
  Cesium3DTilesetGraphicsKey,
  Cesium3DTilesetGraphicsSerializateJSON,
} from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { Cesium3DTilesetGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import { useGraphicsBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';
import StringAttribute from './string-attribute.vue';

defineOptions({ name: 'Cesium3dTilesetGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: Cesium3DTilesetGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<
  Cesium.Cesium3DTilesetGraphics,
  Cesium3DTilesetGraphicsSerializateJSON
>({
  graphic: () => props.entity?.tileset,
  omit: props.omit,
  toJSON: (graphics, omit) => Cesium3DTilesetGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => Cesium3DTilesetGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="tileset"
    graphics-field="show"
    label="可见"
  />
  <StringAttribute
    v-if="!hide?.includes('uri')"
    v-model="model.uri"
    graphics="tileset"
    graphics-field="uri"
    label="资源路径"
  />
  <NumberAttribute
    v-if="!hide?.includes('maximumScreenSpaceError')"
    v-model="model.maximumScreenSpaceError"
    graphics="tileset"
    graphics-field="maximumScreenSpaceError"
    label="maximumScreenSpaceError"
  />
</template>
