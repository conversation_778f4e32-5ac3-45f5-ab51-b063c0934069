/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by @xiankq/openapi-typescript-expand
// Power by openapi-typescript

import {bdvRequest} from "./request";



/**
 * @tag 资源 查询接口
 * @summary 自动补全查询
 * @url /elasticsearch/resources/autoCompleteSearch
 * @method post
 * @description 自动补全查询
 */

export module ElasticsearchResourcesAutoCompleteSearchUsingPost {
  export type Operation = paths['/elasticsearch/resources/autoCompleteSearch']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 资源 查询接口
 * @summary 自动补全查询
 * @url /elasticsearch/resources/autoCompleteSearch
 * @method post
 * @description 自动补全查询
 */

export function elasticsearchResourcesAutoCompleteSearchUsingPost(options:ElasticsearchResourcesAutoCompleteSearchUsingPost.Options):Promise<ElasticsearchResourcesAutoCompleteSearchUsingPost.Result> {
  return bdvRequest({
    url:'/elasticsearch/resources/autoCompleteSearch',
    method:'post',
    ...options,
  });
}

/**
 * @tag 资源 查询接口
 * @summary 查询明细
 * @url /elasticsearch/resources/getDetailObject
 * @method post
 * @description 查询明细
 */

export module ElasticsearchResourcesGetDetailObjectUsingPost {
  export type Operation = paths['/elasticsearch/resources/getDetailObject']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 资源 查询接口
 * @summary 查询明细
 * @url /elasticsearch/resources/getDetailObject
 * @method post
 * @description 查询明细
 */

export function elasticsearchResourcesGetDetailObjectUsingPost(options:ElasticsearchResourcesGetDetailObjectUsingPost.Options):Promise<ElasticsearchResourcesGetDetailObjectUsingPost.Result> {
  return bdvRequest({
    url:'/elasticsearch/resources/getDetailObject',
    method:'post',
    ...options,
  });
}

/**
 * @tag 资源 查询接口
 * @summary 列表查询
 * @url /elasticsearch/resources/getList
 * @method post
 * @description 列表查询
 */

export module ElasticsearchResourcesGetListUsingPost {
  export type Operation = paths['/elasticsearch/resources/getList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 资源 查询接口
 * @summary 列表查询
 * @url /elasticsearch/resources/getList
 * @method post
 * @description 列表查询
 */

export function elasticsearchResourcesGetListUsingPost(options:ElasticsearchResourcesGetListUsingPost.Options):Promise<ElasticsearchResourcesGetListUsingPost.Result> {
  return bdvRequest({
    url:'/elasticsearch/resources/getList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 资源 查询接口
 * @summary 查询树列表
 * @url /elasticsearch/resources/getTreeList
 * @method post
 * @description 查询树列表
 */

export module ElasticsearchResourcesGetTreeListUsingPost {
  export type Operation = paths['/elasticsearch/resources/getTreeList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 资源 查询接口
 * @summary 查询树列表
 * @url /elasticsearch/resources/getTreeList
 * @method post
 * @description 查询树列表
 */

export function elasticsearchResourcesGetTreeListUsingPost(options:ElasticsearchResourcesGetTreeListUsingPost.Options):Promise<ElasticsearchResourcesGetTreeListUsingPost.Result> {
  return bdvRequest({
    url:'/elasticsearch/resources/getTreeList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 资源 查询接口
 * @summary 点位网格数据列表
 * @url /elasticsearch/resources/gridPointList
 * @method post
 * @description 点位网格数据列表
 */

export module ElasticsearchResourcesGridPointListUsingPost {
  export type Operation = paths['/elasticsearch/resources/gridPointList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 资源 查询接口
 * @summary 点位网格数据列表
 * @url /elasticsearch/resources/gridPointList
 * @method post
 * @description 点位网格数据列表
 */

export function elasticsearchResourcesGridPointListUsingPost(options:ElasticsearchResourcesGridPointListUsingPost.Options):Promise<ElasticsearchResourcesGridPointListUsingPost.Result> {
  return bdvRequest({
    url:'/elasticsearch/resources/gridPointList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 资源 查询接口
 * @summary 点位网格聚合统计
 * @url /elasticsearch/resources/gridPointStatistic
 * @method post
 * @description 点位网格聚合统计
 */

export module ElasticsearchResourcesGridPointStatisticUsingPost {
  export type Operation = paths['/elasticsearch/resources/gridPointStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 资源 查询接口
 * @summary 点位网格聚合统计
 * @url /elasticsearch/resources/gridPointStatistic
 * @method post
 * @description 点位网格聚合统计
 */

export function elasticsearchResourcesGridPointStatisticUsingPost(options:ElasticsearchResourcesGridPointStatisticUsingPost.Options):Promise<ElasticsearchResourcesGridPointStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/elasticsearch/resources/gridPointStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 资源 查询接口
 * @summary 分页查询
 * @url /elasticsearch/resources/listPage
 * @method post
 * @description 分页查询
 */

export module ElasticsearchResourcesListPageUsingPost {
  export type Operation = paths['/elasticsearch/resources/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 资源 查询接口
 * @summary 分页查询
 * @url /elasticsearch/resources/listPage
 * @method post
 * @description 分页查询
 */

export function elasticsearchResourcesListPageUsingPost(options:ElasticsearchResourcesListPageUsingPost.Options):Promise<ElasticsearchResourcesListPageUsingPost.Result> {
  return bdvRequest({
    url:'/elasticsearch/resources/listPage',
    method:'post',
    ...options,
  });
}
export interface paths {
    "/elasticsearch/resources/autoCompleteSearch": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 自动补全查询
         * @description 自动补全查询
         */
        post: operations["autoCompleteSearchUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/elasticsearch/resources/getDetailObject": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询明细
         * @description 查询明细
         */
        post: operations["getDetailObjectUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/elasticsearch/resources/getList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 列表查询
         * @description 列表查询
         */
        post: operations["getListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/elasticsearch/resources/getTreeList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询树列表
         * @description 查询树列表
         */
        post: operations["getTreeListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/elasticsearch/resources/gridPointList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 点位网格数据列表
         * @description 点位网格数据列表
         */
        post: operations["gridPointListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/elasticsearch/resources/gridPointStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 点位网格聚合统计
         * @description 点位网格聚合统计
         */
        post: operations["gridPointStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/elasticsearch/resources/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询
         * @description 分页查询
         */
        post: operations["listPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /**
         * AutoCompleteSearchFormDTO
         * @description 自动补全查询查询
         */
        AutoCompleteSearchFormDTO: {
            /** @description 关键字 */
            keyword?: string;
            /** @description 类型编码 */
            typeCode?: string;
        };
        /**
         * 查询排序配置项
         * @description 查询排序配置项
         */
        ChaXunPaiXuPeiZhiXiang: {
            /**
             * @description 排序方式
             * @example ASC/asc;DESC/desc
             */
            orderType?: string;
            /** @description 属性名称 */
            propertyName?: string;
        };
        /**
         * GetOneDTO对象
         * @description 明细查询入参
         */
        GetOneDTODuiXiang: {
            /** @description 数据ID */
            id?: string;
            /** @description 类型编码 */
            typeCode?: string;
        };
        /**
         * GridPointListFormDTO
         * @description 网格点位列表入参DTO
         */
        GridPointListFormDTO: {
            /** @description GeoHash 字符串 */
            geoHash?: string;
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 年度 */
            year?: string;
        };
        /**
         * GridPointStatisticDTO
         * @description 网格点位统计结果DTO
         */
        GridPointStatisticDTO: {
            /**
             * Format: int64
             * @description 总数
             */
            count?: number;
            /** @description GeoHash 字符串 */
            geoHash?: string;
            /** @description 点位ID，精度大于等于7级时直接返回点位 */
            id?: string;
            /**
             * Format: double
             * @description 纬度
             */
            latitude?: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude?: number;
        };
        /**
         * GridPointStatisticFormDTO
         * @description 网格点位统计入参DTO
         */
        GridPointStatisticFormDTO: {
            /**
             * Format: double
             * @description 右下角纬度
             */
            bottomRightLatitude?: number;
            /**
             * Format: double
             * @description 右下角经度
             */
            bottomRightLongitude?: number;
            /**
             * Format: int32
             * @description 精度: 1(5,004km x 5,004km) 2(1,251km x 625km) 3(156km x 156km) 4(39km x 19.5km) 5(4.9km x 4.9km) 6(1.2km x 0.61km) 7(152.8m x 152.8m)
             */
            precision?: number;
            /**
             * Format: double
             * @description 左上角纬度
             */
            topLeftLatitude?: number;
            /**
             * Format: double
             * @description 左上角经度
             */
            topLeftLongitude?: number;
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 年度 */
            year?: string;
        };
        /**
         * PageParam«ResourceSearchFormDTO»
         * @description 分页参数
         */
        PageParamResourceSearchFormDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["ResourceSearchFormDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«SearchResultDTO»
         * @description 分页参数
         */
        PageParamSearchResultDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["SearchResultDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["SearchResultDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * ResourceSearchFormDTO
         * @description 查询入参
         */
        ResourceSearchFormDTO: {
            /**
             * Format: double
             * @description 距离,单位:km,必须填写经度和维度才会查询
             */
            distance?: number;
            /**
             * Format: double
             * @description 纬度
             */
            latitude?: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude?: number;
            /** @description 查询内容 */
            queryContent?: string;
            /** @description 查询内容方式：拆分匹配-match；顺序匹配-like（默认） */
            queryContentType?: string;
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 年度 */
            year?: string;
        };
        /**
         * ResourceSuggestResultDTO
         * @description 资源补充查询
         */
        ResourceSuggestResultDTO: {
            /** @description id */
            id?: string;
            /**
             * Format: double
             * @description 纬度
             */
            latitude?: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude?: number;
            /** @description 父类名称 */
            parentTypeName?: string;
            /** @description 标题 */
            title?: string;
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 类型名称 */
            typeName?: string;
        };
        /**
         * ResourceTreeSearchFormDTO
         * @description 树查询
         */
        ResourceTreeSearchFormDTO: {
            /**
             * Format: double
             * @description 距离,单位:km,必须填写经度和维度才会查询
             */
            distance?: number;
            /**
             * Format: double
             * @description 纬度
             */
            latitude?: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude?: number;
            /** @description 查询内容 */
            queryContent?: string;
            /** @description 查询内容方式：拆分匹配-match；顺序匹配-like（默认） */
            queryContentType?: string;
            /** @description 是否统计数量:1-是 0-否 */
            readCount?: string;
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 年度 */
            year?: string;
        };
        /** Result«List«GridPointStatisticDTO»» */
        ResultListGridPointStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["GridPointStatisticDTO"][];
            message?: string;
        };
        /** Result«List«object»» */
        ResultListobject: {
            /** Format: int32 */
            code?: number;
            data?: Record<string, never>[];
            message?: string;
        };
        /** Result«List«ResourceSuggestResultDTO»» */
        ResultListResourceSuggestResultDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ResourceSuggestResultDTO"][];
            message?: string;
        };
        /** Result«List«SearchResultDTO»» */
        ResultListSearchResultDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SearchResultDTO"][];
            message?: string;
        };
        /** Result«Map» */
        ResultMap: {
            /** Format: int32 */
            code?: number;
            data?: Record<string, never>;
            message?: string;
        };
        /** Result«PageParam«SearchResultDTO»» */
        ResultPageParamSearchResultDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamSearchResultDTO"];
            message?: string;
        };
        /**
         * SearchResultDTO
         * @description 资源查询
         */
        SearchResultDTO: {
            /**
             * Format: double
             * @description 距离,单位:km,当距离查询时才会返回距离
             */
            distance?: number;
            /** @description id */
            id?: string;
            /**
             * Format: double
             * @description 纬度
             */
            latitude?: number;
            /**
             * Format: double
             * @description 经度
             */
            longitude?: number;
            /** @description 父类名称 */
            parentTypeName?: string;
            /** @description tableRowId */
            tableRowId?: string;
            /** @description 标题 */
            title?: string;
            /** @description 类型编码 */
            typeCode?: string;
            /** @description 类型名称 */
            typeName?: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    autoCompleteSearchUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["AutoCompleteSearchFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListResourceSuggestResultDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getDetailObjectUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["GetOneDTODuiXiang"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultMap"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ResourceSearchFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSearchResultDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getTreeListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["ResourceTreeSearchFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSearchResultDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    gridPointListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["GridPointListFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListobject"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    gridPointStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["GridPointStatisticFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListGridPointStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamResourceSearchFormDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamSearchResultDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
}
