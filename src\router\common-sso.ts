import type { Router } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { promiseTimeout } from '@vueuse/core';

/**
 * 通用的单点登录路由拦截，后端会直接注入token到sso页面，将token写入储存并跳转首页
 */
export function setupCommonSSO(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const token = to.query.token;
    if (token && to.name === 'SSO' as any) {
      const userStore = useUserStore();
      userStore.authorization = token as string;
      await promiseTimeout(500);
      Reflect.deleteProperty(to.query, 'token');
      return next('/home');
    }
    else {
      return next();
    }
  });
}
