import type { Cartesian2SerializateJSON } from './cartesian2';

import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type { ShadowModeSerializateJSON } from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import type { PlaneSerializateJSON } from './plane';
import * as Cesium from 'cesium';
import { Cartesian2Serializate } from './cartesian2';

import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { PlaneSerializate } from './plane';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PlaneGraphicsSerializateJSON {
  show?: boolean;
  plane?: PlaneSerializateJSON;
  dimensions?: Cartesian2SerializateJSON;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
}

export type PlaneGraphicsKey = keyof PlaneGraphicsSerializateJSON;

export class PlaneGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.PlaneGraphics,
    omit?: PlaneGraphicsKey[],
    time?: Cesium.JulianDate,
  ): PlaneGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      plane: PlaneSerializate.toJSON(getValue('plane')),
      dimensions: Cartesian2Serializate.toJSON(getValue('dimensions')),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
    };
  }

  static fromJSON(
    json?: PlaneGraphicsSerializateJSON,
    omit?: PlaneGraphicsKey[],
  ): Cesium.PlaneGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.PlaneGraphics({
      show: getValue('show') ?? true,
      plane: PlaneSerializate.fromJSON(getValue('plane')),
      dimensions: Cartesian2Serializate.fromJSON(getValue('dimensions')),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
    });
  }
}
