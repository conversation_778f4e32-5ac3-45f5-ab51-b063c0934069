<script lang="ts" setup>
import type * as Cesium from 'cesium';

import { shallowRef } from 'vue-demi';
import PlottingPresetAttribute from './plotting-preset-attribute.vue';
import PlottingPresetPanel from './plotting-preset-panel.vue';
import { usePlotProvideState } from './state';

defineOptions({
  name: 'PlottingPresetModule',
  inheritAttrs: false,
});

/**
 * CesiumPlotModule
 */
const props = defineProps<{
  /**
   * appCode
   */
  appCode: string;
  /**
   * baseUrl
   */
  baseUrl: string;
  assetUrl: string;
  viewer: Cesium.Viewer;
  zIndex?: number;
  to?: any;
}>();

const emit = defineEmits<{
  (event: 'close'): void;
}>();

const parentElRef = shallowRef<HTMLElement>();
const { dataSource } = usePlotProvideState(
  parentElRef,
  props.baseUrl,
  props.appCode,
  props.assetUrl,
  props.viewer,
);
</script>

<template>
  <div v-bind="$attrs" ref="parentElRef" class="plotting-preset-module">
    <template v-if="parentElRef">
      <PlottingPresetPanel />
      <PlottingPresetAttribute />
    </template>
  </div>
</template>

<style lang="scss" scoped>
.plotting-preset-module {
  --el-fill-color-light: rgb(155 193 255 / 30%);
  --el-fill-color-blank: rgb(188 214 255 / 20%);
  --el-input-text-color: #fff;
  --el-text-color-regular: #fff;
  --el-border-color: transparent;
  --el-border: 0;
  --el-border-radius-base: 6px;

  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);

  :deep(.i-icon) {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
  }

  :deep(.el-input-number.is-without-controls .el-input__wrapper) {
    padding-right: 4px;
    padding-left: 4px;
  }

  :deep(.el-form-item) {
    margin-bottom: 10px;

    --font-size: 12px;
    --el-font-size-base: var(--font-size);
  }

  :deep(.el-select-v2) {
    font-size: var(--font-size);
  }

  :deep(.el-collapse) {
    --el-collapse-header-height: 44px;
    --el-border-color-lighter: transparent;
    --el-collapse-header-bg-color: transparent;
    --el-collapse-header-text-color: #eef8ff;
    --el-collapse-content-bg-color: transparent;
    --el-collapse-content-text-color: #aad5ef;
    --el-collapse-content-font-size: 12px;
    --el-collapse-header-font-size: 15px;
  }

  :deep().el-collapse-item__content {
    padding-bottom: 0;
  }

  :deep(.input-px .el-input__wrapper::after) {
    padding-bottom: 0.2rem;
    line-height: 1;
    content: 'px';
  }

  :deep(.input-mater .el-input__wrapper::after) {
    padding-bottom: 0.2rem;
    line-height: 1;
    content: 'm';
  }

  :deep(.input-rate .el-input__wrapper::after) {
    padding-bottom: 0.2rem;
    line-height: 1;
    content: '%';
  }

  :deep(.el-form) {
    overflow: hidden;

    --el-form-label-font-size: 12px;

    .el-form-item__label {
      justify-content: center;
      min-width: 60px;
      font-size: 12px;
      color: #ddf6ff;
    }

    .el-input {
      --el-input-text-color: #fff;

      color: #fff;
    }

    .el-slider {
      padding-bottom: 20px;
    }

    .el-switch {
      --el-switch-on-color: rgb(62 107 212 / 80%);
      --el-switch-off-color: rgb(188 214 255 / 20%);
    }

    .el-form-item__content {
      > [class^='el-'] {
        flex: 1;
        margin-left: 0;
      }
    }

    .el-button {
      --el-button-hover-bg-color: var(--el-fill-color-blank);
    }
  }
}
</style>
