import * as Cesium from 'cesium';

export interface QuaternionSerializateJSON {
  x?: number;
  y?: number;
  z?: number;
  w?: number;
}

export type QuaternionKey = keyof QuaternionSerializateJSON;

export class QuaternionSerializate {
  private constructor() {}

  static toJSON(data?: Cesium.Quaternion): QuaternionSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    return {
      x: data.x,
      y: data.y,
      z: data.z,
      w: data.w,
    };
  }

  static fromJSON(json?: QuaternionSerializateJSON): Cesium.Quaternion | undefined {
    if (!json) {
      return undefined;
    }
    return new Cesium.Quaternion(json.x, json.y, json.z, json.w);
  }
}
