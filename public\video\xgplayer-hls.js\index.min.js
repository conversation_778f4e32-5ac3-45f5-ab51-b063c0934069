!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("core-js/modules/es.array.concat.js"),require("core-js/modules/es.array.from.js"),require("core-js/modules/es.array.join.js"),require("core-js/modules/es.array.map.js"),require("core-js/modules/es.array.slice.js"),require("core-js/modules/es.object.assign.js"),require("core-js/modules/es.object.keys.js"),require("core-js/modules/es.string.iterator.js"),require("core-js/modules/esnext.iterator.map.js"),require("xgplayer"),require("core-js/modules/es.array.iterator.js"),require("core-js/modules/es.array.splice.js"),require("core-js/modules/es.object.to-string.js"),require("core-js/modules/es.promise.js"),require("core-js/modules/es.regexp.exec.js"),require("core-js/modules/esnext.iterator.constructor.js"),require("core-js/modules/esnext.iterator.for-each.js"),require("core-js/modules/web.dom-collections.for-each.js"),require("core-js/modules/web.dom-collections.iterator.js"),require("core-js/modules/web.url.js"),require("core-js/modules/web.url.to-json.js"),require("core-js/modules/web.url-search-params.js"),require("core-js/modules/esnext.iterator.reduce.js"),require("core-js/modules/es.array.includes.js"),require("core-js/modules/es.function.name.js"),require("core-js/modules/es.json.stringify.js"),require("core-js/modules/es.regexp.to-string.js"),require("core-js/modules/es.array.filter.js"),require("core-js/modules/es.promise.finally.js"),require("core-js/modules/es.regexp.test.js"),require("core-js/modules/esnext.iterator.filter.js"),require("core-js/modules/es.typed-array.uint8-array.js"),require("core-js/modules/es.typed-array.copy-within.js"),require("core-js/modules/es.typed-array.every.js"),require("core-js/modules/es.typed-array.fill.js"),require("core-js/modules/es.typed-array.filter.js"),require("core-js/modules/es.typed-array.find.js"),require("core-js/modules/es.typed-array.find-index.js"),require("core-js/modules/es.typed-array.for-each.js"),require("core-js/modules/es.typed-array.includes.js"),require("core-js/modules/es.typed-array.index-of.js"),require("core-js/modules/es.typed-array.iterator.js"),require("core-js/modules/es.typed-array.join.js"),require("core-js/modules/es.typed-array.last-index-of.js"),require("core-js/modules/es.typed-array.map.js"),require("core-js/modules/es.typed-array.reduce.js"),require("core-js/modules/es.typed-array.reduce-right.js"),require("core-js/modules/es.typed-array.reverse.js"),require("core-js/modules/es.typed-array.set.js"),require("core-js/modules/es.typed-array.slice.js"),require("core-js/modules/es.typed-array.some.js"),require("core-js/modules/es.typed-array.sort.js"),require("core-js/modules/es.typed-array.subarray.js"),require("core-js/modules/es.typed-array.to-locale-string.js"),require("core-js/modules/es.typed-array.to-string.js"),require("core-js/modules/esnext.typed-array.at.js"),require("core-js/modules/esnext.typed-array.find-last.js"),require("core-js/modules/esnext.typed-array.find-last-index.js"),require("core-js/modules/es.number.is-nan.js"),require("core-js/modules/es.string.replace.js"),require("core-js/modules/es.object.get-prototype-of.js"),require("core-js/modules/es.symbol.js"),require("core-js/modules/es.string.trim.js"),require("core-js/modules/es.set.js"),require("core-js/modules/es.array.find.js"),require("core-js/modules/es.number.is-finite.js"),require("core-js/modules/esnext.iterator.find.js"),require("core-js/modules/es.string.includes.js"),require("core-js/modules/es.number.constructor.js"),require("core-js/modules/es.array-buffer.constructor.js"),require("core-js/modules/es.regexp.flags.js"),require("core-js/modules/es.string.pad-start.js"),require("core-js/modules/es.typed-array.float32-array.js"),require("core-js/modules/es.typed-array.float64-array.js"),require("core-js/modules/es.typed-array.int8-array.js"),require("core-js/modules/es.typed-array.int16-array.js"),require("core-js/modules/es.typed-array.int32-array.js"),require("core-js/modules/es.typed-array.uint8-clamped-array.js"),require("core-js/modules/es.typed-array.uint16-array.js"),require("core-js/modules/es.typed-array.uint32-array.js"),require("core-js/modules/es.regexp.constructor.js"),require("core-js/modules/es.regexp.sticky.js"),require("core-js/modules/es.string.split.js"),require("core-js/modules/es.string.match.js"),require("core-js/modules/es.array.find-index.js")):"function"==typeof define&&define.amd?define(["core-js/modules/es.array.concat.js","core-js/modules/es.array.from.js","core-js/modules/es.array.join.js","core-js/modules/es.array.map.js","core-js/modules/es.array.slice.js","core-js/modules/es.object.assign.js","core-js/modules/es.object.keys.js","core-js/modules/es.string.iterator.js","core-js/modules/esnext.iterator.map.js","xgplayer","core-js/modules/es.array.iterator.js","core-js/modules/es.array.splice.js","core-js/modules/es.object.to-string.js","core-js/modules/es.promise.js","core-js/modules/es.regexp.exec.js","core-js/modules/esnext.iterator.constructor.js","core-js/modules/esnext.iterator.for-each.js","core-js/modules/web.dom-collections.for-each.js","core-js/modules/web.dom-collections.iterator.js","core-js/modules/web.url.js","core-js/modules/web.url.to-json.js","core-js/modules/web.url-search-params.js","core-js/modules/esnext.iterator.reduce.js","core-js/modules/es.array.includes.js","core-js/modules/es.function.name.js","core-js/modules/es.json.stringify.js","core-js/modules/es.regexp.to-string.js","core-js/modules/es.array.filter.js","core-js/modules/es.promise.finally.js","core-js/modules/es.regexp.test.js","core-js/modules/esnext.iterator.filter.js","core-js/modules/es.typed-array.uint8-array.js","core-js/modules/es.typed-array.copy-within.js","core-js/modules/es.typed-array.every.js","core-js/modules/es.typed-array.fill.js","core-js/modules/es.typed-array.filter.js","core-js/modules/es.typed-array.find.js","core-js/modules/es.typed-array.find-index.js","core-js/modules/es.typed-array.for-each.js","core-js/modules/es.typed-array.includes.js","core-js/modules/es.typed-array.index-of.js","core-js/modules/es.typed-array.iterator.js","core-js/modules/es.typed-array.join.js","core-js/modules/es.typed-array.last-index-of.js","core-js/modules/es.typed-array.map.js","core-js/modules/es.typed-array.reduce.js","core-js/modules/es.typed-array.reduce-right.js","core-js/modules/es.typed-array.reverse.js","core-js/modules/es.typed-array.set.js","core-js/modules/es.typed-array.slice.js","core-js/modules/es.typed-array.some.js","core-js/modules/es.typed-array.sort.js","core-js/modules/es.typed-array.subarray.js","core-js/modules/es.typed-array.to-locale-string.js","core-js/modules/es.typed-array.to-string.js","core-js/modules/esnext.typed-array.at.js","core-js/modules/esnext.typed-array.find-last.js","core-js/modules/esnext.typed-array.find-last-index.js","core-js/modules/es.number.is-nan.js","core-js/modules/es.string.replace.js","core-js/modules/es.object.get-prototype-of.js","core-js/modules/es.symbol.js","core-js/modules/es.string.trim.js","core-js/modules/es.set.js","core-js/modules/es.array.find.js","core-js/modules/es.number.is-finite.js","core-js/modules/esnext.iterator.find.js","core-js/modules/es.string.includes.js","core-js/modules/es.number.constructor.js","core-js/modules/es.array-buffer.constructor.js","core-js/modules/es.regexp.flags.js","core-js/modules/es.string.pad-start.js","core-js/modules/es.typed-array.float32-array.js","core-js/modules/es.typed-array.float64-array.js","core-js/modules/es.typed-array.int8-array.js","core-js/modules/es.typed-array.int16-array.js","core-js/modules/es.typed-array.int32-array.js","core-js/modules/es.typed-array.uint8-clamped-array.js","core-js/modules/es.typed-array.uint16-array.js","core-js/modules/es.typed-array.uint32-array.js","core-js/modules/es.regexp.constructor.js","core-js/modules/es.regexp.sticky.js","core-js/modules/es.string.split.js","core-js/modules/es.string.match.js","core-js/modules/es.array.find-index.js"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).HlsPlayer=t(null,null,null,null,null,null,null,null,null,e.Player)}(this,(function(e,t,r,n,i,s,a,o,u,c){"use strict";function l(e,t,r){return t=w(t),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,h()?Reflect.construct(t,r||[],w(e).constructor):t.apply(e,r))}function d(e,t,r){if(h())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var i=new(e.bind.apply(e,n));return r&&T(i,r.prototype),i}function h(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(h=function(){return!!e})()}function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach((function(t){x(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function v(){v=function(){return t};var e,t={},r=Object.prototype,n=r.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},s="function"==typeof Symbol?Symbol:{},a=s.iterator||"@@iterator",o=s.asyncIterator||"@@asyncIterator",u=s.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(j){c=function(e,t,r){return e[t]=r}}function l(e,t,r,n){var s=t&&t.prototype instanceof y?t:y,a=Object.create(s.prototype),o=new L(n||[]);return i(a,"_invoke",{value:T(e,r,o)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}t.wrap=l;var h="suspendedStart",f="executing",p="completed",m={};function y(){}function _(){}function g(){}var b={};c(b,a,(function(){return this}));var k=Object.getPrototypeOf,S=k&&k(k(O([])));S&&S!==r&&n.call(S,a)&&(b=S);var x=g.prototype=y.prototype=Object.create(b);function E(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function r(i,s,a,o){var u=d(e[i],e,s);if("throw"!==u.type){var c=u.arg,l=c.value;return l&&"object"==typeof l&&n.call(l,"__await")?t.resolve(l.__await).then((function(e){r("next",e,a,o)}),(function(e){r("throw",e,a,o)})):t.resolve(l).then((function(e){c.value=e,a(c)}),(function(e){return r("throw",e,a,o)}))}o(u.arg)}var s;i(this,"_invoke",{value:function(e,n){function i(){return new t((function(t,i){r(e,n,t,i)}))}return s=s?s.then(i,i):i()}})}function T(t,r,n){var i=h;return function(s,a){if(i===f)throw Error("Generator is already running");if(i===p){if("throw"===s)throw a;return{value:e,done:!0}}for(n.method=s,n.arg=a;;){var o=n.delegate;if(o){var u=D(o,n);if(u){if(u===m)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===h)throw i=p,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=f;var c=d(t,r,n);if("normal"===c.type){if(i=n.done?p:"suspendedYield",c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(i=p,n.method="throw",n.arg=c.arg)}}}function D(t,r){var n=r.method,i=t.iterator[n];if(i===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,D(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var s=d(i,t.iterator,r.arg);if("throw"===s.type)return r.method="throw",r.arg=s.arg,r.delegate=null,m;var a=s.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,m):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function C(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function O(t){if(t||""===t){var r=t[a];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,s=function r(){for(;++i<t.length;)if(n.call(t,i))return r.value=t[i],r.done=!1,r;return r.value=e,r.done=!0,r};return s.next=s}}throw new TypeError(typeof t+" is not iterable")}return _.prototype=g,i(x,"constructor",{value:g,configurable:!0}),i(g,"constructor",{value:_,configurable:!0}),_.displayName=c(g,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===_||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,c(e,u,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},E(w.prototype),c(w.prototype,o,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,i,s){void 0===s&&(s=Promise);var a=new w(l(e,r,n,i),s);return t.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(x),c(x,u,"Generator"),c(x,a,(function(){return this})),c(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},t.values=O,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(C),!t)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function i(n,i){return o.type="throw",o.arg=t,r.next=n,i&&(r.method="next",r.arg=e),!!i}for(var s=this.tryEntries.length-1;s>=0;--s){var a=this.tryEntries[s],o=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var u=n.call(a,"catchLoc"),c=n.call(a,"finallyLoc");if(u&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(u){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var a=s?s.completion:{};return a.type=e,a.arg=t,s?(this.method="next",this.next=s.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),C(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;C(r)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:O(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),m}},t}function m(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(e,t,r,n,i,s,a){try{var o=e[s](a),u=o.value}catch(c){return void r(c)}o.done?t(u):Promise.resolve(u).then(n,i)}function g(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var s=e.apply(t,r);function a(e){_(s,n,i,a,o,"next",e)}function o(e){_(s,n,i,a,o,"throw",e)}a(void 0)}))}}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function k(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,m(n.key),n)}}function S(e,t,r){return t&&k(e.prototype,t),r&&k(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function x(e,t,r){return(t=m(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function E(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&T(e,t)}function w(e){return(w=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function T(e,t){return(T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function D(e){var t="function"==typeof Map?new Map:void 0;return D=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return d(e,arguments,w(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),T(r,e)},D(e)}function A(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},s=Object.keys(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function C(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=w(e)););return e}function L(){return L="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=C(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(arguments.length<3?e:r):i.value}},L.apply(this,arguments)}function O(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,s,a,o=[],u=!0,c=!1;try{if(s=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=s.call(r)).done)&&(o.push(n.value),o.length!==t);u=!0);}catch(l){c=!0,i=l}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw i}}return o}}(e,t)||R(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e){return function(e){if(Array.isArray(e))return I(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||R(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){if(e){if("string"==typeof e)return I(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?I(e,t):void 0}}function I(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function P(){var e,t,r=new Promise((function(r,n){e=r,t=n}));return r.used=!1,r.resolve=function(){return r.used=!0,e.apply(void 0,arguments)},r.reject=function(){return r.used=!0,t.apply(void 0,arguments)},r}function B(){try{return parseInt(performance.now(),10)}catch(e){return(new Date).getTime()}}var U,M=function(){function e(){b(this,e)}return S(e,null,[{key:"start",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6||1===e.length&&e.start(0)<0?0:e.start(0):0}},{key:"end",value:function(e){return e&&e.length?1===e.length&&e.end(0)-e.start(0)<1e-6?0:e.end(e.length-1):0}},{key:"get",value:function(e){if(e)try{return e.buffered}catch(t){}}},{key:"buffers",value:function(e,t){if(!e||!e.length)return[];for(var r=[],n=0,i=e.length;n<i;n++){var s=r.length;if(s&&t){var a=r[s-1],o=a[1];if(e.start(n)-o<=t){var u=e.end(n);u>o&&(a[1]=u)}else r.push([e.start(n),e.end(n)])}else r.push([e.start(n),e.end(n)])}return r}},{key:"totalLength",value:function(e){return e&&e.length?e.reduce((function(e,t){return e+(t[1]-t[0])}),0):0}},{key:"info",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!t||!t.length)return{start:0,end:0,buffers:[]};for(var i=0,s=0,a=0,o=0,u=0,c=0,l=0,d=e.buffers(t,n),h=0,f=d.length;h<f;h++){var p=d[h];if(r+n>=p[0]&&r<=p[1])i=p[0],s=p[1],a=h;else{if(r+n<p[0]){o=p[0],u=p[1];break}r+n>p[1]&&(c=p[0],l=p[1])}}return{start:i,end:s,index:a,buffers:d,nextStart:o,nextEnd:u,prevStart:c,prevEnd:l,currentTime:r,behind:r-i,remaining:s?s-r:0,length:e.totalLength&&e.totalLength(d)}}}])}(),N="manifest",F="network",V="network_timeout",z="network_forbidden",q="network_notfound",G="network_range_not_satisfiable",H="demux",K="remux",W="media",Y="drm",X="other",Q="runtime",J={FLV:"FLV",HLS:"HLS",MP4:"MP4",FMP4:"FMP4",MSE_ADD_SB:"MSE_ADD_SB",MSE_APPEND_BUFFER:"MSE_APPEND_BUFFER",MSE_OTHER:"MSE_OTHER",MSE_FULL:"MSE_FULL",OPTION:"OPTION",DASH:"DASH",LICENSE:"LICENSE",CUSTOM_LICENSE:"CUSTOM_LICENSE",MSE_HIJACK:"MSE_HIJACK",EME_HIJACK:"EME_HIJACK",SIDX:"SIDX",NO_CANPLAY_ERROR:"NO_CANPLAY_ERROR",BUFFERBREAK_ERROR:"BUFFERBREAK_ERROR",WAITING_TIMEOUT_ERROR:"WAITING_TIMEOUT_ERROR",MEDIA_ERR_ABORTED:"MEDIA_ERR_ABORTED",MEDIA_ERR_NETWORK:"MEDIA_ERR_NETWORK",MEDIA_ERR_DECODE:"MEDIA_ERR_DECODE",MEDIA_ERR_SRC_NOT_SUPPORTED:"MEDIA_ERR_SRC_NOT_SUPPORTED",MEDIA_ERR_CODEC_NOT_SUPPORTED:"MEDIA_ERR_CODEC_NOT_SUPPORTED",MEDIA_ERR_URL_EMPTY:"MEDIA_ERR_URL_EMPTY"},Z=(x(x(x(x(x(x(x(x(x(x(U={},N,{HLS:1100,DASH:1200}),F,2100),V,2101),z,2103),q,2104),G,2116),H,{FLV:3100,HLS:3200,MP4:3300,FMP4:3400,SIDX:3410}),K,{FMP4:4100,MP4:4200}),W,{MEDIA_ERR_ABORTED:5101,MEDIA_ERR_NETWORK:5102,MEDIA_ERR_DECODE:5103,MEDIA_ERR_SRC_NOT_SUPPORTED:5104,MEDIA_ERR_CODEC_NOT_SUPPORTED:5105,MEDIA_ERR_URL_EMPTY:5106,MSE_ADD_SB:5200,MSE_APPEND_BUFFER:5201,MSE_OTHER:5202,MSE_FULL:5203,MSE_HIJACK:5204,EME_HIJACK:5301}),Y,{LICENSE:7100,CUSTOM_LICENSE:7200}),x(x(U,X,8e3),Q,{NO_CANPLAY_ERROR:9001,BUFFERBREAK_ERROR:9002,WAITING_TIMEOUT_ERROR:9003})),$=function(e){function t(e,r,n,i,s){var a;return b(this,t),(a=l(this,t,[s||(null==n?void 0:n.message)])).errorType=e===V?F:e,a.originError=n,a.ext=i,a.errorCode=Z[e][r]||Z[e],a.errorMessage=a.message,a.errorCode||(a.errorType=X,a.errorCode=Z[a.errorType]),a}return E(t,e),S(t,null,[{key:"create",value:function(e,r,n,i,s){return e instanceof t?e:(e instanceof Error&&(n=e,e=""),e||(e=X),new t(e,r,n,i,s))}},{key:"network",value:function(e){var r;return new t(null!=e&&e.isTimeout?V:F,null,e instanceof Error?e:null,{url:null==e?void 0:e.url,response:null==e?void 0:e.response,httpCode:null==e||null===(r=e.response)||void 0===r?void 0:r.status})}}])}(D(Error)),ee="undefined"!=typeof window,te={DEBUG:1,LOG:2,WARN:3,ERROR:4},re=["Boolean","Number","String","Undefined","Null","Date","Object"],ne=function(){function e(t,r){b(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),this.logCacheLevel=(null==r?void 0:r.logCacheLevel)||3,this.logMaxSize=(null==r?void 0:r.logMaxSize)||204800,this.logSize=0,this.logTextArray=[]}return S(e,[{key:"debug",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[te.DEBUG].concat(n)),e.disabled||(t=console).debug.apply(t,[this._prefix,ie()].concat(n))}},{key:"log",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[te.LOG].concat(n)),e.disabled||(t=console).log.apply(t,[this._prefix,ie()].concat(n))}},{key:"warn",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[te.WARN].concat(n)),e.disabled||(t=console).warn.apply(t,[this._prefix,ie()].concat(n))}},{key:"error",value:function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];this.logCache.apply(this,[te.ERROR].concat(n)),e.disabled||(t=console).error.apply(t,[this._prefix,ie()].concat(n))}},{key:"logCache",value:function(e){if(!(e<this.logCacheLevel)){var t="";try{for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];var s=n.map((function(e){return ae(e)}));t=this._prefix+ie()+JSON.stringify(s)}catch(o){return}if(e>=this.logCacheLevel&&(this.logSize+=t.length,this.logTextArray.push(t)),this.logSize>this.logMaxSize){var a=this.logTextArray.shift();this.logSize-=a.length}}}},{key:"getLogCache",value:function(){var e=this.logTextArray.join("\n");return this.reset(),e}},{key:"reset",value:function(){this.logTextArray=[],this.logSize=0}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}},{key:"setLogLevel",value:function(e){this.logCacheLevel=e}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}])}();function ie(){return(new Date).toLocaleString()}function se(e){if("object"!==y(e))return e;var t=Object.prototype.toString.call(e).slice(8,-1);switch(t){case"Array":case"Uint8Array":case"ArrayBuffer":return t+"["+e.length+"]";case"Object":return"{}";default:return t}}function ae(e,t,r){r||(r=1),t||(t=2);var n={};if(!e||"object"!==y(e))return e;var i=Object.prototype.toString.call(e).slice(8,-1);if(!re.includes(i))return i;if(!(r>t)){for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r===t?n[s]=se(e[s]):"object"===y(e[s])?n[s]=ae(e[s],t,r+1):n[s]=e[s]);return n}}x(ne,"disabled",!0);var oe=function(){try{return ee?window.MediaSource:null}catch(e){}}(),ue="appendBuffer",ce="removeBuffer",le="updateDuration",de=function(){function e(t,r){var n=this;b(this,e),x(this,"media",null),x(this,"mediaSource",null),x(this,"_openPromise",P()),x(this,"_queue",Object.create(null)),x(this,"_sourceBuffer",Object.create(null)),x(this,"_mseFullFlag",{}),x(this,"_st",0),x(this,"_opst",0),x(this,"_logger",null),x(this,"_config",null),x(this,"_url",null),x(this,"_onSBUpdateEnd",(function(e){var t=n._queue[e];if(t){var r=t[0];if((null==r?void 0:r.opName)!==le&&t.shift(),r){var i=B()-n._opst;n._logger.debug("UpdateEnd",r.opName,i,r.context),r.promise.resolve({name:r.opName,context:r.context,costtime:i}),n._startQueue(e)}}})),x(this,"_onSBUpdateError",(function(e,t){var r=n._queue[e];if(r){var i=r[0];i&&(n._logger.error("UpdateError",e,i.opName,i.context),i.promise.reject(new $(W,J.MSE_APPEND_BUFFER,t)))}})),this._config=Object.assign(e.getDefaultConfig(),r),t&&this.bindMedia(t),this._logger=new ne("MSE"),this._config.openLog&&ne.enable()}return S(e,[{key:"isOpened",get:function(){var e;return"open"===(null===(e=this.mediaSource)||void 0===e?void 0:e.readyState)}},{key:"url",get:function(){return this._url}},{key:"duration",get:function(){var e;return(null===(e=this.mediaSource)||void 0===e?void 0:e.duration)||-1}},{key:"isEnded",get:function(){return!!this.mediaSource&&"ended"===this.mediaSource.readyState}},{key:"isFull",value:function(t){return t?this._mseFullFlag[t]:this._mseFullFlag[e.VIDEO]}},{key:"updateDuration",value:function(e){var t=this,r=this.mediaSource&&this.mediaSource.duration>e;if(this.mediaSource&&this.mediaSource.duration>e){var n=0;if(Object.keys(this._sourceBuffer).forEach((function(e){try{n=Math.max(t.bufferEnd(e)||0,n)}catch(r){}})),e<n)return Promise.resolve()}return this._enqueueBlockingOp((function(){t.isEnded?t._logger.debug("[debug mse] setDuration ended"):t.mediaSource&&(t.mediaSource.duration=e,t._logger.debug("[debug mse] setDuration"))}),le,{isReduceDuration:r})}},{key:"open",value:function(){var e=this;if(this._openPromise.used&&!this.isOpened&&this.mediaSource){var t=this.mediaSource;t.addEventListener("sourceopen",(function r(){var n=B()-e._st;e._logger.debug("MSE OPEN",n),t.removeEventListener("sourceopen",r),e._openPromise.resolve({costtime:n})})),this._openPromise=P()}return this._openPromise}},{key:"bindMedia",value:(n=g(v().mark((function e(t){var r,n,i=this;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.mediaSource&&!this.media){e.next=3;break}return e.next=3,this.unbindMedia();case 3:if(t&&oe){e.next=5;break}throw new Error("Param media or MediaSource does not exist");case 5:return this.media=t,r=this.mediaSource=new oe,this._st=B(),n=function e(){var n=B()-i._st;i._logger.debug("MSE OPEN"),r.removeEventListener("sourceopen",e),URL.revokeObjectURL(t.src),i._openPromise.resolve({costtime:n})},r.addEventListener("sourceopen",n),this._url=URL.createObjectURL(r),t.src=this._url,e.abrupt("return",this._openPromise);case 13:case"end":return e.stop()}}),e,this)}))),function(e){return n.apply(this,arguments)})},{key:"unbindMedia",value:(r=g(v().mark((function e(){var t,r,n,i=this;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._openPromise.used||this._openPromise.resolve(),t=this.mediaSource){if(Object.keys(this._queue).forEach((function(e){var t=i._queue[e];t&&t.forEach((function(e){var t,r;return null===(t=e.promise)||void 0===t||null===(r=t.resolve)||void 0===r?void 0:r.call(t)}))})),r=!!this.media&&this.media.readyState>=1,n="open"===t.readyState,r&&n)try{t.endOfStream()}catch(s){}Object.keys(this._sourceBuffer).forEach((function(e){try{t.removeSourceBuffer(i._sourceBuffer[e])}catch(s){}}))}if(this.media){this.media.removeAttribute("src");try{this.media.load()}catch(s){}this.media=null}this.mediaSource=null,this._openPromise=P(),this._queue=Object.create(null),this._sourceBuffer=Object.create(null);case 8:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"createSource",value:function(e,t){if(!this._sourceBuffer[e]&&this.mediaSource){var r;try{r=this._sourceBuffer[e]=this.mediaSource.addSourceBuffer(t)}catch(n){throw new $(W,J.MSE_ADD_SB,n)}r.mimeType=t,r.addEventListener("updateend",this._onSBUpdateEnd.bind(this,e)),r.addEventListener("error",this._onSBUpdateError.bind(this,e))}}},{key:"changeType",value:function(e,t){var r=this,n=this._sourceBuffer[e];return this.mediaSource&&n&&n.mimeType!==t?"function"!=typeof n.changeType?Promise.reject():this._enqueueOp(e,(function(){n.changeType(t),n.mimeType=t,r._onSBUpdateEnd(e)}),"changeType",{mimeType:t}):Promise.resolve()}},{key:"createOrChangeSource",value:function(e,t){return this.createSource(e,t),this.changeType(e,t)}},{key:"append",value:function(e,t,r){var n=this;return t&&t.byteLength&&this._sourceBuffer[e]?this._enqueueOp(e,(function(){var i;n.mediaSource&&!n.media.error&&(n._logger.debug("MSE APPEND START",r),n._opst=B(),null===(i=n._sourceBuffer[e])||void 0===i||i.appendBuffer(t))}),ue,r):Promise.resolve()}},{key:"remove",value:function(e,t,r,n){var i=this,s=!1;return this._mseFullFlag[e]&&(s=!0),this._enqueueOp(e,(function(){if(i.mediaSource&&!i.media.error){var s=i._sourceBuffer[e];t>=r||!s?i._onSBUpdateEnd(e):(i._opst=B(),i._logger.debug("MSE REMOVE START",e,t,r,n),s.remove(t,r))}}),ce,n,s)}},{key:"clearBuffer",value:function(e,t){var r,n=this;return Object.keys(this._sourceBuffer).forEach((function(i){r=n._enqueueOp(i,(function(){if(n.mediaSource&&!n.media.error){var r=n._sourceBuffer[i];n._logger.debug("MSE clearBuffer START",i,e,t),r.remove(e,t)}}),ce)})),r||Promise.resolve()}},{key:"clearAllBuffer",value:function(){var e,t=this;return Object.keys(this._sourceBuffer).forEach((function(r){e=t._enqueueOp(r,(function(){if(t.mediaSource&&!t.media.error){var e=t._sourceBuffer[r];t._logger.debug("MSE clearAllBuffer START",r),e.remove(0,M.end(M.get(e)))}}))})),e}},{key:"clearOpQueues",value:function(e,t){var r;this._logger.debug("MSE clearOpQueue START");var n=this._queue[e];if(t&&n)this._queue[e]=[];else if(n&&n[e]&&!(n.length<5)){var i=[];n.forEach((function(e){e.context&&e.context.isinit&&i.push(e)})),this._queue[e]=n.slice(0,2),i.length>0&&(r=this._queue[e]).push.apply(r,i)}}},{key:"endOfStream",value:function(e){var t=this;return this.mediaSource&&"open"===this.mediaSource.readyState?this._enqueueBlockingOp((function(){var r=t.mediaSource;r&&"open"===r.readyState&&(t._logger.debug("MSE endOfStream START"),e?r.endOfStream(e):r.endOfStream())}),"endOfStream"):Promise.resolve()}},{key:"setLiveSeekableRange",value:function(e,t){var r=this.mediaSource;e<0||t<e||null==r||!r.setLiveSeekableRange||"open"!==r.readyState||r.setLiveSeekableRange(e,t)}},{key:"getSourceBuffer",value:function(e){return this._sourceBuffer[e]}},{key:"buffered",value:function(e){return M.get(this._sourceBuffer[e])}},{key:"bufferStart",value:function(e){return M.start(this.buffered(e))}},{key:"bufferEnd",value:function(e){return M.end(this.buffered(e))}},{key:"_enqueueOp",value:function(e,t,r,n,i){var s=this;if(!this.mediaSource)return Promise.resolve();var a=this._queue[e]=this._queue[e]||[],o={exec:t,promise:P(),opName:r,context:n};return i?(a.splice(0,0,o),this._mseFullFlag[e]=!1,this._startQueue(e)):a.push(o),this.isOpened||this.isEnded?1===a.length&&this._startQueue(e):this._openPromise.then((function(){1===a.length&&s._startQueue(e)})),o.promise}},{key:"_enqueueBlockingOp",value:(t=g(v().mark((function e(t,r,n){var i,s,a=this;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.mediaSource){e.next=2;break}return e.abrupt("return",Promise.resolve());case 2:if((i=Object.keys(this._sourceBuffer)).length){e.next=5;break}return e.abrupt("return",t());case 5:return s=[],i.forEach((function(e){var t=a._queue[e],i=P();s.push(i),t.push({exec:function(){i.resolve()},promise:i,opName:r,context:n}),1===t.length&&a._startQueue(e)})),e.abrupt("return",Promise.all(s).then((function(){try{return t()}finally{i.forEach((function(e){var t=a._queue[e],r=a._sourceBuffer[e];null==t||t.shift(),r&&r.updating||a._startQueue(e)}))}})));case 8:case"end":return e.stop()}}),e,this)}))),function(e,r,n){return t.apply(this,arguments)})},{key:"_startQueue",value:function(e){var t=this._queue[e];if(t){var r=t[0];if(r&&!this._mseFullFlag[e])try{r.exec()}catch(n){n&&n.message&&n.message.indexOf("SourceBuffer is full")>=0?(this._mseFullFlag[e]=!0,this._logger.error("[MSE error],  context,",r.context," ,name,",r.opName,",err,SourceBuffer is full"),r.promise.reject(new $(W,J.MSE_FULL,n))):(this._logger.error(n),r.promise.reject(new $(W,J.MSE_OTHER,n)),t.shift(),this._startQueue(e))}}}},{key:"setTimeoffset",value:function(e,t,r){var n=this;return this._enqueueOp(e,(function(){t<0&&(t+=.001),n._sourceBuffer[e].timestampOffset=t,n._onSBUpdateEnd(e)}),"setTimeoffset",r)}},{key:"abort",value:function(e,t){var r=this;return this.isOpened?this._enqueueOp(e,(function(){r._sourceBuffer[e].abort(),r._onSBUpdateEnd(e)}),"abort",t):Promise.resolve()}}],[{key:"getDefaultConfig",value:function(){return{openLog:!1}}},{key:"isSupported",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.42E01E,mp4a.40.2"';if(!oe)return!1;try{return oe.isTypeSupported(e)}catch(t){return this._logger.error(e,t),!1}}}]);var t,r,n}();x(de,"VIDEO","video"),x(de,"AUDIO","audio");var he="fetch",fe="xhr",pe="ws",ve="arraybuffer",me="text",ye="json",_e=function(e){function t(e,r,n,i){var s;return b(this,t),x(s=l(this,t,[i]),"retryCount",0),x(s,"isTimeout",!1),x(s,"loaderType",he),x(s,"startTime",0),x(s,"endTime",0),x(s,"options",{}),s.url=e,s.request=r,s.response=n,s}return E(t,e),S(t)}(D(Error)),ge=Object.prototype.toString;function be(e){if("[object Object]"!==ge.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function ke(e){if(e&&null!==e[0]&&void 0!==e[0]&&(0!==e[0]||null!==e[1]&&void 0!==e[1])){var t="bytes="+e[0]+"-";return e[1]&&(t+=e[1]),t}}function Se(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function xe(e,t){if(e){if(!t)return e;var r,n=Object.keys(t).map((function(e){if(null!=(r=t[e]))return Array.isArray(r)?e+="[]":r=[r],r.map((function(t){var r;return r=t,"[object Date]"===ge.call(r)?t=t.toISOString():function(e){return null!==e&&"object"===y(e)}(t)&&(t=JSON.stringify(t)),"".concat(Se(e),"=").concat(Se(t))})).join("&")})).filter(Boolean).join("&");if(n){var i=e.indexOf("#");-1!==i&&(e=e.slice(0,i)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}}function Ee(e,t,r,n,i,s,a,o,u,c,l){return i=null!=i?parseFloat(i):null,n=parseInt(n||"0",10),Number.isNaN(n)&&(n=0),{data:e,done:t,options:{range:u,vid:c,index:o,contentLength:n,age:i,startTime:s,firstByteTime:a,endTime:Date.now(),priOptions:l},response:r}}function we(e,t){return Math.round(8*e*1e3/t/1024)}var Te={ERROR:"error",TTFB:"core.ttfb",LOAD_START:"core.loadstart",LOAD_RESPONSE_HEADERS:"core.loadresponseheaders",LOAD_COMPLETE:"core.loadcomplete",LOAD_RETRY:"core.loadretry",SOURCEBUFFER_CREATED:"core.sourcebuffercreated",ANALYZE_DURATION_EXCEEDED:"core.analyzedurationexceeded",REMOVE_BUFFER:"core.removebuffer",BUFFEREOS:"core.buffereos",KEYFRAME:"core.keyframe",METADATA_PARSED:"core.metadataparsed",SEI:"core.sei",SEI_IN_TIME:"core.seiintime",FLV_SCRIPT_DATA:"core.flvscriptdata",LOWDECODE:"core.lowdecode",SWITCH_URL_SUCCESS:"core.switchurlsuccess",SWITCH_URL_FAILED:"core.switchurlfailed",SPEED:"core.speed",HLS_MANIFEST_LOADED:"core.hlsmanifestloaded",HLS_LEVEL_LOADED:"core.hlslevelloaded",DEMUXED_TRACK:"core.demuxedtrack",STREAM_EXCEPTION:"core.streamexception",LARGE_AV_FIRST_FRAME_GAP_DETECT:"LARGE_AV_FIRST_FRAME_GAP_DETECT",LARGE_VIDEO_DTS_GAP_DETECT:"LARGE_VIDEO_DTS_GAP_DETECT",LARGE_AUDIO_DTS_GAP_DETECT:"LARGE_AUDIO_DTS_GAP_DETECT",AUDIO_GAP_DETECT:"AUDIO_GAP_DETECT",AUDIO_OVERLAP_DETECT:"AUDIO_OVERLAP_DETECT",MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT:"MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT",REAL_TIME_SPEED:"real_time_speed"},De={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,n,s,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var o=new i(n,s||e,a),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],o]:e._events[u].push(o):(e._events[u]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,s=n.length,a=new Array(s);i<s;i++)a[i]=n[i].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var u,c,l=this._events[o],d=arguments.length;if(l.fn){switch(l.once&&this.removeListener(e,l.fn,void 0,!0),d){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,t),!0;case 3:return l.fn.call(l.context,t,n),!0;case 4:return l.fn.call(l.context,t,n,i),!0;case 5:return l.fn.call(l.context,t,n,i,s),!0;case 6:return l.fn.call(l.context,t,n,i,s,a),!0}for(c=1,u=new Array(d-1);c<d;c++)u[c-1]=arguments[c];l.fn.apply(l.context,u)}else{var h,f=l.length;for(c=0;c<f;c++)switch(l[c].once&&this.removeListener(e,l[c].fn,void 0,!0),d){case 1:l[c].fn.call(l[c].context);break;case 2:l[c].fn.call(l[c].context,t);break;case 3:l[c].fn.call(l[c].context,t,n);break;case 4:l[c].fn.call(l[c].context,t,n,i);break;default:if(!u)for(h=1,u=new Array(d-1);h<d;h++)u[h-1]=arguments[h];l[c].fn.apply(l[c].context,u)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||a(this,s);else{for(var u=0,c=[],l=o.length;u<l;u++)(o[u].fn!==t||i&&!o[u].once||n&&o[u].context!==n)&&c.push(o[u]);c.length?this._events[s]=1===c.length?c[0]:c:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o}(De);var Ae=De.exports,Ce=2097152,Le=function(e){function t(){var e;return b(this,t),x(e=l(this,t),"_abortController",null),x(e,"_timeoutTimer",null),x(e,"_reader",null),x(e,"_response",null),x(e,"_aborted",!1),x(e,"_index",-1),x(e,"_range",null),x(e,"_receivedLength",0),x(e,"_running",!1),x(e,"_logger",null),x(e,"_vid",""),x(e,"_onProcessMinLen",0),x(e,"_onCancel",null),x(e,"_priOptions",null),e}return E(t,e),S(t,[{key:"load",value:function(e){var t,r=this,n=e.url,i=e.vid,s=e.timeout,a=e.responseType,o=e.onProgress,u=e.index,c=e.onTimeout,l=e.onCancel,d=e.range,h=e.transformResponse,f=e.request,p=e.params,m=e.logger,y=e.method,_=e.headers,b=e.body,k=e.mode,S=e.credentials,x=e.cache,E=e.redirect,w=e.referrer,T=e.referrerPolicy,D=e.onProcessMinLen,A=e.priOptions;this._logger=m,this._aborted=!1,this._onProcessMinLen=D,this._onCancel=l,this._abortController="undefined"!=typeof AbortController&&new AbortController,this._running=!0,this._index=u,this._range=d||[0,0],this._vid=i||n,this._priOptions=A||{};var C={method:y,headers:_,body:b,mode:k,credentials:S,cache:x,redirect:E,referrer:w,referrerPolicy:T,signal:null===(t=this._abortController)||void 0===t?void 0:t.signal},L=!1;clearTimeout(this._timeoutTimer),n=xe(n,p);var O=ke(d);O&&(_=f?f.headers:C.headers=C.headers||(Headers?new Headers:{}),Headers&&_ instanceof Headers?_.append("Range",O):_.Range=O),s&&(this._timeoutTimer=setTimeout((function(){if(L=!0,r.cancel(),c){var e=new _e(n,C,null,"timeout");e.isTimeout=!0,c(e,{index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions})}}),s));var j=Date.now();return this._logger.debug("[fetch load start], index,",u,",range,",d),new Promise((function(e,t){fetch(f||n,f?void 0:C).then(function(){var i=g(v().mark((function i(s){var c,l,f,p;return v().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(clearTimeout(r._timeoutTimer),r._response=s,!r._aborted&&r._running){i.next=4;break}return i.abrupt("return");case 4:if(h&&(s=h(s,n)||s),s.ok){i.next=7;break}throw new _e(n,C,s,"bad network response");case 7:if(c=Date.now(),a!==me){i.next=15;break}return i.next=11,s.text();case 11:l=i.sent,r._running=!1,i.next=37;break;case 15:if(a!==ye){i.next=22;break}return i.next=18,s.json();case 18:l=i.sent,r._running=!1,i.next=37;break;case 22:if(!o){i.next=29;break}return r.resolve=e,r.reject=t,r._loadChunk(s,o,j,c),i.abrupt("return");case 29:return i.next=31,s.arrayBuffer();case 31:l=i.sent,l=new Uint8Array(l),r._running=!1,f=Date.now()-j,p=we(l.byteLength,f),r.emit(Te.REAL_TIME_SPEED,{speed:p,len:l.byteLength,time:f,vid:r._vid,index:r._index,range:r._range,priOptions:r._priOptions});case 37:r._logger.debug("[fetch load end], index,",u,",range,",d),e(Ee(l,!0,s,s.headers.get("Content-Length"),s.headers.get("age"),j,c,u,d,r._vid,r._priOptions));case 39:case"end":return i.stop()}}),i)})));return function(e){return i.apply(this,arguments)}}()).catch((function(e){var i;clearTimeout(r._timeoutTimer),r._running=!1,r._aborted&&!L||((e=e instanceof _e?e:new _e(n,C,null,null===(i=e)||void 0===i?void 0:i.message)).startTime=j,e.endTime=Date.now(),e.isTimeout=L,e.options={index:r._index,range:r._range,vid:r._vid,priOptions:r._priOptions},t(e))}))}))}},{key:"cancel",value:(r=g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._aborted){e.next=2;break}return e.abrupt("return");case 2:if(this._aborted=!0,this._running=!1,!this._response){e.next=14;break}if(e.prev=5,!this._reader){e.next=9;break}return e.next=9,this._reader.cancel();case 9:e.next=13;break;case 11:e.prev=11,e.t0=e.catch(5);case 13:this._response=this._reader=null;case 14:if(this._abortController){try{this._abortController.abort()}catch(t){}this._abortController=null}this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions});case 16:case"end":return e.stop()}}),e,this,[[5,11]])}))),function(){return r.apply(this,arguments)})},{key:"_loadChunk",value:function(e,t,r,n){var i=this;if(!e.body||!e.body.getReader){this._running=!1;var s=new _e(e.url,"",e,"onProgress of bad response.body.getReader");return s.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},void this.reject(s)}this._onProcessMinLen>0&&(this._cache=new Uint8Array(Ce),this._writeIdx=0);var a,o,u,c=this._reader=e.body.getReader(),l=function(){var s=g(v().mark((function s(){var d,h,f,p,m,y,_,g;return v().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:return o=Date.now(),s.prev=1,s.next=4,c.read();case 4:a=s.sent,console.log(a),u=Date.now(),s.next=14;break;case 9:return s.prev=9,s.t0=s.catch(1),u=Date.now(),i._aborted||(i._running=!1,s.t0.options={index:i._index,range:i._range,vid:i._vid,priOptions:i._priOptions},i.reject(s.t0)),s.abrupt("return");case 14:if(h=(null===(d=i._range)||void 0===d?void 0:d.length)>0?i._range[0]:0,f=h+i._receivedLength,!i._aborted){s.next=20;break}return i._running=!1,t(void 0,!1,{range:[f,f],vid:i._vid,index:i._index,startTime:o,endTime:u,st:r,firstByteTime:n,priOptions:i._priOptions},e),s.abrupt("return");case 20:p=a.value?a.value.byteLength:0,i._receivedLength+=p,i._logger.debug("【fetchLoader,onProgress call】,task,",i._range,", start,",f,", end,",h+i._receivedLength,", done,",a.done),i._onProcessMinLen>0?i._writeIdx+p>=i._onProcessMinLen||a.done?((m=new Uint8Array(i._writeIdx+p)).set(i._cache.slice(0,i._writeIdx),0),p>0&&m.set(a.value,i._writeIdx),i._writeIdx=0,i._logger.debug("【fetchLoader,onProgress enough】,done,",a.done,",len,",m.byteLength,", writeIdx,",i._writeIdx)):p>0&&i._writeIdx+p<Ce?(i._cache.set(a.value,i._writeIdx),i._writeIdx+=p,i._logger.debug("【fetchLoader,onProgress cache】,len,",p,", writeIdx,",i._writeIdx)):p>0&&(y=new Uint8Array(i._writeIdx+p+2048),i._logger.debug("【fetchLoader,onProgress extra start】,size,",i._writeIdx+p+2048,", datalen,",p,", writeIdx,",i._writeIdx),y.set(i._cache.slice(0,i._writeIdx),0),p>0&&y.set(a.value,i._writeIdx),i._writeIdx+=p,delete i._cache,i._cache=y,i._logger.debug("【fetchLoader,onProgress extra end】,len,",p,", writeIdx,",i._writeIdx)):m=a.value,(m&&m.byteLength>0||a.done)&&(console.log(m,"fetch"),t(m,a.done,{range:[i._range[0]+i._receivedLength-(m?m.byteLength:0),i._range[0]+i._receivedLength],vid:i._vid,index:i._index,startTime:o,endTime:u,st:r,firstByteTime:n,priOptions:i._priOptions},e)),a.done?(_=Date.now()-r,g=we(i._receivedLength,_),i.emit(Te.REAL_TIME_SPEED,{speed:g,len:i._receivedLength,time:_,vid:i._vid,index:i._index,range:i._range,priOptions:i._priOptions}),i._running=!1,i._logger.debug("[fetchLoader onProgress end],task,",i._range,",done,",a.done),i.resolve(Ee(a,!0,e,e.headers.get("Content-Length"),e.headers.get("age"),r,n,i._index,i._range,i._vid,i._priOptions))):l();case 26:case"end":return s.stop()}}),s,null,[[1,9]])})));return function(){return s.apply(this,arguments)}}();l()}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}}],[{key:"isSupported",value:function(){return!("undefined"==typeof fetch)}}]);var r}(Ae),Oe=2097152,je=function(e){function t(){var e;return b(this,t),x(e=l(this,t),"_socket",null),x(e,"_timeoutTimer",null),x(e,"_response",null),x(e,"_aborted",!1),x(e,"_index",-1),x(e,"_range",null),x(e,"_receivedLength",0),x(e,"_running",!1),x(e,"_logger",null),x(e,"_vid",""),x(e,"_onProcessMinLen",0),x(e,"_onCancel",null),x(e,"_priOptions",null),x(e,"_startTime",null),x(e,"_endTime",null),e}return E(t,e),S(t,[{key:"load",value:function(e){var t=this,r=e.url,n=e.vid,i=e.timeout,s=e.responseType,a=e.onProgress,o=e.index,u=e.onTimeout,c=e.onCancel,l=e.range;e.transformResponse,e.request;var d=e.params,h=e.logger;e.method,e.headers,e.body,e.mode,e.credentials,e.cache,e.redirect,e.referrer,e.referrerPolicy;var f=e.onProcessMinLen,p=e.priOptions;this._logger=h,this._aborted=!1,this._onProcessMinLen=f,this._onCancel=c,this._running=!0,this._index=o,this._range=l||[0,0],this._vid=n||r,this._priOptions=p||{};var v=!1;clearTimeout(this._timeoutTimer),r=xe(r,d),i&&(this._timeoutTimer=setTimeout((function(){if(v=!0,t.cancel(),u){var e=new _e(r,null,"timeout");e.isTimeout=!0,u(e,{index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions})}}),i));var m=Date.now();return new Promise((function(e,n){t._socket=new WebSocket(r),t._socket.binaryType=s,t._logger.debug("[websocket load start], index,",o,",ws,",t._socket),t._socket.onopen=function(){if(clearTimeout(t._timeoutTimer),!t._aborted&&t._running){t._logger.debug("[websocket connected],ws",t._socket),t._startTime=Date.now();var r=Date.now();t._socket.onmessage=function(i){if(t._startTime=t._endTime||t._startTime,t._endTime=Date.now(),!t._aborted&&t._running){var u;if(s===me)u=i.data,t._running=!1;else if(s===ye)u=JSON.parse(i.data),t._running=!1;else{if(a)return t.resolve=e,t.reject=n,void t._loadChunk(new Uint8Array(i.data),a,m,r);u=new Uint8Array(i.data),t._running=!1;var c=Date.now()-m,d=we(u.byteLength,c);t.emit(Te.REAL_TIME_SPEED,{speed:d,len:u.byteLength,time:c,vid:t._vid,index:t._index,range:t._range,priOptions:t._priOptions})}t._logger.debug("[websocket load end], index,",o,",range,",l),e(Ee(u,!0,null,null,null,m,r,o,l,t._vid,t._priOptions))}}}},t._socket.onclose=function(e){t._endTime=null,t._startTime=null},t._socket.onerror=function(e){var i;t._endTime=Date.now(),clearTimeout(t._timeoutTimer),t._running=!1,t._aborted&&!v||((e=new _e(r,null,null===(i=e)||void 0===i?void 0:i.message)).startTime=m,e.endTime=Date.now(),e.isTimeout=v,e.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},n(e))}}))}},{key:"cancel",value:(r=g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._aborted){e.next=2;break}return e.abrupt("return");case 2:if(this._aborted=!0,this._running=!1,this._socket){try{this._socket.close()}catch(t){}this._socket=null}this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions});case 6:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"_loadChunk",value:function(e,t,r,n,i){var s;this._onProcessMinLen>0&&(this._cache=new Uint8Array(Oe),this._writeIdx=0);var a=(null===(s=this._range)||void 0===s?void 0:s.length)>0?this._range[0]:0,o=a+this._receivedLength;if(this._aborted)return this._running=!1,void t(void 0,!1,{range:[o,o],vid:this._vid,index:this._index,startTime:this._startTime,endTime:this._endTime,st:r,firstByteTime:n,priOptions:this._priOptions});var u=e.byteLength;this._receivedLength+=u;var c,l=[2,3].indexOf(this._socket.readyState)>0;if(this._logger.debug("【WsLoader,onProgress call】,task,",this._range,", start,",o,", end,",a+this._receivedLength,", done,",l),this._onProcessMinLen>0){if(this._writeIdx+u>=this._onProcessMinLen||l)(c=new Uint8Array(this._writeIdx+u)).set(this._cache.slice(0,this._writeIdx),0),u>0&&c.set(e,this._writeIdx),this._writeIdx=0,this._logger.debug("【WsLoader,onProgress enough】,done,",l,",len,",c.byteLength,", writeIdx,",this._writeIdx);else if(u>0&&this._writeIdx+u<Oe)this._cache.set(e,this._writeIdx),this._writeIdx+=u,this._logger.debug("【WsLoader,onProgress cache】,len,",u,", writeIdx,",this._writeIdx);else if(u>0){var d=new Uint8Array(this._writeIdx+u+2048);this._logger.debug("【WsLoader,onProgress extra start】,size,",this._writeIdx+u+2048,", datalen,",u,", writeIdx,",this._writeIdx),d.set(this._cache.slice(0,this._writeIdx),0),u>0&&d.set(e,this._writeIdx),this._writeIdx+=u,delete this._cache,this._cache=d,this._logger.debug("【WsLoader,onProgress extra end】,len,",u,", writeIdx,",this._writeIdx)}}else c=e;if((c&&c.byteLength>0||l)&&t(c,l,{range:[this._range[0]+this._receivedLength-(c?c.byteLength:0),this._range[0]+this._receivedLength],vid:this._vid,index:this._index,startTime:this._startTime,endTime:this._endTime,st:r,firstByteTime:n,priOptions:this._priOptions},null),l){var h=Date.now()-r,f=we(this._receivedLength,h);this.emit(Te.REAL_TIME_SPEED,{speed:f,len:this._receivedLength,time:h,vid:this._vid,index:this._index,range:this._range,priOptions:this._priOptions}),this._running=!1,this._logger.debug("[WsLoader onProgress end],task,",this._range,",done,",!0),this.resolve(Ee(c,!0,null,null,null,r,n,this._index,this._range,this._vid,this._priOptions))}}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}}],[{key:"isSupported",value:function(){return!("undefined"==typeof WebSocket)}}]);var r}(Ae);var Re=function(e){function t(){var e;return b(this,t),x(e=l(this,t),"_xhr",null),x(e,"_aborted",!1),x(e,"_timeoutTimer",null),x(e,"_range",null),x(e,"_receivedLength",0),x(e,"_url",null),x(e,"_onProgress",null),x(e,"_index",-1),x(e,"_headers",null),x(e,"_currentChunkSizeKB",384),x(e,"_timeout",null),x(e,"_xhr",null),x(e,"_withCredentials",null),x(e,"_startTime",-1),x(e,"_loadCompleteResolve",null),x(e,"_loadCompleteReject",null),x(e,"_runing",!1),x(e,"_logger",!1),x(e,"_vid",""),x(e,"_responseType",void 0),x(e,"_credentials",void 0),x(e,"_method",void 0),x(e,"_transformResponse",void 0),x(e,"_firstRtt",void 0),x(e,"_onCancel",null),x(e,"_priOptions",null),e}return E(t,e),S(t,[{key:"load",value:function(e){var t=this;clearTimeout(this._timeoutTimer),this._logger=e.logger,this._range=e.range,this._onProgress=e.onProgress,this._index=e.index,this._headers=e.headers,this._withCredentials="include"===e.credentials||"same-origin"===e.credentials,this._body=e.body||null,e.method&&(this._method=e.method),this._timeout=e.timeout||null,this._runing=!0,this._vid=e.vid||e.url,this._responseType=e.responseType,this._firstRtt=-1,this._onTimeout=e.onTimeout,this._onCancel=e.onCancel,this._request=e.request,this._priOptions=e.priOptions||{},this._logger.debug("【xhrLoader task】, range",this._range),this._url=xe(e.url,e.params);var r=Date.now();return new Promise((function(e,r){t._loadCompleteResolve=e,t._loadCompleteReject=r,t._startLoad()})).catch((function(e){if(clearTimeout(t._timeoutTimer),t._runing=!1,!t._aborted)throw(e=e instanceof _e?e:new _e(t._url,t._request)).startTime=r,e.endTime=Date.now(),e.options={index:t._index,vid:t._vid,priOptions:t._priOptions},e}))}},{key:"_startLoad",value:function(){var e=null;if(this._responseType===ve&&this._range&&this._range.length>1)if(this._onProgress){this._firstRtt=-1;var t=1024*this._currentChunkSizeKB,r=this._range[0]+this._receivedLength,n=this._range[1];t<this._range[1]-r&&(n=r+t),e=[r,n],this._logger.debug("[xhr_loader->],tast :",this._range,", SubRange, ",e)}else e=this._range,this._logger.debug("[xhr_loader->],tast :",this._range,", allRange, ",e);this._internalOpen(e)}},{key:"_internalOpen",value:function(e){var t=this;try{this._startTime=Date.now();var r=this._xhr=new XMLHttpRequest;r.open(this._method||"GET",this._url,!0),r.responseType=this._responseType,this._timeout&&(r.timeout=this._timeout),r.withCredentials=this._withCredentials,r.onload=this._onLoad.bind(this),r.onreadystatechange=this._onReadyStatechange.bind(this),r.onerror=function(e){var r,n,i;t._running=!1;var s=new _e(t._url,t._request,null==e||null===(r=e.currentTarget)||void 0===r?void 0:r.response,"xhr.onerror.status:"+(null==e||null===(n=e.currentTarget)||void 0===n?void 0:n.status)+",statusText,"+(null==e||null===(i=e.currentTarget)||void 0===i?void 0:i.statusText));s.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(s)},r.ontimeout=function(e){t.cancel();var r=new _e(t._url,t._request,{status:408},"timeout");t._onTimeout&&(r.isTimeout=!0,t._onTimeout(r,{index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions})),r.options={index:t._index,range:t._range,vid:t._vid,priOptions:t._priOptions},t._loadCompleteReject(r)};var n=this._headers||{},i=ke(e);i&&(n.Range=i),n&&Object.keys(n).forEach((function(e){r.setRequestHeader(e,n[e])})),this._logger.debug("[xhr.send->] tast,",this._range,",load sub range, ",e),r.send(this._body)}catch(s){s.options={index:this._index,range:e,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(s)}}},{key:"_onReadyStatechange",value:function(e){2===e.target.readyState&&this._firstRtt<0&&(this._firstRtt=Date.now())}},{key:"_onLoad",value:function(e){var t,r=e.target.status;if(r<200||r>299){var n=new _e(this._url,null,p(p({},e.target.response),{},{status:r}),"bad response,status:"+r);return n.options={index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions},this._loadCompleteReject(n)}var i,s=null,a=!1,o=(null===(t=this._range)||void 0===t?void 0:t.length)>0?this._range[0]:0;if(this._responseType===ve){var u,c=new Uint8Array(e.target.response);if(i=o+this._receivedLength,c&&c.byteLength>0){this._receivedLength+=c.byteLength;var l=Date.now()-this._startTime,d=we(this._receivedLength,l);this.emit(Te.REAL_TIME_SPEED,{speed:d,len:this._receivedLength,time:l,vid:this._vid,index:this._index,range:[i,o+this._receivedLength],priOptions:this._priOptions})}s=c,a=!((null===(u=this._range)||void 0===u?void 0:u.length)>1&&this._range[1]&&this._receivedLength<this._range[1]-this._range[0]),this._logger.debug("[xhr load done->], tast :",this._range,", start",i,"end ",o+this._receivedLength,",dataLen,",c?c.byteLength:0,",receivedLength",this._receivedLength,",index,",this._index,", done,",a)}else a=!0,s=e.target.response;var h={ok:r>=200&&r<300,status:r,statusText:this._xhr.statusText,url:this._xhr.responseURL,headers:this._getHeaders(this._xhr),body:this._xhr.response};this._transformResponse&&(h=this._transformResponse(h,this._url)||h),this._onProgress&&this._onProgress(s,a,{index:this._index,vid:this._vid,range:[i,o+this._receivedLength],startTime:this._startTime,endTime:Date.now(),priOptions:this._priOptions},h),a?(this._runing=!1,this._loadCompleteResolve&&this._loadCompleteResolve(Ee(this._onProgress?null:s,a,h,h.headers["content-length"],h.headers.age,this._startTime,this._firstRtt,this._index,this._range,this._vid,this._priOptions))):this._startLoad()}},{key:"cancel",value:function(){if(!this._aborted)return this._aborted=!0,this._runing=!1,L(w(t.prototype),"removeAllListeners",this).call(this),this._onCancel&&this._onCancel({index:this._index,range:this._range,vid:this._vid,priOptions:this._priOptions}),this._xhr?this._xhr.abort():void 0}},{key:"receiveLen",get:function(){return this._receivedLength}},{key:"running",get:function(){return this._running},set:function(e){this._running=e}},{key:"_getHeaders",value:function(e){var t,r={},n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=R(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){o=!0,s=e},f:function(){try{a||null==r.return||r.return()}finally{if(o)throw s}}}}(e.getAllResponseHeaders().trim().split("\r\n"));try{for(n.s();!(t=n.n()).done;){var i=t.value.split(": ");r[i[0].toLowerCase()]=i.slice(1).join(": ")}}catch(s){n.e(s)}finally{n.f()}return r}}],[{key:"isSupported",value:function(){return"undefined"!=typeof XMLHttpRequest}}])}(Ae),Ie=["retry","retryDelay","onRetryError","transformError"],Pe=function(){return S((function e(t,r){b(this,e),this.promise=P(),this.alive=!!r.onProgress,!r.logger&&(r.logger=new ne("Loader")),this._loaderType=t,this._loader=t===he&&window.fetch?new Le:t===pe&&window.WebSocket?new je:new Re,this._config=r,this._retryCount=0,this._retryTimer=null,this._canceled=!1,this._retryCheckFunc=r.retryCheckFunc,this._logger=r.logger}),[{key:"exec",value:function(){var e=this,t=this._config,r=t.retry,n=t.retryDelay,i=t.onRetryError,s=t.transformError,a=A(t,Ie),o=function(){var t=g(v().mark((function t(){var u,c,l;return v().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e._loader.load(a);case 3:u=t.sent,e.promise.resolve(u),t.next=27;break;case 7:if(t.prev=7,t.t0=t.catch(0),e._loader.running=!1,e._logger.debug("[task request catch err]",t.t0),!e._canceled){t.next=13;break}return t.abrupt("return");case 13:if(t.t0.loaderType=e._loaderType,t.t0.retryCount=e._retryCount,c=t.t0,s&&(c=s(c)||c),i&&e._retryCount>0&&i(c,e._retryCount,{index:a.index,vid:a.vid,range:a.range,priOptions:a.priOptions}),e._retryCount++,l=!0,e._retryCheckFunc&&(l=e._retryCheckFunc(t.t0)),!(l&&e._retryCount<=r)){t.next=26;break}return clearTimeout(e._retryTimer),e._logger.debug("[task request setTimeout],retry",e._retryCount,",retry range,",a.range),e._retryTimer=setTimeout(o,n),t.abrupt("return");case 26:e.promise.reject(c);case 27:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(){return t.apply(this,arguments)}}();return o(),this.promise}},{key:"cancel",value:(e=g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return clearTimeout(this._retryTimer),this._canceled=!0,this._loader.running=!1,e.abrupt("return",this._loader.cancel());case 4:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"running",get:function(){return this._loader&&this._loader.running}},{key:"loader",get:function(){return this._loader}}]);var e}();function Be(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if((t=t.filter(Boolean)).length<2)return t[0];var n=new Uint8Array(t.reduce((function(e,t){return e+t.byteLength}),0)),i=0;return t.forEach((function(e){n.set(e,i),i+=e.byteLength})),n}function Ue(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return new Promise((function(t){return setTimeout(t,e)}))}var Me=function(e){function t(e){var r;return b(this,t),x(r=l(this,t,[e]),"type",he),x(r,"_queue",[]),x(r,"_alive",[]),x(r,"_currentTask",null),x(r,"_config",void 0),r._config=function(e){return p({loaderType:he,retry:0,retryDelay:0,timeout:0,request:null,onTimeout:void 0,onProgress:void 0,onRetryError:void 0,transformRequest:void 0,transformResponse:void 0,transformError:void 0,responseType:me,range:void 0,url:"",params:void 0,method:"GET",headers:{},body:void 0,mode:void 0,credentials:void 0,cache:void 0,redirect:void 0,referrer:void 0,referrerPolicy:void 0,integrity:void 0,onProcessMinLen:0},e)}(e),r._config.loaderType!==fe&&(Le.isSupported()||je.isSupported())||(r.type=fe),r.log=e.logger,r}return E(t,e),S(t,[{key:"isFetch",value:function(){return this.type===he}},{key:"isWs",value:function(){return this.type===pe}},{key:"isWebSocketURL",value:function(e){return/wss?:\/\/(.+?)/.test(e)}},{key:"load",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!1;"string"!=typeof e&&e?(r=e,n=!!this.isWebSocketURL(r.url)):(r.url=e||r.url||this._config.url,n=!!this.isWebSocketURL(r.url)),(this._config.loaderType===pe||n)&&je.isSupported()&&(this.type=pe,this._config.loaderType=r.loaderType=pe),(r=Object.assign({},this._config,r)).params&&(r.params=Object.assign({},r.params)),r.headers&&be(r.headers)&&(r.headers=Object.assign({},r.headers)),r.body&&be(r.body)&&(r.body=Object.assign({},r.body)),r.transformRequest&&(r=r.transformRequest(r)||r),r.logger=this.log;var i=new Pe(this.type,r);return i.loader.on(Te.REAL_TIME_SPEED,(function(e){t.emit(Te.REAL_TIME_SPEED,e)})),this._queue.push(i),1!==this._queue.length||this._currentTask&&this._currentTask.running||this._processTask(),i.promise}},{key:"cancel",value:(r=g(v().mark((function e(){var t;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this._queue.map((function(e){return e.cancel()})).concat(this._alive.map((function(e){return e.cancel()}))),this._currentTask&&t.push(this._currentTask.cancel()),this._queue=[],this._alive=[],e.next=6,Promise.all(t);case 6:return e.next=8,Ue();case 8:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"_processTask",value:function(){var e=this;if(this._currentTask=this._queue.shift(),this._currentTask){this._currentTask.alive&&this._alive.push(this._currentTask);var t=this._currentTask.exec().catch((function(e){}));t&&"function"==typeof t.finally&&t.finally((function(){var t,r;null!==(t=e._currentTask)&&void 0!==t&&t.alive&&(null===(r=e._alive)||void 0===r?void 0:r.length)>0&&(e._alive=e._alive.filter((function(t){return t&&t!==e._currentTask}))),e._processTask()}))}}}],[{key:"isFetchSupport",value:function(){return Le.isSupported()}},{key:"isWsSupport",value:function(){return je.isSupported()}}]);var r}(Ae),Ne=function(){return S((function e(){b(this,e),x(this,"_prevCurrentTime",0)}),[{key:"do",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=arguments.length>2?arguments[2]:void 0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;if(e){var i=e.currentTime,s=0;if(this._prevCurrentTime===i){var a=M.info(M.get(e),i);if(!a.buffers.length)return;r&&a.nextStart||a.nextStart&&a.nextStart-i<t?s=a.nextStart+.1:a.end&&a.end-i>n&&!e.seeking&&(s=i+.1)}this._prevCurrentTime=i,s&&i!==s&&(e.currentTime=s)}}}])}(),Fe=function(){return S((function e(t){var r=this;b(this,e),x(this,"_seiSet",new Set),this.emitter=t,t.on(Te.SEI,(function(e){e&&r._seiSet.add(e)}))}),[{key:"throw",value:function(e){var t=this;if(null!=e&&this._seiSet.size){var r=e-.2,n=e+.2,i=[];this._seiSet.forEach((function(e){e.time>=r&&e.time<=n&&i.push(e)})),i.forEach((function(e){t._seiSet.delete(e),t.emitter.emit(Te.SEI_IN_TIME,e)}))}}},{key:"reset",value:function(){this._seiSet.clear()}}])}(),Ve=function(){return S((function e(){b(this,e),x(this,"_chunkSpeeds",[]),x(this,"_speeds",[])}),[{key:"addRecord",value:function(e,t){e&&t&&(this._speeds.push(8e3*e/t),this._speeds=this._speeds.slice(-3))}},{key:"addChunkRecord",value:function(e,t){e&&t&&(this._chunkSpeeds.push(8e3*e/t),this._chunkSpeeds=this._chunkSpeeds.slice(-100))}},{key:"getAvgSpeed",value:function(){return this._chunkSpeeds.length||this._speeds.length?this._speeds.length?this._speeds.reduce((function(e,t){return e+t}))/this._speeds.length:this._chunkSpeeds.reduce((function(e,t){return e+t}))/this._chunkSpeeds.length:0}},{key:"getLatestSpeed",value:function(){return this._chunkSpeeds.length||this._speeds.length?this._speeds.length?this._speeds[this._speeds.length-1]:this._chunkSpeeds[this._chunkSpeeds.length-1]:0}},{key:"reset",value:function(){this._chunkSpeeds=[],this._speeds=[]}}])}(),ze=function(){return S((function e(t){b(this,e),x(this,"encodeType",""),x(this,"audioCodec",""),x(this,"videoCodec",""),x(this,"domain",""),x(this,"fps",0),x(this,"bitrate",0),x(this,"width",0),x(this,"height",0),x(this,"samplerate",0),x(this,"channelCount",0),x(this,"gop",0),x(this,"_bitsAccumulateSize",0),x(this,"_bitsAccumulateDuration",0),this._timescale=t}),[{key:"getStats",value:function(){return{encodeType:this.encodeType,audioCodec:this.audioCodec,videoCodec:this.videoCodec,domain:this.domain,fps:this.fps,bitrate:this.bitrate,width:this.width,height:this.height,samplerate:this.samplerate,channelCount:this.channelCount,gop:this.gop}}},{key:"setEncodeType",value:function(e){this.encodeType=e}},{key:"setFpsFromScriptData",value:function(e){var t,r=e.data,n=null==r||null===(t=r.onMetaData)||void 0===t?void 0:t.framerate;n&&n>0&&n<100&&(this.fps=n)}},{key:"setVideoMeta",value:function(e){if(this.width=e.width,this.height=e.height,this.videoCodec=e.codec,this.encodeType=e.codecType,e.fpsNum&&e.fpsDen){var t=e.fpsNum/e.fpsDen;t>0&&t<100&&(this.fps=t)}}},{key:"setAudioMeta",value:function(e){this.audioCodec=e.codec,this.samplerate=e.sampleRate,this.channelCount=e.channelCount}},{key:"setDomain",value:function(e){this.domain=e.split("/").slice(2,3)[0]}},{key:"updateBitrate",value:function(e){var t=this;if((!this.fps||this.fps>=100)&&e.length){var r=e.reduce((function(e,t){return e+t.duration}),0)/e.length;this.fps=Math.round(this._timescale/r)}e.forEach((function(e){1===e.gopId&&t.gop++,t._bitsAccumulateDuration+=e.duration/(t._timescale/1e3),t._bitsAccumulateSize+=e.units.reduce((function(e,t){return e+t.length}),0),t._bitsAccumulateDuration>=1e3&&(t.bitrate=8*t._bitsAccumulateSize,t._bitsAccumulateDuration=0,t._bitsAccumulateSize=0)}))}}])}(),qe=function(){return S((function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;b(this,e),x(this,"_core",null),x(this,"_samples",[]),this._core=t,this._timescale=r,this._stats=new ze(r),this._bindEvents()}),[{key:"getStats",value:function(){var e,t,r,n,i,s,a=(null===(e=this._core)||void 0===e?void 0:e.media)||{},o=a.currentTime,u=void 0===o?0:o,c=a.decodeFps,l=void 0===c?0:c;return p(p({},this._stats.getStats()),{},{downloadSpeed:(null===(t=this._core)||void 0===t||null===(r=t.speedInfo)||void 0===r?void 0:r.call(t).speed)||0,avgSpeed:(null===(n=this._core)||void 0===n||null===(i=n.speedInfo)||void 0===i?void 0:i.call(n).avgSpeed)||0,currentTime:u,bufferEnd:(null===(s=this._core)||void 0===s||null===(s=s.bufferInfo())||void 0===s?void 0:s.remaining)||0,decodeFps:l})}},{key:"_bindEvents",value:function(){var e=this;this._core.on(Te.DEMUXED_TRACK,(function(t){var r=t.videoTrack;return e._stats.updateBitrate(r.samples)})),this._core.on(Te.FLV_SCRIPT_DATA,(function(t){e._stats.setFpsFromScriptData(t)})),this._core.on(Te.METADATA_PARSED,(function(t){"video"===t.type?e._stats.setVideoMeta(t.track):e._stats.setAudioMeta(t.track)})),this._core.on(Te.TTFB,(function(t){e._stats.setDomain(t.responseUrl)}))}},{key:"reset",value:function(){this._samples=[],this._stats=new ze(this._timescale)}}])}(),Ge="video",He="audio",Ke="metadata",We="avc",Ye="hevc",Xe="aac",Qe="g7110a",Je="g7110m",Ze="LARGE_AV_SHIFT",$e="LARGE_VIDEO_GAP",et="LARGE_VIDEO_GAP_BETWEEN_CHUNK",tt="LARGE_AUDIO_GAP",rt="AUDIO_FILLED",nt="AUDIO_DROPPED",it=function(){return S((function e(){b(this,e),x(this,"id",1),x(this,"type",Ge),x(this,"codecType",We),x(this,"pid",-1),x(this,"hvcC",void 0),x(this,"codec",""),x(this,"timescale",0),x(this,"formatTimescale",0),x(this,"sequenceNumber",0),x(this,"baseMediaDecodeTime",0),x(this,"baseDts",0),x(this,"duration",0),x(this,"warnings",[]),x(this,"samples",[]),x(this,"pps",[]),x(this,"sps",[]),x(this,"vps",[]),x(this,"fpsNum",0),x(this,"fpsDen",0),x(this,"sarRatio",[]),x(this,"width",0),x(this,"height",0),x(this,"nalUnitSize",4),x(this,"present",!1),x(this,"isVideoEncryption",!1),x(this,"isAudioEncryption",!1),x(this,"isVideo",!0),x(this,"kid",null),x(this,"pssh",null),x(this,"ext",void 0)}),[{key:"reset",value:function(){this.sequenceNumber=this.width=this.height=this.fpsDen=this.fpsNum=this.duration=this.baseMediaDecodeTime=this.timescale=0,this.codec="",this.present=!1,this.pid=-1,this.pps=[],this.sps=[],this.vps=[],this.sarRatio=[],this.samples=[],this.warnings=[],this.hvcC=null}},{key:"exist",value:function(){return!!(this.pps.length&&this.sps.length&&this.codec)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isVideoEncryption}}])}(),st=function(){return S((function e(){b(this,e),x(this,"id",2),x(this,"type",He),x(this,"codecType",Xe),x(this,"pid",-1),x(this,"codec",""),x(this,"sequenceNumber",0),x(this,"sampleDuration",0),x(this,"timescale",0),x(this,"formatTimescale",0),x(this,"baseMediaDecodeTime",0),x(this,"duration",0),x(this,"warnings",[]),x(this,"samples",[]),x(this,"baseDts",0),x(this,"sampleSize",16),x(this,"sampleRate",0),x(this,"channelCount",0),x(this,"objectType",0),x(this,"sampleRateIndex",0),x(this,"config",[]),x(this,"present",!1),x(this,"isVideoEncryption",!1),x(this,"isAudioEncryption",!1),x(this,"kid",null),x(this,"ext",void 0)}),[{key:"reset",value:function(){this.sequenceNumber=0,this.timescale=0,this.sampleDuration=0,this.sampleRate=0,this.channelCount=0,this.baseMediaDecodeTime=0,this.present=!1,this.pid=-1,this.codec="",this.samples=[],this.config=[],this.warnings=[]}},{key:"exist",value:function(){return!!(this.sampleRate&&this.channelCount&&this.codec&&this.codecType===Xe)}},{key:"hasSample",value:function(){return!!this.samples.length}},{key:"isEncryption",get:function(){return this.isAudioEncryption}}])}(),at=function(){return S((function e(t,r,n){b(this,e),x(this,"flag",{}),x(this,"keyframe",!1),x(this,"gopId",0),x(this,"duration",0),x(this,"size",0),x(this,"units",[]),x(this,"chromaFormat",420),this.originPts=this.pts=t,this.originDts=this.dts=r,n&&(this.units=n)}),[{key:"cts",get:function(){return this.pts-this.dts}},{key:"setToKeyframe",value:function(){this.keyframe=!0,this.flag.dependsOn=2,this.flag.isNonSyncSample=0}}])}(),ot=S((function e(t,r,n,i){b(this,e),x(this,"duration",1024),x(this,"flag",{dependsOn:2,isNonSyncSample:0}),x(this,"keyframe",!0),this.originPts=this.pts=this.dts=t,this.data=r,this.size=r.byteLength,this.sampleOffset=i,n&&(this.duration=n)})),ut=S((function e(t,r){b(this,e),x(this,"time",0),this.data=t,this.originPts=this.pts=r})),ct=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t)}(ut),lt=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t)}(ut),dt=function(){return S((function e(){b(this,e),x(this,"id",3),x(this,"type",Ke),x(this,"timescale",0),x(this,"flvScriptSamples",[]),x(this,"seiSamples",[])}),[{key:"exist",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length||!this.timescale)}},{key:"reset",value:function(){this.timescale=0,this.flvScriptSamples=[],this.seiSamples=[]}},{key:"hasSample",value:function(){return!(!this.flvScriptSamples.length&&!this.seiSamples.length)}}])}(),ht=function(){return S((function e(t){if(b(this,e),x(this,"_bytesAvailable",void 0),x(this,"_bitsAvailable",0),x(this,"_word",0),!t)throw new Error("ExpGolomb data params is required");this._data=t,this._bytesAvailable=t.byteLength,this._bytesAvailable&&this._loadWord()}),[{key:"_loadWord",value:function(){var e=this._data.byteLength-this._bytesAvailable,t=Math.min(4,this._bytesAvailable);if(0===t)throw new Error("No bytes available");var r=new Uint8Array(4);r.set(this._data.subarray(e,e+t)),this._word=new DataView(r.buffer).getUint32(0),this._bitsAvailable=8*t,this._bytesAvailable-=t}},{key:"skipBits",value:function(e){if(this._bitsAvailable>e)this._word<<=e,this._bitsAvailable-=e;else{e-=this._bitsAvailable;var t=Math.floor(e/8);e-=8*t,this._bytesAvailable-=t,this._loadWord(),this._word<<=e,this._bitsAvailable-=e}}},{key:"readBits",value:function(e){if(e>32)throw new Error("Cannot read more than 32 bits");var t=Math.min(this._bitsAvailable,e),r=this._word>>>32-t;return this._bitsAvailable-=t,this._bitsAvailable>0?this._word<<=t:this._bytesAvailable>0&&this._loadWord(),(t=e-t)>0&&this._bitsAvailable?r<<t|this.readBits(t):r}},{key:"skipLZ",value:function(){var e;for(e=0;e<this._bitsAvailable;++e)if(0!=(this._word&2147483648>>>e))return this._word<<=e,this._bitsAvailable-=e,e;return this._loadWord(),e+this.skipLZ()}},{key:"skipUEG",value:function(){this.skipBits(1+this.skipLZ())}},{key:"readUEG",value:function(){var e=this.skipLZ();return this.readBits(e+1)-1}},{key:"readEG",value:function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}},{key:"readBool",value:function(){return 1===this.readBits(1)}},{key:"readUByte",value:function(){return this.readBits(8)}},{key:"skipScalingList",value:function(e){for(var t=8,r=8,n=0;n<e;n++)0!==r&&(r=(t+this.readEG()+256)%256),t=0===r?t:r}}])}(),ft=function(){function e(t){b(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]")}return S(e,[{key:"warn",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).warn.apply(t,[this._prefix].concat(n))}}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}])}();x(ft,"disabled",!0);var pt=function(){function e(){b(this,e)}return S(e,null,[{key:"decode",value:function(t){for(var r=[],n=t,i=0,s=t.length;i<s;)if(n[i]<128)r.push(String.fromCharCode(n[i])),++i;else{if(n[i]<192);else if(n[i]<224){if(e._checkContinuation(n,i,1)){var a=(31&n[i])<<6|63&n[i+1];if(a>=128){r.push(String.fromCharCode(65535&a)),i+=2;continue}}}else if(n[i]<240){if(e._checkContinuation(n,i,2)){var o=(15&n[i])<<12|(63&n[i+1])<<6|63&n[i+2];if(o>=2048&&55296!=(63488&o)){r.push(String.fromCharCode(65535&o)),i+=3;continue}}}else if(n[i]<248&&e._checkContinuation(n,i,3)){var u=(7&n[i])<<18|(63&n[i+1])<<12|(63&n[i+2])<<6|63&n[i+3];if(u>65536&&u<1114112){u-=65536,r.push(String.fromCharCode(u>>>10|55296)),r.push(String.fromCharCode(1023&u|56320)),i+=4;continue}}r.push(String.fromCharCode(65533)),++i}return r.join("")}},{key:"_checkContinuation",value:function(e,t,r){var n=e;if(t+r<n.length){for(;r--;)if(128!=(192&n[++t]))return!1;return!0}return!1}}])}(),vt="undefined"!=typeof window,mt=vt&&navigator.userAgent.toLocaleLowerCase(),yt=vt&&/^((?!chrome|android).)*safari/.test(mt),_t=vt&&mt.includes("firefox"),gt=vt&&mt.includes("android");function bt(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];t=t.filter(Boolean);var n=new Uint8Array(t.reduce((function(e,t){return e+t.byteLength}),0)),i=0;return t.forEach((function(e){n.set(e,i),i+=e.byteLength})),n}var kt=Math.pow(2,32);function St(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<8)+(e[t+1]||0)}function xt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<24>>>0)+(e[t+1]<<16)+(e[t+2]<<8)+(e[t+3]||0)}function Et(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return xt(e,t)*kt+xt(e,t+4)}function wt(e){for(var t,r="avc1.",n=0;n<3;n++)(t=e[n].toString(16)).length<2&&(t="0".concat(t)),r+=t;return r}function Tt(e){if(!Array.isArray(e)){for(var t=[],r="",n=0;n<e.length;n++)n%2&&(r=e[n-1]+e[n],t.push(parseInt(r,16)),r="");return t}return e.map((function(e){return parseInt(e,16)}))}var Dt=function(){return S((function e(){b(this,e)}),null,[{key:"parseAnnexB",value:function(e){for(var t=e.length,r=2,n=0;null!==e[r]&&void 0!==e[r]&&1!==e[r];)r++;if((n=++r+2)>=t)return[];for(var i=[];n<t;)switch(e[n]){case 0:if(0!==e[n-1]){n+=2;break}if(0!==e[n-2]){n++;break}r!==n-2&&i.push(e.subarray(r,n-2));do{n++}while(1!==e[n]&&n<t);n=(r=n+1)+2;break;case 1:if(0!==e[n-1]||0!==e[n-2]){n+=3;break}r!==n-2&&i.push(e.subarray(r,n-2)),n=(r=n+1)+2;break;default:n+=3}return r<t&&i.push(e.subarray(r)),i}},{key:"parseAvcC",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;if(!(e.length<4)){for(var r,n=e.length,i=[],s=0;s+t<n;)if(r=xt(e,s),3===t&&(r>>>=8),s+=t,r){if(s+r>n)break;i.push(e.subarray(s,s+r)),s+=r}return i}}},{key:"parseSEI",value:function(e,t){for(var r=e.length,n=t?2:1,i=0,s=0,a="";255===e[n];)i+=255,n++;for(i+=e[n++];255===e[n];)s+=255,n++;if(s+=e[n++],5===i&&r>n+16)for(var o=0;o<16;o++)a+=e[n].toString(16),n++;return{payload:e.subarray(n),type:i,size:s,uuid:a}}},{key:"removeEPB",value:function(e){for(var t=e.byteLength,r=[],n=1;n<t-2;)0===e[n]&&0===e[n+1]&&3===e[n+2]?(r.push(n+2),n+=2):n++;if(!r.length)return e;var i=t-r.length,s=new Uint8Array(i),a=0;for(n=0;n<i;a++,n++)a===r[0]&&(a++,r.shift()),s[n]=e[a];return s}}])}(),At=function(){function e(){b(this,e)}return S(e,null,[{key:"parseAVCDecoderConfigurationRecord",value:function(t){if(!(t.length<7)){for(var r,n,i=1+(3&t[4]),s=[],a=[],o=6,u=31&t[5],c=0;c<u;c++)if(n=t[o]<<8|t[o+1],o+=2,n){var l=t.subarray(o,o+n);o+=n,s.push(l),r||(r=e.parseSPS(Dt.removeEPB(l)))}var d,h=t[o];o++;for(var f=0;f<h;f++)d=t[o]<<8|t[o+1],o+=2,d&&(a.push(t.subarray(o,o+d)),o+=d);return{sps:r,spsArr:s,ppsArr:a,nalUnitSize:i}}}},{key:"parseSPS",value:function(e){var t=new ht(e);t.readUByte();var r=t.readUByte(),n=t.readUByte(),i=t.readUByte();t.skipUEG();var s=420;if(100===r||110===r||122===r||244===r||44===r||83===r||86===r||118===r||128===r||138===r||144===r){var a=t.readUEG();if(a<=3&&(s=[0,420,422,444][a]),3===a&&t.skipBits(1),t.skipUEG(),t.skipUEG(),t.skipBits(1),t.readBool())for(var o=3!==a?8:12,u=0;u<o;u++)t.readBool()&&(u<6?t.skipScalingList(16):t.skipScalingList(64))}t.skipUEG();var c=t.readUEG();if(0===c)t.readUEG();else if(1===c){t.skipBits(1),t.skipUEG(),t.skipUEG();for(var l=t.readUEG(),d=0;d<l;d++)t.skipUEG()}t.skipUEG(),t.skipBits(1);var h=t.readUEG(),f=t.readUEG(),p=t.readBits(1);0===p&&t.skipBits(1),t.skipBits(1);var v,m,y,_,g,b=0,k=0,S=0,x=0;if(t.readBool()&&(b=t.readUEG(),k=t.readUEG(),S=t.readUEG(),x=t.readUEG()),t.readBool()){if(t.readBool())switch(t.readUByte()){case 1:v=[1,1];break;case 2:v=[12,11];break;case 3:v=[10,11];break;case 4:v=[16,11];break;case 5:v=[40,33];break;case 6:v=[24,11];break;case 7:v=[20,11];break;case 8:v=[32,11];break;case 9:v=[80,33];break;case 10:v=[18,11];break;case 11:v=[15,11];break;case 12:v=[64,33];break;case 13:v=[160,99];break;case 14:v=[4,3];break;case 15:v=[3,2];break;case 16:v=[2,1];break;case 255:v=[t.readUByte()<<8|t.readUByte(),t.readUByte()<<8|t.readUByte()]}if(t.readBool()&&t.readBool(),t.readBool()&&(t.readBits(4),t.readBool()&&t.readBits(24)),t.readBool()&&(t.readUEG(),t.readUEG()),t.readBool()){var E=t.readBits(32),w=t.readBits(32);m=t.readBool(),g=(y=w)/(_=2*E)}}return{codec:wt(e.subarray(1,4)),profileIdc:r,profileCompatibility:n,levelIdc:i,chromaFormat:s,width:Math.ceil(16*(h+1)-2*(b+k)),height:(2-p)*(f+1)*16-(p?2:4)*(S+x),sarRatio:v,fpsNum:y,fpsDen:_,fps:g,fixedFrame:m}}}])}(),Ct=function(){function e(){b(this,e)}return S(e,null,[{key:"getRateIndexByRate",value:function(t){return e.FREQ.indexOf(t)}},{key:"parseADTS",value:function(t,r){for(var n=t.length,i=0;i+2<n&&(255!==t[i]||240!=(246&t[i+1]));)i++;if(!(i>=n)){var s=i,a=[],o=(60&t[i+2])>>>2,u=e.FREQ[o];if(!u)throw new Error("Invalid sampling index: ".concat(o));for(var c,l,d=1+((192&t[i+2])>>>6),h=(1&t[i+2])<<2|(192&t[i+3])>>>6,f=e._getConfig(o,h,d),p=f.config,v=f.codec,m=0,y=e.getFrameDuration(u);i+7<n;)if(255===t[i]&&240==(246&t[i+1])){if(n-i<(l=(3&t[i+3])<<11|t[i+4]<<3|(224&t[i+5])>>5))break;c=2*(1&~t[i+1]),a.push({pts:r+m*y,data:t.subarray(i+7+c,i+l)}),m++,i+=l}else i++;return{skip:s,remaining:i>=n?void 0:t.subarray(i),frames:a,samplingFrequencyIndex:o,sampleRate:u,objectType:d,channelCount:h,codec:v,config:p,originCodec:"mp4a.40.".concat(d)}}}},{key:"parseAudioSpecificConfig",value:function(t){if(t.length){var r=t[0]>>>3,n=(7&t[0])<<1|t[1]>>>7,i=(120&t[1])>>>3,s=e.FREQ[n];if(s){var a=e._getConfig(n,i,r);return{samplingFrequencyIndex:n,sampleRate:s,objectType:r,channelCount:i,config:a.config,codec:a.codec,originCodec:"mp4a.40.".concat(r)}}}}},{key:"getFrameDuration",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4;return 1024*t/e}},{key:"_getConfig",value:function(e,t,r){var n,i,s=[];return _t?e>=6?(n=5,i=e-3):(n=2,i=e):gt?(n=2,i=e):(n=2===r||5===r?r:5,i=e,e>=6?i=e-3:1===t&&(n=2,i=e)),s[0]=n<<3,s[0]|=(14&e)>>1,s[1]=(1&e)<<7,s[1]|=t<<3,5===n&&(s[1]|=(14&i)>>1,s[2]=(1&i)<<7,s[2]|=8,s[3]=0),{config:s,codec:"mp4a.40.".concat(n)}}},{key:"getSilentFrame",value:function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}}}])}();x(Ct,"FREQ",[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350]);var Lt=function(){function e(){b(this,e)}return S(e,null,[{key:"parseHEVCDecoderConfigurationRecord",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(t.length<23)){r=r||{};for(var n,i,s,a,o,u=1+(3&t[21]),c=[],l=[],d=[],h=23,f=t[22],p=0;p<f;p++){s=63&t[h],a=t[h+1]<<8|t[h+2],h+=3;for(var v=0;v<a;v++)if(o=t[h]<<8|t[h+1],h+=2,o){switch(s){case 32:var m=t.subarray(h,h+o);n||(n=e.parseVPS(Dt.removeEPB(m),r)),d.push(m);break;case 33:var y=t.subarray(h,h+o);i||(i=e.parseSPS(Dt.removeEPB(y),r)),c.push(y);break;case 34:l.push(t.subarray(h,h+o))}h+=o}}return{hvcC:r,sps:i,spsArr:c,ppsArr:l,vpsArr:d,nalUnitSize:u}}}},{key:"parseVPS",value:function(t,r){r=r||{};var n=new ht(t);n.readUByte(),n.readUByte(),n.readBits(12);var i=n.readBits(3);return r.numTemporalLayers=Math.max(r.numTemporalLayers||0,i+1),n.readBits(17),e._parseProfileTierLevel(n,i,r),r}},{key:"parseSPS",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r=r||{};var n=new ht(t);n.readUByte(),n.readUByte(),n.readBits(4);var i=n.readBits(3);r.numTemporalLayers=Math.max(i+1,r.numTemporalLayers||0),r.temporalIdNested=n.readBits(1),e._parseProfileTierLevel(n,i,r),n.readUEG();var s=r.chromaFormatIdc=n.readUEG(),a=420;s<=3&&(a=[0,420,422,444][s]);var o=0;3===s&&(o=n.readBits(1));var u,c,l,d,h=n.readUEG(),f=n.readUEG(),p=n.readBits(1);if(1===p&&(u=n.readUEG(),c=n.readUEG(),l=n.readUEG(),d=n.readUEG()),r.bitDepthLumaMinus8=n.readUEG(),r.bitDepthChromaMinus8=n.readUEG(),1===p){var v=1!==s&&2!==s||0!==o?1:2,m=1===s&&0===o?2:1;h-=v*(c+u),f-=m*(d+l)}return{codec:"hev1.1.6.L93.B0",width:h,height:f,chromaFormat:a,hvcC:r}}},{key:"_parseProfileTierLevel",value:function(e,t,r){var n=r.generalTierFlag||0;r.generalProfileSpace=e.readBits(2),r.generalTierFlag=Math.max(e.readBits(1),n),r.generalProfileIdc=Math.max(e.readBits(5),r.generalProfileIdc||0),r.generalProfileCompatibilityFlags=e.readBits(32),r.generalConstraintIndicatorFlags=[e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8),e.readBits(8)];var i=e.readBits(8);n<r.generalTierFlag?r.generalLevelIdc=i:r.generalLevelIdc=Math.max(i,r.generalLevelIdc||0);for(var s=[],a=[],o=0;o<t;o++)s[o]=e.readBits(1),a[o]=e.readBits(1);t>0&&e.readBits(2*(8-t));for(var u=0;u<t;u++)0!==s[u]&&(e.readBits(2),e.readBits(1),e.readBits(5),e.readBits(16),e.readBits(16),e.readBits(4),e.readBits(16),e.readBits(16),e.readBits(12)),0!==a[u]&&e.readBits(8)}}])}(),Ot=1e3,jt=5e3,Rt=function(){return S((function e(t,r,n){b(this,e),this.videoTrack=t,this.audioTrack=r,this.metadataTrack=n,this._baseDts=-1,this._baseDtsInited=!1,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastVideoDuration=0,this._keyFrameInNextChunk=!1,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0}),[{key:"fix",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t=Math.round(1e3*t);var i=this.videoTrack,s=this.audioTrack;!r&&n||(this._videoLastSample=null,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=0,this._videoTimestampBreak=0,this._lastAudioExceptionGapDot=-1/0,this._lastAudioExceptionOverlapDot=-1/0,this._lastAudioExceptionLargeGapDot=-1/0,this._lastVideoExceptionLargeGapDot=-1/0,this._lastVideoExceptionChunkFirstDtsDot=-1/0),r&&!n&&(this._baseDtsInited=!1),this._baseDtsInited||this._calculateBaseDts(s,i),!n&&t&&(this._audioNextPts=this._videoNextDts=t);var a=this._baseDtsInited&&(this._videoTimestampBreak||!this.videoTrack.exist())&&(this._audioTimestampBreak||!this.audioTrack.exist());if(a&&this._resetBaseDtsWhenStreamBreaked(),this._fixAudio(s),this._keyFrameInNextChunk=!1,this._fixVideo(i),this.metadataTrack.exist()){var o=this.metadataTrack.timescale;this.metadataTrack.seiSamples.forEach((function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/o})),this.metadataTrack.flvScriptSamples.forEach((function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/o}))}i.samples.length&&(i.baseMediaDecodeTime=i.samples[0].dts),s.samples.length&&(s.baseMediaDecodeTime=s.samples[0].pts*s.timescale/1e3)}},{key:"_fixVideo",value:function(e){var t=this,r=e.samples;if(r.length){var n;if(r.forEach((function(e){e.dts-=t._baseDts,e.pts-=t._baseDts,e.keyframe&&(t._keyFrameInNextChunk=!0)})),e.fpsNum&&e.fpsDen)n=e.timescale*(e.fpsDen/e.fpsNum);else if(e.length>1){var i=e.samples[0],s=e.samples[r.length-1];n=Math.floor((s.dts-i.dts)/(r.length-1))}else n=this._lastVideoDuration||40;var a=r.pop();if(this._videoLastSample&&r.unshift(this._videoLastSample),this._videoLastSample=a,r.length){if(void 0===this._videoNextDts){var o=r[0];this._videoNextDts=o.dts}var u=r.length,c=0,l=r[0],d=this._videoNextDts-l.dts;if(Math.abs(d)>200){var h;if(Math.abs(l.dts-this._lastVideoExceptionChunkFirstDtsDot)>5e3)this._lastVideoExceptionChunkFirstDtsDot=l.dts,e.warnings.push({type:et,nextDts:this._videoNextDts,firstSampleDts:l.dts,nextSampleDts:null===(h=r[1])||void 0===h?void 0:h.dts,sampleDuration:d});this._videoTimestampBreak>=5?(this._videoNextDts=l.dts,this._videoTimestampBreak=0):(l.dts+=d,l.pts+=d,this.audioTrack.exist()||(this._videoTimestampBreak=1))}for(var f=0;f<u;f++){var p=r[f].dts,v=r[f+1];((c=f<u-1?v.dts-p:a?a.dts-p:n)>1e3||c<0)&&(this._videoTimestampBreak++,Math.abs(p-this._lastVideoExceptionLargeGapDot)>5e3&&(this._lastVideoExceptionLargeGapDot=p,e.warnings.push({type:$e,time:p/e.timescale,dts:p,originDts:r[f].originDts,nextDts:this._videoNextDts,sampleDuration:c,refSampleDuration:n})),c=n),r[f].duration=c,this._videoNextDts+=c,this._lastVideoDuration=c}}}}},{key:"_fixAudio",value:function(e){var t=this,r=e.samples;r.length&&(r.forEach((function(e){e.dts=e.pts-=t._baseDts})),this._doFixAudioInternal(e,r,1e3))}},{key:"_calculateBaseDts",value:function(e,t){var r=e.samples,n=t.samples;if(!r.length&&!n.length)return!1;var i=1/0,s=1/0;r.length&&(e.baseDts=i=r[0].pts),n.length&&(t.baseDts=s=n[0].dts),this._baseDts=Math.min(i,s);var a=s-i;return Number.isFinite(a)&&Math.abs(a)>500&&t.warnings.push({type:Ze,videoBaseDts:s,audioBasePts:i,baseDts:this._baseDts,delta:a}),this._baseDtsInited=!0,!0}},{key:"_resetBaseDtsWhenStreamBreaked",value:function(){this._calculateBaseDts(this.audioTrack,this.videoTrack)&&(this.audioTrack.exist()?this.videoTrack.exist()?this._baseDts-=Math.min(this._audioNextPts,this._videoNextDts):this._baseDts-=this._audioNextPts:this._baseDts-=this._videoNextDts,this._videoTimestampBreak=0,this._audioTimestampBreak=0)}},{key:"_doFixAudioInternal",value:function(e,t,r){e.sampleDuration||(e.sampleDuration=e.codecType===Xe?Ct.getFrameDuration(e.timescale,r):this._getG711Duration(e));var n=e.sampleDuration,i=e.codecType===Xe?1024:n*e.timescale/1e3;if(void 0===this._audioNextPts){var s=t[0];this._audioNextPts=s.pts}for(var a=0;a<t.length;a++){var o=this._audioNextPts,u=t[a],c=u.pts-o;if(0===a&&this._audioTimestampBreak>=5&&this._keyFrameInNextChunk&&(o=this._audioNextPts=u.dts,c=0,this._audioTimestampBreak=0),!this._audioTimestampBreak&&c>=3*n&&c<=Ot&&!yt){var l=this._getSilentFrame(e)||t[0].data.subarray(),d=Math.floor(c/n);Math.abs(u.pts-this._lastAudioExceptionGapDot)>jt&&(this._lastAudioExceptionGapDot=u.pts,e.warnings.push({type:rt,pts:u.pts,originPts:u.originPts,count:d,nextPts:o,refSampleDuration:n}));for(var h=0;h<d;h++){var f=new ot(Math.floor(this._audioNextPts+n)-Math.floor(this._audioNextPts),l,i);f.originPts=Math.floor(this._baseDts+o),t.splice(a,0,f),this._audioNextPts+=n,a++}a--}else c<=-3*n&&c>=-1e3?(Math.abs(u.pts-this._lastAudioExceptionOverlapDot)>jt&&(this._lastAudioExceptionOverlapDot=u.pts,e.warnings.push({type:nt,pts:u.pts,originPts:u.originPts,nextPts:o,refSampleDuration:n})),t.splice(a,1),a--):(Math.abs(c)>Ot&&(this._audioTimestampBreak++,Math.abs(u.pts-this._lastAudioExceptionLargeGapDot)>jt&&(this._lastAudioExceptionLargeGapDot=u.pts,e.warnings.push({type:tt,time:u.pts/1e3,pts:u.pts,originPts:u.originPts,nextPts:o,sampleDuration:c,refSampleDuration:n}))),u.dts=u.pts=o,u.duration=i,this._audioNextPts+=n)}}},{key:"_getG711Duration",value:function(e){var t=e.sampleSize,r=e.channelCount,n=e.sampleRate,i=e.samples[0];if(i)return 2*i.data.byteLength/r/(t/8)/n*1e3}},{key:"_getSilentFrame",value:function(e){return e.codecType===Xe?Ct.getSilentFrame(e.codec,e.channelCount):new Uint8Array(8*e.sampleDuration*e.channelCount)}}])}(),It=function(){function e(){b(this,e)}return S(e,null,[{key:"parse",value:function(t){if(!(t.length<3)){var r={},n=e._parseValue(new DataView(t.buffer,t.byteOffset,t.byteLength)),i=e._parseValue(new DataView(t.buffer,t.byteOffset+n.size,t.byteLength-n.size));return r[n.data]=i.data,r}}},{key:"_parseValue",value:function(t){var r,n=t.byteLength,i=1,s=!1;switch(t.getUint8(0)){case 0:r=t.getFloat64(1),i+=8;break;case 1:r=!!t.getUint8(1),i+=1;break;case 2:var a=e._parseString(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i));r=a.data,i+=a.size;break;case 3:r={};var o=0;for(9==(16777215&t.getUint32(n-4))&&(o=3);i<n-4;){var u=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-o)),c=u.size,l=u.data;if(u.isEnd)break;r[l.name]=l.value,i+=c}if(i<=n-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 8:r={},i+=4;var d=0;for(9==(16777215&t.getUint32(n-4))&&(d=3);i<n-8;){var h=e._parseObject(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i-d)),f=h.size,p=h.data;if(h.isEnd)break;r[p.name]=p.value,i+=f}if(i<=n-3)9===(16777215&t.getUint32(i-1))&&(i+=3);break;case 9:r=void 0,i=1,s=!0;break;case 10:r=[];var v=t.getUint32(1);i+=4;for(var m=0;m<v;m++){var y=e._parseValue(new DataView(t.buffer,t.byteOffset+i,t.byteLength-i)),_=y.data,g=y.size;r.push(_),i+=g}break;case 11:var b=t.getFloat64(i)+6e4*t.getInt16(i+8);r=new Date(b),i+=10;break;case 12:var k=t.getUint32(1);i+=4,r="",k>0&&(r=pt.decode(new Uint8Array(t.buffer,t.byteOffset+i,k))),i+=k;break;default:i=n}return{data:r,size:i,isEnd:s}}},{key:"_parseString",value:function(e){var t=e.getUint16(0),r="";return t>0&&(r=pt.decode(new Uint8Array(e.buffer,e.byteOffset+2,t))),{data:r,size:2+t}}},{key:"_parseObject",value:function(t){if(!(t.byteLength<3)){var r=e._parseString(t),n=e._parseValue(new DataView(t.buffer,t.byteOffset+r.size,t.byteLength-r.size));return{data:{name:r.data,value:n.data},size:r.size+n.size,isEnd:n.isEnd}}}}])}(),Pt=new ft("FlvDemuxer"),Bt=function(){function e(t,r,n){b(this,e),x(this,"_headerParsed",!1),x(this,"_remainingData",null),x(this,"_gopId",0),x(this,"_needAddMetaBeforeKeyFrameNal",!0),this.videoTrack=t||new it,this.audioTrack=r||new st,this.metadataTrack=n||new dt,this._fixer=new Rt(this.videoTrack,this.audioTrack,this.metadataTrack)}return S(e,[{key:"demux",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=this.audioTrack,s=this.videoTrack,a=this.metadataTrack;if(!r&&n||(this._remainingData=null,this._headerParsed=!1),r?(s.reset(),i.reset(),a.reset()):(s.samples=[],i.samples=[],a.seiSamples=[],a.flvScriptSamples=[],s.warnings=[],i.warnings=[],this._remainingData&&(t=bt(this._remainingData,t),this._remainingData=null)),!t.length)return{videoTrack:s,audioTrack:i,metadataTrack:a};var o=0;if(!this._headerParsed){if(!e.probe(t))throw new Error("Invalid flv file");i.present=(4&t[4])>>>2!=0,s.present=0!=(1&t[4]),this._headerParsed=!0,o=xt(t,5)+4}for(var u,c,l,d,h,f=t.length;o+15<f&&(u=t[o],!(o+15+(c=t[o+1]<<16|t[o+2]<<8|t[o+3])>f));)l=(t[o+7]<<24>>>0)+(t[o+4]<<16)+(t[o+5]<<8)+t[o+6],o+=11,d=t.subarray(o,o+c),8===u?this._parseAudio(d,l):9===u?this._parseVideo(d,l):18===u?this._parseScript(d,l):Pt.warn("Invalid tag type: ".concat(u)),(h=xt(t,o+=c))!==11+c&&Pt.warn("Invalid PrevTagSize ".concat(h," (").concat(11+c,")")),o+=4;return o<f&&(this._remainingData=t.subarray(o)),i.formatTimescale=s.formatTimescale=s.timescale=a.timescale=1e3,i.timescale=i.sampleRate||0,!i.exist()&&i.hasSample()&&i.reset(),!s.exist()&&s.hasSample()&&s.reset(),{videoTrack:s,audioTrack:i,metadataTrack:a}}},{key:"fix",value:function(e,t,r){return this._fixer.fix(e,t,r),{videoTrack:this.videoTrack,audioTrack:this.audioTrack,metadataTrack:this.metadataTrack}}},{key:"demuxAndFix",value:function(e,t,r,n){return this.demux(e,t,r),this.fix(n,t,r)}},{key:"_parseAudio",value:function(t,r){if(t.length){var n=(240&t[0])>>>4,i=this.audioTrack;if(10!==n&&7!==n&&8!==n)return Pt.warn("Unsupported sound format: ".concat(n)),void i.reset();if(10!==n){var s=(12&t[0])>>2,a=(2&t[0])>>1,o=1&t[0];i.sampleRate=e.AUDIO_RATE[s],i.sampleSize=a?16:8,i.channelCount=o+1}10===n?this._parseAac(t,r):this._parseG711(t,r,n)}}},{key:"_parseG711",value:function(e,t,r){var n=this.audioTrack;n.codecType=7===r?Qe:Je,n.sampleRate=8e3,n.codec=n.codecType,n.samples.push(new ot(t,e.subarray(1)))}},{key:"_parseAac",value:function(e,t){var r=this.audioTrack;if(r.codecType=Xe,0===e[1]){var n=Ct.parseAudioSpecificConfig(e.subarray(2));n?(r.codec=n.codec,r.channelCount=n.channelCount,r.sampleRate=n.sampleRate,r.config=n.config,r.objectType=n.objectType,r.sampleRateIndex=n.samplingFrequencyIndex):(r.reset(),Pt.warn("Cannot parse AudioSpecificConfig",e))}else if(1===e[1]){if(null==t)return;r.samples.push(new ot(t,e.subarray(2)))}else Pt.warn("Unknown AACPacketType: ".concat(e[1]))}},{key:"_parseVideo",value:function(e,t){var r=this;if(!(e.length<6)){var n=(240&e[0])>>>4,i=15&e[0],s=this.videoTrack;if(7!==i&&12!==i)return s.reset(),void Pt.warn("Unsupported codecId: ".concat(i));var a=12===i;s.codecType=a?Ye:We;var o=e[1],u=(e[2]<<16|e[3]<<8|e[4])<<8>>8;if(0===o){var c=e.subarray(5),l=a?Lt.parseHEVCDecoderConfigurationRecord(c):At.parseAVCDecoderConfigurationRecord(c);if(l){var d=l.hvcC,h=l.sps,f=l.ppsArr,p=l.spsArr,v=l.vpsArr,m=l.nalUnitSize;d&&(s.hvcC=s.hvcC||d),h&&(s.codec=h.codec,s.width=h.width,s.height=h.height,s.sarRatio=h.sarRatio,s.fpsNum=h.fpsNum,s.fpsDen=h.fpsDen),p.length&&(s.sps=p),f.length&&(s.pps=f),v&&v.length&&(s.vps=v),m&&(s.nalUnitSize=m)}else Pt.warn("Cannot parse ".concat(a?"HEVC":"AVC","DecoderConfigurationRecord"),e)}else if(1===o){var y=Dt.parseAvcC(e.subarray(5),s.nalUnitSize);if((y=this._checkAddMetaNalToUnits(a,y,s))&&y.length){var _=new at(t+u,t,y);1===n&&_.setToKeyframe(),s.samples.push(_),y.forEach((function(e){var n=a?e[0]>>>1&63:31&e[0];switch(n){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!a&&5!==n||a&&5===n)break;_.setToKeyframe();break;case 6:case 39:case 40:if(!a&&6!==n||a&&6===n)break;r.metadataTrack.seiSamples.push(new lt(Dt.parseSEI(Dt.removeEPB(e),a),t+u))}})),_.keyframe&&this._gopId++,_.gopId=this._gopId}else Pt.warn("Cannot parse NALUs",e)}else 2===o||Pt.warn("Unknown AVCPacketType: ".concat(o))}}},{key:"_checkAddMetaNalToUnits",value:function(e,t,r){return e&&this._needAddMetaBeforeKeyFrameNal?t.map((function(e){return e[0]>>>1&63})).includes(32)?(this._needAddMetaBeforeKeyFrameNal=!1,t):(t.unshift(r.pps[0]),t.unshift(r.sps[0]),t.unshift(r.vps[0]),t.filter(Boolean)):(this._needAddMetaBeforeKeyFrameNal=!1,t)}},{key:"_parseScript",value:function(e,t){this.metadataTrack.flvScriptSamples.push(new ct(It.parse(e),t))}}],[{key:"probe",value:function(e){return 70===e[0]&&76===e[1]&&86===e[2]&&1===e[3]&&xt(e,5)>=9}}])}();x(Bt,"AUDIO_RATE",[5500,11e3,22e3,44e3]);var Ut=9e4,Mt=45e4,Nt=9e4,Ft=function(){return S((function e(t,r,n){b(this,e),this.videoTrack=t,this.audioTrack=r,this.metadataTrack=n,this._baseDts=-1,this._baseDtsInited=!1,this._audioNextPts=void 0,this._videoNextDts=void 0,this._audioTimestampBreak=!1,this._videoTimestampBreak=!1,this._lastAudioExceptionGapDot=0,this._lastAudioExceptionOverlapDot=0,this._lastAudioExceptionLargeGapDot=0}),[{key:"fix",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t=Math.round(9e4*t);var i=this.videoTrack,s=this.audioTrack,a=i.samples,o=s.samples;if(a.length||o.length){var u=a[0],c=o[0],l=0;if(a.length&&o.length&&(l=u.dts-c.pts),this._baseDtsInited||this._calculateBaseDts(this.audioTrack,this.videoTrack),r&&(this._calculateBaseDts(this.audioTrack,this.videoTrack),this._baseDts-=t),!n){this._videoNextDts=l>0?t+l:t,this._audioNextPts=l>0?t:t-l;var d=u?u.dts-this._baseDts-this._videoNextDts:0,h=c?c.pts-this._baseDts-this._audioNextPts:0;Math.abs(d||h)>Nt&&(this._calculateBaseDts(this.audioTrack,this.videoTrack),this._baseDts-=t)}if(this._resetBaseDtsWhenStreamBreaked(),this._fixAudio(s),this._fixVideo(i),this.metadataTrack.exist()){var f=this.metadataTrack.timescale;this.metadataTrack.seiSamples.forEach((function(t){t.pts=t.originPts-e._baseDts,t.time=Math.max(0,t.pts)/f}))}i.samples.length&&(i.baseMediaDecodeTime=i.samples[0].dts),s.samples.length&&(s.baseMediaDecodeTime=s.samples[0].pts*s.timescale/9e4)}}},{key:"_fixVideo",value:function(e){var t=this,r=e.samples;if(r.length){if(r.forEach((function(e){e.dts-=t._baseDts,e.pts-=t._baseDts})),void 0===this._videoNextDts){var n=r[0];this._videoNextDts=n.dts}var i,s,a=r.length,o=0,u=r[0],c=r[1],l=this._videoNextDts-u.dts;if(Math.abs(l)>45e3)u.dts+=l,u.pts+=l,e.warnings.push({type:et,nextDts:this._videoNextDts,firstSampleDts:u.dts,nextSampleDts:null===(i=r[1])||void 0===i?void 0:i.dts,sampleDuration:l}),c&&Math.abs(c.dts-u.dts)>Nt&&(this._videoTimestampBreak=!0,r.forEach((function(e,t){0!==t&&(e.dts+=l,e.pts+=l)})));if(e.fpsNum&&e.fpsDen)s=e.timescale*(e.fpsDen/e.fpsNum);else{var d=e.samples[0],h=e.samples[a-1];s=1===a?9e3:Math.floor((h.dts-d.dts)/(a-1))}for(var f=0;f<a;f++){var p=r[f].dts,v=r[f+1];if((o=f<a-1?v.dts-p:r[f-1]?Math.min(p-r[f-1].dts,s):s)>Nt||o<0){this._videoTimestampBreak=!0,o=this._audioTimestampBreak?s:Math.max(o,2700);var m=this._audioNextPts||0;v&&v.dts>m&&(o=s),e.warnings.push({type:$e,time:p/e.timescale,dts:p,originDts:r[f].originDts,nextDts:this._videoNextDts,sampleDuration:o,refSampleDuration:s})}r[f].duration=o,this._videoNextDts+=o}}}},{key:"_fixAudio",value:function(e){var t=this,r=e.samples;r.length&&(r.forEach((function(e){e.pts-=t._baseDts,e.dts=e.pts})),this._doFixAudioInternal(e,r,9e4))}},{key:"_calculateBaseDts",value:function(e,t){var r=e.samples,n=t.samples;if(!r.length&&!n.length)return!1;var i=1/0,s=1/0;r.length&&(e.baseDts=i=r[0].pts),n.length&&(t.baseDts=s=n[0].dts),this._baseDts=Math.min(i,s);var a=s-i;return Number.isFinite(a)&&Math.abs(a)>45e3&&t.warnings.push({type:Ze,videoBaseDts:s,audioBasePts:i,baseDts:this._baseDts,delta:a}),this._baseDtsInited=!0,!0}},{key:"_resetBaseDtsWhenStreamBreaked",value:function(){if(this._baseDtsInited&&this._videoTimestampBreak&&this._audioTimestampBreak){if(!this._calculateBaseDts(this.audioTrack,this.videoTrack))return;this._baseDts-=Math.min(this._audioNextPts,this._videoNextDts),this._audioLastSample=null,this._videoLastSample=null,this._videoTimestampBreak=!1,this._audioTimestampBreak=!1}}},{key:"_doFixAudioInternal",value:function(e,t,r){e.sampleDuration||(e.sampleDuration=Ct.getFrameDuration(e.timescale,r));var n=e.sampleDuration;if(void 0===this._audioNextPts){var i=t[0];this._audioNextPts=i.pts}for(var s=0;s<t.length;s++){var a=this._audioNextPts,o=t[s],u=o.pts-a;if(!this._audioTimestampBreak&&u>=3*n&&u<=Ut&&!yt){var c=Ct.getSilentFrame(e.codec,e.channelCount)||t[0].data.subarray(),l=Math.floor(u/n);Math.abs(o.pts-this._lastAudioExceptionGapDot)>Mt&&(this._lastAudioExceptionGapDot=o.pts),e.warnings.push({type:rt,pts:o.pts,originPts:o.originPts,count:l,nextPts:a,refSampleDuration:n});for(var d=0;d<l;d++){var h=new ot(Math.floor(a),c);h.originPts=Math.floor(this._baseDts+a),t.splice(s,0,h),this._audioNextPts+=n,s++}s--}else u<=-3*n&&u>=-9e4?(Math.abs(o.pts-this._lastAudioExceptionOverlapDot)>Mt&&(this._lastAudioExceptionOverlapDot=o.pts,e.warnings.push({type:nt,pts:o.pts,originPts:o.originPts,nextPts:a,refSampleDuration:n})),t.splice(s,1),s--):(Math.abs(u)>=Ut&&(this._audioTimestampBreak=!0,Math.abs(o.pts-this._lastAudioExceptionLargeGapDot)>Mt&&(this._lastAudioExceptionLargeGapDot=o.pts,e.warnings.push({type:tt,time:o.pts/1e3,pts:o.pts,originPts:o.originPts,nextPts:a,sampleDuration:u,refSampleDuration:n}))),o.dts=o.pts=a,this._audioNextPts+=n)}}}])}(),Vt=new ft("TsDemuxer"),zt=function(){function e(t,r,n){b(this,e),x(this,"_pmtId",-1),x(this,"_remainingPacketData",null),x(this,"_videoPesData",[]),x(this,"_audioPesData",[]),x(this,"_gopId",0),this.videoTrack=t||new it,this.audioTrack=r||new st,this.metadataTrack=n||new dt,this._fixer=new Ft(this.videoTrack,this.audioTrack,this.metadataTrack)}return S(e,[{key:"demux",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],n=this.audioTrack,i=this.videoTrack,s=this.metadataTrack;t&&(this._pmtId=-1,i.reset(),n.reset(),s.reset()),!r||t?(this._remainingPacketData=null,this._videoPesData=[],this._audioPesData=[]):(i.samples=[],n.samples=[],s.seiSamples=[],i.warnings=[],n.warnings=[],this._remainingPacketData&&(e=bt(this._remainingPacketData,e),this._remainingPacketData=null));var a=e.length,o=a%188;o&&(this._remainingPacketData=e.subarray(a-o),a-=o);for(var u=i.pid,c=n.pid,l=0;l<a;l+=188){if(71!==e[l])throw new Error("TS packet did not start with 0x47");var d=!!(64&e[l+1]),h=((31&e[l+1])<<8)+e[l+2],f=(48&e[l+3])>>4,p=void 0;if(f>1){if((p=l+5+e[l+4])===l+188)continue}else p=l+4;switch(h){case 0:d&&(p+=e[p]+1),this._pmtId=(31&e[p+10])<<8|e[p+11];break;case this._pmtId:d&&(p+=e[p]+1);var v=p+3+((15&e[p+1])<<8|e[p+2])-4,m=(15&e[p+10])<<8|e[p+11];for(p+=12+m;p<v;){var y=(31&e[p+1])<<8|e[p+2];switch(e[p]){case 15:n.pid=c=y;break;case 27:if(-1!==u)break;i.codecType=We,i.pid=u=y;break;case 36:if(-1!==u)break;i.codecType=Ye,i.pid=u=y;break;default:Vt.warn("Unsupported stream. type: ".concat(e[p],", pid: ").concat(y))}p+=5+((15&e[p+3])<<8|e[p+4])}break;case u:d&&this._videoPesData.length&&this._parseVideoData(),this._videoPesData.push(e.subarray(p,l+188));break;case c:d&&this._audioPesData.length&&this._parseAudioData(),this._audioPesData.push(e.subarray(p,l+188));break;case 17:case 8191:break;default:Vt.warn("Unknown pid: ".concat(h))}}return this._parseVideoData(),this._parseAudioData(),n.formatTimescale=i.formatTimescale=i.timescale=s.timescale=9e4,n.timescale=n.sampleRate||0,{videoTrack:i,audioTrack:n,metadataTrack:s}}},{key:"fix",value:function(e,t,r){return this._fixer.fix(e,t,r),{videoTrack:this.videoTrack,audioTrack:this.audioTrack,metadataTrack:this.metadataTrack}}},{key:"demuxAndFix",value:function(e,t,r,n){return this.demux(e,t,r),this.fix(n,t,r)}},{key:"_parseVideoData",value:function(){if(this._videoPesData.length){var t=e._parsePES(bt.apply(void 0,j(this._videoPesData)));if(t){var r=Dt.parseAnnexB(t.data);r?this._createVideoSample(r,t.pts,t.dts):Vt.warn("Cannot parse avc units",t),this._videoPesData=[]}else Vt.warn("Cannot parse video pes",this._videoPesData)}}},{key:"_createVideoSample",value:function(e,t,r){var n=this;if(e.length){var i=this.videoTrack,s=i.codecType===Ye,a=new at(t,r);e.forEach((function(e){var r=s?e[0]>>>1&63:31&e[0];switch(r){case 5:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:if(!s&&5!==r||s&&5===r)break;a.setToKeyframe(),n._gopId++;break;case 6:case 39:case 40:if(!s&&6!==r||s&&6===r)break;return void n.metadataTrack.seiSamples.push(new lt(Dt.parseSEI(Dt.removeEPB(e),s),t));case 32:if(!s)break;if(!i.vps.length){var o=Lt.parseVPS(Dt.removeEPB(e),i.hvcC);i.hvcC=i.hvcC||o,i.vps=[e]}break;case 7:case 33:if(!s&&7!==r||s&&7===r)break;if(!i.sps.length){var u=Dt.removeEPB(e),c=s?Lt.parseSPS(u,i.hvcC):At.parseSPS(u);i.sps=[e],i.hvcC=i.hvcC||c.hvcC,i.codec=c.codec,i.width=c.width,i.height=c.height,i.sarRatio=c.sarRatio,i.fpsNum=c.fpsNum,i.fpsDen=c.fpsDen}break;case 8:case 34:if(!s&&8!==r||s&&8===r)break;i.pps.length||(i.pps=[e])}a.units.push(e)})),a.gopId=this._gopId,this._pushVideoSample(i,a)}}},{key:"_pushVideoSample",value:function(e,t){if(t.units.length)if(null===t.pts||void 0===t.pts){Vt.warn("Video sample no pts",t);var r=e.samples[e.samples.length-1];r?(t.pts=r.pts,t.dts=r.dts):Vt.warn("Drop video sample",t)}else e.samples.push(t)}},{key:"_parseAudioData",value:function(){if(this._audioPesData.length){var t=e._parsePES(bt.apply(void 0,j(this._audioPesData)));t?(this._parseAacData(t),this._audioPesData=[]):Vt.warn("Cannot parse audio pes",this._audioPesData)}}},{key:"_parseAacData",value:function(e){var t=this.audioTrack,r=e.pts;if(null==r){if(Vt.warn("AAC pes not pts",t),!t.samples.length||!t.sampleRate)return;r=t.samples[t.samples.length-1].pts+Ct.getFrameDuration(t.sampleRate)}var n,i=Ct.parseADTS(e.data,r);i?(t.codec=i.codec,t.channelCount=i.channelCount,t.sampleRate=i.sampleRate,t.objectType=i.objectType,t.sampleRateIndex=i.samplingFrequencyIndex,t.config=i.config,(n=t.samples).push.apply(n,j(i.frames.map((function(e){return new ot(e.pts,e.data)})))),i.skip&&Vt.warn("Skip aac adts ".concat(i.skip," bits")),i.remaining&&Vt.warn("Remaining aac adts ".concat(i.remaining," bits"))):Vt.warn("Cannot parse aac adts",e)}}],[{key:"probe",value:function(e){return!!e.length&&(71===e[0]&&71===e[188]&&71===e[376])}},{key:"_parsePES",value:function(e){var t=e[8];if(!(null==t||e.length<t+9)&&1===(e[0]<<16|e[1]<<8|e[2])){var r=(e[4]<<8)+e[5];if(!(r&&r>e.length-6)){var n,i,s=e[7];return 192&s&&(n=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2,64&s?n-(i=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2)>54e5&&(n=i):i=n),{data:e.subarray(9+t),pts:n,dts:i}}}}}])}(),qt=function(){function e(){b(this,e)}return S(e,null,[{key:"findBox",value:function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=[];if(!t)return i;for(var s=0,a="",o=0;t.length>7;){if(s=xt(t),a=String.fromCharCode.apply(null,t.subarray(4,8)),o=8,1===s?(s=Et(t,8),o+=8):s||(s=t.length),!r[0]||a===r[0]){var u=t.subarray(0,s);if(!(r.length<2))return e.findBox(u.subarray(o),r.slice(1),n+o);i.push({start:n,size:s,headerSize:o,type:a,data:u})}n+=s,t=t.subarray(s)}return i}},{key:"tfhd",value:function(e){return Wt(e,!0,(function(e,t){e.trackId=xt(t);var r=4,n=1&e.flags,i=2&e.flags,s=8&e.flags,a=16&e.flags,o=32&e.flags;n&&(r+=4,e.baseDataOffset=xt(t,r),r+=4),i&&(e.sampleDescriptionIndex=xt(t,r),r+=4),s&&(e.defaultSampleDuration=xt(t,r),r+=4),a&&(e.defaultSampleSize=xt(t,r),r+=4),o&&(e.defaultSampleFlags=xt(t,r))}))}},{key:"sidx",value:function(e){return Wt(e,!0,(function(e,t){var r=0;e.reference_ID=xt(t,r),r+=4,e.timescale=xt(t,r),r+=4,0===e.version?(e.earliest_presentation_time=xt(t,r),r+=4,e.first_offset=xt(t,r),r+=4):(e.earliest_presentation_time=Et(t,r),r+=8,e.first_offset=Et(t,r),r+=8),r+=2,e.references=[];var n=St(t,r);r+=2;for(var i=0;i<n;i++){var s={};e.references.push(s);var a=xt(t,r);r+=4,s.reference_type=a>>31&1,s.referenced_size=2147483647&a,s.subsegment_duration=xt(t,r),a=xt(t,r+=4),r+=4,s.starts_with_SAP=a>>31&1,s.SAP_type=a>>28&7,s.SAP_delta_time=268435455&a}}))}},{key:"moov",value:function(t){return Wt(t,!1,(function(t,r,n){t.mvhd=e.mvhd(e.findBox(r,["mvhd"],n)[0]),t.trak=e.findBox(r,["trak"],n).map((function(t){return e.trak(t)})),t.pssh=e.pssh(e.findBox(r,["pssh"],n)[0])}))}},{key:"mvhd",value:function(e){return Wt(e,!0,(function(e,t){var r=0;1===e.version?(e.timescale=xt(t,16),e.duration=Et(t,20),r+=28):(e.timescale=xt(t,8),e.duration=xt(t,12),r+=16),e.nextTrackId=xt(t,r+76)}))}},{key:"trak",value:function(t){return Wt(t,!1,(function(t,r,n){t.tkhd=e.tkhd(e.findBox(r,["tkhd"],n)[0]),t.mdia=e.mdia(e.findBox(r,["mdia"],n)[0])}))}},{key:"tkhd",value:function(e){return Wt(e,!0,(function(e,t){var r=0;1===e.version?(e.trackId=xt(t,16),e.duration=Et(t,24),r+=32):(e.trackId=xt(t,8),e.duration=xt(t,16),r+=20),e.width=xt(t,r+52),e.height=xt(t,r+56)}))}},{key:"mdia",value:function(t){return Wt(t,!1,(function(t,r,n){t.mdhd=e.mdhd(e.findBox(r,["mdhd"],n)[0]),t.hdlr=e.hdlr(e.findBox(r,["hdlr"],n)[0]),t.minf=e.minf(e.findBox(r,["minf"],n)[0])}))}},{key:"mdhd",value:function(e){return Wt(e,!0,(function(e,t){var r=0;1===e.version?(e.timescale=xt(t,16),e.duration=Et(t,20),r+=28):(e.timescale=xt(t,8),e.duration=xt(t,12),r+=16);var n=St(t,r);e.language=String.fromCharCode(96+(n>>10&31),96+(n>>5&31),96+(31&n))}))}},{key:"hdlr",value:function(e){return Wt(e,!0,(function(e,t){0===e.version&&(e.handlerType=String.fromCharCode.apply(null,t.subarray(4,8)))}))}},{key:"minf",value:function(t){return Wt(t,!1,(function(t,r,n){t.vmhd=e.vmhd(e.findBox(r,["vmhd"],n)[0]),t.smhd=e.smhd(e.findBox(r,["smhd"],n)[0]),t.stbl=e.stbl(e.findBox(r,["stbl"],n)[0])}))}},{key:"vmhd",value:function(e){return Wt(e,!0,(function(e,t){e.graphicsmode=St(t),e.opcolor=[St(t,2),St(t,4),St(t,6)]}))}},{key:"smhd",value:function(e){return Wt(e,!0,(function(e,t){e.balance=St(t)}))}},{key:"stbl",value:function(t){return Wt(t,!1,(function(t,r,n){var i;t.stsd=e.stsd(e.findBox(r,["stsd"],n)[0]),t.stts=e.stts(e.findBox(r,["stts"],n)[0]),t.ctts=e.ctts(e.findBox(r,["ctts"],n)[0]),t.stsc=e.stsc(e.findBox(r,["stsc"],n)[0]),t.stsz=e.stsz(e.findBox(r,["stsz"],n)[0]),t.stco=e.stco(e.findBox(r,["stco"],n)[0]),t.stco||(t.co64=e.co64(e.findBox(r,["co64"],n)[0]),t.stco=t.co64);var s=null===(i=t.stsd.entries[0])||void 0===i||null===(i=i.sinf)||void 0===i||null===(i=i.schi)||void 0===i?void 0:i.tenc.default_IV_size;t.stss=e.stss(e.findBox(r,["stss"],n)[0]),t.senc=e.senc(e.findBox(r,["senc"],n)[0],s)}))}},{key:"senc",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:8;return Wt(e,!0,(function(e,r){var n=0,i=xt(r,n);n+=4,e.samples=[];for(var s=0;s<i;s++){for(var a={InitializationVector:[]},o=0;o<t;o++)a.InitializationVector[o]=r[n+o];if(n+=t,2&e.flags){a.subsamples=[];var u=St(r,n);n+=2;for(var c=0;c<u;c++){var l={};l.BytesOfClearData=St(r,n),n+=2,l.BytesOfProtectedData=xt(r,n),n+=4,a.subsamples.push(l)}}e.samples.push(a)}}))}},{key:"pssh",value:function(e){return Wt(e,!0,(function(e,t){for(var r=[],n=[],i=0,s=0;s<16;s++)n.push(Xt(t[i+s]));if(i+=16,e.version>0){var a=xt(t,i);i+=4;for(var o=0;o<(""+a).length;o++)for(var u=0;u<16;u++){var c=t[i];i+=1,r.push(Xt(c))}}var l=xt(t,i);e.data_size=l,i+=4,e.kid=r,e.system_id=n,e.buffer=t}))}},{key:"stsd",value:function(t){return Wt(t,!0,(function(t,r,n){t.entryCount=xt(r),t.entries=e.findBox(r.subarray(4),[],n+4).map((function(t){switch(t.type){case"avc1":case"avc2":case"avc3":case"avc4":return e.avc1(t);case"hvc1":case"hev1":return e.hvc1(t);case"mp4a":return e.mp4a(t);case"alaw":case"ulaw":return e.alaw(t);case"enca":return Wt(t,!1,(function(t,r,n){t.channelCount=St(r,16),t.samplesize=St(r,18),t.sampleRate=xt(r,24)/65536,r=r.subarray(28),t.sinf=e.sinf(e.findBox(r,["sinf"],n)[0]),t.esds=e.esds(e.findBox(r,["esds"],n)[0])}));case"encv":return Wt(t,!1,(function(t,r,n){t.width=St(r,24),t.height=St(r,26),t.horizresolution=xt(r,28),t.vertresolution=xt(r,32),r=r.subarray(78),t.sinf=e.sinf(e.findBox(r,["sinf"],n)[0]),t.avcC=e.avcC(e.findBox(r,["avcC"],n)[0]),t.hvcC=e.hvcC(e.findBox(r,["hvcC"],n)[0]),t.pasp=e.pasp(e.findBox(r,["pasp"],n)[0])}))}})).filter(Boolean)}))}},{key:"tenc",value:function(e){return Wt(e,!1,(function(e,t){var r=6;e.default_IsEncrypted=t[r],r+=1,e.default_IV_size=t[r],r+=1,e.default_KID=[];for(var n=0;n<16;n++)e.default_KID.push(Xt(t[r])),r+=1}))}},{key:"schi",value:function(t){return Wt(t,!1,(function(t,r,n){t.tenc=e.tenc(e.findBox(r,["tenc"],n)[0])}))}},{key:"sinf",value:function(t){return Wt(t,!1,(function(t,r,n){t.schi=e.schi(e.findBox(r,["schi"],n)[0]),t.frma=e.frma(e.findBox(r,["frma"],n)[0])}))}},{key:"frma",value:function(e){return Wt(e,!1,(function(e,t){e.data_format="";for(var r=0;r<4;r++)e.data_format+=String.fromCharCode(t[r])}))}},{key:"avc1",value:function(t){return Wt(t,!1,(function(t,r,n){var i=Ht(t,r),s=r.subarray(i);n+=i,t.avcC=e.avcC(e.findBox(s,["avcC"],n)[0]),t.pasp=e.pasp(e.findBox(s,["pasp"],n)[0])}))}},{key:"avcC",value:function(e){return Wt(e,!1,(function(e,t){e.configurationVersion=t[0],e.AVCProfileIndication=t[1],e.profileCompatibility=t[2],e.AVCLevelIndication=t[3],e.codec=wt([t[1],t[2],t[3]]),e.lengthSizeMinusOne=3&t[4],e.spsLength=31&t[5],e.sps=[];for(var r=6,n=0;n<e.spsLength;n++){var i=St(t,r);r+=2,e.sps.push(t.subarray(r,r+i)),r+=i}e.ppsLength=t[r],r+=1,e.pps=[];for(var s=0;s<e.ppsLength;s++){var a=St(t,r);r+=2,e.pps.push(t.subarray(r,r+=a)),r+=a}}))}},{key:"hvc1",value:function(t){return Wt(t,!1,(function(t,r,n){var i=Ht(t,r),s=r.subarray(i);n+=i,t.hvcC=e.hvcC(e.findBox(s,["hvcC"],n)[0]),t.pasp=e.pasp(e.findBox(s,["pasp"],n)[0])}))}},{key:"hvcC",value:function(e){return Wt(e,!1,(function(t,r){t.data=e.data,t.codec="hev1.1.6.L93.B0",t.configurationVersion=r[0];var n=r[1];t.generalProfileSpace=n>>6,t.generalTierFlag=(32&n)>>5,t.generalProfileIdc=31&n,t.generalProfileCompatibility=xt(r,2),t.generalConstraintIndicatorFlags=r.subarray(6,12),t.generalLevelIdc=r[12],t.avgFrameRate=St(r,19),t.numOfArrays=r[22],t.vps=[],t.sps=[],t.pps=[];for(var i=23,s=0,a=0,o=0,u=0;u<t.numOfArrays;u++){s=63&r[i],a=St(r,i+1),i+=3;for(var c,l=[],d=0;d<a;d++)o=St(r,i),i+=2,l.push(r.subarray(i,i+o)),i+=o;if(32===s)(c=t.vps).push.apply(c,l);else if(33===s){var h;(h=t.sps).push.apply(h,l)}else if(34===s){var f;(f=t.pps).push.apply(f,l)}}}))}},{key:"pasp",value:function(e){return Wt(e,!1,(function(e,t){e.hSpacing=xt(t),e.vSpacing=xt(t,4)}))}},{key:"mp4a",value:function(t){return Wt(t,!1,(function(t,r,n){var i=Kt(t,r);t.esds=e.esds(e.findBox(r.subarray(i),["esds"],n+i)[0])}))}},{key:"esds",value:function(e){return Wt(e,!0,(function(e,t){e.codec="mp4a.";for(var r=0,n=0,i=0,s=0;t.length;){for(s=t[r=0],n=t[r+1],r+=2;128&n;)i=(127&n)<<7,n=t[r],r+=1;if(i+=127&n,3===s)t=t.subarray(r+3);else{if(4!==s){if(5===s){var a=e.config=t.subarray(r,r+i),o=(248&a[0])>>3;return 31===o&&a.length>=2&&(o=32+((7&a[0])<<3)+((224&a[1])>>5)),e.objectType=o,e.codec+=o.toString(16),void("."===e.codec[e.codec.length-1]&&(e.codec=e.codec.substring(0,e.codec.length-1)))}return void("."===e.codec[e.codec.length-1]&&(e.codec=e.codec.substring(0,e.codec.length-1)))}e.codec+=(t[r].toString(16)+".").padStart(3,"0"),t=t.subarray(r+13)}}}))}},{key:"alaw",value:function(e){return Wt(e,!1,(function(e,t){Kt(e,t)}))}},{key:"stts",value:function(e){return Wt(e,!0,(function(e,t){for(var r=xt(t),n=[],i=4,s=0;s<r;s++)n.push({count:xt(t,i),delta:xt(t,i+4)}),i+=8;e.entryCount=r,e.entries=n}))}},{key:"ctts",value:function(e){return Wt(e,!0,(function(e,t){var r=xt(t),n=[],i=4;if(1===e.version)for(var s=0;s<r;s++)n.push({count:xt(t,i),offset:xt(t,i+4)}),i+=8;else for(var a=0;a<r;a++)n.push({count:xt(t,i),offset:-(1+~xt(t,i+4))}),i+=8;e.entryCount=r,e.entries=n}))}},{key:"stsc",value:function(e){return Wt(e,!0,(function(e,t){for(var r=xt(t),n=[],i=4,s=0;s<r;s++)n.push({firstChunk:xt(t,i),samplesPerChunk:xt(t,i+4),sampleDescriptionIndex:xt(t,i+8)}),i+=12;e.entryCount=r,e.entries=n}))}},{key:"stsz",value:function(e){return Wt(e,!0,(function(e,t){var r=xt(t),n=xt(t,4),i=[];if(!r)for(var s=8,a=0;a<n;a++)i.push(xt(t,s)),s+=4;e.sampleSize=r,e.sampleCount=n,e.entrySizes=i}))}},{key:"stco",value:function(e){return Wt(e,!0,(function(e,t){for(var r=xt(t),n=[],i=4,s=0;s<r;s++)n.push(xt(t,i)),i+=4;e.entryCount=r,e.entries=n}))}},{key:"co64",value:function(e){return Wt(e,!0,(function(e,t){for(var r=xt(t),n=[],i=4,s=0;s<r;s++)n.push(Et(t,i)),i+=8;e.entryCount=r,e.entries=n}))}},{key:"stss",value:function(e){return Wt(e,!0,(function(e,t){for(var r=xt(t),n=[],i=4,s=0;s<r;s++)n.push(xt(t,i)),i+=4;e.entryCount=r,e.entries=n}))}},{key:"moof",value:function(t){return Wt(t,!1,(function(t,r,n){t.mfhd=e.mfhd(e.findBox(r,["mfhd"],n)[0]),t.traf=e.findBox(r,["traf"],n).map((function(t){return e.traf(t)}))}))}},{key:"mfhd",value:function(e){return Wt(e,!0,(function(e,t){e.sequenceNumber=xt(t)}))}},{key:"traf",value:function(t){return Wt(t,!1,(function(t,r,n){t.tfhd=e.tfhd(e.findBox(r,["tfhd"],n)[0]),t.tfdt=e.tfdt(e.findBox(r,["tfdt"],n)[0]),t.trun=e.trun(e.findBox(r,["trun"],n)[0])}))}},{key:"trun",value:function(e){return Wt(e,!0,(function(e,t){var r=e.version,n=e.flags,i=t.length,s=e.sampleCount=xt(t),a=4;if(i>a&&1&n&&(e.dataOffset=-(1+~xt(t,a)),a+=4),i>a&&4&n&&(e.firstSampleFlags=xt(t,a),a+=4),e.samples=[],i>a)for(var o,u=0;u<s;u++)o={},256&n&&(o.duration=xt(t,a),a+=4),512&n&&(o.size=xt(t,a),a+=4),1024&n&&(o.flags=xt(t,a),a+=4),2048&n&&(o.cts=r?-(1+~xt(t,a+4)):xt(t,a),a+=4),e.samples.push(o)}))}},{key:"tfdt",value:function(e){return Wt(e,!0,(function(e,t){1===e.version?e.baseMediaDecodeTime=Et(t):e.baseMediaDecodeTime=xt(t)}))}},{key:"probe",value:function(t){return!!e.findBox(t,["ftyp"])}},{key:"parseSampleFlags",value:function(e){return{isLeading:(12&e[0])>>>2,dependsOn:3&e[0],isDependedOn:(192&e[1])>>>6,hasRedundancy:(48&e[1])>>>4,paddingValue:(14&e[1])>>>1,isNonSyncSample:1&e[1],degradationPriority:e[2]<<8|e[3]}}},{key:"moovToTrack",value:function(e,t,r){var n,i,s=e.trak;if(s&&s.length){var a=s.find((function(e){var t;return"vide"===(null===(t=e.mdia)||void 0===t||null===(t=t.hdlr)||void 0===t?void 0:t.handlerType)})),o=s.find((function(e){var t;return"soun"===(null===(t=e.mdia)||void 0===t||null===(t=t.hdlr)||void 0===t?void 0:t.handlerType)}));if(a&&t){var u,c,l,d=t,h=null===(u=a.tkhd)||void 0===u?void 0:u.trackId;null!=h&&(d.id=a.tkhd.trackId),d.tkhdDuration=a.tkhd.duration,d.mvhdDurtion=e.mvhd.duration,d.mvhdTimecale=e.mvhd.timescale,d.timescale=d.formatTimescale=a.mdia.mdhd.timescale,d.duration=a.mdia.mdhd.duration||d.mvhdDurtion/d.mvhdTimecale*d.timescale;var f,p,v,m,y=a.mdia.minf.stbl.stsd.entries[0];if(d.width=y.width,d.height=y.height,y.pasp&&(d.sarRatio=[y.pasp.hSpacing,y.pasp.vSpacing]),y.hvcC)d.codecType=Ye,d.codec=y.hvcC.codec,d.vps=y.hvcC.vps,d.sps=y.hvcC.sps,d.pps=y.hvcC.pps,d.hvcC=y.hvcC.data;else{if(!y.avcC)throw new Error("unknown video stsd entry");d.codec=y.avcC.codec,d.sps=y.avcC.sps,d.pps=y.avcC.pps}if(d.present=!0,d.ext={},d.ext.stss=null===(c=a.mdia)||void 0===c||null===(c=c.minf)||void 0===c||null===(c=c.stbl)||void 0===c?void 0:c.stss,d.ext.ctts=null===(l=a.mdia)||void 0===l||null===(l=l.minf)||void 0===l||null===(l=l.stbl)||void 0===l?void 0:l.ctts,y&&"encv"===y.type)d.isVideoEncryption=!0,y.default_KID=null===(f=y.sinf)||void 0===f||null===(f=f.schi)||void 0===f?void 0:f.tenc.default_KID,y.default_IsEncrypted=null===(p=y.sinf)||void 0===p||null===(p=p.schi)||void 0===p?void 0:p.tenc.default_IsEncrypted,y.default_IV_size=null===(v=y.sinf)||void 0===v||null===(v=v.schi)||void 0===v?void 0:v.tenc.default_IV_size,d.videoSenc=a.mdia.minf.stbl.senc&&a.mdia.minf.stbl.senc.samples,y.data_format=null===(m=y.sinf)||void 0===m||null===(m=m.frma)||void 0===m?void 0:m.data_format,d.useEME=e.useEME,d.kidValue=e.kidValue,d.pssh=e.pssh,d.encv=y}if(o&&r){var _,g,b,k,S,x=r,E=null===(_=o.tkhd)||void 0===_?void 0:_.trackId;null!=E&&(x.id=o.tkhd.trackId),x.tkhdDuration=o.tkhd.duration,x.mvhdDurtion=e.mvhd.duration,x.mvhdTimecale=e.mvhd.timescale,x.timescale=x.formatTimescale=o.mdia.mdhd.timescale,x.duration=o.mdia.mdhd.duration||x.mvhdDurtion/x.mvhdTimecale*x.timescale;var w,T,D,A,C=o.mdia.minf.stbl.stsd.entries[0];switch(x.sampleSize=C.sampleSize,x.sampleRate=C.sampleRate,x.channelCount=C.channelCount,x.present=!0,C.type){case"alaw":x.codecType=x.codec=Qe,x.sampleRate=8e3;break;case"ulaw":x.codecType=x.codec=Je,x.sampleRate=8e3;break;default:x.sampleDuration=Ct.getFrameDuration(x.sampleRate,x.timescale),x.sampleRateIndex=Ct.getRateIndexByRate(x.sampleRate),x.objectType=(null===(n=C.esds)||void 0===n?void 0:n.objectType)||2,C.esds&&(x.config=Array.from(C.esds.config)),x.codec=(null===(i=C.esds)||void 0===i?void 0:i.codec)||"mp4a.40.2"}if(x.sampleDuration=Ct.getFrameDuration(x.sampleRate,x.timescale),x.objectType=(null===(g=C.esds)||void 0===g?void 0:g.objectType)||2,C.esds&&(C.esds.config?x.config=Array.from(C.esds.config):console.warn("esds config is null")),x.codec=(null===(b=C.esds)||void 0===b?void 0:b.codec)||"mp4a.40.2",x.sampleRateIndex=Ct.getRateIndexByRate(x.sampleRate),x.ext={},x.ext.stss=null===(k=o.mdia)||void 0===k||null===(k=k.minf)||void 0===k||null===(k=k.stbl)||void 0===k?void 0:k.stss,x.ext.ctts=null===(S=o.mdia)||void 0===S||null===(S=S.minf)||void 0===S||null===(S=S.stbl)||void 0===S?void 0:S.ctts,x.present=!0,C&&"enca"===C.type)x.isAudioEncryption=!0,C.data_format=null===(w=C.sinf)||void 0===w||null===(w=w.frma)||void 0===w?void 0:w.data_format,C.default_KID=null===(T=C.sinf)||void 0===T||null===(T=T.schi)||void 0===T?void 0:T.tenc.default_KID,C.default_IsEncrypted=null===(D=C.sinf)||void 0===D||null===(D=D.schi)||void 0===D?void 0:D.tenc.default_IsEncrypted,C.default_IV_size=null===(A=C.sinf)||void 0===A||null===(A=A.schi)||void 0===A?void 0:A.tenc.default_IV_size,x.audioSenc=o.mdia.minf.stbl.senc&&o.mdia.minf.stbl.senc.samples,x.useEME=e.useEME,x.kidValue=e.kidValue,x.enca=C}if(r&&(r.isVideoEncryption=!!t&&t.isVideoEncryption),t&&(t.isAudioEncryption=!!r&&r.isAudioEncryption),null!=t&&t.encv||null!=r&&r.enca){var L,O,j=null==t||null===(L=t.encv)||void 0===L?void 0:L.default_KID,R=null==r||null===(O=r.enca)||void 0===O?void 0:O.default_KID,I=j||R?(j||R).join(""):null;t&&(t.kid=I),r&&(r.kid=I)}return t&&(t.flags=3841),r&&(r.flags=1793),{videoTrack:t,audioTrack:r}}}},{key:"evaluateDefaultDuration",value:function(e,t,r){var n,i=null==t||null===(n=t.samples)||void 0===n?void 0:n.length;return i?1024*i/t.timescale*e.timescale/r:1024}},{key:"moofToSamples",value:function(t,r,n){var i={};return t.mfhd&&(r&&(r.sequenceNumber=t.mfhd.sequenceNumber),n&&(n.sequenceNumber=t.mfhd.sequenceNumber)),t.traf.forEach((function(t){var s=t.tfhd,a=t.tfdt,o=t.trun;if(s&&o){a&&(r&&r.id===s.trackId&&(r.baseMediaDecodeTime=a.baseMediaDecodeTime),n&&n.id===s.trackId&&(n.baseMediaDecodeTime=a.baseMediaDecodeTime));var u=s.defaultSampleSize||0,c=s.defaultSampleDuration||e.evaluateDefaultDuration(r,n,o.samples.length||o.sampleCount),l=o.dataOffset||0,d=0,h=-1;if(!o.samples.length&&o.sampleCount){i[s.trackId]=[];for(var f=0;f<o.sampleCount;f++)i[s.trackId].push({offset:l,dts:d,duration:c,size:u}),d+=c,l+=u}else i[s.trackId]=o.samples.map((function(e,t){return(e={offset:l,dts:d,pts:d+(e.cts||0),duration:e.duration||c,size:e.size||u,gopId:h,keyframe:0===t||null!==e.flags&&void 0!==e.flags&&(65536&e.flags)>>>0!=65536}).keyframe&&(h++,e.gopId=h),d+=e.duration,l+=e.size,e}))}})),i}},{key:"moovToSamples",value:function(e){var t=e.trak;if(t&&t.length){var r=t.find((function(e){var t;return"vide"===(null===(t=e.mdia)||void 0===t||null===(t=t.hdlr)||void 0===t?void 0:t.handlerType)})),n=t.find((function(e){var t;return"soun"===(null===(t=e.mdia)||void 0===t||null===(t=t.hdlr)||void 0===t?void 0:t.handlerType)}));if(r||n){var i,s;if(r){var a,o=null===(a=r.mdia)||void 0===a||null===(a=a.minf)||void 0===a?void 0:a.stbl;if(!o)return;var u=o.stts,c=o.stsc,l=o.stsz,d=o.stco,h=o.stss,f=o.ctts;if(!(u&&c&&l&&d&&h))return;i=Gt(u,c,l,d,f,h)}if(n){var p,v,m=null===(p=n.mdia)||void 0===p||null===(p=p.minf)||void 0===p?void 0:p.stbl;if(!m)return;var y=null===(v=n.mdia.mdhd)||void 0===v?void 0:v.timescale,_=m.stts,g=m.stsc,b=m.stsz,k=m.stco;if(!(y&&_&&g&&b&&k))return;s=Gt(_,g,b,k)}return{videoSamples:i,audioSamples:s}}}}}])}();function Gt(e,t,r,n,i,s){var a,o,u,c=[],l=null==i?void 0:i.entries,d=t.entries,h=n.entries,f=r.entrySizes,p=null==s?void 0:s.entries;p&&(a={},p.forEach((function(e){a[e-1]=!0}))),l&&(o=[],l.forEach((function(e){for(var t=e.count,r=e.offset,n=0;n<t;n++)o.push(r)})));var v=-1,m=0,y=0,_=0,g=0,b=0,k=d[0].samplesPerChunk,S=d[1]?d[1].firstChunk-1:1/0;return e.entries.forEach((function(e){for(var t=e.count,n=e.delta,i=0;i<t;i++)u={dts:m,duration:n,size:f[y]||r.sampleSize,offset:h[_]+b,index:y},p&&(u.keyframe=a[y],u.keyframe&&v++,u.gopId=v),o&&y<o.length&&(u.pts=u.dts+o[y]),c.push(u),m+=n,++y<k?b+=u.size:(_++,b=0,_>=S&&(g++,S=d[g+1]?d[g+1].firstChunk-1:1/0),k+=d[g].samplesPerChunk)})),c}function Ht(e,t){return e.dataReferenceIndex=St(t,6),e.width=St(t,24),e.height=St(t,26),e.horizresolution=xt(t,28),e.vertresolution=xt(t,32),e.frameCount=St(t,40),e.depth=St(t,74),78}function Kt(e,t){return e.dataReferenceIndex=St(t,6),e.channelCount=St(t,16),e.sampleSize=St(t,18),e.sampleRate=xt(t,24)/65536,28}function Wt(e,t,r){if(e){if(e.size!==e.data.length)throw new Error("box ".concat(e.type," size !== data.length"));var n={start:e.start,size:e.size,headerSize:e.headerSize,type:e.type};return t&&(n.version=e.data[e.headerSize],n.flags=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(e[t]<<16)+(e[t+1]<<8)+(e[t+2]||0)}(e.data,e.headerSize+1),n.headerSize+=4),r(n,e.data.subarray(n.headerSize),n.start+n.headerSize),n}}var Yt=function(e,t,r){for(var n=String(r),i=t>>0,s=Math.ceil(i/n.length),a=[],o=String(e);s--;)a.push(n);return a.join("").substring(0,i-o.length)+o},Xt=function(){for(var e=[],t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return r.forEach((function(t){e.push(Yt(Number(t).toString(16),2,0))})),e[0]},Qt=function(){return S((function e(t,r,n){b(this,e),this.videoTrack=t||new it,this.audioTrack=r||new st,this.metadataTrack=n||new dt}),[{key:"demux",value:function(e,t){var r=this.videoTrack,n=this.audioTrack,i=r.exist(),s=n.exist();if(r.samples=[],n.samples=[],t){if(!s){var a=qt.findBox(t,["moov"])[0];if(!a)throw new Error("cannot found moov box");qt.moovToTrack(qt.moov(a),null,n)}var o=qt.findBox(t,["moof"])[0];if(o){var u=qt.moofToSamples(qt.moof(o),null,n)[n.id],c=n.baseMediaDecodeTime;if(u){var l=o.start;u.map((function(e){e.offset+=l;var r=t.subarray(e.offset,e.offset+e.size);n.samples.push(new ot(e.dts+c,r,e.duration))}))}}}if(e){if(!i&&!s){var d=qt.findBox(e,["moov"])[0];if(!d)throw new Error("cannot found moov box");qt.moovToTrack(qt.moov(d),r,n)}var h=qt.findBox(e,["moof"])[0];if(h){var f,p=qt.moofToSamples(qt.moof(h),r,n),v=r.baseMediaDecodeTime,m=n.baseMediaDecodeTime,y=h.start;Object.keys(p).forEach((function(t){r.id==t?p[t].map((function(t){t.offset+=y;var n=new at((t.pts||t.dts)+v,t.dts+v);n.duration=t.duration,n.gopId=t.gopId,t.keyframe&&n.setToKeyframe();var i=e.subarray(t.offset,t.offset+t.size);n.data=i;for(var s=0,a=i.length-1;s<a;)f=xt(i,s),s+=4,n.units.push(i.subarray(s,s+f)),s+=f;r.samples.push(n)})):n.id==t&&p[t].map((function(t){t.offset+=y;var r=e.subarray(t.offset,t.offset+t.size);n.samples.push(new ot(t.dts+m,r,t.duration))}))}))}}return{videoTrack:r,audioTrack:n,metadataTrack:this.metadataTrack}}},{key:"reset",value:function(){this.videoTrack.reset(),this.audioTrack.reset(),this.metadataTrack.reset()}}],[{key:"probe",value:function(e){return qt.probe(e)}}])}();function Jt(e){for(var t=0,r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];n.forEach((function(e){t+=e.length}));var s=new e(t),a=0;return n.forEach((function(e){s.set(e,a),a+=e.length})),s}var Zt,$t=function(){return S((function e(){b(this,e),this.buffer=new Uint8Array(0)}),[{key:"write",value:function(){for(var e=this,t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];r.forEach((function(t){t?e.buffer=Jt(Uint8Array,e.buffer,t):window.console.warn(t)}))}}],[{key:"writeUint16",value:function(e){return new Uint8Array([e>>8&255,255&e])}},{key:"writeUint32",value:function(e){return new Uint8Array([e>>24,e>>16&255,e>>8&255,255&e])}}])}(),er=Math.pow(2,32)-1,tr=function(){function e(){b(this,e)}return S(e,null,[{key:"box",value:function(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=8+(r=r.filter(Boolean)).reduce((function(e,t){return e+t.byteLength}),0),s=new Uint8Array(i);s[0]=i>>24&255,s[1]=i>>16&255,s[2]=i>>8&255,s[3]=255&i,s.set(e,4);var a=8;return r.forEach((function(e){s.set(e,a),a+=e.byteLength})),s}},{key:"ftyp",value:function(t){return t.find((function(e){return e.type===Ge&&e.codecType===Ye}))?e.FTYPHEV1:e.FTYPAVC1}},{key:"initSegment",value:function(t){return bt(e.ftyp(t),e.moov(t))}},{key:"pssh",value:function(t){var r=new Uint8Array([1,0,0,0].concat([16,119,239,236,192,178,77,2,172,227,60,30,82,226,251,75],[0,0,0,1],Tt(t.kid),[0,0,0,0]));return e.box(e.types.pssh,r)}},{key:"moov",value:function(t){if(t[0].useEME&&(t[0].encv||t[0].enca)){t[0].pssh||(t[0].pssh={kid:t[0].kid});var r=this.pssh(t[0].pssh);return e.box.apply(e,[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale),e.mvex(t)].concat(j(t.map((function(t){return e.trak(t)}))),[r]))}return e.box.apply(e,[e.types.moov,e.mvhd(t[0].mvhdDurtion||t[0].duration,t[0].mvhdTimecale||t[0].timescale)].concat(j(t.map((function(t){return e.trak(t)}))),[e.mvex(t)]))}},{key:"mvhd",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4,n=e.box(e.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,t>>24&255,t>>16&255,t>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]));return n}},{key:"trak",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.tkhdDuration||0,t.width,t.height),e.mdia(t))}},{key:"tkhd",value:function(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=e.box(e.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>24&255,t>>16&255,t>>8&255,255&t,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>8&255,255&n,0,0,i>>8&255,255&i,0,0]));return s}},{key:"mdia",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minf(t))}},{key:"mdhd",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:9e4,n=e.box(e.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r,t>>24&255,t>>16&255,t>>8&255,255&t,85,196,0,0]));return n}},{key:"hdlr",value:function(t){return e.box(e.types.hdlr,e.HDLR_TYPES[t])}},{key:"minf",value:function(t){return e.box(e.types.minf,t.type===Ge?e.VMHD:e.SMHD,e.DINF,e.stbl(t))}},{key:"stbl",value:function(t){var r=[];return t&&t.ext&&t.ext.stss&&r.push(e.stss(t.ext.stss.entries)),e.box(e.types.stbl,e.stsd(t),e.STTS,r[0],e.STSC,e.STSZ,e.STCO)}},{key:"stsd",value:function(t){var r;return r="audio"===t.type?t.useEME&&t.enca?e.enca(t):e.mp4a(t):t.useEME&&t.encv?e.encv(t):e.avc1hev1(t),e.box(e.types.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),r)}},{key:"enca",value:function(t){var r=t.enca.channelCount,n=t.enca.sampleRate,i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,r,0,16,0,0,0,0,n>>8&255,255&n,0,0]),s=e.esds(t.config),a=e.sinf(t.enca);return e.box(e.types.enca,i,s,a)}},{key:"encv",value:function(t){var r,n,i=t.sps.length>0?t.sps[0]:[],s=t.pps.length>0?t.pps[0]:[],a=t.width,o=t.height,u=t.sarRatio[0],c=t.sarRatio[1],l=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,a>>8&255,255&a,o>>8&255,255&o,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),d=new Uint8Array((r=(n=[1,i[1],i[2],i[3],255,225,i.length>>>8&255,255&i.length]).concat.apply(n,j(i)).concat([1,s.length>>>8&255,255&s.length])).concat.apply(r,j(s))),h=new Uint8Array([0,0,88,57,0,15,200,192,0,4,86,72]),f=e.sinf(t.encv),p=new Uint8Array([u>>24,u>>16&255,u>>8&255,255&u,c>>24,c>>16&255,c>>8&255,255&c]);return e.box(e.types.encv,l,e.box(e.types.avcC,d),e.box(e.types.btrt,h),f,e.box(e.types.pasp,p))}},{key:"schi",value:function(t){var r=new Uint8Array([]),n=e.tenc(t);return e.box(e.types.schi,r,n)}},{key:"tenc",value:function(t){var r=new Uint8Array([0,0,0,0,0,0,255&t.default_IsEncrypted,255&t.default_IV_size].concat(Tt(t.default_KID)));return e.box(e.types.tenc,r)}},{key:"sinf",value:function(t){var r=new Uint8Array([]),n=new Uint8Array([t.data_format.charCodeAt(0),t.data_format.charCodeAt(1),t.data_format.charCodeAt(2),t.data_format.charCodeAt(3)]),i=new Uint8Array([0,0,0,0,99,101,110,99,0,1,0,0]),s=e.schi(t);return e.box(e.types.sinf,r,e.box(e.types.frma,n),e.box(e.types.schm,i),s)}},{key:"avc1hev1",value:function(t){var r=t.codecType===Ye,n=r?e.types.hvc1:e.types.avc1,i=r?e.hvcC(t):e.avcC(t),s=[new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t.width>>8&255,255&t.width,t.height>>8&255,255&t.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),i];return r?s.push(e.box(e.types.fiel,new Uint8Array([1,0]))):t.sarRatio&&t.sarRatio.length>1&&s.push(e.pasp(t.sarRatio)),e.box.apply(e,[n].concat(s))}},{key:"avcC",value:function(t){var r,n,i,s=[],a=[];return t.sps.forEach((function(e){i=e.byteLength,s.push(i>>>8&255),s.push(255&i),s.push.apply(s,j(e))})),t.pps.forEach((function(e){i=e.byteLength,a.push(i>>>8&255),a.push(255&i),a.push.apply(a,j(e))})),e.box(e.types.avcC,new Uint8Array((r=(n=[1,s[3],s[4],s[5],255,224|t.sps.length]).concat.apply(n,s).concat([t.pps.length])).concat.apply(r,a)))}},{key:"hvcC",value:function(t){var r=t.hvcC;if(r instanceof ArrayBuffer||r instanceof Uint8Array)return r;var n,i=t.vps,s=t.sps,a=t.pps;if(r){var o=r.generalProfileCompatibilityFlags,u=r.generalConstraintIndicatorFlags,c=(i.length&&1)+(s.length&&1)+(a.length&&1);n=[1,r.generalProfileSpace<<6|r.generalTierFlag<<5|r.generalProfileIdc,o>>>24,o>>>16,o>>>8,o,u[0],u[1],u[2],u[3],u[4],u[5],r.generalLevelIdc,240,0,252,252|r.chromaFormatIdc,248|r.bitDepthLumaMinus8,248|r.bitDepthChromaMinus8,0,0,r.numTemporalLayers<<3|r.temporalIdNested<<2|3,c];var l=function(e){var t;n.push(e.length>>8,e.length),(t=n).push.apply(t,j(e))};i.length&&(n.push(160,0,i.length),i.forEach(l)),s.length&&(n.push(161,0,s.length),s.forEach(l)),a.length&&(n.push(162,0,a.length),a.forEach(l))}else n=[1,1,96,0,0,0,144,0,0,0,0,0,93,240,0,252,253,248,248,0,0,15,3,160,0,1,0,24,64,1,12,1,255,255,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,153,152,9,161,0,1,0,45,66,1,1,1,96,0,0,3,0,144,0,0,3,0,0,3,0,93,160,2,128,128,45,22,89,153,164,147,43,154,128,128,128,130,0,0,3,0,2,0,0,3,0,50,16,162,0,1,0,7,68,1,193,114,180,98,64];return e.box(e.types.hvcC,new Uint8Array(n))}},{key:"pasp",value:function(t){var r=O(t,2),n=r[0],i=r[1];return e.box(e.types.pasp,new Uint8Array([n>>24,n>>16&255,n>>8&255,255&n,i>>24,i>>16&255,i>>8&255,255&i]))}},{key:"mp4a",value:function(t){return e.box(e.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,t.sampleRate>>8&255,255&t.sampleRate,0,0]),t.config.length?e.esds(t.config):void 0)}},{key:"esds",value:function(t){var r=t.length;return e.box(e.types.esds,new Uint8Array([0,0,0,0,3,23+r,0,0,0,4,15+r,64,21,0,6,0,0,0,218,192,0,0,218,192,5].concat([r]).concat(t).concat([6,1,2])))}},{key:"mvex",value:function(t){return e.box.apply(e,[e.types.mvex].concat(j(t.map((function(t){return e.trex(t.id)})))))}},{key:"trex",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}},{key:"trex1",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,2,0,0,0,0,0,0,1,0,0]))}},{key:"trex2",value:function(t){return e.box(e.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t,0,0,0,1,0,0,4,0,0,0,0,0,2,0,0,0]))}},{key:"moof",value:function(t){return e.box.apply(e,[e.types.moof,e.mfhd(t[0].samples?t[0].samples[0].gopId:0)].concat(j(t.map((function(t){return e.traf(t)})))))}},{key:"mfhd",value:function(t){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"traf",value:function(t){var r=e.tfhd(t.id),n=e.tfdt(t,t.baseMediaDecodeTime),i=0;if(t.isVideo&&t.videoSenc&&t.videoSenc.forEach((function(e){i+=8,e.subsamples&&e.subsamples.length&&(i+=2,i+=6*e.subsamples.length)})),t.videoSencLength=i,t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)){if(t.isVideoEncryption){if(t.isVideo){var s=e.saiz(t),a=e.saio(t),o=e.trun1(t),u=e.senc(t);return e.box(e.types.traf,r,n,s,a,o,u)}if(t.isAudioEncryption){var c=e.sbgp(),l=e.saiz(t),d=e.saio(t),h=e.senc(t),f=e.trun1(t);return e.box(e.types.traf,r,n,c,l,d,h,f)}var p=e.sbgp(),v=e.trun1(t);return e.box(e.types.traf,r,n,p,v)}if(t.isVideo){var m=e.trun1(t);return e.box(e.types.traf,r,n,m)}var y=e.sbgp(),_=e.saiz(t),g=e.saio(t),b=e.senc(t),k=e.trun1(t);return e.box(e.types.traf,r,n,y,_,g,b,k)}var S=e.sdtp(t);return e.box(e.types.traf,r,n,S,e.trun(t.samples,S.byteLength+76))}},{key:"sdtp",value:function(t){var r=new $t;return t.samples.forEach((function(e){r.write(new Uint8Array(t.isVideo?[e.keyframe?32:16]:[16]))})),e.box(e.types.sdtp,this.extension(0,0),r.buffer)}},{key:"trun1",value:function(t){var r=new $t,n=$t.writeUint32(t.samples.length),i=null;if(t.isVideo){var s=t.videoSencLength;i=$t.writeUint32(16*t.samples.length+s+149),!t.isVideoEncryption&&t.isAudioEncryption&&(i=$t.writeUint32(16*t.samples.length+92))}else{var a=12*t.samples.length+124;t.isAudioEncryption&&(a=12*t.samples.length+8*t.audioSenc.length+177),i=$t.writeUint32(a)}return t.samples.forEach((function(e){r.write($t.writeUint32(e.duration)),r.write($t.writeUint32(e.size)),r.write($t.writeUint32(e.keyframe?33554432:65536)),t.isVideo&&r.write($t.writeUint32(e.cts?e.cts:0))})),e.box(e.types.trun,this.extension(0,t.flags),n,i,r.buffer)}},{key:"senc",value:function(t){var r=new $t,n=t.samples.length,i=t.isVideo?16:8,s=t.isVideo?2:0,a=[],o=0;return t.isVideo?(a=t.videoSenc,o=t.videoSencLength):a=t.audioSenc,o=o||i*n,r.write($t.writeUint32(16+o),e.types.senc,this.extension(0,s)),r.write($t.writeUint32(n)),a.forEach((function(e){for(var t=0;t<e.InitializationVector.length;t++)r.write(new Uint8Array([e.InitializationVector[t]]));e.subsamples&&e.subsamples.length&&(r.write($t.writeUint16(e.subsamples.length)),e.subsamples.forEach((function(e){r.write($t.writeUint16(e.BytesOfClearData)),r.write($t.writeUint32(e.BytesOfProtectedData))})))})),r.buffer}},{key:"saio",value:function(t){var r=12*t.samples.length+141;!t.isVideo&&t.isAudioEncryption&&(r=149);var n=new Uint8Array([1,0,0,0,0,0,0,1,0,0,0,0,r>>24&255,r>>16&255,r>>8&255,255&r]);return e.box(e.types.saio,n)}},{key:"saiz",value:function(t){var r=t.samples.length,n=new Uint8Array([0,0,0,0,16,r>>24&255,r>>16&255,r>>8&255,255&r]);return e.box(e.types.saiz,n)}},{key:"sbgp",value:function(){var t=new Uint8Array([114,111,108,108,0,0,0,1,0,0,1,25,0,0,0,1]);return e.box(e.types.sbgp,this.extension(0,0),t)}},{key:"extension",value:function(e,t){return new Uint8Array([e,t>>16&255,t>>8&255,255&t])}},{key:"tfhd",value:function(t){return e.box(e.types.tfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))}},{key:"tfdt",value:function(t,r){var n=Math.floor(r/(er+1)),i=Math.floor(r%(er+1));return t.useEME&&(t.isVideoEncryption||t.isAudioEncryption)?e.box(e.types.tfdt,new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i])):e.box(e.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,i>>24,i>>16&255,i>>8&255,255&i]))}},{key:"trun",value:function(t,r){var n=t.length,i=12+16*n;r+=8+i;var s=new Uint8Array(i);s.set([0,0,15,1,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0);for(var a=0;a<n;a++){var o=t[a],u=o.duration,c=o.size,l=o.flag,d=void 0===l?{}:l,h=o.cts,f=void 0===h?0:h;s.set([u>>>24&255,u>>>16&255,u>>>8&255,255&u,c>>>24&255,c>>>16&255,c>>>8&255,255&c,d.isLeading<<2|(null===d.dependsOn||void 0===d.dependsOn?1:d.dependsOn),d.isDependedOn<<6|d.hasRedundancy<<4|d.paddingValue<<1|(null===d.isNonSyncSample||void 0===d.isNonSyncSample?1:d.isNonSyncSample),61440&d.degradationPriority,15&d.degradationPriority,f>>>24&255,f>>>16&255,f>>>8&255,255&f],12+16*a)}return e.box(e.types.trun,s)}},{key:"moovMP4",value:function(t){return e.box.apply(e,[e.types.moov,e.mvhd(t[0].duration,t[0].timescale)].concat(j(t.map((function(t){return e.trackMP4(t)})))))}},{key:"trackMP4",value:function(t){return e.box(e.types.trak,e.tkhd(t.id,t.duration,t.width,t.height),e.mdiaMP4(t))}},{key:"mdiaMP4",value:function(t){return e.box(e.types.mdia,e.mdhd(t.duration,t.timescale),e.hdlr(t.type),e.minfMP4(t))}},{key:"minfMP4",value:function(t){return e.box(e.types.minf,t.type===Ge?e.VMHD:e.SMHD,e.DINF,e.stblMP4(t))}},{key:"stblMP4",value:function(t){var r=t.ext,n=[e.stsd(t),e.stts(r.stts),e.stsc(r.stsc),e.stsz(r.stsz),e.stco(r.stco)];return r.stss.length&&n.push(e.stss(r.stss)),r.ctts.length&&n.push(e.ctts(r.ctts)),e.box.apply(e,[e.types.stbl].concat(n))}},{key:"stts",value:function(t){var r=t.length,n=new Uint8Array(8*r),i=0;return t.forEach((function(e){var t=e.value,r=e.count;n.set([r>>24,r>>16&255,r>>8&255,255&r,t>>24,t>>16&255,t>>8&255,255&t],i),i+=8})),e.box(e.types.stts,bt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stsc",value:function(t){var r=t.length,n=new Uint8Array(12*r),i=0;return t.forEach((function(e){var t=e.firstChunk,r=e.samplesPerChunk,s=e.sampleDescIndex;n.set([t>>24,t>>16&255,t>>8&255,255&t,r>>24,r>>16&255,r>>8&255,255&r,s>>24,s>>16&255,s>>8&255,255&s],i),i+=12})),e.box(e.types.stsc,bt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stsz",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stsz,bt(new Uint8Array([0,0,0,0,0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stco",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stco,bt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"stss",value:function(t){var r=t.length,n=new Uint8Array(4*r),i=0;return t.forEach((function(e){n.set([e>>24,e>>16&255,e>>8&255,255&e],i),i+=4})),e.box(e.types.stss,bt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"ctts",value:function(t){var r=t.length,n=new Uint8Array(8*r),i=0;return t.forEach((function(e){var t=e.value,r=e.count;n.set([r>>24,r>>16&255,r>>8&255,255&r,t>>24,t>>16&255,t>>8&255,255&t],i),i+=8})),e.box(e.types.ctts,bt(new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r]),n))}},{key:"styp",value:function(){return e.box(e.types.styp,new Uint8Array([109,115,100,104,0,0,0,0,109,115,100,104,109,115,105,120]))}},{key:"sidx",value:function(t){var r=t.timescale,n=t.samples[0].duration,i=n*t.samples.length,s=t.samples[0].sampleOffset*n,a=8;t.samples.forEach((function(e){a+=e.size}));var o=0;if(t.isVideo){var u,c=0;t.videoSenc&&(u=t.videoSenc),t.isVideo&&u.forEach((function(e){c+=8,e.subsamples&&e.subsamples.length&&(c+=2,c+=6*e.subsamples.length)})),t.videoSencLength=c,o=a+141+16*t.samples.length+c,t.useEME&&t.isAudioEncryption&&!t.isVideoEncryption&&(o=a+16*t.samples.length+84)}else o=a+116+12*t.samples.length,t.useEME&&t.isAudioEncryption&&(o=a+169+12*t.samples.length+8*t.audioSenc.length);var l=new Uint8Array([0,0,0,0,0,0,0,255&t.id,r>>24&255,r>>16&255,r>>8&255,255&r,s>>24&255,s>>16&255,s>>8&255,255&s,0,0,0,0,0,0,0,1,0,o>>16&255,o>>8&255,255&o,i>>24&255,i>>16&255,i>>8&255,255&i,144,0,0,0]);return e.box(e.types.sidx,l)}},{key:"mdat",value:function(t){return e.box(e.types.mdat,t)}}])}();Zt=tr,x(tr,"types",["avc1","avcC","hvc1","hvcC","dinf","dref","esds","ftyp","hdlr","mdat","mdhd","mdia","mfhd","minf","moof","moov","mp4a","mvex","mvhd","pasp","stbl","stco","stsc","stsd","stsz","stts","tfdt","tfhd","traf","trak","trex","tkhd","vmhd","smhd","ctts","stss","styp","pssh","sidx","sbgp","saiz","saio","senc","trun","encv","enca","sinf","btrt","frma","tenc","schm","schi","mehd","fiel","sdtp"].reduce((function(e,t){return e[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)],e}),Object.create(null))),x(tr,"HDLR_TYPES",{video:new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),audio:new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0])}),x(tr,"FTYPAVC1",Zt.box(Zt.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]))),x(tr,"FTYPHEV1",Zt.box(Zt.types.ftyp,new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,104,101,118,49]))),x(tr,"DINF",Zt.box(Zt.types.dinf,Zt.box(Zt.types.dref,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1])))),x(tr,"VMHD",Zt.box(Zt.types.vmhd,new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]))),x(tr,"SMHD",Zt.box(Zt.types.smhd,new Uint8Array([0,0,0,0,0,0,0,0]))),x(tr,"StblTable",new Uint8Array([0,0,0,0,0,0,0,0])),x(tr,"STTS",Zt.box(Zt.types.stts,Zt.StblTable)),x(tr,"STSC",Zt.box(Zt.types.stsc,Zt.StblTable)),x(tr,"STSZ",Zt.box(Zt.types.stsz,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]))),x(tr,"STCO",Zt.box(Zt.types.stco,Zt.StblTable));var rr=function(){function e(t,r){b(this,e),this.name=t||"",this._prefix="[".concat(this.name,"]"),e.disabled=r}return S(e,[{key:"debug",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).debug.apply(t,[this._prefix].concat(n))}}},{key:"log",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).log.apply(t,[this._prefix].concat(n))}}},{key:"warn",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).warn.apply(t,[this._prefix].concat(n))}}},{key:"error",value:function(){var t;if(!e.disabled){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];(t=console).error.apply(t,[this._prefix].concat(n))}}},{key:"table",value:function(){var t;e.disabled||(console.group(this._prefix),(t=console).table.apply(t,arguments),console.groupEnd())}}],[{key:"enable",value:function(){e.disabled=!1}},{key:"disable",value:function(){e.disabled=!0}}])}();x(rr,"disabled",!0);for(var nr=function(){return S((function e(t,r,n){b(this,e),this.videoTrack=t,this.audioTrack=r;var i=/Chrome\/([^.]+)/.exec(navigator.userAgent);this.forceFirstIDR=i&&Number(i[1])<50,this.log=new rr("FMP4Remuxer",!n||!n.openLog||!n.openLog)}),[{key:"remux",value:function(){var e,t,r,n,i,s=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=this.videoTrack,u=this.audioTrack,c=o.exist(),l=u.exist(),d=[];return s&&(a&&a.initMerge?(c&&d.push(this.videoTrack),l&&d.push(this.audioTrack),r=tr.initSegment(d)):(c&&(e=tr.initSegment([this.videoTrack])),l&&(t=tr.initSegment([this.audioTrack])))),c&&o.hasSample()&&(n=this._remuxVideo()),l&&u.hasSample()&&(i=this._remuxAudio()),o.samples=[],u.samples=[],{initSegment:r,videoInitSegment:e,audioInitSegment:t,videoSegment:n,audioSegment:i}}},{key:"_remuxVideo",value:function(){var e=this.videoTrack;this.forceFirstIDR&&(e.samples[0].flag={dependsOn:2,isNonSyncSample:0});var t=e.samples,r=0;t.forEach((function(e){r+=e.units.reduce((function(e,t){return e+t.byteLength}),0),r+=4*e.units.length}));for(var n,i=new Uint8Array(r),s=new DataView(i.buffer),a=function(e,r){r=t[o];var a=0;r.units.forEach((function(t){s.setUint32(e,t.byteLength),e+=4,i.set(t,e),e+=t.byteLength,a+=4+t.byteLength})),r.size=a,c=e,n=r},o=0,u=t.length,c=0;o<u;o++)a(c,n);var l=tr.mdat(i);return bt(tr.moof([e]),l)}},{key:"_remuxAudio",value:function(){var e=this.audioTrack,t=new Uint8Array(e.samples.reduce((function(e,t){return e+t.size}),0));e.samples.reduce((function(e,r){return t.set(r.data,e),e+r.size}),0);var r=tr.mdat(t);return bt(tr.moof([e]),r)}},{key:"reset",value:function(){this.videoTrack.reset(),this.audioTrack.reset()}}])}(),ir=function(){return S((function e(){b(this,e)}),[{key:"mixIn",value:function(e){return Object.assign(this,e)}},{key:"clone",value:function(){var e=new this.constructor;return Object.assign(e,this),e}}],[{key:"create",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return d(this,t)}}])}(),sr=function(e){function t(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4*r.length;b(this,t),e=l(this,t);var i=r;if(i instanceof ArrayBuffer&&(i=new Uint8Array(i)),(i instanceof Int8Array||i instanceof Uint8ClampedArray||i instanceof Int16Array||i instanceof Uint16Array||i instanceof Int32Array||i instanceof Uint32Array||i instanceof Float32Array||i instanceof Float64Array)&&(i=new Uint8Array(i.buffer,i.byteOffset,i.byteLength)),i instanceof Uint8Array){for(var s=i.byteLength,a=[],o=0;o<s;o+=1)a[o>>>2]|=i[o]<<24-o%4*8;e.words=a,e.sigBytes=s}else e.words=r,e.sigBytes=n;return e}return E(t,e),S(t,[{key:"toString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ar;return e.stringify(this)}},{key:"concat",value:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var s=0;s<i;s+=1){var a=r[s>>>2]>>>24-s%4*8&255;t[n+s>>>2]|=a<<24-(n+s)%4*8}else for(var o=0;o<i;o+=4)t[n+o>>>2]=r[o>>>2];return this.sigBytes+=i,this}},{key:"clamp",value:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=Math.ceil(t/4)}},{key:"clone",value:function(){var e=L(w(t.prototype),"clone",this).call(this);return e.words=this.words.slice(0),e}}],[{key:"random",value:function(e){for(var r,n=[],i=function(e){var t=e,r=987654321,n=4294967295;return function(){var e=((r=36969*(65535&r)+(r>>16)&n)<<16)+(t=18e3*(65535&t)+(t>>16)&n)&n;return e/=4294967296,(e+=.5)*(Math.random()>.5?1:-1)}},s=0;s<e;s+=4){var a=i(4294967296*(r||Math.random()));r=987654071*a(),n.push(4294967296*a()|0)}return new t(n,e)}}])}(ir),ar={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i+=1){var s=t[i>>>2]>>>24-i%4*8&255;n.push((s>>>4).toString(16)),n.push((15&s).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new sr(r,t/2)}},or=function(e){for(var t=e.length,r=[],n=0;n<t;n+=1)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new sr(r,t)},ur=function(e){return or(unescape(encodeURIComponent(e)))},cr=function(e){function t(){var e;return b(this,t),(e=l(this,t))._minBufferSize=0,e}return E(t,e),S(t,[{key:"reset",value:function(){this._data=new sr,this._nDataBytes=0}},{key:"_append",value:function(e){var t=e;"string"==typeof t&&(t=ur(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}},{key:"_process",value:function(e){var t,r=this._data,n=this.blockSize,i=r.words,s=r.sigBytes,a=s/(4*n),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*n,u=Math.min(4*o,s);if(o){for(var c=0;c<o;c+=n)this._doProcessBlock(i,c);t=i.splice(0,o),r.sigBytes-=u}return new sr(t,u)}},{key:"clone",value:function(){var e=L(w(t.prototype),"clone",this).call(this);return e._data=this._data.clone(),e}}])}(ir),lr=function(e){function t(e){var r;return b(this,t),(r=l(this,t)).blockSize=16,r.cfg=Object.assign(new ir,e),r.reset(),r}return E(t,e),S(t,[{key:"reset",value:function(){L(w(t.prototype),"reset",this).call(this),this._doReset()}},{key:"update",value:function(e){return this._append(e),this._process(),this}},{key:"finalize",value:function(e){return e&&this._append(e),this._doFinalize()}}],[{key:"_createHelper",value:function(e){return function(t,r){return new e(r).finalize(t)}}},{key:"_createHmacHelper",value:function(e){return function(t,r){return new dr(e,r).finalize(t)}}}])}(cr),dr=function(e){function t(e,r){var n;b(this,t),n=l(this,t);var i=new e;n._hasher=i;var s=r;"string"==typeof s&&(s=ur(s));var a=i.blockSize,o=4*a;s.sigBytes>o&&(s=i.finalize(r)),s.clamp();var u=s.clone();n._oKey=u;var c=s.clone();n._iKey=c;for(var d=u.words,h=c.words,f=0;f<a;f+=1)d[f]^=1549556828,h[f]^=909522486;return u.sigBytes=o,c.sigBytes=o,n.reset(),n}return E(t,e),S(t,[{key:"reset",value:function(){var e=this._hasher;e.reset(),e.update(this._iKey)}},{key:"update",value:function(e){return this._hasher.update(e),this}},{key:"finalize",value:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}}])}(ir),hr={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],s=0;s<r;s+=3)for(var a=(t[s>>>2]>>>24-s%4*8&255)<<16|(t[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|t[s+2>>>2]>>>24-(s+2)%4*8&255,o=0;o<4&&s+.75*o<r;o+=1)i.push(n.charAt(a>>>6*(3-o)&63));var u=n.charAt(64);if(u)for(;i.length%4;)i.push(u);return i.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){this._reverseMap=[],n=this._reverseMap;for(var i=0;i<r.length;i+=1)n[r.charCodeAt(i)]=i}var s=r.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return function(e,t,r){for(var n=[],i=0,s=0;s<t;s+=1)if(s%4){var a=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;n[i>>>2]|=a<<24-i%4*8,i+=1}return sr.create(n,i)}(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},fr=[],pr=0;pr<64;pr+=1)fr[pr]=4294967296*Math.abs(Math.sin(pr+1))|0;var vr=function(e,t,r,n,i,s,a){var o=e+(t&r|~t&n)+i+a;return(o<<s|o>>>32-s)+t},mr=function(e,t,r,n,i,s,a){var o=e+(t&n|r&~n)+i+a;return(o<<s|o>>>32-s)+t},yr=function(e,t,r,n,i,s,a){var o=e+(t^r^n)+i+a;return(o<<s|o>>>32-s)+t},_r=function(e,t,r,n,i,s,a){var o=e+(r^(t|~n))+i+a;return(o<<s|o>>>32-s)+t},gr=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t,[{key:"_doReset",value:function(){this._hash=new sr([1732584193,4023233417,2562383102,271733878])}},{key:"_doProcessBlock",value:function(e,t){for(var r=e,n=0;n<16;n+=1){var i=t+n,s=e[i];r[i]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}var a=this._hash.words,o=r[t+0],u=r[t+1],c=r[t+2],l=r[t+3],d=r[t+4],h=r[t+5],f=r[t+6],p=r[t+7],v=r[t+8],m=r[t+9],y=r[t+10],_=r[t+11],g=r[t+12],b=r[t+13],k=r[t+14],S=r[t+15],x=a[0],E=a[1],w=a[2],T=a[3];x=vr(x,E,w,T,o,7,fr[0]),T=vr(T,x,E,w,u,12,fr[1]),w=vr(w,T,x,E,c,17,fr[2]),E=vr(E,w,T,x,l,22,fr[3]),x=vr(x,E,w,T,d,7,fr[4]),T=vr(T,x,E,w,h,12,fr[5]),w=vr(w,T,x,E,f,17,fr[6]),E=vr(E,w,T,x,p,22,fr[7]),x=vr(x,E,w,T,v,7,fr[8]),T=vr(T,x,E,w,m,12,fr[9]),w=vr(w,T,x,E,y,17,fr[10]),E=vr(E,w,T,x,_,22,fr[11]),x=vr(x,E,w,T,g,7,fr[12]),T=vr(T,x,E,w,b,12,fr[13]),w=vr(w,T,x,E,k,17,fr[14]),E=vr(E,w,T,x,S,22,fr[15]),x=mr(x,E,w,T,u,5,fr[16]),T=mr(T,x,E,w,f,9,fr[17]),w=mr(w,T,x,E,_,14,fr[18]),E=mr(E,w,T,x,o,20,fr[19]),x=mr(x,E,w,T,h,5,fr[20]),T=mr(T,x,E,w,y,9,fr[21]),w=mr(w,T,x,E,S,14,fr[22]),E=mr(E,w,T,x,d,20,fr[23]),x=mr(x,E,w,T,m,5,fr[24]),T=mr(T,x,E,w,k,9,fr[25]),w=mr(w,T,x,E,l,14,fr[26]),E=mr(E,w,T,x,v,20,fr[27]),x=mr(x,E,w,T,b,5,fr[28]),T=mr(T,x,E,w,c,9,fr[29]),w=mr(w,T,x,E,p,14,fr[30]),E=mr(E,w,T,x,g,20,fr[31]),x=yr(x,E,w,T,h,4,fr[32]),T=yr(T,x,E,w,v,11,fr[33]),w=yr(w,T,x,E,_,16,fr[34]),E=yr(E,w,T,x,k,23,fr[35]),x=yr(x,E,w,T,u,4,fr[36]),T=yr(T,x,E,w,d,11,fr[37]),w=yr(w,T,x,E,p,16,fr[38]),E=yr(E,w,T,x,y,23,fr[39]),x=yr(x,E,w,T,b,4,fr[40]),T=yr(T,x,E,w,o,11,fr[41]),w=yr(w,T,x,E,l,16,fr[42]),E=yr(E,w,T,x,f,23,fr[43]),x=yr(x,E,w,T,m,4,fr[44]),T=yr(T,x,E,w,g,11,fr[45]),w=yr(w,T,x,E,S,16,fr[46]),E=yr(E,w,T,x,c,23,fr[47]),x=_r(x,E,w,T,o,6,fr[48]),T=_r(T,x,E,w,p,10,fr[49]),w=_r(w,T,x,E,k,15,fr[50]),E=_r(E,w,T,x,h,21,fr[51]),x=_r(x,E,w,T,g,6,fr[52]),T=_r(T,x,E,w,l,10,fr[53]),w=_r(w,T,x,E,y,15,fr[54]),E=_r(E,w,T,x,u,21,fr[55]),x=_r(x,E,w,T,v,6,fr[56]),T=_r(T,x,E,w,S,10,fr[57]),w=_r(w,T,x,E,f,15,fr[58]),E=_r(E,w,T,x,b,21,fr[59]),x=_r(x,E,w,T,d,6,fr[60]),T=_r(T,x,E,w,_,10,fr[61]),w=_r(w,T,x,E,c,15,fr[62]),E=_r(E,w,T,x,m,21,fr[63]),a[0]=a[0]+x|0,a[1]=a[1]+E|0,a[2]=a[2]+w|0,a[3]=a[3]+T|0}},{key:"_doFinalize",value:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32;var i=Math.floor(r/4294967296),s=r;t[15+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),t[14+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),e.sigBytes=4*(t.length+1),this._process();for(var a=this._hash,o=a.words,u=0;u<4;u+=1){var c=o[u];o[u]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}return a}},{key:"clone",value:function(){var e=L(w(t.prototype),"clone",this).call(this);return e._hash=this._hash.clone(),e}}])}(lr);lr._createHelper(gr),lr._createHmacHelper(gr);var br=function(e){function t(e){var r;return b(this,t),(r=l(this,t)).cfg=Object.assign(new ir,{keySize:4,hasher:gr,iterations:1},e),r}return E(t,e),S(t,[{key:"compute",value:function(e,t){for(var r,n=this.cfg,i=n.hasher.create(),s=sr.create(),a=s.words,o=n.keySize,u=n.iterations;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(var c=1;c<u;c+=1)r=i.finalize(r),i.reset();s.concat(r)}return s.sigBytes=4*o,s}}])}(ir),kr=function(e){function t(e,r,n){var i;return b(this,t),(i=l(this,t)).cfg=Object.assign(new ir,n),i._xformMode=e,i._key=r,i.reset(),i}return E(t,e),S(t,[{key:"reset",value:function(){L(w(t.prototype),"reset",this).call(this),this._doReset()}},{key:"process",value:function(e){return this._append(e),this._process()}},{key:"finalize",value:function(e){return e&&this._append(e),this._doFinalize()}}],[{key:"createEncryptor",value:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}},{key:"createDecryptor",value:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}},{key:"_createHelper",value:function(e){var t=function(e){return"string"==typeof e?Or:Cr};return{encrypt:function(r,n,i){return t(n).encrypt(e,r,n,i)},decrypt:function(r,n,i){return t(n).decrypt(e,r,n,i)}}}}])}(cr);kr._ENC_XFORM_MODE=1,kr._DEC_XFORM_MODE=2,kr.keySize=4,kr.ivSize=4;var Sr=function(e){function t(e,r){var n;return b(this,t),(n=l(this,t))._cipher=e,n._iv=r,n}return E(t,e),S(t,null,[{key:"createEncryptor",value:function(e,t){return this.Encryptor.create(e,t)}},{key:"createDecryptor",value:function(e,t){return this.Decryptor.create(e,t)}}])}(ir);function xr(e,t,r){var n,i=e,s=this._iv;s?(n=s,this._iv=void 0):n=this._prevBlock;for(var a=0;a<r;a+=1)i[t+a]^=n[a]}var Er=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t)}(Sr);Er.Encryptor=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t,[{key:"processBlock",value:function(e,t){var r=this._cipher,n=r.blockSize;xr.call(this,e,t,n),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}}])}(Er),Er.Decryptor=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t,[{key:"processBlock",value:function(e,t){var r=this._cipher,n=r.blockSize,i=e.slice(t,t+n);r.decryptBlock(e,t),xr.call(this,e,t,n),this._prevBlock=i}}])}(Er);var wr={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[],a=0;a<n;a+=4)s.push(i);var o=sr.create(s,n);e.concat(o)},unpad:function(e){var t=e,r=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=r}},Tr=function(e){function t(e,r,n){var i;return b(this,t),(i=l(this,t,[e,r,Object.assign({mode:Er,padding:wr},n)])).blockSize=4,i}return E(t,e),S(t,[{key:"reset",value:function(){var e;L(w(t.prototype),"reset",this).call(this);var r=this.cfg,n=r.iv,i=r.mode;this._xformMode===this.constructor._ENC_XFORM_MODE?e=i.createEncryptor:(e=i.createDecryptor,this._minBufferSize=1),this._mode=e.call(i,this,n&&n.words),this._mode.__creator=e}},{key:"_doProcessBlock",value:function(e,t){this._mode.processBlock(e,t)}},{key:"_doFinalize",value:function(){var e,t=this.cfg.padding;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}}])}(kr),Dr=function(e){function t(e){var r;return b(this,t),(r=l(this,t)).mixIn(e),r}return E(t,e),S(t,[{key:"toString",value:function(e){return(e||this.formatter).stringify(this)}}])}(ir),Ar={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?sr.create([1398893684,1701076831]).concat(r).concat(t):t).toString(hr)},parse:function(e){var t,r=hr.parse(e),n=r.words;return 1398893684===n[0]&&1701076831===n[1]&&(t=sr.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),Dr.create({ciphertext:r,salt:t})}},Cr=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t,null,[{key:"encrypt",value:function(e,t,r,n){var i=Object.assign(new ir,this.cfg,n),s=e.createEncryptor(r,i),a=s.finalize(t),o=s.cfg;return Dr.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:s.blockSize,formatter:i.format})}},{key:"decrypt",value:function(e,t,r,n){var i=t,s=Object.assign(new ir,this.cfg,n);return i=this._parse(i,s.format),e.createDecryptor(r,s).finalize(i.ciphertext)}},{key:"_parse",value:function(e,t){return"string"==typeof e?t.parse(e,this):e}}])}(ir);Cr.cfg=Object.assign(new ir,{format:Ar});var Lr={execute:function(e,t,r,n){var i=n;i||(i=sr.random(8));var s=br.create({keySize:t+r}).compute(e,i),a=sr.create(s.words.slice(t),4*r);return s.sigBytes=4*t,Dr.create({key:s,iv:a,salt:i})}},Or=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t,null,[{key:"encrypt",value:function(e,t,r,n){var i=Object.assign(new ir,this.cfg,n),s=i.kdf.execute(r,e.keySize,e.ivSize);i.iv=s.iv;var a=Cr.encrypt.call(this,e,t,s.key,i);return a.mixIn(s),a}},{key:"decrypt",value:function(e,t,r,n){var i=t,s=Object.assign(new ir,this.cfg,n);i=this._parse(i,s.format);var a=s.kdf.execute(r,e.keySize,e.ivSize,i.salt);return s.iv=a.iv,Cr.decrypt.call(this,e,i,a.key,s)}}])}(Cr);Or.cfg=Object.assign(Cr.cfg,{kdf:Lr});for(var jr=[],Rr=[],Ir=[],Pr=[],Br=[],Ur=[],Mr=[],Nr=[],Fr=[],Vr=[],zr=[],qr=0;qr<256;qr+=1)zr[qr]=qr<128?qr<<1:qr<<1^283;for(var Gr=0,Hr=0,Kr=0;Kr<256;Kr+=1){var Wr=Hr^Hr<<1^Hr<<2^Hr<<3^Hr<<4;Wr=Wr>>>8^255&Wr^99,jr[Gr]=Wr,Rr[Wr]=Gr;var Yr=zr[Gr],Xr=zr[Yr],Qr=zr[Xr],Jr=257*zr[Wr]^16843008*Wr;Ir[Gr]=Jr<<24|Jr>>>8,Pr[Gr]=Jr<<16|Jr>>>16,Br[Gr]=Jr<<8|Jr>>>24,Ur[Gr]=Jr,Jr=16843009*Qr^65537*Xr^257*Yr^16843008*Gr,Mr[Wr]=Jr<<24|Jr>>>8,Nr[Wr]=Jr<<16|Jr>>>16,Fr[Wr]=Jr<<8|Jr>>>24,Vr[Wr]=Jr,Gr?(Gr=Yr^zr[zr[zr[Qr^Yr]]],Hr^=zr[zr[Hr]]):Gr=Hr=1}var Zr=[0,1,2,4,8,16,32,64,128,27,54],$r=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t,[{key:"_doReset",value:function(){var e;if(!this._nRounds||this._keyPriorReset!==this._key){this._keyPriorReset=this._key;var t=this._keyPriorReset,r=t.words,n=t.sigBytes/4;this._nRounds=n+6;var i=4*(this._nRounds+1);this._keySchedule=[];for(var s=this._keySchedule,a=0;a<i;a+=1)a<n?s[a]=r[a]:(e=s[a-1],a%n?n>6&&a%n==4&&(e=jr[e>>>24]<<24|jr[e>>>16&255]<<16|jr[e>>>8&255]<<8|jr[255&e]):(e=jr[(e=e<<8|e>>>24)>>>24]<<24|jr[e>>>16&255]<<16|jr[e>>>8&255]<<8|jr[255&e],e^=Zr[a/n|0]<<24),s[a]=s[a-n]^e);this._invKeySchedule=[];for(var o=this._invKeySchedule,u=0;u<i;u+=1){var c=i-u;e=u%4?s[c]:s[c-4],o[u]=u<4||c<=4?e:Mr[jr[e>>>24]]^Nr[jr[e>>>16&255]]^Fr[jr[e>>>8&255]]^Vr[jr[255&e]]}}}},{key:"encryptBlock",value:function(e,t){this._doCryptBlock(e,t,this._keySchedule,Ir,Pr,Br,Ur,jr)}},{key:"decryptBlock",value:function(e,t){var r=e,n=r[t+1];r[t+1]=r[t+3],r[t+3]=n,this._doCryptBlock(r,t,this._invKeySchedule,Mr,Nr,Fr,Vr,Rr),n=r[t+1],r[t+1]=r[t+3],r[t+3]=n}},{key:"_doCryptBlock",value:function(e,t,r,n,i,s,a,o){for(var u=e,c=this._nRounds,l=u[t]^r[0],d=u[t+1]^r[1],h=u[t+2]^r[2],f=u[t+3]^r[3],p=4,v=1;v<c;v+=1){var m=n[l>>>24]^i[d>>>16&255]^s[h>>>8&255]^a[255&f]^r[p];p+=1;var y=n[d>>>24]^i[h>>>16&255]^s[f>>>8&255]^a[255&l]^r[p];p+=1;var _=n[h>>>24]^i[f>>>16&255]^s[l>>>8&255]^a[255&d]^r[p];p+=1;var g=n[f>>>24]^i[l>>>16&255]^s[d>>>8&255]^a[255&h]^r[p];p+=1,l=m,d=y,h=_,f=g}var b=(o[l>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&f])^r[p];p+=1;var k=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[f>>>8&255]<<8|o[255&l])^r[p];p+=1;var S=(o[h>>>24]<<24|o[f>>>16&255]<<16|o[l>>>8&255]<<8|o[255&d])^r[p];p+=1;var x=(o[f>>>24]<<24|o[l>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p];p+=1,u[t]=b,u[t+1]=k,u[t+2]=S,u[t+3]=x}}])}(Tr);$r.keySize=8,Tr._createHelper($r);var en=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t)}(Sr);en.Encryptor=function(e){function t(){return b(this,t),l(this,t,arguments)}return E(t,e),S(t,[{key:"processBlock",value:function(e,t){var r=e,n=this._cipher,i=n.blockSize,s=this._iv,a=this._counter;s&&(this._counter=s.slice(0),a=this._counter,this._iv=void 0);var o=a.slice(0);n.encryptBlock(o,0),a[i-1]=a[i-1]+1|0;for(var u=0;u<i;u+=1)r[t+u]^=o[u]}}])}(en),en.Decryptor=en.Encryptor;var tn=function(){return S((function e(){b(this,e);var t=window.crypto||window.msCrypto;this.subtle=t&&(t.subtle||t.webkitSubtle),this.externalDecryptor=null}),[{key:"destroy",value:function(){var e;null!==(e=this.externalDecryptor)&&void 0!==e&&e.destroy&&this.externalDecryptor.destroy()}},{key:"decrypt",value:function(e,t){if(e||t){var r=[];return e&&(r[0]=this._decryptSegment(e)),t&&(r[1]=this._decryptSegment(t)),Promise.all(r)}}},{key:"_decryptSegment",value:(t=g(v().mark((function e(t){var r;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.data,!t.key){e.next=5;break}return e.next=4,this._decryptData(t.data,t.key,t.keyIv);case 4:r=e.sent;case 5:if(t.map){e.next=7;break}return e.abrupt("return",r);case 7:return e.abrupt("return",Be(t.map,r));case 8:case"end":return e.stop()}}),e,this)}))),function(e){return t.apply(this,arguments)})},{key:"_decryptData",value:(e=g(v().mark((function e(t,r,n){var i;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.externalDecryptor){e.next=6;break}return e.next=3,this.externalDecryptor.decrypt(t,r,n);case 3:return e.abrupt("return",e.sent);case 6:if(this.subtle){e.next=8;break}throw new Error("crypto is not defined");case 8:return e.next=10,this.subtle.importKey("raw",r,{name:"AES-CBC"},!1,["encrypt","decrypt"]);case 10:return i=e.sent,e.t0=Uint8Array,e.next=14,this.subtle.decrypt({name:"AES-CBC",iv:n},i,t);case 14:return e.t1=e.sent,e.abrupt("return",new e.t0(e.t1));case 16:case"end":return e.stop()}}),e,this)}))),function(t,r,n){return e.apply(this,arguments)})}]);var e,t}(),rn=p(p({},Te),{},{STREAM_PARSED:"core.streamparsed",NO_AUDIO_TRACK:"core.noaudiotrack",SUBTITLE_SEGMENTS:"core.subtitlesegments",SUBTITLE_PLAYLIST:"core.subtitleplaylist",SEI_PAYLOAD_TIME:"core.seipayloadtime",APPEND_COST:"core.appendcost"}),nn=new ne("Transmuxer"),sn=function(){return S((function e(t,r,n){b(this,e),x(this,"_initSegmentId",""),this.hls=t,this._demuxer=r?new Qt:new zt,this._isMP4=r,n&&(this._remuxer=new nr(this._demuxer.videoTrack,this._demuxer.audioTrack))}),[{key:"transmux",value:function(e,t,r,n,i,s){var a=this._demuxer;try{this._isMP4?a.demux(e,t):a.demuxAndFix(Be(e,t),r,n,i)}catch(_){throw new $(H,J.HLS,_)}var o=a.videoTrack,u=a.audioTrack,c=a.metadataTrack,l="".concat(o.codec,"/").concat(o.width,"/").concat(o.height,"/").concat(u.codec,"/").concat(u.config);if(l!==this._initSegmentId&&(this._initSegmentId=l,s=!0),this._fireEvents(o,u,c,r||s),this.hls.emit(rn.DEMUXED_TRACK,{videoTrack:o,audioTrack:u}),!this._remuxer)return[o,u];try{var d=this._remuxer.remux(s),h=d.videoInitSegment,f=d.videoSegment,p=d.audioInitSegment,v=d.audioSegment,m=Be(h,f),y=Be(p,v);return[m?{codec:o.codec,data:m}:void 0,y?{codec:u.codec,data:y}:void 0]}catch(_){throw new $(K,J.FMP4,_)}}},{key:"_fireEvents",value:function(e,t,r,n){var i=this;nn.debug(e.samples,t.samples),n&&(e.exist()&&this.hls.emit(rn.METADATA_PARSED,{type:"video",track:e,meta:{codec:e.codec,timescale:e.timescale,width:e.width,height:e.height,sarRatio:e.sarRatio,baseDts:e.baseDts}}),t.exist()&&this.hls.emit(rn.METADATA_PARSED,{type:"audio",track:t,meta:{codec:t.codec,channelCount:t.channelCount,sampleRate:t.sampleRate,timescale:t.timescale,baseDts:t.baseDts}}),nn.debug("discontinuity",e,t)),e.warnings.forEach((function(e){var t;switch(e.type){case Ze:t=rn.LARGE_AV_FIRST_FRAME_GAP_DETECT;break;case $e:t=rn.LARGE_VIDEO_DTS_GAP_DETECT;break;case et:t=rn.MAX_DTS_DELTA_WITH_NEXT_SEGMENT_DETECT}t&&i.hls.emit(rn.STREAM_EXCEPTION,p(p({},e),{},{type:t})),nn.warn("video exception",e)})),t.warnings.forEach((function(e){var t;switch(e.type){case tt:t=rn.LARGE_AUDIO_DTS_GAP_DETECT;break;case rt:t=rn.AUDIO_GAP_DETECT;break;case nt:t=rn.AUDIO_OVERLAP_DETECT}t&&i.hls.emit(rn.STREAM_EXCEPTION,p(p({},e),{},{type:t})),nn.warn("audio exception",e)})),e.samples.forEach((function(e){e.keyframe&&i.hls.emit(rn.KEYFRAME,{pts:e.pts})})),r.seiSamples.forEach((function(e){i.hls.emit(rn.SEI,p(p({},e),{},{originPts:e.originPts/90,sei:{code:e.data.type,content:e.data.payload,dts:e.pts}}))}))}}])}(),an=new ne("BufferService"),on=function(){return S((function e(t){b(this,e),x(this,"_decryptor",new tn),x(this,"_transmuxer",null),x(this,"_mse",null),x(this,"_softVideo",null),x(this,"_sourceCreated",!1),x(this,"_needInitSegment",!0),x(this,"_directAppend",!1),this.hls=t,t.config.softDecode?this._softVideo=t.media:(this._mse=new de,t.config.url&&this._mse.bindMedia(t.media)),t.config.decryptor&&(this._decryptor.externalDecryptor=t.config.decryptor)}),[{key:"baseDts",get:function(){var e;return null===(e=this._transmuxer)||void 0===e||null===(e=e._demuxer)||void 0===e||null===(e=e._fixer)||void 0===e?void 0:e._baseDts}},{key:"nbSb",get:function(){var e;return null!==(e=this._mse)&&void 0!==e&&e._sourceBuffer?Object.keys(this._mse._sourceBuffer).length:0}},{key:"msIsOpend",get:function(){var e;return null===(e=this._mse)||void 0===e?void 0:e.isOpened}},{key:"updateDuration",value:(u=g(v().mark((function e(t){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(an.debug("update duration",t),!this._mse){e.next=9;break}if(this._mse.isOpened){e.next=5;break}return e.next=5,this._mse.open();case 5:return e.next=7,this._mse.updateDuration(t);case 7:e.next=10;break;case 9:this._softVideo&&(this._softVideo.duration=t);case 10:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"createSource",value:function(e,t,r,n){if(!this._sourceCreated){var i=e||t;if(i){if(zt.probe(i))this._transmuxer||(this._transmuxer=new sn(this.hls,!1,!this._softVideo));else{if(!qt.probe(i))throw new $(X,null,null,null,"unsupported stream");if(this._softVideo)this._transmuxer||(this._transmuxer=new sn(this.hls,!0));else{this._directAppend=!0;var s=!1;e&&!r&&qt.findBox(e,["moov","trak"]).forEach((function(e){var t=qt.findBox(e.data,["trak","mdia","minf","stbl","stsd"])[0];if(t){var i=qt.stsd(t).entries[0];if(i)if(i.hvcC)r=i.hvcC.codec||"hev1.1.6.L93.B0";else if(i.avcC)r=i.avcC.codec;else if(i.sampleRate||i.esds){var a;n=(null===(a=i.esds)||void 0===a?void 0:a.codec)||"mp4a.40.2",s=!0}}})),t&&!n&&qt.findBox(t,["moov","trak","mdia","minf","stbl","stsd"]).forEach((function(e){var t=qt.stsd(e).entries[0];t&&t.esds&&(n=t.esds.codec)})),e&&!r&&(r="avc1.42e01e"),t&&!n&&(n="mp4a.40.2"),s&&(r+=", ".concat(n),n=""),this._createMseSource(r,n)}}this._softVideo&&(this._sourceCreated=!0)}}}},{key:"appendBuffer",value:(o=g(v().mark((function e(t,r,n,i,s,a,o){var u,c,l,d,h,f,p,m,y;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null!=n&&n.length||null!=i&&i.length){e.next=2;break}return e.abrupt("return");case 2:if(!this._directAppend){e.next=7;break}return u=[],n&&u.push(this._mse.append(de.VIDEO,n)),i&&u.push(this._mse.append(de.AUDIO,i)),e.abrupt("return",Promise.all(u));case 7:if(c=this._needInitSegment||s,l=this._transmuxer.transmux(n,i,s,a,o,this._needInitSegment||s),d=O(l,2),h=d[0],f=d[1],i&&r&&(null==r||r.setTrackExist(!1,!0)),i&&t&&(null==t||t.setTrackExist(!0,!1)),r||null==t||t.setTrackExist(!!h,!!f),h&&!f&&this.hls.emit(rn.NO_AUDIO_TRACK),!this._softVideo){e.next=18;break}this._softVideo.appendBuffer(h,f),this._needInitSegment=!1,e.next=28;break;case 18:if(!this._mse){e.next=28;break}return(p=!this._sourceCreated)&&this._createMseSource(null==h?void 0:h.codec,null==f?void 0:f.codec),this._needInitSegment=!1,m=this._mse,y=[],c&&!p&&this._handleCodecChange(h,f),h&&y.push(m.append(de.VIDEO,h.data)),f&&y.push(m.append(de.AUDIO,f.data)),e.abrupt("return",Promise.all(y));case 28:case"end":return e.stop()}}),e,this)}))),function(e,t,r,n,i,s,a){return o.apply(this,arguments)})},{key:"removeBuffer",value:(a=g(v().mark((function e(){var t,r,n,i=this,s=arguments;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]?s[0]:0,r=s.length>1&&void 0!==s[1]?s[1]:1/0,n=this.hls.media,!(!this._mse||!n||t<0||r<t||t>=this._mse.duration)){e.next=5;break}return e.abrupt("return");case 5:return e.abrupt("return",this._mse.clearBuffer(t,r).then((function(){return i.hls.emit(Te.REMOVE_BUFFER,{start:t,end:r,removeEnd:r})})));case 6:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)})},{key:"evictBuffer",value:(s=g(v().mark((function e(t){var r,n,i;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this.hls.media,this._mse&&r&&t&&!(t<0)){e.next=3;break}return e.abrupt("return");case 3:if(n=r.currentTime,!((i=n-t)<=0)){e.next=7;break}return e.abrupt("return");case 7:if(!(M.start(M.get(r))+1>=i)){e.next=10;break}return e.abrupt("return");case 10:return e.abrupt("return",this.removeBuffer(0,i));case 11:case"end":return e.stop()}}),e,this)}))),function(e){return s.apply(this,arguments)})},{key:"clearAllBuffer",value:(i=g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=2;break}return e.abrupt("return",this._mse.clearAllBuffer());case 2:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)})},{key:"decryptBuffer",value:function(e,t){return this._decryptor.decrypt(e,t)}},{key:"reset",value:(n=g(v().mark((function e(){var t,r=arguments;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=r.length>0&&void 0!==r[0]&&r[0],!this._mse||t){e.next=8;break}return this._transmuxer=null,this._sourceCreated=!1,e.next=6,this._mse.unbindMedia();case 6:return e.next=8,this._mse.bindMedia(this.hls.media);case 8:this._needInitSegment=!0,this._directAppend=!1;case 10:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"endOfStream",value:(r=g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._mse){e.next=5;break}if(!this._sourceCreated){e.next=5;break}return e.next=4,this._mse.endOfStream();case 4:this.hls.emit(Te.BUFFEREOS);case 5:this._softVideo&&this._softVideo.endOfStream();case 6:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"setLiveSeekableRange",value:(t=g(v().mark((function e(t,r){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this._mse&&this._mse.setLiveSeekableRange(t,r);case 1:case"end":return e.stop()}}),e,this)}))),function(e,r){return t.apply(this,arguments)})},{key:"destroy",value:(e=g(v().mark((function e(){var t;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null===(t=this._decryptor)||void 0===t||t.destroy(),!this._mse){e.next=4;break}return e.next=4,this._mse.unbindMedia();case 4:this._decryptor=null,this._mse=null,this._softVideo=null;case 7:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})},{key:"_createMseSource",value:function(e,t){an.debug("create mse source, videoCodec=".concat(e,", audioCodec=").concat(t));var r=this._mse;r&&(e&&(r.createSource(de.VIDEO,"video/mp4;codecs=".concat(e)),this._sourceCreated=!0),t&&(r.createSource(de.AUDIO,"audio/mp4;codecs=".concat(t)),this._sourceCreated=!0),this.hls.emit(Te.SOURCEBUFFER_CREATED))}},{key:"_handleCodecChange",value:function(e,t){var r=this._mse;[{type:de.VIDEO,codecs:null==e?void 0:e.codec},{type:de.AUDIO,codecs:null==t?void 0:t.codec}].filter((function(e){return!!e.codecs})).forEach((function(e){var t=e.type,n=e.codecs,i=r.getSourceBuffer(t);if(i){var s=n.split(",")[0];new RegExp(s,"ig").test(i.mimeType)||r.changeType(t,"".concat(t,"/mp4;codecs=").concat(n))}}))}},{key:"seamlessSwitch",value:function(){this._needInitSegment=!0}}]);var e,t,r,n,i,s,a,o,u}();var un=S((function e(){b(this,e),x(this,"version",0),x(this,"streams",[]),x(this,"isMaster",!0)})),cn="AUDIO",ln="SUBTITLE",dn=S((function e(){b(this,e),x(this,"id",0),x(this,"url",""),x(this,"default",!1),x(this,"autoSelect",!1),x(this,"forced",!1),x(this,"group",""),x(this,"name",""),x(this,"lang",""),x(this,"segments",[]),x(this,"endSN",0)})),hn=function(e){function t(){var e;b(this,t);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return x(e=l(this,t,[].concat(n)),"mediaType",cn),x(e,"channels",0),e}return E(t,e),S(t)}(dn),fn=function(e){function t(){var e;b(this,t);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return x(e=l(this,t,[].concat(n)),"mediaType",ln),e}return E(t,e),S(t)}(dn),pn=S((function e(){b(this,e),x(this,"id",0),x(this,"bitrate",0),x(this,"width",0),x(this,"height",0),x(this,"name",""),x(this,"url",""),x(this,"audioCodec",""),x(this,"videoCodec",""),x(this,"textCodec",""),x(this,"audioGroup",""),x(this,"audioStreams",[]),x(this,"subtitleStreams",[]),x(this,"closedCaptionsStream",[])})),vn=S((function e(){b(this,e),x(this,"version",0),x(this,"url",""),x(this,"type",""),x(this,"startCC",0),x(this,"endCC",0),x(this,"startSN",0),x(this,"endSN",0),x(this,"totalDuration",0),x(this,"targetDuration",0),x(this,"live",!0),x(this,"segments",[])})),mn=function(){return S((function e(){b(this,e),x(this,"sn",0),x(this,"cc",0),x(this,"url",""),x(this,"title",""),x(this,"start",0),x(this,"duration",0),x(this,"key",null),x(this,"byteRange",null),x(this,"isInitSegment",!1),x(this,"initSegment",null),x(this,"isLast",!1),x(this,"hasAudio",!1),x(this,"hasVideo",!1)}),[{key:"end",get:function(){return this.start+this.duration}},{key:"setTrackExist",value:function(e,t){this.hasVideo=e,this.hasAudio=t}},{key:"setByteRange",value:function(e,t){this.byteRange=[0];var r=e.split("@");1===r.length&&t&&t.byteRange?(this.byteRange[0]=t.byteRange[1]||0,this.byteRange[0]&&(this.byteRange[0]+=1)):this.byteRange[0]=parseInt(r[1]),this.byteRange[1]=this.byteRange[0]+parseInt(r[0])-1}}])}(),yn=function(){function e(t){b(this,e),x(this,"method",""),x(this,"url",""),x(this,"iv",null),x(this,"keyFormat",""),x(this,"keyFormatVersions",""),t instanceof e&&(this.method=t.method,this.url=t.url,this.keyFormat=t.keyFormat,this.keyFormatVersions=t.keyFormatVersions,t.iv&&(this.iv=new Uint8Array(t.iv)))}return S(e,[{key:"clone",value:function(t){var r=new e(this);return null!=t&&r.setIVFromSN(t),r}},{key:"setIVFromSN",value:function(e){if(!this.iv&&"AES-128"===this.method&&"number"==typeof e&&this.url){this.iv=new Uint8Array(16);for(var t=12;t<16;t++)this.iv[t]=e>>8*(15-t)&255}}}])}(),_n=/^#(EXT[^:]*)(?::(.*))?$/,gn=/([^=]+)=(?:"([^"]*)"|([^",]*))(?:,|$)/g,bn=/^(?:[a-zA-Z0-9+\-.]+:)?\/\//,kn=/^((?:[a-zA-Z0-9+\-.]+:)?\/\/[^/?#]*)?([^?#]*\/)?/;function Sn(e){return e.split(/[\r\n]/).map((function(e){return e.trim()})).filter(Boolean)}function xn(e){var t=e.match(_n);if(t&&t[1])return[t[1].replace("EXT-X-",""),t[2]]}function En(e){for(var t={},r=gn.exec(e);r;)t[r[1]]=r[2]||r[3],r=gn.exec(e);return t}function wn(e,t){if(!t||!e||bn.test(e))return e;var r=kn.exec(t);return r?"/"===e[0]?r[1]+e:r[1]+r[2]+e:e}var Tn={audio:[/^mp4a/,/^vorbis$/,/^opus$/,/^flac$/,/^[ae]c-3$/],video:[/^avc/,/^hev/,/^hvc/,/^vp0?[89]/,/^av1$/],text:[/^vtt$/,/^wvtt/,/^stpp/]};function Dn(e,t){var r=Tn[e];if(r&&t&&t.length)for(var n=0;n<r.length;n++)for(var i=0;i<t.length;i++)if(r[n].test(t[i]))return t[i]}function An(e,t){for(var r,n=new un,i=0,s=[],a=[];r=e[i++];){var o=xn(r);if(o){var u=O(o,2),c=u[0],l=u[1];if("VERSION"===c)n.version=parseInt(l);else if("MEDIA"===c&&l){var d=En(l),h=void 0;switch(d.TYPE){case"AUDIO":h=new hn;break;case"SUBTITLES":h=new fn;break;default:h=new dn}h.url=wn(d.URI,t),h.default="YES"===d.DEFAULT,h.autoSelect="YES"===d.AUTOSELECT,h.group=d["GROUP-ID"],h.name=d.NAME,h.lang=d.LANGUAGE,d.CHANNELS&&(h.channels=Number(d.CHANNELS.split("/")[0]),Number.isNaN(h.channels)&&(h.channels=0)),"AUDIO"===d.TYPE&&d.URI&&s.push(h),"SUBTITLES"===d.TYPE&&a.push(h)}else if("STREAM-INF"===c&&l){var f=new pn,p=En(l);if(f.bitrate=parseInt(p["AVERAGE-BANDWIDTH"]||p.BANDWIDTH),f.name=p.NAME,f.url=wn(e[i++],t),p.RESOLUTION){var v=O(p.RESOLUTION.split("x"),2),m=v[0],y=v[1];f.width=parseInt(m),f.height=parseInt(y)}if(p.CODECS){var _=p.CODECS.split(/[ ,]+/).filter(Boolean);f.videoCodec=Dn("video",_),f.audioCodec=Dn("audio",_),f.textCodec=Dn("text",_)}f.audioGroup=p.AUDIO,f.subtitleGroup=p.SUBTITLES,n.streams.push(f)}}}return n.streams.forEach((function(e,t){e.id=t})),s.length&&(s.forEach((function(e,t){e.id=t})),n.streams.forEach((function(e){e.audioGroup&&(e.audioStreams=s.filter((function(t){return t.group===e.audioGroup})))}))),a.length&&(a.forEach((function(e,t){e.id=t})),n.streams.forEach((function(e){e.subtitleGroup&&(e.subtitleStreams=a.filter((function(t){return t.group===e.subtitleGroup})))}))),n}function Cn(e,t){var r=new vn;r.url=t;for(var n,i=new mn,s=null,a=null,o=0,u=0,c=0,l=0,d=!1;(n=e[l++])&&!d;)if("#"===n[0]){var h=xn(n);if(h){var f=O(h,2),p=f[0],v=f[1];switch(p){case"VERSION":r.version=parseInt(v);break;case"PLAYLIST-TYPE":r.type=null==v?void 0:v.toUpperCase();break;case"TARGETDURATION":r.targetDuration=parseFloat(v);break;case"ENDLIST":var m=r.segments[r.segments.length-1];m&&(m.isLast=!0),r.live=!1,d=!0;break;case"MEDIA-SEQUENCE":u=r.startSN=parseInt(v);break;case"DISCONTINUITY-SEQUENCE":c=r.startCC=parseInt(v);break;case"DISCONTINUITY":c++;break;case"BYTERANGE":i.setByteRange(v,r.segments[r.segments.length-1]);break;case"EXTINF":var y=O(v.split(","),2),_=y[0],g=y[1];i.start=o,i.duration=parseFloat(_),o+=i.duration,i.title=g;break;case"KEY":var b=En(v);if("NONE"===b.METHOD){a=null;break}if("AES-128"!==b.METHOD)throw new Error("encrypt ".concat(b.METHOD,"/").concat(b.KEYFORMAT," is not supported"));if((a=new yn).method=b.METHOD,a.url=/^blob:/.test(b.URI)?b.URI:wn(b.URI,t),a.keyFormat=b.KEYFORMAT||"identity",a.keyFormatVersions=b.KEYFORMATVERSIONS,b.IV){var k=b.IV.slice(2);k=(1&k.length?"0":"")+k,a.iv=new Uint8Array(k.length/2);for(var S=0,x=k.length/2;S<x;S++)a.iv[S]=parseInt(k.slice(2*S,2*S+2),16)}break;case"MAP":var E=En(v);i.url=wn(E.URI,t),E.BYTERANGE&&i.setByteRange(E.BYTERANGE),i.isInitSegment=!0,i.sn=0,a&&(i.key=a.clone(0)),s=i,i=new mn}}}else i.sn=u,i.cc=c,i.url=wn(n,t),a&&(i.key=a.clone(u)),s&&(i.initSegment=s),r.segments.push(i),i=new mn,u++;r.segments=r.segments.filter((function(e){return 0!==e.duration}));var w=r.segments[r.segments.length-1];return w&&(r.endSN=w.sn),r.totalDuration=o,r.endCC=c,r}var Ln=function(){function e(){b(this,e)}return S(e,null,[{key:"parse",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=arguments.length>1?arguments[1]:void 0;if(!t.includes("#EXTM3U"))throw new Error("Invalid m3u8 file");var n=Sn(t);return e.isMediaPlaylist(t)?Cn(n,r):An(n,r)}},{key:"isMediaPlaylist",value:function(e){return e.includes("#EXTINF:")||e.includes("#EXT-X-TARGETDURATION:")}}])}(),On=function(){return S((function e(t){var r=this;b(this,e),x(this,"_emitOnLoaded",(function(e,t){var n=e.response,i=e.options||{},s=i.firstByteTime,a=i.startTime,o=i.endTime,u=i.contentLength,c=o-a;r.hls.emit(Te.SPEED,{time:c,byteLength:u,url:t}),r.hls.emit(Te.LOAD_COMPLETE,{url:t,elapsed:c||0}),r.hls.emit(Te.TTFB,{url:t,responseUrl:n.url,elapsed:s-a}),r.hls.emit(Te.LOAD_RESPONSE_HEADERS,{headers:n.headers,url:t})})),x(this,"_onLoaderRetry",(function(e,t){r.hls.emit(rn.LOAD_RETRY,{error:$.network(e),retryTime:t})})),this.hls=t,this._timer=null;var n=this.hls.config,i=n.retryCount,s=n.retryDelay,a=n.loadTimeout,o=n.fetchOptions;this._loader=new Me(p(p({},o),{},{responseType:"text",retry:i,retryDelay:s,timeout:a,onRetryError:this._onLoaderRetry})),this._audioLoader=new Me(p(p({},o),{},{responseType:"text",retry:i,retryDelay:s,timeout:a,onRetryError:this._onLoaderRetry})),this._subtitleLoader=new Me(p(p({},o),{},{responseType:"text",retry:i,retryDelay:s,timeout:a,onRetryError:this._onLoaderRetry}))}),[{key:"load",value:(e=g(v().mark((function e(t,r,n){var i,s,a,o,u,c,l,d,h,f,p,m,y,_;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=[this._loader.load(t)],r&&i.push(this._audioLoader.load(r)),n&&i.push(this._subtitleLoader.load(n)),e.prev=3,e.next=6,Promise.all(i);case 6:if(u=e.sent,c=O(u,3),l=c[0],d=c[1],h=c[2],l){e.next=13;break}return e.abrupt("return",[]);case 13:this._emitOnLoaded(l,t),s=l.data,r?(a=null==d?void 0:d.data,o=null==h?void 0:h.data,a&&this._emitOnLoaded(d,r),o&&this._emitOnLoaded(h,n)):(o=null==h?void 0:h.data)&&this._emitOnLoaded(h,n),e.next=21;break;case 18:throw e.prev=18,e.t0=e.catch(3),$.network(e.t0);case 21:if(f=this.hls.config.onPreM3U8Parse,e.prev=22,f&&(s=f(s)||s,a&&(a=f(a,!0)||a),o&&(o=f(o,!0)||o)),p=Ln.parse(s,t),!1!==(null===(_=p)||void 0===_?void 0:_.live)||!p.segments||p.segments.length){e.next=27;break}throw new Error("empty segments list");case 27:a&&(m=Ln.parse(a,r)),o&&(y=Ln.parse(o,n)),e.next=34;break;case 31:throw e.prev=31,e.t1=e.catch(22),new $(N,J.HLS,e.t1);case 34:return p&&(p.isMaster?this.hls.emit(rn.HLS_MANIFEST_LOADED,{playlist:p}):this.hls.emit(rn.HLS_LEVEL_LOADED,{playlist:p})),e.abrupt("return",[p,m,y]);case 36:case"end":return e.stop()}}),e,this,[[3,18],[22,31]])}))),function(t,r,n){return e.apply(this,arguments)})},{key:"parseText",value:function(e,t){var r,n=this.hls.config.onPreM3U8Parse;try{var i;if(n&&(e=n(e)||e),!1===(null===(i=r=Ln.parse(e,t))||void 0===i?void 0:i.live)&&r.segments&&!r.segments.length)throw new Error("empty segments list")}catch(s){throw new $(N,J.HLS,s)}return r&&(r.isMaster?this.hls.emit(rn.HLS_MANIFEST_LOADED,{playlist:r}):this.hls.emit(rn.HLS_LEVEL_LOADED,{playlist:r})),[r]}},{key:"poll",value:function(e,t,r,n,i,s){var a=this;clearTimeout(this._timer),s=s||3e3;var o=this.hls.config.pollRetryCount,u=function(){var c=g(v().mark((function c(){var l;return v().wrap((function(c){for(;;)switch(c.prev=c.next){case 0:return clearTimeout(a._timer),c.prev=1,c.next=4,a.load(e,t,r);case 4:if((l=c.sent)[0]){c.next=7;break}return c.abrupt("return");case 7:o=a.hls.config.pollRetryCount,n(l[0],l[1],l[2]),c.next=15;break;case 11:c.prev=11,c.t0=c.catch(1),--o<=0&&i(c.t0);case 15:a._timer=setTimeout(u,s);case 16:case"end":return c.stop()}}),c,null,[[1,11]])})));return function(){return c.apply(this,arguments)}}();this._timer=setTimeout(u,s)}},{key:"stopPoll",value:function(){return clearTimeout(this._timer),this.cancel()}},{key:"cancel",value:function(){return Promise.all([this._loader.cancel(),this._audioLoader.cancel()])}}]);var e}();function jn(e,t,r){return t>r&&(r=t),Math.min(Math.max(e,t),r)}var Rn=function(){return S((function e(t,r,n){b(this,e),x(this,"live",void 0),x(this,"id",0),x(this,"bitrate",0),x(this,"width",0),x(this,"height",0),x(this,"name",""),x(this,"url",""),x(this,"audioCodec",""),x(this,"videoCodec",""),x(this,"textCodec",""),x(this,"startCC",0),x(this,"endCC",0),x(this,"startSN",0),x(this,"endSN",-1),x(this,"totalDuration",0),x(this,"targetDuration",0),x(this,"snDiff",null),x(this,"segments",[]),x(this,"audioStreams",[]),x(this,"subtitleStreams",[]),x(this,"closedCaptions",[]),x(this,"currentAudioStream",null),x(this,"currentSubtitleStream",null),this.update(t,r,n)}),[{key:"lastSegment",get:function(){return this.segments.length?this.segments[this.segments.length-1]:null}},{key:"segmentDuration",get:function(){var e;return this.targetDuration||(null===(e=this.segments[0])||void 0===e?void 0:e.duration)||0}},{key:"liveEdge",get:function(){return this.endTime}},{key:"endTime",get:function(){var e;return(null===(e=this.lastSegment)||void 0===e?void 0:e.end)||0}},{key:"currentSubtitleEndSn",get:function(){var e;return(null===(e=this.currentSubtitleStream)||void 0===e?void 0:e.endSN)||0}},{key:"clearOldSegment",value:function(e,t){return this.currentAudioStream&&this._clearSegments(e,t),this._clearSegments(e,t)}},{key:"getAudioSegment",value:function(e){if(e&&this.currentAudioStream){var t=e.sn-this.snDiff;return this.currentAudioStream.segments.find((function(e){return e.sn===t}))}}},{key:"update",value:function(e,t){this.url=e.url,Array.isArray(e.segments)?(null!==this.live&&void 0!==this.live||(this.live=e.live),this._updateSegments(e,this),this.startCC=e.startCC,this.endCC=e.endCC,this.startSN=e.startSN,this.endSN=e.endSN||-1,this.totalDuration=e.totalDuration,this.targetDuration=e.targetDuration,this.live=e.live,t&&this.currentAudioStream&&Array.isArray(t.segments)&&(this._updateSegments(t,this.currentAudioStream),(null===this.snDiff||void 0===this.snDiff)&&e.segments.length&&t.segments.length&&(this.snDiff=e.segments[0].sn-t.segments[0].sn))):(this.id=e.id,this.bitrate=e.bitrate,this.width=e.width,this.height=e.height,this.name=e.name,this.audioCodec=e.audioCodec,this.videoCodec=e.videoCodec,this.textCodec=e.textCodec,this.audioStreams=e.audioStreams,this.subtitleStreams=e.subtitleStreams,!this.currentAudioStream&&this.audioStreams.length&&(this.currentAudioStream=this.audioStreams.find((function(e){return e.default}))||this.audioStreams[0]),!this.currentSubtitleStream&&this.subtitleStreams.length&&(this.currentSubtitleStream=this.subtitleStreams.find((function(e){return e.default}))||this.subtitleStreams[0]))}},{key:"updateSubtitle",value:function(e){var t=this;if(e&&this.currentSubtitleStream&&Array.isArray(e.segments)){var r=this._updateSegments(e,this.currentSubtitleStream),n=this.currentSubtitleStream.segments;if(n.length>100&&(this.currentSubtitleStream.segments=n.slice(100)),r)return r.map((function(e){return{sn:e.sn,url:e.url,duration:e.duration,start:e.start,end:e.end,lang:t.currentSubtitleStream.lang}}))}}},{key:"switchSubtitle",value:function(e){var t=this.subtitleStreams.find((function(t){return t.lang===e})),r=this.currentSubtitleStream;t&&(this.currentSubtitleStream=t,r.segments=[])}},{key:"_clearSegments",value:function(e,t){for(var r=0,n=this.segments,i=0,s=n.length;i<s;i++)if(n[i].end>=e){r=i;break}return r>t&&(r=t),r&&(this.segments=this.segments.slice(r),this.currentAudioStream&&(this.currentAudioStream.segments=this.currentAudioStream.segments.slice(r))),t-r}},{key:"_updateSegments",value:function(e,t){var r=t.segments;if(this.live){var n=r[r.length-1],i=(null==n?void 0:n.sn)||-1;if(i<e.endSN&&e.segments.length){var s=e.segments.findIndex((function(e){return e.sn===i})),a=s<0?e.segments:e.segments.slice(s+1);if(r.length&&a.length){var o=n.end;a.forEach((function(e){e.start=o,o=e.end}));var u=(null==n?void 0:n.cc)||-1;u>a[0].cc&&a.forEach((function(e){return e.cc+=u}))}return t.endSN=e.endSN,t.segments=r.concat(a),a}}else t.segments=e.segments}}])}(),In=function(){return S((function e(t){b(this,e),x(this,"streams",[]),x(this,"currentStream",null),x(this,"dvrWindow",0),x(this,"_segmentPointer",-1),this.hls=t}),[{key:"lastSegment",get:function(){var e;return null===(e=this.currentStream)||void 0===e?void 0:e.lastSegment}},{key:"currentSegment",get:function(){var e;return null===(e=this.currentSegments)||void 0===e?void 0:e[this._segmentPointer]}},{key:"nextSegment",get:function(){var e;return null===(e=this.currentSegments)||void 0===e?void 0:e[this._segmentPointer+1]}},{key:"currentSegments",get:function(){var e;return null===(e=this.currentStream)||void 0===e?void 0:e.segments}},{key:"currentSubtitleEndSn",get:function(){var e;return null===(e=this.currentStream)||void 0===e?void 0:e.currentSubtitleEndSn}},{key:"liveEdge",get:function(){var e;return null===(e=this.currentStream)||void 0===e?void 0:e.liveEdge}},{key:"totalDuration",get:function(){var e;return(null===(e=this.currentStream)||void 0===e?void 0:e.totalDuration)||0}},{key:"seekRange",get:function(){var e=this.currentSegments;if(e&&e.length)return[e[0].start,e[e.length-1].end]}},{key:"isEmpty",get:function(){var e;return!(null!==(e=this.currentSegments)&&void 0!==e&&e.length)}},{key:"isLive",get:function(){var e;return null===(e=this.currentStream)||void 0===e?void 0:e.live}},{key:"hasSubtitle",get:function(){var e;return!(null===(e=this.currentStream)||void 0===e||!e.currentSubtitleStream)}},{key:"getAudioSegment",value:function(e){var t;return null===(t=this.currentStream)||void 0===t?void 0:t.getAudioSegment(e)}},{key:"moveSegmentPointer",value:function(e){var t;null==e&&(e=this._segmentPointer+1),this._segmentPointer=jn(e,-1,null===(t=this.currentSegments)||void 0===t?void 0:t.length)}},{key:"reset",value:function(){this.streams=[],this.currentStream=null,this.dvrWindow=0,this._segmentPointer=-1}},{key:"getSegmentByIndex",value:function(e){var t;return null===(t=this.currentSegments)||void 0===t?void 0:t[e]}},{key:"setNextSegmentByIndex",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this._segmentPointer=e-1}},{key:"findSegmentIndexByTime",value:function(e){var t=this.currentSegments;if(t){for(var r,n=0,i=t.length;n<i;n++)if(e>=(r=t[n]).start&&e<r.end)return n;var s=t[t.length-1];if(Math.abs(e-s.end)<.2)return t.length-1}}},{key:"upsertPlaylist",value:function(e,t,r){var n=this;if(e){if(e.isMaster)this.streams.length=e.streams.length,e.streams.filter((function(e){return e.url})).forEach((function(e,t){n.streams[t]?n.streams[t].update(e):n.streams[t]=new Rn(e)})),this.currentStream=this.streams[0];else if(Array.isArray(e.segments)){var i=this.currentStream;if(i){i.update(e,t,r);var s=i.updateSubtitle(r);s&&this.hls.emit(rn.SUBTITLE_SEGMENTS,{list:s})}else this.reset(),this.currentStream=this.streams[0]=new Rn(e,t,r)}this.currentStream&&this.hls.isLive&&!this.dvrWindow&&(this.dvrWindow=this.currentSegments.reduce((function(e,t){return e+=t.duration}),0))}}},{key:"switchSubtitle",value:function(e){var t;null===(t=this.currentStream)||void 0===t||t.switchSubtitle(e)}},{key:"clearOldSegment",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50,t=this.currentStream;if(this.dvrWindow&&t){var r=t.endTime-this.dvrWindow;if(!(r<=0)){var n=t.segments;n.length<=e||(this._segmentPointer=t.clearOldSegment(r,this._segmentPointer))}}}},{key:"checkSegmentTrackChange",value:function(e,t){var r=this.findSegmentIndexByTime(e),n=this.getSegmentByIndex(r);if(n&&(n.hasAudio||n.hasVideo)){if(2!==t&&n.hasAudio&&n.hasVideo)return n;if(!(n.end-e>.3)){var i=this.getSegmentByIndex(r+1);if(i&&(i.hasAudio||i.hasVideo))return i.hasAudio!==n.hasAudio||i.hasVideo!==n.hasVideo?i:void 0}}}}])}(),Pn=function(){return S((function e(t){var r=this;b(this,e),x(this,"error",null),x(this,"_emitOnLoaded",(function(e,t){var n=e.data,i=e.response,s=e.options||{},a=s.firstByteTime,o=s.startTime,u=s.endTime,c=s.contentLength,l=u-o;r._bandwidthService.addRecord(c||n.byteLength,l),r.hls.emit(Te.SPEED,{time:l,byteLength:c,url:t}),r.hls.emit(Te.LOAD_COMPLETE,{url:t,elapsed:l||0}),r.hls.emit(Te.TTFB,{url:t,responseUrl:i.url,elapsed:a-o}),r.hls.emit(Te.LOAD_RESPONSE_HEADERS,{headers:i.headers,url:t})})),x(this,"_onLoaderRetry",(function(e,t){r.hls.emit(Te.LOAD_RETRY,{error:$.network(e),retryTime:t})})),this.hls=t,this._bandwidthService=new Ve,this._mapCache={},this._keyCache={};var n=this.hls.config,i=n.retryCount,s=n.retryDelay,a=n.loadTimeout,o=n.fetchOptions;this._segmentLoader=new Me(p(p({},o),{},{responseType:"arraybuffer",retry:i,retryDelay:s,timeout:a,onRetryError:this._onLoaderRetry})),this._audioSegmentLoader=new Me(p(p({},o),{},{responseType:"arraybuffer",retry:i,retryDelay:s,timeout:a,onRetryError:this._onLoaderRetry})),this._keyLoader=new Me(p(p({},o),{},{responseType:"arraybuffer",retry:i,retryDelay:s,timeout:a,onRetryError:this._onLoaderRetry}))}),[{key:"speedInfo",value:function(){return{speed:this._bandwidthService.getLatestSpeed(),avgSpeed:this._bandwidthService.getAvgSpeed()}}},{key:"load",value:function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:r,i=[];return e&&(i[0]=this.loadVideoSegment(e,r)),t&&(i[1]=this.loadAudioSegment(t,n)),Promise.all(i)}},{key:"loadVideoSegment",value:function(e,t){return this._loadSegment(this._segmentLoader,e,t)}},{key:"loadAudioSegment",value:function(e,t){return this._loadSegment(this._audioSegmentLoader,e,t)}},{key:"_loadSegment",value:(t=g(v().mark((function e(t,r,n){var i,s,a,o,u,c,l,d,h,f,p,m,y,_,g,b=this;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return l=[],this.hls.emit(Te.LOAD_START,{url:r.url}),l[0]=t.load(r.url),n&&r.initSegment&&(h=r.initSegment.url,(s=this._mapCache[h])||(this.hls.emit(Te.LOAD_START,{url:h}),l[1]=t.load(h).then((function(e){e&&(Object.keys(b._mapCache)>30&&(b._mapCache={}),s=b._mapCache[h]=e.data,b._emitOnLoaded(e,h))}))),(f=null===(d=r.initSegment.key)||void 0===d?void 0:d.url)&&(c=r.initSegment.key.iv,(u=this._keyCache[f])||(this.hls.emit(Te.LOAD_START,{url:f}),l[2]=this._keyLoader.load(f).then((function(e){e&&(u=b._keyCache[f]=e.data,b._emitOnLoaded(e,f))}))))),(p=null===(i=r.key)||void 0===i?void 0:i.url)&&(o=r.key.iv,(a=this._keyCache[p])||(this.hls.emit(Te.LOAD_START,{url:p}),l[3]=this._keyLoader.load(p).then((function(e){e&&(a=b._keyCache[p]=e.data,b._emitOnLoaded(e,p))})))),e.next=8,Promise.all(l);case 8:if(m=e.sent,y=O(m,1),_=y[0]){e.next=13;break}return e.abrupt("return");case 13:return g=_.data,this._emitOnLoaded(_,r.url),e.abrupt("return",{data:g,map:s,key:a,mapKey:u,keyIv:o,mapKeyIv:c});case 16:case"end":return e.stop()}}),e,this)}))),function(e,r,n){return t.apply(this,arguments)})},{key:"reset",value:function(){this.error=null,this._mapCache={},this._keyCache={},this._bandwidthService.reset()}},{key:"cancel",value:(e=g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.all([this._keyLoader.cancel(),this._segmentLoader.cancel(),this._audioSegmentLoader.cancel()]);case 2:case"end":return e.stop()}}),e,this)}))),function(){return e.apply(this,arguments)})}]);var e,t}(),Bn=new ne("hls"),Un=function(e){function t(e){var r,n,i;return b(this,t),x(r=l(this,t),"version",t.version),x(r,"media",null),x(r,"config",null),x(r,"_manifestLoader",null),x(r,"_segmentLoader",null),x(r,"_playlist",null),x(r,"_bufferService",null),x(r,"_gapService",null),x(r,"_seiService",null),x(r,"_stats",null),x(r,"_prevSegSn",null),x(r,"_prevSegCc",null),x(r,"_tickTimer",null),x(r,"_tickInterval",500),x(r,"_segmentProcessing",!1),x(r,"_reloadOnPlay",!1),x(r,"_switchUrlOpts",null),x(r,"_loadSegment",g(v().mark((function e(){var t,n,i;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!r._segmentProcessing&&r.media){e.next=2;break}return e.abrupt("return");case 2:if(t=r._playlist.nextSegment){e.next=5;break}return e.abrupt("return");case 5:if(r.isLive){e.next=12;break}if(n=r.bufferInfo(),i=Math.abs(n.end-r.media.duration)<.1,!(n.remaining>=r.config.preloadTime||i)){e.next=11;break}return i&&r._bufferService.msIsOpend&&r._bufferService.endOfStream(),e.abrupt("return");case 11:!r._urlSwitching&&r._prevSegSn!==t.sn-1&&n.end&&Math.abs(t.start-n.end)>1&&r._playlist.setNextSegmentByIndex(r._playlist.findSegmentIndexByTime(n.end+.1));case 12:return e.abrupt("return",r._loadSegmentDirect());case 13:case"end":return e.stop()}}),e)})))),x(r,"_onPlay",g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(clearTimeout(r._disconnectTimer),!r._reloadOnPlay){e.next=6;break}r._reloadOnPlay=!1,r.replay(!0),e.next=9;break;case 6:return e.next=8,r._loadSegment();case 8:r._startTick();case 9:case"end":return e.stop()}}),e)})))),x(r,"_onPause",(function(){if(r.isLive){if(!r._reloadOnPlay){var e=r.config.disconnectTime;if(null==e&&(e=r._playlist.dvrWindow),!Number.isFinite(e))return;clearTimeout(r._disconnectTimer),r._disconnectTimer=setTimeout((function(){r._reloadOnPlay=!0,r._clear()}),e||0)}}else r._stopTick()})),x(r,"_onSeeking",g(v().mark((function e(){var t,n,i,s,a,o,u;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r.media){e.next=2;break}return e.abrupt("return");case 2:if(t=r.media.currentTime,!(n=r._playlist.seekRange)){e.next=9;break}if(!((i=jn(t,n[0],r.isLive?n[1]:r.media.duration))>=0&&Math.abs(t-i)>=.1)){e.next=9;break}return r.media.currentTime=i,e.abrupt("return");case 9:if(s=r._playlist.currentSegment,a=M.info(M.get(r.media),t,.1),!s){e.next=14;break}if(!(a.end&&Math.abs(a.end-s.end)<.2)){e.next=14;break}return e.abrupt("return");case 14:if(o=r._playlist.findSegmentIndexByTime(t),u=r._playlist.getSegmentByIndex(o),null!=o&&u&&(!r._segmentProcessing||u!==r._playlist.nextSegment)){e.next=18;break}return e.abrupt("return");case 18:return Bn.debug("seek to",t,u),r._playlist.setNextSegmentByIndex(o),r._stopTick(),e.next=23,r._segmentLoader.cancel();case 23:if(r._segmentProcessing=!1,a.end&&!r.isLive){e.next=27;break}return e.next=27,r._loadSegmentDirect(!0);case 27:r._startTick();case 28:case"end":return e.stop()}}),e)})))),x(r,"_onTimeupdate",(function(){if(r.media){var e,t=r.config;if(t.isLive&&t.maxLatency&&t.targetLatency&&r.media){var n=r._playlist.liveEdge;if(!n)return;var i=n-r.media.currentTime;i>=t.maxLatency&&(Bn.debug("latency jump, currentTime:".concat(r.media.currentTime,", liveEdge:").concat(n,",  latency=").concat(i)),r.media.currentTime=n-t.targetLatency)}if(t.seiInTime)null===(e=r._seiService)||void 0===e||e.throw(r.media.currentTime);r.config.allowedStreamTrackChange&&!r.config.softDecode&&r._checkStreamTrackChange(r.media.currentTime)}})),x(r,"_tick",(function(){if(r.media){r._startTick();var e=r.media,t=M.get(e),n=r._segmentLoader.error;if(n){(!e.readyState||r.bufferInfo(.5).remaining<1)&&(n.fatal=!0,r._emitError($.network(n)))}else M.end(t)<.1||!e.readyState||(!function(e){return e&&!e.paused&&!e.ended&&0!==e.playbackRate&&0!==e.readyState}(e)?e.readyState<2&&r._gapService&&r._gapService.do(e,r.config.maxJumpDistance,!e.currentTime||r.isLive):(r._loadSegment(),r._gapService&&r._gapService.do(e,r.config.maxJumpDistance,r.isLive)))}})),r.config=(i=(null==(n=e)?void 0:n.media)||document.createElement("video"),e=p(p({maxPlaylistSize:50,retryCount:3,retryDelay:1e3,pollRetryCount:2,loadTimeout:1e4,preloadTime:30,softDecode:!1,bufferBehind:10,maxJumpDistance:3,startTime:0,targetLatency:10,maxLatency:20,allowedStreamTrackChange:!0,seiInTime:!1,manifestList:[]},n),{},{media:i})),r.media=r.config.media,r._manifestLoader=new On(r),r._segmentLoader=new Pn(r),r._playlist=new In(r),r._bufferService=new on(r),e.seiInTime&&(r._seiService=new Fe(r)),e.softDecode||(r._gapService=new Ne),r._stats=new qe(r,9e4),r.media.addEventListener("play",r._onPlay),r.media.addEventListener("pause",r._onPause),r.media.addEventListener("seeking",r._onSeeking),r.media.addEventListener("timeupdate",r._onTimeupdate),r}return E(t,e),S(t,[{key:"isLive",get:function(){return this._playlist.isLive}},{key:"streams",get:function(){return this._playlist.streams}},{key:"currentStream",get:function(){return this._playlist.currentStream}},{key:"hasSubtitle",get:function(){return this._playlist.hasSubtitle}},{key:"baseDts",get:function(){var e;return null===(e=this._bufferService)||void 0===e?void 0:e.baseDts}},{key:"speedInfo",value:function(){return this._segmentLoader.speedInfo()}},{key:"bufferInfo",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.1;return M.info(M.get(this.media),null===(e=this.media)||void 0===e?void 0:e.currentTime,t)}},{key:"getStats",value:function(){return this._stats.getStats()}},{key:"playbackQuality",value:function(){return function(e){if(!e)return{};if("function"==typeof e.getVideoPlaybackQuality){var t=e.getVideoPlaybackQuality();return{droppedVideoFrames:t.droppedVideoFrames||t.corruptedVideoFrames,totalVideoFrames:t.totalVideoFrames,creationTime:t.creationTime}}return{droppedVideoFrames:e.webkitDroppedFrameCount,totalVideoFrames:e.webkitDecodedFrameCount,creationTime:performance.now()}}(this.media)}},{key:"load",value:(_=g(v().mark((function e(t){var r,n=arguments;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.length>1&&void 0!==n[1]&&n[1],t&&(this.config.url=t),t=this.config.url,e.next=5,this._reset(r);case 5:return e.next=7,this._loadData(t);case 7:this._startTick();case 8:case"end":return e.stop()}}),e,this)}))),function(e){return _.apply(this,arguments)})},{key:"_loadData",value:(m=g(v().mark((function e(t){var r,n,i,s,a,o,u,c,l,d;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:try{t&&(t=t.trim())}catch(h){}if(t){e.next=3;break}throw this._emitError(new $(X,J.OPTION,null,null,"m3u8 url is missing"));case 3:return e.next=5,this._loadM3U8(t);case 5:if(r=e.sent,n=this._playlist.currentStream,!this._urlSwitching){e.next=17;break}if(0===n.bitrate&&null!==(i=this._switchUrlOpts)&&void 0!==i&&i.bitrate&&(n.bitrate=null===(s=this._switchUrlOpts)||void 0===s?void 0:s.bitrate),a=this._getSeamlessSwitchPoint(),this.config.startTime=a,o=this._playlist.findSegmentIndexByTime(a),!(u=this._playlist.getSegmentByIndex(o+1))){e.next=17;break}return c=u.start,e.next=17,this._bufferService.removeBuffer(c);case 17:if(!r){e.next=29;break}if(!this.isLive){e.next=25;break}this._bufferService.setLiveSeekableRange(0,4294967295),Bn.log("totalDuration first time got",this._playlist.totalDuration),this.config.targetLatency<this._playlist.totalDuration&&(this.config.targetLatency=this._playlist.totalDuration,this.config.maxLatency=1.5*this.config.targetLatency),r.isMaster||this._pollM3U8(t),e.next=29;break;case 25:return e.next=27,this._bufferService.updateDuration(n.totalDuration);case 27:(l=this.config.startTime)&&(null!==(d=this._switchUrlOpts)&&void 0!==d&&d.seamless||(this.media.currentTime=l),this._playlist.setNextSegmentByIndex(this._playlist.findSegmentIndexByTime(l)||0));case 29:return e.next=31,this._loadSegment();case 31:case"end":return e.stop()}}),e,this)}))),function(e){return m.apply(this,arguments)})},{key:"replay",value:(f=g(v().mark((function e(t){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.config.startTime=0,e.next=3,this.load();case 3:return this._reloadOnPlay=!1,e.abrupt("return",this.media.play(!t));case 5:case"end":return e.stop()}}),e,this)}))),function(e){return f.apply(this,arguments)})},{key:"switchURL",value:(h=g(v().mark((function e(t){var r,n,i,s,a,o,u,c=arguments;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=c.length>1&&void 0!==c[1]?c[1]:{},n={seamless:!1,startTime:0,bitrate:0},e.t0=y(r),e.next="number"===e.t0?5:"boolean"===e.t0?7:"object"===e.t0?9:11;break;case 5:return r={startTime:r},e.abrupt("break",12);case 7:return r={seamless:r},e.abrupt("break",12);case 9:for(i in r)void 0!==r[i]&&null!==r[i]||delete r[i];return e.abrupt("break",12);case 11:throw"unsupported switchURL args: ".concat(r);case 12:if(r=Object.assign({},n,r),a=(s=r).seamless,o=s.startTime,this.config.url=t,this.config.startTime=o,this._switchUrlOpts=r,a){e.next=38;break}if(e.prev=18,!this.config.softDecode){e.next=23;break}e.t1=this.load(t),e.next=26;break;case 23:return e.next=25,this.load(t);case 25:e.t1=e.sent;case 26:u=e.t1,e.next=33;break;case 29:throw e.prev=29,e.t2=e.catch(18),this.emit(rn.SWITCH_URL_FAILED,e.t2),e.t2;case 33:return this._reloadOnPlay=!1,u&&this.emit(rn.SWITCH_URL_SUCCESS,{url:t}),e.abrupt("return",this.media.play(!0));case 38:return this._urlSwitching=!0,this._prevSegSn=null,this._prevSegCc=null,this._playlist.reset(),this._bufferService.seamlessSwitch(),e.next=45,this._clear();case 45:return e.next=47,this._loadData(t);case 47:this._startTick();case 48:this._switchUrlOpts=null;case 49:case"end":return e.stop()}}),e,this,[[18,29]])}))),function(e){return h.apply(this,arguments)})},{key:"switchStream",value:(d=g(v().mark((function e(t){var r,n,i,s,a,o=arguments;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=!(o.length>1&&void 0!==o[1])||o[1],n=this.currentStream,i=this.streams,n&&n.id!==t&&i&&!(i.length<2)){e.next=5;break}return e.abrupt("return");case 5:if(s=i.find((function(e){return e.id===t}))){e.next=8;break}return e.abrupt("return");case 8:return e.prev=8,e.next=11,this._clear();case 11:if(!r){e.next=14;break}return e.next=14,this._bufferService.clearAllBuffer();case 14:e.next=19;break;case 16:throw e.prev=16,e.t0=e.catch(8),this._emitError($.create(e.t0));case 19:if(n.currentAudioStream&&s.audioStreams.length>2&&(a=n.currentAudioStream.id,s.currentAudioStream=s.audioStreams.find((function(e){return e.id===a}))||s.currentAudioStream),this._playlist.currentStream=s,e.prev=21,!this.isLive&&s.segments.length){e.next=25;break}return e.next=25,this._refreshM3U8();case 25:return this._playlist.setNextSegmentByIndex(this._playlist.findSegmentIndexByTime(this.media.currentTime)||0),this._prevSegCc=null,e.next=29,this._loadSegmentDirect();case 29:e.next=35;break;case 31:throw e.prev=31,e.t1=e.catch(21),this._playlist.currentStream=n,e.t1;case 35:return this._startTick(),e.abrupt("return",s);case 37:case"end":return e.stop()}}),e,this,[[8,16],[21,31]])}))),function(e){return d.apply(this,arguments)})},{key:"switchAudioStream",value:(c=g(v().mark((function e(t){var r,n,i,s,a=arguments;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=!(a.length>1&&void 0!==a[1])||a[1],n=this.currentStream){e.next=4;break}return e.abrupt("return");case 4:if((i=n.currentAudioStream)&&i.id!==t&&!(n.audioStreams.length<2)){e.next=7;break}return e.abrupt("return");case 7:if(s=n.audioStreams.find((function(e){return e.id===t}))){e.next=10;break}return e.abrupt("return");case 10:return e.prev=10,e.next=13,this._clear();case 13:if(!r){e.next=16;break}return e.next=16,this._bufferService.clearAllBuffer();case 16:e.next=21;break;case 18:throw e.prev=18,e.t0=e.catch(10),this._emitError($.create(e.t0));case 21:if(n.currentAudioStream=s,e.prev=22,!this.isLive&&s.segments.length){e.next=26;break}return e.next=26,this._refreshM3U8();case 26:return this._playlist.setNextSegmentByIndex(this._playlist.findSegmentIndexByTime(this.media.currentTime)||0),this._prevSegCc=null,e.next=30,this._loadSegmentDirect();case 30:e.next=36;break;case 32:throw e.prev=32,e.t1=e.catch(22),n.currentAudioStream=i,e.t1;case 36:return this._startTick(),e.abrupt("return",s);case 38:case"end":return e.stop()}}),e,this,[[10,18],[22,32]])}))),function(e){return c.apply(this,arguments)})},{key:"switchSubtitleStream",value:(u=g(v().mark((function e(t){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._playlist.switchSubtitle(t),e.next=3,this._manifestLoader.stopPoll();case 3:return e.next=5,this._refreshM3U8();case 5:case"end":return e.stop()}}),e,this)}))),function(e){return u.apply(this,arguments)})},{key:"destroy",value:(o=g(v().mark((function e(){var t;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.media){e.next=2;break}return e.abrupt("return");case 2:return this.removeAllListeners(),this._playlist.reset(),this._segmentLoader.reset(),null===(t=this._seiService)||void 0===t||t.reset(),this.media.removeEventListener("play",this._onPlay),this.media.removeEventListener("pause",this._onPause),this.media.removeEventListener("seeking",this._onSeeking),this.media.removeEventListener("timeupdate",this._onTimeupdate),e.next=12,Promise.all([this._clear(),this._bufferService.destroy()]);case 12:this.media=null;case 13:case"end":return e.stop()}}),e,this)}))),function(){return o.apply(this,arguments)})},{key:"_loadM3U8",value:(a=g(v().mark((function e(t){var r,n,i,s,a,o;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,!(i=null===(n=this.config.manifestList)||void 0===n||null===(n=n.filter((function(e){return e.url===t}))[0])||void 0===n?void 0:n.manifest)){e.next=6;break}e.t0=this._manifestLoader.parseText(i,t),e.next=9;break;case 6:return e.next=8,this._manifestLoader.load(t);case 8:e.t0=e.sent;case 9:s=e.t0,a=O(s,1),r=a[0],e.next=17;break;case 14:throw e.prev=14,e.t1=e.catch(0),this._emitError($.create(e.t1));case 17:if(r){e.next=19;break}return e.abrupt("return");case 19:if(this._playlist.upsertPlaylist(r),!r.isMaster){e.next=24;break}return null!==(o=this._playlist.currentStream.subtitleStreams)&&void 0!==o&&o.length&&this.emit(rn.SUBTITLE_PLAYLIST,{list:this._playlist.currentStream.subtitleStreams}),e.next=24,this._refreshM3U8();case 24:return this.emit(rn.STREAM_PARSED),e.abrupt("return",r);case 26:case"end":return e.stop()}}),e,this,[[0,14]])}))),function(e){return a.apply(this,arguments)})},{key:"_refreshM3U8",value:function(){var e,t,r=this,n=this._playlist.currentStream;if(!n||!n.url)throw this._emitError($.create(null,null,new Error("m3u8 url is not defined")));var i=n.url,s=null===(e=n.currentAudioStream)||void 0===e?void 0:e.url,a=null===(t=n.currentSubtitleStream)||void 0===t?void 0:t.url;return this._manifestLoader.load(i,s,a).then((function(e){var t=O(e,3),n=t[0],o=t[1],u=t[2];n&&(r._playlist.upsertPlaylist(n,o,u),r.isLive&&r._pollM3U8(i,s,a))})).catch((function(e){throw r._emitError($.create(e))}))}},{key:"_pollM3U8",value:function(e,t,r){var n,i=this,s=this._playlist.isEmpty;this._manifestLoader.poll(e,t,r,(function(e,t,r){i._playlist.upsertPlaylist(e,t,r),i._playlist.clearOldSegment(),e&&s&&!i._playlist.isEmpty&&i._loadSegment(),s&&(s=i._playlist.isEmpty)}),(function(e){i._emitError($.create(e))}),1e3*((null===(n=this._playlist.lastSegment)||void 0===n?void 0:n.duration)||0))}},{key:"_loadSegmentDirect",value:(s=g(v().mark((function e(t){var r,n,i;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=this._playlist.nextSegment){e.next=3;break}return e.abrupt("return");case 3:return n=!1,i=null,e.prev=5,this._segmentProcessing=!0,e.next=9,this._reqAndBufferSegment(r,this._playlist.getAudioSegment(r));case 9:n=e.sent,e.next=15;break;case 12:e.prev=12,e.t0=e.catch(5),i=e.t0;case 15:return e.prev=15,this._segmentProcessing=!1,e.finish(15);case 18:if(!i){e.next=20;break}return e.abrupt("return",this._emitError($.create(i)));case 20:return n&&(this._urlSwitching&&(this._urlSwitching=!1,this.emit(rn.SWITCH_URL_SUCCESS,{url:this.config.url})),this._playlist.moveSegmentPointer(),r.isLast?this._end():t||this._loadSegment()),e.abrupt("return",n);case 22:case"end":return e.stop()}}),e,this,[[5,12,15,18]])}))),function(e){return s.apply(this,arguments)})},{key:"_reqAndBufferSegment",value:(i=g(v().mark((function e(t,r){var n,i,s,a,o,u,c,l,d;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return i=t?t.cc:r.cc,s=this._prevSegCc!==i,a=[],e.prev=3,e.next=6,this._segmentLoader.load(t,r,s);case 6:a=e.sent,e.next=14;break;case 9:throw e.prev=9,e.t0=e.catch(3),e.t0.fatal=!1,this._segmentLoader.error=e.t0,e.t0;case 14:if(a[0]){e.next=16;break}return e.abrupt("return");case 16:return e.next=18,(n=this._bufferService).decryptBuffer.apply(n,j(a));case 18:if(o=e.sent){e.next=21;break}return e.abrupt("return");case 21:return u=t?t.sn:r.sn,c=t?t.start:r.start,l=this._playlist.currentStream,this._bufferService.createSource(o[0],o[1],null==l?void 0:l.videoCodec,null==l?void 0:l.audioCodec),d=Date.now(),e.next=28,this._bufferService.appendBuffer(t,r,o[0],o[1],s,this._prevSegSn===u-1,c);case 28:return this.emit(rn.APPEND_COST,{elapsed:Date.now()-d,url:t.url}),e.next=31,this._bufferService.evictBuffer(this.config.bufferBehind);case 31:return this._prevSegCc=i,this._prevSegSn=u,e.abrupt("return",!0);case 34:case"end":return e.stop()}}),e,this,[[3,9]])}))),function(e,t){return i.apply(this,arguments)})},{key:"_checkStreamTrackChange",value:function(e){var t=this._playlist.checkSegmentTrackChange(e,this._bufferService.nbSb);t&&this.switchURL(this.config.url,t.start+.2)}},{key:"_clear",value:(n=g(v().mark((function e(){return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return clearTimeout(this._disconnectTimer),this._stopTick(),e.next=4,Promise.all([this._segmentLoader.cancel(),this._manifestLoader.stopPoll()]);case 4:this._segmentProcessing=!1;case 5:case"end":return e.stop()}}),e,this)}))),function(){return n.apply(this,arguments)})},{key:"_reset",value:(r=g(v().mark((function e(){var t,r,n=arguments;return v().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n.length>0&&void 0!==n[0]&&n[0],this._reloadOnPlay=!1,this._prevSegSn=null,this._prevSegCc=null,this._switchUrlOpts=null,this._playlist.reset(),this._segmentLoader.reset(),null===(t=this._seiService)||void 0===t||t.reset(),this._stats.reset(),e.next=11,this._clear();case 11:return e.abrupt("return",this._bufferService.reset(r));case 12:case"end":return e.stop()}}),e,this)}))),function(){return r.apply(this,arguments)})},{key:"_end",value:function(){this._clear(),this._bufferService.endOfStream(),(this.media.readyState<=2||this.media.buffered.length>1)&&this._startTick()}},{key:"_stopTick",value:function(){this._tickTimer&&clearTimeout(this._tickTimer),this._tickTimer=null}},{key:"_startTick",value:function(){this._stopTick(),this._tickTimer=setTimeout(this._tick,this._tickInterval)}},{key:"_emitError",value:function(e){var t,r,n,i,s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];!1===(null===(t=e.originError)||void 0===t?void 0:t.fatal)?Bn.warn(e):(Bn.table(e),Bn.error(e),Bn.error(null===(r=this.media)||void 0===r?void 0:r.error),null!==(n=this.media)&&void 0!==n&&n.readyState&&this.media.pause(),this._stopTick(),this._urlSwitching&&(this._urlSwitching=!1,this.emit(rn.SWITCH_URL_FAILED,e)),this.emit(rn.ERROR,e),s&&this._end(),null===(i=this._seiService)||void 0===i||i.reset());return e}},{key:"_getSeamlessSwitchPoint",value:function(){var e=this.media,t=e.currentTime;if(!e.paused){var r,n=this._playlist.findSegmentIndexByTime(e.currentTime),i=this._playlist.getSegmentByIndex(n),s=null===(r=this._stats)||void 0===r?void 0:r.getStats().downloadSpeed;if(s&&i)t+=i.duration*this._playlist.currentStream.bitrate/s+1;else t+=5}return t}}],[{key:"isSupported",value:function(e){return e&&"video"!==e&&"audio"!==e?"undefined"!=typeof WebAssembly:de.isSupported()}},{key:"enableLogger",value:function(){ne.enable(),ft.enable()}},{key:"disableLogger",value:function(){ne.disable(),ft.disable()}}]);var r,n,i,s,a,o,u,c,d,h,f,m,_}(Ae);x(Un,"version","3.0.8");try{localStorage.getItem("xgd")?Un.enableLogger():Un.disableLogger()}catch(Vn){}var Mn=function(){return S((function e(t,r){var n=this;b(this,e),x(this,"_opts",null),x(this,"_plugin",null),x(this,"_onLowDecode",(function(){var e,t,r=n._opts,i=r.media,s=r.innerDegrade;null===(e=n._plugin)||void 0===e||null===(e=e.player)||void 0===e||e.emit("lowdecode",i.degradeInfo),null===(t=n._plugin)||void 0===t||null===(t=t.player)||void 0===t||t.emit("core_event",p(p({},i.degradeInfo),{},{eventName:rn.LOWDECODE})),1===s&&n._degrade(i.src)})),x(this,"_degrade",(function(e){var t=n._plugin.player,r=t.video;if(!r||"MVideo"===r.TAG){var i=t.video.degradeVideo;t.video=i,r.degrade(e),e&&(t.config.url=e);var s=t.root.firstChild;"MVideo"===s.TAG&&t.root.replaceChild(i,s);var a=n._plugin.constructor.pluginName.toLowerCase();t.unRegisterPlugin(a),t.once("canplay",(function(){t.play()}))}})),x(this,"forceDegradeToVideo",(function(e){1===n._opts.innerDegrade&&n._degrade(e)})),this._opts=t,this._plugin=r,this._init()}),[{key:"_init",value:function(){var e=this._opts,t=e.media,r=e.preloadTime,n=e.innerDegrade,i=e.isLive;t&&(i||!t.setPlayMode?(n&&t.setAttribute("innerdegrade",n),r&&t.setAttribute("preloadtime",r),this._bindEvents()):t.setPlayMode("VOD"))}},{key:"_bindEvents",value:function(){this._opts.media.addEventListener("lowdecode",this._onLowDecode)}},{key:"destroy",value:function(){var e;null===(e=this._opts)||void 0===e||null===(e=e.media)||void 0===e||e.removeEventListener("lowdecode",this._onLowDecode),this._plugin=null}}])}();function Nn(e,t){var r={startTime:t.player.currentTime};switch(y(e)){case"boolean":r.seamless=e;break;case"object":Object.assign(r,e)}return r}var Fn=function(e){function t(){var e;b(this,t);for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return x(e=l(this,t,[].concat(n)),"hls",null),x(e,"pluginExtension",null),x(e,"getStats",(function(){var t;return null===(t=e.hls)||void 0===t?void 0:t.getStats()})),x(e,"_onSwitchSubtitle",(function(t){var r,n=t.lang;null===(r=e.hls)||void 0===r||r.switchSubtitleStream(n)})),x(e,"_onSwitchURL",(function(t,r){var n=e,i=n.player,s=n.hls;if(s){var a,o=Nn(r,e);i.config.url=t,s.switchURL(t,o).catch((function(e){})),!o.seamless&&null!==(a=e.player.config)&&void 0!==a&&null!==(a=a.hls)&&void 0!==a&&a.keepStatusAfterSwitch&&e._keepPauseStatus()}})),x(e,"_keepPauseStatus",(function(){e.player.paused&&e.player.once("canplay",(function(){e.player.pause()}))})),e}return E(t,e),S(t,[{key:"core",get:function(){return this.hls}},{key:"version",get:function(){var e;return null===(e=this.hls)||void 0===e?void 0:e.version}},{key:"softDecode",get:function(){var e,t=null===(e=this.player)||void 0===e||null===(e=e.config)||void 0===e?void 0:e.mediaType;return!!t&&"video"!==t&&"audio"!==t}},{key:"beforePlayerInit",value:function(){var e=this,t=this.player.config;if(t.url||t.__allowHlsEmptyUrl__){this.hls&&this.hls.destroy(),this.player.switchURL=this._onSwitchURL;var r,n=t.hls||{};if(n.innerDegrade=n.innerDegrade||t.innerDegrade,null!==n.disconnectTime&&void 0!==n.disconnectTime||(n.disconnectTime=0),this.hls=new Un(p({softDecode:this.softDecode,isLive:t.isLive,media:this.player.media||this.player.video,startTime:t.startTime,url:t.url},n)),this.softDecode||c.BasePlugin.defineGetterOrSetter(this.player,{url:{get:function(){var t;return null===(t=e.hls)||void 0===t||null===(t=t.media)||void 0===t?void 0:t.src},configurable:!0}}),this.softDecode&&(this.pluginExtension=new Mn(p({isLive:t.isLive,media:this.player.video},n),this),this.player.forceDegradeToVideo=function(){for(var t,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return null===(t=e.pluginExtension)||void 0===t?void 0:t.forceDegradeToVideo.apply(t,n)}),t.isLive)null===(r=this.player)||void 0===r||r.useHooks("replay",(function(){var t;return null===(t=e.hls)||void 0===t?void 0:t.replay()}));this.on(c.Events.SWITCH_SUBTITLE||"switch_subtitle",this._onSwitchSubtitle),this.on(c.Events.URL_CHANGE,this._onSwitchURL),this.on(c.Events.DESTROY,this.destroy.bind(this)),this._transError(),this._transCoreEvent(Te.TTFB),this._transCoreEvent(Te.LOAD_START),this._transCoreEvent(Te.LOAD_RESPONSE_HEADERS),this._transCoreEvent(Te.LOAD_COMPLETE),this._transCoreEvent(Te.LOAD_RETRY),this._transCoreEvent(Te.SOURCEBUFFER_CREATED),this._transCoreEvent(Te.REMOVE_BUFFER),this._transCoreEvent(Te.BUFFEREOS),this._transCoreEvent(Te.KEYFRAME),this._transCoreEvent(Te.METADATA_PARSED),this._transCoreEvent(Te.DEMUXED_TRACK),this._transCoreEvent(Te.SEI),this._transCoreEvent(Te.SEI_IN_TIME),this._transCoreEvent(Te.SPEED),this._transCoreEvent(Te.HLS_MANIFEST_LOADED),this._transCoreEvent(Te.HLS_LEVEL_LOADED),this._transCoreEvent(Te.STREAM_EXCEPTION),this._transCoreEvent(Te.SWITCH_URL_SUCCESS),this._transCoreEvent(Te.SWITCH_URL_FAILED),this._transCoreEvent(rn.NO_AUDIO_TRACK),this._transCoreEvent(rn.STREAM_PARSED),this._transCoreEvent(rn.SUBTITLE_SEGMENTS),this._transCoreEvent(rn.SUBTITLE_PLAYLIST),this._transCoreEvent(rn.APPEND_COST),t.url&&this.hls.load(t.url,!0).catch((function(e){}))}}},{key:"destroy",value:function(){var e;this.hls&&(this.hls.destroy(),this.hls=null),null===(e=this.pluginExtension)||void 0===e||e.destroy(),this.pluginExtension=null}},{key:"_transError",value:function(){var e=this;this.hls.on(rn.ERROR,(function(t){e.player&&e.player.emit(c.Events.ERROR,new c.Errors(e.player,t))}))}},{key:"_transCoreEvent",value:function(e){var t=this;this.hls.on(e,(function(r){t.player&&(t.player.emit("core_event",p(p({},r),{},{eventName:e})),e===Te.SEI_IN_TIME&&t.hls.hasSubtitle&&t._emitSeiPaylodTime(r))}))}},{key:"_emitSeiPaylodTime",value:function(e){try{var t=JSON.parse(Array.from(e.data.payload).map((function(e){return String.fromCharCode(e)})).join("").slice(0,-1));if(!t.rtmp_dts)return;this.player.emit("core_event",{eventName:rn.SEI_PAYLOAD_TIME,time:t.rtmp_dts})}catch(r){}}}],[{key:"pluginName",get:function(){return"hls"}},{key:"isSupported",value:function(e,t){return Un.isSupported(e,t)}}])}(c.BasePlugin);return x(Fn,"Hls",Un),x(Fn,"EVENT",rn),Fn}));
//# sourceMappingURL=index.min.js.map
