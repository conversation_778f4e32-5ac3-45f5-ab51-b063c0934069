import type { ChannelInstance, VideoDetect, VideoTreeNode } from './abstract';

/** 危化企业视频方案（海康） */
export class DEVideoDetect implements VideoDetect {
  constructor(private readonly baseURL: string) {}

  private _request(params: RequsetParams) {
    return requset({ ...params, baseURL: this.baseURL } as any);
  }

  /**
   * 递归获取所有列表   （每次请求1000项 如果存在下一页则继续请求）
   * @returns
   */
  private async _getList() {
    const api = async (pageNo: number) => {
      const { data } = await this._request({
        url: '/api/irds/v2/resource/resourcesByParams',
        method: 'post',
        data: {
          resourceType: 'camera',
          pageSize: 1000,
          pageNo,
          isSubRegion: true,
        },
      });
      return data;
    };
    const recursion = async (pageNo = 1) => {
      const data = (await api(pageNo))?.data ?? {};
      const { list = [], total = 0, pageSize = 1000 } = data;
      if (total > pageNo * pageSize) {
        const _list = await recursion(pageNo + 1);
        list.push(_list);
      }
      return list;
    };
    return await recursion();
  }

  /** 摄像头树 */
  private async _cameraList() {
    const list = await this._getList();
    const treeMap: Record<string, any> = {};
    // 组合成tree
    list.forEach((item) => {
      const regionPathNames = item.regionPathName.split('/');
      regionPathNames.shift();
      const regionPaths = item.regionPath.substring(1, item.regionPath.length - 1).split('@');
      regionPaths.shift();
      regionPaths.forEach((path, index) => {
        treeMap[path] ??= {
          regionIndexCode: path,
          name: regionPathNames[index],
          parentRegionIndexCode: index == 0 ? 0 : regionPaths[index - 1],
          children: [],
        };
      });
      const parentRegionIndexCode = regionPaths[regionPaths.length - 1];
      treeMap[parentRegionIndexCode].children.push(item);
    });
    Object.keys(treeMap).forEach((key) => {
      const item = treeMap[key];
      if (item.parentRegionIndexCode) {
        const parent = treeMap[item.parentRegionIndexCode]?.children;
        parent.push(item);
      }
    });
    return Object.values(treeMap).filter(e => !e.parentRegionIndexCode);
  }

  /** 获取摄像头总数量 */
  async getCount(): Promise<number> {
    const { data } = await this._request({
      url: '/api/resource/v1/camera/advance/cameraList',
      method: 'post',
      data: { pageNo: 1, pageSize: 1 },
    });
    return data.total;
  }

  /**
   * 获取树列表
   * @type {params} 不传则返回根列表  传入上一次`getNodes` 的列表项则可获取下级
   */
  async getNodes(params?: VideoTreeNode): Promise<VideoTreeNode[]> {
    const children = !params ? await this._cameraList() : params?.ext?.children;
    return children?.map((item) => {
      const onLine = item.status !== 0 || item?.children?.find(e => e.status !== 0);
      return {
        code: item.externalCode || item.regionIndexCode,
        name: item.name || item.channelName,
        isLeaf: !item.children?.length,
        onLine: !!onLine,
        isChannel: !!item.indexCode,
        ext: item,
      };
    });
  }

  getInstance(data: VideoTreeNode) {
    return new DEChannelInstance(data, this.baseURL);
  }
}

/** 摄像头实例 */
export class DEChannelInstance implements ChannelInstance {
  constructor(public data: VideoTreeNode, private baseURL: string) {}
  private _request(params: RequsetParams) {
    return requset({ ...params, baseURL: this.baseURL } as any);
  }

  /** 获取实时流RTSP地址 */
  async realTimeRTSP(): Promise<string> {
    const { data } = await this._request({
      url: '/api/video/v1/cameras/previewURLs',
      method: 'POST',
      data: {
        cameraIndexCode: this.data.ext.indexCode,
        protocol: 'rtsp',
        expand: 'streamform=rtp&transcode=1&videotype=h264',
      },
    });
    return data.data?.url;
  }
}

interface RequsetParams {
  url: string;
  method: string;
  data?: any;
  params?: any;
}

const fetch = axios.create().request;

function requset({ baseURL, url, method, data, params }) {
  return fetch({
    url: baseURL,
    method,
    headers: {
      'Ph-Url': url,
    },
    data: data || {},
    params: params || {},
  });
}
