<!-- Cartesian3Attribute -->
<script lang="ts" setup>
import type { Cartesian3SerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'Cartesian3Attribute' });

const props = withDefaults(
  defineProps<{
    modelValue?: Cartesian3SerializateJSON;
    label?: string;
    xLabel?: string;
    yLabel?: string;
    zLabel?: string;
  }>(),
  {
    xLabel: 'X',
    yLabel: 'Y',
    zLabel: 'Z',
  },
);

const emit = defineEmits<{
  (event: 'update:modelValue', value?: Cartesian3SerializateJSON): void;
}>();

const model = ref<Cartesian3SerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div>
    <el-form-item :label="label" />
    <NumberAttribute v-model="model.x" :label="xLabel" />
    <NumberAttribute v-model="model.y" :label="yLabel" />
    <NumberAttribute v-model="model.z" :label="zLabel" />
  </div>
</template>
