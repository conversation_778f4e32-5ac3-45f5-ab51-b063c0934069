import type { Cartesian3SerializateJSON } from './cartesian3';

import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type {
  ArcTypeSerializateJSON,
  ClassificationTypeSerializateJSON,
  ShadowModeSerializateJSON,
} from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { Cartesian3Serializate } from './cartesian3';

import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PolylineGraphicsSerializateJSON {
  show?: boolean;
  positions?: Cartesian3SerializateJSON[];
  width?: number;
  granularity?: number;
  material?: MaterialPropertySerializateJSON;
  depthFailMaterial?: MaterialPropertySerializateJSON;
  arcType?: ArcTypeSerializateJSON;
  clampToGround?: boolean;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  classificationType?: ClassificationTypeSerializateJSON;
  zIndex?: number;
}

export type PolylineGraphicsKey = keyof PolylineGraphicsSerializateJSON;

export class PolylineGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.PolylineGraphics,
    omit?: PolylineGraphicsKey[],
    time?: Cesium.JulianDate,
  ): PolylineGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map((item: Cesium.Cartesian3) =>
        Cartesian3Serializate.toJSON(item),
      ),
      width: getValue('width'),
      granularity: getValue('granularity'),
      material: MaterialPropertySerializate.toJSON(data.material),
      depthFailMaterial: MaterialPropertySerializate.toJSON(data.depthFailMaterial),
      arcType: EnumSerializate.toJSON(Cesium.ArcType, getValue('arcType')) ?? 'GEODESIC',
      clampToGround: getValue('clampToGround'),
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType:
        EnumSerializate.toJSON(Cesium.ClassificationType, getValue('classificationType')) ?? 'BOTH',
      zIndex: getValue('zIndex'),
    };
  }

  static fromJSON(
    json?: PolylineGraphicsSerializateJSON,
    omit?: PolylineGraphicsKey[],
  ): Cesium.PolylineGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.PolylineGraphics({
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map(item => Cartesian3Serializate.fromJSON(item)!),
      width: getValue('width'),
      granularity: getValue('granularity'),
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      depthFailMaterial: MaterialPropertySerializate.fromJSON(getValue('depthFailMaterial')),
      arcType: EnumSerializate.fromJSON(Cesium.ArcType, getValue('arcType')),
      clampToGround: getValue('clampToGround'),
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType: EnumSerializate.fromJSON(
        Cesium.ClassificationType,
        getValue('classificationType'),
      ),
      zIndex: getValue('zIndex'),
    });
  }
}
