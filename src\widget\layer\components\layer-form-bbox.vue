<script lang="ts" setup>
import { useAsync } from '@/hooks/use-async';
import { toPublicPath } from '@/utils/resolve-path';
import { XMLParser } from 'fast-xml-parser';
import qs from 'qs';

export interface LayerFormBboxProps {
  url?: string;
  layer?: string;
  modelValue?: (number | undefined)[];
}

export interface LayerFormBboxEmits {
  (event: 'update:modelValue', data?: (number | undefined)[]): void;
}

defineOptions({ name: 'LayerFormBbox' });
const props = defineProps<LayerFormBboxProps>();
const emit = defineEmits<LayerFormBboxEmits>();

const model = useVModel(props, 'modelValue', emit, {
  defaultValue: [],
  clone: true,
  deep: true,
  passive: true,
});

const n = computed({
  get: () => model.value![3],
  set: (val) => {
    const res = [...model.value!];
    res[3] = val;
    emit('update:modelValue', res);
  },
});

const w = computed({
  get: () => model.value![0],
  set: (val) => {
    const res = [...model.value!];
    res[0] = val;
    emit('update:modelValue', res);
  },
});

const s = computed({
  get: () => model.value![1],
  set: (val) => {
    const res = [...model.value!];
    res[1] = val;
    emit('update:modelValue', res);
  },
});

const e = computed({
  get: () => model.value![2],
  set: (val) => {
    const res = [...model.value!];
    res[2] = val;
    emit('update:modelValue', res);
  },
});

const errorText = ref('');

const { execute: getBbox, isLoading: getBboxLoading } = useAsync(async () => {
  errorText.value = '';

  if (!props.url) {
    errorText.value = '请设置URL地址';

    return;
  }
  let url = props.url;
  if (!url.startsWith('http')) {
    url = window.location.origin + toPublicPath(props.url);
  }

  const urlParse = new URL(url);

  if (!urlParse) {
    errorText.value = '无效的URL地址';
    return;
  }

  try {
    const qsParse = qs.parse(urlParse.search?.replace(/^\?/, ''));
    Object.keys(qsParse).forEach((key) => {
      qsParse[key.toLowerCase()] = qsParse[key];
    });
    const layer = qsParse.layer || props.layer;
    const { data } = await axios.request({
      url: urlParse.origin + urlParse.pathname,
      params: {
        layer,
        service: qsParse.service,
        version: qsParse.version,
        request: 'GetCapabilities',
      },
    });

    const xml = new XMLParser().parse(data);

    let xmlLayer: any;

    // wmts
    if (Array.isArray(xml?.Capabilities?.Contents?.Layer)) {
      xmlLayer = xml?.Capabilities?.Contents?.Layer?.find((item: any) => item['ows:Identifier'] === layer);
    }
    else {
      xmlLayer = xml?.Capabilities?.Contents?.Layer;
    }

    const boundingBox = xmlLayer?.['ows:WGS84BoundingBox'];
    const [w, s] = boundingBox?.['ows:LowerCorner']?.split(' ') ?? [];
    const [e, n] = boundingBox?.['ows:UpperCorner']?.split(' ') ?? [];
    if (!w || !s || !e || !n) {
      errorText.value = '获取不到有效的BBOX范围';
    }
    else if (+w < -180 || +w > 180) {
      errorText.value = '获取不到有效的BBOX范围';
    }
    else {
      emit('update:modelValue', [+w, +s, +e, +n]);
    }
  }
  catch (error) {
    console.error(error);
    errorText.value = '获取失败,自动获取仅支持中台或geoserver服务';
  }
});
</script>

<template>
  <div flex="~ col">
    <header-title2 my="16px" content="瓦片范围设置">
      <template #extra>
        <el-button size="small" :loading="getBboxLoading" @click="getBbox()">
          自动获取
        </el-button>
        <el-button size="small" @click="emit('update:modelValue', [])">
          一键清除
        </el-button>
      </template>
    </header-title2>
    <el-text type="danger" py="10px">
      {{ errorText }}
    </el-text>
    <el-form>
      <el-form-item class="mx-130px! px-0!" label-width="0">
        <div text="center 12px #fff/50%" w="100%" lh="20px">
          N
        </div>
        <el-input-number
          v-model="n"
          text="center"
          placeholder="请输入"
          :controls="false"
          :precision="5"
          :min="s ?? -90"
          :max="90"
        />
      </el-form-item>
      <div flex="~ items-center" class="horizontal">
        <el-form-item px="6px!" label-width="0">
          <div flex="~ items-center">
            <span text="center 12px #fff/50%" px="10px">W</span>
            <el-input-number
              v-model="w"
              text="center"
              placeholder="请输入"
              :controls="false"
              :precision="5"
              :min="-180"
              :max="e ?? 180"
            />
          </div>
        </el-form-item>
        <el-form-item px="6px!" label-width="0">
          <div flex="~ items-center">
            <el-input-number
              v-model="e"
              text="center"
              placeholder="请输入"
              :controls="false"
              :precision="5"
              :min="e ?? -180"
              :max=" 180"
            />
            <span text="center 12px #fff/50%" px="10px">E</span>
          </div>
        </el-form-item>
      </div>
      <el-form-item class="mx-130px! px-0!" label-width="0">
        <el-input-number
          v-model="s"
          text="center"
          placeholder="请输入"
          :controls="false"
          :precision="5"
          :min="-90"
          :max="n ?? 90"
        />
        <div text="center 12px #fff/50%" w="100%" lh="20px">
          S
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
