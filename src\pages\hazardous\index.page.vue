<script lang="ts" setup>
import LeftFunctionalGroup from './components/left-functional-group.vue';
import Right from './components/right.vue';
import { useCompanyActiveProvide } from './hooks';

defineOptions({ name: 'HazardousIndexPage' });
definePage({
  meta: {
    title: '三维融合',
  },
});

useCompanyActiveProvide();
</script>

<template>
  <layout-scaffold>
    <template #left>
      <LeftFunctionalGroup />
    </template>
    <template #right>
      <Right />
    </template>
  </layout-scaffold>
</template>
