<!-- 卷帘分析 -->
<script lang="ts" setup>
import { layerInfoGetLayerInfoUsingGet } from '@/genapi/cimapi';
import { createLayer } from '@/widget/layer/utils/create-layer';
import { legacy } from '@/widget/layer/utils/legacy';

import { isPromise } from '@x3d/all';
import { useCzImageryLayer, useCzViewer } from '@x3d/vue-hooks';
import * as Cesium from 'cesium';
import { ImageryLayer } from 'cesium';

defineOptions({ name: 'SplitLayer' });
const emits = defineEmits<{ (event: 'close'): void }>();
const viewer = useCzViewer();
const splitLineRef = ref<HTMLElement>();
const treeSelectRef = templateRef('treeSelectRef');
const splitPosition = ref<number>(50);

const { state: treeList, isLoading } = useAsyncState(
  async () => {
    const { data = [] } = await layerInfoGetLayerInfoUsingGet({});
    return legacy(data);
  },
  [],
  {
    immediate: true,
    resetOnExecute: false,
  },
);

const { pressed } = useMousePressed({ target: splitLineRef });
const { x } = useMouse();

const leftId = ref('');

const leftLayer = shallowRef<ImageryLayer>();

watch(leftId, async () => {
  const data = treeSelectRef.value?.getNode(leftId.value).data;
  const options = createLayer(data);
  const layer = isPromise(options?.value) ? await options?.value : options?.value;
  leftLayer.value = layer instanceof ImageryLayer ? layer : undefined;
});

useCzImageryLayer(leftLayer);

const rightId = ref('');

const rightLayer = shallowRef<ImageryLayer>();

watch(rightId, async () => {
  const data = treeSelectRef.value?.getNode(rightId.value).data;
  const options = createLayer(data);
  const layer = isPromise(options?.value) ? await options?.value : options?.value;
  rightLayer.value = layer instanceof ImageryLayer ? layer : undefined;
});

useCzImageryLayer(rightLayer);

watchEffect(() => {
  if (leftLayer.value) {
    leftLayer.value.splitDirection = Cesium.SplitDirection.LEFT;
  }

  if (rightLayer.value) {
    rightLayer.value.splitDirection = Cesium.SplitDirection.RIGHT;
  }
});
watchEffect(() => {
  viewer.value.scene.splitPosition = splitPosition.value / 100;
});

watch(splitPosition, (val) => {
  viewer.value.scene.splitPosition = val / 100;
});

watch(x, () => {
  if (pressed.value) {
    splitPosition.value = (x.value / document.body.clientWidth) * 100;
  }
});
function handleClear() {
  leftId.value = '';
  rightId.value = '';
}
</script>

<template>
  <drag-card

    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="卷帘对比-参数设置"
    class="h-288px w-400px"
    @close="emits('close')"
  >
    <el-form v-loading="isLoading" mt="24px" pr="30px">
      <el-form-item label="左边图层" :label-width="$vh(80)">
        <ElTreeSelect
          ref="treeSelectRef"
          v-model="leftId"
          :data="treeList"
          node-key="id"
          :render-after-expand="false"
          :props="{ label: 'layerName' }"
        />
      </el-form-item>
      <el-form-item label="右边图层" :label-width="$vh(80)">
        <ElTreeSelect
          v-model="rightId"
          :data="treeList"
          node-key="id"
          :render-after-expand="false"
          :props="{ label: 'layerName' }"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span />
      <el-button class="plain-#FF6363 px-26px!" @click="handleClear">
        清除
      </el-button>
    </template>
  </drag-card>
  <teleport to="#cesium-container">
    <div
      ref="splitLineRef"
      class="split-layer-line"
      :style="{ left: `${Cesium.Math.clamp(splitPosition, 0, 100)}vw` }"
    />
  </teleport>
</template>

<style lang="scss" scope>
.skyline-analysis {
  display: inline-block;
}
</style>

<style lang="scss">
.split-layer-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 10px;
  pointer-events: all;
  cursor: col-resize;
  user-select: none;
  background: #f0f2f5;
  transform: translateX(-50%);
}
</style>
