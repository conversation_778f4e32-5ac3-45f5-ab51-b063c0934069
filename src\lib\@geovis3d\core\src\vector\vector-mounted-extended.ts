import * as Cesium from 'cesium';

declare module 'cesium' {
  export interface Entity {
    /**
     * @internal
     */
    _isMounted: boolean;
    get isMounted(): boolean;
  }

  export interface DataSource {
    /**
     * @internal
     */
    _isMounted: boolean;
    get isMounted(): boolean;
  }

  export interface CustomDataSource {
    /**
     * @internal
     */
    _isMounted: boolean;
    get isMounted(): boolean;
  }

  export interface CzmlDataSource {
    /**
     * @internal
     */
    _isMounted: boolean;
    get isMounted(): boolean;
  }

  export interface GeoJsonDataSource {
    /**
     * @internal
     */
    _isMounted: boolean;
    get isMounted(): boolean;
  }

  export interface GpxDataSource {
    /**
     * @internal
     */
    _isMounted: boolean;
    get isMounted(): boolean;
  }

  export interface KmlDataSource {
    /**
     * @internal
     */
    _isMounted: boolean;
    get isMounted(): boolean;
  }
}

function _defineIsMounted(obj: any) {
  if (!Object.hasOwn(obj.prototype, 'isMounted')) {
    Object.defineProperty(obj.prototype, 'isMounted', {
      get() {
        return !!this._isMounted;
      },
    });
  }
}

export function defineIsMounted() {
  [
    Cesium.GeoJsonDataSource,
    Cesium.GpxDataSource,
    Cesium.KmlDataSource,
    Cesium.CustomDataSource,
    Cesium.CzmlDataSource,
    Cesium.Entity,
  ].forEach(_defineIsMounted);
}

function entitiesChangedListener(_: any, added: Cesium.Entity[], removed: Cesium.Entity[]) {
  added.forEach(entity => setMounted(entity, true));
  removed.forEach(entity => setMounted(entity, false));
}

function dataSourceMountHelper(dataSource: Cesium.DataSource, mounted: boolean) {
  const { values, collectionChanged } = dataSource.entities;
  // entities 会在forEach的时候产生数组长度上的变化 在此浅拷贝
  const entityList = [...values];
  entityList.forEach(entity => setMounted(entity, mounted));
  if (mounted) {
    collectionChanged.addEventListener(entitiesChangedListener);
  }
  else {
    collectionChanged.removeEventListener(entitiesChangedListener);
  }
}

function setMounted(data: Cesium.Entity | Cesium.DataSource, mounted: boolean) {
  mounted = !!mounted;
  if (mounted !== !!data._isMounted) {
    data._isMounted = mounted;
    const event = data instanceof Cesium.Entity ? data.definitionChanged : data.changedEvent;
    event.raiseEvent(data, 'isMounted', mounted, !mounted);
    if (!(data instanceof Cesium.Entity)) {
      dataSourceMountHelper(data, mounted);
    }
  }
}

/**
 *
 * @param viewer
 */
export function vectorMountedExtended(viewer: Cesium.Viewer) {
  defineIsMounted();

  const { defaultDataSource, dataSources } = viewer.dataSourceDisplay;
  setMounted(defaultDataSource, true);
  for (let index = 0; index < dataSources.length; index++) {
    const dataSource = dataSources.get(index);
    setMounted(dataSource, true);
  }
  dataSources.dataSourceAdded.addEventListener((_, dataSource) => setMounted(dataSource, true));
  dataSources.dataSourceRemoved.addEventListener((_, dataSource) => setMounted(dataSource, false));
}
