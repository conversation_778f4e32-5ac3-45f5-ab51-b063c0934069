<!-- 倾斜摄影编辑 -->
<script lang="ts" setup>
import type { Cesium3DTileset } from 'cesium';
import type { TreeInstance } from 'element-plus';
import { cartesianToWgs84, throttle } from '@x3d/all';
import { useCzEventArrayListener, useCzEventListener, useCzPrimitive } from '@x3d/vue-hooks';
import { useLayerEffectState } from '../state/layer-effect-state';

defineOptions({ name: 'TilesetEditDialog' });

const props = defineProps<{
  tilesetId?: string;
}>();
const emit = defineEmits<{
  (event: 'update:tilesetId', data?: string): void;
}>();

const tilesetIdRef = useVModel(props, 'tilesetId', emit);

const { getLayerRecordById } = useLayerEffectState();

const tileset = computed(() => {
  return tilesetIdRef.value ? getLayerRecordById(tilesetIdRef.value)?.layer as Cesium3DTileset : undefined;
});

interface TilesetImpl {
  gradient?: boolean;
  color?: string;
  color2?: string;
  showGrid?: boolean;
  showBox?: boolean;
  showAxis?: boolean;
  scale?: number;
  alpha?: number;
}
const treeRef = shallowRef<TreeInstance>();
const tilesetParam = ref<TilesetImpl>({
  scale: 1,
  alpha: 1,
});

const loadedTime = ref(0);

const startMilliseconds = performance.now();

useCzEventListener(
  () => tileset.value?.initialTilesLoaded,
  throttle(() => {
    const endMilliseconds = performance.now();
    loadedTime.value = (endMilliseconds - startMilliseconds) / 1000.0;
    treeRef.value?.$forceUpdate();
  }, 500),
);

const statistics = ref({
  numberOfLoadedTilesTotal: 0,
  numberOfTilesTotal: 0,
  geometryByteLength: 0,
  texturesByteLength: 0,
});

useCzEventArrayListener(
  [() => tileset.value?.tileLoad, () => tileset.value?.tileUnload],
  throttle(() => {
    statistics.value = { ...((tileset.value as any)?.statistics || {}) };
  }, 500),
);

watch(tileset, (tileset) => {
  tileset && (tileset.preferLeaves = true);
});

watchDeep(tilesetParam, (formData) => {
  if (!tileset.value) {
    return;
  }
  // 是否显示网格
  tileset.value.debugWireframe = !!formData.showGrid;
  // 是否显示包围盒
  tileset.value.debugShowContentBoundingVolume = !!formData.showBox;
  // 设置偏移经纬度

  // 设置颜色透明度
  const color = Cesium.Color.fromCssColorString(formData.color || '#FFF').withAlpha(formData?.alpha || 1);
  // 渐变
  if (formData.gradient) {
    const color2 = Cesium.Color.fromCssColorString(formData.color2 || '#FFF').withAlpha(formData?.alpha || 1);

    const customShader = new Cesium.CustomShader({
      uniforms: {
        u_color: {
          type: Cesium.UniformType.VEC3,
          value: color,
        },
        u_color2: {
          type: Cesium.UniformType.VEC3,
          value: color2,
        },
        u_time: {
          type: Cesium.UniformType.FLOAT,
          value: 0,
        },
      },
      fragmentShaderText: `
           void fragmentMain(FragmentInput fsInput, inout czm_modelMaterial material)
            {
                // 渐变
                float _baseHeight = 0.0; // 起始高度
                float _heightRange = 30.0; // 截止高度
                float vtxf_height = fsInput.attributes.positionMC.z-_baseHeight;
                float vtxf_a11 = fract(czm_frameNumber / 120.0) * 3.14159265 * 2.0;
                // 去掉呼吸效果，去掉： + sin(vtxf_a11) * 0.1
                float vtxf_a12 = vtxf_height / _heightRange + sin(vtxf_a11) * 0.1;
                material.diffuse= u_color.rgb;
                material.diffuse += u_color2.rgb * vtxf_a12;
            }
            `,
    });
    tileset.value.customShader = customShader;
    tileset.value.style = undefined;
  }
  else {
    // 设置颜色透明度
    tileset.value.style = new Cesium.Cesium3DTileStyle({
      color: color.toCssColorString(),
    });
    tileset.value.customShader = undefined;
  }
});

function toggle(data: any) {
  data.hidden = !data.hidden;
  Cesium.Matrix4.setUniformScale(
    data.transform,
    data.hidden ? 0.0001 : 1,
    data.transform,
  );
}

const options = ref({
  scaleX: 1,
  scaleY: 1,
  scaleZ: 1,
  longitude: 0,
  latitude: 0,
  height: 0,
  heading: 0,
  pitch: 0,
  roll: 0,
});

watchEffect(() => {
  if (tileset.value) {
    const [longitude, latitude, height] = cartesianToWgs84(tileset.value.boundingSphere.center);
    options.value.longitude = longitude;
    options.value.latitude = latitude;
    options.value.height = height ?? 0;
  }
});

const originPosition = computed(() => tileset.value?.boundingSphere?.center.clone());
const originModelMatrix = computed(() => tileset.value?.modelMatrix?.clone());
const originTransform = computed(() => tileset.value?.root?.transform.clone());

// 坐标轴
useCzPrimitive(() => {
  return tilesetParam.value.showAxis
    ? new Cesium.DebugModelMatrixPrimitive({
      modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
        originPosition.value!,
        new Cesium.HeadingPitchRoll(),
        Cesium.Ellipsoid.WGS84,
        Cesium.Transforms.eastNorthUpToFixedFrame,
      ),
      length: 1000,
      width: 5.0,
    }) as any
    : undefined;
},
);

// 待变换坐标
const finalPosition = computed(() => {
  const { longitude, latitude, height } = options.value;
  if ([longitude, latitude, height].every(e => e === 0)) {
    return undefined;
  }
  else {
    return Cesium.Cartesian3.fromDegrees(longitude, latitude, height);
  }
});

const rotation = computed(() => {
  const h = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(options.value.heading));
  const p = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(options.value.pitch));
  const r = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(options.value.roll));
  return [h, p, r];
});

watch([
  tileset,
  () => options.value.scaleX,
  () => options.value.scaleY,
  () => options.value.scaleZ,
  finalPosition,
  rotation,
], ([
  tileset,
  scaleX,
  scaleY,
  scaleZ,
  finalPosition,
  rotation,
]) => {
  if (!finalPosition || !tileset) {
    return;
  }

  // 平移
  const diff = Cesium.Cartesian3.subtract(finalPosition, originPosition.value!, new Cesium.Cartesian3());
  const matrix = Cesium.Matrix4.fromTranslation(diff, new Cesium.Matrix4());
  tileset.modelMatrix = Cesium.Matrix4.multiply(originModelMatrix.value!, matrix, new Cesium.Matrix4());

  // 旋转
  let transform = Cesium.Matrix4.multiplyByMatrix3(originTransform.value!, rotation[0], new Cesium.Matrix4());
  transform = Cesium.Matrix4.multiplyByMatrix3(transform!, rotation[1], transform);
  transform = Cesium.Matrix4.multiplyByMatrix3(transform!, rotation[2], transform);

  // 缩放
  const scaleMatrix4 = Cesium.Matrix4.fromScale(new Cesium.Cartesian3(scaleX, scaleY, scaleZ));

  transform = Cesium.Matrix4.multiply(transform, scaleMatrix4, transform);
  tileset.root.transform = transform;
});
</script>

<template>
  <drag-card
    v-if="tilesetIdRef"
    title="倾斜摄影-图层编辑"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    @close="tilesetIdRef = undefined"
  >
    <el-scrollbar h="70vh!">
      <div class="tile-view" flex="~ col" h-full>
        <el-divider>基础信息</el-divider>
        <el-descriptions :column="1" mx="20px" border>
          <el-descriptions-item label="已瓦片加载" label-class-name="w-200px!">
            {{ statistics.numberOfLoadedTilesTotal || "--" }} / {{ statistics.numberOfTilesTotal || "--" }}
          </el-descriptions-item>
          <el-descriptions-item label="GPU 内存" label-class-name="w-200px!">
            {{ ((statistics.geometryByteLength + statistics.texturesByteLength) / 1048576 || 0)?.toFixed(2) }}MB
          </el-descriptions-item>
          <el-descriptions-item label="加载用时" label-class-name="w-200px!">
            {{ loadedTime ? `${loadedTime?.toFixed(2)}s` : "--" }}
          </el-descriptions-item>
        </el-descriptions>
        <el-divider mt="40px!">
          模型树
        </el-divider>
        <el-scrollbar v-if="tileset" h="160px!" px="20px">
          <el-tree
            ref="treeRef"
            :load="
              (node, resolve) =>
                resolve(
                  node.level ? node.data?.children : tileset?.root?.children || [],
                )
            "
            lazy
            w-700px
            :data="tileset?.root ? [tileset.root] : []"
          >
            <template #default="{ data }">
              <el-button link mr="10px" @click.stop="toggle(data)">
                <el-icon
                  :class="!data.hidden ? 'i-custom:open-eye' : 'i-custom:close-eye'"
                />
              </el-button>
              <span class="prefix" :class="{ 'is-leaf': data.children.length === 0 }">
                {{ `第${data._depth + 1}层` }}
              </span>
              <span v-if="data?._contentHeader?.uri">
                :{{ data._contentHeader.uri }}
              </span>
            </template>
          </el-tree>
        </el-scrollbar>
        <el-empty v-else :image-size="$vh(50)" />
        <el-divider mt="30px!">
          模型编辑
        </el-divider>
        <el-form :label-width="$vh(58)" pb="20px" mx="20px" grid="~ cols-2">
          <el-form-item label="三角网">
            <el-switch v-model="tilesetParam.showGrid" />
          </el-form-item>
          <el-form-item label="包围盒">
            <el-switch v-model="tilesetParam.showBox" />
          </el-form-item>
          <el-form-item label="坐标轴">
            <el-switch v-model="tilesetParam.showAxis" />
          </el-form-item>
          <el-form-item label="渐变色" grid="col-span-2">
            <el-switch v-model="tilesetParam.gradient" />
          </el-form-item>

          <el-form-item label="颜色" grid="col-span-2">
            <div flex="~" un-children="pr-20px">
              <el-color-picker v-model="tilesetParam.color" show-alpha />
              <el-color-picker v-if="tilesetParam.gradient" v-model="tilesetParam.color2" show-alpha />
            </div>
          </el-form-item>

          <el-form-item label="X比例">
            <el-input-number
              v-model="options.scaleX"
              :step="0.1"
              :max="10"
              :min="0.1"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="Y比例">
            <el-input-number
              v-model="options.scaleY"
              :step="0.1"
              :max="10"
              :min="0.1"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="Z比例">
            <el-input-number
              v-model="options.scaleZ"
              :step="0.1"
              :max="10"
              :min="0.1"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="透明度">
            <el-input-number
              v-model="tilesetParam.alpha"
              :step="0.1"
              :min="0"
              :max="1"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="经度">
            <el-input-number
              v-model="options.longitude"
              :step="0.0001"
              :min="-180"
              :max="180"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="纬度">
            <el-input-number
              v-model="options.latitude"
              :step="0.0001"
              :min="-90"
              :max="90"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="高度">
            <el-input-number
              v-model="options.height"
              :step="1"
              :min="0"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="方向角">
            <el-input-number
              v-model="options.heading"
              :step="1"
              :min="-180"
              :max="180"
              placeholder="请输入"
              controls-position="right"
            />
          </el-form-item>
          <el-form-item label="倾斜角">
            <el-input-number
              v-model="options.pitch"
              :step="1"
              :min="-180"
              :max="180"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label="滚动角">
            <el-input-number
              v-model="options.roll"
              :step="1"
              :min="-180"
              :max="180"
              controls-position="right"
              placeholder="请输入"
            />
          </el-form-item>
        </el-form>
      </div>
    </el-scrollbar>
  </drag-card>
</template>

<style lang="scss" scoped>
.el-form {
  :deep() .el-input-number {
    &__decrease,
    &__increase {
      --el-input-number-controls-height: 19px !important;
    }
  }

  :deep() .el-input {
    --el-input-height: 38px;
    --el-text-color-placeholder: #7e8082;

    &__wrapper {
      padding-left: 11px;
    }
  }

  :deep() .el-select {
    --el-fill-color-blank: #1c1d1e;
    --el-color-primary: #616162;

    &__wrapper {
      height: 38px;
    }
  }

  .horizontal {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
  }
}
</style>
