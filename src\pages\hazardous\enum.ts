/** 枚举转 el:'select 所需的option */
function enum2option<K extends string | number | symbol, V extends string>(enums: Record<K, V>) {
  return Object.keys(enums).map(value => ({
    label: enums[value],
    value,
  }));
}

/** 危险源等级 */
export const HAZARD_RANK_ENUM = {
  1: '一级',
  2: '二级',
  3: '三级',
  4: '四级',
  9: '一般危险源',
};

/** 企业风险等级 */
export const COMPANY_RISK_RANK_ENUM: Record<number, string> = {
  1: '重大风险',
  2: '较大风险',
  3: '一般风险',
  4: '低风险',
  // '9': '离线',
};

/** 企业风险等级 */
export const COMPANY_RISK_RANK_ENUM_COLORS: Record<number, string> = {
  1: '#FF3333',
  2: '#FF9A33',
  3: '#FFD033',
  4: '#33C3FF',
};

/** 企业风险等级 */
export const COMPANY_RISK_RANK_ENUM_OPTION = enum2option(COMPANY_RISK_RANK_ENUM);

/**
 * 企业类型
 */
export const COMPANY_TYPE_ENUM: Record<string, string> = {
  '01': '生产',
  '02': '经营',
  '03': '使用',
  '04': '化工',
  '05': '医药',
  '06': '其他',
};

/**
 * 企业规模
 */
export const COMPANY_SCALE_ENUM: Record<number, string> = {
  1: '大型',
  2: '中型',
  3: '小型',
  4: '微型',
};

/**
 * 企业所属行业
 */
export const COMPANY_INDUSTRY_ENUM: Record<string, string> = {
  A: '农、林、牧、渔业',
  B: '采矿业',
  C: '制造业',
  D: '电力、热力、燃气及水生产和供应业',
  E: '建筑业',
  F: '批发和零售业',
  G: '交通运输、仓储和邮政业',
  H: '住宿和餐饮业',
  I: '信息传输、软件和信息技术服务业',
  J: '金融业',
  K: '房地产业',
  L: '租赁和商务服务业',
  M: '科学研究和技术服务业',
  N: '水利、环境和公共设施管理业',
  O: '居民服务、修理和其他服务业',
  P: '教育',
  Q: '卫生和社会工作',
  R: '文化、体育和娱乐业',
  S: '公共管理、社会保障和社会组织',
  T: '国际组织',
};

/**
 * 火灾等级
 */
export const FIRE_LEVEL_ENUM: Record<number, string> = {
  1: '一般火灾',
  2: '较大火灾',
  3: '重大火灾',
};

/**
 * 火灾等级
 */
export const FIRE_LEVEL_SELECT_OPTION = enum2option(FIRE_LEVEL_ENUM);

/**
 * 非煤矿山证书类型
 */
export const CERTIFICATE_TYPE_ENUM: Record<number, string> = {
  1: '安全生产管理人员',
  2: '注册安全工程师',
  3: '安全生产责任人',
  4: '特种作业人员',
};

/** 降雨量映射 */
export const RAINFALL_AMOUNT_OPTIONS = [
  {
    value: 0,
    name: '0mm',
    color: '#c6c6c6',
  },
  {
    value: 0.01,
    name: '0-10mm',
    color: '#8bc07c',
  },
  {
    value: 10,
    name: '10-25mm',
    color: '#5dc65d',
  },
  {
    value: 25,
    name: '25-50mm',
    color: '#78c0fa',
  },
  {
    value: 50,
    name: '50-100m',
    color: '#3030fc',
  },
  {
    value: 100,
    name: '100-250m',
    color: '#fc30fc',
  },
  {
    value: 250,
    name: '>250mm',
    color: '#92305d',
  },
];
