<script lang="ts" setup>
defineOptions({ name: 'Right' });

const activeName = ref('first');
</script>

<template>
  <div class="file-right-content">
    <el-tabs v-model="activeName">
      <el-tab-pane label="文件详情" name="first" />
      <el-tab-pane label="文件操作" name="second" />
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
.file-right-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 450px;
  height: 300px;
  background: var(--el-bg-color); // #292B2E;
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 6px;
}

::v-deep(.el-tabs__nav) {
  justify-content: space-around;
  width: 100% !important;
  padding: 5px;
  background: #1c1d1e;
}

::v-deep(.el-tabs__item) {
  width: 100%;
  height: 40px;
}

::v-deep(.el-tabs__item.is-active) {
  background: var(--el-bg-color);
}

/* 去下划线 */
::v-deep(.el-tabs__active-bar) {
  display: none;
}

::v-deep(.el-tabs) {
  --el-border-color-light: rgb(0 0 0 / 0%);

  padding-top: 0;
}
</style>
