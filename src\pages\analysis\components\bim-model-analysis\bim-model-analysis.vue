<!-- BIM + 倾斜摄影 -->
<script lang="ts" setup>
import { toStaticFilePath } from '@/utils/resolve-path';
import { CzPlotScheme, PRESET_PLOT_SCHEMES_OPTIONS } from '@x3d/all';
import { useCzPrimitive, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'BimModelAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});

const viewer = useCzViewer();
const { primitive } = useCzPrimitive(async () => {
  return await Cesium.Cesium3DTileset.fromUrl(toStaticFilePath('/3d-tileset/qx-xuexiao/tileset.json'));
});

const { primitive: monomer } = useCzPrimitive(async () => {
  return await Cesium.Cesium3DTileset.fromUrl(toStaticFilePath('/3d-tileset/bim-daxue/tileset.json'));
});

const originPosition = computed(() => monomer.value?.boundingSphere?.center.clone());
const originModelMatrix = computed(() => monomer.value?.modelMatrix?.clone());
const originTransform = computed(() => monomer.value?.root?.transform.clone());

const rotation = computed(() => {
  const h = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(-12));
  const p = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(6));
  const r = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(25));
  return [h, p, r];
});
const finalPosition = computed(() => {
  return Cesium.Cartesian3.fromDegrees(125.138973, 43.827008, 200);
});
watch([monomer, finalPosition, rotation], ([monomer, finalPosition, rotation]) => {
  if (!finalPosition || !monomer) {
    return;
  }
  const scale = 1;

  // 平移
  const diff = Cesium.Cartesian3.subtract(finalPosition, originPosition.value!, new Cesium.Cartesian3());
  const matrix = Cesium.Matrix4.fromTranslation(diff, new Cesium.Matrix4());
  monomer.modelMatrix = Cesium.Matrix4.multiply(originModelMatrix.value!, matrix, new Cesium.Matrix4());

  // 旋转
  let transform = Cesium.Matrix4.multiplyByMatrix3(originTransform.value!, rotation[0], new Cesium.Matrix4());
  transform = Cesium.Matrix4.multiplyByMatrix3(transform!, rotation[1], transform);
  transform = Cesium.Matrix4.multiplyByMatrix3(transform!, rotation[2], transform);

  // 缩放
  const scaleMatrix4 = Cesium.Matrix4.fromUniformScale(scale);
  transform = Cesium.Matrix4.multiply(transform, scaleMatrix4, transform);
  monomer.root.transform = transform;
});

watchEffect(() => {
  primitive.value && viewer.value.flyTo(primitive.value);
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="BIM与倾斜摄影"
    class="w-400px"
    @close="emits('close')"
  >
    <!-- <el-divider>绘制要素</el-divider>
    <div m="x-20px y-10px">
      <el-button type="primary" @click="plotStart()">
        绘制起点
      </el-button>
      <el-button type="primary" @click="plotEnd()">
        绘制终点
      </el-button>
    </div>
    <el-divider>绘制障碍</el-divider> -->
    <div m="x-20px y-10px">
      <!-- <el-button type="primary" @click="plotObstacles('Point')">
        绘制障碍点
      </el-button>
      <el-button type="primary" @click="plotObstacles('Polyline')">
        绘制障碍线
      </el-button> -->
      <!-- <el-button type="primary" @click="plotObstacles()">
        绘制障碍面
      </el-button> -->
    </div>
  </drag-card>
</template>
