<!-- XML编辑器组件 -->
<script lang="ts" setup>
import { xml } from '@codemirror/lang-xml';
import { oneDark } from '@codemirror/theme-one-dark';
import { Codemirror } from 'vue-codemirror';

export interface XmlDialogProps {
  content?: string;
}

export interface XmlDialogEmits {
  (event: 'update:content', data?: string): void;
}

defineOptions({ name: 'XmlDialog' });
const props = defineProps<XmlDialogProps>();
const emit = defineEmits<XmlDialogEmits>();
const model = shallowRef(false);

watchEffect(() => {
  model.value = !!props.content;
});

const xmlContent = ref('');

watch(() => props.content, (newContent) => {
  if (newContent) {
    xmlContent.value = newContent;
  }
}, { immediate: true });

function formatXML() {
  try {
    // 使用DOMParser解析XML
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlContent.value, 'text/xml');

    // 检查解析错误
    const parseError = xmlDoc.getElementsByTagName('parsererror');
    if (parseError.length > 0) {
      ElMessage.error('XML格式错误，无法格式化');
      return;
    }

    // 使用XMLSerializer将格式化后的XML序列化为字符串
    const serializer = new XMLSerializer();
    let formattedXml = '';

    // 简单格式化函数
    function formatXmlString(xml: string) {
      let formatted = '';
      let indent = '';
      const tab = '  '; // 两个空格作为缩进

      xml.split(/>\s*</).forEach((node) => {
        if (node.match(/^\/\w/)) {
          // 如果是结束标签，减少缩进
          indent = indent.substring(tab.length);
        }

        formatted += `${indent}<${node}>\n`;

        if (node.match(/^<?\w[^>]*[^/]$/)) {
          // 如果是开始标签，增加缩进
          indent += tab;
        }
      });

      return formatted.substring(1, formatted.length - 2);
    }

    formattedXml = formatXmlString(serializer.serializeToString(xmlDoc));
    xmlContent.value = formattedXml;
    ElMessage.success('XML格式化成功');
  }
  catch (error) {
    console.error('XML格式化错误:', error);
    ElMessage.error('XML格式化失败');
  }
}
</script>

<template>
  <el-dialog
    :model-value="model"
    title="XML编辑器"
    destroy-on-close
    append-to-body
    @close="emit('update:content', undefined)"
  >
    <div class="xml-editor-container">
      <Codemirror
        v-model="xmlContent"
        placeholder="XML内容"
        :style="{ height: '60vh' }"
        :autofocus="true"
        :indent-with-tab="true"
        :tab-size="2"
        :extensions="[xml(), oneDark]"
      />
      <div class="editor-actions">
        <el-button type="primary" @click="formatXML">
          <el-icon class="i-tabler:code mr-5px" />
          格式化XML
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.xml-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}
</style>
