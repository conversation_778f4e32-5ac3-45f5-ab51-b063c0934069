import type { MaterialPropertySerializateController } from './material-property';

import { OilMaterialProperty } from '@/lib/@geovis3d/material';

export interface OilMaterialPropertySerializateJSON {
  // color?: string;
}

export default <
  MaterialPropertySerializateController<
    'OilMaterialProperty',
    OilMaterialProperty,
    OilMaterialPropertySerializateJSON
  >
>{
  type: 'OilMaterialProperty',
  hit: (property) => {
    return property instanceof OilMaterialProperty;
  },
  toJSON(_property, _time) {
    // time ??= Cesium.JulianDate.now();
    // const color = property?.getValue(time)?.color;
    return {
      type: 'OilMaterialProperty',
      params: {
        // color: ColorSerializate.toJSON(color),
      },
    };
  },
  fromJSON(_json) {
    // const color = json?.params?.color;
    // return new OilMaterialProperty(ColorSerializate.fromJSON(color));
    return new OilMaterialProperty();
  },
};
