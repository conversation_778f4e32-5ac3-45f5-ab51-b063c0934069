<!-- PolylinePulseLinkMaterialPropertyAttribute 属性编辑 -->
<script lang="ts" setup>
import type { PolylinePulseLinkMaterialPropertySerializateJSON } from '@/lib/@geovis3d/plotting';

import ColorAttribute from '../color-attribute.vue';
import { useShallowBinding } from '../hooks';
import NumberAttribute from '../number-attribute.vue';

defineOptions({ name: 'PolylinePulseLinkMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: PolylinePulseLinkMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: PolylinePulseLinkMaterialPropertySerializateJSON): void;
}>();

const model = ref<PolylinePulseLinkMaterialPropertySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="滋滋线" /> -->
  <ColorAttribute v-model="model.color" label="颜色" />
  <NumberAttribute v-model="model.time" :min="1" :precision="0" label="持续时间" />
</template>
