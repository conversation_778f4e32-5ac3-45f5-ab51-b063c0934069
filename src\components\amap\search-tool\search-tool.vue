<!-- 搜索工具 -->
<script lang="ts" setup>
defineOptions({ name: 'SearchTool' });
const type = ref<'poi' | 'navigation'>('poi');
</script>

<template>
  <div class="search-tool">
    <poi-search v-if="type === 'poi'" @change="type = 'navigation'" />
    <navigation-search v-else @change="type = 'poi'" />
  </div>
  <poi-detail />
</template>

<style scoped lang="scss">
.search-tool {
  position: relative;
  top: 0;
  left: 24px;
  display: flex;
  flex-direction: column;
  width: 420px;
  pointer-events: none !important;

  * {
    pointer-events: initial;
  }
}
</style>
