import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';
import { positionUpdate } from './utils/position-update';

/**
 * point标绘配置
 */
export default <PlottingControllerOptions>{
  type: 'point',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 1,
  location: { visible: true },
  altitude: { visible: true },
  update(entity) {
    if (!entity.point) {
      entity.point = new Cesium.PointGraphics({
        pixelSize: 10,
        color: Cesium.Color.RED,
      });
    }
    positionUpdate(entity);
  },
};
