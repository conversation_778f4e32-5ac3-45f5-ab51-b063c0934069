import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.Cesium3DTile} 拓展用法与 {@link Cesium.Cesium3DTile} 基本一致。
 *
 * `GcCesium3DTile.event`鼠标事件监听
 */
export class GcCesium3DTile extends Cesium.Cesium3DTile {
  constructor(...options: ConstructorParameters<typeof Cesium.Cesium3DTile>) {
    super(...options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
