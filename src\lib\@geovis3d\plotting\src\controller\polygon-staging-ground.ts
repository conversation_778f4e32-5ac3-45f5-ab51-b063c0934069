import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import { stagingGround } from '@/lib/@geovis3d/geometry';
import * as Cesium from 'cesium';

/**
 * polygon-staging-ground 标绘配置 集结地
 */
export default <PlottingControllerOptions>{
  type: 'polygon-staging-ground',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 3,
  location: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  // interval: { visible: false },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    const coords: any = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);

    if (coords.length < 3) {
      entity._cache = undefined;
      return;
    }
    const coordinates = stagingGround(coords);
    entity._cache = new Cesium.PolygonHierarchy(
      coordinates.map(e => wgs84ToCartesian([...e, 0])),
    );
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
