<!-- 剖面分析 -->
<script lang="ts" setup>
import type { EChartsOption } from 'echarts';
import { CzEntity, CzPlotEntity, distance } from '@x3d/all';
import { useCzEntities, useCzViewer } from '@x3d/vue-hooks';

import { Color, PointGraphics } from 'cesium';

defineOptions({ name: 'ProfileAnalysis' });
const viewer = useCzViewer();
const plotEntity = shallowRef<CzPlotEntity>();
const curPoint = shallowRef<CzEntity>();
const points = ref<Cesium.Cartesian3[]>();

function drawSlice() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      manualTerminate: (entity) => {
        const bool = entity.record.positions.getLength() >= 2;
        points.value = entity.record.positions.getPositions();
        return bool;
      },
      effect(entity) {
        entity.polyline ??= new Cesium.PolylineGraphics({
          material: Cesium.Color.fromCssColorString('#FCC650'),
          width: 2,
          clampToGround: true,
        });
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 2 ? positions : [];
        entity.polyline.positions = new Cesium.CallbackProperty(() => cache, false);
      },
    },
  });
}
const { isActive } = useCzEntities(() => [plotEntity.value, curPoint.value]);
// useCzEntity(() => curPoint.value);
const seriesData = computedAsync(async () => {
  if (!points.value?.length)
    return [];
  let disCartographic: { x: number; y: number; z: number }[] = [];
  const { count: lineLength } = await distance(points.value || []);
  for (let i = 1; i < points.value?.length || 0; i++) {
    const startPoint = points.value?.[i - 1] || Cesium.Cartesian3.ZERO;
    const endPoint = points.value?.[i] || Cesium.Cartesian3.ZERO;
    const count = Cesium.Cartesian3.distance(startPoint, endPoint);
    const num = Math.round(500 * (count / lineLength));
    const pointsH = await profileAnalyse(startPoint, endPoint, num);
    disCartographic = disCartographic?.concat(pointsH?.arrPoint || []);
  }
  return disCartographic;
});

// 世界坐标转换为经纬度
function getDegrees(cart: Cesium.Cartesian3) {
  const cartographic = viewer.value.scene.globe.ellipsoid.cartesianToCartographic(cart);
  const lat = Cesium.Math.toDegrees(cartographic.latitude);
  const lng = Cesium.Math.toDegrees(cartographic.longitude);
  const alt = cartographic.height;
  return { x: lng, y: lat, z: alt };
}
// 剖面分析
async function profileAnalyse(
  start: Cesium.Cartesian3,
  end: Cesium.Cartesian3,
  count: number,
) {
  const startPoint = Cesium.Cartographic.fromCartesian(start);
  const endPoint = Cesium.Cartographic.fromCartesian(end);
  const profile = {
    arrHB: [] as number[],
    arrPoint: [] as { x: number; y: number; z: number }[],
    ponits: [] as Cesium.Cartesian3[],
    disc: [] as number[],
  };
  profile.ponits.push(start);
  profile.arrPoint.push(getDegrees(start));
  profile.arrHB.push(startPoint.height);
  // 插值100个点，点越多模拟越精确，但是效率会低
  const arrHeight = [];
  for (let i = 0; i < count; i++) {
    const cart = Cesium.Cartesian3.lerp(start, end, i / count, new Cesium.Cartesian3());
    arrHeight.push(cart);
  }
  // scene.clampToHeightMostDetailed获取模型样本高度，详情可查看cesium文档
  await viewer.value.scene
    .clampToHeightMostDetailed(arrHeight)
    .then((clampedCartesians) => {
      for (let i = 0; i < count; i++) {
        profile.disc.push(
          Cesium.Cartesian3.distance(profile.ponits[i], clampedCartesians[i]),
        );
        profile.ponits.push(clampedCartesians[i]);
        profile.arrPoint.push(getDegrees(clampedCartesians[i]));
        profile.arrHB.push(
          Cesium.Cartographic.fromCartesian(clampedCartesians[i]).height,
        );
      }
      profile.ponits.push(end);
      profile.arrPoint.push(getDegrees(end));
      profile.arrHB.push(endPoint.height);
    });
  return profile;
}

const option = computed(() => {
  return {
    grid: {
      left: 10,
      right: 10,
      bottom: 20,
      top: 20,
      containLabel: !0,
    },
    tooltip: {
      trigger: 'axis',
      formatter(e: any) {
        let htmldiv = '';
        const { x, y, z } = seriesData.value[e?.[0]?.dataIndex];
        curPoint.value = new CzEntity({
          position: Cesium.Cartesian3.fromDegrees(x, y),
          point: new PointGraphics({
            pixelSize: 10.0,
            color: Color.RED,
            outlineColor: Color.WHITE,
            outlineWidth: 2,
            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          }),
        });
        return (htmldiv
          += `所在位置:${x.toFixed(2)},${y.toFixed(2)}<br/>`
            + `高程值:${z > 1000 ? `${(z / 1000).toFixed(2)}km` : `${z.toFixed(2)}m`}<br/>`);
      },
    },
    xAxis: {
      name: '距离',
      type: 'category',
      boundaryGap: !1,
      axisLabel: {
        show: !1,
      },
      data: seriesData.value?.map(e => e.z),
    },

    yAxis: {
      type: 'value',
      axisLabel: {
        rotate: 0,
        formatter: '{value} 米',
      },
    },

    series: [
      {
        name: '高程值',
        type: 'line',
        smooth: true,
        symbol: 'none',
        sampling: 'average',
        itemStyle: {
          normal: {
            color: 'rgb(255, 70, 131)',
          },
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(255, 158, 68)',
              },
              {
                offset: 1,
                color: 'rgb(255, 70, 131)',
              },
            ]),
          },
        },
        data: seriesData.value?.map(e => e.z),
      },
    ],
  } as EChartsOption;
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="剖面分析"
    class="w-500px"
    @mouseleave="curPoint = undefined"
  >
    <vue-echarts
      v-show="points?.length"
      :option="option"
      autoresize
      class="h-200px w-full px-20px"
    />
    <template #footer>
      <el-button class="plain-#5474FC" @click="drawSlice">
        绘制横切面
      </el-button>
      <el-button
        class="plain-#FF6363"
        @click="(plotEntity = undefined), (points = []), (curPoint = undefined)"
      >
        清空
      </el-button>
    </template>
  </drag-card>
</template>
