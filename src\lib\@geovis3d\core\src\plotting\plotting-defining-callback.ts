import type * as Cesium from 'cesium';
import type { PlottingController } from './plotting-controller';

import { coordinateToCartesian } from '@/lib/@geovis3d/coordinate';
import { promiseTimeout } from '@/lib/@geovis3d/shared';

/**
 * 标绘处于定义态时的相关回调
 * @param controller
 */
export function plottingDefiningCallback(controller: PlottingController) {
  /**
   * 避免双击事件诱发成单击两次的事件
   */
  let doubleClicking = false;

  /**
   * 添加控制点
   *
   * 左键点击回调 、判断是否终止定义态
   * @internal
   */
  const add = async ({ position }: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    await promiseTimeout(1); // 下一事件循环中在执行，避免双击事件诱发成单击两次的事件

    const { entity: parent, scene, coordinates } = controller;
    if (!doubleClicking) {
      // 不是模型标 要贴模型
      const mode = controller.entity.model ? 'globePick' : 'auto';
      const cartesian = coordinateToCartesian(position, scene!, mode)!;

      if (cartesian) {
        coordinates.addPosition(cartesian);
        if (controller.options?.forceTerminate?.(parent)) {
          controller?._setDefining(false);
        }
      }
    }
  };

  /**
   * 停止定义态,左键双击回调
   * @internal
   */
  const terminate = async () => {
    const parent = controller.entity;
    if (controller.options?.manualTerminate?.(parent)) {
      controller?._setDefining(false);
      // 阻止双击事件诱发成单击两次的事件
      doubleClicking = true;
      await promiseTimeout(2);
      doubleClicking = false;
    }
  };

  /**
   * 右键单击回调，回退控制点
   * @internal
   */
  const revocate = async () => {
    const coordinates = controller.coordinates;
    const length = coordinates.getLength();
    if (length) {
      coordinates.removeIndex(length - 1);
    }
  };

  controller.definitionChanged.addEventListener((_, field, value) => {
    if (field == 'defining') {
      const scene = controller.scene!;
      if (value) {
        scene.geovis3d?.screenEvent.on('LEFT_CLICK', add);
        scene.geovis3d?.screenEvent.on('LEFT_DOUBLE_CLICK', terminate);
        scene.geovis3d?.screenEvent.on('RIGHT_CLICK', revocate);
      }
      else {
        scene.geovis3d?.screenEvent.off('LEFT_CLICK', add);
        scene.geovis3d?.screenEvent.off('LEFT_DOUBLE_CLICK', terminate);
        scene.geovis3d?.screenEvent.off('RIGHT_CLICK', revocate);
      }
    }
  });
}
