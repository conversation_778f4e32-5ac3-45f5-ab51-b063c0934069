<!-- 站点火险排名 -->
<script lang="ts" setup>
import { computedLoading } from '@/hooks/computed-loading';
import mockData from '../mockData2.json'; // 导入模拟数据
// import { oneMapCtrGetStationList } from '../api';
import { injectFireFactorState } from '../state';

defineOptions({ name: 'RightStationList' });
computedLoading
const { activeStationId } = injectFireFactorState()!;

const keyword = ref('');

const { state, execute, isLoading } = useAsyncState(
  async () => {
    // 使用模拟数据替换API调用
    const records = mockData.records;

    // 如果有关键词，进行过滤
    const filteredRecords = keyword.value
      ? records.filter(item =>
          item.name.includes(keyword.value)
          || (item.code && item.code.includes(keyword.value)),
        )
      : records;

    // 保持原有的排序逻辑
    return filteredRecords.sort((a, b) => +b.risklevel - +a.risklevel);
  },
  [],
  {
    immediate: true,
  },
);

watchThrottled([keyword], () => execute(), {
  throttle: 2000,
});

const risklevels = [
  {
    code: '1',
    label: '一级火险',
  },
  {
    code: '2',
    label: '二级火险',
  },
  {
    code: '3',
    label: '三级火险',
  },
  {
    code: '4',
    label: '四级火险',
  },
  {
    code: '5',
    label: '五级火险',
  },
];
</script>

<template>
  <header-title1>火险因子监测</header-title1>
  <div class="mx-10px">
    <el-input v-model="keyword" placeholder="请输入站点名称/编号" size="large">
      <template #prefix>
        <el-icon class="i-tabler:search" />
      </template>
    </el-input>
  </div>
  <el-scrollbar v-loading="isLoading" wrap-class="p-10px" h="800px!">
    <div
      v-for="item in state"
      :key="item.id"
      flex="~ col"
      b="1px"
      b-color="[var(--el-border-color)]"
      p="10px"
      rd="6px"
      m="b-10px"
      text="14px"
      cursor="pointer"
      @click="activeStationId = item.id"
    >
      <div flex="~ justify-between">
        <el-text text="start! 16px!" flex="1" font="bold" truncated>
          {{ item.name }}
        </el-text>
        <el-text v-if="+item.risklevel > 2" text="start! 16px!" font="bold" type="danger">
          {{ risklevels.find(e => e.code === item.risklevel)?.label }}
        </el-text>
        <el-text v-else-if="item.isonline" text="start! 16px!" type="success">
          在线
        </el-text>
        <el-text v-else text="start! 16px!" type="danger">
          离线
        </el-text>
      </div>
      <el-divider m="y-10px!" />
      <span>地区：{{ item.areaname }}</span>
      <span>负责人/电话：{{ item.dutyman }}/{{ item.dutymantel }}</span>
    </div>
  </el-scrollbar>
</template>
