<!-- TimeIntervalCollectionAttribute -->
<script lang="ts" setup>
import type { TimeIntervalCollectionSerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import BooleanAttribute from './boolean-attribute.vue';
import { useShallowBinding } from './hooks';
import JulianDateAttribute from './julian-date-attribute.vue';

defineOptions({ name: 'TimeIntervalCollectionAttribute' });

const props = defineProps<{
  modelValue?: TimeIntervalCollectionSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: TimeIntervalCollectionSerializateJSON): void;
}>();

const model = ref<TimeIntervalCollectionSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <JulianDateAttribute v-model="model.start" label="start" />
  <JulianDateAttribute v-model="model.stop" label="stop" />
  <BooleanAttribute v-model="model.isStartIncluded" label="isStartIncluded" />
  <BooleanAttribute v-model="model.isStopIncluded" label="isStopIncluded" />
  data?: any;
</template>
