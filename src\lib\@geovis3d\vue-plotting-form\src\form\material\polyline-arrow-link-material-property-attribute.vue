<!-- PolylineArrowLinkMaterialPropertyAttribute 属性编辑 -->
<script lang="ts" setup>
import type { PolylineArrowLinkMaterialPropertySerializateJSON } from '@/lib/@geovis3d/plotting';

import ColorAttribute from '../color-attribute.vue';
import { useShallowBinding } from '../hooks';
import NumberAttribute from '../number-attribute.vue';

defineOptions({ name: 'PolylineArrowLinkMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: PolylineArrowLinkMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: PolylineArrowLinkMaterialPropertySerializateJSON): void;
}>();

const model = ref<PolylineArrowLinkMaterialPropertySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="动态箭头线" /> -->
  <ColorAttribute v-model="model.color" label="颜色" />
  <NumberAttribute v-model="model.time" :min="1" :precision="0" label="持续时间" />
</template>
