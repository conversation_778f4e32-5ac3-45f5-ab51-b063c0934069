<!-- 企业基本信息 -->
<script lang="ts" setup>
import type {
  CompanyDTO,
} from '@/genapi/production';
import {
  productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet,
} from '@/genapi/production';
import { computedLoading } from '@/hooks/computed-loading';
import { COMPANY_SCALE_ENUM, COMPANY_TYPE_ENUM, HAZARD_RANK_ENUM } from '../../enum';

defineOptions({ name: 'DetailBasicInfo' });

const props = defineProps<{
  data?: CompanyDTO;
}>();
  // 危险源列表
const [sources] = computedLoading(async () => {
  if (!props.data?.companyCode)
    return;
  const { data } = await productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet({
    path: { companyCode: props.data?.companyCode },
  });
  return data;
});

const sourceActives = ref<string[]>();

watchEffect(() => {
  if (sources.value) {
    sourceActives.value = sources.value.map(e => e.id!);
  }
});
</script>

<template>
  <el-scrollbar class="h-100%" wrap-class="px-20px">
    <header-title2 class="mb-10px">
      企业信息
    </header-title2>
    <el-descriptions :column="3" border>
      <el-descriptions-item label="行政区划">
        {{ data?.areaCode }}
      </el-descriptions-item>
      <el-descriptions-item label="成立时间">
        {{ data?.establishDate }}
      </el-descriptions-item>
      <el-descriptions-item label="企业类型">
        {{ COMPANY_TYPE_ENUM[data?.companyType || ''] || '其他' }}
      </el-descriptions-item>
      <el-descriptions-item label="法人代表">
        {{ data?.representativePerson }}
      </el-descriptions-item>
      <el-descriptions-item label="主要负责人">
        {{ data?.responsiblePerson }}
      </el-descriptions-item>
      <el-descriptions-item label="主要负责人电话">
        {{ data?.responsiblePhone }}
      </el-descriptions-item>
      <el-descriptions-item label="企业规模">
        {{ COMPANY_SCALE_ENUM[data?.companyScale || ''] || '其他' }}
      </el-descriptions-item>
      <el-descriptions-item label="安全负责人">
        {{ data?.safetyResponsiblePerson }}
      </el-descriptions-item>
      <el-descriptions-item label="安全负责人电话">
        {{ data?.safetyResponsiblePhone }}
      </el-descriptions-item>
      <el-descriptions-item label="职工人数">
        {{ data?.peopleEmployee }}
      </el-descriptions-item>
      <el-descriptions-item label="具体地址">
        {{ data?.addressRegistry }}
      </el-descriptions-item>
    </el-descriptions>

    <header-title2 class="my-10px">
      危险源
    </header-title2>
    <el-collapse v-model="sourceActives">
      <el-collapse-item v-for="source in sources" :key="source.id" :name="source.id">
        <template #title>
          {{ source.hazardName }}
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="危险源名称">
            {{ source?.hazardName }}
          </el-descriptions-item>
          <el-descriptions-item label="危险源等级">
            {{ HAZARD_RANK_ENUM[source?.hazardRank ?? ''] }}
          </el-descriptions-item>
          <el-descriptions-item label="R值">
            {{ source?.rvalue }}
          </el-descriptions-item>
          <el-descriptions-item label="危险源类型">
            {{ source?.hazardFacility }}
          </el-descriptions-item>
          <el-descriptions-item label="投用日期">
            {{ source?.establishDate }}
          </el-descriptions-item>
          <el-descriptions-item label="与防护目标最近距离">
            {{ source?.protectionTargetDistance }}
          </el-descriptions-item>
          <el-descriptions-item label="500米范围内人数">
            {{ source?.people500m }}
          </el-descriptions-item>
        </el-descriptions>
      </el-collapse-item>
    </el-collapse>
  </el-scrollbar>
</template>

<style scoped lang="scss">
  .el-descriptions {
  --el-border-color-lighter: #3c57a1;
  --el-descriptions-item-bordered-label-background: #08357d;
}

:deep().el-descriptions__label {
  text-align: right !important;
}

.el-collapse {
  .el-descriptions {
    --el-descriptions-table-border: 0;
    --el-descriptions-item-bordered-label-background: rgba(#fff, 0%);

    background: rgb(#292b2e, 30%);
  }

  :deep() .el-descriptions__label {
    color: #b8e5ff !important;
    text-align: right !important;
  }

  :deep().el-descriptions__content {
    --el-text-color-primary: #fff;
  }
}
</style>
