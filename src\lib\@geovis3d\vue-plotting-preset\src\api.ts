import type { AxiosInstance } from 'axios';
import type { ApiRequest, PlotAssetModel, PlotDetailLayer, PlotSimpleLayer } from './types';

import { getKeys } from '@/lib/@geovis3d/core';
import axios from 'axios';

/**
 * 标绘接口
 * @property baseURL  基础接口路径
 * @property appCode  应用编码
 */
export class PlotApi {
  constructor(baseURL: string, appCode: string) {
    this._http = axios.create({ baseURL });
    this._appCode = appCode;
  }

  private _appCode: string;

  private _http: AxiosInstance;

  /**
   * 获取图标树
   * @param dir
   */
  async getDirTreeList(dir?: string): Promise<ApiRequest<PlotAssetModel[]>> {
    const { data } = await this._http({
      url: '/basic/localFile/readDir2Tree',
      params: {
        dir,
      },
    });
    return data;
  }

  /**
   * 获取标绘面板列表
   * @param params
   */
  async getPlotLayerList(): Promise<ApiRequest<PlotSimpleLayer[]>> {
    const { data } = await this._http({
      url: '/basic/mapPlotting/getList',
      method: 'post',
      data: {
        appCode: this._appCode,
      },
    });
    return data;
  }

  /**
   * 获取标绘图层详情
   * @param id
   */
  async getPlotLayerById(id: string): Promise<ApiRequest<PlotDetailLayer | undefined>> {
    const { data } = await this._http({
      url: `/basic/mapPlotting/getById/${id}`,
    });
    return data;
  }

  /**
   * 更新标绘图层，有ID则更新，无ID则新增
   * @param params
   */
  async saveOrUpdatePlotLayer(params: PlotDetailLayer): Promise<ApiRequest> {
    const data = new FormData();
    getKeys(params).forEach((key) => {
      data.set(key, params[key] as any);
    });
    data.set('appCode', this._appCode);
    return this._http({
      url: '/basic/mapPlotting/saveOrUpdate',
      headers: { 'Content-Type': 'multipart/form-data' },
      method: 'post',
      data,
    });
  }

  /**
   * 删除某个图层
   * @param id
   */
  async deletePlotLayerById(id: string): Promise<ApiRequest> {
    const { data } = await this._http({
      url: `/basic/mapPlotting/deleteById/${id}`,
    });
    return data;
  }
}
