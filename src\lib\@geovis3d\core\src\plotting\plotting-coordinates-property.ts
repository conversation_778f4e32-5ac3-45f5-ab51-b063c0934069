import * as Cesium from 'cesium';

/**
 * 标绘点位配置
 */
export interface ControlPointSymbol {
  /**
   * 唯一ID
   */
  id: string;

  /**
   * 笛卡尔点位
   */
  position: Cesium.Cartesian3;
}

/**
 * 标绘控制点位储存器
 */
export class PlottingCoordinatesProperty extends Cesium.ConstantProperty {
  constructor(value?: ControlPointSymbol[]) {
    super(value ?? []);
  }

  declare getValue: (
    time?: Cesium.JulianDate,
    result?: ControlPointSymbol[]
  ) => ControlPointSymbol[];

  declare setValue: (value: ControlPointSymbol[]) => void;

  declare equals: (other?: Cesium.Property) => boolean;

  cloneValue() {
    return [...this.getValue()];
  }

  getLength() {
    return this.getValue().length;
  }

  containsId(id: string): ControlPointSymbol | undefined {
    return this.getValue().find(e => e.id === id);
  }

  getById(id: string): Cesium.Cartesian3 | undefined {
    const item = this.getValue().find(e => e.id === id);
    return item?.position;
  }

  getPositions(): Cesium.Cartesian3[] {
    return this.getValue().map(e => e.position);
  }

  /**
   * 获取中心点。
   * 若只有一个点，则此点为中心点
   */
  getCenter(): Cesium.Cartesian3 | null {
    const positions = this.getPositions();
    if (positions.length === 0) {
      return null;
    }
    else if (positions.length == 1) {
      return positions[0].clone();
    }
    else {
      const total = positions.reduce(
        (total, current) => Cesium.Cartesian3.add(total, current, total),
        new Cesium.Cartesian3(0, 0, 0),
      );
      return Cesium.Cartesian3.divideByScalar(total, positions.length, total);
    }
  }

  getByIndex(index: number): Cesium.Cartesian3 | undefined {
    return this.getValue()[index]?.position;
  }

  setByIndex(index: number, position: Cesium.Cartesian3): boolean {
    const value = this.cloneValue();
    const item = value[index];
    if (item) {
      const id = item.id;
      value.splice(index, 1, { id, position });
      this.setValue(value);
      return true;
    }
    return false;
  }

  addPosition(position: Cesium.Cartesian3, index?: number): ControlPointSymbol {
    const value = this.cloneValue();
    const item = { id: Cesium.createGuid(), position };
    !Number.isNaN(+`${index}`) ? value.splice(+index!, 0, item) : value.push(item);
    this.setValue(value);
    return item;
  }

  setById(id: string, position: Cesium.Cartesian3): boolean {
    const index = this.getValue().findIndex(e => e.id === id);
    return this.setByIndex(index, position);
  }

  removeById(id: string): boolean {
    const value = this.cloneValue();
    const index = value.findIndex(e => e.id === id);
    if (index != -1) {
      return this.removeIndex(index);
    }
    return false;
  }

  removeIndex(index: number): boolean {
    const value = this.cloneValue();
    if (index <= value.length) {
      value.splice(index, 1);
      this.setValue(value);
      return true;
    }
    return false;
  }
}
