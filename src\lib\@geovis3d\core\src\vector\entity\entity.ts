import type { ArgsFunction } from '@/lib/@geovis3d/shared';
import type { ParticleGraphicsConstructorOptions } from './particle-graphics';
import { effectHelper } from '@/lib/@geovis3d/shared';

import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';
import { entityParticleGraphicsEffect, ParticleGraphics } from './particle-graphics';

import { createEntityPopover, createEntityPrompt, createEntityTooltip } from './popper-render';

/**
 * Entity自身html弹层渲染回调
 */
export interface CreateEntityPopoverRenderOptions<E extends GcEntity = GcEntity> {
  entity: E;
  element: HTMLDivElement;
  onCleanup: (fn: VoidFunction) => void;
  close: () => void;
}

/**
 * Entity自身html弹层渲染函数
 */
export type CreateEntityPopoverRender = (options: CreateEntityPopoverRenderOptions) => void;

type EntityConstructorOptions = Cesium.Entity.ConstructorOptions;

/**
 * GcEntity构造参数
 */
export interface GcEntityConstructorOptions extends EntityConstructorOptions {
  /**
   * 当entity载入时，会依次触发此数组内的副作用函数
   */
  effects?: CesiumEntityEffect[];

  /**
   * 粒子系统
   * 见： {@link ParticleGraphics}
   */
  particle?: ParticleGraphics | ParticleGraphicsConstructorOptions;

  /**
   * 悬停时的弹层
   * 对应事件: HOVER
   */
  tooltip?: CreateEntityPopoverRender;

  /**
   * 单击时html弹层
   * 对应事件: LEFT_CLICK
   */
  popover?: CreateEntityPopoverRender;

  /**
   * 拖拽时html弹层
   * 对应事件: DARG
   */
  prompt?: CreateEntityPopoverRender;

  /**
   * 右键时html弹层
   * 对应事件: RIGHT_CLCIK
   */
  contextmenu?: CreateEntityPopoverRender;
}

export class GcEntity extends Cesium.Entity {
  constructor(options: GcEntityConstructorOptions = {}) {
    const { particle, tooltip, popover, prompt, contextmenu, effects, ...superOptions } = options;
    super(superOptions);
    this._event = new CesiumVectorEventCollection();
    this.tooltip = tooltip;
    this.popover = popover;
    this.prompt = prompt;
    this.contextmenu = contextmenu;
    this.addProperty('particle');

    if (particle) {
      const particleSystemGraphics
        = particle instanceof ParticleGraphics ? particle : new ParticleGraphics(particle);
      this.particle = particleSystemGraphics;
      entityParticleGraphicsEffect(this);
    }

    createEntityTooltip(this);
    createEntityPopover(this);
    createEntityPrompt(this);
    effects?.forEach(effect => createEntityEffect(this, effect));
  }

  get scene(): Cesium.Scene | undefined {
    let owner = this.entityCollection?.owner;
    let count = 0;
    while (owner instanceof Cesium.CompositeEntityCollection) {
      if (count > 100) {
        break;
      }
      else {
        owner = owner.owner;
        count++;
      }
    }
    if (!(owner instanceof Cesium.CompositeEntityCollection)) {
      return (owner.clustering as any)._scene;
    }
  }

  /**
   * 将粒子系统`ParticleSystem`与`Entity`结合
   *
   * 优势在于不需要进行一大堆entity矩阵转换对粒子系统进行定位(内部已实现)，只需要直接将想要的粒子参数输入即可。
   * 与 {@link Cesium.ParticleSystem} 初始化几乎一致。
   */
  declare particle?: ParticleGraphics;

  declare readonly isMounted: boolean;

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  get event(): CesiumVectorEventCollection {
    return this._event;
  }

  /**
   * 悬停时html弹层
   * 对应事件: HOVER
   */
  tooltip?: CreateEntityPopoverRender;

  /**
   * 单击时html弹层
   * 对应事件: LEFT_CLICK
   */
  popover?: CreateEntityPopoverRender;

  /**
   * 拖拽时html弹层
   * 对应事件: DARG
   */
  prompt?: CreateEntityPopoverRender;

  /**
   * 右键时html弹层
   * 对应事件: RIGHT_CLCIK
   */
  contextmenu?: CreateEntityPopoverRender;
}

/**
 * entity载入时的副作用函数
 *
 * @param onCleanup 当entity被移除时触发的函数，可用于清除副作用
 * @param entity 此entity
 *
 */
export type CesiumEntityEffect = ArgsFunction<
  [onCleanup: ArgsFunction<[VoidFunction]>, entity: GcEntity]
>;

/**
 * 创建entity载入时的副作用函数
 * @param entity
 * @param effect
 */
export function createEntityEffect(entity: GcEntity, effect: CesiumEntityEffect) {
  const [execute, destroy] = effectHelper((onCleanup) => {
    effect(onCleanup, entity);
  });
  entity.definitionChanged.addEventListener((_, field, value) => {
    if (field === 'isMounted') {
      value ? execute() : destroy();
    }
  });
}
