import type { Coord, Feature, LineString, Point, Position } from '@turf/turf';

import { getCoord, lineString, midpoint, point } from '@turf/turf';
import {
  getAngleOfThreePoints,
  getBaseLength,
  getBatchCoords,
  getBezierPoints,
  getThirdPoint,
  HALF_PI,
  isClockWise,
  mathDistance,
  wholeDistance,
} from './common';

export interface DoubleArrowFactorOptions {
  headHeightFactor: number;
  headWidthFactor: number;
  neckWidthFactor: number;
  neckHeightFactor: number;
}
/**
 *  双箭头，需要5个点
 */
export function clampAttackArrow(
  points: Coord[],
  options?: DoubleArrowFactorOptions,
): Feature<LineString> {
  if (points.length < 5) {
    throw new Error('points.length must >=5');
  }
  const headHeightFactor = options?.headHeightFactor ?? 0.25;
  const headWidthFactor = options?.headWidthFactor ?? 0.3;
  const neckHeightFactor = options?.neckHeightFactor ?? 0.85;
  const neckWidthFactor = options?.neckWidthFactor ?? 0.15;
  options = {
    headHeightFactor,
    headWidthFactor,
    neckHeightFactor,
    neckWidthFactor,
  };

  const [coord1, coord2, coord3, coord4, coord5] = getBatchCoords(points);

  let leftArrowCoords: Position[];
  let rightArrowCoords: Position[];
  if (isClockWise(coord1, coord2, coord3)) {
    leftArrowCoords = getBatchCoords(getArrowPoints(coord1, coord5, coord4, false, options));
    rightArrowCoords = getBatchCoords(getArrowPoints(coord5, coord2, coord3, true, options));
  }
  else {
    leftArrowCoords = getBatchCoords(getArrowPoints(coord2, coord5, coord3, false, options));
    rightArrowCoords = getBatchCoords(getArrowPoints(coord5, coord1, coord4, true, options));
  }
  const m = leftArrowCoords.length;

  const t = (m - 5) / 2;
  const llBodyCoords = leftArrowCoords.slice(0, t);
  const lArrowCoords = leftArrowCoords.slice(t, t + 5);
  const lrBodyCoords = leftArrowCoords.slice(t + 5, m);
  const rlBodyCoords = rightArrowCoords.slice(0, t);
  const rArrowCoords = rightArrowCoords.slice(t, t + 5);
  const rrBodyCoords = rightArrowCoords.slice(t + 5, m);
  const _rlBodyCoords = getBezierPoints(lineString(rlBodyCoords)).geometry.coordinates;
  const bodyCoords = getBezierPoints(lineString(rrBodyCoords.concat(llBodyCoords.slice(1))));
  const _lrBodyCoords = getBezierPoints(lineString(lrBodyCoords));

  const pnts = _rlBodyCoords.concat(
    rArrowCoords,
    bodyCoords.geometry.coordinates,
    lArrowCoords,
    _lrBodyCoords.geometry.coordinates,
  );

  return lineString(pnts);
}

/**
 * 插值箭形上的点
 * @param coord1
 * @param coord2
 * @param coord3
 * @param clockWise

 */
function getArrowPoints(
  coord1: Coord,
  coord2: Coord,
  coord3: Coord,
  clockWise: boolean,
  options: DoubleArrowFactorOptions,
): Feature<Point>[] {
  const midPoint = midpoint(coord1, coord2);
  const len = mathDistance(midPoint, coord3);
  let midPoint1 = getThirdPoint(coord3, midPoint, 0, len * 0.3, true);
  let midPoint2 = getThirdPoint(coord3, midPoint, 0, len * 0.5, true);
  midPoint1 = getThirdPoint(midPoint, midPoint1, HALF_PI, len / 5, clockWise);
  midPoint2 = getThirdPoint(midPoint, midPoint2, HALF_PI, len / 4, clockWise);
  const points = [midPoint, midPoint1, midPoint2, coord3];
  const arrowPoints = getArrowHeadPoints(points, options);
  if (arrowPoints && arrowPoints.length > 0) {
    const neckLeftPoint = arrowPoints[0];
    const neckRightPoint = arrowPoints[4];

    const tailWidthFactor = mathDistance(coord1, coord2) / getBaseLength(points) / 2;
    const bodyPoints = getArrowBodyPoints(points, neckLeftPoint, neckRightPoint, tailWidthFactor);
    const length = bodyPoints.length;
    let lPoints = bodyPoints.slice(0, length / 2);
    let rPoints = bodyPoints.slice(length / 2, length);
    lPoints.push(neckLeftPoint);
    rPoints.push(neckRightPoint);
    lPoints = lPoints.reverse();
    lPoints.push(point(getCoord(coord2)));
    rPoints = rPoints.reverse();
    rPoints.push(point(getCoord(coord1)));
    return [...lPoints.reverse(), ...arrowPoints, ...rPoints];
  }

  throw new Error('插值出错');
}

/**
 * 插值头部点
 * @param points

 */
function getArrowHeadPoints(points: Coord[], options: DoubleArrowFactorOptions): Feature<Point>[] {
  const len = getBaseLength(points);
  const headHeight = len * options.headHeightFactor;
  const headPoint = point(getCoord(points.at(-1)));
  const headWidth = headHeight * options.headWidthFactor;
  const neckWidth = headHeight * options.neckWidthFactor;
  const neckHeight = headHeight * options.neckHeightFactor;
  const headEndPoint = getThirdPoint(points.at(-2), headPoint, 0, headHeight, true);
  const neckEndPoint = getThirdPoint(points.at(-2), headPoint, 0, neckHeight, true);
  const headLeft = getThirdPoint(headPoint, headEndPoint, HALF_PI, headWidth, false);
  const headRight = getThirdPoint(headPoint, headEndPoint, HALF_PI, headWidth, true);
  const neckLeft = getThirdPoint(headPoint, neckEndPoint, HALF_PI, neckWidth, false);
  const neckRight = getThirdPoint(headPoint, neckEndPoint, HALF_PI, neckWidth, true);
  return [neckLeft, headLeft, headPoint, headRight, neckRight];
}

/**
 * 插值面部分数据
 * @param points
 * @param neckLeft
 * @param neckRight
 * @param tailWidthFactor

 */
function getArrowBodyPoints(
  points: Coord[],
  neckLeft: Coord,
  neckRight: Coord,
  tailWidthFactor: number,
): Feature<Point>[] {
  const allLen = wholeDistance(points);
  const len = getBaseLength(points);
  const tailWidth = len * tailWidthFactor;
  const neckWidth = mathDistance(neckLeft, neckRight);
  const widthDif = (tailWidth - neckWidth) / 2;
  let tempLen = 0;
  const leftBodyCoords: Feature<Point>[] = [];
  const rightBodyCoords: Feature<Point>[] = [];

  for (let i = 1; i < points.length - 1; i++) {
    const angle = getAngleOfThreePoints(points[i - 1], points[i], points[i + 1]) / 2;
    tempLen += mathDistance(points[i - 1], points[i]);
    const w = (tailWidth / 2 - (tempLen / allLen) * widthDif) / Math.sin(angle);
    const left = getThirdPoint(points[i - 1], points[i], Math.PI - angle, w, true);
    const right = getThirdPoint(points[i - 1], points[i], angle, w, false);
    leftBodyCoords.push(left);
    rightBodyCoords.push(right);
  }
  return [...leftBodyCoords, ...rightBodyCoords];
}
