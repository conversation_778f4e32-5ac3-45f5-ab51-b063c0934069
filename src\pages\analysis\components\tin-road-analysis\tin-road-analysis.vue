<!-- TIN地形与道路 -->
<script lang="ts" setup>
import { toStaticFilePath } from '@/utils/resolve-path';
import { CzEntity, CzPlotScheme, PRESET_PLOT_SCHEMES_OPTIONS } from '@x3d/all';
import { useCzEntity, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'TinRoadAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});

const viewer = useCzViewer();

const { entity } = useCzEntity(() => {
  return new CzEntity({
    position: Cesium.Cartesian3.fromDegrees(110.398594, 20.038757),
    model: {
      uri: toStaticFilePath(`/common-glb/road_design.glb`),
    },
  });
});
watchEffect(() => {
  entity.value && viewer.value.flyTo(entity.value);
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="TIN地形与道路"
    class="w-400px"
    @close="emits('close')"
  >
    <!--  -->
  </drag-card>
</template>
