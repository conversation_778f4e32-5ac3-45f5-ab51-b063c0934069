<!-- 巡查抽查 -->
<script lang="ts" setup>
import FullSreenArea from './full-sreen-area.vue';
import SpotCheckGOVFeedback from './spot-check-gov-feedback.vue';
import <PERSON><PERSON>heckGOV from './spot-check-gov.vue';

defineOptions({ name: 'SpotCheckModal' });

const tab = ref('SpotCheckGOV');
</script>

<template>
  <FullSreenArea class="flex flex-col items-stretch">
    <el-tabs v-model="tab" type="card" class="demo-tabs">
      <el-tab-pane label="政府巡查" name="SpotCheckGOV">
        <SpotCheckGOV />
      </el-tab-pane>
      <el-tab-pane label="政府巡查反馈" name="SpotCheckGOVFeedback">
        <SpotCheckGOVFeedback />
      </el-tab-pane>
    </el-tabs>
  </FullSreenArea>
</template>

<style scoped lang="scss">
  .el-tabs {
  overflow: hidden;

  :deep(.el-tabs__content) {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
  }

  :deep(.el-tab-pane) {
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
  }
}
</style>
