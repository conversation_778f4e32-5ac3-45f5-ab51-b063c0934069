import type { AxiosRequestConfig } from 'axios';

import axios from 'axios';

const _axios = axios.create({});

export async function webServiceRequest<T = any>(config: AxiosRequestConfig) {
  const { data } = await _axios.request({
    baseURL: WebService.instance.baseURL,
    ...config,
  });
  return data as T;
}

/** AMap服务单例 */
export class WebService {
  private constructor() { }

  private static _instance: WebService;

  static get instance() {
    return (WebService._instance ??= new WebService());
  }

  private _key = '';

  get key() {
    return this._key;
  }

  private _baseURL = import.meta.env.VITE_AMAP_URL;

  get baseURL() {
    return this._baseURL;
  }

  /**
   * 注册AMap WEB 服务
   * @param key
   * @param url
   */
  register(key: string, baseURL?: string) {
    this._key = key;
    baseURL && (this._baseURL = baseURL);
  }
}
