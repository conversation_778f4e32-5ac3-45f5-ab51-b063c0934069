import type { Coord, Feature, LineString, Point } from '@turf/turf';

import { lineString } from '@turf/turf';
import { getArrowBodyPoints, getArrowHeadPoints } from './attack-arrow';
import { getBaseLength, getBatchCoords, getQBSplinePoints, getThirdPoint, HALF_PI } from './common';

export interface TailedSquadCombatOptions {
  headHeightFactor: number;
  headWidthFactor: number;
  neckHeightFactor: number;
  neckWidthFactor: number;
  tailWidthFactor: number;
  swallowTailFactor: number;
  headTailFactor: number;
}

/**
 * 燕尾直箭头
 */
export function tailStraightArrow(
  points: Coord[],
  options?: TailedSquadCombatOptions,
): Feature<LineString> {
  if (points.length < 2) {
    throw new Error('points.length must >=2');
  }
  const headHeightFactor = options?.headHeightFactor ?? 0.18;
  const headWidthFactor = options?.headWidthFactor ?? 0.3;
  const neckHeightFactor = options?.neckHeightFactor ?? 0.85;
  const neckWidthFactor = options?.neckWidthFactor ?? 0.15;
  const tailWidthFactor = options?.tailWidthFactor ?? 0.1;
  const swallowTailFactor = options?.swallowTailFactor ?? 1;
  const headTailFactor = options?.headTailFactor ?? 0.8;
  const _options = {
    headHeightFactor,
    headWidthFactor,
    neckHeightFactor,
    neckWidthFactor,
    tailWidthFactor,
    swallowTailFactor,
    headTailFactor,
  };

  function getTailPoints(points: Coord[]): Feature<Point>[] {
    const allLen = getBaseLength(points);
    const tailWidth = allLen * tailWidthFactor;
    const tailLeft = getThirdPoint(points[1], points[0], HALF_PI, tailWidth, false);
    const tailRight = getThirdPoint(points[1], points[0], HALF_PI, tailWidth, true);
    const len = tailWidth * swallowTailFactor;
    const swallowTailPoint = getThirdPoint(points[1], points[0], 0, len, true);
    return [tailLeft, swallowTailPoint, tailRight];
  }

  const tailCoords = getTailPoints(points);
  const headCoords = getArrowHeadPoints(points, tailCoords[0], tailCoords[2], _options);
  const neckLeft = headCoords[0];
  const neckRight = headCoords[4];
  const bodyCoords = getArrowBodyPoints(points, neckLeft, neckRight, tailWidthFactor);
  const count = bodyCoords.length;
  let leftCoords = [tailCoords[0]].concat(bodyCoords.slice(0, count / 2));
  leftCoords.push(neckLeft);
  let rightCoords = [tailCoords[2]].concat(bodyCoords.slice(count / 2, count));
  rightCoords.push(neckRight);
  leftCoords = getQBSplinePoints(leftCoords);
  rightCoords = getQBSplinePoints(rightCoords);
  const coords = [
    ...leftCoords,
    ...headCoords,
    ...rightCoords.reverse(),
    tailCoords[1],
    leftCoords[0],
  ];
  return lineString(getBatchCoords(coords));
}
