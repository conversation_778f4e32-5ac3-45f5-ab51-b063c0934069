import * as Cesium from 'cesium';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface NearFarScalarSerializateJSON {
  near?: number;
  nearValue?: number;
  far?: number;
  farValue?: number;
}

export type NearFarScalarKey = keyof NearFarScalarSerializateJSON;

export class NearFarScalarSerializate {
  private constructor() {}
  static toJSON(
    data?: Cesium.NearFarScalar,
    time?: Cesium.JulianDate,
  ): NearFarScalarSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      near: getValue('near'),
      nearValue: getValue('nearValue'),
      far: getValue('far'),
      farValue: getValue('farValue'),
    };
  }

  static fromJSON(json?: NearFarScalarSerializateJSON): Cesium.NearFarScalar | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json);

    return new Cesium.NearFarScalar(
      getValue('near'),
      getValue('nearValue'),
      getValue('far'),
      getValue('farValue'),
    );
  }
}
