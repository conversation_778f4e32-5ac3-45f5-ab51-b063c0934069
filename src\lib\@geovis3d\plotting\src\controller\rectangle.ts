import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';

import * as Cesium from 'cesium';

/**
 * rectangle
 */
export default <PlottingControllerOptions>{
  type: 'rectangle',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
  center: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  update(entity) {
    if (!entity.rectangle) {
      entity.rectangle = new Cesium.RectangleGraphics({
        material: Cesium.Color.RED,
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    if (positions.length < 2) {
      entity._cache = null;
      return;
    }
    entity._cache = Cesium.Rectangle.fromCartesianArray(positions ?? []);
    entity.rectangle!.coordinates = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
