{"code": 200, "data": {"records": [{"id": "1", "operationEvent": "用户登录", "operationTime": "2023-11-15T10:30:25", "userName": "<PERSON>", "apiPath": "/api/auth/login"}, {"id": "2", "operationEvent": "数据查询", "operationTime": "2023-11-14T09:45:13", "userName": "<PERSON>", "apiPath": "/api/data/query"}, {"id": "3", "operationEvent": "文件上传", "operationTime": "2023-11-13T14:22:36", "userName": "<PERSON>", "apiPath": "/api/file/upload"}, {"id": "4", "operationEvent": "用户注销", "operationTime": "2023-11-12T11:08:47", "userName": "<PERSON>", "apiPath": "/api/auth/logout"}, {"id": "5", "operationEvent": "权限修改", "operationTime": "2023-11-11T16:30:02", "userName": "Ana", "apiPath": "/api/permissions"}, {"id": "6", "operationEvent": "密码重置", "operationTime": "2023-11-10T08:15:54", "userName": "<PERSON><PERSON>", "apiPath": "/api/user/reset"}, {"id": "7", "operationEvent": "系统配置更新", "operationTime": "2023-11-09T13:42:19", "userName": "Synge", "apiPath": "/api/settings"}, {"id": "8", "operationEvent": "用户登录", "operationTime": "2023-11-08T10:05:33", "userName": "<PERSON><PERSON>", "apiPath": "/api/auth/login"}, {"id": "9", "operationEvent": "数据导出", "operationTime": "2023-11-07T15:37:21", "userName": "Fadel", "apiPath": "/api/data/export"}, {"id": "10", "operationEvent": "用户创建", "operationTime": "2023-11-06T09:22:48", "userName": "<PERSON>", "apiPath": "/api/user/create"}, {"id": "11", "operationEvent": "角色分配", "operationTime": "2023-11-05T14:18:36", "userName": "Stroman", "apiPath": "/api/roles/assign"}, {"id": "12", "operationEvent": "文件删除", "operationTime": "2023-11-04T11:52:07", "userName": "<PERSON><PERSON>", "apiPath": "/api/file/delete"}, {"id": "13", "operationEvent": "系统备份", "operationTime": "2023-11-03T16:28:42", "userName": "<PERSON>", "apiPath": "/api/system/backup"}, {"id": "14", "operationEvent": "用户封禁", "operationTime": "2023-11-02T10:14:23", "userName": "<PERSON>", "apiPath": "/api/user/ban"}, {"id": "15", "operationEvent": "用户登录", "operationTime": "2023-11-01T13:45:59", "userName": "Trantow", "apiPath": "/api/auth/login"}, {"id": "16", "operationEvent": "用户注销", "operationTime": "2023-10-31T09:32:18", "userName": "Basket", "apiPath": "/api/auth/logout"}, {"id": "17", "operationEvent": "数据查询", "operationTime": "2023-10-30T14:57:44", "userName": "<PERSON><PERSON>", "apiPath": "/api/data/query"}, {"id": "18", "operationEvent": "文件下载", "operationTime": "2023-10-29T11:23:05", "userName": "<PERSON>", "apiPath": "/api/file/download"}, {"id": "19", "operationEvent": "系统设置修改", "operationTime": "2023-10-28T16:08:32", "userName": "<PERSON><PERSON><PERSON>", "apiPath": "/api/settings/update"}, {"id": "20", "operationEvent": "文件上传", "operationTime": "2023-10-27T10:44:17", "userName": "Li", "apiPath": "/api/file/upload"}], "total": 20, "size": 10, "current": 1, "orders": [], "optimizeCountSql": true, "searchCount": true, "maxLimit": null, "countId": null, "pages": 2}, "msg": "操作成功"}