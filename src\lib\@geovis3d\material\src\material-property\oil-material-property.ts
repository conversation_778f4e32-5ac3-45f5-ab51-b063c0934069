import * as Cesium from 'cesium';

import image from './assets/oil.jpg';
import { getCesiumMaterialCache, setCesiumMaterialCache } from './material-cache';

/**
 * 溢油
 */
export class OilMaterialProperty implements Cesium.MaterialProperty {
  constructor() {}

  static readonly MaterialType = 'OilMaterial';

  getType(_time?: Cesium.JulianDate) {
    return OilMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: any) {
    result ??= {};

    return result;
  }

  equals(other?: OilMaterialProperty) {
    return other instanceof OilMaterialProperty;
  }
}

const WaterMaterial = getCesiumMaterialCache('Water');

setCesiumMaterialCache(OilMaterialProperty.MaterialType, {
  ...WaterMaterial,
  fabric: {
    ...WaterMaterial.fabric,
    type: 'Oil',
    uniforms: {
      ...WaterMaterial.fabric.uniforms,
      normalMap: image,
      frequency: 100,
      animationSpeed: 0.01,
      amplitude: 1,
    },
  },
  translucent: () => true,
});
