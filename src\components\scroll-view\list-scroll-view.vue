<!--
 列表滚动视图
 继承于 ElScrollbar
 传入default slot 会传出列表项数据
  -->
<script lang="ts" setup>
import { ElScrollbar } from 'element-plus';

defineOptions({ name: 'ListScrollView', extends: ElScrollbar });
defineProps<{
  empty?: boolean;
  loading?: boolean;
  list?: Record<string, any>[];
  nodeKey: string;
}>();

const scrollbarRef = shallowRef<InstanceType<typeof ElScrollbar>>();

const vm = getCurrentInstance();
const attrs = computed(() => {
  const { empty, loading, list, nodeKey, ...other } = vm?.vnode?.props ?? {};
  return other as any;
});
</script>

<template>
  <ElScrollbar ref="scrollbarRef" v-loading="loading" class="list-scroll-view" v-bind="attrs">
    <slot name="header" />
    <TransitionGroup class="overflow-hidden" name="list" tag="div">
      <div
        v-for="(data, _index) in list"
        :key="data[nodeKey]"
        :style="{
          transitionDelay: `${_index * 50}ms`,
        }"
      >
        <slot :data="data" :index="_index" />
      </div>
    </TransitionGroup>
    <slot v-if="!loading && empty" name="empty">
      <el-empty />
    </slot>

    <slot name="footer" />
  </ElScrollbar>
</template>

<style lang="scss">
  .list-scroll-view {
  .list-move,
  .list-enter-active,
  .list-leave-active {
    transition: all 0.3s ease;
  }

  .list-enter-from,
  .list-leave-to {
    opacity: 0;
    transform: translateX(-30px);
  }

  .default-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
  }

  /* 确保将离开的元素从布局流中删除
  以便能够正确地计算移动的动画。 */
  .list-leave-active {
    display: none;
  }
}
</style>
