import type { ParticleSystemConstructorOptions } from '@/lib/@geovis3d/core';
import { GcParticleSystem } from '@/lib/@geovis3d/core';
import * as Cesium from 'cesium';

import image from './assets/water.png';

/**
 * 喷雾粒子特效系统构造函数参数
 */
export type SprayParticleSystemConstructorOptions = Omit<ParticleSystemConstructorOptions, 'image'>;

/**
 * 喷雾特效粒子系统
 */
export class SprayParticleSystem extends GcParticleSystem {
  constructor(options?: SprayParticleSystemConstructorOptions) {
    super({
      ...options,
      image,
      startColor: options?.startColor ?? Cesium.Color.RED.withAlpha(0.7),
      endColor: options?.endColor ?? Cesium.Color.YELLOW.withAlpha(0.3),
      startScale: options?.startScale ?? 0,
      endScale: options?.endScale ?? 10,
      minimumParticleLife: options?.minimumParticleLife ?? 1,
      maximumParticleLife: options?.maximumParticleLife ?? 6,
      minimumSpeed: options?.minimumSpeed ?? 1,
      maximumSpeed: options?.maximumSpeed ?? 4,
      imageSize: options?.imageSize ?? new Cesium.Cartesian2(25, 25),
      emissionRate: options?.emissionRate ?? 5,
      lifetime: options?.lifetime ?? 16,
      sizeInMeters: options?.sizeInMeters ?? true,
      emitter: options?.emitter ?? new Cesium.CircleEmitter(2),
      updateCallback(p, dt) {
        const gravityScratch = new Cesium.Cartesian3();
        const position = p.position;
        Cesium.Cartesian3.normalize(position, gravityScratch);
        Cesium.Cartesian3.fromElements(20 * dt, gravityScratch.y * dt, -30 * dt, gravityScratch);
        p.velocity = Cesium.Cartesian3.add(p.velocity, gravityScratch, p.velocity);
        options?.updateCallback?.(p, dt);
      },
    });
  }
}
