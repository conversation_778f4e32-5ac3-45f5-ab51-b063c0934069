<script lang="ts" setup>
import type { FormInstance } from 'element-plus';
import { useUserStore } from '@/stores/user';

import { promiseTimeout } from '@vueuse/shared';

defineOptions({ name: 'Login' });

const userStore = useUserStore();

const formRef = shallowRef<FormInstance>();

const form = reactive({
  principal: '',
  password: '',
});

const rules = ref({
  principal: [{ required: true, message: '请输入账号' }],
  password: [{ required: true, message: '请输入密码' }],
});

const { execute, isLoading } = useAsyncState(
  async () => {
    await formRef.value!.validate();
    const { code } = await userStore.login(form.principal, form.password);
    if (code === 200) {
      ElMessage.success('登录成功');
    }
    else {
      ElMessage.error('登录失败');
    }
  },
  null,
  {
    immediate: false,
  },
);

watchEffect(async () => {
  if (userStore.isLogined) {
    await promiseTimeout(2000);
    location.href = location.href.replace(location.hash, '');
  }
});
</script>

<template>
  <div class="login-container">
    <div class="layout-header">
      <div flex="~ 1" />
      <div text="28px" class="MiSans-Semibold" lh="1px">
        三维数据处理系统
      </div>
      <div flex="~ 1 justify-end" />
    </div>
    <div class="input-container" flex="~ col" p="x-44px y-56px">
      <div text="#E2F4FF 30px" font="bold" flex="~ justify-center">
        欢迎登录
      </div>
      <div
        class="relative lh-30px"
        p="b-30px t-20px"
        text="#E2F4FF 22px"
        flex="~ justify-center"
        un-before="absolute h-1px w-44px bg-#95B6BC content-empty translate-x--120px translate-y-15px"
        un-after="absolute h-1px w-44px bg-#95B6BC content-empty translate-x-120px  translate-y-15px"
      >
        welcom to login
      </div>
      <el-form ref="formRef" :rules="rules" :model="form">
        <el-form-item mb="40px!">
          <el-input v-model="form.principal" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item mb="12px!">
          <el-input
            v-model="form.password"
            placeholder="请输入密码"
            type="password"
            show-password
          />
        </el-form-item>
        <div>
          <el-checkbox
            class="[--el-checkbox-checked-text-color:#CCEDFF]! [--el-checkbox-font-size:16px]!"
          >
            记住密码
          </el-checkbox>
        </div>
      </el-form>
      <el-button
        mt="94px!"
        bolck
        type="primary"
        h="62px!"
        rd="31px!"
        un-text="24px!"
        :loading="isLoading"
        @click="() => execute()"
      >
        登录
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.el-input {
  --el-input-height: 62px;
  --el-input-bg-color: #2a77994d;
  --el-input-border-color: none;

  font-size: 22px;
}

.login-container {
  width: 100vw;
  height: 100vh;
  background-image: url('./assets/bg.png');
  background-size: cover;
}

.layout-header {
  position: absolute;
  top: -2px;
  right: 0;
  left: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 87px;
  background-image: url('@/layout/assets/header.png');
  background-size: 100% 100%;
}

.input-container {
  position: absolute;
  top: 50%;
  right: 6%;
  width: 500px;
  height: 632px;
  background: rgb(78 186 255 / 22%);
  border: 1px solid #507fc0;
  border-radius: 20px;
  box-shadow: inset 0 0 6px 2px rgb(115 251 255 / 20%);
  transform: translateY(-50%);
}
</style>
