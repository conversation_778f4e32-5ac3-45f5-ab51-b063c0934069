<script lang="ts" setup>
import { activateLayerType, resetLayerState } from '@/components/time-service-layer/state';
import { useCzDataSource, useCzImageryLayer } from '@x3d/vue-hooks';
import { FIRE_FACTOR_LAYERS } from './layers';
import RightRealtimeWarning from './right-realtime-warning.vue';
import RightStationList from './right-station-list.vue';
import RightWarningStat from './right-warning-stat.vue';

defineOptions({ name: 'Right' });

const tabs = [
  {
    name: '站点火险等级排名',
    component: RightStationList,
  },
  {
    name: '火险预测预警报告',
    component: RightWarningStat,
  },
  {
    name: '站点实时预警信息',
    component: RightRealtimeWarning,
  },
];
const current = ref(tabs[0].name);

const currentLayer = ref<string>();

// 图层类型与时序图层类型的映射
const layerTypeMapping: Record<string, string | null> = {
  火险分级: 'fire_level',
  林地植被种类: 'fire_class',
  可燃物含水率: 'Moisture',
  // 其他图层类型不关联时序图层
  凋落物含水率: null,
  土壤含水率: null,
  地表温度: null,
  地表湿度: null,
  光照度: null,
  日累计降水量: null,
};

// 点击图层切换处理
function handleLayerClick(layerName: string) {
  if (currentLayer.value === layerName) {
    // 再次点击同一图层时，取消选择
    currentLayer.value = undefined;
    // 重置时序图层状态
    resetLayerState();
    return;
  }

  currentLayer.value = layerName;

  // 查找对应的时序图层类型
  const timeSeriesType = layerTypeMapping[layerName];

  if (timeSeriesType) {
    // 如果有对应的时序图层类型，激活时间轴
    activateLayerType(timeSeriesType);
  }
  else {
    // 没有对应的时序图层类型，重置时序图层状态
    resetLayerState();
  }
}

const currentConfig = computed(() => {
  return FIRE_FACTOR_LAYERS.find(e => e.name === currentLayer.value);
});

const imageryLayer = computedAsync(async () => {
  if (currentLayer.value) {
    const layer = await currentConfig.value?.getLayer?.();
    return layer;
  }
  else {
    return undefined;
  }
}, undefined);

useCzImageryLayer(imageryLayer);

const dataSource = computedAsync(async () => {
  if (currentLayer.value) {
    const dataSource = await currentConfig.value?.getDataSource?.();
    return dataSource;
  }
  else {
    return undefined;
  }
}, undefined);

useCzDataSource(dataSource);
</script>

<template>
  <div
    of="y-hidden"
    p="t-60px b-40px"
    flex="~ justify-start"
    pointer-events="none!"
    select-none
    un-children="pointer-events-initial"
  >
    <div flex="~ col items-end">
      <div
        v-for="item in tabs"
        :key="item.name"
        flex="~ col justify-center items-center"
        text="14px"
        :bg="current !== item.name ? `[var(--el-bg-color)]` : '[var(--el-color-primary)]'"
        size="70px"
        cursor="pointer"
        b-b="1px"
        b-r="1px"
        b-l="1px"
        b-color="#fff/20"
        @click="current = item.name"
      >
        <span> {{ item.name.slice(0, item.name.length - 4) }}</span>
        <span> {{ item.name.slice(item.name.length - 4, item.name.length) }}</span>
      </div>
      <div
        v-for="item in FIRE_FACTOR_LAYERS"
        :key="item.name"
        flex="~ col justify-center items-center"
        text="14px"
        size="70px"
        :bg="currentLayer !== item.name ? `[var(--el-bg-color)]` : '[var(--el-color-primary)]'"
        cursor="pointer"
        b-b="1px"
        b-r="1px"
        b-l="1px"
        b-color="#fff/20"
        @click="handleLayerClick(item.name)"
      >
        <span text="center"> {{ item.name }}</span>
      </div>
    </div>
    <div w="400px" bg="[var(--el-bg-color)]">
      <template v-for="item in tabs" :key="item.name">
        <div v-show="item.name === current" w="100%!">
          <component :is="item.component" />
        </div>
      </template>
    </div>

    <!-- 图例 -->
    <teleport v-if="currentConfig?.legendName" to="body">
      <div
        position="fixed z-10 right-500px bottom-60px"
        bg="[var(--el-bg-color)]"
        rd="6px"
        b="1px"
        b-color="#fff/10"
        min-w="140px"
        text="14px"
        pb="10px"
      >
        <header-title1 p="x-10px! y-6px!">
          <span text="16px">{{ currentConfig?.legendName }}</span>
        </header-title1>
        <el-divider m="0!" />
        <div v-for="item in currentConfig.legends" :key="item.join('')" flex="~ items-center" p="x-10px t-4px">
          <div w="20px" h="10px" :style="{ background: item[1] }" mr="4px" />
          <span>{{ item[0] }}</span>
        </div>
      </div>
    </teleport>
  </div>
</template>
