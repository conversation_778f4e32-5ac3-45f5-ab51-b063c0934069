<!-- PlaneGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { PlaneGraphicsKey, PlaneGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { PlaneGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian2Attribute from './cartesian2-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import PlaneAttribute from './plane-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'PlaneGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: PlaneGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.PlaneGraphics, PlaneGraphicsSerializateJSON>({
  graphic: () => props.entity?.plane,
  omit: props.omit,
  toJSON: (graphics, omit) => PlaneGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => PlaneGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="plane"
    graphics-field="show"
    label="可见"
  />
  <PlaneAttribute
    v-if="!hide?.includes('plane')"
    v-model="model.plane"
    graphics="plane"
    graphics-field="plane"
    label="平面"
  />
  <Cartesian2Attribute
    v-if="!hide?.includes('dimensions')"
    v-model="model.dimensions"
    graphics="plane"
    graphics-field="dimensions"
    label="尺寸"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="plane"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="plane"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="plane"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="plane"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="plane"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="plane"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="plane"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
