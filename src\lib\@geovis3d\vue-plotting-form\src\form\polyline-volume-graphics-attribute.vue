<!-- PolylineVolumeGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type {
  PolylineVolumeGraphicsKey,
  PolylineVolumeGraphicsSerializateJSON,
} from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { PolylineVolumeGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian2ArrayAttribute from './cartesian2-array-attribute.vue';
import Cartesian3ArrayAttribute from './cartesian3-array-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import CornerTypeAttribute from './corner-type-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'PolylineVolumeGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: PolylineVolumeGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<
  Cesium.PolylineVolumeGraphics,
  PolylineVolumeGraphicsSerializateJSON
>({
  graphic: () => props.entity?.polylineVolume,
  omit: props.omit,
  toJSON: (graphics, omit) => PolylineVolumeGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => PolylineVolumeGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="polylineVolume"
    graphics-field="show"
    label="可见"
  />
  <Cartesian3ArrayAttribute
    v-if="!hide?.includes('positions')"
    v-model="model.positions"
    graphics="polylineVolume"
    graphics-field="positions"
    label="positions"
  />
  <Cartesian2ArrayAttribute
    v-if="!hide?.includes('shape')"
    v-model="model.shape"
    graphics="polylineVolume"
    graphics-field="shape"
    label="形状"
  />
  <CornerTypeAttribute
    v-if="!hide?.includes('cornerType')"
    v-model="model.cornerType"
    graphics="polylineVolume"
    graphics-field="cornerType"
    label="拐角类型"
  />
  <NumberAttribute
    v-if="!hide?.includes('granularity')"
    v-model="model.granularity"
    graphics="polylineVolume"
    graphics-field="granularity"
    label="渲染粒度"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="polylineVolume"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="polylineVolume"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="polylineVolume"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="polylineVolume"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="polylineVolume"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="polylineVolume"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="polylineVolume"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
