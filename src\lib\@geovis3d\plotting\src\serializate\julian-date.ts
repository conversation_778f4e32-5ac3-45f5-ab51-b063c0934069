import * as Cesium from 'cesium';

export type JulianDateSerializateJSON = string;

export class JulianDateSerializate {
  private constructor() {}

  static toJSON(data?: Cesium.JulianDate): JulianDateSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }

    return Cesium.JulianDate.toIso8601(data);
  }

  static fromJSON(json?: JulianDateSerializateJSON): Cesium.JulianDate | undefined {
    if (!json) {
      return undefined;
    }
    return Cesium.JulianDate.fromIso8601(json);
  }
}
