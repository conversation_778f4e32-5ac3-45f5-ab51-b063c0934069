<script lang="ts" setup>
import { usePromiseModal } from '@/hooks/use-promise-modal';
import { FIRE_FACTOR_BASE_URL } from '../api';

defineOptions({ name: 'LeftReportDialog' });

const data = shallowRef<any>();

const { isActive, open, completed, forceClose } = usePromiseModal<void>(
  (r: any) => {
    data.value = r;
  },
);

defineExpose({
  open,
  forceClose,
  completed,
});
</script>

<template>
  <el-dialog :model-value="isActive" destroy-on-close title="评估报告" w="800px!" @close="forceClose()">
    <div v-if="data.type === '5'">
      <div text="24px center" font="bold">
        {{ data?.name }}
      </div>
      <div text="14px center">
        {{ data?.reporttime }}
      </div>
      <el-divider my="20px!" />
      <div h="300px" text="18px" v-html="data?.content" />
    </div>
    <div v-else flex="~ col justify-center items-center">
      <el-image h="500px!" w="725px!" :src="`${FIRE_FACTOR_BASE_URL}/imgfile${data?.content}`" />
    </div>
  </el-dialog>
</template>
