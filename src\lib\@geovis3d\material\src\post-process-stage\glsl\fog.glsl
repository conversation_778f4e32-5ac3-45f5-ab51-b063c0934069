uniform sampler2D colorTexture;
uniform sampler2D depthTexture;
uniform float potency;
in vec2 v_textureCoordinates;

out vec4 vFragColor;

//雾场景
void main(void) {
    vec4 origcolor = texture(colorTexture, v_textureCoordinates);
    vec4 fogcolor = vec4(0.8, 0.8, 0.8, 0.5);
    vec4 depthcolor = texture(depthTexture, v_textureCoordinates);
    float f = (depthcolor.r - 0.22) / ((1.0 - potency) * 10.0);
    if(f < 0.0)
        f = 0.0;
    else if(f > 1.0)
        f = 1.0;
    vFragColor = mix(origcolor, fogcolor, f);
}
