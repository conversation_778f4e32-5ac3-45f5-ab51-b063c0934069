<script setup lang="ts">
import { opLogListPageUsingPost } from '@/genapi/cimapi';
import { computedLoading } from '@/hooks/computed-loading';
import dayjs from 'dayjs';
// 导入模拟数据
const currentItem = ref<any>(null);
const dateRange = ref([]);

const visibleDrawer = ref(false);
const pagination = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
  size: 'default',
  background: false,
});

/**
 * 序号
 * @param index 序号
 */
const indexMethod = (index: number) => index + 1;

const [taskTableData, isLoading] = computedLoading(async () => {
  // 实际项目中应使用API调用
  const { code, data } = await opLogListPageUsingPost({
    data: {
      current: pagination.currentPage,
      size: pagination.pageSize,
      extra: {},
      orderConfigList: [],
      query: {
        startTime: dateRange.value && dateRange.value[0] ? dateRange.value[0] : undefined,
        endTime: dateRange.value && dateRange.value[1] ? dateRange.value[1] : undefined,
      },
    },
  });
  if (code === 200) {
    pagination.total = Number(data?.total) || 0;
    return data?.records || [];
  }
  pagination.total = Number(data?.total) || 0;
  return [];
});
function handleSizeChange(val: number) {
  pagination.pageSize = val;
}
function handleCurrentChange(val: number) {
  pagination.currentPage = val;
}

function handleSearch() {
}

function handleDetail(row: any) {
  currentItem.value = row;
  visibleDrawer.value = true;
}

function cancelClick() {
  visibleDrawer.value = false;
}

function _handleCopy(row: any) {
  console.warn(row);
}
</script>

<template>
  <div class="details-main">
    <div class="convert-title">
      系统日志
    </div>
    <div class="search">
      <div>
        <span class="text-14px color-#ffffffD9">筛选条件：</span>
        <el-date-picker
          v-model="dateRange"
          class="ml-10px"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="handleSearch"
        />
      </div>
    </div>
    <div class="table-content">
      <el-table v-loading="isLoading" height="100%" :stripe="true" :data="taskTableData" @row-click="handleDetail">
        <el-table-column type="index" :index="indexMethod" label="序号" width="100" align="center" />
        <el-table-column prop="opEvent" label="操作事件" />
        <el-table-column prop="opPort" label="操作接口" width="240" sortable />
        <el-table-column prop="opTime" label="操作日期" width="200" sortable />
        <el-table-column prop="opUser" label="操作用户" width="150" sortable />
        <el-table-column prop="handleTime" label="请求耗时(ms)" width="120" sortable />
      </el-table>
    </div>
  </div>
  <div class="details-footer">
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes"
        :background="pagination.background"
        layout="sizes, prev, pager, next"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>

  <el-drawer v-model="visibleDrawer" direction="rtl">
    <template #header>
      <h4 class="text-20px color-#ffffffD9">
        日志详情
      </h4>
    </template>
    <template #default>
      <div v-if="currentItem" class="detail-container">
        <el-descriptions :column="1" stripe border :label-width="$vh(150)">
          <el-descriptions-item label="ID">
            {{ currentItem.id }}
          </el-descriptions-item>
          <el-descriptions-item label="操作事件">
            {{ currentItem.opEvent }}
          </el-descriptions-item>
          <el-descriptions-item label="操作接口">
            {{ currentItem.opPort }}
          </el-descriptions-item>
          <el-descriptions-item label="操作日期">
            {{ dayjs(currentItem.opTime).format('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
          <el-descriptions-item label="操作用户">
            {{ currentItem.opUser }}
          </el-descriptions-item>
          <el-descriptions-item label="请求耗时(ms)">
            {{ currentItem.handleTime }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="入参">
            {{ currentItem.opParam }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="出参">
            {{ currentItem.opResult || '' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancelClick">
          关闭
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped lang="scss">
.details-main {
  position: relative;
  width: 100%;
  height: calc(100% - 70px);
  padding: 20px 350px 20px 250px;
}

.details-footer {
  display: flex;
  align-items: center;
  height: 70px;
  padding: 0 200px 0 300px;
  border-top: 1px solid #2f3238;
}

.convert-title {
  position: relative;
  height: 50px;
  font-size: 20px;
  font-weight: 600;
  line-height: 50px;
  color: #ffffffd9;
  text-indent: 15px;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    width: 5px;
    height: 20px;
    content: '';
    background-color: #4584ff;
    transform: translate(-50%, -50%);
  }
}

.search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  margin: 15px 0;

  // margin-top: 10px;
}

.table-content {
  position: relative;
  box-sizing: border-box;
  height: calc(100% - 150px);
  padding: 10px 0;
}

.detail-container {
  padding: 10px;

  :deep(.el-descriptions__table) {
    table-layout: fixed;
  }

  :deep(.el-descriptions__cell) {
    word-break: normal;
    word-wrap: break-word;
    white-space: normal;
  }
}
</style>
