import type { PlottingController } from './plotting-controller';
import type { SampledPointSymbol } from './plotting-sampleds-property';
import { coordinateToCartesian } from '@/lib/@geovis3d/coordinate';

import { arrayDiff, effectHelper, promiseTimeout, throttle } from '@/lib/@geovis3d/shared';

import * as Cesium from 'cesium';

import { GcEntity } from '../vector';
import { PlottingControllerEntity } from './plotting-controller-entity';

export function plottingSampledCallback(controller: PlottingController) {
  /**
   * 避免双击事件诱发成单击两次的事件
   */
  let doubleClicking = false;

  /**
   * 添加采样点
   *
   * 左键点击回调 、判断是否终止定义态
   * @internal
   */
  const add = async ({ position }: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    await promiseTimeout(1); // 下一事件循环中在执行，避免双击事件诱发成单击两次的事件

    const { scene, sampleds } = controller;
    if (!doubleClicking) {
      // 不是模型标要贴模型
      const mode = controller.entity.model ? 'globePick' : 'auto';
      const cartesian = coordinateToCartesian(position, scene!, mode)!;

      if (cartesian) {
        sampleds.addPosition(cartesian);
      }
    }
  };

  /**
   * 停止定义态,左键双击回调
   * @internal
   */
  const terminate = async () => {
    controller!.sampling = false;
    // 阻止双击事件诱发成单击两次的事件
    doubleClicking = true;
    await promiseTimeout(2);
    doubleClicking = false;
  };

  /**
   * 右键单击回调，回退控制点
   * @internal
   */
  const revocate = async () => {
    const sampleds = controller.sampleds;
    const length = sampleds.getLength();
    if (length) {
      sampleds.removeIndex(length - 1);
    }
  };

  controller.definitionChanged.addEventListener((_, field, value) => {
    if (field == 'sampling') {
      const scene = controller.scene!;
      if (value) {
        scene.geovis3d?.screenEvent.on('LEFT_CLICK', add);
        scene.geovis3d?.screenEvent.on('LEFT_DOUBLE_CLICK', terminate);
        scene.geovis3d?.screenEvent.on('RIGHT_CLICK', revocate);
      }
      else {
        scene.geovis3d?.screenEvent.off('LEFT_CLICK', add);
        scene.geovis3d?.screenEvent.off('LEFT_DOUBLE_CLICK', terminate);
        scene.geovis3d?.screenEvent.off('RIGHT_CLICK', revocate);
      }
    }
  });

  /**
   */
  let intervalDraging = false;

  /**
   * 控制点
   */
  const control = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      if (intervalDraging) {
        return;
      }
      const { entity: parent, sampleds } = controller;

      const positionSymbols = sampleds.getValue();
      const prevIds = entities.map(e => e.id);
      const newIds = positionSymbols.map(e => e.id);
      const firstId = newIds[0];
      const { existed, added, missed } = arrayDiff(prevIds, newIds);
      existed.forEach((id) => {
        const entity = entities.find(e => e.id === id);
        const position = sampleds.getById(id);
        entity && (entity.position = new Cesium.ConstantPositionProperty(position));
      });
      added.forEach((id) => {
        if (id === firstId) {
          return;
        }
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.CONTROL_STYLE,
          id,
          position: sampleds.getById(id),
          autoSetPosition: true,
          parent,
          onPositionChanged: ({ position }) => {
            position && sampleds.setById(id, position);
          },
        });
        entities.push(entity);
        parent.entityCollection.add(entity);
      });

      missed.forEach((id) => {
        parent.entityCollection.removeById(id);
        const index = entities.findIndex(e => e.id === id);
        index != -1 && entities.splice(index, 1);
      });
    };
    return { cleanup, render };
  })();

  /**
   *
   * 间隔点
   */
  const interval = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      if (intervalDraging) {
        return;
      }
      const { entity: parent, sampleds, sampling } = controller;
      cleanup();

      if (sampling) {
        return;
      }

      const positions = sampleds.getPositions();
      positions.forEach((position, index) => {
        if (index === 0) {
          return;
        }
        const prev = positions[index - 1];
        const midpoint = Cesium.Cartesian3.midpoint(prev, position, position.clone());
        let item: SampledPointSymbol;
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.INTERVAL_STYLE,
          position: midpoint,
          autoSetPosition: true,
          parent,
          onPositionChanged: ({ position, draging }) => {
            intervalDraging = draging;
            if (position) {
              item
                ? sampleds.setById(item.id, position)
                : (item = sampleds.addPosition(position, index));
            }
          },
        });
        parent.entityCollection.add(entity);
        entities.push(entity);
      });
    };
    return { cleanup, render };
  })();

  /**
   *
   * 连线
   */
  const line = (() => {
    const entities: GcEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const { entity: parent, sampleds } = controller;
      cleanup();
      const positions = sampleds.getPositions();
      if (controller.sampling && controller.mousePosition) {
        positions.push(controller.mousePosition);
      }
      const entity = new GcEntity({
        polyline: {},
        ...PlottingControllerEntity.SAMPLED_LINE_STYLE,
        parent,
      });
      entity.polyline!.positions = new Cesium.CallbackProperty(() => positions, false);
      parent.entityCollection.add(entity);
      entities.push(entity);
    };
    return { cleanup, render };
  })();

  /**
   * 渲染总线
   * @internal
   */
  const render = throttle(() => {
    const { scene, active, entity } = controller;
    if (active && scene && entity.isMounted) {
      line.render();
      control.render();
      interval.render();
    }
    else {
      line.cleanup();
      control.cleanup();
      interval.cleanup();
    }
  }, 0);

  controller.definitionChanged.addEventListener(render);
  controller.entity.definitionChanged.addEventListener(render);
  controller.sampleds.definitionChanged.addEventListener(render);
  controller.coordinates.definitionChanged.addEventListener(render);

  const [lineListenerExecute, lineListenerDestroy] = effectHelper((onCleanup) => {
    const stop = controller.scene?.postUpdate.addEventListener(() => line.render());
    onCleanup(() => stop?.());
  });

  controller.definitionChanged.addEventListener((_, field, value) => {
    if (field === 'sampling') {
      value ? lineListenerExecute() : lineListenerDestroy();
    }
  });
}
