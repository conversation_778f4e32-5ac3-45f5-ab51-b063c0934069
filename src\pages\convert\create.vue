<script setup lang="ts">
import type { UploadFile, UploadInstance, UploadProps, UploadRawFile } from 'element-plus';
import { changeDataFileListUsingGet, changeDataSaveOrUpdateUsingPost, changeDataUpdateFileListUsingPost } from '@/genapi/cimapi';
import { ArrowDown, Back, CircleCloseFilled, Delete, Document, Edit, Folder, Loading, Plus, UploadFilled } from '@element-plus/icons-vue';
import { promiseTimeout } from '@vueuse/core';
import { genFileId } from 'element-plus';
import { ref } from 'vue';
import { uploadChunk } from './upload-chunk';

const currentTab = inject('currentTab') as Ref<any>;
const dialogTitle = ref('选择存放位置');
const currentSelectedDir = ref<any>(null);
const sourceData = ref({
  style: 'OSGB',
  path: '',
});
const targetData = ref({
  style: '3dtiles',
  path: '',
});
const shpHeight = ref(30);
const taskName = ref('');
const dialogVisible = ref(false);
const uploadType = ref('');
// 文件目录, source: 源文件, target: 目标文件
/**
 * 上传
 */
const fileTableRef = ref<any>(); // 上传表格
const fileTableData = ref<any[]>([]); // 上传数据
const filePath = ref<any[]>(['root']); // 上传路径
const uploadStatus = ref('end'); // 上传状态

/**
 * 进度条
 */
const progress = ref(0);
/**
 * 激活上传弹窗
 * @param type 类型
 */
function uploadFileType(type: string) {
  progress.value = 0;
  uploadType.value = type;
  dialogTitle.value = type === 'source' ? '选择文件' : '选择存放位置';
  if (type === 'source') {
    if (sourceData.value.path) {
      filePath.value = ['root', ...sourceData.value.path.split('/').filter(part => part !== '')];
    }
    else {
      filePath.value = ['root'];
    }
  }
  else if (type === 'target') {
    if (targetData.value.path) {
      filePath.value = ['root', ...targetData.value.path.split('/').filter(part => part !== '')];
    }
    else {
      filePath.value = ['root'];
    }
  }
  getFileTableData();
  dialogVisible.value = true;
}

/**
 * 获取文件列表
 */
async function getFileTableData() {
  const type = uploadType.value;

  // let fileName = '';
  // if (filePath.value.length > 1) {
  //   if (fileType.includes(filePath.value[filePath.value.length - 1].split('.').pop())) {
  //     fileName = filePath.value.slice(1, -1).join('/');
  //   }
  //   else {
  //     fileName = filePath.value.slice(1).join('/');
  //   }
  // }
  const fileName = filePath.value.length > 1
    ? filePath.value.slice(1).join('/')
    : '';

  const params = fileName ? { fileName, type } : { type };
  const { code, data } = await changeDataFileListUsingGet({
    params,
  });
  if (code === 200) {
    fileTableData.value = (data || []) as any;
  }
}

/**
 * 勾选选择目录   同时进行验证
 * @param selection 勾选
 */
async function handleSelectionChange(selection: any) {
  if (!selection.length) {
    currentSelectedDir.value = null;
    return;
  }
  if (selection.length > 1) {
    ElMessage.warning('只能选择一个目录!');
    return;
  }
  currentSelectedDir.value = selection[0];

  const selectedItem = selection[0];
  const path = filePath.value.length > 1
    ? `${filePath.value.slice(1).join('/')}/${selectedItem.fileName}`
    : `/${selectedItem.fileName}`;

  try {
    if (uploadType.value === 'source') {
      sourceData.value.path = path;
    }
    else if (uploadType.value === 'target') {
      targetData.value.path = path;
    }
  }
  catch (error) {
    console.error('Selection handling error:', error);
    fileTableRef.value.clearSelection();
  }
}

/**
 * 是否可勾选
 */
function selectable() {
  return true;
}
// 新增转换任务
async function addConvertTask() {
  if (sourceData.value.path === '' || targetData.value.path === '') {
    ElMessage.warning('请选择源文件和目标目录');
    return;
  }

  try {
    const { code } = await changeDataSaveOrUpdateUsingPost({
      data: {
        changeTask: taskName.value,
        id: '',
        sourceFormat: sourceData.value.style,
        sourcePath: sourceData.value.path,
        status: 0,
        targetFormat: targetData.value.style,
        targetPath: targetData.value.path,
        height: (Number(shpHeight.value) || 30) as any,
      },
    });
    if (code === 200) {
      ElMessage.success('新增转换任务成功');
    }
  }
  catch (error: any) {
    ElMessage.error(error.message);
  }
}

/**
 * 获取更新进度
 */
// async function getUpdateProgress() {
//   const fetchProgress = async () => {
//     try {
//       const { done, bytesRead, contentLength } = await changeDataProgressUsingGet({
//         params: { processId: processId.value },
//       });
//       if (!done) {
//         if (Number(bytesRead) === 0 && Number(contentLength) === 0) {
//           progress.value = 0;
//         }
//         else {
//           progress.value = Math.min(100, Math.max(0, Math.round((Number(bytesRead) / Number(contentLength)) * 100)));
//         }
//       }
//       else {
//         progress.value = 100;
//         uploadStatus.value = 'end';
//         return;
//       }
//       setTimeout(fetchProgress, 500);
//     }
//     catch (error) {
//       console.error('文件上传进度获取错误:', error);
//       ElMessage.error('获取进度失败');
//     }
//   };

//   await fetchProgress();
// }

/**
 * 行点击
 * @param row 数据
 */
function handleRowClick(row: any) {
  if (row.dirOrFile === 'dir') {
    filePath.value.push(row.fileName);
    getFileTableData();
  }
}

/**
 * 点击面包屑的路径 跳转对应目录
 * @param item 路径
 */
function handleClickPath(item: string) {
  const index = filePath.value.indexOf(item);
  if (index !== -1) {
    filePath.value = ['root', ...filePath.value.slice(1, index + 1)];
    getFileTableData();
  }
}

function dialogConfirm() {
  dialogVisible.value = false;
  shpHeight.value = 30;
}

/**
 * 目录操作
 */
async function dirOperationFN(command: string | number | object) {
  if (command === 'add') {
    ElMessageBox.prompt('请输入目录名称', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputErrorMessage: '请输入正确的目录名称',
    })
      .then(async ({ value }) => {
        const { code } = await changeDataUpdateFileListUsingPost({
          data: {
            fileName: `${filePath.value.slice(1).join('/')}/${value}`,
            optType: 'add',
            type: uploadType.value === 'source' ? 'source' : 'target',
            updateName: '',
          },
        });
        if (code === 200) {
          ElMessage.success('新增目录成功');
          getFileTableData();
        }
      });
  }
  else if (command === 'edit') {
    if (!currentSelectedDir.value) {
      ElMessage.warning('请选择要修改的目录');
      return;
    }
    ElMessageBox.prompt('请输入目录名称', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputErrorMessage: '请输入正确的目录名称',
    })
      .then(async ({ value }) => {
        const { code } = await changeDataUpdateFileListUsingPost({
          data: {
            fileName: `${filePath.value.slice(1).join('/')}/${currentSelectedDir.value.fileName}`,
            optType: 'update',
            type: uploadType.value === 'source' ? 'source' : 'target',
            updateName: `${filePath.value.slice(1).join('/')}/${value}`,
          },
        });
        if (code === 200) {
          ElMessage.success('修改目录成功');
          getFileTableData();
        }
      });
  }
  else if (command === 'delete') {
    if (!currentSelectedDir.value) {
      ElMessage.warning('请选择要删除的目录');
      return;
    }

    ElMessageBox.confirm(
      '删除操作无法恢复，是否继续？',
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
      .then(async () => {
        const { code } = await changeDataUpdateFileListUsingPost({
          data: {
            fileName: `${filePath.value.slice(1).join('/')}/${currentSelectedDir.value.fileName}`,
            optType: 'del',
            type: uploadType.value === 'source' ? 'source' : 'target',
            updateName: '',
          },
        });
        if (code === 200) {
          ElMessage.success('删除目录成功');
          getFileTableData();
        }
      });
  }
}

/**
 * 弹窗内容清除
 */
function dialogClear() {
  filePath.value = ['root'];
  fileTableData.value = [];
  fileTableRef.value.clearSelection();
  sourceData.value.path = '';
  targetData.value.path = '';
  shpHeight.value = 30;
  dialogVisible.value = false;
}
function back() {
  currentTab.value = 'details';
}

const dialogUpload = ref(false);
const uploadRef = shallowRef<UploadInstance>();
const uploadFileList = shallowRef<UploadFile[]>([]);

function handleUpload() {
  dialogUpload.value = true;
  uploadFileList.value = [];
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

/**
 * 提交上传
 */
async function submitUpload() {
  uploadStatus.value = 'start';

  await promiseTimeout(1000);
  dialogUpload.value = false;
  progress.value = 0;
  await Promise.all(
    uploadFileList.value.map((item) => {
      return uploadChunk({
        file: item.raw!,
        filename: item.name!,
        uuid: item.uid!,
        uploadFilePath: filePath.value.slice(1).join('/'),
        onUploadProgress(loaded: number, total: number) {
          progress.value = Number.parseInt(`${loaded / total * 100}`);
        },
      });
    }),
  );
  progress.value = 100;

  ElMessage.success('上传成功');
  uploadFileList.value = [];
  uploadRef.value?.clearFiles?.();
  dialogUpload.value = false;
  getFileTableData();
}
</script>

<template>
  <div class="create-back">
    <div @click="back">
      <el-icon size="20">
        <Back />
      </el-icon>
      <span>返回</span>
    </div>
  </div>
  <div class="create-main">
    <div class="create-title">
      <span>倾斜摄影转换参数设置</span>
    </div>
    <div class="form-content">
      <div class="form-item">
        <el-row>
          <el-col :span="8">
            <span class="inline-block w-130px">源数据格式:</span>
            <el-select v-model="sourceData.style" class="" style="width: 250px;" placeholder="请选择源数据格式">
              <el-option label="OSGB" value="OSGB" />
              <el-option label="SHP" value="SHP" />
              <el-option label="GLTF" value="GLTF" />
              <el-option label="B3DM" value="B3DM" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <span class="inline-block w-130px">转换后格式:</span>
            <el-select v-model="targetData.style" class="" style="width: 250px;" placeholder="请选择转换后格式">
              <el-option label="3DTiles" value="3DTiles" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <div class="form-item">
        <span>选择数据:</span>
        <el-button type="primary" class="w-180px" @click="uploadFileType('source')">
          选择文件
        </el-button>
        <span class="ml-30px">
          {{ sourceData.path }}
        </span>
      </div>

      <div v-if="sourceData.style === 'SHP'" class="form-item">
        <span>shape高度:</span>
        <el-input v-model="shpHeight" type="number" style="width: 100px;" placeholder="请输入shape高度" />
        <span class="ml-10px">米</span>
      </div>

      <div class="form-item">
        <span>任务名称:</span>
        <el-input v-model="taskName" style="width: 250px;" placeholder="请输入任务名称" />
      </div>

      <div class="form-item">
        <span>选择存放位置:</span>
        <el-button type="primary" class="w-180px" @click="uploadFileType('target')">
          选择存放位置
        </el-button>
        <span class="ml-30px">
          {{ targetData.path }}
        </span>
      </div>
    </div>
    <div class="form-footer">
      <el-button type="primary" @click="addConvertTask">
        创建任务
      </el-button>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" width="700px" :show-close="false">
    <template #header="{ titleId, titleClass }">
      <div class="w-100% flex items-center justify-between">
        <h6 :id="titleId" :class="titleClass">
          {{ dialogTitle }}
        </h6>
        <div>
          <el-icon class="el-icon--left" @click="dialogClear">
            <CircleCloseFilled />
          </el-icon>
        </div>
      </div>
    </template>
    <div class="dialog-breadcrumb-dropdown">
      <div class="breadcrumb-wrapper">
        <span class="mr-10px">/</span>
        <el-breadcrumb separator="/">
          <template v-for="item in filePath" :key="item">
            <el-breadcrumb-item>
              <span class="path-item" @click="handleClickPath(item)">{{ item }}</span>
            </el-breadcrumb-item>
          </template>
        </el-breadcrumb>
      </div>
      <div class="dropdown-wrapper flex">
        <el-button
          v-if="uploadType === 'source'"
          class="mx-20px"
          type="primary"
          size="small"
          :icon="Plus"
          @click="handleUpload"
        >
          上传
        </el-button>
        <el-dialog v-model="dialogUpload" width="450px" :show-close="true">
          <el-upload
            ref="uploadRef"
            v-model:file-list="uploadFileList"
            :on-exceed="handleExceed"
            :limit="1"
            :auto-upload="false"
            accept=".zip"
            class="source-upload"
            drag
            multiple
          >
            <el-icon class="el-icon--upload">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              拖拽到此处或 <em>点击上传</em>
            </div>
          </el-upload>
          <template #footer>
            <div>
              <el-button type="primary" size="small" @click="submitUpload">
                上传
              </el-button>
              <el-button type="primary" size="small" @click="dialogUpload = false">
                取消
              </el-button>
            </div>
          </template>
        </el-dialog>

        <el-dropdown @command="dirOperationFN">
          <el-button type="primary" size="small">
            目录操作<el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="add" :icon="Plus">
                新增目录
              </el-dropdown-item>
              <el-dropdown-item command="edit" :icon="Edit">
                修改目录
              </el-dropdown-item>
              <el-dropdown-item command="delete" :icon="Delete">
                删除目录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="dialog-content calass">
      <el-table
        ref="fileTableRef"
        class="file-table"
        :data="fileTableData"
        style="width:100%;height: 300px;"
        :stripe="true"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" width="55" :selectable="selectable" />
        <el-table-column prop="fileName" label="文件名" sortable>
          <template #default="{ row }">
            <div v-if="row.dirOrFile === 'dir'" class="flex items-center">
              <el-icon size="18">
                <Folder />
              </el-icon>
              <span class="ml-10px">{{ row.fileName }}</span>
            </div>
            <div v-else class="flex items-center">
              <el-icon size="18">
                <Document />
              </el-icon>
              <span class="ml-10px">{{ row.fileName }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer flex content-between items-center">
        <div class="w-80%">
          <div v-if="progress <= 0 && uploadStatus === 'start'" :span="3">
            <div class="flex items-center">
              <span class="font-size-14px">数据准备中：</span><el-icon class="is-loading font-size-18px color-#4176ff">
                <Loading />
              </el-icon>
            </div>
          </div>
          <div v-if="progress > 0" class="w-50%" :span="6">
            <el-progress :percentage="progress" :status="progress === 100 ? 'success' : 'warning'" :duration="5" />
          </div>
        </div>
        <div class="w-20%">
          <el-button @click="dialogClear">
            取消
          </el-button>
          <el-button type="primary" @click="dialogConfirm">
            确定
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.create-back {
  height: 70px;
  padding: 0 20px;
  font-size: 20px;
  font-weight: 600;
  line-height: 50px;
  color: #ffffffd9;
  border-bottom: 1px solid #2f3238;

  > div {
    display: flex;
    align-items: center;
    height: 100%;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: #4584ff;
    }

    > span {
      margin: 0 15px;
    }
  }
}

.create-main {
  position: relative;
  height: calc(100% - 70px);

  // height: calc(100% - 70px);
  padding: 0 45px 20px;
}

.create-title {
  position: relative;
  height: 50px;
  font-size: 20px;
  font-weight: 600;
  line-height: 50px;
  color: #ffffffd9;
  text-indent: 15px;

  &::before {
    position: absolute;
    top: 50%;
    left: 0;
    display: inline-block;
    width: 5px;
    height: 20px;
    content: '';
    background-color: #4584ff;
    transform: translate(-50%, -50%);
  }
}

.search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.form-content {
  .form-item {
    height: 50px;
    margin-top: 30px;
    font-size: 14px;
    line-height: 50px;
    color: #ffffffd9;

    > span:first-child {
      display: inline-block;
      width: 130px;
    }
  }
}

.form-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  margin: 20px 40px;
  text-align: end;
}

.path-item {
  font-size: 16px;
  color: #ffffffd9;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-bottom: 1px solid #fff;
  }
}

.dialog-breadcrumb-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;

  .breadcrumb-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dropdown-wrapper {
    margin-left: 20px;
  }
}
</style>
