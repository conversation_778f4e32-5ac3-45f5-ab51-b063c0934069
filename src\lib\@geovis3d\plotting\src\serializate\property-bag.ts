import * as Cesium from 'cesium';

export type PropertyBagSerializateJSON = Record<string, any>;

export class PropertyBagSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.PropertyBag,
    time?: Cesium.JulianDate,
  ): PropertyBagSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const result: Record<string, any> = {};

    data.propertyNames.forEach((item) => {
      result[item] = data[item]?.getValue(time);
    });
    return result;
  }

  static fromJSON(json?: PropertyBagSerializateJSON): Cesium.PropertyBag | undefined {
    if (!json) {
      return undefined;
    }
    return new Cesium.PropertyBag(json);
  }
}
