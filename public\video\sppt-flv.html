<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="./jessibuca/jessibuca.js" charset="utf-8"></script>
    <style>
        #dplayer {
            overflow: hidden;
        }

        html,
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            display: inline-block;
            height: 100vh;
            width: 100vw;
        }

        #mse {
            width: inherit !important;
            height: inherit !important;
        }

        #mse.full-screen video {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100vw !important;
            height: calc(100vw * 0.5625) !important;
        }
    </style>
    <script>
        //   AbortController&&  function AbortController() { }

        function initVideo() {
            const options = {
                container: document.querySelector('#mse'),
                autoWasm: true,
                background: "",
                controlAutoHide: false,
                debug: false,
                decoder: "./jessibuca/decoder.js",
                forceNoOffscreen: false,
                hasAudio: true,
                heartTimeout: 5,
                heartTimeoutReplay: true,
                heartTimeoutReplayTimes: 3,
                hiddenAutoPause: false,
                hotKey: true,
                isFlv: true,
                isFullResize: false,
                isNotMute: false,
                isResize: false,
                keepScreenOn: true,
                loadingText: "请稍等, 视频加载中......",
                loadingTimeout: 10,
                loadingTimeoutReplay: true,
                loadingTimeoutReplayTimes: 3,
                openWebglAlignment: false,
                operateBtns: {
                    fullscreen: false,
                    screenshot: false,
                    play: false,
                    audio: false,
                    record: false
                },
                recordType: "mp4",
                rotate: 0,
                showBandwidth: false,
                supportDblclickFullscreen: false,
                timeout: 10,
                useMSE: true,
                useWCS: location.hostname === "localhost" || location.protocol === "https:",
                useWebFullScreen: true,
                videoBuffer: 0.1,
                wasmDecodeErrorReplay: true,
                wcsUseVideoRender: true
            };
            const player = new window.Jessibuca({ ...options });
            player.play(location.search.replace("?url=", "").replace(/\&scaleType\=.*/, ""))
            // let player = new Player({
            //     id: 'mse',
            //     url: location.search.replace("?url=", "").replace(/\&scaleType\=.*/, ""),
            //     // url:"ws://**************:8080/forest?ip=**************&channel=0",
            //     // url: "http://**************:8080/sppt-flv/rtp/46010800012008000001_46010600001321000002.live.flv",
            //     playsinline: true,
            //     isLive: true,
            //     autoplay: true,
            //     autoplayMuted: true,
            //     controls: false,
            //     closeVideoDblclick: true,
            //     closeVideoClick: true,
            //     enableWorker: true,
            //     enableStashBuffer: true, // 启用缓存
            //     stashInitialSize: 2048, // 缓存大小2m
            //     lazyLoad: false,
            //     hasAudio: false,
            //     lazyLoadMaxDuration: 2 * 60,
            //     autoCleanupSourceBuffer: true, // 自动清除缓存
            //     autoCleanupMaxBackwardDuration: 65,
            //     autoCleanupMinBackwardDuration: 60,
            //     plugins: [window.FlvPlayer]
            // });
            // location.href.match('scaleType=1') && document.querySelector("#mse").classList.toggle('full-screen')
        }
    </script>
</head>

<body onload="initVideo()">
    <div id="mse"></div>
</body>

</html>
