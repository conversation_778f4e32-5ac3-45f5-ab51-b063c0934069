<!-- 政府巡查 -->
<script lang="ts" setup>
import { productionHazardousChemicalsGovernmentPatrolUsingGet } from '@/genapi/production';
import { computedLoading } from '@/hooks/computed-loading';

defineOptions({ name: 'SpotCheckGov' });

// 政府巡查
const [data, loading] = computedLoading(async () => {
  const { data } = await productionHazardousChemicalsGovernmentPatrolUsingGet({});
  return data;
});
</script>

<template>
  <el-table v-loading="loading" :data="data" height="100%" stripe>
    <el-table-column label="序号" align="center" type="index" />
    <el-table-column label="行政区划" align="center" prop="regionName" />
    <el-table-column label="系统在线情况" align="center">
      <el-table-column label="在线率" align="center" prop="sysOnlineRate" />
      <el-table-column label="接入数" align="center" prop="sysAccessNum" />
      <el-table-column label="在线数" align="center" prop="sysOnlineNum" />
      <el-table-column label="离线已报备" align="center" prop="sysOfflineNum" />
      <el-table-column label="停产已报备" align="center" prop="sysStopNum" />
      <el-table-column label="未报备" align="center" prop="sysUnreported" />
    </el-table-column>
    <el-table-column label="视频监控在线情况" align="center">
      <el-table-column label="在线率" align="center" prop="videoOnlineRate" />
      <el-table-column label="接入企业" align="center" prop="videoAccessCompanyNum" />
      <el-table-column label="在线企业" align="center" prop="videoOnlineCompanyNum" />
      <el-table-column label="离线已报备" align="center" prop="videoOfflineReported" />
      <el-table-column label="未报备" align="center" prop="videoOfflineUnreported" />
    </el-table-column>
    <el-table-column label="安全承诺情况" align="center">
      <el-table-column label="承诺率" align="center" prop="promiseRate" />
      <el-table-column label="应承诺" align="center" prop="promiseShouldNum" />
      <el-table-column label="已承诺" align="center" prop="promisedNum" />
      <el-table-column label="未承诺" align="center" prop="promiseNotNum" />
    </el-table-column>
    <el-table-column label="超过24小时未销警指标数" align="center" prop="over24NotHandler" />
    <el-table-column label="预警及通报处置情况" align="center">
      <el-table-column label="预警未销警" align="center" prop="warnNotHandler" />
      <el-table-column label="预警未反馈" align="center" prop="warnNotFeedback" />
      <el-table-column label="通报未反馈" align="center" prop="warnNotificationNotFeedback" />
    </el-table-column>
  </el-table>
</template>
