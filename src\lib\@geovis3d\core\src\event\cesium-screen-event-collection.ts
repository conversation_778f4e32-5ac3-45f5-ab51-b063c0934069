import { getKeys } from '@/lib/@geovis3d/shared';
import * as Cesium from 'cesium';

import { CesiumEventCollection, KEYBOARD_EVENT_MODIFIERS } from './cesium-event-collection';

export const SPACE_EVENT_TYPE_MAPER: Record<
  Cesium.ScreenSpaceEventType,
  keyof CesiumScreenEventMapper
> = {
  [Cesium.ScreenSpaceEventType.LEFT_DOWN]: 'LEFT_DOWN',
  [Cesium.ScreenSpaceEventType.LEFT_UP]: 'LEFT_UP',
  [Cesium.ScreenSpaceEventType.LEFT_CLICK]: 'LEFT_CLICK',
  [Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK]: 'LEFT_DOUBLE_CLICK',
  [Cesium.ScreenSpaceEventType.RIGHT_DOWN]: 'RIGHT_DOWN',
  [Cesium.ScreenSpaceEventType.RIGHT_UP]: 'RIGHT_UP',
  [Cesium.ScreenSpaceEventType.RIGHT_CLICK]: 'RIGHT_CLICK',
  [Cesium.ScreenSpaceEventType.MIDDLE_DOWN]: 'MIDDLE_DOWN',
  [Cesium.ScreenSpaceEventType.MIDDLE_UP]: 'MIDDLE_UP',
  [Cesium.ScreenSpaceEventType.MIDDLE_CLICK]: 'MIDDLE_CLICK',
  [Cesium.ScreenSpaceEventType.MOUSE_MOVE]: 'MOUSE_MOVE',
  [Cesium.ScreenSpaceEventType.WHEEL]: 'WHEEL',
  [Cesium.ScreenSpaceEventType.PINCH_START]: 'PINCH_START',
  [Cesium.ScreenSpaceEventType.PINCH_END]: 'PINCH_END',
  [Cesium.ScreenSpaceEventType.PINCH_MOVE]: 'PINCH_MOVE',
};

/**
 * Cesium 屏幕鼠标(手势)事件回调集合
 */
export interface CesiumScreenEventMapper {
  LEFT_DOWN: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  LEFT_UP: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  LEFT_CLICK: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  LEFT_DOUBLE_CLICK: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  RIGHT_DOWN: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  RIGHT_UP: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  RIGHT_CLICK: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  MIDDLE_DOWN: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  MIDDLE_UP: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  MIDDLE_CLICK: Cesium.ScreenSpaceEventHandler.PositionedEventCallback;
  MOUSE_MOVE: Cesium.ScreenSpaceEventHandler.MotionEventCallback;
  WHEEL: Cesium.ScreenSpaceEventHandler.WheelEventCallback;
  PINCH_START: Cesium.ScreenSpaceEventHandler.TwoPointEventCallback;
  PINCH_END: Cesium.ScreenSpaceEventHandler.TwoPointEventCallback;
  PINCH_MOVE: Cesium.ScreenSpaceEventHandler.TwoPointMotionEventCallback;
}

/**
 * 屏幕事件字段
 */
export type CesiumScreenEventType = keyof CesiumScreenEventMapper;

/**
 * 屏幕事件参数
 */
export type CesiumScreenEventArgs<T extends CesiumScreenEventType> = Parameters<
  CesiumScreenEventMapper[T]
>;

/**
 * 屏幕点击事件
 */
export class CesiumScreenEventCollection extends CesiumEventCollection<
  keyof CesiumScreenEventMapper,
  CesiumScreenEventMapper
> {
  constructor(scene: Cesium.Scene) {
    super();
    const eventHandler = new Cesium.ScreenSpaceEventHandler(scene.canvas);
    // 实现原生相同的屏幕事件事件

    getKeys(SPACE_EVENT_TYPE_MAPER).forEach((typeKey) => {
      const type = SPACE_EVENT_TYPE_MAPER[typeKey];
      KEYBOARD_EVENT_MODIFIERS.forEach((modifier) => {
        eventHandler.setInputAction(
          (...args: any) => this.emit(type, args, modifier),
          typeKey,
          modifier,
        );
      });
    });
  }
}
