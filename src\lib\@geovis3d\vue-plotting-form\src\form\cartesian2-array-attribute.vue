<!-- Cartesian2ArrayAttribute -->
<script lang="ts" setup>
import type { Cartesian2SerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import { useShallowBinding } from './hooks';

defineOptions({ name: 'Cartesian2ArrayAttribute' });

const props = defineProps<{
  modelValue?: Cartesian2SerializateJSON[];
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: Cartesian2SerializateJSON[]): void;
}>();

const model = ref<Cartesian2SerializateJSON[]>([]);

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = [...(value ?? [])]),
  rtl: value => emit('update:modelValue', [...(value ?? [])]),
});
</script>

<template>
  <div class="cartesian2-array-attribute">
    Cartesian2ArrayAttribute
  </div>
</template>
