export function createPDF(dataURL: string) {
  const width = 297; // A4 宽度 (mm)
  const height = 210; // A4 高度 (mm)
  const pdfWidth = width * 72 / 25.4; // 转换为点 (pt)
  const pdfHeight = height * 72 / 25.4; // 转换为点 (pt)

  const imgObj = createImageObject(dataURL);

  // 计算图片在页面中的位置
  const imgX = (pdfWidth - imgObj.width) / 2;
  const imgY = (pdfHeight - imgObj.height) / 2;

  // PDF 文档结构
  const pdf = [
    '%PDF-1.4',
    '1 0 obj << /Type /Catalog /Pages 2 0 R >> endobj',
    '2 0 obj << /Type /Pages /Kids [3 0 R] /Count 1 >> endobj',
    `3 0 obj << /Type /Page /Parent 2 0 R /Resources << /XObject << /I1 4 0 R >> >> /MediaBox [0 0 ${pdfWidth} ${pdfHeight}] /Contents 5 0 R >> endobj`,
    `4 0 obj << /Type /XObject /Subtype /Image /Width ${imgObj.width} /Height ${imgObj.height} /ColorSpace /DeviceRGB /BitsPerComponent 8 /Filter /DCTDecode /Length ${imgObj.data.byteLength} >> stream`,
    binaryString(imgObj.data),
    'endstream endobj',
    '5 0 obj << /Length 44 >> stream',
    `q ${imgObj.width} 0 0 ${imgObj.height} ${imgX} ${imgY} cm /I1 Do Q`,
    'endstream endobj',
    'xref',
    '0 6',
    '0000000000 65535 f ',
    '0000000009 00000 n ',
    '0000000056 00000 n ',
    '0000000115 00000 n ',
    '0000000202 00000 n ',
    '0000000281 00000 n ',
    'trailer << /Size 6 /Root 1 0 R >>',
    'startxref',
  ];
  pdf.push(`${pdf.join('\n').length}`, '%%EOF');

  const buffer = new Uint8Array(pdf.join('\n').split('').map(c => c.charCodeAt(0)));
  const blob = new Blob([buffer], { type: 'application/pdf' });
  return blob;
}

function createImageObject(imageData: string) {
  const parts = imageData.split(',')[1];
  const binary = atob(parts);
  const length = binary.length;
  const buffer = new ArrayBuffer(length);
  const view = new Uint8Array(buffer);

  for (let i = 0; i < length; i++) {
    view[i] = binary.charCodeAt(i);
  }

  return {
    data: view,
    width: 200, // 图片宽度 (px)
    height: 200, // 图片高度 (px)
  };
}

function binaryString(buffer: Uint8Array) {
  let result = '';
  const len = buffer.byteLength;
  for (let i = 0; i < len; i++) {
    result += String.fromCharCode(buffer[i]);
  }
  return result;
}
