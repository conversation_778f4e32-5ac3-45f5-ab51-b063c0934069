<!-- 火险预测预警报告 -->
<script lang="ts" setup>
import { computedLoading } from '@/hooks/computed-loading';
import { oneMapCtrGetAlarmStationSort } from '../api';

defineOptions({ name: 'RightWarningStat' });
const types = [
  {
    value: '1',
    label: '本日',
  },
  {
    value: '2',
    label: '本月',
  },
  {
    value: '3',
    label: '本年',
  },
];
const currentType = ref(types[1].value);

const [data, isLoading] = computedLoading(async () => {
  const { data } = await oneMapCtrGetAlarmStationSort({
    areaIsn: '00000000.00000021.',
    areaId: '460000',
    areaId2: '2b36576129924b5496f8d775d40f94ce',
    keyword: '',
    staCode: '',
    type: currentType.value,
  });
  const res = (data ?? []) as any[];
  return res.sort((a, b) => b.num - a.num);
}, []);
</script>

<template>
  <header-title1>
    高频预警站点统计分析
    <template #extra>
      <el-select-v2 v-model="currentType" w="90px!" :options="types" />
    </template>
  </header-title1>
  <div v-loading="isLoading" flex="~ col" m="x-20px b-20px">
    <div v-for="(item, index) in data" :key="item.name" m="y-6px">
      <div flex="~ justify-between" text="16px">
        <span>{{ item.name }}</span>
        <span font="bold">{{ item.num }}</span>
      </div>
      <div flex="~" gap="x-4px">
        <div
          h="6px"
          :style="{ flex: item.num / data[0].num }"
          rd="2px"
          :bg="index === 0 ? 'red/80' : index === 1 ? 'orange/80' : index === 2 ? 'yellow/80' : '#4176ff/80'"
        />
        <div h="6px" :style="{ flex: 1 - (item.num / data[0].num) }" rd="2px" bg="#4176ff/10" />
      </div>
    </div>
    <el-empty v-if="!isLoading && !data.length" />
  </div>
</template>
