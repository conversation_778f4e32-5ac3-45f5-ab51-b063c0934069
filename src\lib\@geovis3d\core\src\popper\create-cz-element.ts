import * as Cesium from 'cesium';

export interface CreateCzElementOptions {
  scene: Cesium.Scene;
  group?: string;
  guid?: string;
  className?: string | string[];
  style?: CSSStyleDeclaration;
}

export interface CreateCzElementRetrun {
  element: HTMLDivElement;
  destroy: VoidFunction;
}

/**
 * 创建一个插入到与cesium同级的div元素
 */
export function createCzElement(options: CreateCzElementOptions): CreateCzElementRetrun {
  const { scene, group = 'default-cesium-element-group', className, style = {} } = options;

  const guid = options.guid || Cesium.createGuid();
  const parent = scene.canvas.parentElement!;

  // 设置一个父级element进行分组
  let container: HTMLDivElement = parent.querySelector(`div[cesium-element-group=${group}]`) as any;
  if (!container) {
    container = document.createElement('div');
    container.setAttribute('cesium-element-group', group);
    parent.append(container);
  }

  const element = document.createElement('div');
  element.setAttribute('guid', guid);
  container.append(element);

  if (Array.isArray(className)) {
    element.classList.add(...className.filter(e => !!e));
  }
  else if (className) {
    element.classList.add(className);
  }
  Object.assign(element.style, style);

  let destroyed = false;

  const destroy = () => {
    if (!destroyed) {
      element.remove();
      !container.hasChildNodes() && parent.removeChild(container);
      destroyed = true;
    }
  };

  return {
    element,
    destroy,
  };
}
