<!-- 资源弹窗 -->
<script lang="ts" setup>
import LocatedPopper1 from '@/components/located-popper/located-popper1.vue';
import { elasticsearchResourcesGetDetailObjectUsingPost } from '@/genapi/elasticsearch';

import { computedLoading } from '@/hooks/computed-loading';
import { cartesianToWgs84 } from '@x3d/all';
import { ResourceMap } from './resource-map';

defineOptions({
  name: 'DataAttributePopper',
});

const props = defineProps<DataAttributePopperProps>();

const emit = defineEmits<DataAttributePopperEmits>();

export interface DataAttributePopperProps {
  modelValue?: any;
}

export interface DataAttributePopperEmits {
  (event: 'update:modelValue', data?: any): void;
}

const model = useVModel(props, 'modelValue', emit);

// 查询详情
const [detail, isLoading] = computedLoading(async () => {
  if (!model.value?.id || !model.value?.typeCode) {
    return undefined;
  }
  const res = await elasticsearchResourcesGetDetailObjectUsingPost({
    data: {
      id: model.value?.id,
      typeCode: model.value?.typeCode,
    },
  });
  return res.data ?? {};
},
);
const position = computed(() => {
  return Cesium.Cartesian3.fromDegrees(model.value?.longitude, model.value?.latitude);
});
// 内容
const RenderContent = computed(() => {
  if (detail.value && model.value?.typeName) {
    const [longitude, latitude] = cartesianToWgs84(position.value!);
    return ResourceMap[model.value?.typeName]?.({
      data: detail.value,
      raw: model.value,
      longitude,
      latitude,
    });
  }
  else {
    return undefined;
  }
});
</script>

<template>
  <LocatedPopper1
    v-if="model"
    class="data-attribute-popper w-600px!"
    :position="position"
    show-close
    :header="detail?.title || model?.typeName"
    content-class="px-20px! py-30px"
    @close="model = undefined"
  >
    <el-scrollbar v-loading="isLoading" wrap-class="max-h-400px! h-auto!">
      <component :is="RenderContent" />
    </el-scrollbar>
  </LocatedPopper1>
</template>

<style scoped lang="scss">
:deep().el-descriptions__table {
  table-layout: fixed;
}
</style>
