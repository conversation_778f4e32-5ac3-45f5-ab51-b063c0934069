<!-- HorizontalOriginAttribute -->
<script lang="ts" setup>
import type { HorizontalOriginSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'HorizontalOriginAttribute' });

const props = defineProps<{
  modelValue?: HorizontalOriginSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: HorizontalOriginSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-form-item :label="label">
    <div class="horizontal-origin-attribute">
      <div
        class="basic-horizontal-origin-item"
        :class="{
          'basic-horizontal-origin-item--active': model == 'LEFT',
        }"
        title="水平参照-左"
        @click="model = 'LEFT'"
      >
        <IconParkAlignLeft />
      </div>
      <div
        class="basic-horizontal-origin-item"
        :class="{
          'basic-horizontal-origin-item--active': model == 'CENTER',
        }"
        title="水平参照-中"
        @click="model = 'CENTER'"
      >
        <IconParkAlignHorizontally />
      </div>
      <div
        class="basic-horizontal-origin-item"
        :class="{
          'basic-horizontal-origin-item--active': model == 'RIGHT',
        }"
        title="水平参照-右"
        @click="model = 'RIGHT'"
      >
        <IconParkAlignRight />
      </div>
    </div>
  </el-form-item>
</template>

<style scoped lang="scss">
.horizontal-origin-attribute {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-around;
  height: var(--el-component-size);
  font-size: 1.2rem;
  background-color: var(--el-fill-color-blank);
  border-radius: var(--el-border-radius-base);

  .basic-horizontal-origin-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 33.33%;
    height: var(--el-component-size);
    cursor: pointer;
    border-radius: var(--el-border-radius-base);

    &.basic-horizontal-origin-item--active {
      background: var(--el-fill-color-light);
    }
  }
}
</style>
