import type * as Cesium from 'cesium';

import type {
  CesiumScreenEventCollection,
  CesiumScreenEventMapper,
} from './cesium-screen-event-collection';
import { throttle } from '@/lib/@geovis3d/shared';
import { CesiumEventCollection, KEYBOARD_EVENT_MODIFIERS } from './cesium-event-collection';
import { deconstructCesiumPick } from './deconstruct-cesium-pick';

export interface PrimitivePositionedEventParams {
  context: Cesium.ScreenSpaceEventHandler.PositionedEvent;
  pick: any;
}

export interface PrimitiveHoverEventParams {
  context: Cesium.ScreenSpaceEventHandler.MotionEvent;
  pick: any;
  hover: boolean;
}

export interface PrimitiveDragEventParams {
  context: Cesium.ScreenSpaceEventHandler.MotionEvent;
  pick: any;
  draging: boolean;
}

export type PrimitivePositionedEventCallback = (data: PrimitivePositionedEventParams) => void;

export type PrimitiveHoverEventCallback = (data: PrimitiveHoverEventParams) => void;

export type PrimitiveDragEventCallback = (data: PrimitiveDragEventParams) => void;

/**
 * Cesium 类Primitive 鼠标(手势)事件回调集合
 */
export interface CesiumVectorEventCallbackMapper {
  LEFT_DOWN: PrimitivePositionedEventCallback;
  LEFT_UP: PrimitivePositionedEventCallback;
  LEFT_CLICK: PrimitivePositionedEventCallback;
  LEFT_DOUBLE_CLICK: PrimitivePositionedEventCallback;
  RIGHT_DOWN: PrimitivePositionedEventCallback;
  RIGHT_UP: PrimitivePositionedEventCallback;
  RIGHT_CLICK: PrimitivePositionedEventCallback;
  MIDDLE_DOWN: PrimitivePositionedEventCallback;
  MIDDLE_UP: PrimitivePositionedEventCallback;
  MIDDLE_CLICK: PrimitivePositionedEventCallback;
  HOVER: PrimitiveHoverEventCallback;
  DRAG: PrimitiveDragEventCallback;
}

export type CesiumVectorEventType = keyof CesiumVectorEventCallbackMapper;

export const PRIMITIVE_EVENT_TYPES: CesiumVectorEventType[] = [
  'LEFT_DOWN',
  'LEFT_UP',
  'LEFT_CLICK',
  'LEFT_DOUBLE_CLICK',
  'RIGHT_DOWN',
  'RIGHT_UP',
  'RIGHT_CLICK',
  'MIDDLE_DOWN',
  'MIDDLE_UP',
  'MIDDLE_CLICK',
  'HOVER',
  'DRAG',
];

/**
 * Primitive属性事件总线
 */
export class CesiumVectorEventCollection extends CesiumEventCollection<
  keyof CesiumVectorEventCallbackMapper,
  CesiumVectorEventCallbackMapper
> {}

// 全局的Primitive属性事件总线
export function globalPrimitiveEventRegister(
  scene: Cesium.Scene,
  screenEvent: CesiumScreenEventCollection,
): CesiumVectorEventCollection {
  const event = new CesiumVectorEventCollection();
  // 通用是事件绑定函数，此类事件与ScreenSpaceEventType事件回参相同
  const commonBind = (
    screenSpaceEventType: keyof CesiumScreenEventMapper,
    vectorEventType: keyof CesiumVectorEventCallbackMapper,
  ) => {
    KEYBOARD_EVENT_MODIFIERS.forEach((modifier) => {
      screenEvent.on(
        screenSpaceEventType,
        (context: any) => {
          const position = context.position;
          const pick = scene.pick(position);
          pick && event.emit(vectorEventType, [{ context, pick }], modifier);
        },
        modifier,
      );
    });
  };

  PRIMITIVE_EVENT_TYPES.forEach((type) => {
    switch (type) {
      case 'LEFT_DOWN':
        commonBind('LEFT_DOWN', type);
        break;
      case 'LEFT_UP':
        commonBind('LEFT_UP', type);
        break;
      case 'LEFT_CLICK':
        commonBind('LEFT_CLICK', type);
        break;
      case 'LEFT_DOUBLE_CLICK':
        commonBind('LEFT_DOUBLE_CLICK', type);
        break;
      case 'RIGHT_DOWN':
        commonBind('RIGHT_DOWN', type);
        break;
      case 'RIGHT_UP':
        commonBind('RIGHT_UP', type);
        break;
      case 'RIGHT_CLICK':
        commonBind('RIGHT_CLICK', type);
        break;
      case 'MIDDLE_DOWN':
        commonBind('MIDDLE_DOWN', type);
        break;
      case 'MIDDLE_UP':
        commonBind('MIDDLE_UP', type);
        break;
      case 'MIDDLE_CLICK':
        commonBind('MIDDLE_CLICK', type);
        break;
      case 'HOVER':
        // hover事件循环：鼠标每次移动都拾取到具有primitive属性的pick对象，存在pick则触发hover事件。
        // 如果当前pick(或未拾取到pick)不等于上一次循环中的pick，则上一个pick需要触发一次hover=false的事件（hover结束）
        KEYBOARD_EVENT_MODIFIERS.forEach((modifier) => {
          let preHoverPick: any;
          let stopPrev: (() => void) | null;
          const callback = (context: Cesium.ScreenSpaceEventHandler.MotionEvent) => {
            const pick = scene.pick(context.endPosition);
            if (!pick || pick !== preHoverPick) {
              stopPrev?.();
              stopPrev = null;
              preHoverPick = null;
            }
            if (pick) {
              const _emit = (hover: boolean) =>
                event.emit('HOVER', [{ context, pick, hover }], modifier);
              preHoverPick = pick;
              stopPrev = () => _emit(false);
              _emit(true);
            }
          };
          // 节流
          const fn = throttle(callback, 1);
          screenEvent.on('MOUSE_MOVE', fn, modifier);
        });

        break;
      case 'DRAG':
        // 拖拽事件循环： 鼠标下按=>鼠标移动=>鼠标弹起
        KEYBOARD_EVENT_MODIFIERS.forEach((modifier) => {
          let pick: any;
          let moved = false; // 是否存在移动行为
          let startPosition: Cesium.Cartesian2 | undefined;

          // 鼠标下按 拾取到具有primitive属性的pick对象则开始进行拖拽事件循环
          screenEvent.on(
            'LEFT_DOWN',
            (context) => {
              pick = scene.pick(context.position);
              if (pick) {
                startPosition = context.position; // 鼠标下按，该点为起始点
              }
            },
            modifier,
          );

          screenEvent.on(
            'MOUSE_MOVE',
            (context) => {
              if (!pick) {
                return;
              }

              // 此前没有鼠标移动行为，则此次为拖拽循环中第一次触发，startPosition应为鼠标下按时的坐标
              if (!moved) {
                moved = true;
                const params = {
                  context: {
                    startPosition: startPosition!,
                    endPosition: context.endPosition,
                  },
                  pick,
                  draging: true,
                };
                event.emit('DRAG', [params], modifier);
              }
              else {
                startPosition = context.endPosition.clone();
                event.emit('DRAG', [{ context, pick, draging: true }], modifier);
              }
            },
            modifier,
          );

          // 重置拖拽事件循环
          screenEvent.on(
            'LEFT_UP',
            (context) => {
              event.emit(
                'DRAG',
                [
                  {
                    context: {
                      startPosition: startPosition!,
                      endPosition: context.position,
                    },
                    pick,
                    draging: false,
                  },
                ],
                modifier,
              );
              pick = undefined;
              moved = false;
              startPosition = undefined;
            },
            modifier,
          );
        });
        break;
    }
  });
  return event;
}

/**
 * Primitive相关实体自身携带的屏幕事件注册器
 */
export function primitiveAppendRegister(vectorEvent: CesiumVectorEventCollection) {
  PRIMITIVE_EVENT_TYPES.forEach((type) => {
    KEYBOARD_EVENT_MODIFIERS.forEach((modifier) => {
      vectorEvent.on(
        type,
        (args: any) => {
          const pick = args?.pick;
          const cesiumEventCollection = deconstructCesiumPick<CesiumVectorEventCollection>(
            pick,
            'event',
          );
          Object.values(cesiumEventCollection).forEach((event) => {
            // 聚合
            if (Array.isArray(event)) {
              event.forEach((e) => {
                e?.emit(type, [args], modifier);
              });
            }
            else {
              event?.emit(type, [args], modifier);
            }
          });
        },
        modifier,
      );
    });
  });
}
