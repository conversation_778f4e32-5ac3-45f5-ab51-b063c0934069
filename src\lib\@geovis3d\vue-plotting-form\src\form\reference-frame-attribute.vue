<!-- ReferenceFrameAttribute -->
<script lang="ts" setup>
import type { ReferenceFrameSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'ReferenceFrameAttribute' });

const props = defineProps<{
  modelValue?: ReferenceFrameSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: ReferenceFrameSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const options = [
  {
    label: '固定',
    value: 'FIXED',
  },
  {
    label: '惯性',
    value: 'INERTIAL',
  },
];
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="model" :options="options" :clearable="false" />
  </el-form-item>
</template>
