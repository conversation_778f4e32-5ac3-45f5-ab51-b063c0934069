<!-- 高度测量 -->
<script lang="ts" setup>
import {
  cartesianToCartographic,
  clampToHeightMostDetailedByTilesetOrTerrain,
  CzPlotEntity,
} from '@x3d/all';
import { useCzEntities, useCzScene } from '@x3d/vue-hooks';
import { ClassificationType } from 'cesium';

defineOptions({ name: 'MeasureHeight' });
const props = withDefaults(
  defineProps<{
    modelValue?: boolean;
    isActive?: boolean;
  }>(),
  { modelValue: true },
);
const scene = useCzScene();
const show = useVModel(props, 'modelValue');
const unit = ref(1);
const result = ref(0);
const startH = ref(0);
const endH = ref(0);

const plotEntity = shallowRef<CzPlotEntity>();
function initEntity() {
  plotEntity.value = new CzPlotEntity({
    scheme: {
      forceTerminate: (entity) => {
        return entity.record.positions.getLength() > 1;
      },
      control: { visible: true },
      effect(entity) {
        entity.polyline ??= new Cesium.PolylineGraphics({
          material: Cesium.Color.fromCssColorString('#FCC650'),
          width: 2,
        });
        const { record, controller } = entity;

        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 2 ? positions : [];
        entity.polyline.positions = new Cesium.CallbackProperty(() => cache, false);
        entity.position = new Cesium.ConstantPositionProperty(
          record.positions.getCenter()!,
        );
        positions.length > 1
        && clampToHeightMostDetailedByTilesetOrTerrain({
          positions,
          scene: scene.value,
          classificationType: ClassificationType.TERRAIN,
        }).then((cars3) => {
          const positions = cars3.map(e => cartesianToCartographic(e));
          startH.value = positions[0].height;
          endH.value = positions[1].height;
          result.value = startH.value - endH.value;
          let text = '';
          if (result.value / 1000 > 1) {
            text = `高度差：${(result.value / 1000).toFixed(2)}km`;
          }
          else {
            text = `高度差：${(+result.value).toFixed(2)}m`;
          }
          entity.label = new Cesium.LabelGraphics({
            text,
            font: '16pt Source Han Sans CN',
            pixelOffset: new Cesium.Cartesian2(0, -20),
          });
        });
      },
    },
  });
}
useCzEntities(() => [plotEntity.value]);

initEntity();
defineExpose({ initEntity });
</script>

<template>
  <basic-card
    v-show="show"
    show-close
    title="量测设置"
    @close="show = false"
  >
    <div class="px-24px py-16px">
      高度测量
      <el-select v-model="unit" mt="8px">
        <el-option label=" 米（m）" :value="1" />
        <el-option label=" 千米（km）" :value="1000" />
      </el-select>
      <div text="#FCC650 14px" mt="12px" lh="24px">
        提示：左键在球上选点，第二次左键结束高度量测
      </div>
    </div>
    <template #footer>
      <div flex="~ justify-between" class="w-full px-24px font-blod-18px">
        起点海拔：
        <span> {{ (startH / unit).toFixed(2) }}{{ unit === 1 ? "m" : "km" }} </span>
      </div>
      <el-divider />
      <div flex="~ justify-between" class="w-full px-24px font-blod-18px">
        终点海拔：
        <span> {{ (endH / unit).toFixed(2) }}{{ unit === 1 ? "m" : "km" }} </span>
      </div>
      <el-divider />
      <div flex="~ justify-between" class="w-full px-24px font-blod-18px">
        高度差：
        <span> {{ (result / unit).toFixed(2) }}{{ unit === 1 ? "m" : "km" }} </span>
      </div>
    </template>
  </basic-card>
</template>

<style lang="scss" scoped>
.el-select {
  :deep() &__wrapper {
    min-height: 44px !important;
  }
}

.basic-card {
  :deep() .footer-container {
    flex-direction: column;
    padding-right: 0;
    padding-left: 0;
  }
}
</style>
