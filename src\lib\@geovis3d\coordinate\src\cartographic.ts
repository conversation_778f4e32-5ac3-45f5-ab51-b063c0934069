import type { BD09, Coordinate, GCJ02, WGS84 } from './types';

import * as Cesium from 'cesium';

import { wgs84ToBd09, wgs84ToGcj02 } from './wgs84';

/**
 * Cartographic 转 WGS84
 * @param position Cartographic
 * @returns WGS84
 */
export function cartographicToWgs84(position: Cesium.Cartographic): WGS84 {
  const longitude = Cesium.Math.toDegrees(position.longitude);
  const latitude = Cesium.Math.toDegrees(position.latitude);
  return [longitude, latitude, position.height];
}

/**
 * Cartographic 转 Cartesian3
 * @param position Cartographic
 * @returns Cartesian3
 */
export function cartographicToCartesian(position: Cesium.Cartographic): Cesium.Cartesian3 {
  return Cesium.Ellipsoid.WGS84.cartographicToCartesian(position);
}

/**
 * Cartographic 转 Coordinate
 * @param position Cartographic
 * @returns Coordinate
 */
export function cartographicToCoordinate(
  position: Cesium.Cartographic,
  scene: Cesium.Scene,
): Coordinate {
  const cartesian = cartographicToCartesian(position);
  return scene.cartesianToCanvasCoordinates(cartesian);
}

/**
 * Cartographic 转 GCJ02
 * @param position Cartographic
 * @returns GCJ02
 */
export function cartographicToGcj02(position: Cesium.Cartographic): GCJ02 {
  const wgs84 = cartographicToWgs84(position);
  return wgs84ToGcj02(wgs84);
}

/**
 * Cartographic 转 BD09
 * @param position Cartographic
 * @returns BD09
 */
export function cartographicToBd09(position: Cesium.Cartographic): BD09 {
  const wgs84 = cartographicToWgs84(position);
  return wgs84ToBd09(wgs84);
}
