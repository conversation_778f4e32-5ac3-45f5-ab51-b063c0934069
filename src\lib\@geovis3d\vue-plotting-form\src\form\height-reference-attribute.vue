<!-- HeightReferenceAttribute -->
<script lang="ts" setup>
import type { HeightReferenceSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'HeightReferenceAttribute' });

const props = defineProps<{
  modelValue?: HeightReferenceSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: HeightReferenceSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-form-item :label="label">
    <div class="height-reference-attribute">
      <div
        class="basic-height-reference-item"
        :class="{
          'basic-height-reference-item--active': model == 'NONE',
        }"
        title="高度参照-无"
        @click="model = 'NONE'"
      >
        <span>不贴地</span>
      </div>
      <div
        class="basic-height-reference-item"
        :class="{
          'basic-height-reference-item--active': model == 'CLAMP_TO_GROUND',
        }"
        title="高度参照-贴地"
        @click="model = 'CLAMP_TO_GROUND'"
      >
        <span>贴地</span>
      </div>
      <div
        class="basic-height-reference-item"
        :class="{
          'basic-height-reference-item--active': model == 'RELATIVE_TO_GROUND',
        }"
        title="高度参照-相对于地面"
        @click="model = 'RELATIVE_TO_GROUND'"
      >
        <span>相对地面</span>
      </div>
    </div>
  </el-form-item>
</template>

<style scoped lang="scss">
.height-reference-attribute {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-around;
  height: var(--el-component-size);
  font-size: 1.2rem;
  background-color: var(--el-fill-color-blank);
  border-radius: var(--el-border-radius-base);

  .basic-height-reference-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 33.33%;
    height: var(--el-component-size);
    font-size: 12px;
    cursor: pointer;
    border-radius: var(--el-border-radius-base);

    &.basic-height-reference-item--active {
      background: var(--el-fill-color-light);
    }
  }
}
</style>
