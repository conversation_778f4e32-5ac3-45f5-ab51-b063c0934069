window.HlsJsPlayer=function(t){var e={};function r(i){if(e[i])return e[i].exports;var n=e[i]={i:i,l:!1,exports:{}};return t[i].call(n.exports,n,n.exports,r),n.l=!0,n.exports}return r.m=t,r.c=e,r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(i,n,function(e){return t[e]}.bind(null,n));return i},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=0)}([function(t,e,r){t.exports=r(1)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(){function t(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,r,i){return r&&t(e.prototype,r),i&&t(e,i),e}}(),a=l(r(2)),s=l(r(3)),o=l(r(4));function l(t){return t&&t.__esModule?t:{default:t}}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}var d=function(t){function e(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=u(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));r.hlsOpts=t.hlsOpts||{},r.hlsOpts.mediaType=r.config.mediaType;var i=a.default.util,n=r;if(n.once("complete",(function(){if(n.config.isLive&&(i.addClass(n.root,"xgplayer-is-live"),!i.findDom(n.controls,".xgplayer-live"))){var t=i.createDom("xg-live",n.lang.LIVE||"正在直播",{},"xgplayer-live");n.controls.appendChild(t)}})),r.browser=o.default.getBrowserVersion(),void 0===n.config.useHls){if("mobile"===a.default.sniffer.device&&"MacIntel"!==navigator.platform&&"Win32"!==navigator.platform||r.browser.indexOf("Safari")>-1)return u(r)}else if(!n.config.useHls)return u(r);r._start=r.start,r.start=function(){window.XgVideoProxy||r.root.insertBefore(r.video,r.root.firstChild),setTimeout((function(){r.emit("complete"),r.danmu&&"function"==typeof r.danmu.resize&&r.danmu.resize()}),1)},Number.isFinite=Number.isFinite||function(t){return"number"==typeof t&&isFinite(t)};var l=void 0;return l=new s.default(r.hlsOpts),r.hls=l,Object.defineProperty(n,"src",{get:function(){return n.currentSrc},set:function(t){i.removeClass(n.root,"xgplayer-is-live");var e=document.querySelector(".xgplayer-live");e&&e.parentNode.removeChild(e),n.autoplay=!0,n.paused||n.pause(),n.hls.stopLoad(),n.hls.detachMedia(),n.hls.destroy(),n.hls=new s.default(n.hlsOpts),n.register(t),n.once("canplay",(function(){n.play().catch((function(t){}))})),n.hls.loadSource(t),n.hls.attachMedia(n.video)},configurable:!0}),r.register(r.config.url),r.once("complete",(function(){l.attachMedia(n.video),n.config.videoInit||n.once("canplay",(function(){n.play().catch((function(t){}))}))})),r.once("destroy",(function(){l.stopLoad()})),r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),n(e,[{key:"switchURL",value:function(t){var e=this;e.url=t,e.config.url=t;var r=e.currentTime;a.default.util.addClass(e.root,"xgplayer-is-enter"),e.once("playing",(function(){a.default.util.removeClass(e.root,"xgplayer-is-enter")})),e.once("canplay",(function(){e.currentTime=r,e.play()})),"object"===i(e.hls)&&(e.hls.originUrl=t),e.src=t}},{key:"register",value:function(t){var e=this.hls,r=a.default.util,i=this;e.on(s.default.Events.MEDIA_ATTACHED,(function(){e.loadSource(t)})),e.on(s.default.Events.LEVEL_LOADED,(function(t,n){if(!e.inited&&(e.inited=!0,n&&n.details&&n.details.live&&(r.addClass(i.root,"xgplayer-is-live"),!r.findDom(i.root,".xgplayer-live")))){var a=r.createDom("xg-live",i.lang.LIVE||"正在直播",{},"xgplayer-live");i.controls.appendChild(a)}})),e.on(s.default.Events.ERROR,(function(t,r){if(i.emit("HLS_ERROR",{errorType:r.type,errorDetails:r.details,errorFatal:r.fatal}),r.fatal)switch(r.type){case s.default.ErrorTypes.NETWORK_ERROR:e.startLoad();break;case s.default.ErrorTypes.MEDIA_ERROR:e.recoverMediaError();break;default:i.emit("error",r)}})),this._statistics()}},{key:"updateURLOnly",value:function(t){var e=this.hls.levelController;Array.isArray(e._levels)&&e._levels.length>0&&(e._levels[0].url=[t]),this.config.url=t,this.url=t,this.hls.originUrl=t}},{key:"_statistics",value:function(){var t={speed:0,playerType:"HlsPlayer"},e={videoDataRate:0,audioDataRate:0},r=this.hls,i=this;r.on(s.default.Events.FRAG_LOAD_PROGRESS,(function(e,r){t.speed=r.stats.loaded/1e3})),r.on(s.default.Events.FRAG_PARSING_DATA,(function(t,r){"video"===r.type&&(e.fps=parseInt(r.nb/(r.endPTS-r.startPTS)))})),r.on(s.default.Events.FRAG_PARSING_INIT_SEGMENT,(function(t,r){if(e.hasAudio=!!(r&&r.tracks&&r.tracks.audio),e.hasVideo=!!(r&&r.tracks&&r.tracks.video),e.hasAudio&&r.tracks.audio){var n=r.tracks.audio;e.audioChannelCount=n.metadata&&n.metadata.channelCount?n.metadata.channelCount:0,e.audioCodec=n.codec}if(e.hasVideo&&r.tracks.video){var a=r.tracks.video;e.videoCodec=a.codec,e.width=a.metadata&&a.metadata.width?a.metadata.width:0,e.height=a.metadata&&a.metadata.height?a.metadata.height:0}e.duration=r.frag&&r.frag.duration?r.frag.duration:0,e.level=r.frag&&r.frag.level?r.frag.level:0,(e.videoCodec||e.audioCodec)&&(e.mimeType='video/hls; codecs="'+e.videoCodec+";"+e.audioCodec+'"'),i.mediainfo=e,i.emit("media_info",e)})),this._statisticsTimmer=setInterval((function(){i.emit("statistics_info",t),t.speed=0}),1e3)}},{key:"destroy",value:function(){clearInterval(this._statisticsTimmer),this.hls&&this.hls.destroy(),function t(e,r,i){null===e&&(e=Function.prototype);var n=Object.getOwnPropertyDescriptor(e,r);if(void 0===n){var a=Object.getPrototypeOf(e);return null===a?void 0:t(a,r,i)}if("value"in n)return n.value;var s=n.get;return void 0!==s?s.call(i):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"destroy",this).call(this)}}]),e}(a.default);d.isSupported=s.default.isSupported,d.HlsJs=s.default,e.default=d,t.exports=e.default},function(t,e){t.exports=window.Player},function(t,e,r){var i;"undefined"!=typeof window&&(i=()=>(()=>{var t={"./src/config.ts":
/*!***********************!*\
  !*** ./src/config.ts ***!
  \***********************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{enableStreamingMode:()=>A,hlsDefaultConfig:()=>D,mergeConfig:()=>L});var i=r(/*! ./controller/abr-controller */"./src/controller/abr-controller.ts"),n=r(/*! ./controller/audio-stream-controller */"./src/controller/audio-stream-controller.ts"),a=r(/*! ./controller/audio-track-controller */"./src/controller/audio-track-controller.ts"),s=r(/*! ./controller/subtitle-stream-controller */"./src/controller/subtitle-stream-controller.ts"),o=r(/*! ./controller/subtitle-track-controller */"./src/controller/subtitle-track-controller.ts"),l=r(/*! ./controller/buffer-controller */"./src/controller/buffer-controller.ts"),u=r(/*! ./controller/timeline-controller */"./src/controller/timeline-controller.ts"),d=r(/*! ./controller/cap-level-controller */"./src/controller/cap-level-controller.ts"),c=r(/*! ./controller/fps-controller */"./src/controller/fps-controller.ts"),h=r(/*! ./controller/eme-controller */"./src/controller/eme-controller.ts"),f=r(/*! ./controller/cmcd-controller */"./src/controller/cmcd-controller.ts"),g=r(/*! ./utils/xhr-loader */"./src/utils/xhr-loader.ts"),v=r(/*! ./utils/fetch-loader */"./src/utils/fetch-loader.ts"),p=r(/*! ./utils/cues */"./src/utils/cues.ts"),m=r(/*! ./utils/mediakeys-helper */"./src/utils/mediakeys-helper.ts"),y=r(/*! ./utils/logger */"./src/utils/logger.ts");function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach((function(e){b(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function b(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var D=S(S({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,ignoreDevicePixelRatio:!1,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,maxBufferSize:6e7,maxBufferHole:.1,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,enableSoftwareAES:!0,manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,startLevel:void 0,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:g.default,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:i.default,bufferController:l.default,capLevelController:d.default,fpsController:c.default,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystemOptions:{},requestMediaKeySystemAccessFunc:m.requestMediaKeySystemAccess,testBandwidth:!0,progressive:!1,lowLatencyMode:!0,cmcd:void 0,enableDateRangeMetadataCues:!0,enableEmsgMetadataCues:!0,enableID3MetadataCues:!0},{cueHandler:p.default,enableWebVTT:!0,enableIMSC1:!0,enableCEA708Captions:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}),{},{subtitleStreamController:s.SubtitleStreamController,subtitleTrackController:o.default,timelineController:u.TimelineController,audioStreamController:n.default,audioTrackController:a.default,emeController:h.default,cmcdController:f.default});function L(t,e){if((e.liveSyncDurationCount||e.liveMaxLatencyDurationCount)&&(e.liveSyncDuration||e.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(void 0!==e.liveMaxLatencyDurationCount&&(void 0===e.liveSyncDurationCount||e.liveMaxLatencyDurationCount<=e.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(void 0!==e.liveMaxLatencyDuration&&(void 0===e.liveSyncDuration||e.liveMaxLatencyDuration<=e.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');return E({},t,e)}function A(t){var e=t.loader;e!==v.default&&e!==g.default?(y.logger.log("[config]: Custom loader detected, cannot enable progressive streaming"),t.progressive=!1):(0,v.fetchSupported)()&&(t.loader=v.default,t.progressive=!0,t.enableSoftwareAES=!0,y.logger.log("[config]: Progressive streaming enabled, using FetchLoader"))}},"./src/controller/abr-controller.ts":
/*!******************************************!*\
  !*** ./src/controller/abr-controller.ts ***!
  \******************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>d});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../utils/ewma-bandwidth-estimator */"./src/utils/ewma-bandwidth-estimator.ts"),a=r(/*! ../events */"./src/events.ts"),s=r(/*! ../errors */"./src/errors.ts"),o=r(/*! ../types/loader */"./src/types/loader.ts"),l=r(/*! ../utils/logger */"./src/utils/logger.ts");function u(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}const d=function(){function t(t){this.hls=void 0,this.lastLoadedFragLevel=0,this._nextAutoLevel=-1,this.timer=void 0,this.onCheck=this._abandonRulesCheck.bind(this),this.fragCurrent=null,this.partCurrent=null,this.bitrateTestDelay=0,this.bwEstimator=void 0,this.hls=t;var e=t.config;this.bwEstimator=new n.default(e.abrEwmaSlowVoD,e.abrEwmaFastVoD,e.abrEwmaDefaultEstimate),this.registerListeners()}var e,r,d,c=t.prototype;return c.registerListeners=function(){var t=this.hls;t.on(a.Events.FRAG_LOADING,this.onFragLoading,this),t.on(a.Events.FRAG_LOADED,this.onFragLoaded,this),t.on(a.Events.FRAG_BUFFERED,this.onFragBuffered,this),t.on(a.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.on(a.Events.ERROR,this.onError,this)},c.unregisterListeners=function(){var t=this.hls;t.off(a.Events.FRAG_LOADING,this.onFragLoading,this),t.off(a.Events.FRAG_LOADED,this.onFragLoaded,this),t.off(a.Events.FRAG_BUFFERED,this.onFragBuffered,this),t.off(a.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.off(a.Events.ERROR,this.onError,this)},c.destroy=function(){this.unregisterListeners(),this.clearTimer(),this.hls=this.onCheck=null,this.fragCurrent=this.partCurrent=null},c.onFragLoading=function(t,e){var r,i=e.frag;i.type===o.PlaylistLevelType.MAIN&&(this.timer||(this.fragCurrent=i,this.partCurrent=null!=(r=e.part)?r:null,this.timer=self.setInterval(this.onCheck,100)))},c.onLevelLoaded=function(t,e){var r=this.hls.config;e.details.live?this.bwEstimator.update(r.abrEwmaSlowLive,r.abrEwmaFastLive):this.bwEstimator.update(r.abrEwmaSlowVoD,r.abrEwmaFastVoD)},c._abandonRulesCheck=function(){var t=this.fragCurrent,e=this.partCurrent,r=this.hls,n=r.autoLevelEnabled,s=(r.config,r.media);if(t&&s){var o=e?e.stats:t.stats,u=e?e.duration:t.duration;if(o.aborted||o.loaded&&o.loaded===o.total||0===t.level)return this.clearTimer(),void(this._nextAutoLevel=-1);if(n&&!s.paused&&s.playbackRate&&s.readyState){var d=r.mainForwardBufferInfo;if(null!==d){var c=performance.now()-o.loading.start,h=Math.abs(s.playbackRate);if(!(c<=500*u/h)){var f=o.loaded&&o.loading.first,g=this.bwEstimator.getEstimate(),v=r.levels,p=r.minAutoLevel,m=v[t.level],y=o.total||Math.max(o.loaded,Math.round(u*m.maxBitrate/8)),E=f?1e3*o.loaded/c:0,T=E?(y-o.loaded)/E:8*y/g,S=d.len/h;if(!(T<=S)){var b,D=Number.POSITIVE_INFINITY;for(b=t.level-1;b>p;b--){var L=v[b].maxBitrate;if((D=E?u*L/(6.4*E):u*L/g)<S)break}D>=T||(l.logger.warn("Fragment "+t.sn+(e?" part "+e.index:"")+" of level "+t.level+" is loading too slowly and will cause an underbuffer; aborting and switching to level "+b+"\n      Current BW estimate: "+((0,i.isFiniteNumber)(g)?(g/1024).toFixed(3):"Unknown")+" Kb/s\n      Estimated load time for current fragment: "+T.toFixed(3)+" s\n      Estimated load time for the next fragment: "+D.toFixed(3)+" s\n      Time to underbuffer: "+S.toFixed(3)+" s"),r.nextLoadLevel=b,f&&this.bwEstimator.sample(c,o.loaded),this.clearTimer(),t.loader&&(this.fragCurrent=this.partCurrent=null,t.loader.abort()),r.trigger(a.Events.FRAG_LOAD_EMERGENCY_ABORTED,{frag:t,part:e,stats:o}))}}}}}},c.onFragLoaded=function(t,e){var r=e.frag,n=e.part;if(r.type===o.PlaylistLevelType.MAIN&&(0,i.isFiniteNumber)(r.sn)){var s=n?n.stats:r.stats,l=n?n.duration:r.duration;if(this.clearTimer(),this.lastLoadedFragLevel=r.level,this._nextAutoLevel=-1,this.hls.config.abrMaxWithRealBitrate){var u=this.hls.levels[r.level],d=(u.loaded?u.loaded.bytes:0)+s.loaded,c=(u.loaded?u.loaded.duration:0)+l;u.loaded={bytes:d,duration:c},u.realBitrate=Math.round(8*d/c)}if(r.bitrateTest){var h={stats:s,frag:r,part:n,id:r.type};this.onFragBuffered(a.Events.FRAG_BUFFERED,h)}}},c.onFragBuffered=function(t,e){var r=e.frag,i=e.part,n=i?i.stats:r.stats;if(!n.aborted&&r.type===o.PlaylistLevelType.MAIN&&"initSegment"!==r.sn){var a=n.parsing.end-n.loading.start;this.bwEstimator.sample(a,n.loaded),n.bwEstimate=this.bwEstimator.getEstimate(),r.bitrateTest?this.bitrateTestDelay=a/1e3:this.bitrateTestDelay=0}},c.onError=function(t,e){switch(e.details){case s.ErrorDetails.FRAG_LOAD_ERROR:case s.ErrorDetails.FRAG_LOAD_TIMEOUT:this.clearTimer()}},c.clearTimer=function(){self.clearInterval(this.timer),this.timer=void 0},c.getNextABRAutoLevel=function(){var t=this.fragCurrent,e=this.partCurrent,r=this.hls,i=r.maxAutoLevel,n=r.config,a=r.minAutoLevel,s=r.media,o=e?e.duration:t?t.duration:0,u=(s&&s.currentTime,s&&0!==s.playbackRate?Math.abs(s.playbackRate):1),d=this.bwEstimator?this.bwEstimator.getEstimate():n.abrEwmaDefaultEstimate,c=r.mainForwardBufferInfo,h=(c?c.len:0)/u,f=this.findBestLevel(d,a,i,h,n.abrBandWidthFactor,n.abrBandWidthUpFactor);if(f>=0)return f;l.logger.trace((h?"rebuffering expected":"buffer is empty")+", finding optimal quality level");var g=o?Math.min(o,n.maxStarvationDelay):n.maxStarvationDelay,v=n.abrBandWidthFactor,p=n.abrBandWidthUpFactor;if(!h){var m=this.bitrateTestDelay;m&&(g=(o?Math.min(o,n.maxLoadingDelay):n.maxLoadingDelay)-m,l.logger.trace("bitrate test took "+Math.round(1e3*m)+"ms, set first fragment max fetchDuration to "+Math.round(1e3*g)+" ms"),v=p=1)}return f=this.findBestLevel(d,a,i,h+g,v,p),Math.max(f,0)},c.findBestLevel=function(t,e,r,n,a,s){for(var o,u=this.fragCurrent,d=this.partCurrent,c=this.lastLoadedFragLevel,h=this.hls.levels,f=h[c],g=!(null==f||null===(o=f.details)||void 0===o||!o.live),v=null==f?void 0:f.codecSet,p=d?d.duration:u?u.duration:0,m=r;m>=e;m--){var y=h[m];if(y&&(!v||y.codecSet===v)){var E=y.details,T=(d?null==E?void 0:E.partTarget:null==E?void 0:E.averagetargetduration)||p,S=void 0;S=m<=c?a*t:s*t;var b=h[m].maxBitrate,D=b*T/S;if(l.logger.trace("level/adjustedbw/bitrate/avgDuration/maxFetchDuration/fetchDuration: "+m+"/"+Math.round(S)+"/"+b+"/"+T+"/"+n+"/"+D),S>b&&(0===D||!(0,i.isFiniteNumber)(D)||g&&!this.bitrateTestDelay||D<n))return m}}return-1},e=t,(r=[{key:"nextAutoLevel",get:function(){var t=this._nextAutoLevel,e=this.bwEstimator;if(-1!==t&&!e.canEstimate())return t;var r=this.getNextABRAutoLevel();return-1!==t&&this.hls.levels[r].loadError?t:(-1!==t&&(r=Math.min(t,r)),r)},set:function(t){this._nextAutoLevel=t}}])&&u(e.prototype,r),d&&u(e,d),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/controller/audio-stream-controller.ts":
/*!***************************************************!*\
  !*** ./src/controller/audio-stream-controller.ts ***!
  \***************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>E});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ./base-stream-controller */"./src/controller/base-stream-controller.ts"),a=r(/*! ../events */"./src/events.ts"),s=r(/*! ../utils/buffer-helper */"./src/utils/buffer-helper.ts"),o=r(/*! ./fragment-tracker */"./src/controller/fragment-tracker.ts"),l=r(/*! ../types/level */"./src/types/level.ts"),u=r(/*! ../types/loader */"./src/types/loader.ts"),d=r(/*! ../loader/fragment */"./src/loader/fragment.ts"),c=r(/*! ../demux/chunk-cache */"./src/demux/chunk-cache.ts"),h=r(/*! ../demux/transmuxer-interface */"./src/demux/transmuxer-interface.ts"),f=r(/*! ../types/transmuxer */"./src/types/transmuxer.ts"),g=r(/*! ./fragment-finders */"./src/controller/fragment-finders.ts"),v=r(/*! ../utils/discontinuities */"./src/utils/discontinuities.ts"),p=r(/*! ../errors */"./src/errors.ts");function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function y(t,e){return(y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}const E=function(t){var e,r;function E(e,r){var i;return(i=t.call(this,e,r,"[audio-stream-controller]")||this).videoBuffer=null,i.videoTrackCC=-1,i.waitingVideoCC=-1,i.audioSwitch=!1,i.trackId=-1,i.waitingData=null,i.mainDetails=null,i.bufferFlushed=!1,i.cachedTrackLoadedData=null,i._registerListeners(),i}r=t,(e=E).prototype=Object.create(r.prototype),e.prototype.constructor=e,y(e,r);var T=E.prototype;return T.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null},T._registerListeners=function(){var t=this.hls;t.on(a.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(a.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(a.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(a.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.on(a.Events.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),t.on(a.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.on(a.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.on(a.Events.ERROR,this.onError,this),t.on(a.Events.BUFFER_RESET,this.onBufferReset,this),t.on(a.Events.BUFFER_CREATED,this.onBufferCreated,this),t.on(a.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),t.on(a.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),t.on(a.Events.FRAG_BUFFERED,this.onFragBuffered,this)},T._unregisterListeners=function(){var t=this.hls;t.off(a.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(a.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(a.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(a.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.off(a.Events.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),t.off(a.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.off(a.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.off(a.Events.ERROR,this.onError,this),t.off(a.Events.BUFFER_RESET,this.onBufferReset,this),t.off(a.Events.BUFFER_CREATED,this.onBufferCreated,this),t.off(a.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),t.off(a.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),t.off(a.Events.FRAG_BUFFERED,this.onFragBuffered,this)},T.onInitPtsFound=function(t,e){var r=e.frag,i=e.id,a=e.initPTS;if("main"===i){var s=r.cc;this.initPTS[r.cc]=a,this.log("InitPTS for cc: "+s+" found from main: "+a),this.videoTrackCC=s,this.state===n.State.WAITING_INIT_PTS&&this.tick()}},T.startLoad=function(t){if(!this.levels)return this.startPosition=t,void(this.state=n.State.STOPPED);var e=this.lastCurrentTime;this.stopLoad(),this.setInterval(100),this.fragLoadError=0,e>0&&-1===t?(this.log("Override startPosition with lastCurrentTime @"+e.toFixed(3)),t=e,this.state=n.State.IDLE):(this.loadedmetadata=!1,this.state=n.State.WAITING_TRACK),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()},T.doTick=function(){switch(this.state){case n.State.IDLE:this.doTickIdle();break;case n.State.WAITING_TRACK:var e,r=this.levels,i=this.trackId,a=null==r||null===(e=r[i])||void 0===e?void 0:e.details;if(a){if(this.waitForCdnTuneIn(a))break;this.state=n.State.WAITING_INIT_PTS}break;case n.State.FRAG_LOADING_WAITING_RETRY:var o,l=performance.now(),u=this.retryDate;(!u||l>=u||null!==(o=this.media)&&void 0!==o&&o.seeking)&&(this.log("RetryDate reached, switch back to IDLE state"),this.resetStartWhenNotLoaded(this.trackId),this.state=n.State.IDLE);break;case n.State.WAITING_INIT_PTS:var d=this.waitingData;if(d){var c=d.frag,h=d.part,f=d.cache,v=d.complete;if(void 0!==this.initPTS[c.cc]){this.waitingData=null,this.waitingVideoCC=-1,this.state=n.State.FRAG_LOADING;var p={frag:c,part:h,payload:f.flush(),networkDetails:null};this._handleFragmentLoadProgress(p),v&&t.prototype._handleFragmentLoadComplete.call(this,p)}else if(this.videoTrackCC!==this.waitingVideoCC)this.log("Waiting fragment cc ("+c.cc+") cancelled because video is at cc "+this.videoTrackCC),this.clearWaitingFragment();else{var m=this.getLoadPosition(),y=s.BufferHelper.bufferInfo(this.mediaBuffer,m,this.config.maxBufferHole);(0,g.fragmentWithinToleranceTest)(y.end,this.config.maxFragLookUpTolerance,c)<0&&(this.log("Waiting fragment cc ("+c.cc+") @ "+c.start+" cancelled because another fragment at "+y.end+" is needed"),this.clearWaitingFragment())}}else this.state=n.State.IDLE}this.onTickEnd()},T.clearWaitingFragment=function(){var t=this.waitingData;t&&(this.fragmentTracker.removeFragment(t.frag),this.waitingData=null,this.waitingVideoCC=-1,this.state=n.State.IDLE)},T.resetLoadingState=function(){this.clearWaitingFragment(),t.prototype.resetLoadingState.call(this)},T.onTickEnd=function(){var t=this.media;t&&t.readyState&&(this.lastCurrentTime=t.currentTime)},T.doTickIdle=function(){var t,e,r=this.hls,i=this.levels,s=this.media,o=this.trackId,l=r.config;if(i&&i[o]&&(s||!this.startFragRequested&&l.startFragPrefetch)){var c=i[o].details;if(!c||c.live&&this.levelLastLoaded!==o||this.waitForCdnTuneIn(c))this.state=n.State.WAITING_TRACK;else{var h=this.mediaBuffer?this.mediaBuffer:this.media;this.bufferFlushed&&h&&(this.bufferFlushed=!1,this.afterBufferFlushed(h,d.ElementaryStreamTypes.AUDIO,u.PlaylistLevelType.AUDIO));var f=this.getFwdBufferInfo(h,u.PlaylistLevelType.AUDIO);if(null!==f){var g=this.getFwdBufferInfo(this.videoBuffer?this.videoBuffer:this.media,u.PlaylistLevelType.MAIN),v=f.len,p=this.getMaxBufferLength(null==g?void 0:g.len),m=this.audioSwitch;if(!(v>=p)||m){if(!m&&this._streamEnded(f,c))return r.trigger(a.Events.BUFFER_EOS,{type:"audio"}),void(this.state=n.State.ENDED);var y=c.fragments[0].start,E=f.end;if(m&&s){var T=this.getLoadPosition();E=T,c.PTSKnown&&T<y&&(f.end>y||f.nextStart)&&(this.log("Alt audio track ahead of main track, seek to start of alt audio track"),s.currentTime=y+.05)}if(!(g&&E>g.end+c.targetduration)&&(g&&g.len||!f.len)){var S=this.getNextFragment(E,c);S?"identity"!==(null===(t=S.decryptdata)||void 0===t?void 0:t.keyFormat)||null!==(e=S.decryptdata)&&void 0!==e&&e.key?this.loadFragment(S,c,E):this.loadKey(S,c):this.bufferFlushed=!0}}}}}},T.getMaxBufferLength=function(e){var r=t.prototype.getMaxBufferLength.call(this);return e?Math.max(r,e):r},T.onMediaDetaching=function(){this.videoBuffer=null,t.prototype.onMediaDetaching.call(this)},T.onAudioTracksUpdated=function(t,e){var r=e.audioTracks;this.resetTransmuxer(),this.levels=r.map((function(t){return new l.Level(t)}))},T.onAudioTrackSwitching=function(t,e){var r=!!e.url;this.trackId=e.id;var i=this.fragCurrent;null!=i&&i.loader&&i.loader.abort(),this.fragCurrent=null,this.clearWaitingFragment(),r?this.setInterval(100):this.resetTransmuxer(),r?(this.audioSwitch=!0,this.state=n.State.IDLE):this.state=n.State.STOPPED,this.tick()},T.onManifestLoading=function(){this.mainDetails=null,this.fragmentTracker.removeAllFragments(),this.startPosition=this.lastCurrentTime=0,this.bufferFlushed=!1},T.onLevelLoaded=function(t,e){this.mainDetails=e.details,null!==this.cachedTrackLoadedData&&(this.hls.trigger(a.Events.AUDIO_TRACK_LOADED,this.cachedTrackLoadedData),this.cachedTrackLoadedData=null)},T.onAudioTrackLoaded=function(t,e){var r;if(null!=this.mainDetails){var i=this.levels,a=e.details,s=e.id;if(i){this.log("Track "+s+" loaded ["+a.startSN+","+a.endSN+"],duration:"+a.totalduration);var o=i[s],l=0;if(a.live||null!==(r=o.details)&&void 0!==r&&r.live){var u=this.mainDetails;if(a.fragments[0]||(a.deltaUpdateFailed=!0),a.deltaUpdateFailed||!u)return;!o.details&&a.hasProgramDateTime&&u.hasProgramDateTime?((0,v.alignMediaPlaylistByPDT)(a,u),l=a.fragments[0].start):l=this.alignPlaylists(a,o.details)}o.details=a,this.levelLastLoaded=s,this.startFragRequested||!this.mainDetails&&a.live||this.setStartPosition(o.details,l),this.state!==n.State.WAITING_TRACK||this.waitForCdnTuneIn(a)||(this.state=n.State.IDLE),this.tick()}else this.warn("Audio tracks were reset while loading level "+s)}else this.cachedTrackLoadedData=e},T._handleFragmentLoadProgress=function(t){var e,r=t.frag,i=t.part,a=t.payload,s=this.config,o=this.trackId,l=this.levels;if(l){var d=l[o];console.assert(d,"Audio track is defined on fragment load progress");var g=d.details;console.assert(g,"Audio track details are defined on fragment load progress");var v=s.defaultAudioCodec||d.audioCodec||"mp4a.40.2",p=this.transmuxer;p||(p=this.transmuxer=new h.default(this.hls,u.PlaylistLevelType.AUDIO,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)));var m=this.initPTS[r.cc],y=null===(e=r.initSegment)||void 0===e?void 0:e.data;if(void 0!==m){var E=i?i.index:-1,T=-1!==E,S=new f.ChunkMetadata(r.level,r.sn,r.stats.chunkCount,a.byteLength,E,T);p.push(a,y,v,"",r,i,g.totalduration,!1,S,m)}else this.log("Unknown video PTS for cc "+r.cc+", waiting for video PTS before demuxing audio frag "+r.sn+" of ["+g.startSN+" ,"+g.endSN+"],track "+o),(this.waitingData=this.waitingData||{frag:r,part:i,cache:new c.default,complete:!1}).cache.push(new Uint8Array(a)),this.waitingVideoCC=this.videoTrackCC,this.state=n.State.WAITING_INIT_PTS}else this.warn("Audio tracks were reset while fragment load was in progress. Fragment "+r.sn+" of level "+r.level+" will not be buffered")},T._handleFragmentLoadComplete=function(e){this.waitingData?this.waitingData.complete=!0:t.prototype._handleFragmentLoadComplete.call(this,e)},T.onBufferReset=function(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1},T.onBufferCreated=function(t,e){var r=e.tracks.audio;r&&(this.mediaBuffer=r.buffer||null),e.tracks.video&&(this.videoBuffer=e.tracks.video.buffer||null)},T.onFragBuffered=function(t,e){var r,i=e.frag,n=e.part;i.type===u.PlaylistLevelType.AUDIO?this.fragContextChanged(i)?this.warn("Fragment "+i.sn+(n?" p: "+n.index:"")+" of level "+i.level+" finished buffering, but was aborted. state: "+this.state+", audioSwitch: "+this.audioSwitch):("initSegment"!==i.sn&&(this.fragPrevious=i,this.audioSwitch&&(this.audioSwitch=!1,this.hls.trigger(a.Events.AUDIO_TRACK_SWITCHED,{id:this.trackId}))),this.fragBufferedComplete(i,n)):this.loadedmetadata||i.type!==u.PlaylistLevelType.MAIN||null!==(r=this.videoBuffer||this.media)&&void 0!==r&&r.buffered.length&&(this.loadedmetadata=!0)},T.onError=function(e,r){switch(r.details){case p.ErrorDetails.FRAG_LOAD_ERROR:case p.ErrorDetails.FRAG_LOAD_TIMEOUT:case p.ErrorDetails.KEY_LOAD_ERROR:case p.ErrorDetails.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(u.PlaylistLevelType.AUDIO,r);break;case p.ErrorDetails.AUDIO_TRACK_LOAD_ERROR:case p.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT:this.state!==n.State.ERROR&&this.state!==n.State.STOPPED&&(this.state=r.fatal?n.State.ERROR:n.State.IDLE,this.warn(r.details+" while loading frag, switching to "+this.state+" state"));break;case p.ErrorDetails.BUFFER_FULL_ERROR:if("audio"===r.parent&&(this.state===n.State.PARSING||this.state===n.State.PARSED)){var i=!0,a=this.getFwdBufferInfo(this.mediaBuffer,u.PlaylistLevelType.AUDIO);a&&a.len>.5&&(i=!this.reduceMaxBufferLength(a.len)),i&&(this.warn("Buffer full error also media.currentTime is not buffered, flush audio buffer"),this.fragCurrent=null,t.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio")),this.resetLoadingState()}}},T.onBufferFlushed=function(t,e){e.type===d.ElementaryStreamTypes.AUDIO&&(this.bufferFlushed=!0)},T._handleTransmuxComplete=function(t){var e,r="audio",i=this.hls,s=t.remuxResult,o=t.chunkMeta,l=this.getCurrentContext(o);if(!l)return this.warn("The loading context changed while buffering fragment "+o.sn+" of level "+o.level+". This chunk will not be buffered."),void this.resetStartWhenNotLoaded(o.level);var u=l.frag,c=l.part,h=l.level.details,f=s.audio,g=s.text,v=s.id3,p=s.initSegment;if(!this.fragContextChanged(u)&&h){if(this.state=n.State.PARSING,this.audioSwitch&&f&&this.completeAudioSwitch(),null!=p&&p.tracks&&(this._bufferInitSegment(p.tracks,u,o),i.trigger(a.Events.FRAG_PARSING_INIT_SEGMENT,{frag:u,id:r,tracks:p.tracks})),f){var y=f.startPTS,E=f.endPTS,T=f.startDTS,S=f.endDTS;c&&(c.elementaryStreams[d.ElementaryStreamTypes.AUDIO]={startPTS:y,endPTS:E,startDTS:T,endDTS:S}),u.setElementaryStreamInfo(d.ElementaryStreamTypes.AUDIO,y,E,T,S),this.bufferFragmentData(f,u,c,o)}if(null!=v&&null!==(e=v.samples)&&void 0!==e&&e.length){var b=m({id:r,frag:u,details:h},v);i.trigger(a.Events.FRAG_PARSING_METADATA,b)}if(g){var D=m({id:r,frag:u,details:h},g);i.trigger(a.Events.FRAG_PARSING_USERDATA,D)}}},T._bufferInitSegment=function(t,e,r){if(this.state===n.State.PARSING){t.video&&delete t.video;var i=t.audio;if(i){i.levelCodec=i.codec,i.id="audio",this.log("Init audio buffer, container:"+i.container+", codecs[parsed]=["+i.codec+"]"),this.hls.trigger(a.Events.BUFFER_CODECS,t);var s=i.initSegment;if(null!=s&&s.byteLength){var o={type:"audio",frag:e,part:null,chunkMeta:r,parent:e.type,data:s};this.hls.trigger(a.Events.BUFFER_APPENDING,o)}this.tick()}}},T.loadFragment=function(e,r,a){var s=this.fragmentTracker.getState(e);this.fragCurrent=e,(this.audioSwitch||s===o.FragmentState.NOT_LOADED||s===o.FragmentState.PARTIAL)&&("initSegment"===e.sn?this._loadInitSegment(e):r.live&&!(0,i.isFiniteNumber)(this.initPTS[e.cc])?(this.log("Waiting for video PTS in continuity counter "+e.cc+" of live stream before loading audio fragment "+e.sn+" of level "+this.trackId),this.state=n.State.WAITING_INIT_PTS):(this.startFragRequested=!0,t.prototype.loadFragment.call(this,e,r,a)))},T.completeAudioSwitch=function(){var e=this.hls,r=this.media,i=this.trackId;r&&(this.log("Switching audio track : flushing all audio"),t.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio")),this.audioSwitch=!1,e.trigger(a.Events.AUDIO_TRACK_SWITCHED,{id:i})},E}(n.default)},"./src/controller/audio-track-controller.ts":
/*!**************************************************!*\
  !*** ./src/controller/audio-track-controller.ts ***!
  \**************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>u});var i=r(/*! ../events */"./src/events.ts"),n=r(/*! ../errors */"./src/errors.ts"),a=r(/*! ./base-playlist-controller */"./src/controller/base-playlist-controller.ts"),s=r(/*! ../types/loader */"./src/types/loader.ts");function o(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function l(t,e){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}const u=function(t){var e,r;function a(e){var r;return(r=t.call(this,e,"[audio-track-controller]")||this).tracks=[],r.groupId=null,r.tracksInGroup=[],r.trackId=-1,r.trackName="",r.selectDefaultTrack=!0,r.registerListeners(),r}r=t,(e=a).prototype=Object.create(r.prototype),e.prototype.constructor=e,l(e,r);var u,d,c,h=a.prototype;return h.registerListeners=function(){var t=this.hls;t.on(i.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(i.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.on(i.Events.LEVEL_LOADING,this.onLevelLoading,this),t.on(i.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(i.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.on(i.Events.ERROR,this.onError,this)},h.unregisterListeners=function(){var t=this.hls;t.off(i.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(i.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.off(i.Events.LEVEL_LOADING,this.onLevelLoading,this),t.off(i.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(i.Events.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.off(i.Events.ERROR,this.onError,this)},h.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,t.prototype.destroy.call(this)},h.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.trackName="",this.selectDefaultTrack=!0},h.onManifestParsed=function(t,e){this.tracks=e.audioTracks||[]},h.onAudioTrackLoaded=function(t,e){var r=e.id,i=e.details,n=this.tracksInGroup[r];if(n){var a=n.details;n.details=e.details,this.log("audioTrack "+r+" loaded ["+i.startSN+"-"+i.endSN+"]"),r===this.trackId&&(this.retryCount=0,this.playlistLoaded(r,e,a))}else this.warn("Invalid audio track id "+r)},h.onLevelLoading=function(t,e){this.switchLevel(e.level)},h.onLevelSwitching=function(t,e){this.switchLevel(e.level)},h.switchLevel=function(t){var e=this.hls.levels[t];if(null!=e&&e.audioGroupIds){var r=e.audioGroupIds[e.urlId];if(this.groupId!==r){this.groupId=r;var n=this.tracks.filter((function(t){return!r||t.groupId===r}));this.selectDefaultTrack&&!n.some((function(t){return t.default}))&&(this.selectDefaultTrack=!1),this.tracksInGroup=n;var a={audioTracks:n};this.log("Updating audio tracks, "+n.length+' track(s) found in "'+r+'" group-id'),this.hls.trigger(i.Events.AUDIO_TRACKS_UPDATED,a),this.selectInitialTrack()}}},h.onError=function(e,r){t.prototype.onError.call(this,e,r),!r.fatal&&r.context&&r.context.type===s.PlaylistContextType.AUDIO_TRACK&&r.context.id===this.trackId&&r.context.groupId===this.groupId&&this.retryLoadingOrFail(r)},h.setAudioTrack=function(t){var e=this.tracksInGroup;if(t<0||t>=e.length)this.warn("Invalid id passed to audio-track controller");else{this.clearTimer();var r=e[this.trackId];this.log("Now switching to audio-track index "+t);var n=e[t],a=n.id,s=n.groupId,o=void 0===s?"":s,l=n.name,u=n.type,d=n.url;if(this.trackId=t,this.trackName=l,this.selectDefaultTrack=!1,this.hls.trigger(i.Events.AUDIO_TRACK_SWITCHING,{id:a,groupId:o,name:l,type:u,url:d}),!n.details||n.details.live){var c=this.switchParams(n.url,null==r?void 0:r.details);this.loadPlaylist(c)}}},h.selectInitialTrack=function(){var t=this.tracksInGroup;console.assert(t.length,"Initial audio track should be selected when tracks are known");var e=this.trackName,r=this.findTrackId(e)||this.findTrackId();-1!==r?this.setAudioTrack(r):(this.warn("No track found for running audio group-ID: "+this.groupId),this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.MEDIA_ERROR,details:n.ErrorDetails.AUDIO_TRACK_LOAD_ERROR,fatal:!0}))},h.findTrackId=function(t){for(var e=this.tracksInGroup,r=0;r<e.length;r++){var i=e[r];if((!this.selectDefaultTrack||i.default)&&(!t||t===i.name))return i.id}return-1},h.loadPlaylist=function(t){var e=this.tracksInGroup[this.trackId];if(this.shouldLoadTrack(e)){var r=e.id,n=e.groupId,a=e.url;if(t)try{a=t.addDirectives(a)}catch(t){this.warn("Could not construct new URL with HLS Delivery Directives: "+t)}this.log("loading audio-track playlist for id: "+r),this.clearTimer(),this.hls.trigger(i.Events.AUDIO_TRACK_LOADING,{url:a,id:r,groupId:n,deliveryDirectives:t||null})}},u=a,(d=[{key:"audioTracks",get:function(){return this.tracksInGroup}},{key:"audioTrack",get:function(){return this.trackId},set:function(t){this.selectDefaultTrack=!1,this.setAudioTrack(t)}}])&&o(u.prototype,d),c&&o(u,c),Object.defineProperty(u,"prototype",{writable:!1}),a}(a.default)},"./src/controller/base-playlist-controller.ts":
/*!****************************************************!*\
  !*** ./src/controller/base-playlist-controller.ts ***!
  \****************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../types/level */"./src/types/level.ts"),a=r(/*! ./level-helper */"./src/controller/level-helper.ts"),s=r(/*! ../utils/logger */"./src/utils/logger.ts"),o=r(/*! ../errors */"./src/errors.ts"),l=function(){function t(t,e){this.hls=void 0,this.timer=-1,this.canLoad=!1,this.retryCount=0,this.log=void 0,this.warn=void 0,this.log=s.logger.log.bind(s.logger,e+":"),this.warn=s.logger.warn.bind(s.logger,e+":"),this.hls=t}var e=t.prototype;return e.destroy=function(){this.clearTimer(),this.hls=this.log=this.warn=null},e.onError=function(t,e){e.fatal&&e.type===o.ErrorTypes.NETWORK_ERROR&&this.clearTimer()},e.clearTimer=function(){clearTimeout(this.timer),this.timer=-1},e.startLoad=function(){this.canLoad=!0,this.retryCount=0,this.loadPlaylist()},e.stopLoad=function(){this.canLoad=!1,this.clearTimer()},e.switchParams=function(t,e){var r=null==e?void 0:e.renditionReports;if(r)for(var a=0;a<r.length;a++){var s=r[a],o=""+s.URI;if(o===t.slice(-o.length)){var l=parseInt(s["LAST-MSN"]),u=parseInt(s["LAST-PART"]);if(e&&this.hls.config.lowLatencyMode){var d=Math.min(e.age-e.partTarget,e.targetduration);void 0!==u&&d>e.partTarget&&(u+=1)}if((0,i.isFiniteNumber)(l))return new n.HlsUrlParameters(l,(0,i.isFiniteNumber)(u)?u:void 0,n.HlsSkip.No)}}},e.loadPlaylist=function(t){},e.shouldLoadTrack=function(t){return this.canLoad&&t&&!!t.url&&(!t.details||t.details.live)},e.playlistLoaded=function(t,e,r){var i=this,n=e.details,s=e.stats,o=s.loading.end?Math.max(0,self.performance.now()-s.loading.end):0;if(n.advancedDateTime=Date.now()-o,n.live||null!=r&&r.live){if(n.reloaded(r),r&&this.log("live playlist "+t+" "+(n.advanced?"REFRESHED "+n.lastPartSn+"-"+n.lastPartIndex:"MISSED")),r&&n.fragments.length>0&&(0,a.mergeDetails)(r,n),!this.canLoad||!n.live)return;var l,u=void 0,d=void 0;if(n.canBlockReload&&n.endSN&&n.advanced){var c=this.hls.config.lowLatencyMode,h=n.lastPartSn,f=n.endSN,g=n.lastPartIndex,v=h===f;-1!==g?(u=v?f+1:h,d=v?c?0:g:g+1):u=f+1;var p=n.age,m=p+n.ageHeader,y=Math.min(m-n.partTarget,1.5*n.targetduration);if(y>0){if(r&&y>r.tuneInGoal)this.warn("CDN Tune-in goal increased from: "+r.tuneInGoal+" to: "+y+" with playlist age: "+n.age),y=0;else{var E=Math.floor(y/n.targetduration);u+=E,void 0!==d&&(d+=Math.round(y%n.targetduration/n.partTarget)),this.log("CDN Tune-in age: "+n.ageHeader+"s last advanced "+p.toFixed(2)+"s goal: "+y+" skip sn "+E+" to part "+d)}n.tuneInGoal=y}if(l=this.getDeliveryDirectives(n,e.deliveryDirectives,u,d),c||!v)return void this.loadPlaylist(l)}else l=this.getDeliveryDirectives(n,e.deliveryDirectives,u,d);var T=(0,a.computeReloadInterval)(n,s);void 0!==u&&n.canBlockReload&&(T-=n.partTarget||1),this.log("reload live playlist "+t+" in "+Math.round(T)+" ms"),this.timer=self.setTimeout((function(){return i.loadPlaylist(l)}),T)}else this.clearTimer()},e.getDeliveryDirectives=function(t,e,r,i){var a=(0,n.getSkipValue)(t,r);return null!=e&&e.skip&&t.deltaUpdateFailed&&(r=e.msn,i=e.part,a=n.HlsSkip.No),new n.HlsUrlParameters(r,i,a)},e.retryLoadingOrFail=function(t){var e,r=this,i=this.hls.config,n=this.retryCount<i.levelLoadingMaxRetry;if(n)if(this.retryCount++,t.details.indexOf("LoadTimeOut")>-1&&null!==(e=t.context)&&void 0!==e&&e.deliveryDirectives)this.warn("retry playlist loading #"+this.retryCount+' after "'+t.details+'"'),this.loadPlaylist();else{var a=Math.min(Math.pow(2,this.retryCount)*i.levelLoadingRetryDelay,i.levelLoadingMaxRetryTimeout);this.timer=self.setTimeout((function(){return r.loadPlaylist()}),a),this.warn("retry playlist loading #"+this.retryCount+" in "+a+' ms after "'+t.details+'"')}else this.warn('cannot recover from error "'+t.details+'"'),this.clearTimer(),t.fatal=!0;return n},t}()},"./src/controller/base-stream-controller.ts":
/*!**************************************************!*\
  !*** ./src/controller/base-stream-controller.ts ***!
  \**************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{State:()=>b,default:()=>D});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../task-loop */"./src/task-loop.ts"),a=r(/*! ./fragment-tracker */"./src/controller/fragment-tracker.ts"),s=r(/*! ../utils/buffer-helper */"./src/utils/buffer-helper.ts"),o=r(/*! ../utils/logger */"./src/utils/logger.ts"),l=r(/*! ../events */"./src/events.ts"),u=r(/*! ../errors */"./src/errors.ts"),d=r(/*! ../types/transmuxer */"./src/types/transmuxer.ts"),c=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),h=r(/*! ../utils/discontinuities */"./src/utils/discontinuities.ts"),f=r(/*! ./fragment-finders */"./src/controller/fragment-finders.ts"),g=r(/*! ./level-helper */"./src/controller/level-helper.ts"),v=r(/*! ../loader/fragment-loader */"./src/loader/fragment-loader.ts"),p=r(/*! ../crypt/decrypter */"./src/crypt/decrypter.ts"),m=r(/*! ../utils/time-ranges */"./src/utils/time-ranges.ts"),y=r(/*! ../types/loader */"./src/types/loader.ts");function E(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function T(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function S(t,e){return(S=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var b={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_TRACK:"WAITING_TRACK",PARSING:"PARSING",PARSED:"PARSED",ENDED:"ENDED",ERROR:"ERROR",WAITING_INIT_PTS:"WAITING_INIT_PTS",WAITING_LEVEL:"WAITING_LEVEL"},D=function(t){var e,r;function n(e,r,i){var n;return(n=t.call(this)||this).hls=void 0,n.fragPrevious=null,n.fragCurrent=null,n.fragmentTracker=void 0,n.transmuxer=null,n._state=b.STOPPED,n.media=null,n.mediaBuffer=null,n.config=void 0,n.bitrateTest=!1,n.lastCurrentTime=0,n.nextLoadPosition=0,n.startPosition=0,n.loadedmetadata=!1,n.fragLoadError=0,n.retryDate=0,n.levels=null,n.fragmentLoader=void 0,n.levelLastLoaded=null,n.startFragRequested=!1,n.decrypter=void 0,n.initPTS=[],n.onvseeking=null,n.onvended=null,n.logPrefix="",n.log=void 0,n.warn=void 0,n.logPrefix=i,n.log=o.logger.log.bind(o.logger,i+":"),n.warn=o.logger.warn.bind(o.logger,i+":"),n.hls=e,n.fragmentLoader=new v.default(e.config),n.fragmentTracker=r,n.config=e.config,n.decrypter=new p.default(e,e.config),e.on(l.Events.KEY_LOADED,n.onKeyLoaded,T(n)),e.on(l.Events.LEVEL_SWITCHING,n.onLevelSwitching,T(n)),n}r=t,(e=n).prototype=Object.create(r.prototype),e.prototype.constructor=e,S(e,r);var D,L,A,R=n.prototype;return R.doTick=function(){this.onTickEnd()},R.onTickEnd=function(){},R.startLoad=function(t){},R.stopLoad=function(){this.fragmentLoader.abort();var t=this.fragCurrent;t&&this.fragmentTracker.removeFragment(t),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=b.STOPPED},R._streamEnded=function(t,e){var r=this.fragCurrent,i=this.fragmentTracker;if(!e.live&&r&&this.media&&r.sn>=e.endSN&&!t.nextStart){var n=e.partList;if(null!=n&&n.length){var o=n[n.length-1];return s.BufferHelper.isBuffered(this.media,o.start+o.duration/2)}var l=i.getState(r);return l===a.FragmentState.PARTIAL||l===a.FragmentState.OK}return!1},R.onMediaAttached=function(t,e){var r=this.media=this.mediaBuffer=e.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),r.addEventListener("seeking",this.onvseeking),r.addEventListener("ended",this.onvended);var i=this.config;this.levels&&i.autoStartLoad&&this.state===b.STOPPED&&this.startLoad(i.startPosition)},R.onMediaDetaching=function(){var t=this.media;null!=t&&t.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),t&&this.onvseeking&&this.onvended&&(t.removeEventListener("seeking",this.onvseeking),t.removeEventListener("ended",this.onvended),this.onvseeking=this.onvended=null),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()},R.onMediaSeeking=function(){var t=this.config,e=this.fragCurrent,r=this.media,n=this.mediaBuffer,a=this.state,o=r?r.currentTime:0,l=s.BufferHelper.bufferInfo(n||r,o,t.maxBufferHole);if(this.log("media seeking to "+((0,i.isFiniteNumber)(o)?o.toFixed(3):o)+", state: "+a),a===b.ENDED)this.resetLoadingState();else if(e){var u=t.maxFragLookUpTolerance,d=e.start-u,c=e.start+e.duration+u;if(!l.len||c<l.start||d>l.end){var h=o>c;(o<d||h)&&(h&&e.loader&&(this.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),e.loader.abort()),this.resetLoadingState())}}r&&(this.lastCurrentTime=o),this.loadedmetadata||l.len||(this.nextLoadPosition=this.startPosition=o),this.tickImmediate()},R.onMediaEnded=function(){this.startPosition=this.lastCurrentTime=0},R.onKeyLoaded=function(t,e){if(this.state===b.KEY_LOADING&&e.frag===this.fragCurrent&&this.levels){this.state=b.IDLE;var r=this.levels[e.frag.level].details;r&&this.loadFragment(e.frag,r,e.frag.start)}},R.onLevelSwitching=function(t,e){this.fragLoadError=0},R.onHandlerDestroying=function(){this.stopLoad(),t.prototype.onHandlerDestroying.call(this)},R.onHandlerDestroyed=function(){this.state=b.STOPPED,this.hls.off(l.Events.KEY_LOADED,this.onKeyLoaded,this),this.hls.off(l.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),this.fragmentLoader&&this.fragmentLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.fragmentLoader=this.fragmentTracker=null,t.prototype.onHandlerDestroyed.call(this)},R.loadKey=function(t,e){this.log("Loading key for "+t.sn+" of ["+e.startSN+"-"+e.endSN+"], "+("[stream-controller]"===this.logPrefix?"level":"track")+" "+t.level),this.state=b.KEY_LOADING,this.fragCurrent=t,this.hls.trigger(l.Events.KEY_LOADING,{frag:t})},R.loadFragment=function(t,e,r){this._loadFragForPlayback(t,e,r)},R._loadFragForPlayback=function(t,e,r){var i=this;this._doFragLoad(t,e,r,(function(e){if(i.fragContextChanged(t))return i.warn("Fragment "+t.sn+(e.part?" p: "+e.part.index:"")+" of level "+t.level+" was dropped during download."),void i.fragmentTracker.removeFragment(t);t.stats.chunkCount++,i._handleFragmentLoadProgress(e)})).then((function(e){if(e){i.fragLoadError=0;var r=i.state;i.fragContextChanged(t)?(r===b.FRAG_LOADING||!i.fragCurrent&&r===b.PARSING)&&(i.fragmentTracker.removeFragment(t),i.state=b.IDLE):("payload"in e&&(i.log("Loaded fragment "+t.sn+" of level "+t.level),i.hls.trigger(l.Events.FRAG_LOADED,e)),i._handleFragmentLoadComplete(e))}})).catch((function(e){i.state!==b.STOPPED&&i.state!==b.ERROR&&(i.warn(e),i.resetFragmentLoading(t))}))},R.flushMainBuffer=function(t,e,r){if(void 0===r&&(r=null),t-e){var i={startOffset:t,endOffset:e,type:r};this.fragLoadError=0,this.hls.trigger(l.Events.BUFFER_FLUSHING,i)}},R._loadInitSegment=function(t){var e=this;this._doFragLoad(t).then((function(r){if(!r||e.fragContextChanged(t)||!e.levels)throw new Error("init load aborted");return r})).then((function(r){var i=e.hls,n=r.payload,a=t.decryptdata;if(n&&n.byteLength>0&&a&&a.key&&a.iv&&"AES-128"===a.method){var s=self.performance.now();return e.decrypter.webCryptoDecrypt(new Uint8Array(n),a.key.buffer,a.iv.buffer).then((function(e){var n=self.performance.now();return i.trigger(l.Events.FRAG_DECRYPTED,{frag:t,payload:e,stats:{tstart:s,tdecrypt:n}}),r.payload=e,r}))}return r})).then((function(r){var i=e.fragCurrent,n=e.hls,a=e.levels;if(!a)throw new Error("init load aborted, missing levels");var s=a[t.level].details;console.assert(s,"Level details are defined when init segment is loaded");var o=t.stats;e.state=b.IDLE,e.fragLoadError=0,t.data=new Uint8Array(r.payload),o.parsing.start=o.buffering.start=self.performance.now(),o.parsing.end=o.buffering.end=self.performance.now(),r.frag===i&&n.trigger(l.Events.FRAG_BUFFERED,{stats:o,frag:i,part:null,id:t.type}),e.tick()})).catch((function(r){e.state!==b.STOPPED&&e.state!==b.ERROR&&(e.warn(r),e.resetFragmentLoading(t))}))},R.fragContextChanged=function(t){var e=this.fragCurrent;return!t||!e||t.level!==e.level||t.sn!==e.sn||t.urlId!==e.urlId},R.fragBufferedComplete=function(t,e){var r,i,n=this.mediaBuffer?this.mediaBuffer:this.media;this.log("Buffered "+t.type+" sn: "+t.sn+(e?" part: "+e.index:"")+" of "+("[stream-controller]"===this.logPrefix?"level":"track")+" "+t.level+" "+(n?m.default.toString(s.BufferHelper.getBuffered(n)):"(detached)")),this.state=b.IDLE,n&&(!this.loadedmetadata&&t.type==y.PlaylistLevelType.MAIN&&n.buffered.length&&(null===(r=this.fragCurrent)||void 0===r?void 0:r.sn)===(null===(i=this.fragPrevious)||void 0===i?void 0:i.sn)&&(this.loadedmetadata=!0,this.seekToStartPos()),this.tick())},R.seekToStartPos=function(){},R._handleFragmentLoadComplete=function(t){var e=this.transmuxer;if(e){var r=t.frag,i=t.part,n=t.partsLoaded,a=!n||0===n.length||n.some((function(t){return!t})),s=new d.ChunkMetadata(r.level,r.sn,r.stats.chunkCount+1,0,i?i.index:-1,!a);e.flush(s)}},R._handleFragmentLoadProgress=function(t){},R._doFragLoad=function(t,e,r,n){var a=this;if(void 0===r&&(r=null),!this.levels)throw new Error("frag load aborted, missing levels");if(r=Math.max(t.start,r||0),this.config.lowLatencyMode&&e){var s=e.partList;if(s&&n){r>t.end&&e.fragmentHint&&(t=e.fragmentHint);var o=this.getNextPart(s,t,r);if(o>-1){var u=s[o];return this.log("Loading part sn: "+t.sn+" p: "+u.index+" cc: "+t.cc+" of playlist ["+e.startSN+"-"+e.endSN+"] parts [0-"+o+"-"+(s.length-1)+"] "+("[stream-controller]"===this.logPrefix?"level":"track")+": "+t.level+", target: "+parseFloat(r.toFixed(3))),this.nextLoadPosition=u.start+u.duration,this.state=b.FRAG_LOADING,this.hls.trigger(l.Events.FRAG_LOADING,{frag:t,part:s[o],targetBufferTime:r}),this.doFragPartsLoad(t,s,o,n).catch((function(t){return a.handleFragLoadError(t)}))}if(!t.url||this.loadedEndOfParts(s,r))return Promise.resolve(null)}}return this.log("Loading fragment "+t.sn+" cc: "+t.cc+" "+(e?"of ["+e.startSN+"-"+e.endSN+"] ":"")+("[stream-controller]"===this.logPrefix?"level":"track")+": "+t.level+", target: "+parseFloat(r.toFixed(3))),(0,i.isFiniteNumber)(t.sn)&&!this.bitrateTest&&(this.nextLoadPosition=t.start+t.duration),this.state=b.FRAG_LOADING,this.hls.trigger(l.Events.FRAG_LOADING,{frag:t,targetBufferTime:r}),this.fragmentLoader.load(t,n).catch((function(t){return a.handleFragLoadError(t)}))},R.doFragPartsLoad=function(t,e,r,i){var n=this;return new Promise((function(a,s){var o=[];!function r(u){var d=e[u];n.fragmentLoader.loadPart(t,d,i).then((function(i){o[d.index]=i;var s=i.part;n.hls.trigger(l.Events.FRAG_LOADED,i);var c=e[u+1];if(!c||c.fragment!==t)return a({frag:t,part:s,partsLoaded:o});r(u+1)})).catch(s)}(r)}))},R.handleFragLoadError=function(t){var e=t.data;return e&&e.details===u.ErrorDetails.INTERNAL_ABORTED?this.handleFragLoadAborted(e.frag,e.part):this.hls.trigger(l.Events.ERROR,e),null},R._handleTransmuxerFlush=function(t){var e=this.getCurrentContext(t);if(e&&this.state===b.PARSING){var r=e.frag,i=e.part,n=e.level,a=self.performance.now();r.stats.parsing.end=a,i&&(i.stats.parsing.end=a),this.updateLevelTiming(r,i,n,t.partial)}else this.fragCurrent||(this.state=b.IDLE)},R.getCurrentContext=function(t){var e=this.levels,r=t.level,i=t.sn,n=t.part;if(!e||!e[r])return this.warn("Levels object was unset while buffering fragment "+i+" of level "+r+". The current chunk will not be buffered."),null;var a=e[r],s=n>-1?(0,g.getPartWith)(a,i,n):null,o=s?s.fragment:(0,g.getFragmentWithSN)(a,i,this.fragCurrent);return o?{frag:o,part:s,level:a}:null},R.bufferFragmentData=function(t,e,r,i){if(t&&this.state===b.PARSING){var n=t.data1,a=t.data2,s=n;if(n&&a&&(s=(0,c.appendUint8Array)(n,a)),s&&s.length){var o={type:t.type,frag:e,part:r,chunkMeta:i,parent:e.type,data:s};this.hls.trigger(l.Events.BUFFER_APPENDING,o),t.dropped&&t.independent&&!r&&this.flushBufferGap(e)}}},R.flushBufferGap=function(t){var e=this.media;if(e)if(s.BufferHelper.isBuffered(e,e.currentTime)){var r=e.currentTime,i=s.BufferHelper.bufferInfo(e,r,0),n=t.duration,a=Math.min(2*this.config.maxFragLookUpTolerance,.25*n),o=Math.max(Math.min(t.start-a,i.end-a),r+a);t.start-o>a&&this.flushMainBuffer(o,t.start)}else this.flushMainBuffer(0,t.start)},R.getFwdBufferInfo=function(t,e){var r=this.config,n=this.getLoadPosition();if(!(0,i.isFiniteNumber)(n))return null;var a=s.BufferHelper.bufferInfo(t,n,r.maxBufferHole);if(0===a.len&&void 0!==a.nextStart){var o=this.fragmentTracker.getBufferedFrag(n,e);if(o&&a.nextStart<o.end)return s.BufferHelper.bufferInfo(t,n,Math.max(a.nextStart,r.maxBufferHole))}return a},R.getMaxBufferLength=function(t){var e,r=this.config;return e=t?Math.max(8*r.maxBufferSize/t,r.maxBufferLength):r.maxBufferLength,Math.min(e,r.maxMaxBufferLength)},R.reduceMaxBufferLength=function(t){var e=this.config,r=t||e.maxBufferLength;return e.maxMaxBufferLength>=r&&(e.maxMaxBufferLength/=2,this.warn("Reduce max buffer length to "+e.maxMaxBufferLength+"s"),!0)},R.getNextFragment=function(t,e){var r=e.fragments,i=r.length;if(!i)return null;var n,a=this.config,s=r[0].start;if(e.live){var o=a.initialLiveManifestSize;if(i<o)return this.warn("Not enough fragments to start playback (have: "+i+", need: "+o+")"),null;e.PTSKnown||this.startFragRequested||-1!==this.startPosition||(n=this.getInitialLiveFragment(e,r),this.startPosition=n?this.hls.liveSyncPosition||n.start:t)}else t<=s&&(n=r[0]);if(!n){var l=a.lowLatencyMode?e.partEnd:e.fragmentEnd;n=this.getFragmentAtPosition(t,l,e)}return this.mapToInitFragWhenRequired(n)},R.mapToInitFragWhenRequired=function(t){return null==t||!t.initSegment||null!=t&&t.initSegment.data||this.bitrateTest?t:t.initSegment},R.getNextPart=function(t,e,r){for(var i=-1,n=!1,a=!0,s=0,o=t.length;s<o;s++){var l=t[s];if(a=a&&!l.independent,i>-1&&r<l.start)break;var u=l.loaded;!u&&(n||l.independent||a)&&l.fragment===e&&(i=s),n=u}return i},R.loadedEndOfParts=function(t,e){var r=t[t.length-1];return r&&e>r.start&&r.loaded},R.getInitialLiveFragment=function(t,e){var r=this.fragPrevious,i=null;if(r){if(t.hasProgramDateTime&&(this.log("Live playlist, switching playlist, load frag with same PDT: "+r.programDateTime),i=(0,f.findFragmentByPDT)(e,r.endProgramDateTime,this.config.maxFragLookUpTolerance)),!i){var n=r.sn+1;if(n>=t.startSN&&n<=t.endSN){var a=e[n-t.startSN];r.cc===a.cc&&(i=a,this.log("Live playlist, switching playlist, load frag with next SN: "+i.sn))}i||(i=(0,f.findFragWithCC)(e,r.cc))&&this.log("Live playlist, switching playlist, load frag with same CC: "+i.sn)}}else{var s=this.hls.liveSyncPosition;null!==s&&(i=this.getFragmentAtPosition(s,this.bitrateTest?t.fragmentEnd:t.edge,t))}return i},R.getFragmentAtPosition=function(t,e,r){var i,n=this.config,s=this.fragPrevious,o=r.fragments,l=r.endSN,u=r.fragmentHint,d=n.maxFragLookUpTolerance,c=!!(n.lowLatencyMode&&r.partList&&u);if(c&&u&&!this.bitrateTest&&(o=o.concat(u),l=u.sn),t<e){var h=t>e-d?0:d;i=(0,f.findFragmentByPTS)(s,o,t,h)}else i=o[o.length-1];if(i){var g=i.sn-r.startSN;if(this.fragmentTracker.getState(i)===a.FragmentState.OK&&(s=i),s&&i.sn===s.sn&&!c&&s&&i.level===s.level){var v=o[g+1];i.sn<l&&this.fragmentTracker.getState(v)!==a.FragmentState.OK?(this.log("SN "+i.sn+" just loaded, load next one: "+v.sn),i=v):i=null}}return i},R.synchronizeToLiveEdge=function(t){var e=this.config,r=this.media;if(r){var i=this.hls.liveSyncPosition,n=r.currentTime,a=t.fragments[0].start,s=t.edge,o=n>=a-e.maxFragLookUpTolerance&&n<=s;if(null!==i&&r.duration>i&&(n<i||!o)){var l=void 0!==e.liveMaxLatencyDuration?e.liveMaxLatencyDuration:e.liveMaxLatencyDurationCount*t.targetduration;(!o&&r.readyState<4||n<s-l)&&(this.loadedmetadata||(this.nextLoadPosition=i),r.readyState&&(this.warn("Playback: "+n.toFixed(3)+" is located too far from the end of live sliding playlist: "+s+", reset currentTime to : "+i.toFixed(3)),r.currentTime=i))}}},R.alignPlaylists=function(t,e){var r=this.levels,n=this.levelLastLoaded,a=this.fragPrevious,s=null!==n?r[n]:null,o=t.fragments.length;if(!o)return this.warn("No fragments in live playlist"),0;var l=t.fragments[0].start,u=!e,d=t.alignedSliding&&(0,i.isFiniteNumber)(l);if(u||!d&&!l){(0,h.alignStream)(a,s,t);var c=t.fragments[0].start;return this.log("Live playlist sliding: "+c.toFixed(2)+" start-sn: "+(e?e.startSN:"na")+"->"+t.startSN+" prev-sn: "+(a?a.sn:"na")+" fragments: "+o),c}return l},R.waitForCdnTuneIn=function(t){return t.live&&t.canBlockReload&&t.partTarget&&t.tuneInGoal>Math.max(t.partHoldBack,3*t.partTarget)},R.setStartPosition=function(t,e){var r=this.startPosition;if(r<e&&(r=-1),-1===r||-1===this.lastCurrentTime){var n=t.startTimeOffset;(0,i.isFiniteNumber)(n)?(r=e+n,n<0&&(r+=t.totalduration),r=Math.min(Math.max(e,r),e+t.totalduration),this.log("Start time offset "+n+" found in playlist, adjust startPosition to "+r),this.startPosition=r):t.live?r=this.hls.liveSyncPosition||e:this.startPosition=r=0,this.lastCurrentTime=r}this.nextLoadPosition=r},R.getLoadPosition=function(){var t=this.media,e=0;return this.loadedmetadata&&t?e=t.currentTime:this.nextLoadPosition&&(e=this.nextLoadPosition),e},R.handleFragLoadAborted=function(t,e){this.transmuxer&&"initSegment"!==t.sn&&t.stats.aborted&&(this.warn("Fragment "+t.sn+(e?" part"+e.index:"")+" of level "+t.level+" was aborted"),this.resetFragmentLoading(t))},R.resetFragmentLoading=function(t){this.fragCurrent&&(this.fragContextChanged(t)||this.state===b.FRAG_LOADING_WAITING_RETRY)||(this.state=b.IDLE)},R.onFragmentOrKeyLoadError=function(t,e){if(!e.fatal){var r=e.frag;if(r&&r.type===t){var i=this.fragCurrent;console.assert(i&&r.sn===i.sn&&r.level===i.level&&r.urlId===i.urlId,"Frag load error must match current frag to retry");var n=this.config;if(this.fragLoadError+1<=n.fragLoadingMaxRetry){this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition);var a=Math.min(Math.pow(2,this.fragLoadError)*n.fragLoadingRetryDelay,n.fragLoadingMaxRetryTimeout);this.warn("Fragment "+r.sn+" of "+t+" "+r.level+" failed to load, retrying in "+a+"ms"),this.retryDate=self.performance.now()+a,this.fragLoadError++,this.state=b.FRAG_LOADING_WAITING_RETRY}else e.levelRetry?(t===y.PlaylistLevelType.AUDIO&&(this.fragCurrent=null),this.fragLoadError=0,this.state=b.IDLE):(o.logger.error(e.details+" reaches max retry, redispatch as fatal ..."),e.fatal=!0,this.hls.stopLoad(),this.state=b.ERROR)}}},R.afterBufferFlushed=function(t,e,r){if(t){var i=s.BufferHelper.getBuffered(t);this.fragmentTracker.detectEvictedFragments(e,i,r),this.state===b.ENDED&&this.resetLoadingState()}},R.resetLoadingState=function(){this.fragCurrent=null,this.fragPrevious=null,this.state=b.IDLE},R.resetStartWhenNotLoaded=function(t){if(!this.loadedmetadata){this.startFragRequested=!1;var e=this.levels?this.levels[t].details:null;null!=e&&e.live?(this.startPosition=-1,this.setStartPosition(e,0),this.resetLoadingState()):this.nextLoadPosition=this.startPosition}},R.updateLevelTiming=function(t,e,r,i){var n=this,a=r.details;console.assert(!!a,"level.details must be defined"),Object.keys(t.elementaryStreams).reduce((function(e,s){var o=t.elementaryStreams[s];if(o){var u=o.endPTS-o.startPTS;if(u<=0)return n.warn("Could not parse fragment "+t.sn+" "+s+" duration reliably ("+u+")"),e||!1;var d=i?0:(0,g.updateFragPTSDTS)(a,t,o.startPTS,o.endPTS,o.startDTS,o.endDTS);return n.hls.trigger(l.Events.LEVEL_PTS_UPDATED,{details:a,level:r,drift:d,type:s,frag:t,start:o.startPTS,end:o.endPTS}),!0}return e}),!1)||(this.warn("Found no media in fragment "+t.sn+" of level "+r.id+" resetting transmuxer to fallback to playlist timing"),this.resetTransmuxer()),this.state=b.PARSED,this.hls.trigger(l.Events.FRAG_PARSED,{frag:t,part:e})},R.resetTransmuxer=function(){this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null)},D=n,(L=[{key:"state",get:function(){return this._state},set:function(t){var e=this._state;e!==t&&(this._state=t,this.log(e+"->"+t))}}])&&E(D.prototype,L),A&&E(D,A),Object.defineProperty(D,"prototype",{writable:!1}),n}(n.default)},"./src/controller/buffer-controller.ts":
/*!*********************************************!*\
  !*** ./src/controller/buffer-controller.ts ***!
  \*********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>f});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../utils/logger */"./src/utils/logger.ts"),s=r(/*! ../errors */"./src/errors.ts"),o=r(/*! ../utils/buffer-helper */"./src/utils/buffer-helper.ts"),l=r(/*! ../utils/mediasource-helper */"./src/utils/mediasource-helper.ts"),u=r(/*! ../loader/fragment */"./src/loader/fragment.ts"),d=r(/*! ./buffer-operation-queue */"./src/controller/buffer-operation-queue.ts"),c=(0,l.getMediaSource)(),h=/([ha]vc.)(?:\.[^.,]+)+/,f=function(){function t(t){var e=this;this.details=null,this._objectUrl=null,this.operationQueue=void 0,this.listeners=void 0,this.hls=void 0,this.bufferCodecEventsExpected=0,this._bufferCodecEventsTotal=0,this.media=null,this.mediaSource=null,this.appendError=0,this.tracks={},this.pendingTracks={},this.sourceBuffer=void 0,this._onMediaSourceOpen=function(){var t=e.hls,r=e.media,i=e.mediaSource;a.logger.log("[buffer-controller]: Media source opened"),r&&(e.updateMediaElementDuration(),t.trigger(n.Events.MEDIA_ATTACHED,{media:r})),i&&i.removeEventListener("sourceopen",e._onMediaSourceOpen),e.checkPendingTracks()},this._onMediaSourceClose=function(){a.logger.log("[buffer-controller]: Media source closed")},this._onMediaSourceEnded=function(){a.logger.log("[buffer-controller]: Media source ended")},this.hls=t,this._initSourceBuffer(),this.registerListeners()}var e=t.prototype;return e.hasSourceTypes=function(){return this.getSourceBufferTypes().length>0||Object.keys(this.pendingTracks).length>0},e.destroy=function(){this.unregisterListeners(),this.details=null},e.registerListeners=function(){var t=this.hls;t.on(n.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(n.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(n.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.on(n.Events.BUFFER_RESET,this.onBufferReset,this),t.on(n.Events.BUFFER_APPENDING,this.onBufferAppending,this),t.on(n.Events.BUFFER_CODECS,this.onBufferCodecs,this),t.on(n.Events.BUFFER_EOS,this.onBufferEos,this),t.on(n.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(n.Events.LEVEL_UPDATED,this.onLevelUpdated,this),t.on(n.Events.FRAG_PARSED,this.onFragParsed,this),t.on(n.Events.FRAG_CHANGED,this.onFragChanged,this)},e.unregisterListeners=function(){var t=this.hls;t.off(n.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(n.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(n.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.off(n.Events.BUFFER_RESET,this.onBufferReset,this),t.off(n.Events.BUFFER_APPENDING,this.onBufferAppending,this),t.off(n.Events.BUFFER_CODECS,this.onBufferCodecs,this),t.off(n.Events.BUFFER_EOS,this.onBufferEos,this),t.off(n.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(n.Events.LEVEL_UPDATED,this.onLevelUpdated,this),t.off(n.Events.FRAG_PARSED,this.onFragParsed,this),t.off(n.Events.FRAG_CHANGED,this.onFragChanged,this)},e._initSourceBuffer=function(){this.sourceBuffer={},this.operationQueue=new d.default(this.sourceBuffer),this.listeners={audio:[],video:[],audiovideo:[]}},e.onManifestParsed=function(t,e){var r=2;(e.audio&&!e.video||!e.altAudio)&&(r=1),this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=r,this.details=null,a.logger.log(this.bufferCodecEventsExpected+" bufferCodec event(s) expected")},e.onMediaAttaching=function(t,e){var r=this.media=e.media;if(r&&c){var i=this.mediaSource=new c;i.addEventListener("sourceopen",this._onMediaSourceOpen),i.addEventListener("sourceended",this._onMediaSourceEnded),i.addEventListener("sourceclose",this._onMediaSourceClose),r.src=self.URL.createObjectURL(i),this._objectUrl=r.src}},e.onMediaDetaching=function(){var t=this.media,e=this.mediaSource,r=this._objectUrl;if(e){if(a.logger.log("[buffer-controller]: media source detaching"),"open"===e.readyState)try{e.endOfStream()}catch(t){a.logger.warn("[buffer-controller]: onMediaDetaching: "+t.message+" while calling endOfStream")}this.onBufferReset(),e.removeEventListener("sourceopen",this._onMediaSourceOpen),e.removeEventListener("sourceended",this._onMediaSourceEnded),e.removeEventListener("sourceclose",this._onMediaSourceClose),t&&(r&&self.URL.revokeObjectURL(r),t.src===r?(t.removeAttribute("src"),t.load()):a.logger.warn("[buffer-controller]: media.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={}}this.hls.trigger(n.Events.MEDIA_DETACHED,void 0)},e.onBufferReset=function(){var t=this;this.getSourceBufferTypes().forEach((function(e){var r=t.sourceBuffer[e];try{r&&(t.removeBufferListeners(e),t.mediaSource&&t.mediaSource.removeSourceBuffer(r),t.sourceBuffer[e]=void 0)}catch(t){a.logger.warn("[buffer-controller]: Failed to reset the "+e+" buffer",t)}})),this._initSourceBuffer()},e.onBufferCodecs=function(t,e){var r=this,i=this.getSourceBufferTypes().length;Object.keys(e).forEach((function(t){if(i){var n=r.tracks[t];if(n&&"function"==typeof n.buffer.changeType){var s=e[t],o=s.id,l=s.codec,u=s.levelCodec,d=s.container,c=s.metadata,f=(n.levelCodec||n.codec).replace(h,"$1"),g=(u||l).replace(h,"$1");if(f!==g){var v=d+";codecs="+(u||l);r.appendChangeType(t,v),a.logger.log("[buffer-controller]: switching codec "+f+" to "+g),r.tracks[t]={buffer:n.buffer,codec:l,container:d,levelCodec:u,metadata:c,id:o}}}}else r.pendingTracks[t]=e[t]})),i||(this.bufferCodecEventsExpected=Math.max(this.bufferCodecEventsExpected-1,0),this.mediaSource&&"open"===this.mediaSource.readyState&&this.checkPendingTracks())},e.appendChangeType=function(t,e){var r=this,i=this.operationQueue,n={execute:function(){var n=r.sourceBuffer[t];n&&(a.logger.log("[buffer-controller]: changing "+t+" sourceBuffer type to "+e),n.changeType(e)),i.shiftAndExecuteNext(t)},onStart:function(){},onComplete:function(){},onError:function(e){a.logger.warn("[buffer-controller]: Failed to change "+t+" SourceBuffer type",e)}};i.append(n,t)},e.onBufferAppending=function(t,e){var r=this,i=this.hls,l=this.operationQueue,u=this.tracks,d=e.data,c=e.type,h=e.frag,f=e.part,g=e.chunkMeta,v=g.buffering[c],p=self.performance.now();v.start=p;var m=h.stats.buffering,y=f?f.stats.buffering:null;0===m.start&&(m.start=p),y&&0===y.start&&(y.start=p);var E=u.audio,T="audio"===c&&1===g.id&&"audio/mpeg"===(null==E?void 0:E.container),S={execute:function(){if(v.executeStart=self.performance.now(),T){var t=r.sourceBuffer[c];if(t){var e=h.start-t.timestampOffset;Math.abs(e)>=.1&&(a.logger.log("[buffer-controller]: Updating audio SourceBuffer timestampOffset to "+h.start+" (delta: "+e+") sn: "+h.sn+")"),t.timestampOffset=h.start)}}r.appendExecutor(d,c)},onStart:function(){},onComplete:function(){var t=self.performance.now();v.executeEnd=v.end=t,0===m.first&&(m.first=t),y&&0===y.first&&(y.first=t);var e=r.sourceBuffer,i={};for(var a in e)i[a]=o.BufferHelper.getBuffered(e[a]);r.appendError=0,r.hls.trigger(n.Events.BUFFER_APPENDED,{type:c,frag:h,part:f,chunkMeta:g,parent:h.type,timeRanges:i})},onError:function(t){a.logger.error("[buffer-controller]: Error encountered while trying to append to the "+c+" SourceBuffer",t);var e={type:s.ErrorTypes.MEDIA_ERROR,parent:h.type,details:s.ErrorDetails.BUFFER_APPEND_ERROR,err:t,fatal:!1};t.code===DOMException.QUOTA_EXCEEDED_ERR?e.details=s.ErrorDetails.BUFFER_FULL_ERROR:(r.appendError++,e.details=s.ErrorDetails.BUFFER_APPEND_ERROR,r.appendError>i.config.appendErrorMaxRetry&&(a.logger.error("[buffer-controller]: Failed "+i.config.appendErrorMaxRetry+" times to append segment in sourceBuffer"),e.fatal=!0,i.stopLoad())),i.trigger(n.Events.ERROR,e)}};l.append(S,c)},e.onBufferFlushing=function(t,e){var r=this,i=this.operationQueue,s=function(t){return{execute:r.removeExecutor.bind(r,t,e.startOffset,e.endOffset),onStart:function(){},onComplete:function(){r.hls.trigger(n.Events.BUFFER_FLUSHED,{type:t})},onError:function(e){a.logger.warn("[buffer-controller]: Failed to remove from "+t+" SourceBuffer",e)}}};e.type?i.append(s(e.type),e.type):this.getSourceBufferTypes().forEach((function(t){i.append(s(t),t)}))},e.onFragParsed=function(t,e){var r=this,i=e.frag,s=e.part,o=[],l=s?s.elementaryStreams:i.elementaryStreams;l[u.ElementaryStreamTypes.AUDIOVIDEO]?o.push("audiovideo"):(l[u.ElementaryStreamTypes.AUDIO]&&o.push("audio"),l[u.ElementaryStreamTypes.VIDEO]&&o.push("video")),0===o.length&&a.logger.warn("Fragments must have at least one ElementaryStreamType set. type: "+i.type+" level: "+i.level+" sn: "+i.sn),this.blockBuffers((function(){var t=self.performance.now();i.stats.buffering.end=t,s&&(s.stats.buffering.end=t);var e=s?s.stats:i.stats;r.hls.trigger(n.Events.FRAG_BUFFERED,{frag:i,part:s,stats:e,id:i.type})}),o)},e.onFragChanged=function(t,e){this.flushBackBuffer()},e.onBufferEos=function(t,e){var r=this;this.getSourceBufferTypes().reduce((function(t,i){var n=r.sourceBuffer[i];return e.type&&e.type!==i||n&&!n.ended&&(n.ended=!0,a.logger.log("[buffer-controller]: "+i+" sourceBuffer now EOS")),t&&!(n&&!n.ended)}),!0)&&this.blockBuffers((function(){var t=r.mediaSource;t&&"open"===t.readyState&&t.endOfStream()}))},e.onLevelUpdated=function(t,e){var r=e.details;r.fragments.length&&(this.details=r,this.getSourceBufferTypes().length?this.blockBuffers(this.updateMediaElementDuration.bind(this)):this.updateMediaElementDuration())},e.flushBackBuffer=function(){var t=this.hls,e=this.details,r=this.media,a=this.sourceBuffer;if(r&&null!==e){var s=this.getSourceBufferTypes();if(s.length){var l=e.live&&null!==t.config.liveBackBufferLength?t.config.liveBackBufferLength:t.config.backBufferLength;if((0,i.isFiniteNumber)(l)&&!(l<0)){var u=r.currentTime,d=e.levelTargetDuration,c=Math.max(l,d),h=Math.floor(u/d)*d-c;s.forEach((function(r){var i=a[r];if(i){var s=o.BufferHelper.getBuffered(i);s.length>0&&h>s.start(0)&&(t.trigger(n.Events.BACK_BUFFER_REACHED,{bufferEnd:h}),e.live&&t.trigger(n.Events.LIVE_BACK_BUFFER_REACHED,{bufferEnd:h}),t.trigger(n.Events.BUFFER_FLUSHING,{startOffset:0,endOffset:h,type:r}))}}))}}}},e.updateMediaElementDuration=function(){if(this.details&&this.media&&this.mediaSource&&"open"===this.mediaSource.readyState){var t=this.details,e=this.hls,r=this.media,n=this.mediaSource,s=t.fragments[0].start+t.totalduration,o=r.duration,l=(0,i.isFiniteNumber)(n.duration)?n.duration:0;t.live&&e.config.liveDurationInfinity?(a.logger.log("[buffer-controller]: Media Source duration is set to Infinity"),n.duration=1/0,this.updateSeekableRange(t)):(s>l&&s>o||!(0,i.isFiniteNumber)(o))&&(a.logger.log("[buffer-controller]: Updating Media Source duration to "+s.toFixed(3)),n.duration=s)}},e.updateSeekableRange=function(t){var e=this.mediaSource,r=t.fragments;if(r.length&&t.live&&null!=e&&e.setLiveSeekableRange){var i=Math.max(0,r[0].start),n=Math.max(i,i+t.totalduration);e.setLiveSeekableRange(i,n)}},e.checkPendingTracks=function(){var t=this.bufferCodecEventsExpected,e=this.operationQueue,r=this.pendingTracks,i=Object.keys(r).length;if(i&&!t||2===i){this.createSourceBuffers(r),this.pendingTracks={};var a=this.getSourceBufferTypes();if(0===a.length)return void this.hls.trigger(n.Events.ERROR,{type:s.ErrorTypes.MEDIA_ERROR,details:s.ErrorDetails.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,reason:"could not create source buffer for media codec(s)"});a.forEach((function(t){e.executeNext(t)}))}},e.createSourceBuffers=function(t){var e=this.sourceBuffer,r=this.mediaSource;if(!r)throw Error("createSourceBuffers called when mediaSource was null");var i=0;for(var o in t)if(!e[o]){var l=t[o];if(!l)throw Error("source buffer exists for track "+o+", however track does not");var u=l.levelCodec||l.codec,d=l.container+";codecs="+u;a.logger.log("[buffer-controller]: creating sourceBuffer("+d+")");try{var c=e[o]=r.addSourceBuffer(d),h=o;this.addBufferListener(h,"updatestart",this._onSBUpdateStart),this.addBufferListener(h,"updateend",this._onSBUpdateEnd),this.addBufferListener(h,"error",this._onSBUpdateError),this.tracks[o]={buffer:c,codec:u,container:l.container,levelCodec:l.levelCodec,metadata:l.metadata,id:l.id},i++}catch(t){a.logger.error("[buffer-controller]: error while trying to add sourceBuffer: "+t.message),this.hls.trigger(n.Events.ERROR,{type:s.ErrorTypes.MEDIA_ERROR,details:s.ErrorDetails.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:t,mimeType:d})}}i&&this.hls.trigger(n.Events.BUFFER_CREATED,{tracks:this.tracks})},e._onSBUpdateStart=function(t){this.operationQueue.current(t).onStart()},e._onSBUpdateEnd=function(t){var e=this.operationQueue;e.current(t).onComplete(),e.shiftAndExecuteNext(t)},e._onSBUpdateError=function(t,e){a.logger.error("[buffer-controller]: "+t+" SourceBuffer error",e),this.hls.trigger(n.Events.ERROR,{type:s.ErrorTypes.MEDIA_ERROR,details:s.ErrorDetails.BUFFER_APPENDING_ERROR,fatal:!1});var r=this.operationQueue.current(t);r&&r.onError(e)},e.removeExecutor=function(t,e,r){var n=this.media,s=this.mediaSource,o=this.operationQueue,l=this.sourceBuffer[t];if(!n||!s||!l)return a.logger.warn("[buffer-controller]: Attempting to remove from the "+t+" SourceBuffer, but it does not exist"),void o.shiftAndExecuteNext(t);var u=(0,i.isFiniteNumber)(n.duration)?n.duration:1/0,d=(0,i.isFiniteNumber)(s.duration)?s.duration:1/0,c=Math.max(0,e),h=Math.min(r,u,d);h>c?(a.logger.log("[buffer-controller]: Removing ["+c+","+h+"] from the "+t+" SourceBuffer"),console.assert(!l.updating,t+" sourceBuffer must not be updating"),l.remove(c,h)):o.shiftAndExecuteNext(t)},e.appendExecutor=function(t,e){var r=this.operationQueue,i=this.sourceBuffer[e];if(!i)return a.logger.warn("[buffer-controller]: Attempting to append to the "+e+" SourceBuffer, but it does not exist"),void r.shiftAndExecuteNext(e);i.ended=!1,console.assert(!i.updating,e+" sourceBuffer must not be updating"),i.appendBuffer(t)},e.blockBuffers=function(t,e){var r=this;if(void 0===e&&(e=this.getSourceBufferTypes()),!e.length)return a.logger.log("[buffer-controller]: Blocking operation requested, but no SourceBuffers exist"),void Promise.resolve().then(t);var i=this.operationQueue,n=e.map((function(t){return i.appendBlocker(t)}));Promise.all(n).then((function(){t(),e.forEach((function(t){var e=r.sourceBuffer[t];e&&e.updating||i.shiftAndExecuteNext(t)}))}))},e.getSourceBufferTypes=function(){return Object.keys(this.sourceBuffer)},e.addBufferListener=function(t,e,r){var i=this.sourceBuffer[t];if(i){var n=r.bind(this,t);this.listeners[t].push({event:e,listener:n}),i.addEventListener(e,n)}},e.removeBufferListeners=function(t){var e=this.sourceBuffer[t];e&&this.listeners[t].forEach((function(t){e.removeEventListener(t.event,t.listener)}))},t}()},"./src/controller/buffer-operation-queue.ts":
/*!**************************************************!*\
  !*** ./src/controller/buffer-operation-queue.ts ***!
  \**************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});var i=r(/*! ../utils/logger */"./src/utils/logger.ts"),n=function(){function t(t){this.buffers=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.buffers=t}var e=t.prototype;return e.append=function(t,e){var r=this.queues[e];r.push(t),1===r.length&&this.buffers[e]&&this.executeNext(e)},e.insertAbort=function(t,e){this.queues[e].unshift(t),this.executeNext(e)},e.appendBlocker=function(t){var e,r=new Promise((function(t){e=t})),i={execute:e,onStart:function(){},onComplete:function(){},onError:function(){}};return this.append(i,t),r},e.executeNext=function(t){var e=this.buffers,r=this.queues,n=e[t],a=r[t];if(a.length){var s=a[0];try{s.execute()}catch(e){i.logger.warn("[buffer-operation-queue]: Unhandled exception executing the current operation"),s.onError(e),n&&n.updating||(a.shift(),this.executeNext(t))}}},e.shiftAndExecuteNext=function(t){this.queues[t].shift(),this.executeNext(t)},e.current=function(t){return this.queues[t][0]},t}()},"./src/controller/cap-level-controller.ts":
/*!************************************************!*\
  !*** ./src/controller/cap-level-controller.ts ***!
  \************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>a});var i=r(/*! ../events */"./src/events.ts");function n(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}const a=function(){function t(t){this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.hls=void 0,this.streamController=void 0,this.clientRect=void 0,this.hls=t,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}var e,r,a,s=t.prototype;return s.setStreamController=function(t){this.streamController=t},s.destroy=function(){this.unregisterListener(),this.hls.config.capLevelToPlayerSize&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null},s.registerListeners=function(){var t=this.hls;t.on(i.Events.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),t.on(i.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(i.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.on(i.Events.BUFFER_CODECS,this.onBufferCodecs,this),t.on(i.Events.MEDIA_DETACHING,this.onMediaDetaching,this)},s.unregisterListener=function(){var t=this.hls;t.off(i.Events.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),t.off(i.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(i.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.off(i.Events.BUFFER_CODECS,this.onBufferCodecs,this),t.off(i.Events.MEDIA_DETACHING,this.onMediaDetaching,this)},s.onFpsDropLevelCapping=function(e,r){t.isLevelAllowed(r.droppedLevel,this.restrictedLevels)&&this.restrictedLevels.push(r.droppedLevel)},s.onMediaAttaching=function(t,e){this.media=e.media instanceof HTMLVideoElement?e.media:null},s.onManifestParsed=function(t,e){var r=this.hls;this.restrictedLevels=[],this.firstLevel=e.firstLevel,r.config.capLevelToPlayerSize&&e.video&&this.startCapping()},s.onBufferCodecs=function(t,e){this.hls.config.capLevelToPlayerSize&&e.video&&this.startCapping()},s.onMediaDetaching=function(){this.stopCapping()},s.detectPlayerSize=function(){if(this.media&&this.mediaHeight>0&&this.mediaWidth>0){var t=this.hls.levels;if(t.length){var e=this.hls;e.autoLevelCapping=this.getMaxLevel(t.length-1),e.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=e.autoLevelCapping}}},s.getMaxLevel=function(e){var r=this,i=this.hls.levels;if(!i.length)return-1;var n=i.filter((function(i,n){return t.isLevelAllowed(n,r.restrictedLevels)&&n<=e}));return this.clientRect=null,t.getMaxLevelByMediaSize(n,this.mediaWidth,this.mediaHeight)},s.startCapping=function(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,this.hls.firstLevel=this.getMaxLevel(this.firstLevel),self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())},s.stopCapping=function(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)},s.getDimensions=function(){if(this.clientRect)return this.clientRect;var t=this.media,e={width:0,height:0};if(t){var r=t.getBoundingClientRect();e.width=r.width,e.height=r.height,e.width||e.height||(e.width=r.right-r.left||t.width||0,e.height=r.bottom-r.top||t.height||0)}return this.clientRect=e,e},t.isLevelAllowed=function(t,e){return void 0===e&&(e=[]),-1===e.indexOf(t)},t.getMaxLevelByMediaSize=function(t,e,r){if(!t||!t.length)return-1;for(var i,n,a=t.length-1,s=0;s<t.length;s+=1){var o=t[s];if((o.width>=e||o.height>=r)&&(i=o,!(n=t[s+1])||i.width!==n.width||i.height!==n.height)){a=s;break}}return a},e=t,(r=[{key:"mediaWidth",get:function(){return this.getDimensions().width*this.contentScaleFactor}},{key:"mediaHeight",get:function(){return this.getDimensions().height*this.contentScaleFactor}},{key:"contentScaleFactor",get:function(){var t=1;if(!this.hls.config.ignoreDevicePixelRatio)try{t=self.devicePixelRatio}catch(t){}return t}}])&&n(e.prototype,r),a&&n(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/controller/cmcd-controller.ts":
/*!*******************************************!*\
  !*** ./src/controller/cmcd-controller.ts ***!
  \*******************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>h});var i=r(/*! ../events */"./src/events.ts"),n=r(/*! ../types/cmcd */"./src/types/cmcd.ts"),a=r(/*! ../utils/buffer-helper */"./src/utils/buffer-helper.ts"),s=r(/*! ../utils/logger */"./src/utils/logger.ts");function o(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function l(t,e,r){return e&&o(t.prototype,e),r&&o(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function u(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=new Array(e);r<e;r++)i[r]=t[r];return i}function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}var h=function(){function t(e){var r=this;this.hls=void 0,this.config=void 0,this.media=void 0,this.sid=void 0,this.cid=void 0,this.useHeaders=!1,this.initialized=!1,this.starved=!1,this.buffering=!0,this.audioBuffer=void 0,this.videoBuffer=void 0,this.onWaiting=function(){r.initialized&&(r.starved=!0),r.buffering=!0},this.onPlaying=function(){r.initialized||(r.initialized=!0),r.buffering=!1},this.applyPlaylistData=function(t){try{r.apply(t,{ot:n.CMCDObjectType.MANIFEST,su:!r.initialized})}catch(t){s.logger.warn("Could not generate manifest CMCD data.",t)}},this.applyFragmentData=function(t){try{var e=t.frag,i=r.hls.levels[e.level],a=r.getObjectType(e),o={d:1e3*e.duration,ot:a};a!==n.CMCDObjectType.VIDEO&&a!==n.CMCDObjectType.AUDIO&&a!=n.CMCDObjectType.MUXED||(o.br=i.bitrate/1e3,o.tb=r.getTopBandwidth(a)/1e3,o.bl=r.getBufferLength(a)),r.apply(t,o)}catch(t){s.logger.warn("Could not generate segment CMCD data.",t)}},this.hls=e;var i=this.config=e.config,a=i.cmcd;null!=a&&(i.pLoader=this.createPlaylistLoader(),i.fLoader=this.createFragmentLoader(),this.sid=a.sessionId||t.uuid(),this.cid=a.contentId,this.useHeaders=!0===a.useHeaders,this.registerListeners())}var e=t.prototype;return e.registerListeners=function(){var t=this.hls;t.on(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(i.Events.MEDIA_DETACHED,this.onMediaDetached,this),t.on(i.Events.BUFFER_CREATED,this.onBufferCreated,this)},e.unregisterListeners=function(){var t=this.hls;t.off(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(i.Events.MEDIA_DETACHED,this.onMediaDetached,this),t.off(i.Events.BUFFER_CREATED,this.onBufferCreated,this),this.onMediaDetached()},e.destroy=function(){this.unregisterListeners(),this.hls=this.config=this.audioBuffer=this.videoBuffer=null},e.onMediaAttached=function(t,e){this.media=e.media,this.media.addEventListener("waiting",this.onWaiting),this.media.addEventListener("playing",this.onPlaying)},e.onMediaDetached=function(){this.media&&(this.media.removeEventListener("waiting",this.onWaiting),this.media.removeEventListener("playing",this.onPlaying),this.media=null)},e.onBufferCreated=function(t,e){var r,i;this.audioBuffer=null===(r=e.tracks.audio)||void 0===r?void 0:r.buffer,this.videoBuffer=null===(i=e.tracks.video)||void 0===i?void 0:i.buffer},e.createData=function(){var t;return{v:n.CMCDVersion,sf:n.CMCDStreamingFormat.HLS,sid:this.sid,cid:this.cid,pr:null===(t=this.media)||void 0===t?void 0:t.playbackRate,mtp:this.hls.bandwidthEstimate/1e3}},e.apply=function(e,r){void 0===r&&(r={}),c(r,this.createData());var i=r.ot===n.CMCDObjectType.INIT||r.ot===n.CMCDObjectType.VIDEO||r.ot===n.CMCDObjectType.MUXED;if(this.starved&&i&&(r.bs=!0,r.su=!0,this.starved=!1),null==r.su&&(r.su=this.buffering),this.useHeaders){var a=t.toHeaders(r);if(!Object.keys(a).length)return;e.headers||(e.headers={}),c(e.headers,a)}else{var s=t.toQuery(r);if(!s)return;e.url=t.appendQueryToUri(e.url,s)}},e.getObjectType=function(t){var e=t.type;return"subtitle"===e?n.CMCDObjectType.TIMED_TEXT:"initSegment"===t.sn?n.CMCDObjectType.INIT:"audio"===e?n.CMCDObjectType.AUDIO:"main"===e?this.hls.audioTracks.length?n.CMCDObjectType.VIDEO:n.CMCDObjectType.MUXED:void 0},e.getTopBandwidth=function(t){var e,r=0,i=this.hls;if(t===n.CMCDObjectType.AUDIO)e=i.audioTracks;else{var a=i.maxAutoLevel,s=a>-1?a+1:i.levels.length;e=i.levels.slice(0,s)}for(var o,l=u(e);!(o=l()).done;){var d=o.value;d.bitrate>r&&(r=d.bitrate)}return r>0?r:NaN},e.getBufferLength=function(t){var e=this.hls.media,r=t===n.CMCDObjectType.AUDIO?this.audioBuffer:this.videoBuffer;return r&&e?1e3*a.BufferHelper.bufferInfo(r,e.currentTime,this.config.maxBufferHole).len:NaN},e.createPlaylistLoader=function(){var t=this.config.pLoader,e=this.applyPlaylistData,r=t||this.config.loader;return function(){function t(t){this.loader=void 0,this.loader=new r(t)}var i=t.prototype;return i.destroy=function(){this.loader.destroy()},i.abort=function(){this.loader.abort()},i.load=function(t,r,i){e(t),this.loader.load(t,r,i)},l(t,[{key:"stats",get:function(){return this.loader.stats}},{key:"context",get:function(){return this.loader.context}}]),t}()},e.createFragmentLoader=function(){var t=this.config.fLoader,e=this.applyFragmentData,r=t||this.config.loader;return function(){function t(t){this.loader=void 0,this.loader=new r(t)}var i=t.prototype;return i.destroy=function(){this.loader.destroy()},i.abort=function(){this.loader.abort()},i.load=function(t,r,i){e(t),this.loader.load(t,r,i)},l(t,[{key:"stats",get:function(){return this.loader.stats}},{key:"context",get:function(){return this.loader.context}}]),t}()},t.uuid=function(){var t=URL.createObjectURL(new Blob),e=t.toString();return URL.revokeObjectURL(t),e.slice(e.lastIndexOf("/")+1)},t.serialize=function(t){for(var e,r=[],i=function(t){return!Number.isNaN(t)&&null!=t&&""!==t&&!1!==t},n=function(t){return Math.round(t)},a=function(t){return 100*n(t/100)},s={br:n,d:n,bl:a,dl:a,mtp:a,nor:function(t){return encodeURIComponent(t)},rtp:a,tb:n},o=u(Object.keys(t||{}).sort());!(e=o()).done;){var l=e.value,d=t[l];if(i(d)&&!("v"===l&&1===d||"pr"==l&&1===d)){var c=s[l];c&&(d=c(d));var h=typeof d,f=void 0;f="ot"===l||"sf"===l||"st"===l?l+"="+d:"boolean"===h?l:"number"===h?l+"="+d:l+"="+JSON.stringify(d),r.push(f)}}return r.join(",")},t.toHeaders=function(e){for(var r={},i=["Object","Request","Session","Status"],n=[{},{},{},{}],a={br:0,d:0,ot:0,tb:0,bl:1,dl:1,mtp:1,nor:1,nrr:1,su:1,cid:2,pr:2,sf:2,sid:2,st:2,v:2,bs:3,rtp:3},s=0,o=Object.keys(e);s<o.length;s++){var l=o[s];n[null!=a[l]?a[l]:1][l]=e[l]}for(var u=0;u<n.length;u++){var d=t.serialize(n[u]);d&&(r["CMCD-"+i[u]]=d)}return r},t.toQuery=function(e){return"CMCD="+encodeURIComponent(t.serialize(e))},t.appendQueryToUri=function(t,e){if(!e)return t;var r=t.includes("?")?"&":"?";return""+t+r+e},t}()},"./src/controller/eme-controller.ts":
/*!******************************************!*\
  !*** ./src/controller/eme-controller.ts ***!
  \******************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l});var i=r(/*! ../events */"./src/events.ts"),n=r(/*! ../errors */"./src/errors.ts"),a=r(/*! ../utils/logger */"./src/utils/logger.ts"),s=r(/*! ../utils/mediakeys-helper */"./src/utils/mediakeys-helper.ts");function o(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}const l=function(){function t(t){this.hls=void 0,this._widevineLicenseUrl=void 0,this._licenseXhrSetup=void 0,this._licenseResponseCallback=void 0,this._emeEnabled=void 0,this._requestMediaKeySystemAccess=void 0,this._drmSystemOptions=void 0,this._config=void 0,this._mediaKeysList=[],this._media=null,this._hasSetMediaKeys=!1,this._requestLicenseFailureCount=0,this.mediaKeysPromise=null,this._onMediaEncrypted=this.onMediaEncrypted.bind(this),this.hls=t,this._config=t.config,this._widevineLicenseUrl=this._config.widevineLicenseUrl,this._licenseXhrSetup=this._config.licenseXhrSetup,this._licenseResponseCallback=this._config.licenseResponseCallback,this._emeEnabled=this._config.emeEnabled,this._requestMediaKeySystemAccess=this._config.requestMediaKeySystemAccessFunc,this._drmSystemOptions=this._config.drmSystemOptions,this._registerListeners()}var e,r,l,u=t.prototype;return u.destroy=function(){this._unregisterListeners(),this.hls=this._onMediaEncrypted=null,this._requestMediaKeySystemAccess=null},u._registerListeners=function(){this.hls.on(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(i.Events.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.on(i.Events.MANIFEST_PARSED,this.onManifestParsed,this)},u._unregisterListeners=function(){this.hls.off(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(i.Events.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.off(i.Events.MANIFEST_PARSED,this.onManifestParsed,this)},u.getLicenseServerUrl=function(t){switch(t){case s.KeySystems.WIDEVINE:if(!this._widevineLicenseUrl)break;return this._widevineLicenseUrl}throw new Error('no license server URL configured for key-system "'+t+'"')},u._attemptKeySystemAccess=function(t,e,r){var i=this,n=function(t,e,r,i){switch(t){case s.KeySystems.WIDEVINE:return function(t,e,r){var i={audioCapabilities:[],videoCapabilities:[]};return t.forEach((function(t){i.audioCapabilities.push({contentType:'audio/mp4; codecs="'+t+'"',robustness:r.audioRobustness||""})})),e.forEach((function(t){i.videoCapabilities.push({contentType:'video/mp4; codecs="'+t+'"',robustness:r.videoRobustness||""})})),[i]}(e,r,i);default:throw new Error("Unknown key-system: "+t)}}(t,e,r,this._drmSystemOptions);a.logger.log("Requesting encrypted media key-system access");var o=this.requestMediaKeySystemAccess(t,n);this.mediaKeysPromise=o.then((function(e){return i._onMediaKeySystemAccessObtained(t,e)})),o.catch((function(e){a.logger.error('Failed to obtain key-system "'+t+'" access:',e)}))},u._onMediaKeySystemAccessObtained=function(t,e){var r=this;a.logger.log('Access for key-system "'+t+'" obtained');var i={mediaKeysSessionInitialized:!1,mediaKeySystemAccess:e,mediaKeySystemDomain:t};this._mediaKeysList.push(i);var n=Promise.resolve().then((function(){return e.createMediaKeys()})).then((function(e){return i.mediaKeys=e,a.logger.log('Media-keys created for key-system "'+t+'"'),r._onMediaKeysCreated(),e}));return n.catch((function(t){a.logger.error("Failed to create media-keys:",t)})),n},u._onMediaKeysCreated=function(){var t=this;this._mediaKeysList.forEach((function(e){e.mediaKeysSession||(e.mediaKeysSession=e.mediaKeys.createSession(),t._onNewMediaKeySession(e.mediaKeysSession))}))},u._onNewMediaKeySession=function(t){var e=this;a.logger.log("New key-system session "+t.sessionId),t.addEventListener("message",(function(r){e._onKeySessionMessage(t,r.message)}),!1)},u._onKeySessionMessage=function(t,e){a.logger.log("Got EME message event, creating license request"),this._requestLicense(e,(function(e){a.logger.log("Received license data (length: "+(e?e.byteLength:e)+"), updating key-session"),t.update(e).catch((function(t){a.logger.warn("Updating key-session failed: "+t)}))}))},u.onMediaEncrypted=function(t){var e=this;if(a.logger.log('Media is encrypted using "'+t.initDataType+'" init data type'),!this.mediaKeysPromise)return a.logger.error("Fatal: Media is encrypted but no CDM access or no keys have been requested"),void this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_NO_KEYS,fatal:!0});var r=function(r){e._media&&(e._attemptSetMediaKeys(r),e._generateRequestWithPreferredKeySession(t.initDataType,t.initData))};this.mediaKeysPromise.then(r).catch(r)},u._attemptSetMediaKeys=function(t){if(!this._media)throw new Error("Attempted to set mediaKeys without first attaching a media element");if(!this._hasSetMediaKeys){var e=this._mediaKeysList[0];if(!e||!e.mediaKeys)return a.logger.error("Fatal: Media is encrypted but no CDM access or no keys have been obtained yet"),void this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_NO_KEYS,fatal:!0});a.logger.log("Setting keys for encrypted media"),this._media.setMediaKeys(e.mediaKeys),this._hasSetMediaKeys=!0}},u._generateRequestWithPreferredKeySession=function(t,e){var r=this,s=this._mediaKeysList[0];if(!s)return a.logger.error("Fatal: Media is encrypted but not any key-system access has been obtained yet"),void this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_NO_ACCESS,fatal:!0});if(s.mediaKeysSessionInitialized)a.logger.warn("Key-Session already initialized but requested again");else{var o=s.mediaKeysSession;if(!o)return a.logger.error("Fatal: Media is encrypted but no key-session existing"),void this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_NO_SESSION,fatal:!0});if(!e)return a.logger.warn("Fatal: initData required for generating a key session is null"),void this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_NO_INIT_DATA,fatal:!0});a.logger.log('Generating key-session request for "'+t+'" init data type'),s.mediaKeysSessionInitialized=!0,o.generateRequest(t,e).then((function(){a.logger.debug("Key-session generation succeeded")})).catch((function(t){a.logger.error("Error generating key-session request:",t),r.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_NO_SESSION,fatal:!1})}))}},u._createLicenseXhr=function(t,e,r){var i=new XMLHttpRequest;i.responseType="arraybuffer",i.onreadystatechange=this._onLicenseRequestReadyStageChange.bind(this,i,t,e,r);var n=this._licenseXhrSetup;if(n)try{n.call(this.hls,i,t),n=void 0}catch(t){a.logger.error(t)}try{i.readyState||i.open("POST",t,!0),n&&n.call(this.hls,i,t)}catch(t){throw new Error("issue setting up KeySystem license XHR "+t)}return i},u._onLicenseRequestReadyStageChange=function(t,e,r,s){switch(t.readyState){case 4:if(200===t.status){this._requestLicenseFailureCount=0,a.logger.log("License request succeeded");var o=t.response,l=this._licenseResponseCallback;if(l)try{o=l.call(this.hls,t,e)}catch(t){a.logger.error(t)}s(o)}else{if(a.logger.error("License Request XHR failed ("+e+"). Status: "+t.status+" ("+t.statusText+")"),this._requestLicenseFailureCount++,this._requestLicenseFailureCount>3)return void this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0});var u=3-this._requestLicenseFailureCount+1;a.logger.warn("Retrying license request, "+u+" attempts left"),this._requestLicense(r,s)}}},u._generateLicenseRequestChallenge=function(t,e){switch(t.mediaKeySystemDomain){case s.KeySystems.WIDEVINE:return e}throw new Error("unsupported key-system: "+t.mediaKeySystemDomain)},u._requestLicense=function(t,e){a.logger.log("Requesting content license for key-system");var r=this._mediaKeysList[0];if(!r)return a.logger.error("Fatal error: Media is encrypted but no key-system access has been obtained yet"),void this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_NO_ACCESS,fatal:!0});try{var s=this.getLicenseServerUrl(r.mediaKeySystemDomain),o=this._createLicenseXhr(s,t,e);a.logger.log("Sending license request to URL: "+s);var l=this._generateLicenseRequestChallenge(r,t);o.send(l)}catch(t){a.logger.error("Failure requesting DRM license: "+t),this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.KEY_SYSTEM_ERROR,details:n.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0})}},u.onMediaAttached=function(t,e){if(this._emeEnabled){var r=e.media;this._media=r,r.addEventListener("encrypted",this._onMediaEncrypted)}},u.onMediaDetached=function(){var t=this._media,e=this._mediaKeysList;t&&(t.removeEventListener("encrypted",this._onMediaEncrypted),this._media=null,this._mediaKeysList=[],Promise.all(e.map((function(t){if(t.mediaKeysSession)return t.mediaKeysSession.close().catch((function(){}))}))).then((function(){return t.setMediaKeys(null)})).catch((function(){})))},u.onManifestParsed=function(t,e){if(this._emeEnabled){var r=e.levels.map((function(t){return t.audioCodec})).filter((function(t){return!!t})),i=e.levels.map((function(t){return t.videoCodec})).filter((function(t){return!!t}));this._attemptKeySystemAccess(s.KeySystems.WIDEVINE,r,i)}},e=t,(r=[{key:"requestMediaKeySystemAccess",get:function(){if(!this._requestMediaKeySystemAccess)throw new Error("No requestMediaKeySystemAccess function configured");return this._requestMediaKeySystemAccess}}])&&o(e.prototype,r),l&&o(e,l),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/controller/fps-controller.ts":
/*!******************************************!*\
  !*** ./src/controller/fps-controller.ts ***!
  \******************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>a});var i=r(/*! ../events */"./src/events.ts"),n=r(/*! ../utils/logger */"./src/utils/logger.ts");const a=function(){function t(t){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=t,this.registerListeners()}var e=t.prototype;return e.setStreamController=function(t){this.streamController=t},e.registerListeners=function(){this.hls.on(i.Events.MEDIA_ATTACHING,this.onMediaAttaching,this)},e.unregisterListeners=function(){this.hls.off(i.Events.MEDIA_ATTACHING,this.onMediaAttaching)},e.destroy=function(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null},e.onMediaAttaching=function(t,e){var r=this.hls.config;if(r.capLevelOnFPSDrop){var i=e.media instanceof self.HTMLVideoElement?e.media:null;this.media=i,i&&"function"==typeof i.getVideoPlaybackQuality&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),r.fpsDroppedMonitoringPeriod)}},e.checkFPS=function(t,e,r){var a=performance.now();if(e){if(this.lastTime){var s=a-this.lastTime,o=r-this.lastDroppedFrames,l=e-this.lastDecodedFrames,u=1e3*o/s,d=this.hls;if(d.trigger(i.Events.FPS_DROP,{currentDropped:o,currentDecoded:l,totalDroppedFrames:r}),u>0&&o>d.config.fpsDroppedMonitoringThreshold*l){var c=d.currentLevel;n.logger.warn("drop FPS ratio greater than max allowed value for currentLevel: "+c),c>0&&(-1===d.autoLevelCapping||d.autoLevelCapping>=c)&&(c-=1,d.trigger(i.Events.FPS_DROP_LEVEL_CAPPING,{level:c,droppedLevel:d.currentLevel}),d.autoLevelCapping=c,this.streamController.nextLevelSwitch())}}this.lastTime=a,this.lastDroppedFrames=r,this.lastDecodedFrames=e}},e.checkFPSInterval=function(){var t=this.media;if(t)if(this.isVideoPlaybackQualityAvailable){var e=t.getVideoPlaybackQuality();this.checkFPS(t,e.totalVideoFrames,e.droppedVideoFrames)}else this.checkFPS(t,t.webkitDecodedFrameCount,t.webkitDroppedFrameCount)},t}()},"./src/controller/fragment-finders.ts":
/*!********************************************!*\
  !*** ./src/controller/fragment-finders.ts ***!
  \********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{findFragWithCC:()=>u,findFragmentByPDT:()=>a,findFragmentByPTS:()=>s,fragmentWithinToleranceTest:()=>o,pdtWithinToleranceTest:()=>l});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../utils/binary-search */"./src/utils/binary-search.ts");function a(t,e,r){if(null===e||!Array.isArray(t)||!t.length||!(0,i.isFiniteNumber)(e))return null;if(e<(t[0].programDateTime||0))return null;if(e>=(t[t.length-1].endProgramDateTime||0))return null;r=r||0;for(var n=0;n<t.length;++n){var a=t[n];if(l(e,r,a))return a}return null}function s(t,e,r,i){void 0===r&&(r=0),void 0===i&&(i=0);var a=null;if(t?a=e[t.sn-e[0].sn+1]||null:0===r&&0===e[0].start&&(a=e[0]),a&&0===o(r,i,a))return a;var s=n.default.search(e,o.bind(null,r,i));return s||a}function o(t,e,r){void 0===t&&(t=0),void 0===e&&(e=0);var i=Math.min(e,r.duration+(r.deltaPTS?r.deltaPTS:0));return r.start+r.duration-i<=t?1:r.start-i>t&&r.start?-1:0}function l(t,e,r){var i=1e3*Math.min(e,r.duration+(r.deltaPTS?r.deltaPTS:0));return(r.endProgramDateTime||0)-i>t}function u(t,e){return n.default.search(t,(function(t){return t.cc<e?1:t.cc>e?-1:0}))}},"./src/controller/fragment-tracker.ts":
/*!********************************************!*\
  !*** ./src/controller/fragment-tracker.ts ***!
  \********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{FragmentState:()=>i,FragmentTracker:()=>s});var i,n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../types/loader */"./src/types/loader.ts");!function(t){t.NOT_LOADED="NOT_LOADED",t.APPENDING="APPENDING",t.PARTIAL="PARTIAL",t.OK="OK"}(i||(i={}));var s=function(){function t(t){this.activeFragment=null,this.activeParts=null,this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hls=t,this._registerListeners()}var e=t.prototype;return e._registerListeners=function(){var t=this.hls;t.on(n.Events.BUFFER_APPENDED,this.onBufferAppended,this),t.on(n.Events.FRAG_BUFFERED,this.onFragBuffered,this),t.on(n.Events.FRAG_LOADED,this.onFragLoaded,this)},e._unregisterListeners=function(){var t=this.hls;t.off(n.Events.BUFFER_APPENDED,this.onBufferAppended,this),t.off(n.Events.FRAG_BUFFERED,this.onFragBuffered,this),t.off(n.Events.FRAG_LOADED,this.onFragLoaded,this)},e.destroy=function(){this._unregisterListeners(),this.fragments=this.timeRanges=null},e.getAppendedFrag=function(t,e){if(e===a.PlaylistLevelType.MAIN){var r=this.activeFragment,i=this.activeParts;if(!r)return null;if(i)for(var n=i.length;n--;){var s=i[n],o=s?s.end:r.appendedPTS;if(s.start<=t&&void 0!==o&&t<=o)return n>9&&(this.activeParts=i.slice(n-9)),s}else if(r.start<=t&&void 0!==r.appendedPTS&&t<=r.appendedPTS)return r}return this.getBufferedFrag(t,e)},e.getBufferedFrag=function(t,e){for(var r=this.fragments,i=Object.keys(r),n=i.length;n--;){var a=r[i[n]];if((null==a?void 0:a.body.type)===e&&a.buffered){var s=a.body;if(s.start<=t&&t<=s.end)return s}}return null},e.detectEvictedFragments=function(t,e,r){var i=this;Object.keys(this.fragments).forEach((function(n){var a=i.fragments[n];if(a)if(a.buffered){var s=a.range[t];s&&s.time.some((function(t){var r=!i.isTimeBuffered(t.startPTS,t.endPTS,e);return r&&i.removeFragment(a.body),r}))}else a.body.type===r&&i.removeFragment(a.body)}))},e.detectPartialFragments=function(t){var e=this,r=this.timeRanges,i=t.frag,n=t.part;if(r&&"initSegment"!==i.sn){var a=l(i),s=this.fragments[a];s&&(Object.keys(r).forEach((function(t){var a=i.elementaryStreams[t];if(a){var o=r[t],l=null!==n||!0===a.partial;s.range[t]=e.getBufferedTimes(i,n,l,o)}})),s.loaded=null,Object.keys(s.range).length?s.buffered=!0:this.removeFragment(s.body))}},e.fragBuffered=function(t){var e=l(t),r=this.fragments[e];r&&(r.loaded=null,r.buffered=!0)},e.getBufferedTimes=function(t,e,r,i){for(var n={time:[],partial:r},a=e?e.start:t.start,s=e?e.end:t.end,o=t.minEndPTS||s,l=t.maxStartPTS||a,u=0;u<i.length;u++){var d=i.start(u)-this.bufferPadding,c=i.end(u)+this.bufferPadding;if(l>=d&&o<=c){n.time.push({startPTS:Math.max(a,i.start(u)),endPTS:Math.min(s,i.end(u))});break}if(a<c&&s>d)n.partial=!0,n.time.push({startPTS:Math.max(a,i.start(u)),endPTS:Math.min(s,i.end(u))});else if(s<=d)break}return n},e.getPartialFragment=function(t){var e,r,i,n=null,a=0,s=this.bufferPadding,l=this.fragments;return Object.keys(l).forEach((function(u){var d=l[u];d&&o(d)&&(r=d.body.start-s,i=d.body.end+s,t>=r&&t<=i&&(e=Math.min(t-r,i-t),a<=e&&(n=d.body,a=e)))})),n},e.getState=function(t){var e=l(t),r=this.fragments[e];return r?r.buffered?o(r)?i.PARTIAL:i.OK:i.APPENDING:i.NOT_LOADED},e.isTimeBuffered=function(t,e,r){for(var i,n,a=0;a<r.length;a++){if(i=r.start(a)-this.bufferPadding,n=r.end(a)+this.bufferPadding,t>=i&&e<=n)return!0;if(e<=i)return!1}return!1},e.onFragLoaded=function(t,e){var r=e.frag,i=e.part;if("initSegment"!==r.sn&&!r.bitrateTest&&!i){var n=l(r);this.fragments[n]={body:r,loaded:e,buffered:!1,range:Object.create(null)}}},e.onBufferAppended=function(t,e){var r=this,i=e.frag,n=e.part,s=e.timeRanges;if(i.type===a.PlaylistLevelType.MAIN)if(this.activeFragment=i,n){var o=this.activeParts;o||(this.activeParts=o=[]),o.push(n)}else this.activeParts=null;this.timeRanges=s,Object.keys(s).forEach((function(t){var e=s[t];if(r.detectEvictedFragments(t,e),!n)for(var a=0;a<e.length;a++)i.appendedPTS=Math.max(e.end(a),i.appendedPTS||0)}))},e.onFragBuffered=function(t,e){this.detectPartialFragments(e)},e.hasFragment=function(t){var e=l(t);return!!this.fragments[e]},e.removeFragmentsInRange=function(t,e,r){var i=this;Object.keys(this.fragments).forEach((function(n){var a=i.fragments[n];if(a&&a.buffered){var s=a.body;s.type===r&&s.start<e&&s.end>t&&i.removeFragment(s)}}))},e.removeFragment=function(t){var e=l(t);t.stats.loaded=0,t.clearElementaryStreamInfo(),delete this.fragments[e]},e.removeAllFragments=function(){this.fragments=Object.create(null),this.activeFragment=null,this.activeParts=null},t}();function o(t){var e,r;return t.buffered&&((null===(e=t.range.video)||void 0===e?void 0:e.partial)||(null===(r=t.range.audio)||void 0===r?void 0:r.partial))}function l(t){return t.type+"_"+t.level+"_"+t.urlId+"_"+t.sn}},"./src/controller/gap-controller.ts":
/*!******************************************!*\
  !*** ./src/controller/gap-controller.ts ***!
  \******************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{MAX_START_GAP_JUMP:()=>l,SKIP_BUFFER_HOLE_STEP_SECONDS:()=>u,SKIP_BUFFER_RANGE_START:()=>d,STALL_MINIMUM_DURATION_MS:()=>o,default:()=>c});var i=r(/*! ../utils/buffer-helper */"./src/utils/buffer-helper.ts"),n=r(/*! ../errors */"./src/errors.ts"),a=r(/*! ../events */"./src/events.ts"),s=r(/*! ../utils/logger */"./src/utils/logger.ts"),o=250,l=2,u=.1,d=.05,c=function(){function t(t,e,r,i){this.config=void 0,this.media=null,this.fragmentTracker=void 0,this.hls=void 0,this.nudgeRetry=0,this.stallReported=!1,this.stalled=null,this.moved=!1,this.seeking=!1,this.config=t,this.media=e,this.fragmentTracker=r,this.hls=i}var e=t.prototype;return e.destroy=function(){this.media=null,this.hls=this.fragmentTracker=null},e.poll=function(t,e){var r=this.config,n=this.media,a=this.stalled;if(null!==n){var u=n.currentTime,d=n.seeking,c=this.seeking&&!d,h=!this.seeking&&d;if(this.seeking=d,u===t){if((h||c)&&(this.stalled=null),!(n.paused&&!d||n.ended||0===n.playbackRate)&&i.BufferHelper.getBuffered(n).length){var f=i.BufferHelper.bufferInfo(n,u,0),g=f.len>0,v=f.nextStart||0;if(g||v){if(d){var p=f.len>l,m=!v||e&&e.start<=u||v-u>l&&!this.fragmentTracker.getPartialFragment(u);if(p||m)return;this.moved=!1}if(!this.moved&&null!==this.stalled){var y,E=Math.max(v,f.start||0)-u,T=this.hls.levels?this.hls.levels[this.hls.currentLevel]:null,S=(null==T||null===(y=T.details)||void 0===y?void 0:y.live)?2*T.details.targetduration:l;if(E>0&&E<=S)return void this._trySkipBufferHole(null)}var b=self.performance.now();if(null!==a){var D=b-a;if(d||!(D>=o)||(this._reportStall(f),this.media)){var L=i.BufferHelper.bufferInfo(n,u,r.maxBufferHole);this._tryFixBufferStall(L,D)}}else this.stalled=b}}}else if(this.moved=!0,null!==a){if(this.stallReported){var A=self.performance.now()-a;s.logger.warn("playback not stuck anymore @"+u+", after "+Math.round(A)+"ms"),this.stallReported=!1}this.stalled=null,this.nudgeRetry=0}}},e._tryFixBufferStall=function(t,e){var r=this.config,i=this.fragmentTracker,n=this.media;if(null!==n){var a=n.currentTime,o=i.getPartialFragment(a);if(o&&(this._trySkipBufferHole(o)||!this.media))return;t.len>r.maxBufferHole&&e>1e3*r.highBufferWatchdogPeriod&&(s.logger.warn("Trying to nudge playhead over buffer-hole"),this.stalled=null,this._tryNudgeBuffer())}},e._reportStall=function(t){var e=this.hls,r=this.media;!this.stallReported&&r&&(this.stallReported=!0,s.logger.warn("Playback stalling at @"+r.currentTime+" due to low buffer ("+JSON.stringify(t)+")"),e.trigger(a.Events.ERROR,{type:n.ErrorTypes.MEDIA_ERROR,details:n.ErrorDetails.BUFFER_STALLED_ERROR,fatal:!1,buffer:t.len}))},e._trySkipBufferHole=function(t){var e=this.config,r=this.hls,o=this.media;if(null===o)return 0;for(var l=o.currentTime,c=0,h=i.BufferHelper.getBuffered(o),f=0;f<h.length;f++){var g=h.start(f);if(l+e.maxBufferHole>=c&&l<g){var v=Math.max(g+d,o.currentTime+u);return s.logger.warn("skipping hole, adjusting currentTime from "+l+" to "+v),this.moved=!0,this.stalled=null,o.currentTime=v,t&&r.trigger(a.Events.ERROR,{type:n.ErrorTypes.MEDIA_ERROR,details:n.ErrorDetails.BUFFER_SEEK_OVER_HOLE,fatal:!1,reason:"fragment loaded with buffer holes, seeking from "+l+" to "+v,frag:t}),v}c=h.end(f)}return 0},e._tryNudgeBuffer=function(){var t=this.config,e=this.hls,r=this.media,i=this.nudgeRetry;if(null!==r){var o=r.currentTime;if(this.nudgeRetry++,i<t.nudgeMaxRetry){var l=o+(i+1)*t.nudgeOffset;s.logger.warn("Nudging 'currentTime' from "+o+" to "+l),r.currentTime=l,e.trigger(a.Events.ERROR,{type:n.ErrorTypes.MEDIA_ERROR,details:n.ErrorDetails.BUFFER_NUDGE_ON_STALL,fatal:!1})}else s.logger.error("Playhead still not moving while enough data buffered @"+o+" after "+t.nudgeMaxRetry+" nudges"),e.trigger(a.Events.ERROR,{type:n.ErrorTypes.MEDIA_ERROR,details:n.ErrorDetails.BUFFER_STALLED_ERROR,fatal:!0})}},t}()},"./src/controller/id3-track-controller.ts":
/*!************************************************!*\
  !*** ./src/controller/id3-track-controller.ts ***!
  \************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>h});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../utils/texttrack-utils */"./src/utils/texttrack-utils.ts"),s=r(/*! ../demux/id3 */"./src/demux/id3.ts"),o=r(/*! ../loader/date-range */"./src/loader/date-range.ts"),l=r(/*! ../types/demuxer */"./src/types/demuxer.ts");function u(){return self.WebKitDataCue||self.VTTCue||self.TextTrackCue}var d=function(){var t=u();try{new t(0,Number.POSITIVE_INFINITY,"")}catch(t){return Number.MAX_VALUE}return Number.POSITIVE_INFINITY}();function c(t,e){return t.getTime()/1e3-e}const h=function(){function t(t){this.hls=void 0,this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=t,this._registerListeners()}var e=t.prototype;return e.destroy=function(){this._unregisterListeners(),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=null},e._registerListeners=function(){var t=this.hls;t.on(n.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(n.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(n.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(n.Events.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),t.on(n.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(n.Events.LEVEL_UPDATED,this.onLevelUpdated,this)},e._unregisterListeners=function(){var t=this.hls;t.off(n.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(n.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(n.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(n.Events.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),t.off(n.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(n.Events.LEVEL_UPDATED,this.onLevelUpdated,this)},e.onMediaAttached=function(t,e){this.media=e.media},e.onMediaDetaching=function(){this.id3Track&&((0,a.clearCurrentCues)(this.id3Track),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={})},e.onManifestLoading=function(){this.dateRangeCuesAppended={}},e.createTrack=function(t){var e=this.getID3Track(t.textTracks);return e.mode="hidden",e},e.getID3Track=function(t){if(this.media){for(var e=0;e<t.length;e++){var r=t[e];if("metadata"===r.kind&&"id3"===r.label)return(0,a.sendAddTrackEvent)(r,this.media),r}return this.media.addTextTrack("metadata","id3")}},e.onFragParsingMetadata=function(t,e){if(this.media){var r=this.hls.config,i=r.enableEmsgMetadataCues,n=r.enableID3MetadataCues;if(i||n){e.frag;var a=e.samples;e.details,this.id3Track||(this.id3Track=this.createTrack(this.media));for(var o=u(),c=0;c<a.length;c++){var h=a[c].type;if((h!==l.MetadataSchema.emsg||i)&&n){var f=s.getID3Frames(a[c].data);if(f){var g=a[c].pts,v=g+a[c].duration;v>d&&(v=d),v-g<=0&&(v=g+.25);for(var p=0;p<f.length;p++){var m=f[p];if(!s.isTimeStampFrame(m)){this.updateId3CueEnds(g);var y=new o(g,v,"");y.value=m,h&&(y.type=h),this.id3Track.addCue(y)}}}}}}}},e.updateId3CueEnds=function(t){var e,r=null===(e=this.id3Track)||void 0===e?void 0:e.cues;if(r)for(var i=r.length;i--;){var n=r[i];n.startTime<t&&n.endTime===d&&(n.endTime=t)}},e.onBufferFlushing=function(t,e){var r=e.startOffset,i=e.endOffset,n=e.type,s=this.id3Track,o=this.hls;if(o){var u,d=o.config,c=d.enableEmsgMetadataCues,h=d.enableID3MetadataCues;s&&(c||h)&&(u="audio"===n?function(t){return t.type===l.MetadataSchema.audioId3&&h}:"video"===n?function(t){return t.type===l.MetadataSchema.emsg&&c}:function(t){return t.type===l.MetadataSchema.audioId3&&h||t.type===l.MetadataSchema.emsg&&c},(0,a.removeCuesInRange)(s,r,i,u))}},e.onLevelUpdated=function(t,e){var r=this,n=e.details;if(this.media&&n.hasProgramDateTime&&this.hls.config.enableDateRangeMetadataCues){var a=this.dateRangeCuesAppended,s=this.id3Track,h=n.dateRanges,f=Object.keys(h);if(s)for(var g=Object.keys(a).filter((function(t){return!f.includes(t)})),v=function(t){var e=g[t];Object.keys(a[e].cues).forEach((function(t){s.removeCue(a[e].cues[t])})),delete a[e]},p=g.length;p--;)v(p);var m=n.fragments[n.fragments.length-1];if(0!==f.length&&(0,i.isFiniteNumber)(null==m?void 0:m.programDateTime)){this.id3Track||(this.id3Track=this.createTrack(this.media));for(var y=m.programDateTime/1e3-m.start,E=u(),T=function(t){var e=f[t],i=h[e],n=a[e],s=(null==n?void 0:n.cues)||{},u=(null==n?void 0:n.durationKnown)||!1,g=c(i.startDate,y),v=d,p=i.endDate;if(p)v=c(p,y),u=!0;else if(i.endOnNext&&!u){var m=f.reduce((function(t,e){var r=h[e];return r.class===i.class&&r.id!==e&&r.startDate>i.startDate&&t.push(r),t}),[]).sort((function(t,e){return t.startDate.getTime()-e.startDate.getTime()}))[0];m&&(v=c(m.startDate,y),u=!0)}for(var T,S=Object.keys(i.attr),b=0;b<S.length;b++){var D=S[b];if(D!==o.DateRangeAttribute.ID&&D!==o.DateRangeAttribute.CLASS&&D!==o.DateRangeAttribute.START_DATE&&D!==o.DateRangeAttribute.DURATION&&D!==o.DateRangeAttribute.END_DATE&&D!==o.DateRangeAttribute.END_ON_NEXT){var L=s[D];if(L)u&&!n.durationKnown&&(L.endTime=v);else{var A=i.attr[D];L=new E(g,v,""),D!==o.DateRangeAttribute.SCTE35_OUT&&D!==o.DateRangeAttribute.SCTE35_IN||(T=A,A=Uint8Array.from(T.replace(/^0x/,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ")).buffer),L.value={key:D,data:A},L.type=l.MetadataSchema.dateRange,r.id3Track.addCue(L),s[D]=L}}}a[e]={cues:s,dateRange:i,durationKnown:u}},S=0;S<f.length;S++)T(S)}}},t}()},"./src/controller/latency-controller.ts":
/*!**********************************************!*\
  !*** ./src/controller/latency-controller.ts ***!
  \**********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>o});var i=r(/*! ../errors */"./src/errors.ts"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../utils/logger */"./src/utils/logger.ts");function s(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var o=function(){function t(t){var e=this;this.hls=void 0,this.config=void 0,this.media=null,this.levelDetails=null,this.currentTime=0,this.stallCount=0,this._latency=null,this.timeupdateHandler=function(){return e.timeupdate()},this.hls=t,this.config=t.config,this.registerListeners()}var e,r,o,l=t.prototype;return l.destroy=function(){this.unregisterListeners(),this.onMediaDetaching(),this.levelDetails=null,this.hls=this.timeupdateHandler=null},l.registerListeners=function(){this.hls.on(n.Events.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(n.Events.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.on(n.Events.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(n.Events.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.on(n.Events.ERROR,this.onError,this)},l.unregisterListeners=function(){this.hls.off(n.Events.MEDIA_ATTACHED,this.onMediaAttached),this.hls.off(n.Events.MEDIA_DETACHING,this.onMediaDetaching),this.hls.off(n.Events.MANIFEST_LOADING,this.onManifestLoading),this.hls.off(n.Events.LEVEL_UPDATED,this.onLevelUpdated),this.hls.off(n.Events.ERROR,this.onError)},l.onMediaAttached=function(t,e){this.media=e.media,this.media.addEventListener("timeupdate",this.timeupdateHandler)},l.onMediaDetaching=function(){this.media&&(this.media.removeEventListener("timeupdate",this.timeupdateHandler),this.media=null)},l.onManifestLoading=function(){this.levelDetails=null,this._latency=null,this.stallCount=0},l.onLevelUpdated=function(t,e){var r=e.details;this.levelDetails=r,r.advanced&&this.timeupdate(),!r.live&&this.media&&this.media.removeEventListener("timeupdate",this.timeupdateHandler)},l.onError=function(t,e){e.details===i.ErrorDetails.BUFFER_STALLED_ERROR&&(this.stallCount++,a.logger.warn("[playback-rate-controller]: Stall detected, adjusting target latency"))},l.timeupdate=function(){var t=this.media,e=this.levelDetails;if(t&&e){this.currentTime=t.currentTime;var r=this.computeLatency();if(null!==r){this._latency=r;var i=this.config,n=i.lowLatencyMode,a=i.maxLiveSyncPlaybackRate;if(n&&1!==a){var s=this.targetLatency;if(null!==s){var o=r-s,l=o<Math.min(this.maxLatency,s+e.targetduration);if(e.live&&l&&o>.05&&this.forwardBufferLength>1){var u=Math.min(2,Math.max(1,a)),d=Math.round(2/(1+Math.exp(-.75*o-this.edgeStalled))*20)/20;t.playbackRate=Math.min(u,Math.max(1,d))}else 1!==t.playbackRate&&0!==t.playbackRate&&(t.playbackRate=1)}}}}},l.estimateLiveEdge=function(){var t=this.levelDetails;return null===t?null:t.edge+t.age},l.computeLatency=function(){var t=this.estimateLiveEdge();return null===t?null:t-this.currentTime},e=t,(r=[{key:"latency",get:function(){return this._latency||0}},{key:"maxLatency",get:function(){var t=this.config,e=this.levelDetails;return void 0!==t.liveMaxLatencyDuration?t.liveMaxLatencyDuration:e?t.liveMaxLatencyDurationCount*e.targetduration:0}},{key:"targetLatency",get:function(){var t=this.levelDetails;if(null===t)return null;var e=t.holdBack,r=t.partHoldBack,i=t.targetduration,n=this.config,a=n.liveSyncDuration,s=n.liveSyncDurationCount,o=n.lowLatencyMode,l=this.hls.userConfig,u=o&&r||e;(l.liveSyncDuration||l.liveSyncDurationCount||0===u)&&(u=void 0!==a?a:s*i);var d=i;return u+Math.min(1*this.stallCount,d)}},{key:"liveSyncPosition",get:function(){var t=this.estimateLiveEdge(),e=this.targetLatency,r=this.levelDetails;if(null===t||null===e||null===r)return null;var i=r.edge,n=t-e-this.edgeStalled,a=i-r.totalduration,s=i-(this.config.lowLatencyMode&&r.partTarget||r.targetduration);return Math.min(Math.max(a,n),s)}},{key:"drift",get:function(){var t=this.levelDetails;return null===t?1:t.drift}},{key:"edgeStalled",get:function(){var t=this.levelDetails;if(null===t)return 0;var e=3*(this.config.lowLatencyMode&&t.partTarget||t.targetduration);return Math.max(t.age-e,0)}},{key:"forwardBufferLength",get:function(){var t=this.media,e=this.levelDetails;if(!t||!e)return 0;var r=t.buffered.length;return(r?t.buffered.end(r-1):e.edge)-this.currentTime}}])&&s(e.prototype,r),o&&s(e,o),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/controller/level-controller.ts":
/*!********************************************!*\
  !*** ./src/controller/level-controller.ts ***!
  \********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>g});var i=r(/*! ../types/level */"./src/types/level.ts"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../errors */"./src/errors.ts"),s=r(/*! ../utils/codecs */"./src/utils/codecs.ts"),o=r(/*! ./level-helper */"./src/controller/level-helper.ts"),l=r(/*! ./base-playlist-controller */"./src/controller/base-playlist-controller.ts"),u=r(/*! ../types/loader */"./src/types/loader.ts");function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function c(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var f=/chrome|firefox/.test(navigator.userAgent.toLowerCase()),g=function(t){var e,r;function l(e){var r;return(r=t.call(this,e,"[level-controller]")||this)._levels=[],r._firstLevel=-1,r._startLevel=void 0,r.currentLevelIndex=-1,r.manualLevelIndex=-1,r.onParsedComplete=void 0,r._registerListeners(),r}r=t,(e=l).prototype=Object.create(r.prototype),e.prototype.constructor=e,h(e,r);var g,v,p,m=l.prototype;return m._registerListeners=function(){var t=this.hls;t.on(n.Events.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(n.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.on(n.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.on(n.Events.FRAG_LOADED,this.onFragLoaded,this),t.on(n.Events.ERROR,this.onError,this)},m._unregisterListeners=function(){var t=this.hls;t.off(n.Events.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(n.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.off(n.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.off(n.Events.FRAG_LOADED,this.onFragLoaded,this),t.off(n.Events.ERROR,this.onError,this)},m.destroy=function(){this._unregisterListeners(),this.manualLevelIndex=-1,this._levels.length=0,t.prototype.destroy.call(this)},m.startLoad=function(){this._levels.forEach((function(t){t.loadError=0})),t.prototype.startLoad.call(this)},m.onManifestLoaded=function(t,e){var r,l,u=[],d=[],c=[],h={},g=!1,v=!1,p=!1;if(e.levels.forEach((function(t){var e=t.attrs;g=g||!(!t.width||!t.height),v=v||!!t.videoCodec,p=p||!!t.audioCodec,f&&t.audioCodec&&-1!==t.audioCodec.indexOf("mp4a.40.34")&&(t.audioCodec=void 0);var r=t.bitrate+"-"+t.attrs.RESOLUTION+"-"+t.attrs.CODECS;(l=h[r])?l.url.push(t.url):(l=new i.Level(t),h[r]=l,u.push(l)),e&&(e.AUDIO&&(0,o.addGroupId)(l,"audio",e.AUDIO),e.SUBTITLES&&(0,o.addGroupId)(l,"text",e.SUBTITLES))})),(g||v)&&p&&(u=u.filter((function(t){var e=t.videoCodec,r=t.width,i=t.height;return!!e||!(!r||!i)}))),u=u.filter((function(t){var e=t.audioCodec,r=t.videoCodec;return(!e||(0,s.isCodecSupportedInMp4)(e,"audio"))&&(!r||(0,s.isCodecSupportedInMp4)(r,"video"))})),e.audioTracks&&(d=e.audioTracks.filter((function(t){return!t.audioCodec||(0,s.isCodecSupportedInMp4)(t.audioCodec,"audio")})),(0,o.assignTrackIdsByGroup)(d)),e.subtitles&&(c=e.subtitles,(0,o.assignTrackIdsByGroup)(c)),u.length>0){r=u[0].bitrate,u.sort((function(t,e){return t.bitrate-e.bitrate})),this._levels=u;for(var m=0;m<u.length;m++)if(u[m].bitrate===r){this._firstLevel=m,this.log("manifest loaded, "+u.length+" level(s) found, first bitrate: "+r);break}var y=p&&!v,E={levels:u,audioTracks:d,subtitleTracks:c,firstLevel:this._firstLevel,stats:e.stats,audio:p,video:v,altAudio:!y&&d.some((function(t){return!!t.url}))};this.hls.trigger(n.Events.MANIFEST_PARSED,E),(this.hls.config.autoStartLoad||this.hls.forceStartLoad)&&this.hls.startLoad(this.hls.config.startPosition)}else this.hls.trigger(n.Events.ERROR,{type:a.ErrorTypes.MEDIA_ERROR,details:a.ErrorDetails.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:e.url,reason:"no level with compatible codecs found in manifest"})},m.onError=function(e,r){var i;if(t.prototype.onError.call(this,e,r),!r.fatal){var n=r.context,s=this._levels[this.currentLevelIndex];if(n&&(n.type===u.PlaylistContextType.AUDIO_TRACK&&s.audioGroupIds&&n.groupId===s.audioGroupIds[s.urlId]||n.type===u.PlaylistContextType.SUBTITLE_TRACK&&s.textGroupIds&&n.groupId===s.textGroupIds[s.urlId]))this.redundantFailover(this.currentLevelIndex);else{var o,l=!1,d=!0;switch(r.details){case a.ErrorDetails.FRAG_LOAD_ERROR:case a.ErrorDetails.FRAG_LOAD_TIMEOUT:case a.ErrorDetails.KEY_LOAD_ERROR:case a.ErrorDetails.KEY_LOAD_TIMEOUT:if(r.frag){var c=r.frag.type===u.PlaylistLevelType.MAIN?r.frag.level:this.currentLevelIndex,h=this._levels[c];h?(h.fragmentError++,h.fragmentError>this.hls.config.fragLoadingMaxRetry&&(o=c)):o=c}break;case a.ErrorDetails.LEVEL_LOAD_ERROR:case a.ErrorDetails.LEVEL_LOAD_TIMEOUT:n&&(n.deliveryDirectives&&(d=!1),o=n.level),l=!0;break;case a.ErrorDetails.REMUX_ALLOC_ERROR:o=null!=(i=r.level)?i:this.currentLevelIndex,l=!0}void 0!==o&&this.recoverLevel(r,o,l,d)}}},m.recoverLevel=function(t,e,r,i){var n=t.details,a=this._levels[e];if(a.loadError++,r){if(!this.retryLoadingOrFail(t))return void(this.currentLevelIndex=-1);t.levelRetry=!0}if(i){var s=a.url.length;if(s>1&&a.loadError<s)t.levelRetry=!0,this.redundantFailover(e);else if(-1===this.manualLevelIndex){for(var o=-1,l=this._levels,u=l.length;u--;){var d=(u+this.currentLevelIndex)%l.length;if(d!==this.currentLevelIndex&&0===l[d].loadError){o=d;break}}o>-1&&this.currentLevelIndex!==o&&(this.warn(n+": switch to "+o),t.levelRetry=!0,this.hls.nextAutoLevel=o)}}},m.redundantFailover=function(t){var e=this._levels[t],r=e.url.length;if(r>1){var i=(e.urlId+1)%r;this.warn("Switching to redundant URL-id "+i),this._levels.forEach((function(t){t.urlId=i})),this.level=t}},m.onFragLoaded=function(t,e){var r=e.frag;if(void 0!==r&&r.type===u.PlaylistLevelType.MAIN){var i=this._levels[r.level];void 0!==i&&(i.fragmentError=0,i.loadError=0)}},m.onLevelLoaded=function(t,e){var r,i,n=e.level,a=e.details,s=this._levels[n];if(!s)return this.warn("Invalid level index "+n),void(null!==(i=e.deliveryDirectives)&&void 0!==i&&i.skip&&(a.deltaUpdateFailed=!0));n===this.currentLevelIndex?(0===s.fragmentError&&(s.loadError=0,this.retryCount=0),this.playlistLoaded(n,e,s.details)):null!==(r=e.deliveryDirectives)&&void 0!==r&&r.skip&&(a.deltaUpdateFailed=!0)},m.onAudioTrackSwitched=function(t,e){var r=this.hls.levels[this.currentLevelIndex];if(r&&r.audioGroupIds){for(var i=-1,n=this.hls.audioTracks[e.id].groupId,a=0;a<r.audioGroupIds.length;a++)if(r.audioGroupIds[a]===n){i=a;break}i!==r.urlId&&(r.urlId=i,this.startLoad())}},m.loadPlaylist=function(t){var e=this.currentLevelIndex,r=this._levels[e];if(this.canLoad&&r&&r.url.length>0){var i=r.urlId,a=r.url[i];if(t)try{a=t.addDirectives(a)}catch(t){this.warn("Could not construct new URL with HLS Delivery Directives: "+t)}this.log("Attempt loading level index "+e+(t?" at sn "+t.msn+" part "+t.part:"")+" with URL-id "+i+" "+a),this.clearTimer(),this.hls.trigger(n.Events.LEVEL_LOADING,{url:a,level:e,id:i,deliveryDirectives:t||null})}},m.removeLevel=function(t,e){var r=function(t,r){return r!==e},i=this._levels.filter((function(i,n){return n!==t||i.url.length>1&&void 0!==e&&(i.url=i.url.filter(r),i.audioGroupIds&&(i.audioGroupIds=i.audioGroupIds.filter(r)),i.textGroupIds&&(i.textGroupIds=i.textGroupIds.filter(r)),i.urlId=0,!0)})).map((function(t,e){var r=t.details;return null!=r&&r.fragments&&r.fragments.forEach((function(t){t.level=e})),t}));this._levels=i,this.hls.trigger(n.Events.LEVELS_UPDATED,{levels:i})},g=l,(v=[{key:"levels",get:function(){return 0===this._levels.length?null:this._levels}},{key:"level",get:function(){return this.currentLevelIndex},set:function(t){var e,r=this._levels;if(0!==r.length&&(this.currentLevelIndex!==t||null===(e=r[t])||void 0===e||!e.details)){if(t<0||t>=r.length){var i=t<0;if(this.hls.trigger(n.Events.ERROR,{type:a.ErrorTypes.OTHER_ERROR,details:a.ErrorDetails.LEVEL_SWITCH_ERROR,level:t,fatal:i,reason:"invalid level idx"}),i)return;t=Math.min(t,r.length-1)}this.clearTimer();var s=this.currentLevelIndex,o=r[s],l=r[t];this.log("switching to level "+t+" from "+s),this.currentLevelIndex=t;var u=d({},l,{level:t,maxBitrate:l.maxBitrate,uri:l.uri,urlId:l.urlId});delete u._urlId,this.hls.trigger(n.Events.LEVEL_SWITCHING,u);var c=l.details;if(!c||c.live){var h=this.switchParams(l.uri,null==o?void 0:o.details);this.loadPlaylist(h)}}}},{key:"manualLevel",get:function(){return this.manualLevelIndex},set:function(t){this.manualLevelIndex=t,void 0===this._startLevel&&(this._startLevel=t),-1!==t&&(this.level=t)}},{key:"firstLevel",get:function(){return this._firstLevel},set:function(t){this._firstLevel=t}},{key:"startLevel",get:function(){if(void 0===this._startLevel){var t=this.hls.config.startLevel;return void 0!==t?t:this._firstLevel}return this._startLevel},set:function(t){this._startLevel=t}},{key:"nextLoadLevel",get:function(){return-1!==this.manualLevelIndex?this.manualLevelIndex:this.hls.nextAutoLevel},set:function(t){this.level=t,-1===this.manualLevelIndex&&(this.hls.nextAutoLevel=t)}}])&&c(g.prototype,v),p&&c(g,p),Object.defineProperty(g,"prototype",{writable:!1}),l}(l.default)},"./src/controller/level-helper.ts":
/*!****************************************!*\
  !*** ./src/controller/level-helper.ts ***!
  \****************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{addGroupId:()=>o,addSliding:()=>p,adjustSliding:()=>v,assignTrackIdsByGroup:()=>l,computeReloadInterval:()=>m,getFragmentWithSN:()=>y,getPartWith:()=>E,mapFragmentIntersection:()=>g,mapPartIntersection:()=>f,mergeDetails:()=>h,updateFragPTSDTS:()=>c,updatePTS:()=>u});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../utils/logger */"./src/utils/logger.ts"),a=r(/*! ../loader/date-range */"./src/loader/date-range.ts");function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function o(t,e,r){switch(e){case"audio":t.audioGroupIds||(t.audioGroupIds=[]),t.audioGroupIds.push(r);break;case"text":t.textGroupIds||(t.textGroupIds=[]),t.textGroupIds.push(r)}}function l(t){var e={};t.forEach((function(t){var r=t.groupId||"";t.id=e[r]=e[r]||0,e[r]++}))}function u(t,e,r){d(t[e],t[r])}function d(t,e){var r=e.startPTS;if((0,i.isFiniteNumber)(r)){var n,a=0;e.sn>t.sn?(a=r-t.start,n=t):(a=t.start-r,n=e),n.duration!==a&&(n.duration=a)}else e.sn>t.sn?t.cc===e.cc&&t.minEndPTS?e.start=t.start+(t.minEndPTS-t.start):e.start=t.start+t.duration:e.start=Math.max(t.start-e.duration,0)}function c(t,e,r,a,s,o){a-r<=0&&(n.logger.warn("Fragment should have a positive duration",e),a=r+e.duration,o=s+e.duration);var l=r,u=a,c=e.startPTS,h=e.endPTS;if((0,i.isFiniteNumber)(c)){var f=Math.abs(c-r);(0,i.isFiniteNumber)(e.deltaPTS)?e.deltaPTS=Math.max(f,e.deltaPTS):e.deltaPTS=f,l=Math.max(r,c),r=Math.min(r,c),s=Math.min(s,e.startDTS),u=Math.min(a,h),a=Math.max(a,h),o=Math.max(o,e.endDTS)}e.duration=a-r;var g=r-e.start;e.appendedPTS=a,e.start=e.startPTS=r,e.maxStartPTS=l,e.startDTS=s,e.endPTS=a,e.minEndPTS=u,e.endDTS=o;var v,p=e.sn;if(!t||p<t.startSN||p>t.endSN)return 0;var m=p-t.startSN,y=t.fragments;for(y[m]=e,v=m;v>0;v--)d(y[v],y[v-1]);for(v=m;v<y.length-1;v++)d(y[v],y[v+1]);return t.fragmentHint&&d(y[y.length-1],t.fragmentHint),t.PTSKnown=t.alignedSliding=!0,g}function h(t,e){for(var r=null,o=t.fragments,l=o.length-1;l>=0;l--){var u=o[l].initSegment;if(u){r=u;break}}t.fragmentHint&&delete t.fragmentHint.endPTS;var d,h,p,m,y,E=0;if(g(t,e,(function(t,n){t.relurl&&(E=t.cc-n.cc),(0,i.isFiniteNumber)(t.startPTS)&&(0,i.isFiniteNumber)(t.endPTS)&&(n.start=n.startPTS=t.startPTS,n.startDTS=t.startDTS,n.appendedPTS=t.appendedPTS,n.maxStartPTS=t.maxStartPTS,n.endPTS=t.endPTS,n.endDTS=t.endDTS,n.minEndPTS=t.minEndPTS,n.duration=t.endPTS-t.startPTS,n.duration&&(d=n),e.PTSKnown=e.alignedSliding=!0),n.elementaryStreams=t.elementaryStreams,n.loader=t.loader,n.stats=t.stats,n.urlId=t.urlId,t.initSegment&&(n.initSegment=t.initSegment,r=t.initSegment)})),r&&(e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments).forEach((function(t){var e;t.initSegment&&t.initSegment.relurl!==(null===(e=r)||void 0===e?void 0:e.relurl)||(t.initSegment=r)})),e.skippedSegments)if(e.deltaUpdateFailed=e.fragments.some((function(t){return!t})),e.deltaUpdateFailed){n.logger.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(var T=e.skippedSegments;T--;)e.fragments.shift();e.startSN=e.fragments[0].sn,e.startCC=e.fragments[0].cc}else e.canSkipDateRanges&&(e.dateRanges=(h=t.dateRanges,p=e.dateRanges,m=e.recentlyRemovedDateranges,y=s({},h),m&&m.forEach((function(t){delete y[t]})),Object.keys(p).forEach((function(t){var e=new a.DateRange(p[t].attr,y[t]);e.isValid?y[t]=e:n.logger.warn('Ignoring invalid Playlist Delta Update DATERANGE tag: "'+JSON.stringify(p[t].attr)+'"')})),y));var S=e.fragments;if(E){n.logger.warn("discontinuity sliding from playlist, take drift into account");for(var b=0;b<S.length;b++)S[b].cc+=E}e.skippedSegments&&(e.startCC=e.fragments[0].cc),f(t.partList,e.partList,(function(t,e){e.elementaryStreams=t.elementaryStreams,e.stats=t.stats})),d?c(e,d,d.startPTS,d.endPTS,d.startDTS,d.endDTS):v(t,e),S.length&&(e.totalduration=e.edge-S[0].start),e.driftStartTime=t.driftStartTime,e.driftStart=t.driftStart;var D=e.advancedDateTime;if(e.advanced&&D){var L=e.edge;e.driftStart||(e.driftStartTime=D,e.driftStart=L),e.driftEndTime=D,e.driftEnd=L}else e.driftEndTime=t.driftEndTime,e.driftEnd=t.driftEnd,e.advancedDateTime=t.advancedDateTime}function f(t,e,r){if(t&&e)for(var i=0,n=0,a=t.length;n<=a;n++){var s=t[n],o=e[n+i];s&&o&&s.index===o.index&&s.fragment.sn===o.fragment.sn?r(s,o):i--}}function g(t,e,r){for(var i=e.skippedSegments,n=Math.max(t.startSN,e.startSN)-e.startSN,a=(t.fragmentHint?1:0)+(i?e.endSN:Math.min(t.endSN,e.endSN))-e.startSN,s=e.startSN-t.startSN,o=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments,l=t.fragmentHint?t.fragments.concat(t.fragmentHint):t.fragments,u=n;u<=a;u++){var d=l[s+u],c=o[u];i&&!c&&u<i&&(c=e.fragments[u]=d),d&&c&&r(d,c)}}function v(t,e){var r=e.startSN+e.skippedSegments-t.startSN,i=t.fragments;r<0||r>=i.length||p(e,i[r].start)}function p(t,e){if(e){for(var r=t.fragments,i=t.skippedSegments;i<r.length;i++)r[i].start+=e;t.fragmentHint&&(t.fragmentHint.start+=e)}}function m(t,e){var r,i=1e3*t.levelTargetDuration,n=i/2,a=t.age,s=a>0&&a<3*i,o=e.loading.end-e.loading.start,l=t.availabilityDelay;if(!1===t.updated)if(s){var u=333*t.misses;r=Math.max(Math.min(n,2*o),u),t.availabilityDelay=(t.availabilityDelay||0)+r}else r=n;else s?(l=Math.min(l||i/2,a),t.availabilityDelay=l,r=l+i-a):r=i-o;return Math.round(r)}function y(t,e,r){if(!t||!t.details)return null;var i=t.details,n=i.fragments[e-i.startSN];return n||((n=i.fragmentHint)&&n.sn===e?n:e<i.startSN&&r&&r.sn===e?r:null)}function E(t,e,r){if(!t||!t.details)return null;var i=t.details.partList;if(i)for(var n=i.length;n--;){var a=i[n];if(a.index===r&&a.fragment.sn===e)return a}return null}},"./src/controller/stream-controller.ts":
/*!*********************************************!*\
  !*** ./src/controller/stream-controller.ts ***!
  \*********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>m});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ./base-stream-controller */"./src/controller/base-stream-controller.ts"),a=r(/*! ../is-supported */"./src/is-supported.ts"),s=r(/*! ../events */"./src/events.ts"),o=r(/*! ../utils/buffer-helper */"./src/utils/buffer-helper.ts"),l=r(/*! ./fragment-tracker */"./src/controller/fragment-tracker.ts"),u=r(/*! ../types/loader */"./src/types/loader.ts"),d=r(/*! ../loader/fragment */"./src/loader/fragment.ts"),c=r(/*! ../demux/transmuxer-interface */"./src/demux/transmuxer-interface.ts"),h=r(/*! ../types/transmuxer */"./src/types/transmuxer.ts"),f=r(/*! ./gap-controller */"./src/controller/gap-controller.ts"),g=r(/*! ../errors */"./src/errors.ts");function v(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function p(t,e){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var m=function(t){var e,r;function m(e,r){var i;return(i=t.call(this,e,r,"[stream-controller]")||this).audioCodecSwap=!1,i.gapController=null,i.level=-1,i._forceStartLoad=!1,i.altAudio=!1,i.audioOnly=!1,i.fragPlaying=null,i.onvplaying=null,i.onvseeked=null,i.fragLastKbps=0,i.couldBacktrack=!1,i.backtrackFragment=null,i.audioCodecSwitch=!1,i.videoBuffer=null,i._registerListeners(),i}r=t,(e=m).prototype=Object.create(r.prototype),e.prototype.constructor=e,p(e,r);var y,E,T,S=m.prototype;return S._registerListeners=function(){var t=this.hls;t.on(s.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(s.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(s.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(s.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.on(s.Events.LEVEL_LOADING,this.onLevelLoading,this),t.on(s.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.on(s.Events.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.on(s.Events.ERROR,this.onError,this),t.on(s.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.on(s.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.on(s.Events.BUFFER_CREATED,this.onBufferCreated,this),t.on(s.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),t.on(s.Events.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(s.Events.FRAG_BUFFERED,this.onFragBuffered,this)},S._unregisterListeners=function(){var t=this.hls;t.off(s.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(s.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(s.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(s.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.off(s.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.off(s.Events.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.off(s.Events.ERROR,this.onError,this),t.off(s.Events.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.off(s.Events.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.off(s.Events.BUFFER_CREATED,this.onBufferCreated,this),t.off(s.Events.BUFFER_FLUSHED,this.onBufferFlushed,this),t.off(s.Events.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(s.Events.FRAG_BUFFERED,this.onFragBuffered,this)},S.onHandlerDestroying=function(){this._unregisterListeners(),this.onMediaDetaching()},S.startLoad=function(t){if(this.levels){var e=this.lastCurrentTime,r=this.hls;if(this.stopLoad(),this.setInterval(100),this.level=-1,this.fragLoadError=0,!this.startFragRequested){var i=r.startLevel;-1===i&&(r.config.testBandwidth&&this.levels.length>1?(i=0,this.bitrateTest=!0):i=r.nextAutoLevel),this.level=r.nextLoadLevel=i,this.loadedmetadata=!1}e>0&&-1===t&&(this.log("Override startPosition with lastCurrentTime @"+e.toFixed(3)),t=e),this.state=n.State.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()}else this._forceStartLoad=!0,this.state=n.State.STOPPED},S.stopLoad=function(){this._forceStartLoad=!1,t.prototype.stopLoad.call(this)},S.doTick=function(){switch(this.state){case n.State.IDLE:this.doTickIdle();break;case n.State.WAITING_LEVEL:var t,e=this.levels,r=this.level,i=null==e||null===(t=e[r])||void 0===t?void 0:t.details;if(i&&(!i.live||this.levelLastLoaded===this.level)){if(this.waitForCdnTuneIn(i))break;this.state=n.State.IDLE;break}break;case n.State.FRAG_LOADING_WAITING_RETRY:var a,s=self.performance.now(),o=this.retryDate;(!o||s>=o||null!==(a=this.media)&&void 0!==a&&a.seeking)&&(this.log("retryDate reached, switch back to IDLE state"),this.resetStartWhenNotLoaded(this.level),this.state=n.State.IDLE)}this.onTickEnd()},S.onTickEnd=function(){t.prototype.onTickEnd.call(this),this.checkBuffer(),this.checkFragmentChanged()},S.doTickIdle=function(){var t,e,r=this.hls,i=this.levelLastLoaded,a=this.levels,o=this.media,c=r.config,h=r.nextLoadLevel;if(null!==i&&(o||!this.startFragRequested&&c.startFragPrefetch)&&(!this.altAudio||!this.audioOnly)&&a&&a[h]){var f=a[h];this.level=r.nextLoadLevel=h;var g=f.details;if(!g||this.state===n.State.WAITING_LEVEL||g.live&&this.levelLastLoaded!==h)this.state=n.State.WAITING_LEVEL;else{var v=this.getMainFwdBufferInfo();if(null!==v&&!(v.len>=this.getMaxBufferLength(f.maxBitrate))){if(this._streamEnded(v,g)){var p={};return this.altAudio&&(p.type="video"),this.hls.trigger(s.Events.BUFFER_EOS,p),void(this.state=n.State.ENDED)}this.backtrackFragment&&this.backtrackFragment.start>v.end&&(this.backtrackFragment=null);var m=this.backtrackFragment?this.backtrackFragment.start:v.end,y=this.getNextFragment(m,g);if(this.couldBacktrack&&!this.fragPrevious&&y&&"initSegment"!==y.sn&&this.fragmentTracker.getState(y)!==l.FragmentState.OK){var E,T=(null!=(E=this.backtrackFragment)?E:y).sn-g.startSN,S=g.fragments[T-1];S&&y.cc===S.cc&&(y=S,this.fragmentTracker.removeFragment(S))}else this.backtrackFragment&&v.len&&(this.backtrackFragment=null);if(y&&this.fragmentTracker.getState(y)===l.FragmentState.OK&&this.nextLoadPosition>m){var b=this.audioOnly&&!this.altAudio?d.ElementaryStreamTypes.AUDIO:d.ElementaryStreamTypes.VIDEO;o&&this.afterBufferFlushed(o,b,u.PlaylistLevelType.MAIN),y=this.getNextFragment(this.nextLoadPosition,g)}y&&(!y.initSegment||y.initSegment.data||this.bitrateTest||(y=y.initSegment),"identity"!==(null===(t=y.decryptdata)||void 0===t?void 0:t.keyFormat)||null!==(e=y.decryptdata)&&void 0!==e&&e.key?this.loadFragment(y,g,m):this.loadKey(y,g))}}}},S.loadFragment=function(e,r,i){var n,a=this.fragmentTracker.getState(e);this.fragCurrent=e,a===l.FragmentState.NOT_LOADED?"initSegment"===e.sn?this._loadInitSegment(e):this.bitrateTest?(this.log("Fragment "+e.sn+" of level "+e.level+" is being downloaded to test bitrate and will not be buffered"),this._loadBitrateTestFrag(e)):(this.startFragRequested=!0,t.prototype.loadFragment.call(this,e,r,i)):a===l.FragmentState.APPENDING?this.reduceMaxBufferLength(e.duration)&&this.fragmentTracker.removeFragment(e):0===(null===(n=this.media)||void 0===n?void 0:n.buffered.length)&&this.fragmentTracker.removeAllFragments()},S.getAppendedFrag=function(t){var e=this.fragmentTracker.getAppendedFrag(t,u.PlaylistLevelType.MAIN);return e&&"fragment"in e?e.fragment:e},S.getBufferedFrag=function(t){return this.fragmentTracker.getBufferedFrag(t,u.PlaylistLevelType.MAIN)},S.followingBufferedFrag=function(t){return t?this.getBufferedFrag(t.end+.5):null},S.immediateLevelSwitch=function(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)},S.nextLevelSwitch=function(){var t=this.levels,e=this.media;if(null!=e&&e.readyState){var r,i=this.getAppendedFrag(e.currentTime);if(i&&i.start>1&&this.flushMainBuffer(0,i.start-1),!e.paused&&t){var n=t[this.hls.nextLoadLevel],a=this.fragLastKbps;r=a&&this.fragCurrent?this.fragCurrent.duration*n.maxBitrate/(1e3*a)+1:0}else r=0;var s=this.getBufferedFrag(e.currentTime+r);if(s){var o=this.followingBufferedFrag(s);if(o){this.abortCurrentFrag();var l=o.maxStartPTS?o.maxStartPTS:o.start,u=o.duration,d=Math.max(s.end,l+Math.min(Math.max(u-this.config.maxFragLookUpTolerance,.5*u),.75*u));this.flushMainBuffer(d,Number.POSITIVE_INFINITY)}}}},S.abortCurrentFrag=function(){var t=this.fragCurrent;switch(this.fragCurrent=null,this.backtrackFragment=null,null!=t&&t.loader&&t.loader.abort(),this.state){case n.State.KEY_LOADING:case n.State.FRAG_LOADING:case n.State.FRAG_LOADING_WAITING_RETRY:case n.State.PARSING:case n.State.PARSED:this.state=n.State.IDLE}this.nextLoadPosition=this.getLoadPosition()},S.flushMainBuffer=function(e,r){t.prototype.flushMainBuffer.call(this,e,r,this.altAudio?"video":null)},S.onMediaAttached=function(e,r){t.prototype.onMediaAttached.call(this,e,r);var i=r.media;this.onvplaying=this.onMediaPlaying.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),i.addEventListener("playing",this.onvplaying),i.addEventListener("seeked",this.onvseeked),this.gapController=new f.default(this.config,i,this.fragmentTracker,this.hls)},S.onMediaDetaching=function(){var e=this.media;e&&this.onvplaying&&this.onvseeked&&(e.removeEventListener("playing",this.onvplaying),e.removeEventListener("seeked",this.onvseeked),this.onvplaying=this.onvseeked=null,this.videoBuffer=null),this.fragPlaying=null,this.gapController&&(this.gapController.destroy(),this.gapController=null),t.prototype.onMediaDetaching.call(this)},S.onMediaPlaying=function(){this.tick()},S.onMediaSeeked=function(){var t=this.media,e=t?t.currentTime:null;(0,i.isFiniteNumber)(e)&&this.log("Media seeked to "+e.toFixed(3)),this.tick()},S.onManifestLoading=function(){this.log("Trigger BUFFER_RESET"),this.hls.trigger(s.Events.BUFFER_RESET,void 0),this.fragmentTracker.removeAllFragments(),this.couldBacktrack=!1,this.startPosition=this.lastCurrentTime=0,this.fragPlaying=null,this.backtrackFragment=null},S.onManifestParsed=function(t,e){var r,i=!1,n=!1;e.levels.forEach((function(t){(r=t.audioCodec)&&(-1!==r.indexOf("mp4a.40.2")&&(i=!0),-1!==r.indexOf("mp4a.40.5")&&(n=!0))})),this.audioCodecSwitch=i&&n&&!(0,a.changeTypeSupported)(),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=e.levels,this.startFragRequested=!1},S.onLevelLoading=function(t,e){var r=this.levels;if(r&&this.state===n.State.IDLE){var i=r[e.level];(!i.details||i.details.live&&this.levelLastLoaded!==e.level||this.waitForCdnTuneIn(i.details))&&(this.state=n.State.WAITING_LEVEL)}},S.onLevelLoaded=function(t,e){var r,i=this.levels,a=e.level,o=e.details,l=o.totalduration;if(i){this.log("Level "+a+" loaded ["+o.startSN+","+o.endSN+"], cc ["+o.startCC+", "+o.endCC+"] duration:"+l);var u=this.fragCurrent;!u||this.state!==n.State.FRAG_LOADING&&this.state!==n.State.FRAG_LOADING_WAITING_RETRY||u.level!==e.level&&u.loader&&(this.state=n.State.IDLE,this.backtrackFragment=null,u.loader.abort());var d=i[a],c=0;if(o.live||null!==(r=d.details)&&void 0!==r&&r.live){if(o.fragments[0]||(o.deltaUpdateFailed=!0),o.deltaUpdateFailed)return;c=this.alignPlaylists(o,d.details)}if(d.details=o,this.levelLastLoaded=a,this.hls.trigger(s.Events.LEVEL_UPDATED,{details:o,level:a}),this.state===n.State.WAITING_LEVEL){if(this.waitForCdnTuneIn(o))return;this.state=n.State.IDLE}this.startFragRequested?o.live&&this.synchronizeToLiveEdge(o):this.setStartPosition(o,c),this.tick()}else this.warn("Levels were reset while loading level "+a)},S._handleFragmentLoadProgress=function(t){var e,r=t.frag,i=t.part,n=t.payload,a=this.levels;if(a){var s=a[r.level],o=s.details;if(o){var l=s.videoCodec,d=o.PTSKnown||!o.live,f=null===(e=r.initSegment)||void 0===e?void 0:e.data,g=this._getAudioCodec(s),v=this.transmuxer=this.transmuxer||new c.default(this.hls,u.PlaylistLevelType.MAIN,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),p=i?i.index:-1,m=-1!==p,y=new h.ChunkMetadata(r.level,r.sn,r.stats.chunkCount,n.byteLength,p,m),E=this.initPTS[r.cc];v.push(n,f,g,l,r,i,o.totalduration,d,y,E)}else this.warn("Dropping fragment "+r.sn+" of level "+r.level+" after level details were reset")}else this.warn("Levels were reset while fragment load was in progress. Fragment "+r.sn+" of level "+r.level+" will not be buffered")},S.onAudioTrackSwitching=function(t,e){var r=this.altAudio,i=!!e.url,n=e.id;if(!i){if(this.mediaBuffer!==this.media){this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;var a=this.fragCurrent;null!=a&&a.loader&&(this.log("Switching to main audio track, cancel main fragment load"),a.loader.abort()),this.resetTransmuxer(),this.resetLoadingState()}else this.audioOnly&&this.resetTransmuxer();var o=this.hls;r&&o.trigger(s.Events.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:"audio"}),o.trigger(s.Events.AUDIO_TRACK_SWITCHED,{id:n})}},S.onAudioTrackSwitched=function(t,e){var r=e.id,i=!!this.hls.audioTracks[r].url;if(i){var n=this.videoBuffer;n&&this.mediaBuffer!==n&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=n)}this.altAudio=i,this.tick()},S.onBufferCreated=function(t,e){var r,i,n=e.tracks,a=!1;for(var s in n){var o=n[s];if("main"===o.id){if(i=s,r=o,"video"===s){var l=n[s];l&&(this.videoBuffer=l.buffer)}}else a=!0}a&&r?(this.log("Alternate track found, use "+i+".buffered to schedule main fragment loading"),this.mediaBuffer=r.buffer):this.mediaBuffer=this.media},S.onFragBuffered=function(t,e){var r=e.frag,i=e.part;if(!r||r.type===u.PlaylistLevelType.MAIN){if(this.fragContextChanged(r))return this.warn("Fragment "+r.sn+(i?" p: "+i.index:"")+" of level "+r.level+" finished buffering, but was aborted. state: "+this.state),void(this.state===n.State.PARSED&&(this.state=n.State.IDLE));var a=i?i.stats:r.stats;this.fragLastKbps=Math.round(8*a.total/(a.buffering.end-a.loading.first)),"initSegment"!==r.sn&&(this.fragPrevious=r),this.fragBufferedComplete(r,i)}},S.onError=function(t,e){switch(e.details){case g.ErrorDetails.FRAG_LOAD_ERROR:case g.ErrorDetails.FRAG_LOAD_TIMEOUT:case g.ErrorDetails.KEY_LOAD_ERROR:case g.ErrorDetails.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(u.PlaylistLevelType.MAIN,e);break;case g.ErrorDetails.LEVEL_LOAD_ERROR:case g.ErrorDetails.LEVEL_LOAD_TIMEOUT:this.state!==n.State.ERROR&&(e.fatal?(this.warn(""+e.details),this.state=n.State.ERROR):e.levelRetry||this.state!==n.State.WAITING_LEVEL||(this.state=n.State.IDLE));break;case g.ErrorDetails.BUFFER_FULL_ERROR:if("main"===e.parent&&(this.state===n.State.PARSING||this.state===n.State.PARSED)){var r=!0,i=this.getFwdBufferInfo(this.media,u.PlaylistLevelType.MAIN);i&&i.len>.5&&(r=!this.reduceMaxBufferLength(i.len)),r&&(this.warn("buffer full error also media.currentTime is not buffered, flush main"),this.immediateLevelSwitch()),this.resetLoadingState()}}},S.checkBuffer=function(){var t=this.media,e=this.gapController;if(t&&e&&t.readyState){if(this.loadedmetadata||!o.BufferHelper.getBuffered(t).length){var r=this.state!==n.State.IDLE?this.fragCurrent:null;e.poll(this.lastCurrentTime,r)}this.lastCurrentTime=t.currentTime}},S.onFragLoadEmergencyAborted=function(){this.state=n.State.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tickImmediate()},S.onBufferFlushed=function(t,e){var r=e.type;if(r!==d.ElementaryStreamTypes.AUDIO||this.audioOnly&&!this.altAudio){var i=(r===d.ElementaryStreamTypes.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;this.afterBufferFlushed(i,r,u.PlaylistLevelType.MAIN)}},S.onLevelsUpdated=function(t,e){this.levels=e.levels},S.swapAudioCodec=function(){this.audioCodecSwap=!this.audioCodecSwap},S.seekToStartPos=function(){var t=this.media;if(t){var e=t.currentTime,r=this.startPosition;if(r>=0&&e<r){if(t.seeking)return void this.log("could not seek to "+r+", already seeking at "+e);var i=o.BufferHelper.getBuffered(t),n=(i.length?i.start(0):0)-r;n>0&&(n<this.config.maxBufferHole||n<this.config.maxFragLookUpTolerance)&&(this.log("adjusting start position by "+n+" to match buffer start"),r+=n,this.startPosition=r),this.log("seek to target start position "+r+" from current time "+e),t.currentTime=r}}},S._getAudioCodec=function(t){var e=this.config.defaultAudioCodec||t.audioCodec;return this.audioCodecSwap&&e&&(this.log("Swapping audio codec"),e=-1!==e.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5"),e},S._loadBitrateTestFrag=function(t){var e=this;t.bitrateTest=!0,this._doFragLoad(t).then((function(r){var i=e.hls;if(r&&!i.nextLoadLevel&&!e.fragContextChanged(t)){e.fragLoadError=0,e.state=n.State.IDLE,e.startFragRequested=!1,e.bitrateTest=!1;var a=t.stats;a.parsing.start=a.parsing.end=a.buffering.start=a.buffering.end=self.performance.now(),i.trigger(s.Events.FRAG_LOADED,r),t.bitrateTest=!1}}))},S._handleTransmuxComplete=function(t){var e,r="main",a=this.hls,o=t.remuxResult,l=t.chunkMeta,u=this.getCurrentContext(l);if(!u)return this.warn("The loading context changed while buffering fragment "+l.sn+" of level "+l.level+". This chunk will not be buffered."),void this.resetStartWhenNotLoaded(l.level);var c=u.frag,h=u.part,f=u.level,g=o.video,v=o.text,p=o.id3,m=o.initSegment,y=f.details,E=this.altAudio?void 0:o.audio;if(!this.fragContextChanged(c)){if(this.state=n.State.PARSING,m){m.tracks&&(this._bufferInitSegment(f,m.tracks,c,l),a.trigger(s.Events.FRAG_PARSING_INIT_SEGMENT,{frag:c,id:r,tracks:m.tracks}));var T=m.initPTS,S=m.timescale;(0,i.isFiniteNumber)(T)&&(this.initPTS[c.cc]=T,a.trigger(s.Events.INIT_PTS_FOUND,{frag:c,id:r,initPTS:T,timescale:S}))}if(g&&!1!==o.independent){if(y){var b=g.startPTS,D=g.endPTS,L=g.startDTS,A=g.endDTS;if(h)h.elementaryStreams[g.type]={startPTS:b,endPTS:D,startDTS:L,endDTS:A};else if(g.firstKeyFrame&&g.independent&&(this.couldBacktrack=!0),g.dropped&&g.independent){var R=this.getMainFwdBufferInfo();if((R?R.end:this.getLoadPosition())+this.config.maxBufferHole<(g.firstKeyFramePTS?g.firstKeyFramePTS:b)-this.config.maxBufferHole)return void this.backtrack(c);c.setElementaryStreamInfo(g.type,c.start,D,c.start,A,!0)}c.setElementaryStreamInfo(g.type,b,D,L,A),this.backtrackFragment&&(this.backtrackFragment=c),this.bufferFragmentData(g,c,h,l)}}else if(!1===o.independent)return void this.backtrack(c);if(E){var k=E.startPTS,I=E.endPTS,_=E.startDTS,C=E.endDTS;h&&(h.elementaryStreams[d.ElementaryStreamTypes.AUDIO]={startPTS:k,endPTS:I,startDTS:_,endDTS:C}),c.setElementaryStreamInfo(d.ElementaryStreamTypes.AUDIO,k,I,_,C),this.bufferFragmentData(E,c,h,l)}if(y&&null!=p&&null!==(e=p.samples)&&void 0!==e&&e.length){var w={id:r,frag:c,details:y,samples:p.samples};a.trigger(s.Events.FRAG_PARSING_METADATA,w)}if(y&&v){var O={id:r,frag:c,details:y,samples:v.samples};a.trigger(s.Events.FRAG_PARSING_USERDATA,O)}}},S._bufferInitSegment=function(t,e,r,i){var a=this;if(this.state===n.State.PARSING){this.audioOnly=!!e.audio&&!e.video,this.altAudio&&!this.audioOnly&&delete e.audio;var o=e.audio,l=e.video,u=e.audiovideo;if(o){var d=t.audioCodec,c=navigator.userAgent.toLowerCase();this.audioCodecSwitch&&(d&&(d=-1!==d.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5"),1!==o.metadata.channelCount&&-1===c.indexOf("firefox")&&(d="mp4a.40.5")),-1!==c.indexOf("android")&&"audio/mpeg"!==o.container&&(d="mp4a.40.2",this.log("Android: force audio codec to "+d)),t.audioCodec&&t.audioCodec!==d&&this.log('Swapping manifest audio codec "'+t.audioCodec+'" for "'+d+'"'),o.levelCodec=d,o.id="main",this.log("Init audio buffer, container:"+o.container+", codecs[selected/level/parsed]=["+(d||"")+"/"+(t.audioCodec||"")+"/"+o.codec+"]")}l&&(l.levelCodec=t.videoCodec,l.id="main",this.log("Init video buffer, container:"+l.container+", codecs[level/parsed]=["+(t.videoCodec||"")+"/"+l.codec+"]")),u&&this.log("Init audiovideo buffer, container:"+u.container+", codecs[level/parsed]=["+(t.attrs.CODECS||"")+"/"+u.codec+"]"),this.hls.trigger(s.Events.BUFFER_CODECS,e),Object.keys(e).forEach((function(t){var n=e[t].initSegment;null!=n&&n.byteLength&&a.hls.trigger(s.Events.BUFFER_APPENDING,{type:t,data:n,frag:r,part:null,chunkMeta:i,parent:r.type})})),this.tick()}},S.getMainFwdBufferInfo=function(){return this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:this.media,u.PlaylistLevelType.MAIN)},S.backtrack=function(t){this.couldBacktrack=!0,this.backtrackFragment=t,this.resetTransmuxer(),this.flushBufferGap(t),this.fragmentTracker.removeFragment(t),this.fragPrevious=null,this.nextLoadPosition=t.start,this.state=n.State.IDLE},S.checkFragmentChanged=function(){var t=this.media,e=null;if(t&&t.readyState>1&&!1===t.seeking){var r=t.currentTime;if(o.BufferHelper.isBuffered(t,r)?e=this.getAppendedFrag(r):o.BufferHelper.isBuffered(t,r+.1)&&(e=this.getAppendedFrag(r+.1)),e){this.backtrackFragment=null;var i=this.fragPlaying,n=e.level;i&&e.sn===i.sn&&i.level===n&&e.urlId===i.urlId||(this.hls.trigger(s.Events.FRAG_CHANGED,{frag:e}),i&&i.level===n||this.hls.trigger(s.Events.LEVEL_SWITCHED,{level:n}),this.fragPlaying=e)}}},y=m,(E=[{key:"nextLevel",get:function(){var t=this.nextBufferedFrag;return t?t.level:-1}},{key:"currentFrag",get:function(){var t=this.media;return t?this.fragPlaying||this.getAppendedFrag(t.currentTime):null}},{key:"currentProgramDateTime",get:function(){var t=this.media;if(t){var e=t.currentTime,r=this.currentFrag;if(r&&(0,i.isFiniteNumber)(e)&&(0,i.isFiniteNumber)(r.programDateTime)){var n=r.programDateTime+1e3*(e-r.start);return new Date(n)}}return null}},{key:"currentLevel",get:function(){var t=this.currentFrag;return t?t.level:-1}},{key:"nextBufferedFrag",get:function(){var t=this.currentFrag;return t?this.followingBufferedFrag(t):null}},{key:"forceStartLoad",get:function(){return this._forceStartLoad}}])&&v(y.prototype,E),T&&v(y,T),Object.defineProperty(y,"prototype",{writable:!1}),m}(n.default)},"./src/controller/subtitle-stream-controller.ts":
/*!******************************************************!*\
  !*** ./src/controller/subtitle-stream-controller.ts ***!
  \******************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{SubtitleStreamController:()=>g});var i=r(/*! ../events */"./src/events.ts"),n=r(/*! ../utils/buffer-helper */"./src/utils/buffer-helper.ts"),a=r(/*! ./fragment-finders */"./src/controller/fragment-finders.ts"),s=r(/*! ../utils/discontinuities */"./src/utils/discontinuities.ts"),o=r(/*! ./level-helper */"./src/controller/level-helper.ts"),l=r(/*! ./fragment-tracker */"./src/controller/fragment-tracker.ts"),u=r(/*! ./base-stream-controller */"./src/controller/base-stream-controller.ts"),d=r(/*! ../types/loader */"./src/types/loader.ts"),c=r(/*! ../types/level */"./src/types/level.ts");function h(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function f(t,e){return(f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var g=function(t){var e,r;function g(e,r){var i;return(i=t.call(this,e,r,"[subtitle-stream-controller]")||this).levels=[],i.currentTrackId=-1,i.tracksBuffered=[],i.mainDetails=null,i._registerListeners(),i}r=t,(e=g).prototype=Object.create(r.prototype),e.prototype.constructor=e,f(e,r);var p,m,y,E=g.prototype;return E.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null},E._registerListeners=function(){var t=this.hls;t.on(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(i.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(i.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(i.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.on(i.Events.ERROR,this.onError,this),t.on(i.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.on(i.Events.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),t.on(i.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.on(i.Events.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),t.on(i.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(i.Events.FRAG_BUFFERED,this.onFragBuffered,this)},E._unregisterListeners=function(){var t=this.hls;t.off(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(i.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(i.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(i.Events.LEVEL_LOADED,this.onLevelLoaded,this),t.off(i.Events.ERROR,this.onError,this),t.off(i.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.off(i.Events.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),t.off(i.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.off(i.Events.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),t.off(i.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(i.Events.FRAG_BUFFERED,this.onFragBuffered,this)},E.startLoad=function(t){this.stopLoad(),this.state=u.State.IDLE,this.setInterval(500),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()},E.onManifestLoading=function(){this.mainDetails=null,this.fragmentTracker.removeAllFragments()},E.onLevelLoaded=function(t,e){this.mainDetails=e.details},E.onSubtitleFragProcessed=function(t,e){var r=e.frag,i=e.success;if(this.fragPrevious=r,this.state=u.State.IDLE,i){var n=this.tracksBuffered[this.currentTrackId];if(n){for(var a,s=r.start,o=0;o<n.length;o++)if(s>=n[o].start&&s<=n[o].end){a=n[o];break}var l=r.start+r.duration;a?a.end=l:(a={start:s,end:l},n.push(a)),this.fragmentTracker.fragBuffered(r)}}},E.onBufferFlushing=function(t,e){var r=e.startOffset,i=e.endOffset;if(0===r&&i!==Number.POSITIVE_INFINITY){var n=this.currentTrackId,a=this.levels;if(!a.length||!a[n]||!a[n].details)return;var s=i-a[n].details.targetduration;if(s<=0)return;e.endOffsetSubtitles=Math.max(0,s),this.tracksBuffered.forEach((function(t){for(var e=0;e<t.length;)if(t[e].end<=s)t.shift();else{if(!(t[e].start<s))break;t[e].start=s,e++}})),this.fragmentTracker.removeFragmentsInRange(r,s,d.PlaylistLevelType.SUBTITLE)}},E.onFragBuffered=function(t,e){var r;this.loadedmetadata||e.frag.type!==d.PlaylistLevelType.MAIN||null!==(r=this.media)&&void 0!==r&&r.buffered.length&&(this.loadedmetadata=!0)},E.onError=function(t,e){var r,i=e.frag;i&&i.type===d.PlaylistLevelType.SUBTITLE&&(null!==(r=this.fragCurrent)&&void 0!==r&&r.loader&&this.fragCurrent.loader.abort(),this.state=u.State.IDLE)},E.onSubtitleTracksUpdated=function(t,e){var r=this,i=e.subtitleTracks;this.tracksBuffered=[],this.levels=i.map((function(t){return new c.Level(t)})),this.fragmentTracker.removeAllFragments(),this.fragPrevious=null,this.levels.forEach((function(t){r.tracksBuffered[t.id]=[]})),this.mediaBuffer=null},E.onSubtitleTrackSwitch=function(t,e){if(this.currentTrackId=e.id,this.levels.length&&-1!==this.currentTrackId){var r=this.levels[this.currentTrackId];null!=r&&r.details?this.mediaBuffer=this.mediaBufferTimeRanges:this.mediaBuffer=null,r&&this.setInterval(500)}else this.clearInterval()},E.onSubtitleTrackLoaded=function(t,e){var r,i=e.details,n=e.id,l=this.currentTrackId,d=this.levels;if(d.length){var c=d[l];if(!(n>=d.length||n!==l)&&c){this.mediaBuffer=this.mediaBufferTimeRanges;var h=0;if(i.live||null!==(r=c.details)&&void 0!==r&&r.live){var f=this.mainDetails;if(i.deltaUpdateFailed||!f)return;var g=f.fragments[0];c.details?0===(h=this.alignPlaylists(i,c.details))&&g&&(h=g.start,(0,o.addSliding)(i,h)):i.hasProgramDateTime&&f.hasProgramDateTime?((0,s.alignMediaPlaylistByPDT)(i,f),h=i.fragments[0].start):g&&(h=g.start,(0,o.addSliding)(i,h))}c.details=i,this.levelLastLoaded=n,this.startFragRequested||!this.mainDetails&&i.live||this.setStartPosition(c.details,h),this.tick(),i.live&&!this.fragCurrent&&this.media&&this.state===u.State.IDLE&&((0,a.findFragmentByPTS)(null,i.fragments,this.media.currentTime,0)||(this.warn("Subtitle playlist not aligned with playback"),c.details=void 0))}}},E._handleFragmentLoadComplete=function(t){var e=t.frag,r=t.payload,n=e.decryptdata,a=this.hls;if(!this.fragContextChanged(e)&&r&&r.byteLength>0&&n&&n.key&&n.iv&&"AES-128"===n.method){var s=performance.now();this.decrypter.webCryptoDecrypt(new Uint8Array(r),n.key.buffer,n.iv.buffer).then((function(t){var r=performance.now();a.trigger(i.Events.FRAG_DECRYPTED,{frag:e,payload:t,stats:{tstart:s,tdecrypt:r}})}))}},E.doTick=function(){if(this.media){if(this.state===u.State.IDLE){var t=this.currentTrackId,e=this.levels;if(!e.length||!e[t]||!e[t].details)return;var r=e[t].details,i=r.targetduration,s=this.config,o=this.getLoadPosition(),c=n.BufferHelper.bufferedInfo(this.tracksBuffered[this.currentTrackId]||[],o-i,s.maxBufferHole),h=c.end,f=c.len,g=this.getFwdBufferInfo(this.media,d.PlaylistLevelType.MAIN);if(f>this.getMaxBufferLength(null==g?void 0:g.len)+i)return;console.assert(r,"Subtitle track details are defined on idle subtitle stream controller tick");var v,p=r.fragments,m=p.length,y=r.edge,E=this.fragPrevious;if(h<y){var T=s.maxFragLookUpTolerance;!(v=(0,a.findFragmentByPTS)(E,p,Math.max(p[0].start,h),T))&&E&&E.start<p[0].start&&(v=p[0])}else v=p[m-1];if(!(v=this.mapToInitFragWhenRequired(v)))return;if(this.fragmentTracker.getState(v)!==l.FragmentState.NOT_LOADED)return;v.encrypted?this.loadKey(v,r):this.loadFragment(v,r,h)}}else this.state=u.State.IDLE},E.getMaxBufferLength=function(e){var r=t.prototype.getMaxBufferLength.call(this);return e?Math.max(r,e):r},E.loadFragment=function(e,r,i){this.fragCurrent=e,"initSegment"===e.sn?this._loadInitSegment(e):(this.startFragRequested=!0,t.prototype.loadFragment.call(this,e,r,i))},p=g,(m=[{key:"mediaBufferTimeRanges",get:function(){return new v(this.tracksBuffered[this.currentTrackId]||[])}}])&&h(p.prototype,m),y&&h(p,y),Object.defineProperty(p,"prototype",{writable:!1}),g}(u.default),v=function(t){this.buffered=void 0;var e=function(e,r,i){if((r>>>=0)>i-1)throw new DOMException("Failed to execute '"+e+"' on 'TimeRanges': The index provided ("+r+") is greater than the maximum bound ("+i+")");return t[r][e]};this.buffered={get length(){return t.length},end:function(r){return e("end",r,t.length)},start:function(r){return e("start",r,t.length)}}}},"./src/controller/subtitle-track-controller.ts":
/*!*****************************************************!*\
  !*** ./src/controller/subtitle-track-controller.ts ***!
  \*****************************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>d});var i=r(/*! ../events */"./src/events.ts"),n=r(/*! ../utils/texttrack-utils */"./src/utils/texttrack-utils.ts"),a=r(/*! ./base-playlist-controller */"./src/controller/base-playlist-controller.ts"),s=r(/*! ../types/loader */"./src/types/loader.ts");function o(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function l(t,e){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u(t){for(var e=[],r=0;r<t.length;r++){var i=t[r];"subtitles"===i.kind&&i.label&&e.push(t[r])}return e}const d=function(t){var e,r;function a(e){var r;return(r=t.call(this,e,"[subtitle-track-controller]")||this).media=null,r.tracks=[],r.groupId=null,r.tracksInGroup=[],r.trackId=-1,r.selectDefaultTrack=!0,r.queuedDefaultTrack=-1,r.trackChangeListener=function(){return r.onTextTracksChanged()},r.asyncPollTrackChange=function(){return r.pollTrackChange(0)},r.useTextTrackPolling=!1,r.subtitlePollingInterval=-1,r._subtitleDisplay=!0,r.registerListeners(),r}r=t,(e=a).prototype=Object.create(r.prototype),e.prototype.constructor=e,l(e,r);var d,c,h,f=a.prototype;return f.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.trackChangeListener=this.asyncPollTrackChange=null,t.prototype.destroy.call(this)},f.registerListeners=function(){var t=this.hls;t.on(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(i.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(i.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(i.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.on(i.Events.LEVEL_LOADING,this.onLevelLoading,this),t.on(i.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(i.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.on(i.Events.ERROR,this.onError,this)},f.unregisterListeners=function(){var t=this.hls;t.off(i.Events.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(i.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(i.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(i.Events.MANIFEST_PARSED,this.onManifestParsed,this),t.off(i.Events.LEVEL_LOADING,this.onLevelLoading,this),t.off(i.Events.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(i.Events.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.off(i.Events.ERROR,this.onError,this)},f.onMediaAttached=function(t,e){this.media=e.media,this.media&&(this.queuedDefaultTrack>-1&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=-1),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.pollTrackChange(500):this.media.textTracks.addEventListener("change",this.asyncPollTrackChange))},f.pollTrackChange=function(t){self.clearInterval(this.subtitlePollingInterval),this.subtitlePollingInterval=self.setInterval(this.trackChangeListener,t)},f.onMediaDetaching=function(){this.media&&(self.clearInterval(this.subtitlePollingInterval),this.useTextTrackPolling||this.media.textTracks.removeEventListener("change",this.asyncPollTrackChange),this.trackId>-1&&(this.queuedDefaultTrack=this.trackId),u(this.media.textTracks).forEach((function(t){(0,n.clearCurrentCues)(t)})),this.subtitleTrack=-1,this.media=null)},f.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.selectDefaultTrack=!0},f.onManifestParsed=function(t,e){this.tracks=e.subtitleTracks},f.onSubtitleTrackLoaded=function(t,e){var r=e.id,i=e.details,n=this.trackId,a=this.tracksInGroup[n];if(a){var s=a.details;a.details=e.details,this.log("subtitle track "+r+" loaded ["+i.startSN+"-"+i.endSN+"]"),r===this.trackId&&(this.retryCount=0,this.playlistLoaded(r,e,s))}else this.warn("Invalid subtitle track id "+r)},f.onLevelLoading=function(t,e){this.switchLevel(e.level)},f.onLevelSwitching=function(t,e){this.switchLevel(e.level)},f.switchLevel=function(t){var e=this.hls.levels[t];if(null!=e&&e.textGroupIds){var r=e.textGroupIds[e.urlId];if(this.groupId!==r){var n=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0,a=this.tracks.filter((function(t){return!r||t.groupId===r}));this.tracksInGroup=a;var s=this.findTrackId(null==n?void 0:n.name)||this.findTrackId();this.groupId=r;var o={subtitleTracks:a};this.log("Updating subtitle tracks, "+a.length+' track(s) found in "'+r+'" group-id'),this.hls.trigger(i.Events.SUBTITLE_TRACKS_UPDATED,o),-1!==s&&this.setSubtitleTrack(s,n)}}},f.findTrackId=function(t){for(var e=this.tracksInGroup,r=0;r<e.length;r++){var i=e[r];if((!this.selectDefaultTrack||i.default)&&(!t||t===i.name))return i.id}return-1},f.onError=function(e,r){t.prototype.onError.call(this,e,r),!r.fatal&&r.context&&r.context.type===s.PlaylistContextType.SUBTITLE_TRACK&&r.context.id===this.trackId&&r.context.groupId===this.groupId&&this.retryLoadingOrFail(r)},f.loadPlaylist=function(t){var e=this.tracksInGroup[this.trackId];if(this.shouldLoadTrack(e)){var r=e.id,n=e.groupId,a=e.url;if(t)try{a=t.addDirectives(a)}catch(t){this.warn("Could not construct new URL with HLS Delivery Directives: "+t)}this.log("Loading subtitle playlist for id "+r),this.hls.trigger(i.Events.SUBTITLE_TRACK_LOADING,{url:a,id:r,groupId:n,deliveryDirectives:t||null})}},f.toggleTrackModes=function(t){var e=this,r=this.media,i=this.trackId;if(r){var n=u(r.textTracks),a=n.filter((function(t){return t.groupId===e.groupId}));if(-1===t)[].slice.call(n).forEach((function(t){t.mode="disabled"}));else{var s=a[i];s&&(s.mode="disabled")}var o=a[t];o&&(o.mode=this.subtitleDisplay?"showing":"hidden")}},f.setSubtitleTrack=function(t,e){var r,n=this.tracksInGroup;if(this.media){if(this.trackId!==t&&this.toggleTrackModes(t),!(this.trackId===t&&(-1===t||null!==(r=n[t])&&void 0!==r&&r.details)||t<-1||t>=n.length)){this.clearTimer();var a=n[t];if(this.log("Switching to subtitle track "+t),this.trackId=t,a){var s=a.id,o=a.groupId,l=void 0===o?"":o,u=a.name,d=a.type,c=a.url;this.hls.trigger(i.Events.SUBTITLE_TRACK_SWITCH,{id:s,groupId:l,name:u,type:d,url:c});var h=this.switchParams(a.url,null==e?void 0:e.details);this.loadPlaylist(h)}else this.hls.trigger(i.Events.SUBTITLE_TRACK_SWITCH,{id:t})}}else this.queuedDefaultTrack=t},f.onTextTracksChanged=function(){if(this.useTextTrackPolling||self.clearInterval(this.subtitlePollingInterval),this.media&&this.hls.config.renderTextTracksNatively){for(var t=-1,e=u(this.media.textTracks),r=0;r<e.length;r++)if("hidden"===e[r].mode)t=r;else if("showing"===e[r].mode){t=r;break}this.subtitleTrack!==t&&(this.subtitleTrack=t)}},d=a,(c=[{key:"subtitleDisplay",get:function(){return this._subtitleDisplay},set:function(t){this._subtitleDisplay=t,this.trackId>-1&&this.toggleTrackModes(this.trackId)}},{key:"subtitleTracks",get:function(){return this.tracksInGroup}},{key:"subtitleTrack",get:function(){return this.trackId},set:function(t){this.selectDefaultTrack=!1;var e=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0;this.setSubtitleTrack(t,e)}}])&&o(d.prototype,c),h&&o(d,h),Object.defineProperty(d,"prototype",{writable:!1}),a}(a.default)},"./src/controller/timeline-controller.ts":
/*!***********************************************!*\
  !*** ./src/controller/timeline-controller.ts ***!
  \***********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{TimelineController:()=>f});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../utils/cea-608-parser */"./src/utils/cea-608-parser.ts"),s=r(/*! ../utils/output-filter */"./src/utils/output-filter.ts"),o=r(/*! ../utils/webvtt-parser */"./src/utils/webvtt-parser.ts"),l=r(/*! ../utils/texttrack-utils */"./src/utils/texttrack-utils.ts"),u=r(/*! ../utils/imsc1-ttml-parser */"./src/utils/imsc1-ttml-parser.ts"),d=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),c=r(/*! ../types/loader */"./src/types/loader.ts"),h=r(/*! ../utils/logger */"./src/utils/logger.ts"),f=function(){function t(t){if(this.hls=void 0,this.media=null,this.config=void 0,this.enabled=!0,this.Cues=void 0,this.textTracks=[],this.tracks=[],this.initPTS=[],this.timescale=[],this.unparsedVttFrags=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.cea608Parser1=void 0,this.cea608Parser2=void 0,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!0}},this.captionsProperties=void 0,this.hls=t,this.config=t.config,this.Cues=t.config.cueHandler,this.captionsProperties={textTrack1:{label:this.config.captionsTextTrack1Label,languageCode:this.config.captionsTextTrack1LanguageCode},textTrack2:{label:this.config.captionsTextTrack2Label,languageCode:this.config.captionsTextTrack2LanguageCode},textTrack3:{label:this.config.captionsTextTrack3Label,languageCode:this.config.captionsTextTrack3LanguageCode},textTrack4:{label:this.config.captionsTextTrack4Label,languageCode:this.config.captionsTextTrack4LanguageCode}},this.config.enableCEA708Captions){var e=new s.default(this,"textTrack1"),r=new s.default(this,"textTrack2"),i=new s.default(this,"textTrack3"),o=new s.default(this,"textTrack4");this.cea608Parser1=new a.default(1,e,r),this.cea608Parser2=new a.default(3,i,o)}t.on(n.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(n.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(n.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(n.Events.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(n.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.on(n.Events.FRAG_LOADING,this.onFragLoading,this),t.on(n.Events.FRAG_LOADED,this.onFragLoaded,this),t.on(n.Events.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.on(n.Events.FRAG_DECRYPTED,this.onFragDecrypted,this),t.on(n.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),t.on(n.Events.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.on(n.Events.BUFFER_FLUSHING,this.onBufferFlushing,this)}var e=t.prototype;return e.destroy=function(){var t=this.hls;t.off(n.Events.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(n.Events.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(n.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(n.Events.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(n.Events.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.off(n.Events.FRAG_LOADING,this.onFragLoading,this),t.off(n.Events.FRAG_LOADED,this.onFragLoaded,this),t.off(n.Events.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.off(n.Events.FRAG_DECRYPTED,this.onFragDecrypted,this),t.off(n.Events.INIT_PTS_FOUND,this.onInitPtsFound,this),t.off(n.Events.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.off(n.Events.BUFFER_FLUSHING,this.onBufferFlushing,this),this.hls=this.config=this.cea608Parser1=this.cea608Parser2=null},e.addCues=function(t,e,r,i,a){for(var s,o,l,u,d=!1,c=a.length;c--;){var h=a[c],f=(s=h[0],o=h[1],l=e,u=r,Math.min(o,u)-Math.max(s,l));if(f>=0&&(h[0]=Math.min(h[0],e),h[1]=Math.max(h[1],r),d=!0,f/(r-e)>.5))return}if(d||a.push([e,r]),this.config.renderTextTracksNatively){var g=this.captionsTracks[t];this.Cues.newCue(g,e,r,i)}else{var v=this.Cues.newCue(null,e,r,i);this.hls.trigger(n.Events.CUES_PARSED,{type:"captions",cues:v,track:t})}},e.onInitPtsFound=function(t,e){var r=this,i=e.frag,a=e.id,s=e.initPTS,o=e.timescale,l=this.unparsedVttFrags;"main"===a&&(this.initPTS[i.cc]=s,this.timescale[i.cc]=o),l.length&&(this.unparsedVttFrags=[],l.forEach((function(t){r.onFragLoaded(n.Events.FRAG_LOADED,t)})))},e.getExistingTrack=function(t){var e=this.media;if(e)for(var r=0;r<e.textTracks.length;r++){var i=e.textTracks[r];if(i[t])return i}return null},e.createCaptionsTrack=function(t){this.config.renderTextTracksNatively?this.createNativeTrack(t):this.createNonNativeTrack(t)},e.createNativeTrack=function(t){if(!this.captionsTracks[t]){var e=this.captionsProperties,r=this.captionsTracks,i=this.media,n=e[t],a=n.label,s=n.languageCode,o=this.getExistingTrack(t);if(o)r[t]=o,(0,l.clearCurrentCues)(r[t]),(0,l.sendAddTrackEvent)(r[t],i);else{var u=this.createTextTrack("captions",a,s);u&&(u[t]=!0,r[t]=u)}}},e.createNonNativeTrack=function(t){if(!this.nonNativeCaptionsTracks[t]){var e=this.captionsProperties[t];if(e){var r={_id:t,label:e.label,kind:"captions",default:!!e.media&&!!e.media.default,closedCaptions:e.media};this.nonNativeCaptionsTracks[t]=r,this.hls.trigger(n.Events.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:[r]})}}},e.createTextTrack=function(t,e,r){var i=this.media;if(i)return i.addTextTrack(t,e,r)},e.onMediaAttaching=function(t,e){this.media=e.media,this._cleanTracks()},e.onMediaDetaching=function(){var t=this.captionsTracks;Object.keys(t).forEach((function(e){(0,l.clearCurrentCues)(t[e]),delete t[e]})),this.nonNativeCaptionsTracks={}},e.onManifestLoading=function(){this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!0}},this._cleanTracks(),this.tracks=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.textTracks=[],this.unparsedVttFrags=this.unparsedVttFrags||[],this.initPTS=[],this.timescale=[],this.cea608Parser1&&this.cea608Parser2&&(this.cea608Parser1.reset(),this.cea608Parser2.reset())},e._cleanTracks=function(){var t=this.media;if(t){var e=t.textTracks;if(e)for(var r=0;r<e.length;r++)(0,l.clearCurrentCues)(e[r])}},e.onSubtitleTracksUpdated=function(t,e){var r=this;this.textTracks=[];var i=e.subtitleTracks||[],a=i.some((function(t){return t.textCodec===u.IMSC1_CODEC}));if(this.config.enableWebVTT||a&&this.config.enableIMSC1){var s=this.tracks&&i&&this.tracks.length===i.length;if(this.tracks=i||[],this.config.renderTextTracksNatively){var o=this.media?this.media.textTracks:[];this.tracks.forEach((function(t,e){var i;if(e<o.length){for(var n=null,a=0;a<o.length;a++)if(g(o[a],t)){n=o[a];break}n&&(i=n)}if(i)(0,l.clearCurrentCues)(i);else{var s=r._captionsOrSubtitlesFromCharacteristics(t);(i=r.createTextTrack(s,t.name,t.lang))&&(i.mode="disabled")}i&&(i.groupId=t.groupId,r.textTracks.push(i))}))}else if(!s&&this.tracks&&this.tracks.length){var d=this.tracks.map((function(t){return{label:t.name,kind:t.type.toLowerCase(),default:t.default,subtitleTrack:t}}));this.hls.trigger(n.Events.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:d})}}},e._captionsOrSubtitlesFromCharacteristics=function(t){var e;if(null!==(e=t.attrs)&&void 0!==e&&e.CHARACTERISTICS){var r=/transcribes-spoken-dialog/gi.test(t.attrs.CHARACTERISTICS),i=/describes-music-and-sound/gi.test(t.attrs.CHARACTERISTICS);if(r&&i)return"captions"}return"subtitles"},e.onManifestLoaded=function(t,e){var r=this;this.config.enableCEA708Captions&&e.captions&&e.captions.forEach((function(t){var e=/(?:CC|SERVICE)([1-4])/.exec(t.instreamId);if(e){var i="textTrack"+e[1],n=r.captionsProperties[i];n&&(n.label=t.name,t.lang&&(n.languageCode=t.lang),n.media=t)}}))},e.closedCaptionsForLevel=function(t){var e=this.hls.levels[t.level];return null==e?void 0:e.attrs["CLOSED-CAPTIONS"]},e.onFragLoading=function(t,e){var r=this.cea608Parser1,i=this.cea608Parser2,n=this.lastSn,a=this.lastPartIndex;if(this.enabled&&r&&i&&e.frag.type===c.PlaylistLevelType.MAIN){var s,o,l=e.frag.sn,u=null!=(s=null==e||null===(o=e.part)||void 0===o?void 0:o.index)?s:-1;l===n+1||l===n&&u===a+1||(r.reset(),i.reset()),this.lastSn=l,this.lastPartIndex=u}},e.onFragLoaded=function(t,e){var r=e.frag,a=e.payload,s=this.initPTS,o=this.unparsedVttFrags;if(r.type===c.PlaylistLevelType.SUBTITLE)if(a.byteLength){if(!(0,i.isFiniteNumber)(s[r.cc]))return o.push(e),void(s.length&&this.hls.trigger(n.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:r,error:new Error("Missing initial subtitle PTS")}));var l=r.decryptdata,d="stats"in e;if(null==l||null==l.key||"AES-128"!==l.method||d){var h=this.tracks[r.level],f=this.vttCCs;f[r.cc]||(f[r.cc]={start:r.start,prevCC:this.prevCC,new:!0},this.prevCC=r.cc),h&&h.textCodec===u.IMSC1_CODEC?this._parseIMSC1(r,a):this._parseVTTs(r,a,f)}}else this.hls.trigger(n.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:r,error:new Error("Empty subtitle payload")})},e._parseIMSC1=function(t,e){var r=this,i=this.hls;(0,u.parseIMSC1)(e,this.initPTS[t.cc],this.timescale[t.cc],(function(e){r._appendCues(e,t.level),i.trigger(n.Events.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})}),(function(e){h.logger.log("Failed to parse IMSC1: "+e),i.trigger(n.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:e})}))},e._parseVTTs=function(t,e,r){var i,a=this,s=this.hls,l=null!==(i=t.initSegment)&&void 0!==i&&i.data?(0,d.appendUint8Array)(t.initSegment.data,new Uint8Array(e)):e;(0,o.parseWebVTT)(l,this.initPTS[t.cc],this.timescale[t.cc],r,t.cc,t.start,(function(e){a._appendCues(e,t.level),s.trigger(n.Events.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})}),(function(r){a._fallbackToIMSC1(t,e),h.logger.log("Failed to parse VTT cue: "+r),s.trigger(n.Events.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:r})}))},e._fallbackToIMSC1=function(t,e){var r=this,i=this.tracks[t.level];i.textCodec||(0,u.parseIMSC1)(e,this.initPTS[t.cc],this.timescale[t.cc],(function(){i.textCodec=u.IMSC1_CODEC,r._parseIMSC1(t,e)}),(function(){i.textCodec="wvtt"}))},e._appendCues=function(t,e){var r=this.hls;if(this.config.renderTextTracksNatively){var i=this.textTracks[e];if(!i||"disabled"===i.mode)return;t.forEach((function(t){return(0,l.addCueToTrack)(i,t)}))}else{var a=this.tracks[e];if(!a)return;var s=a.default?"default":"subtitles"+e;r.trigger(n.Events.CUES_PARSED,{type:"subtitles",cues:t,track:s})}},e.onFragDecrypted=function(t,e){var r=e.frag;if(r.type===c.PlaylistLevelType.SUBTITLE){if(!(0,i.isFiniteNumber)(this.initPTS[r.cc]))return void this.unparsedVttFrags.push(e);this.onFragLoaded(n.Events.FRAG_LOADED,e)}},e.onSubtitleTracksCleared=function(){this.tracks=[],this.captionsTracks={}},e.onFragParsingUserdata=function(t,e){var r=this.cea608Parser1,i=this.cea608Parser2;if(this.enabled&&r&&i){var n=e.frag,a=e.samples;if(n.type!==c.PlaylistLevelType.MAIN||"NONE"!==this.closedCaptionsForLevel(n))for(var s=0;s<a.length;s++){var o=a[s].bytes;if(o){var l=this.extractCea608Data(o);r.addData(a[s].pts,l[0]),i.addData(a[s].pts,l[1])}}}},e.onBufferFlushing=function(t,e){var r=e.startOffset,i=e.endOffset,n=e.endOffsetSubtitles,a=e.type,s=this.media;if(s&&!(s.currentTime<i)){if(!a||"video"===a){var o=this.captionsTracks;Object.keys(o).forEach((function(t){return(0,l.removeCuesInRange)(o[t],r,i)}))}if(this.config.renderTextTracksNatively&&0===r&&void 0!==n){var u=this.textTracks;Object.keys(u).forEach((function(t){return(0,l.removeCuesInRange)(u[t],r,n)}))}}},e.extractCea608Data=function(t){for(var e=[[],[]],r=31&t[0],i=2,n=0;n<r;n++){var a=t[i++],s=127&t[i++],o=127&t[i++];if((0!==s||0!==o)&&0!=(4&a)){var l=3&a;0!==l&&1!==l||(e[l].push(s),e[l].push(o))}}return e},t}();function g(t,e){return t&&t.label===e.name&&!(t.textTrack1||t.textTrack2)}},"./src/crypt/aes-crypto.ts":
/*!*********************************!*\
  !*** ./src/crypt/aes-crypto.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var i=function(){function t(t,e){this.subtle=void 0,this.aesIV=void 0,this.subtle=t,this.aesIV=e}return t.prototype.decrypt=function(t,e){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},e,t)},t}()},"./src/crypt/aes-decryptor.ts":
/*!************************************!*\
  !*** ./src/crypt/aes-decryptor.ts ***!
  \************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>a,removePadding:()=>n});var i=r(/*! ../utils/typed-array */"./src/utils/typed-array.ts");function n(t){var e=t.byteLength,r=e&&new DataView(t.buffer).getUint8(e-1);return r?(0,i.sliceUint8)(t,0,e-r):t}var a=function(){function t(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}var e=t.prototype;return e.uint8ArrayToUint32Array_=function(t){for(var e=new DataView(t),r=new Uint32Array(4),i=0;i<4;i++)r[i]=e.getUint32(4*i);return r},e.initTable=function(){var t=this.sBox,e=this.invSBox,r=this.subMix,i=r[0],n=r[1],a=r[2],s=r[3],o=this.invSubMix,l=o[0],u=o[1],d=o[2],c=o[3],h=new Uint32Array(256),f=0,g=0,v=0;for(v=0;v<256;v++)h[v]=v<128?v<<1:v<<1^283;for(v=0;v<256;v++){var p=g^g<<1^g<<2^g<<3^g<<4;p=p>>>8^255&p^99,t[f]=p,e[p]=f;var m=h[f],y=h[m],E=h[y],T=257*h[p]^16843008*p;i[f]=T<<24|T>>>8,n[f]=T<<16|T>>>16,a[f]=T<<8|T>>>24,s[f]=T,T=16843009*E^65537*y^257*m^16843008*f,l[p]=T<<24|T>>>8,u[p]=T<<16|T>>>16,d[p]=T<<8|T>>>24,c[p]=T,f?(f=m^h[h[h[E^m]]],g^=h[h[g]]):f=g=1}},e.expandKey=function(t){for(var e=this.uint8ArrayToUint32Array_(t),r=!0,i=0;i<e.length&&r;)r=e[i]===this.key[i],i++;if(!r){this.key=e;var n=this.keySize=e.length;if(4!==n&&6!==n&&8!==n)throw new Error("Invalid aes key size="+n);var a,s,o,l,u=this.ksRows=4*(n+6+1),d=this.keySchedule=new Uint32Array(u),c=this.invKeySchedule=new Uint32Array(u),h=this.sBox,f=this.rcon,g=this.invSubMix,v=g[0],p=g[1],m=g[2],y=g[3];for(a=0;a<u;a++)a<n?o=d[a]=e[a]:(l=o,a%n==0?(l=h[(l=l<<8|l>>>24)>>>24]<<24|h[l>>>16&255]<<16|h[l>>>8&255]<<8|h[255&l],l^=f[a/n|0]<<24):n>6&&a%n==4&&(l=h[l>>>24]<<24|h[l>>>16&255]<<16|h[l>>>8&255]<<8|h[255&l]),d[a]=o=(d[a-n]^l)>>>0);for(s=0;s<u;s++)a=u-s,l=3&s?d[a]:d[a-4],c[s]=s<4||a<=4?l:v[h[l>>>24]]^p[h[l>>>16&255]]^m[h[l>>>8&255]]^y[h[255&l]],c[s]=c[s]>>>0}},e.networkToHostOrderSwap=function(t){return t<<24|(65280&t)<<8|(16711680&t)>>8|t>>>24},e.decrypt=function(t,e,r){for(var i,n,a,s,o,l,u,d,c,h,f,g,v,p,m=this.keySize+6,y=this.invKeySchedule,E=this.invSBox,T=this.invSubMix,S=T[0],b=T[1],D=T[2],L=T[3],A=this.uint8ArrayToUint32Array_(r),R=A[0],k=A[1],I=A[2],_=A[3],C=new Int32Array(t),w=new Int32Array(C.length),O=this.networkToHostOrderSwap;e<C.length;){for(c=O(C[e]),h=O(C[e+1]),f=O(C[e+2]),g=O(C[e+3]),o=c^y[0],l=g^y[1],u=f^y[2],d=h^y[3],v=4,p=1;p<m;p++)i=S[o>>>24]^b[l>>16&255]^D[u>>8&255]^L[255&d]^y[v],n=S[l>>>24]^b[u>>16&255]^D[d>>8&255]^L[255&o]^y[v+1],a=S[u>>>24]^b[d>>16&255]^D[o>>8&255]^L[255&l]^y[v+2],s=S[d>>>24]^b[o>>16&255]^D[l>>8&255]^L[255&u]^y[v+3],o=i,l=n,u=a,d=s,v+=4;i=E[o>>>24]<<24^E[l>>16&255]<<16^E[u>>8&255]<<8^E[255&d]^y[v],n=E[l>>>24]<<24^E[u>>16&255]<<16^E[d>>8&255]<<8^E[255&o]^y[v+1],a=E[u>>>24]<<24^E[d>>16&255]<<16^E[o>>8&255]<<8^E[255&l]^y[v+2],s=E[d>>>24]<<24^E[o>>16&255]<<16^E[l>>8&255]<<8^E[255&u]^y[v+3],w[e]=O(i^R),w[e+1]=O(s^k),w[e+2]=O(a^I),w[e+3]=O(n^_),R=c,k=h,I=f,_=g,e+=4}return w.buffer},t}()},"./src/crypt/decrypter.ts":
/*!********************************!*\
  !*** ./src/crypt/decrypter.ts ***!
  \********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>u});var i=r(/*! ./aes-crypto */"./src/crypt/aes-crypto.ts"),n=r(/*! ./fast-aes-key */"./src/crypt/fast-aes-key.ts"),a=r(/*! ./aes-decryptor */"./src/crypt/aes-decryptor.ts"),s=r(/*! ../utils/logger */"./src/utils/logger.ts"),o=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),l=r(/*! ../utils/typed-array */"./src/utils/typed-array.ts"),u=function(){function t(t,e,r){var i=(void 0===r?{}:r).removePKCS7Padding,n=void 0===i||i;if(this.logEnabled=!0,this.observer=void 0,this.config=void 0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.observer=t,this.config=e,this.removePKCS7Padding=n,n)try{var a=self.crypto;a&&(this.subtle=a.subtle||a.webkitSubtle)}catch(t){}null===this.subtle&&(this.config.enableSoftwareAES=!0)}var e=t.prototype;return e.destroy=function(){this.observer=null},e.isSync=function(){return this.config.enableSoftwareAES},e.flush=function(){var t=this.currentResult;if(t){var e=new Uint8Array(t);return this.reset(),this.removePKCS7Padding?(0,a.removePadding)(e):e}this.reset()},e.reset=function(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)},e.decrypt=function(t,e,r,i){if(this.config.enableSoftwareAES){this.softwareDecrypt(new Uint8Array(t),e,r);var n=this.flush();n&&i(n.buffer)}else this.webCryptoDecrypt(new Uint8Array(t),e,r).then(i)},e.softwareDecrypt=function(t,e,r){var i=this.currentIV,n=this.currentResult,s=this.remainderData;this.logOnce("JS AES decrypt"),s&&(t=(0,o.appendUint8Array)(s,t),this.remainderData=null);var u=this.getValidChunk(t);if(!u.length)return null;i&&(r=i);var d=this.softwareDecrypter;d||(d=this.softwareDecrypter=new a.default),d.expandKey(e);var c=n;return this.currentResult=d.decrypt(u.buffer,0,r),this.currentIV=(0,l.sliceUint8)(u,-16).buffer,c||null},e.webCryptoDecrypt=function(t,e,r){var a=this,s=this.subtle;return this.key===e&&this.fastAesKey||(this.key=e,this.fastAesKey=new n.default(s,e)),this.fastAesKey.expandKey().then((function(e){return s?new i.default(s,r).decrypt(t.buffer,e):Promise.reject(new Error("web crypto not initialized"))})).catch((function(i){return a.onWebCryptoError(i,t,e,r)}))},e.onWebCryptoError=function(t,e,r,i){return s.logger.warn("[decrypter.ts]: WebCrypto Error, disable WebCrypto API:",t),this.config.enableSoftwareAES=!0,this.logEnabled=!0,this.softwareDecrypt(e,r,i)},e.getValidChunk=function(t){var e=t,r=t.length-t.length%16;return r!==t.length&&(e=(0,l.sliceUint8)(t,0,r),this.remainderData=(0,l.sliceUint8)(t,r)),e},e.logOnce=function(t){this.logEnabled&&(s.logger.log("[decrypter.ts]: "+t),this.logEnabled=!1)},t}()},"./src/crypt/fast-aes-key.ts":
/*!***********************************!*\
  !*** ./src/crypt/fast-aes-key.ts ***!
  \***********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var i=function(){function t(t,e){this.subtle=void 0,this.key=void 0,this.subtle=t,this.key=e}return t.prototype.expandKey=function(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])},t}()},"./src/demux/aacdemuxer.ts":
/*!*********************************!*\
  !*** ./src/demux/aacdemuxer.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l});var i=r(/*! ./base-audio-demuxer */"./src/demux/base-audio-demuxer.ts"),n=r(/*! ./adts */"./src/demux/adts.ts"),a=r(/*! ../utils/logger */"./src/utils/logger.ts"),s=r(/*! ../demux/id3 */"./src/demux/id3.ts");function o(t,e){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}const l=function(t){var e,r;function i(e,r){var i;return(i=t.call(this)||this).observer=void 0,i.config=void 0,i.observer=e,i.config=r,i}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,o(e,r);var l=i.prototype;return l.resetInitSegment=function(e,r,i,n){t.prototype.resetInitSegment.call(this,e,r,i,n),this._audioTrack={container:"audio/adts",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"aac",samples:[],manifestCodec:r,duration:n,inputTimeScale:9e4,dropped:0}},i.probe=function(t){if(!t)return!1;for(var e=(s.getID3Data(t,0)||[]).length,r=t.length;e<r;e++)if(n.probe(t,e))return a.logger.log("ADTS sync word found !"),!0;return!1},l.canParse=function(t,e){return n.canParse(t,e)},l.appendFrame=function(t,e,r){n.initTrackConfig(t,this.observer,e,r,t.manifestCodec);var i=n.appendFrame(t,e,r,this.basePTS,this.frameIndex);if(i&&0===i.missing)return i},i}(i.default)},"./src/demux/adts.ts":
/*!***************************!*\
  !*** ./src/demux/adts.ts ***!
  \***************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{appendFrame:()=>m,canGetFrameLength:()=>d,canParse:()=>h,getAudioConfig:()=>s,getFrameDuration:()=>v,getFullFrameLength:()=>u,getHeaderLength:()=>l,initTrackConfig:()=>g,isHeader:()=>c,isHeaderPattern:()=>o,parseFrameHeader:()=>p,probe:()=>f});var i=r(/*! ../utils/logger */"./src/utils/logger.ts"),n=r(/*! ../errors */"./src/errors.ts"),a=r(/*! ../events */"./src/events.ts");function s(t,e,r,s){var o,l,u,d,c=navigator.userAgent.toLowerCase(),h=s,f=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];o=1+((192&e[r+2])>>>6);var g=(60&e[r+2])>>>2;if(!(g>f.length-1))return u=(1&e[r+2])<<2,u|=(192&e[r+3])>>>6,i.logger.log("manifest codec:"+s+", ADTS type:"+o+", samplingIndex:"+g),/firefox/i.test(c)?g>=6?(o=5,d=new Array(4),l=g-3):(o=2,d=new Array(2),l=g):-1!==c.indexOf("android")?(o=2,d=new Array(2),l=g):(o=5,d=new Array(4),s&&(-1!==s.indexOf("mp4a.40.29")||-1!==s.indexOf("mp4a.40.5"))||!s&&g>=6?l=g-3:((s&&-1!==s.indexOf("mp4a.40.2")&&(g>=6&&1===u||/vivaldi/i.test(c))||!s&&1===u)&&(o=2,d=new Array(2)),l=g)),d[0]=o<<3,d[0]|=(14&g)>>1,d[1]|=(1&g)<<7,d[1]|=u<<3,5===o&&(d[1]|=(14&l)>>1,d[2]=(1&l)<<7,d[2]|=8,d[3]=0),{config:d,samplerate:f[g],channelCount:u,codec:"mp4a.40."+o,manifestCodec:h};t.trigger(a.Events.ERROR,{type:n.ErrorTypes.MEDIA_ERROR,details:n.ErrorDetails.FRAG_PARSING_ERROR,fatal:!0,reason:"invalid ADTS sampling index:"+g})}function o(t,e){return 255===t[e]&&240==(246&t[e+1])}function l(t,e){return 1&t[e+1]?7:9}function u(t,e){return(3&t[e+3])<<11|t[e+4]<<3|(224&t[e+5])>>>5}function d(t,e){return e+5<t.length}function c(t,e){return e+1<t.length&&o(t,e)}function h(t,e){return d(t,e)&&o(t,e)&&u(t,e)<=t.length-e}function f(t,e){if(c(t,e)){var r=l(t,e);if(e+r>=t.length)return!1;var i=u(t,e);if(i<=r)return!1;var n=e+i;return n===t.length||c(t,n)}return!1}function g(t,e,r,n,a){if(!t.samplerate){var o=s(e,r,n,a);if(!o)return;t.config=o.config,t.samplerate=o.samplerate,t.channelCount=o.channelCount,t.codec=o.codec,t.manifestCodec=o.manifestCodec,i.logger.log("parsed codec:"+t.codec+", rate:"+o.samplerate+", channels:"+o.channelCount)}}function v(t){return 9216e4/t}function p(t,e){var r=l(t,e);if(e+r<=t.length){var i=u(t,e)-r;if(i>0)return{headerLength:r,frameLength:i}}}function m(t,e,r,i,n){var a,s=i+n*v(t.samplerate),o=p(e,r);if(o){var l=o.frameLength,u=o.headerLength,d=u+l,c=Math.max(0,r+d-e.length);c?(a=new Uint8Array(d-u)).set(e.subarray(r+u,e.length),0):a=e.subarray(r+u,r+d);var h={unit:a,pts:s};return c||t.samples.push(h),{sample:h,length:d,missing:c}}var f=e.length-r;return(a=new Uint8Array(f)).set(e.subarray(r,e.length),0),{sample:{unit:a,pts:s},length:f,missing:-1}}},"./src/demux/base-audio-demuxer.ts":
/*!*****************************************!*\
  !*** ./src/demux/base-audio-demuxer.ts ***!
  \*****************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>c,initPTSFn:()=>d});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../demux/id3 */"./src/demux/id3.ts"),a=r(/*! ../types/demuxer */"./src/types/demuxer.ts"),s=r(/*! ./dummy-demuxed-track */"./src/demux/dummy-demuxed-track.ts"),o=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),l=r(/*! ../utils/typed-array */"./src/utils/typed-array.ts"),u=function(){function t(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.basePTS=null,this.initPTS=null,this.lastPTS=null}var e=t.prototype;return e.resetInitSegment=function(t,e,r,i){this._id3Track={type:"id3",id:3,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}},e.resetTimeStamp=function(t){this.initPTS=t,this.resetContiguity()},e.resetContiguity=function(){this.basePTS=null,this.lastPTS=null,this.frameIndex=0},e.canParse=function(t,e){return!1},e.appendFrame=function(t,e,r){},e.demux=function(t,e){this.cachedData&&(t=(0,o.appendUint8Array)(this.cachedData,t),this.cachedData=null);var r,u=n.getID3Data(t,0),c=u?u.length:0,h=this._audioTrack,f=this._id3Track,g=u?n.getTimeStamp(u):void 0,v=t.length;for((null===this.basePTS||0===this.frameIndex&&(0,i.isFiniteNumber)(g))&&(this.basePTS=d(g,e,this.initPTS),this.lastPTS=this.basePTS),null===this.lastPTS&&(this.lastPTS=this.basePTS),u&&u.length>0&&f.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:u,type:a.MetadataSchema.audioId3,duration:Number.POSITIVE_INFINITY});c<v;){if(this.canParse(t,c)){var p=this.appendFrame(h,t,c);p?(this.frameIndex++,this.lastPTS=p.sample.pts,r=c+=p.length):c=v}else n.canParse(t,c)?(u=n.getID3Data(t,c),f.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:u,type:a.MetadataSchema.audioId3,duration:Number.POSITIVE_INFINITY}),r=c+=u.length):c++;if(c===v&&r!==v){var m=(0,l.sliceUint8)(t,r);this.cachedData?this.cachedData=(0,o.appendUint8Array)(this.cachedData,m):this.cachedData=m}}return{audioTrack:h,videoTrack:(0,s.dummyTrack)(),id3Track:f,textTrack:(0,s.dummyTrack)()}},e.demuxSampleAes=function(t,e,r){return Promise.reject(new Error("["+this+"] This demuxer does not support Sample-AES decryption"))},e.flush=function(t){var e=this.cachedData;return e&&(this.cachedData=null,this.demux(e,0)),{audioTrack:this._audioTrack,videoTrack:(0,s.dummyTrack)(),id3Track:this._id3Track,textTrack:(0,s.dummyTrack)()}},e.destroy=function(){},t}(),d=function(t,e,r){return(0,i.isFiniteNumber)(t)?90*t:9e4*e+(r||0)};const c=u},"./src/demux/chunk-cache.ts":
/*!**********************************!*\
  !*** ./src/demux/chunk-cache.ts ***!
  \**********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var i=function(){function t(){this.chunks=[],this.dataLength=0}var e=t.prototype;return e.push=function(t){this.chunks.push(t),this.dataLength+=t.length},e.flush=function(){var t,e=this.chunks,r=this.dataLength;return e.length?(t=1===e.length?e[0]:function(t,e){for(var r=new Uint8Array(e),i=0,n=0;n<t.length;n++){var a=t[n];r.set(a,i),i+=a.length}return r}(e,r),this.reset(),t):new Uint8Array(0)},e.reset=function(){this.chunks.length=0,this.dataLength=0},t}()},"./src/demux/dummy-demuxed-track.ts":
/*!******************************************!*\
  !*** ./src/demux/dummy-demuxed-track.ts ***!
  \******************************************/(t,e,r)=>{"use strict";function i(t,e){return void 0===t&&(t=""),void 0===e&&(e=9e4),{type:t,id:-1,pid:-1,inputTimeScale:e,sequenceNumber:-1,samples:[],dropped:0}}r.r(e),r.d(e,{dummyTrack:()=>i})},"./src/demux/exp-golomb.ts":
/*!*********************************!*\
  !*** ./src/demux/exp-golomb.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});var i=r(/*! ../utils/logger */"./src/utils/logger.ts");const n=function(){function t(t){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=t,this.bytesAvailable=t.byteLength,this.word=0,this.bitsAvailable=0}var e=t.prototype;return e.loadWord=function(){var t=this.data,e=this.bytesAvailable,r=t.byteLength-e,i=new Uint8Array(4),n=Math.min(4,e);if(0===n)throw new Error("no bytes available");i.set(t.subarray(r,r+n)),this.word=new DataView(i.buffer).getUint32(0),this.bitsAvailable=8*n,this.bytesAvailable-=n},e.skipBits=function(t){var e;this.bitsAvailable>t?(this.word<<=t,this.bitsAvailable-=t):(t-=this.bitsAvailable,t-=(e=t>>3)>>3,this.bytesAvailable-=e,this.loadWord(),this.word<<=t,this.bitsAvailable-=t)},e.readBits=function(t){var e=Math.min(this.bitsAvailable,t),r=this.word>>>32-e;return t>32&&i.logger.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=e,this.bitsAvailable>0?this.word<<=e:this.bytesAvailable>0&&this.loadWord(),(e=t-e)>0&&this.bitsAvailable?r<<e|this.readBits(e):r},e.skipLZ=function(){var t;for(t=0;t<this.bitsAvailable;++t)if(0!=(this.word&2147483648>>>t))return this.word<<=t,this.bitsAvailable-=t,t;return this.loadWord(),t+this.skipLZ()},e.skipUEG=function(){this.skipBits(1+this.skipLZ())},e.skipEG=function(){this.skipBits(1+this.skipLZ())},e.readUEG=function(){var t=this.skipLZ();return this.readBits(t+1)-1},e.readEG=function(){var t=this.readUEG();return 1&t?1+t>>>1:-1*(t>>>1)},e.readBoolean=function(){return 1===this.readBits(1)},e.readUByte=function(){return this.readBits(8)},e.readUShort=function(){return this.readBits(16)},e.readUInt=function(){return this.readBits(32)},e.skipScalingList=function(t){for(var e=8,r=8,i=0;i<t;i++)0!==r&&(r=(e+this.readEG()+256)%256),e=0===r?e:r},e.readSPS=function(){var t,e,r,i=0,n=0,a=0,s=0,o=this.readUByte.bind(this),l=this.readBits.bind(this),u=this.readUEG.bind(this),d=this.readBoolean.bind(this),c=this.skipBits.bind(this),h=this.skipEG.bind(this),f=this.skipUEG.bind(this),g=this.skipScalingList.bind(this);o();var v=o();if(l(5),c(3),o(),f(),100===v||110===v||122===v||244===v||44===v||83===v||86===v||118===v||128===v){var p=u();if(3===p&&c(1),f(),f(),c(1),d())for(e=3!==p?8:12,r=0;r<e;r++)d()&&g(r<6?16:64)}f();var m=u();if(0===m)u();else if(1===m)for(c(1),h(),h(),t=u(),r=0;r<t;r++)h();f(),c(1);var y=u(),E=u(),T=l(1);0===T&&c(1),c(1),d()&&(i=u(),n=u(),a=u(),s=u());var S=[1,1];if(d()&&d())switch(o()){case 1:S=[1,1];break;case 2:S=[12,11];break;case 3:S=[10,11];break;case 4:S=[16,11];break;case 5:S=[40,33];break;case 6:S=[24,11];break;case 7:S=[20,11];break;case 8:S=[32,11];break;case 9:S=[80,33];break;case 10:S=[18,11];break;case 11:S=[15,11];break;case 12:S=[64,33];break;case 13:S=[160,99];break;case 14:S=[4,3];break;case 15:S=[3,2];break;case 16:S=[2,1];break;case 255:S=[o()<<8|o(),o()<<8|o()]}return{width:Math.ceil(16*(y+1)-2*i-2*n),height:(2-T)*(E+1)*16-(T?2:4)*(a+s),pixelRatio:S}},e.readSliceType=function(){return this.readUByte(),this.readUEG(),this.readUEG()},t}()},"./src/demux/id3.ts":
/*!**************************!*\
  !*** ./src/demux/id3.ts ***!
  \**************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{canParse:()=>l,decodeFrame:()=>f,getID3Data:()=>s,getID3Frames:()=>h,getTimeStamp:()=>u,isFooter:()=>a,isHeader:()=>n,isTimeStampFrame:()=>d,testables:()=>E,utf8ArrayToStr:()=>y});var i,n=function(t,e){return e+10<=t.length&&73===t[e]&&68===t[e+1]&&51===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128},a=function(t,e){return e+10<=t.length&&51===t[e]&&68===t[e+1]&&73===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128},s=function(t,e){for(var r=e,i=0;n(t,e);)i+=10,i+=o(t,e+6),a(t,e+10)&&(i+=10),e+=i;if(i>0)return t.subarray(r,r+i)},o=function(t,e){var r=0;return r=(127&t[e])<<21,r|=(127&t[e+1])<<14,r|=(127&t[e+2])<<7,r|=127&t[e+3]},l=function(t,e){return n(t,e)&&o(t,e+6)+10<=t.length-e},u=function(t){for(var e=h(t),r=0;r<e.length;r++){var i=e[r];if(d(i))return m(i)}},d=function(t){return t&&"PRIV"===t.key&&"com.apple.streaming.transportStreamTimestamp"===t.info},c=function(t){var e=String.fromCharCode(t[0],t[1],t[2],t[3]),r=o(t,4);return{type:e,size:r,data:t.subarray(10,10+r)}},h=function(t){for(var e=0,r=[];n(t,e);){for(var i=o(t,e+6),s=(e+=10)+i;e+8<s;){var l=c(t.subarray(e)),u=f(l);u&&r.push(u),e+=l.size+10}a(t,e)&&(e+=10)}return r},f=function(t){return"PRIV"===t.type?g(t):"W"===t.type[0]?p(t):v(t)},g=function(t){if(!(t.size<2)){var e=y(t.data,!0),r=new Uint8Array(t.data.subarray(e.length+1));return{key:t.type,info:e,data:r.buffer}}},v=function(t){if(!(t.size<2)){if("TXXX"===t.type){var e=1,r=y(t.data.subarray(e),!0);e+=r.length+1;var i=y(t.data.subarray(e));return{key:t.type,info:r,data:i}}var n=y(t.data.subarray(1));return{key:t.type,data:n}}},p=function(t){if("WXXX"===t.type){if(t.size<2)return;var e=1,r=y(t.data.subarray(e),!0);e+=r.length+1;var i=y(t.data.subarray(e));return{key:t.type,info:r,data:i}}var n=y(t.data);return{key:t.type,data:n}},m=function(t){if(8===t.data.byteLength){var e=new Uint8Array(t.data),r=1&e[3],i=(e[4]<<23)+(e[5]<<15)+(e[6]<<7)+e[7];return i/=45,r&&(i+=47721858.84),Math.round(i)}},y=function(t,e){void 0===e&&(e=!1);var r=T();if(r){var i=r.decode(t);if(e){var n=i.indexOf("\0");return-1!==n?i.substring(0,n):i}return i.replace(/\0/g,"")}for(var a,s,o,l=t.length,u="",d=0;d<l;){if(0===(a=t[d++])&&e)return u;if(0!==a&&3!==a)switch(a>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:u+=String.fromCharCode(a);break;case 12:case 13:s=t[d++],u+=String.fromCharCode((31&a)<<6|63&s);break;case 14:s=t[d++],o=t[d++],u+=String.fromCharCode((15&a)<<12|(63&s)<<6|(63&o)<<0)}}return u},E={decodeTextFrame:v};function T(){return i||void 0===self.TextDecoder||(i=new self.TextDecoder("utf-8")),i}},"./src/demux/mp3demuxer.ts":
/*!*********************************!*\
  !*** ./src/demux/mp3demuxer.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l});var i=r(/*! ./base-audio-demuxer */"./src/demux/base-audio-demuxer.ts"),n=r(/*! ../demux/id3 */"./src/demux/id3.ts"),a=r(/*! ../utils/logger */"./src/utils/logger.ts"),s=r(/*! ./mpegaudio */"./src/demux/mpegaudio.ts");function o(t,e){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}const l=function(t){var e,r;function i(){return t.apply(this,arguments)||this}r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,o(e,r);var l=i.prototype;return l.resetInitSegment=function(e,r,i,n){t.prototype.resetInitSegment.call(this,e,r,i,n),this._audioTrack={container:"audio/mpeg",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"mp3",samples:[],manifestCodec:r,duration:n,inputTimeScale:9e4,dropped:0}},i.probe=function(t){if(!t)return!1;for(var e=(n.getID3Data(t,0)||[]).length,r=t.length;e<r;e++)if(s.probe(t,e))return a.logger.log("MPEG Audio sync word found !"),!0;return!1},l.canParse=function(t,e){return s.canParse(t,e)},l.appendFrame=function(t,e,r){if(null!==this.basePTS)return s.appendFrame(t,e,r,this.basePTS,this.frameIndex)},i}(i.default)},"./src/demux/mp4demuxer.ts":
/*!*********************************!*\
  !*** ./src/demux/mp4demuxer.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../types/demuxer */"./src/types/demuxer.ts"),a=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),s=r(/*! ./dummy-demuxed-track */"./src/demux/dummy-demuxed-track.ts"),o=/\/emsg[-/]ID3/i;const l=function(){function t(t,e){this.remainderData=null,this.timeOffset=0,this.config=void 0,this.videoTrack=void 0,this.audioTrack=void 0,this.id3Track=void 0,this.txtTrack=void 0,this.config=e}var e=t.prototype;return e.resetTimeStamp=function(){},e.resetInitSegment=function(t,e,r,i){var n=(0,a.parseInitSegment)(t),o=this.videoTrack=(0,s.dummyTrack)("video",1),l=this.audioTrack=(0,s.dummyTrack)("audio",1),u=this.txtTrack=(0,s.dummyTrack)("text",1);if(this.id3Track=(0,s.dummyTrack)("id3",1),this.timeOffset=0,n.video){var d=n.video,c=d.id,h=d.timescale,f=d.codec;o.id=c,o.timescale=u.timescale=h,o.codec=f}if(n.audio){var g=n.audio,v=g.id,p=g.timescale,m=g.codec;l.id=v,l.timescale=p,l.codec=m}u.id=a.RemuxerTrackIdConfig.text,o.sampleDuration=0,o.duration=l.duration=i},e.resetContiguity=function(){},t.probe=function(t){return t=t.length>16384?t.subarray(0,16384):t,(0,a.findBox)(t,["moof"]).length>0},e.demux=function(t,e){this.timeOffset=e;var r=t,i=this.videoTrack,n=this.txtTrack;if(this.config.progressive){this.remainderData&&(r=(0,a.appendUint8Array)(this.remainderData,t));var s=(0,a.segmentValidRange)(r);this.remainderData=s.remainder,i.samples=s.valid||new Uint8Array}else i.samples=r;var o=this.extractID3Track(i,e);return n.samples=(0,a.parseSamples)(e,i),{videoTrack:i,audioTrack:this.audioTrack,id3Track:o,textTrack:this.txtTrack}},e.flush=function(){var t=this.timeOffset,e=this.videoTrack,r=this.txtTrack;e.samples=this.remainderData||new Uint8Array,this.remainderData=null;var i=this.extractID3Track(e,this.timeOffset);return r.samples=(0,a.parseSamples)(t,e),{videoTrack:e,audioTrack:(0,s.dummyTrack)(),id3Track:i,textTrack:(0,s.dummyTrack)()}},e.extractID3Track=function(t,e){var r=this.id3Track;if(t.samples.length){var s=(0,a.findBox)(t.samples,["emsg"]);s&&s.forEach((function(t){var s=(0,a.parseEmsg)(t);if(o.test(s.schemeIdUri)){var l=(0,i.isFiniteNumber)(s.presentationTime)?s.presentationTime/s.timeScale:e+s.presentationTimeDelta/s.timeScale,u=4294967295===s.eventDuration?Number.POSITIVE_INFINITY:s.eventDuration/s.timeScale;u<=.001&&(u=Number.POSITIVE_INFINITY);var d=s.payload;r.samples.push({data:d,len:d.byteLength,dts:l,pts:l,type:n.MetadataSchema.emsg,duration:u})}}))}return r},e.demuxSampleAes=function(t,e,r){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))},e.destroy=function(){},t}()},"./src/demux/mpegaudio.ts":
/*!********************************!*\
  !*** ./src/demux/mpegaudio.ts ***!
  \********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{appendFrame:()=>l,canParse:()=>h,isHeader:()=>c,isHeaderPattern:()=>d,parseHeader:()=>u,probe:()=>f});var i=null,n=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],a=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],s=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],o=[0,1,1,4];function l(t,e,r,i,n){if(!(r+24>e.length)){var a=u(e,r);if(a&&r+a.frameLength<=e.length){var s=i+n*(9e4*a.samplesPerFrame/a.sampleRate),o={unit:e.subarray(r,r+a.frameLength),pts:s,dts:s};return t.config=[],t.channelCount=a.channelCount,t.samplerate=a.sampleRate,t.samples.push(o),{sample:o,length:a.frameLength,missing:0}}}}function u(t,e){var r=t[e+1]>>3&3,l=t[e+1]>>1&3,u=t[e+2]>>4&15,d=t[e+2]>>2&3;if(1!==r&&0!==u&&15!==u&&3!==d){var c=t[e+2]>>1&1,h=t[e+3]>>6,f=1e3*n[14*(3===r?3-l:3===l?3:4)+u-1],g=a[3*(3===r?0:2===r?1:2)+d],v=3===h?1:2,p=s[r][l],m=o[l],y=8*p*m,E=Math.floor(p*f/g+c)*m;if(null===i){var T=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);i=T?parseInt(T[1]):0}return!!i&&i<=87&&2===l&&f>=224e3&&0===h&&(t[e+3]=128|t[e+3]),{sampleRate:g,channelCount:v,frameLength:E,samplesPerFrame:y}}}function d(t,e){return 255===t[e]&&224==(224&t[e+1])&&0!=(6&t[e+1])}function c(t,e){return e+1<t.length&&d(t,e)}function h(t,e){return d(t,e)&&4<=t.length-e}function f(t,e){if(e+1<t.length&&d(t,e)){var r=u(t,e),i=4;null!=r&&r.frameLength&&(i=r.frameLength);var n=e+i;return n===t.length||c(t,n)}return!1}},"./src/demux/sample-aes.ts":
/*!*********************************!*\
  !*** ./src/demux/sample-aes.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>a});var i=r(/*! ../crypt/decrypter */"./src/crypt/decrypter.ts"),n=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts");const a=function(){function t(t,e,r){this.keyData=void 0,this.decrypter=void 0,this.keyData=r,this.decrypter=new i.default(t,e,{removePKCS7Padding:!1})}var e=t.prototype;return e.decryptBuffer=function(t,e){this.decrypter.decrypt(t,this.keyData.key.buffer,this.keyData.iv.buffer,e)},e.decryptAacSample=function(t,e,r,i){var n=t[e].unit;if(!(n.length<=16)){var a=n.subarray(16,n.length-n.length%16),s=a.buffer.slice(a.byteOffset,a.byteOffset+a.length),o=this;this.decryptBuffer(s,(function(a){var s=new Uint8Array(a);n.set(s,16),i||o.decryptAacSamples(t,e+1,r)}))}},e.decryptAacSamples=function(t,e,r){for(;;e++){if(e>=t.length)return void r();if(!(t[e].unit.length<32)){var i=this.decrypter.isSync();if(this.decryptAacSample(t,e,r,i),!i)return}}},e.getAvcEncryptedData=function(t){for(var e=16*Math.floor((t.length-48)/160)+16,r=new Int8Array(e),i=0,n=32;n<t.length-16;n+=160,i+=16)r.set(t.subarray(n,n+16),i);return r},e.getAvcDecryptedUnit=function(t,e){for(var r=new Uint8Array(e),i=0,n=32;n<t.length-16;n+=160,i+=16)t.set(r.subarray(i,i+16),n);return t},e.decryptAvcSample=function(t,e,r,i,a,s){var o=(0,n.discardEPB)(a.data),l=this.getAvcEncryptedData(o),u=this;this.decryptBuffer(l.buffer,(function(n){a.data=u.getAvcDecryptedUnit(o,n),s||u.decryptAvcSamples(t,e,r+1,i)}))},e.decryptAvcSamples=function(t,e,r,i){if(t instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;e++,r=0){if(e>=t.length)return void i();for(var n=t[e].units;!(r>=n.length);r++){var a=n[r];if(!(a.data.length<=48||1!==a.type&&5!==a.type)){var s=this.decrypter.isSync();if(this.decryptAvcSample(t,e,r,i,a,s),!s)return}}}},t}()},"./src/demux/transmuxer-interface.ts":
/*!*******************************************!*\
  !*** ./src/demux/transmuxer-interface.ts ***!
  \*******************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>c});var i=r(/*! ./webworkify-webpack */"./src/demux/webworkify-webpack.js"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../demux/transmuxer */"./src/demux/transmuxer.ts"),s=r(/*! ../utils/logger */"./src/utils/logger.ts"),o=r(/*! ../errors */"./src/errors.ts"),l=r(/*! ../utils/mediasource-helper */"./src/utils/mediasource-helper.ts"),u=r(/*! eventemitter3 */"./node_modules/eventemitter3/index.js"),d=(0,l.getMediaSource)()||{isTypeSupported:function(){return!1}},c=function(){function t(t,e,r,l){var c=this;this.hls=void 0,this.id=void 0,this.observer=void 0,this.frag=null,this.part=null,this.useWorker=void 0,this.worker=void 0,this.onwmsg=void 0,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0;var h=t.config;this.hls=t,this.id=e,this.useWorker=!!h.enableWorker,this.onTransmuxComplete=r,this.onFlush=l;var f=function(t,e){(e=e||{}).frag=c.frag,e.id=c.id,c.hls.trigger(t,e)};this.observer=new u.EventEmitter,this.observer.on(n.Events.FRAG_DECRYPTED,f),this.observer.on(n.Events.ERROR,f);var g={mp4:d.isTypeSupported("video/mp4"),mpeg:d.isTypeSupported("audio/mpeg"),mp3:d.isTypeSupported('audio/mp4; codecs="mp3"')},v=navigator.vendor;if(this.useWorker&&"undefined"!=typeof Worker){var p;s.logger.log("demuxing in webworker");try{p=this.worker=(0,i.default)(/*! ../demux/transmuxer-worker.ts */"./src/demux/transmuxer-worker.ts"),this.onwmsg=this.onWorkerMessage.bind(this),p.addEventListener("message",this.onwmsg),p.onerror=function(t){c.useWorker=!1,s.logger.warn("Exception in webworker, fallback to inline"),c.hls.trigger(n.Events.ERROR,{type:o.ErrorTypes.OTHER_ERROR,details:o.ErrorDetails.INTERNAL_EXCEPTION,fatal:!1,event:"demuxerWorker",error:new Error(t.message+"  ("+t.filename+":"+t.lineno+")")})},p.postMessage({cmd:"init",typeSupported:g,vendor:v,id:e,config:JSON.stringify(h)})}catch(t){s.logger.warn("Error in worker:",t),s.logger.error("Error while initializing DemuxerWorker, fallback to inline"),p&&self.URL.revokeObjectURL(p.objectURL),this.transmuxer=new a.default(this.observer,g,h,v,e),this.worker=null}}else this.transmuxer=new a.default(this.observer,g,h,v,e)}var e=t.prototype;return e.destroy=function(){var t=this.worker;if(t)t.removeEventListener("message",this.onwmsg),t.terminate(),this.worker=null,this.onwmsg=void 0;else{var e=this.transmuxer;e&&(e.destroy(),this.transmuxer=null)}var r=this.observer;r&&r.removeAllListeners(),this.frag=null,this.observer=null,this.hls=null},e.push=function(t,e,r,i,n,o,l,u,d,c){var h,f,g=this;d.transmuxing.start=self.performance.now();var v=this.transmuxer,p=this.worker,m=o?o.start:n.start,y=n.decryptdata,E=this.frag,T=!(E&&n.cc===E.cc),S=!(E&&d.level===E.level),b=E?d.sn-E.sn:-1,D=this.part?d.part-this.part.index:-1,L=0===b&&d.id>1&&d.id===(null==E?void 0:E.stats.chunkCount),A=!S&&(1===b||0===b&&(1===D||L&&D<=0)),R=self.performance.now();(S||b||0===n.stats.parsing.start)&&(n.stats.parsing.start=R),!o||!D&&A||(o.stats.parsing.start=R);var k=!(E&&(null===(h=n.initSegment)||void 0===h?void 0:h.url)===(null===(f=E.initSegment)||void 0===f?void 0:f.url)),I=new a.TransmuxState(T,A,u,S,m,k);if(!A||T||k){s.logger.log("[transmuxer-interface, "+n.type+"]: Starting new transmux session for sn: "+d.sn+" p: "+d.part+" level: "+d.level+" id: "+d.id+"\n        discontinuity: "+T+"\n        trackSwitch: "+S+"\n        contiguous: "+A+"\n        accurateTimeOffset: "+u+"\n        timeOffset: "+m+"\n        initSegmentChange: "+k);var _=new a.TransmuxConfig(r,i,e,l,c);this.configureTransmuxer(_)}if(this.frag=n,this.part=o,p)p.postMessage({cmd:"demux",data:t,decryptdata:y,chunkMeta:d,state:I},t instanceof ArrayBuffer?[t]:[]);else if(v){var C=v.push(t,y,d,I);(0,a.isPromise)(C)?C.then((function(t){g.handleTransmuxComplete(t)})):this.handleTransmuxComplete(C)}},e.flush=function(t){var e=this;t.transmuxing.start=self.performance.now();var r=this.transmuxer,i=this.worker;if(i)i.postMessage({cmd:"flush",chunkMeta:t});else if(r){var n=r.flush(t);(0,a.isPromise)(n)?n.then((function(r){e.handleFlushResult(r,t)})):this.handleFlushResult(n,t)}},e.handleFlushResult=function(t,e){var r=this;t.forEach((function(t){r.handleTransmuxComplete(t)})),this.onFlush(e)},e.onWorkerMessage=function(t){var e=t.data,r=this.hls;switch(e.event){case"init":self.URL.revokeObjectURL(this.worker.objectURL);break;case"transmuxComplete":this.handleTransmuxComplete(e.data);break;case"flush":this.onFlush(e.data);break;case"workerLog":s.logger[e.data.logType]&&s.logger[e.data.logType](e.data.message);break;default:e.data=e.data||{},e.data.frag=this.frag,e.data.id=this.id,r.trigger(e.event,e.data)}},e.configureTransmuxer=function(t){var e=this.worker,r=this.transmuxer;e?e.postMessage({cmd:"configure",config:t}):r&&r.configure(t)},e.handleTransmuxComplete=function(t){t.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(t)},t}()},"./src/demux/transmuxer-worker.ts":
/*!****************************************!*\
  !*** ./src/demux/transmuxer-worker.ts ***!
  \****************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>o});var i=r(/*! ../demux/transmuxer */"./src/demux/transmuxer.ts"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../utils/logger */"./src/utils/logger.ts"),s=r(/*! eventemitter3 */"./node_modules/eventemitter3/index.js");function o(t){var e=new s.EventEmitter,r=function(e,r){t.postMessage({event:e,data:r})};e.on(n.Events.FRAG_DECRYPTED,r),e.on(n.Events.ERROR,r),t.addEventListener("message",(function(n){var s=n.data;switch(s.cmd){case"init":var o=JSON.parse(s.config);t.transmuxer=new i.default(e,s.typeSupported,o,s.vendor,s.id),(0,a.enableLogs)(o.debug,s.id),function(){var t=function(t){a.logger[t]=function(e){r("workerLog",{logType:t,message:e})}};for(var e in a.logger)t(e)}(),r("init",null);break;case"configure":t.transmuxer.configure(s.config);break;case"demux":var u=t.transmuxer.push(s.data,s.decryptdata,s.chunkMeta,s.state);(0,i.isPromise)(u)?u.then((function(e){l(t,e)})):l(t,u);break;case"flush":var c=s.chunkMeta,h=t.transmuxer.flush(c);(0,i.isPromise)(h)?h.then((function(e){d(t,e,c)})):d(t,h,c)}}))}function l(t,e){if(!((r=e.remuxResult).audio||r.video||r.text||r.id3||r.initSegment))return!1;var r,i=[],n=e.remuxResult,a=n.audio,s=n.video;return a&&u(i,a),s&&u(i,s),t.postMessage({event:"transmuxComplete",data:e},i),!0}function u(t,e){e.data1&&t.push(e.data1.buffer),e.data2&&t.push(e.data2.buffer)}function d(t,e,r){e.reduce((function(e,r){return l(t,r)||e}),!1)||t.postMessage({event:"transmuxComplete",data:e[0]}),t.postMessage({event:"flush",data:r})}},"./src/demux/transmuxer.ts":
/*!*********************************!*\
  !*** ./src/demux/transmuxer.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{TransmuxConfig:()=>y,TransmuxState:()=>E,default:()=>v,isPromise:()=>m});var i,n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../errors */"./src/errors.ts"),s=r(/*! ../crypt/decrypter */"./src/crypt/decrypter.ts"),o=r(/*! ../demux/aacdemuxer */"./src/demux/aacdemuxer.ts"),l=r(/*! ../demux/mp4demuxer */"./src/demux/mp4demuxer.ts"),u=r(/*! ../demux/tsdemuxer */"./src/demux/tsdemuxer.ts"),d=r(/*! ../demux/mp3demuxer */"./src/demux/mp3demuxer.ts"),c=r(/*! ../remux/mp4-remuxer */"./src/remux/mp4-remuxer.ts"),h=r(/*! ../remux/passthrough-remuxer */"./src/remux/passthrough-remuxer.ts"),f=r(/*! ../utils/logger */"./src/utils/logger.ts");try{i=self.performance.now.bind(self.performance)}catch(t){f.logger.debug("Unable to use Performance API on this environment"),i=self.Date.now}var g=[{demux:u.default,remux:c.default},{demux:l.default,remux:h.default},{demux:o.default,remux:c.default},{demux:d.default,remux:c.default}],v=function(){function t(t,e,r,i,n){this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.vendor=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.observer=t,this.typeSupported=e,this.config=r,this.vendor=i,this.id=n}var e=t.prototype;return e.configure=function(t){this.transmuxConfig=t,this.decrypter&&this.decrypter.reset()},e.push=function(t,e,r,n){var a=this,s=r.transmuxing;s.executeStart=i();var o=new Uint8Array(t),l=this.config,u=this.currentTransmuxState,d=this.transmuxConfig;n&&(this.currentTransmuxState=n);var c=n||u,h=c.contiguous,f=c.discontinuity,g=c.trackSwitch,v=c.accurateTimeOffset,m=c.timeOffset,y=c.initSegmentChange,E=d.audioCodec,T=d.videoCodec,S=d.defaultInitPts,b=d.duration,D=d.initSegmentData;(f||g||y)&&this.resetInitSegment(D,E,T,b),(f||y)&&this.resetInitialTimestamp(S),h||this.resetContiguity();var L=function(t,e){var r=null;return t.byteLength>0&&null!=e&&null!=e.key&&null!==e.iv&&null!=e.method&&(r=e),r}(o,e);if(L&&"AES-128"===L.method){var A=this.getDecrypter();if(!l.enableSoftwareAES)return this.decryptionPromise=A.webCryptoDecrypt(o,L.key.buffer,L.iv.buffer).then((function(t){var e=a.push(t,null,r);return a.decryptionPromise=null,e})),this.decryptionPromise;var R=A.softwareDecrypt(o,L.key.buffer,L.iv.buffer);if(!R)return s.executeEnd=i(),p(r);o=new Uint8Array(R)}this.needsProbing(o,f,g)&&this.configureTransmuxer(o,d);var k=this.transmux(o,L,m,v,r),I=this.currentTransmuxState;return I.contiguous=!0,I.discontinuity=!1,I.trackSwitch=!1,s.executeEnd=i(),k},e.flush=function(t){var e=this,r=t.transmuxing;r.executeStart=i();var s=this.decrypter,o=this.currentTransmuxState,l=this.decryptionPromise;if(l)return l.then((function(){return e.flush(t)}));var u=[],d=o.timeOffset;if(s){var c=s.flush();c&&u.push(this.push(c,null,t))}var h=this.demuxer,f=this.remuxer;if(!h||!f)return this.observer.emit(n.Events.ERROR,n.Events.ERROR,{type:a.ErrorTypes.MEDIA_ERROR,details:a.ErrorDetails.FRAG_PARSING_ERROR,fatal:!0,reason:"no demux matching with content found"}),r.executeEnd=i(),[p(t)];var g=h.flush(d);return m(g)?g.then((function(r){return e.flushRemux(u,r,t),u})):(this.flushRemux(u,g,t),u)},e.flushRemux=function(t,e,r){var n=e.audioTrack,a=e.videoTrack,s=e.id3Track,o=e.textTrack,l=this.currentTransmuxState,u=l.accurateTimeOffset,d=l.timeOffset;f.logger.log("[transmuxer.ts]: Flushed fragment "+r.sn+(r.part>-1?" p: "+r.part:"")+" of level "+r.level);var c=this.remuxer.remux(n,a,s,o,d,u,!0,this.id);t.push({remuxResult:c,chunkMeta:r}),r.transmuxing.executeEnd=i()},e.resetInitialTimestamp=function(t){var e=this.demuxer,r=this.remuxer;e&&r&&(e.resetTimeStamp(t),r.resetTimeStamp(t))},e.resetContiguity=function(){var t=this.demuxer,e=this.remuxer;t&&e&&(t.resetContiguity(),e.resetNextTimestamp())},e.resetInitSegment=function(t,e,r,i){var n=this.demuxer,a=this.remuxer;n&&a&&(n.resetInitSegment(t,e,r,i),a.resetInitSegment(t,e,r))},e.destroy=function(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)},e.transmux=function(t,e,r,i,n){return e&&"SAMPLE-AES"===e.method?this.transmuxSampleAes(t,e,r,i,n):this.transmuxUnencrypted(t,r,i,n)},e.transmuxUnencrypted=function(t,e,r,i){var n=this.demuxer.demux(t,e,!1,!this.config.progressive),a=n.audioTrack,s=n.videoTrack,o=n.id3Track,l=n.textTrack;return{remuxResult:this.remuxer.remux(a,s,o,l,e,r,!1,this.id),chunkMeta:i}},e.transmuxSampleAes=function(t,e,r,i,n){var a=this;return this.demuxer.demuxSampleAes(t,e,r).then((function(t){return{remuxResult:a.remuxer.remux(t.audioTrack,t.videoTrack,t.id3Track,t.textTrack,r,i,!1,a.id),chunkMeta:n}}))},e.configureTransmuxer=function(t,e){for(var r,i=this.config,n=this.observer,a=this.typeSupported,s=this.vendor,o=e.audioCodec,u=e.defaultInitPts,d=e.duration,c=e.initSegmentData,v=e.videoCodec,p=0,m=g.length;p<m;p++)if(g[p].demux.probe(t)){r=g[p];break}r||(f.logger.warn("Failed to find demuxer by probing frag, treating as mp4 passthrough"),r={demux:l.default,remux:h.default});var y=this.demuxer,E=this.remuxer,T=r.remux,S=r.demux;E&&E instanceof T||(this.remuxer=new T(n,i,a,s)),y&&y instanceof S||(this.demuxer=new S(n,i,a),this.probe=S.probe),this.resetInitSegment(c,o,v,d),this.resetInitialTimestamp(u)},e.needsProbing=function(t,e,r){return!this.demuxer||!this.remuxer||e||r},e.getDecrypter=function(){var t=this.decrypter;return t||(t=this.decrypter=new s.default(this.observer,this.config)),t},t}(),p=function(t){return{remuxResult:{},chunkMeta:t}};function m(t){return"then"in t&&t.then instanceof Function}var y=function(t,e,r,i,n){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=t,this.videoCodec=e,this.initSegmentData=r,this.duration=i,this.defaultInitPts=n},E=function(t,e,r,i,n,a){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.initSegmentChange=void 0,this.discontinuity=t,this.contiguous=e,this.accurateTimeOffset=r,this.trackSwitch=i,this.timeOffset=n,this.initSegmentChange=a}},"./src/demux/tsdemuxer.ts":
/*!********************************!*\
  !*** ./src/demux/tsdemuxer.ts ***!
  \********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>y});var i=r(/*! ./adts */"./src/demux/adts.ts"),n=r(/*! ./mpegaudio */"./src/demux/mpegaudio.ts"),a=r(/*! ./exp-golomb */"./src/demux/exp-golomb.ts"),s=r(/*! ./sample-aes */"./src/demux/sample-aes.ts"),o=r(/*! ../events */"./src/events.ts"),l=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),u=r(/*! ../utils/logger */"./src/utils/logger.ts"),d=r(/*! ../errors */"./src/errors.ts"),c=r(/*! ../types/demuxer */"./src/types/demuxer.ts");function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function f(t,e,r,i){return{key:t,frame:!1,pts:e,dts:r,units:[],debug:i,length:0}}function g(t,e){return(31&t[e+10])<<8|t[e+11]}function v(t,e,r,i){var n={audio:-1,avc:-1,id3:-1,segmentCodec:"aac"},a=e+3+((15&t[e+1])<<8|t[e+2])-4;for(e+=12+((15&t[e+10])<<8|t[e+11]);e<a;){var s=(31&t[e+1])<<8|t[e+2];switch(t[e]){case 207:if(!i){u.logger.log("ADTS AAC with AES-128-CBC frame encryption found in unencrypted stream");break}case 15:-1===n.audio&&(n.audio=s);break;case 21:-1===n.id3&&(n.id3=s);break;case 219:if(!i){u.logger.log("H.264 with AES-128-CBC slice encryption found in unencrypted stream");break}case 27:-1===n.avc&&(n.avc=s);break;case 3:case 4:!0!==r.mpeg&&!0!==r.mp3?u.logger.log("MPEG audio found, not supported in this browser"):-1===n.audio&&(n.audio=s,n.segmentCodec="mp3");break;case 36:u.logger.warn("Unsupported HEVC stream type found")}e+=5+((15&t[e+3])<<8|t[e+4])}return n}function p(t){var e,r,i,n,a,s=0,o=t.data;if(!t||0===t.size)return null;for(;o[0].length<19&&o.length>1;){var l=new Uint8Array(o[0].length+o[1].length);l.set(o[0]),l.set(o[1],o[0].length),o[0]=l,o.splice(1,1)}if(1===((e=o[0])[0]<<16)+(e[1]<<8)+e[2]){if((r=(e[4]<<8)+e[5])&&r>t.size-6)return null;var d=e[7];192&d&&(n=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2,64&d?n-(a=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2)>54e5&&(u.logger.warn(Math.round((n-a)/9e4)+"s delta between PTS and DTS, align them"),n=a):a=n);var c=(i=e[8])+9;if(t.size<=c)return null;t.size-=c;for(var h=new Uint8Array(t.size),f=0,g=o.length;f<g;f++){var v=(e=o[f]).byteLength;if(c){if(c>v){c-=v;continue}e=e.subarray(c),v-=c,c=0}h.set(e,s),s+=v}return r&&(r-=i+3),{data:h,pts:n,dts:a,len:r}}return null}function m(t,e){if(t.units.length&&t.frame){if(void 0===t.pts){var r=e.samples,i=r.length;if(!i)return void e.dropped++;var n=r[i-1];t.pts=n.pts,t.dts=n.dts}e.samples.push(t)}t.debug.length&&u.logger.log(t.pts+"/"+t.dts+":"+t.debug)}const y=function(){function t(t,e,r){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._duration=0,this._pmtId=-1,this._avcTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.avcSample=null,this.remainderData=null,this.observer=t,this.config=e,this.typeSupported=r}t.probe=function(e){var r=t.syncOffset(e);return r>0&&u.logger.warn("MPEG2-TS detected but first sync word found @ offset "+r),-1!==r},t.syncOffset=function(t){for(var e=Math.min(940,t.length-376)+1,r=0;r<e;){if(71===t[r]&&71===t[r+188])return r;r++}return-1},t.createTrack=function(t,e){return{container:"video"===t||"audio"===t?"video/mp2t":void 0,type:t,id:l.RemuxerTrackIdConfig[t],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:"audio"===t?e:void 0}};var e=t.prototype;return e.resetInitSegment=function(e,r,i,n){this.pmtParsed=!1,this._pmtId=-1,this._avcTrack=t.createTrack("video"),this._audioTrack=t.createTrack("audio",n),this._id3Track=t.createTrack("id3"),this._txtTrack=t.createTrack("text"),this._audioTrack.segmentCodec="aac",this.aacOverFlow=null,this.avcSample=null,this.remainderData=null,this.audioCodec=r,this.videoCodec=i,this._duration=n},e.resetTimeStamp=function(){},e.resetContiguity=function(){var t=this._audioTrack,e=this._avcTrack,r=this._id3Track;t&&(t.pesData=null),e&&(e.pesData=null),r&&(r.pesData=null),this.aacOverFlow=null,this.avcSample=null,this.remainderData=null},e.demux=function(e,r,i,n){var a;void 0===i&&(i=!1),void 0===n&&(n=!1),i||(this.sampleAes=null);var s=this._avcTrack,c=this._audioTrack,h=this._id3Track,f=this._txtTrack,m=s.pid,y=s.pesData,E=c.pid,T=h.pid,S=c.pesData,b=h.pesData,D=null,L=this.pmtParsed,A=this._pmtId,R=e.length;if(this.remainderData&&(R=(e=(0,l.appendUint8Array)(this.remainderData,e)).length,this.remainderData=null),R<188&&!n)return this.remainderData=e,{audioTrack:c,videoTrack:s,id3Track:h,textTrack:f};var k=Math.max(0,t.syncOffset(e));(R-=(R-k)%188)<e.byteLength&&!n&&(this.remainderData=new Uint8Array(e.buffer,R,e.buffer.byteLength-R));for(var I=0,_=k;_<R;_+=188)if(71===e[_]){var C=!!(64&e[_+1]),w=((31&e[_+1])<<8)+e[_+2],O=void 0;if((48&e[_+3])>>4>1){if((O=_+5+e[_+4])===_+188)continue}else O=_+4;switch(w){case m:C&&(y&&(a=p(y))&&this.parseAVCPES(s,f,a,!1),y={data:[],size:0}),y&&(y.data.push(e.subarray(O,_+188)),y.size+=_+188-O);break;case E:if(C){if(S&&(a=p(S)))switch(c.segmentCodec){case"aac":this.parseAACPES(c,a);break;case"mp3":this.parseMPEGPES(c,a)}S={data:[],size:0}}S&&(S.data.push(e.subarray(O,_+188)),S.size+=_+188-O);break;case T:C&&(b&&(a=p(b))&&this.parseID3PES(h,a),b={data:[],size:0}),b&&(b.data.push(e.subarray(O,_+188)),b.size+=_+188-O);break;case 0:C&&(O+=e[O]+1),A=this._pmtId=g(e,O);break;case A:C&&(O+=e[O]+1);var x=v(e,O,this.typeSupported,i);(m=x.avc)>0&&(s.pid=m),(E=x.audio)>0&&(c.pid=E,c.segmentCodec=x.segmentCodec),(T=x.id3)>0&&(h.pid=T),null===D||L||(u.logger.log("unknown PID '"+D+"' in TS found"),D=null,_=k-188),L=this.pmtParsed=!0;break;case 17:case 8191:break;default:D=w}}else I++;I>0&&this.observer.emit(o.Events.ERROR,o.Events.ERROR,{type:d.ErrorTypes.MEDIA_ERROR,details:d.ErrorDetails.FRAG_PARSING_ERROR,fatal:!1,reason:"Found "+I+" TS packet/s that do not start with 0x47"}),s.pesData=y,c.pesData=S,h.pesData=b;var P={audioTrack:c,videoTrack:s,id3Track:h,textTrack:f};return n&&this.extractRemainingSamples(P),P},e.flush=function(){var t,e=this.remainderData;return this.remainderData=null,t=e?this.demux(e,-1,!1,!0):{videoTrack:this._avcTrack,audioTrack:this._audioTrack,id3Track:this._id3Track,textTrack:this._txtTrack},this.extractRemainingSamples(t),this.sampleAes?this.decrypt(t,this.sampleAes):t},e.extractRemainingSamples=function(t){var e,r=t.audioTrack,i=t.videoTrack,n=t.id3Track,a=t.textTrack,s=i.pesData,o=r.pesData,l=n.pesData;if(s&&(e=p(s))?(this.parseAVCPES(i,a,e,!0),i.pesData=null):i.pesData=s,o&&(e=p(o))){switch(r.segmentCodec){case"aac":this.parseAACPES(r,e);break;case"mp3":this.parseMPEGPES(r,e)}r.pesData=null}else null!=o&&o.size&&u.logger.log("last AAC PES packet truncated,might overlap between fragments"),r.pesData=o;l&&(e=p(l))?(this.parseID3PES(n,e),n.pesData=null):n.pesData=l},e.demuxSampleAes=function(t,e,r){var i=this.demux(t,r,!0,!this.config.progressive),n=this.sampleAes=new s.default(this.observer,this.config,e);return this.decrypt(i,n)},e.decrypt=function(t,e){return new Promise((function(r){var i=t.audioTrack,n=t.videoTrack;i.samples&&"aac"===i.segmentCodec?e.decryptAacSamples(i.samples,0,(function(){n.samples?e.decryptAvcSamples(n.samples,0,0,(function(){r(t)})):r(t)})):n.samples&&e.decryptAvcSamples(n.samples,0,0,(function(){r(t)}))}))},e.destroy=function(){this._duration=0},e.parseAVCPES=function(t,e,r,i){var n,s=this,o=this.parseAVCNALu(t,r.data),u=this.avcSample,d=!1;r.data=null,u&&o.length&&!t.audFound&&(m(u,t),u=this.avcSample=f(!1,r.pts,r.dts,"")),o.forEach((function(i){switch(i.type){case 1:n=!0,u||(u=s.avcSample=f(!0,r.pts,r.dts,"")),u.frame=!0;var o=i.data;if(d&&o.length>4){var c=new a.default(o).readSliceType();2!==c&&4!==c&&7!==c&&9!==c||(u.key=!0)}break;case 5:n=!0,u||(u=s.avcSample=f(!0,r.pts,r.dts,"")),u.key=!0,u.frame=!0;break;case 6:n=!0,(0,l.parseSEIMessageFromNALu)(i.data,1,r.pts,e.samples);break;case 7:if(n=!0,d=!0,!t.sps){var h=new a.default(i.data).readSPS();t.width=h.width,t.height=h.height,t.pixelRatio=h.pixelRatio,t.sps=[i.data],t.duration=s._duration;for(var g=i.data.subarray(1,4),v="avc1.",p=0;p<3;p++){var y=g[p].toString(16);y.length<2&&(y="0"+y),v+=y}t.codec=v}break;case 8:n=!0,t.pps||(t.pps=[i.data]);break;case 9:n=!1,t.audFound=!0,u&&m(u,t),u=s.avcSample=f(!1,r.pts,r.dts,"");break;case 12:n=!0;break;default:n=!1,u&&(u.debug+="unknown NAL "+i.type+" ")}u&&n&&u.units.push(i)})),i&&u&&(m(u,t),this.avcSample=null)},e.getLastNalUnit=function(t){var e,r,i=this.avcSample;if(i&&0!==i.units.length||(i=t[t.length-1]),null!==(e=i)&&void 0!==e&&e.units){var n=i.units;r=n[n.length-1]}return r},e.parseAVCNALu=function(t,e){var r,i,n=e.byteLength,a=t.naluState||0,s=a,o=[],l=0,u=-1,d=0;for(-1===a&&(u=0,d=31&e[0],a=0,l=1);l<n;)if(r=e[l++],a)if(1!==a)if(r)if(1===r){if(u>=0){var c={data:e.subarray(u,l-a-1),type:d};o.push(c)}else{var h=this.getLastNalUnit(t.samples);if(h&&(s&&l<=4-s&&h.state&&(h.data=h.data.subarray(0,h.data.byteLength-s)),(i=l-a-1)>0)){var f=new Uint8Array(h.data.byteLength+i);f.set(h.data,0),f.set(e.subarray(0,i),h.data.byteLength),h.data=f,h.state=0}}l<n?(u=l,d=31&e[l],a=0):a=-1}else a=0;else a=3;else a=r?0:2;else a=r?0:1;if(u>=0&&a>=0){var g={data:e.subarray(u,n),type:d,state:a};o.push(g)}if(0===o.length){var v=this.getLastNalUnit(t.samples);if(v){var p=new Uint8Array(v.data.byteLength+e.byteLength);p.set(v.data,0),p.set(e,v.data.byteLength),v.data=p}}return t.naluState=a,o},e.parseAACPES=function(t,e){var r,n,a,s,l,c=0,h=this.aacOverFlow,f=e.data;if(h){this.aacOverFlow=null;var g=h.missing,v=h.sample.unit.byteLength;if(-1===g){var p=new Uint8Array(v+f.byteLength);p.set(h.sample.unit,0),p.set(f,v),f=p}else{var m=v-g;h.sample.unit.set(f.subarray(0,g),m),t.samples.push(h.sample),c=h.missing}}for(r=c,n=f.length;r<n-1&&!i.isHeader(f,r);r++);if(r===c||(r<n-1?(a="AAC PES did not start with ADTS header,offset:"+r,s=!1):(a="no ADTS header found in AAC PES",s=!0),u.logger.warn("parsing error:"+a),this.observer.emit(o.Events.ERROR,o.Events.ERROR,{type:d.ErrorTypes.MEDIA_ERROR,details:d.ErrorDetails.FRAG_PARSING_ERROR,fatal:s,reason:a}),!s)){if(i.initTrackConfig(t,this.observer,f,r,this.audioCodec),void 0!==e.pts)l=e.pts;else{if(!h)return void u.logger.warn("[tsdemuxer]: AAC PES unknown PTS");var y=i.getFrameDuration(t.samplerate);l=h.sample.pts+y}for(var E,T=0;r<n;){if(r+=(E=i.appendFrame(t,f,r,l,T)).length,E.missing){this.aacOverFlow=E;break}for(T++;r<n-1&&!i.isHeader(f,r);r++);}}},e.parseMPEGPES=function(t,e){var r=e.data,i=r.length,a=0,s=0,o=e.pts;if(void 0!==o)for(;s<i;)if(n.isHeader(r,s)){var l=n.appendFrame(t,r,s,o,a);if(!l)break;s+=l.length,a++}else s++;else u.logger.warn("[tsdemuxer]: MPEG PES unknown PTS")},e.parseID3PES=function(t,e){if(void 0!==e.pts){var r=h({},e,{type:this._avcTrack?c.MetadataSchema.emsg:c.MetadataSchema.audioId3,duration:Number.POSITIVE_INFINITY});t.samples.push(r)}else u.logger.warn("[tsdemuxer]: ID3 PES unknown PTS")},t}()},"./src/demux/webworkify-webpack.js":
/*!*****************************************!*\
  !*** ./src/demux/webworkify-webpack.js ***!
  \*****************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>l});var i=function(){var t=ENTRY_MODULE,e={},r=function r(i){var n=e[i];if(void 0!==n)return n.exports;var a=e[i]={exports:{}};return t[i].call(a.exports,a,a.exports,r),a.exports};r.m=t,r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i=r(ENTRY_MODULE);return i.default||i}.toString().split("ENTRY_MODULE");function n(t){return(t+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function a(t,e,i){var a={};a[i]=[];var s=e.toString().replace(/^"[^"]+"/,"function"),o=s.match(/^function\s?\w*\(\w+,\s*\w+,\s*(\w+)\)/)||s.match(/^\(\w+,\s*\w+,\s*(\w+)\)\s?\=\s?\>/);if(!o)return a;for(var l,u=o[1],d=new RegExp("(\\\\n|\\W)"+n(u)+"\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)","g");l=d.exec(s);)"dll-reference"!==l[3]&&a[i].push(l[3]);for(d=new RegExp("\\("+n(u)+'\\("(dll-reference\\s([\\.|\\-|\\+|\\w|/|@]+))"\\)\\)\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)',"g");l=d.exec(s);)t[l[2]]||(a[i].push(l[1]),t[l[2]]=r(l[1]).m),a[l[2]]=a[l[2]]||[],a[l[2]].push(l[4]);for(var c,h=Object.keys(a),f=0;f<h.length;f++)for(var g=0;g<a[h[f]].length;g++)c=a[h[f]][g],isNaN(1*c)||(a[h[f]][g]=1*a[h[f]][g]);return a}function s(t){return Object.keys(t).reduce((function(e,r){return e||t[r].length>0}),!1)}function o(t,e,r,n){var a=t[n].map((function(t){return'"'+t+'": '+e[n][t].toString().replace(/^"[^"]+"/,"function")})).join(",");return i[0]+"{"+a+"}"+i[1]+'"'+r+'"'+i[2]}function l(t,e){e=e||{};var i={main:r.m},n=e.all?{main:Object.keys(i.main)}:function(t,e){for(var r={main:[e]},i={main:[]},n={main:{}};s(r);)for(var o=Object.keys(r),l=0;l<o.length;l++){var u=o[l],d=r[u].pop();if(n[u]=n[u]||{},!n[u][d]&&t[u][d]){n[u][d]=!0,i[u]=i[u]||[],i[u].push(d);for(var c=a(t,t[u][d],u),h=Object.keys(c),f=0;f<h.length;f++)r[h[f]]=r[h[f]]||[],r[h[f]]=r[h[f]].concat(c[h[f]])}}return i}(i,t),l="";Object.keys(n).filter((function(t){return"main"!==t})).forEach((function(t){for(var e=0;n[t][e];)e++;n[t].push(e),i[t][e]="(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",l=l+"var "+t+" = ("+o(n,i,e,modules)+")();\n"})),l=l+"new (("+o(n,i,t,"main")+")())(self);";var u=new window.Blob([l],{type:"text/javascript"}),d=(window.URL||window.webkitURL||window.mozURL||window.msURL).createObjectURL(u),c=new window.Worker(d);return c.objectURL=d,c}},"./src/errors.ts":
/*!***********************!*\
  !*** ./src/errors.ts ***!
  \***********************/(t,e,r)=>{"use strict";var i,n;r.r(e),r.d(e,{ErrorDetails:()=>n,ErrorTypes:()=>i}),function(t){t.NETWORK_ERROR="networkError",t.MEDIA_ERROR="mediaError",t.KEY_SYSTEM_ERROR="keySystemError",t.MUX_ERROR="muxError",t.OTHER_ERROR="otherError"}(i||(i={})),function(t){t.KEY_SYSTEM_NO_KEYS="keySystemNoKeys",t.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",t.KEY_SYSTEM_NO_SESSION="keySystemNoSession",t.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",t.KEY_SYSTEM_NO_INIT_DATA="keySystemNoInitData",t.MANIFEST_LOAD_ERROR="manifestLoadError",t.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",t.MANIFEST_PARSING_ERROR="manifestParsingError",t.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",t.LEVEL_EMPTY_ERROR="levelEmptyError",t.LEVEL_LOAD_ERROR="levelLoadError",t.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",t.LEVEL_SWITCH_ERROR="levelSwitchError",t.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",t.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",t.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",t.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",t.FRAG_LOAD_ERROR="fragLoadError",t.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",t.FRAG_DECRYPT_ERROR="fragDecryptError",t.FRAG_PARSING_ERROR="fragParsingError",t.REMUX_ALLOC_ERROR="remuxAllocError",t.KEY_LOAD_ERROR="keyLoadError",t.KEY_LOAD_TIMEOUT="keyLoadTimeOut",t.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",t.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",t.BUFFER_APPEND_ERROR="bufferAppendError",t.BUFFER_APPENDING_ERROR="bufferAppendingError",t.BUFFER_STALLED_ERROR="bufferStalledError",t.BUFFER_FULL_ERROR="bufferFullError",t.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",t.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",t.INTERNAL_EXCEPTION="internalException",t.INTERNAL_ABORTED="aborted",t.UNKNOWN="unknown"}(n||(n={}))},"./src/events.ts":
/*!***********************!*\
  !*** ./src/events.ts ***!
  \***********************/(t,e,r)=>{"use strict";var i;r.r(e),r.d(e,{Events:()=>i}),function(t){t.MEDIA_ATTACHING="hlsMediaAttaching",t.MEDIA_ATTACHED="hlsMediaAttached",t.MEDIA_DETACHING="hlsMediaDetaching",t.MEDIA_DETACHED="hlsMediaDetached",t.BUFFER_RESET="hlsBufferReset",t.BUFFER_CODECS="hlsBufferCodecs",t.BUFFER_CREATED="hlsBufferCreated",t.BUFFER_APPENDING="hlsBufferAppending",t.BUFFER_APPENDED="hlsBufferAppended",t.BUFFER_EOS="hlsBufferEos",t.BUFFER_FLUSHING="hlsBufferFlushing",t.BUFFER_FLUSHED="hlsBufferFlushed",t.MANIFEST_LOADING="hlsManifestLoading",t.MANIFEST_LOADED="hlsManifestLoaded",t.MANIFEST_PARSED="hlsManifestParsed",t.LEVEL_SWITCHING="hlsLevelSwitching",t.LEVEL_SWITCHED="hlsLevelSwitched",t.LEVEL_LOADING="hlsLevelLoading",t.LEVEL_LOADED="hlsLevelLoaded",t.LEVEL_UPDATED="hlsLevelUpdated",t.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",t.LEVELS_UPDATED="hlsLevelsUpdated",t.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",t.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",t.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",t.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",t.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",t.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",t.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",t.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",t.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",t.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",t.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",t.CUES_PARSED="hlsCuesParsed",t.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",t.INIT_PTS_FOUND="hlsInitPtsFound",t.FRAG_LOADING="hlsFragLoading",t.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",t.FRAG_LOADED="hlsFragLoaded",t.FRAG_DECRYPTED="hlsFragDecrypted",t.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",t.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",t.FRAG_PARSING_METADATA="hlsFragParsingMetadata",t.FRAG_PARSED="hlsFragParsed",t.FRAG_BUFFERED="hlsFragBuffered",t.FRAG_CHANGED="hlsFragChanged",t.FPS_DROP="hlsFpsDrop",t.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",t.ERROR="hlsError",t.DESTROYING="hlsDestroying",t.KEY_LOADING="hlsKeyLoading",t.KEY_LOADED="hlsKeyLoaded",t.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",t.BACK_BUFFER_REACHED="hlsBackBufferReached"}(i||(i={}))},"./src/hls.ts":
/*!********************!*\
  !*** ./src/hls.ts ***!
  \********************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>y});var i=r(/*! url-toolkit */"./node_modules/url-toolkit/src/url-toolkit.js"),n=r(/*! ./loader/playlist-loader */"./src/loader/playlist-loader.ts"),a=r(/*! ./loader/key-loader */"./src/loader/key-loader.ts"),s=r(/*! ./controller/id3-track-controller */"./src/controller/id3-track-controller.ts"),o=r(/*! ./controller/latency-controller */"./src/controller/latency-controller.ts"),l=r(/*! ./controller/level-controller */"./src/controller/level-controller.ts"),u=r(/*! ./controller/fragment-tracker */"./src/controller/fragment-tracker.ts"),d=r(/*! ./controller/stream-controller */"./src/controller/stream-controller.ts"),c=r(/*! ./is-supported */"./src/is-supported.ts"),h=r(/*! ./utils/logger */"./src/utils/logger.ts"),f=r(/*! ./config */"./src/config.ts"),g=r(/*! eventemitter3 */"./node_modules/eventemitter3/index.js"),v=r(/*! ./events */"./src/events.ts"),p=r(/*! ./errors */"./src/errors.ts");function m(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var y=function(){function t(e){void 0===e&&(e={}),this.config=void 0,this.userConfig=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this._emitter=new g.EventEmitter,this._autoLevelCapping=void 0,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.emeController=void 0,this.cmcdController=void 0,this._media=null,this.url=null;var r=this.config=(0,f.mergeConfig)(t.DefaultConfig,e);this.userConfig=e,(0,h.enableLogs)(r.debug,"Hls instance"),this._autoLevelCapping=-1,r.progressive&&(0,f.enableStreamingMode)(r);var i=r.abrController,c=r.bufferController,v=r.capLevelController,p=r.fpsController,m=this.abrController=new i(this),y=this.bufferController=new c(this),E=this.capLevelController=new v(this),T=new p(this),S=new n.default(this),b=new a.default(this),D=new s.default(this),L=this.levelController=new l.default(this),A=new u.FragmentTracker(this),R=this.streamController=new d.default(this,A);E.setStreamController(R),T.setStreamController(R);var k=[S,b,L,R];this.networkControllers=k;var I=[m,y,E,T,D,A];this.audioTrackController=this.createController(r.audioTrackController,null,k),this.createController(r.audioStreamController,A,k),this.subtitleTrackController=this.createController(r.subtitleTrackController,null,k),this.createController(r.subtitleStreamController,A,k),this.createController(r.timelineController,null,I),this.emeController=this.createController(r.emeController,null,I),this.cmcdController=this.createController(r.cmcdController,null,I),this.latencyController=this.createController(o.default,null,I),this.coreComponents=I}t.isSupported=function(){return(0,c.isSupported)()};var e,r,y,E=t.prototype;return E.createController=function(t,e,r){if(t){var i=e?new t(this,e):new t(this);return r&&r.push(i),i}return null},E.on=function(t,e,r){void 0===r&&(r=this),this._emitter.on(t,e,r)},E.once=function(t,e,r){void 0===r&&(r=this),this._emitter.once(t,e,r)},E.removeAllListeners=function(t){this._emitter.removeAllListeners(t)},E.off=function(t,e,r,i){void 0===r&&(r=this),this._emitter.off(t,e,r,i)},E.listeners=function(t){return this._emitter.listeners(t)},E.emit=function(t,e,r){return this._emitter.emit(t,e,r)},E.trigger=function(t,e){if(this.config.debug)return this.emit(t,t,e);try{return this.emit(t,t,e)}catch(e){h.logger.error("An internal error happened while handling event "+t+'. Error message: "'+e.message+'". Here is a stacktrace:',e),this.trigger(v.Events.ERROR,{type:p.ErrorTypes.OTHER_ERROR,details:p.ErrorDetails.INTERNAL_EXCEPTION,fatal:!1,event:t,error:e})}return!1},E.listenerCount=function(t){return this._emitter.listenerCount(t)},E.destroy=function(){h.logger.log("destroy"),this.trigger(v.Events.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this.url=null,this.networkControllers.forEach((function(t){return t.destroy()})),this.networkControllers.length=0,this.coreComponents.forEach((function(t){return t.destroy()})),this.coreComponents.length=0},E.attachMedia=function(t){h.logger.log("attachMedia"),this._media=t,this.trigger(v.Events.MEDIA_ATTACHING,{media:t})},E.detachMedia=function(){h.logger.log("detachMedia"),this.trigger(v.Events.MEDIA_DETACHING,void 0),this._media=null},E.loadSource=function(t){this.stopLoad();var e=this.media,r=this.url,n=this.url=i.buildAbsoluteURL(self.location.href,t,{alwaysNormalize:!0});h.logger.log("loadSource:"+n),e&&r&&r!==n&&this.bufferController.hasSourceTypes()&&(this.detachMedia(),this.attachMedia(e)),this.trigger(v.Events.MANIFEST_LOADING,{url:t})},E.startLoad=function(t){void 0===t&&(t=-1),h.logger.log("startLoad("+t+")"),this.networkControllers.forEach((function(e){e.startLoad(t)}))},E.stopLoad=function(){h.logger.log("stopLoad"),this.networkControllers.forEach((function(t){t.stopLoad()}))},E.swapAudioCodec=function(){h.logger.log("swapAudioCodec"),this.streamController.swapAudioCodec()},E.recoverMediaError=function(){h.logger.log("recoverMediaError");var t=this._media;this.detachMedia(),t&&this.attachMedia(t)},E.removeLevel=function(t,e){void 0===e&&(e=0),this.levelController.removeLevel(t,e)},e=t,y=[{key:"version",get:function(){return"1.2.9"}},{key:"Events",get:function(){return v.Events}},{key:"ErrorTypes",get:function(){return p.ErrorTypes}},{key:"ErrorDetails",get:function(){return p.ErrorDetails}},{key:"DefaultConfig",get:function(){return t.defaultConfig?t.defaultConfig:f.hlsDefaultConfig},set:function(e){t.defaultConfig=e}}],(r=[{key:"levels",get:function(){var t=this.levelController.levels;return t||[]}},{key:"currentLevel",get:function(){return this.streamController.currentLevel},set:function(t){h.logger.log("set currentLevel:"+t),this.loadLevel=t,this.abrController.clearTimer(),this.streamController.immediateLevelSwitch()}},{key:"nextLevel",get:function(){return this.streamController.nextLevel},set:function(t){h.logger.log("set nextLevel:"+t),this.levelController.manualLevel=t,this.streamController.nextLevelSwitch()}},{key:"loadLevel",get:function(){return this.levelController.level},set:function(t){h.logger.log("set loadLevel:"+t),this.levelController.manualLevel=t}},{key:"nextLoadLevel",get:function(){return this.levelController.nextLoadLevel},set:function(t){this.levelController.nextLoadLevel=t}},{key:"firstLevel",get:function(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)},set:function(t){h.logger.log("set firstLevel:"+t),this.levelController.firstLevel=t}},{key:"startLevel",get:function(){return this.levelController.startLevel},set:function(t){h.logger.log("set startLevel:"+t),-1!==t&&(t=Math.max(t,this.minAutoLevel)),this.levelController.startLevel=t}},{key:"capLevelToPlayerSize",get:function(){return this.config.capLevelToPlayerSize},set:function(t){var e=!!t;e!==this.config.capLevelToPlayerSize&&(e?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=e)}},{key:"autoLevelCapping",get:function(){return this._autoLevelCapping},set:function(t){this._autoLevelCapping!==t&&(h.logger.log("set autoLevelCapping:"+t),this._autoLevelCapping=t)}},{key:"bandwidthEstimate",get:function(){var t=this.abrController.bwEstimator;return t?t.getEstimate():NaN}},{key:"autoLevelEnabled",get:function(){return-1===this.levelController.manualLevel}},{key:"manualLevel",get:function(){return this.levelController.manualLevel}},{key:"minAutoLevel",get:function(){var t=this.levels,e=this.config.minAutoBitrate;if(!t)return 0;for(var r=t.length,i=0;i<r;i++)if(t[i].maxBitrate>=e)return i;return 0}},{key:"maxAutoLevel",get:function(){var t=this.levels,e=this.autoLevelCapping;return-1===e&&t&&t.length?t.length-1:e}},{key:"nextAutoLevel",get:function(){return Math.min(Math.max(this.abrController.nextAutoLevel,this.minAutoLevel),this.maxAutoLevel)},set:function(t){this.abrController.nextAutoLevel=Math.max(this.minAutoLevel,t)}},{key:"playingDate",get:function(){return this.streamController.currentProgramDateTime}},{key:"mainForwardBufferInfo",get:function(){return this.streamController.getMainFwdBufferInfo()}},{key:"audioTracks",get:function(){var t=this.audioTrackController;return t?t.audioTracks:[]}},{key:"audioTrack",get:function(){var t=this.audioTrackController;return t?t.audioTrack:-1},set:function(t){var e=this.audioTrackController;e&&(e.audioTrack=t)}},{key:"subtitleTracks",get:function(){var t=this.subtitleTrackController;return t?t.subtitleTracks:[]}},{key:"subtitleTrack",get:function(){var t=this.subtitleTrackController;return t?t.subtitleTrack:-1},set:function(t){var e=this.subtitleTrackController;e&&(e.subtitleTrack=t)}},{key:"media",get:function(){return this._media}},{key:"subtitleDisplay",get:function(){var t=this.subtitleTrackController;return!!t&&t.subtitleDisplay},set:function(t){var e=this.subtitleTrackController;e&&(e.subtitleDisplay=t)}},{key:"lowLatencyMode",get:function(){return this.config.lowLatencyMode},set:function(t){this.config.lowLatencyMode=t}},{key:"liveSyncPosition",get:function(){return this.latencyController.liveSyncPosition}},{key:"latency",get:function(){return this.latencyController.latency}},{key:"maxLatency",get:function(){return this.latencyController.maxLatency}},{key:"targetLatency",get:function(){return this.latencyController.targetLatency}},{key:"drift",get:function(){return this.latencyController.drift}},{key:"forceStartLoad",get:function(){return this.streamController.forceStartLoad}}])&&m(e.prototype,r),y&&m(e,y),Object.defineProperty(e,"prototype",{writable:!1}),t}();y.defaultConfig=void 0},"./src/is-supported.ts":
/*!*****************************!*\
  !*** ./src/is-supported.ts ***!
  \*****************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{changeTypeSupported:()=>s,isSupported:()=>a});var i=r(/*! ./utils/mediasource-helper */"./src/utils/mediasource-helper.ts");function n(){return self.SourceBuffer||self.WebKitSourceBuffer}function a(){var t=(0,i.getMediaSource)();if(!t)return!1;var e=n(),r=t&&"function"==typeof t.isTypeSupported&&t.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),a=!e||e.prototype&&"function"==typeof e.prototype.appendBuffer&&"function"==typeof e.prototype.remove;return!!r&&!!a}function s(){var t,e=n();return"function"==typeof(null==e||null===(t=e.prototype)||void 0===t?void 0:t.changeType)}},"./src/loader/date-range.ts":
/*!**********************************!*\
  !*** ./src/loader/date-range.ts ***!
  \**********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{DateRange:()=>u,DateRangeAttribute:()=>i});var i,n=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),a=r(/*! ../utils/attr-list */"./src/utils/attr-list.ts"),s=r(/*! ../utils/logger */"./src/utils/logger.ts");function o(){return(o=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function l(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}!function(t){t.ID="ID",t.CLASS="CLASS",t.START_DATE="START-DATE",t.DURATION="DURATION",t.END_DATE="END-DATE",t.END_ON_NEXT="END-ON-NEXT",t.PLANNED_DURATION="PLANNED-DURATION",t.SCTE35_OUT="SCTE35-OUT",t.SCTE35_IN="SCTE35-IN"}(i||(i={}));var u=function(){function t(t,e){if(this.attr=void 0,this._startDate=void 0,this._endDate=void 0,this._badValueForSameId=void 0,e){var r=e.attr;for(var l in r)if(Object.prototype.hasOwnProperty.call(t,l)&&t[l]!==r[l]){s.logger.warn('DATERANGE tag attribute: "'+l+'" does not match for tags with ID: "'+t.ID+'"'),this._badValueForSameId=l;break}t=o(new a.AttrList({}),r,t)}if(this.attr=t,this._startDate=new Date(t[i.START_DATE]),i.END_DATE in this.attr){var u=new Date(this.attr[i.END_DATE]);(0,n.isFiniteNumber)(u.getTime())&&(this._endDate=u)}}var e,r,u;return e=t,(r=[{key:"id",get:function(){return this.attr.ID}},{key:"class",get:function(){return this.attr.CLASS}},{key:"startDate",get:function(){return this._startDate}},{key:"endDate",get:function(){if(this._endDate)return this._endDate;var t=this.duration;return null!==t?new Date(this._startDate.getTime()+1e3*t):null}},{key:"duration",get:function(){if(i.DURATION in this.attr){var t=this.attr.decimalFloatingPoint(i.DURATION);if((0,n.isFiniteNumber)(t))return t}else if(this._endDate)return(this._endDate.getTime()-this._startDate.getTime())/1e3;return null}},{key:"plannedDuration",get:function(){return i.PLANNED_DURATION in this.attr?this.attr.decimalFloatingPoint(i.PLANNED_DURATION):null}},{key:"endOnNext",get:function(){return this.attr.bool(i.END_ON_NEXT)}},{key:"isValid",get:function(){return!!this.id&&!this._badValueForSameId&&(0,n.isFiniteNumber)(this.startDate.getTime())&&(null===this.duration||this.duration>=0)&&(!this.endOnNext||!!this.class)}}])&&l(e.prototype,r),u&&l(e,u),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/loader/fragment-loader.ts":
/*!***************************************!*\
  !*** ./src/loader/fragment-loader.ts ***!
  \***************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{LoadError:()=>f,default:()=>c});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../errors */"./src/errors.ts");function a(t){var e="function"==typeof Map?new Map:void 0;return(a=function(t){if(null===t||(r=t,-1===Function.toString.call(r).indexOf("[native code]")))return t;var r;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,i)}function i(){return s(t,arguments,u(this).constructor)}return i.prototype=Object.create(t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),l(i,t)})(t)}function s(t,e,r){return(s=o()?Reflect.construct.bind():function(t,e,r){var i=[null];i.push.apply(i,e);var n=new(Function.bind.apply(t,i));return r&&l(n,r.prototype),n}).apply(null,arguments)}function o(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function l(t,e){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var d=Math.pow(2,17),c=function(){function t(t){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=t}var e=t.prototype;return e.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},e.abort=function(){this.loader&&this.loader.abort()},e.load=function(t,e){var r=this,i=t.url;if(!i)return Promise.reject(new f({type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:t,networkDetails:null},"Fragment does not have a "+(i?"part list":"url")));this.abort();var a=this.config,s=a.fLoader,o=a.loader;return new Promise((function(i,l){r.loader&&r.loader.destroy();var u=r.loader=t.loader=s?new s(a):new o(a),c=h(t),g={timeout:a.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:a.fragLoadingMaxRetryTimeout,highWaterMark:"initSegment"===t.sn?1/0:d};t.stats=u.stats,u.load(c,g,{onSuccess:function(e,n,a,s){r.resetLoader(t,u),i({frag:t,part:null,payload:e.data,networkDetails:s})},onError:function(e,i,a){r.resetLoader(t,u),l(new f({type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:t,response:e,networkDetails:a}))},onAbort:function(e,i,a){r.resetLoader(t,u),l(new f({type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.INTERNAL_ABORTED,fatal:!1,frag:t,networkDetails:a}))},onTimeout:function(e,i,a){r.resetLoader(t,u),l(new f({type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,networkDetails:a}))},onProgress:function(r,i,n,a){e&&e({frag:t,part:null,payload:n,networkDetails:a})}})}))},e.loadPart=function(t,e,r){var i=this;this.abort();var a=this.config,s=a.fLoader,o=a.loader;return new Promise((function(l,u){i.loader&&i.loader.destroy();var c=i.loader=t.loader=s?new s(a):new o(a),g=h(t,e),v={timeout:a.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:a.fragLoadingMaxRetryTimeout,highWaterMark:d};e.stats=c.stats,c.load(g,v,{onSuccess:function(n,a,s,o){i.resetLoader(t,c),i.updateStatsFromPart(t,e);var u={frag:t,part:e,payload:n.data,networkDetails:o};r(u),l(u)},onError:function(r,a,s){i.resetLoader(t,c),u(new f({type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.FRAG_LOAD_ERROR,fatal:!1,frag:t,part:e,response:r,networkDetails:s}))},onAbort:function(r,a,s){t.stats.aborted=e.stats.aborted,i.resetLoader(t,c),u(new f({type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.INTERNAL_ABORTED,fatal:!1,frag:t,part:e,networkDetails:s}))},onTimeout:function(r,a,s){i.resetLoader(t,c),u(new f({type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,part:e,networkDetails:s}))}})}))},e.updateStatsFromPart=function(t,e){var r=t.stats,i=e.stats,n=i.total;if(r.loaded+=i.loaded,n){var a=Math.round(t.duration/e.duration),s=Math.min(Math.round(r.loaded/n),a),o=(a-s)*Math.round(r.loaded/s);r.total=r.loaded+o}else r.total=Math.max(r.loaded,r.total);var l=r.loading,u=i.loading;l.start?l.first+=u.first-u.start:(l.start=u.start,l.first=u.first),l.end=u.end},e.resetLoader=function(t,e){t.loader=null,this.loader===e&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),e.destroy()},t}();function h(t,e){void 0===e&&(e=null);var r=e||t,n={frag:t,part:e,responseType:"arraybuffer",url:r.url,headers:{},rangeStart:0,rangeEnd:0},a=r.byteRangeStartOffset,s=r.byteRangeEndOffset;return(0,i.isFiniteNumber)(a)&&(0,i.isFiniteNumber)(s)&&(n.rangeStart=a,n.rangeEnd=s),n}var f=function(t){var e,r;function i(e){for(var r,i=arguments.length,n=new Array(i>1?i-1:0),a=1;a<i;a++)n[a-1]=arguments[a];return(r=t.call.apply(t,[this].concat(n))||this).data=void 0,r.data=e,r}return r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,l(e,r),i}(a(Error))},"./src/loader/fragment.ts":
/*!********************************!*\
  !*** ./src/loader/fragment.ts ***!
  \********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{BaseSegment:()=>f,ElementaryStreamTypes:()=>i,Fragment:()=>g,Part:()=>v});var i,n=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),a=r(/*! url-toolkit */"./node_modules/url-toolkit/src/url-toolkit.js"),s=r(/*! ../utils/logger */"./src/utils/logger.ts"),o=r(/*! ./level-key */"./src/loader/level-key.ts"),l=r(/*! ./load-stats */"./src/loader/load-stats.ts");function u(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,d(t,e)}function d(t,e){return(d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function c(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function h(t,e,r){return e&&c(t.prototype,e),r&&c(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}!function(t){t.AUDIO="audio",t.VIDEO="video",t.AUDIOVIDEO="audiovideo"}(i||(i={}));var f=function(){function t(t){var e;this._byteRange=null,this._url=null,this.baseurl=void 0,this.relurl=void 0,this.elementaryStreams=((e={})[i.AUDIO]=null,e[i.VIDEO]=null,e[i.AUDIOVIDEO]=null,e),this.baseurl=t}return t.prototype.setByteRange=function(t,e){var r=t.split("@",2),i=[];1===r.length?i[0]=e?e.byteRangeEndOffset:0:i[0]=parseInt(r[1]),i[1]=parseInt(r[0])+i[0],this._byteRange=i},h(t,[{key:"byteRange",get:function(){return this._byteRange?this._byteRange:[]}},{key:"byteRangeStartOffset",get:function(){return this.byteRange[0]}},{key:"byteRangeEndOffset",get:function(){return this.byteRange[1]}},{key:"url",get:function(){return!this._url&&this.baseurl&&this.relurl&&(this._url=(0,a.buildAbsoluteURL)(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""},set:function(t){this._url=t}}]),t}(),g=function(t){function e(e,r){var i;return(i=t.call(this,r)||this)._decryptdata=null,i.rawProgramDateTime=null,i.programDateTime=null,i.tagList=[],i.duration=0,i.sn=0,i.levelkey=void 0,i.type=void 0,i.loader=null,i.level=-1,i.cc=0,i.startPTS=void 0,i.endPTS=void 0,i.appendedPTS=void 0,i.startDTS=void 0,i.endDTS=void 0,i.start=0,i.deltaPTS=void 0,i.maxStartPTS=void 0,i.minEndPTS=void 0,i.stats=new l.LoadStats,i.urlId=0,i.data=void 0,i.bitrateTest=!1,i.title=null,i.initSegment=null,i.type=e,i}u(e,t);var r=e.prototype;return r.createInitializationVector=function(t){for(var e=new Uint8Array(16),r=12;r<16;r++)e[r]=t>>8*(15-r)&255;return e},r.setDecryptDataFromLevelKey=function(t,e){var r=t;return"AES-128"===(null==t?void 0:t.method)&&t.uri&&!t.iv&&((r=o.LevelKey.fromURI(t.uri)).method=t.method,r.iv=this.createInitializationVector(e),r.keyFormat="identity"),r},r.setElementaryStreamInfo=function(t,e,r,i,n,a){void 0===a&&(a=!1);var s=this.elementaryStreams,o=s[t];o?(o.startPTS=Math.min(o.startPTS,e),o.endPTS=Math.max(o.endPTS,r),o.startDTS=Math.min(o.startDTS,i),o.endDTS=Math.max(o.endDTS,n)):s[t]={startPTS:e,endPTS:r,startDTS:i,endDTS:n,partial:a}},r.clearElementaryStreamInfo=function(){var t=this.elementaryStreams;t[i.AUDIO]=null,t[i.VIDEO]=null,t[i.AUDIOVIDEO]=null},h(e,[{key:"decryptdata",get:function(){if(!this.levelkey&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkey){var t=this.sn;"number"!=typeof t&&(this.levelkey&&"AES-128"===this.levelkey.method&&!this.levelkey.iv&&s.logger.warn('missing IV for initialization segment with method="'+this.levelkey.method+'" - compliance issue'),t=0),this._decryptdata=this.setDecryptDataFromLevelKey(this.levelkey,t)}return this._decryptdata}},{key:"end",get:function(){return this.start+this.duration}},{key:"endProgramDateTime",get:function(){if(null===this.programDateTime)return null;if(!(0,n.isFiniteNumber)(this.programDateTime))return null;var t=(0,n.isFiniteNumber)(this.duration)?this.duration:0;return this.programDateTime+1e3*t}},{key:"encrypted",get:function(){var t;return!(null===(t=this.decryptdata)||void 0===t||!t.keyFormat||!this.decryptdata.uri)}}]),e}(f),v=function(t){function e(e,r,i,n,a){var s;(s=t.call(this,i)||this).fragOffset=0,s.duration=0,s.gap=!1,s.independent=!1,s.relurl=void 0,s.fragment=void 0,s.index=void 0,s.stats=new l.LoadStats,s.duration=e.decimalFloatingPoint("DURATION"),s.gap=e.bool("GAP"),s.independent=e.bool("INDEPENDENT"),s.relurl=e.enumeratedString("URI"),s.fragment=r,s.index=n;var o=e.enumeratedString("BYTERANGE");return o&&s.setByteRange(o,a),a&&(s.fragOffset=a.fragOffset+a.duration),s}return u(e,t),h(e,[{key:"start",get:function(){return this.fragment.start+this.fragOffset}},{key:"end",get:function(){return this.start+this.duration}},{key:"loaded",get:function(){var t=this.elementaryStreams;return!!(t.audio||t.video||t.audiovideo)}}]),e}(f)},"./src/loader/key-loader.ts":
/*!**********************************!*\
  !*** ./src/loader/key-loader.ts ***!
  \**********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>s});var i=r(/*! ../events */"./src/events.ts"),n=r(/*! ../errors */"./src/errors.ts"),a=r(/*! ../utils/logger */"./src/utils/logger.ts"),s=function(){function t(t){this.hls=void 0,this.loaders={},this.decryptkey=null,this.decrypturl=null,this.hls=t,this.registerListeners()}var e=t.prototype;return e.startLoad=function(t){},e.stopLoad=function(){this.destroyInternalLoaders()},e.registerListeners=function(){this.hls.on(i.Events.KEY_LOADING,this.onKeyLoading,this)},e.unregisterListeners=function(){this.hls.off(i.Events.KEY_LOADING,this.onKeyLoading)},e.destroyInternalLoaders=function(){for(var t in this.loaders){var e=this.loaders[t];e&&e.destroy()}this.loaders={}},e.destroy=function(){this.unregisterListeners(),this.destroyInternalLoaders()},e.onKeyLoading=function(t,e){var r=e.frag,n=r.type,s=this.loaders[n];if(r.decryptdata){var o=r.decryptdata.uri;if(o!==this.decrypturl||null===this.decryptkey){var l=this.hls.config;if(s&&(a.logger.warn("abort previous key loader for type:"+n),s.abort()),!o)return void a.logger.warn("key uri is falsy");var u=l.loader,d=r.loader=this.loaders[n]=new u(l);this.decrypturl=o,this.decryptkey=null;var c={url:o,frag:r,responseType:"arraybuffer"},h={timeout:l.fragLoadingTimeOut,maxRetry:0,retryDelay:l.fragLoadingRetryDelay,maxRetryDelay:l.fragLoadingMaxRetryTimeout,highWaterMark:0},f={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};d.load(c,h,f)}else this.decryptkey&&(r.decryptdata.key=this.decryptkey,this.hls.trigger(i.Events.KEY_LOADED,{frag:r}))}else a.logger.warn("Missing decryption data on fragment in onKeyLoading")},e.loadsuccess=function(t,e,r){var n=r.frag;n.decryptdata?(this.decryptkey=n.decryptdata.key=new Uint8Array(t.data),n.loader=null,delete this.loaders[n.type],this.hls.trigger(i.Events.KEY_LOADED,{frag:n})):a.logger.error("after key load, decryptdata unset")},e.loaderror=function(t,e){var r=e.frag,a=r.loader;a&&a.abort(),delete this.loaders[r.type],this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.KEY_LOAD_ERROR,fatal:!1,frag:r,response:t})},e.loadtimeout=function(t,e){var r=e.frag,a=r.loader;a&&a.abort(),delete this.loaders[r.type],this.hls.trigger(i.Events.ERROR,{type:n.ErrorTypes.NETWORK_ERROR,details:n.ErrorDetails.KEY_LOAD_TIMEOUT,fatal:!1,frag:r})},t}()},"./src/loader/level-details.ts":
/*!*************************************!*\
  !*** ./src/loader/level-details.ts ***!
  \*************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{LevelDetails:()=>a});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts");function n(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var a=function(){function t(t){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.dateRanges=void 0,this.live=!0,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.availabilityDelay=void 0,this.misses=0,this.needSidxRanges=!1,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.fragments=[],this.dateRanges={},this.url=t}var e,r,a;return t.prototype.reloaded=function(t){if(!t)return this.advanced=!0,void(this.updated=!0);var e=this.lastPartSn-t.lastPartSn,r=this.lastPartIndex-t.lastPartIndex;this.updated=this.endSN!==t.endSN||!!r||!!e,this.advanced=this.endSN>t.endSN||e>0||0===e&&r>0,this.updated||this.advanced?this.misses=Math.floor(.6*t.misses):this.misses=t.misses+1,this.availabilityDelay=t.availabilityDelay},e=t,(r=[{key:"hasProgramDateTime",get:function(){return!!this.fragments.length&&(0,i.isFiniteNumber)(this.fragments[this.fragments.length-1].programDateTime)}},{key:"levelTargetDuration",get:function(){return this.averagetargetduration||this.targetduration||10}},{key:"drift",get:function(){var t=this.driftEndTime-this.driftStartTime;return t>0?1e3*(this.driftEnd-this.driftStart)/t:1}},{key:"edge",get:function(){return this.partEnd||this.fragmentEnd}},{key:"partEnd",get:function(){var t;return null!==(t=this.partList)&&void 0!==t&&t.length?this.partList[this.partList.length-1].end:this.fragmentEnd}},{key:"fragmentEnd",get:function(){var t;return null!==(t=this.fragments)&&void 0!==t&&t.length?this.fragments[this.fragments.length-1].end:0}},{key:"age",get:function(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}},{key:"lastPartIndex",get:function(){var t;return null!==(t=this.partList)&&void 0!==t&&t.length?this.partList[this.partList.length-1].index:-1}},{key:"lastPartSn",get:function(){var t;return null!==(t=this.partList)&&void 0!==t&&t.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}}])&&n(e.prototype,r),a&&n(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/loader/level-key.ts":
/*!*********************************!*\
  !*** ./src/loader/level-key.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{LevelKey:()=>a});var i=r(/*! url-toolkit */"./node_modules/url-toolkit/src/url-toolkit.js");function n(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var a=function(){function t(t,e){this._uri=null,this.method=null,this.keyFormat=null,this.keyFormatVersions=null,this.keyID=null,this.key=null,this.iv=null,this._uri=e?(0,i.buildAbsoluteURL)(t,e,{alwaysNormalize:!0}):t}var e,r,a;return t.fromURL=function(e,r){return new t(e,r)},t.fromURI=function(e){return new t(e)},e=t,(r=[{key:"uri",get:function(){return this._uri}}])&&n(e.prototype,r),a&&n(e,a),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/loader/load-stats.ts":
/*!**********************************!*\
  !*** ./src/loader/load-stats.ts ***!
  \**********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{LoadStats:()=>i});var i=function(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}}},"./src/loader/m3u8-parser.ts":
/*!***********************************!*\
  !*** ./src/loader/m3u8-parser.ts ***!
  \***********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>m});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! url-toolkit */"./node_modules/url-toolkit/src/url-toolkit.js"),a=r(/*! ./date-range */"./src/loader/date-range.ts"),s=r(/*! ./fragment */"./src/loader/fragment.ts"),o=r(/*! ./level-details */"./src/loader/level-details.ts"),l=r(/*! ./level-key */"./src/loader/level-key.ts"),u=r(/*! ../utils/attr-list */"./src/utils/attr-list.ts"),d=r(/*! ../utils/logger */"./src/utils/logger.ts"),c=r(/*! ../utils/codecs */"./src/utils/codecs.ts"),h=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-SESSION-DATA:([^\r\n]*)[\r\n]+/g,f=/#EXT-X-MEDIA:(.*)/g,g=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[\S ]*)/.source,/#EXT-X-BYTERANGE:*(.+)/.source,/#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/#.*/.source].join("|"),"g"),v=new RegExp([/#(EXTM3U)/.source,/#EXT-X-(DATERANGE|KEY|MAP|PART|PART-INF|PLAYLIST-TYPE|PRELOAD-HINT|RENDITION-REPORT|SERVER-CONTROL|SKIP|START):(.+)/.source,/#EXT-X-(BITRATE|DISCONTINUITY-SEQUENCE|MEDIA-SEQUENCE|TARGETDURATION|VERSION): *(\d+)/.source,/#EXT-X-(DISCONTINUITY|ENDLIST|GAP)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|")),p=/\.(mp4|m4s|m4v|m4a)$/i,m=function(){function t(){}return t.findGroup=function(t,e){for(var r=0;r<t.length;r++){var i=t[r];if(i.id===e)return i}},t.convertAVC1ToAVCOTI=function(t){var e=t.split(".");if(e.length>2){var r=e.shift()+".";return r+=parseInt(e.shift()).toString(16),r+=("000"+parseInt(e.shift()).toString(16)).slice(-4)}return t},t.resolve=function(t,e){return n.buildAbsoluteURL(e,t,{alwaysNormalize:!0})},t.parseMasterPlaylist=function(e,r){var i,n=[],a=[],s={},o=!1;for(h.lastIndex=0;null!=(i=h.exec(e));)if(i[1]){var l,d=new u.AttrList(i[1]),c={attrs:d,bitrate:d.decimalInteger("AVERAGE-BANDWIDTH")||d.decimalInteger("BANDWIDTH"),name:d.NAME,url:t.resolve(i[2],r)},f=d.decimalResolution("RESOLUTION");f&&(c.width=f.width,c.height=f.height),y((d.CODECS||"").split(/[ ,]+/).filter((function(t){return t})),c),c.videoCodec&&-1!==c.videoCodec.indexOf("avc1")&&(c.videoCodec=t.convertAVC1ToAVCOTI(c.videoCodec)),null!==(l=c.unknownCodecs)&&void 0!==l&&l.length||a.push(c),n.push(c)}else if(i[3]){var g=new u.AttrList(i[3]);g["DATA-ID"]&&(o=!0,s[g["DATA-ID"]]=g)}return{levels:a.length>0&&a.length<n.length?a:n,sessionData:o?s:null}},t.parseMasterPlaylistMedia=function(e,r,i,n){var a;void 0===n&&(n=[]);var s=[],o=0;for(f.lastIndex=0;null!==(a=f.exec(e));){var l=new u.AttrList(a[1]);if(l.TYPE===i){var d={attrs:l,bitrate:0,id:o++,groupId:l["GROUP-ID"],instreamId:l["INSTREAM-ID"],name:l.NAME||l.LANGUAGE||"",type:i,default:l.bool("DEFAULT"),autoselect:l.bool("AUTOSELECT"),forced:l.bool("FORCED"),lang:l.LANGUAGE,url:l.URI?t.resolve(l.URI,r):""};if(n.length){var c=t.findGroup(n,d.groupId)||n[0];E(d,c,"audioCodec"),E(d,c,"textCodec")}s.push(d)}}return s},t.parseLevelPlaylist=function(t,e,r,c,h){var f,m,y,E=new o.LevelDetails(e),b=E.fragments,D=null,L=0,A=0,R=0,k=0,I=null,_=new s.Fragment(c,e),C=-1,w=!1;for(g.lastIndex=0,E.m3u8=t;null!==(f=g.exec(t));){w&&(w=!1,(_=new s.Fragment(c,e)).start=R,_.sn=L,_.cc=k,_.level=r,D&&(_.initSegment=D,_.rawProgramDateTime=D.rawProgramDateTime,D.rawProgramDateTime=null));var O=f[1];if(O){_.duration=parseFloat(O);var x=(" "+f[2]).slice(1);_.title=x||null,_.tagList.push(x?["INF",O,x]:["INF",O])}else if(f[3])(0,i.isFiniteNumber)(_.duration)&&(_.start=R,y&&(_.levelkey=y),_.sn=L,_.level=r,_.cc=k,_.urlId=h,b.push(_),_.relurl=(" "+f[3]).slice(1),T(_,I),I=_,R+=_.duration,L++,A=0,w=!0);else if(f[4]){var P=(" "+f[4]).slice(1);I?_.setByteRange(P,I):_.setByteRange(P)}else if(f[5])_.rawProgramDateTime=(" "+f[5]).slice(1),_.tagList.push(["PROGRAM-DATE-TIME",_.rawProgramDateTime]),-1===C&&(C=b.length);else{if(!(f=f[0].match(v))){d.logger.warn("No matches on slow regex match for level playlist!");continue}for(m=1;m<f.length&&void 0===f[m];m++);var F=(" "+f[m]).slice(1),M=(" "+f[m+1]).slice(1),N=f[m+2]?(" "+f[m+2]).slice(1):"";switch(F){case"PLAYLIST-TYPE":E.type=M.toUpperCase();break;case"MEDIA-SEQUENCE":L=E.startSN=parseInt(M);break;case"SKIP":var U=new u.AttrList(M),B=U.decimalInteger("SKIPPED-SEGMENTS");if((0,i.isFiniteNumber)(B)){E.skippedSegments=B;for(var G=B;G--;)b.unshift(null);L+=B}var K=U.enumeratedString("RECENTLY-REMOVED-DATERANGES");K&&(E.recentlyRemovedDateranges=K.split("\t"));break;case"TARGETDURATION":E.targetduration=parseFloat(M);break;case"VERSION":E.version=parseInt(M);break;case"EXTM3U":break;case"ENDLIST":E.live=!1;break;case"#":(M||N)&&_.tagList.push(N?[M,N]:[M]);break;case"DISCONTINUITY":k++,_.tagList.push(["DIS"]);break;case"GAP":_.tagList.push([F]);break;case"BITRATE":_.tagList.push([F,M]);break;case"DATERANGE":var H=new u.AttrList(M),j=new a.DateRange(H,E.dateRanges[H.ID]);j.isValid||E.skippedSegments?E.dateRanges[j.id]=j:d.logger.warn('Ignoring invalid DATERANGE tag: "'+M+'"'),_.tagList.push(["EXT-X-DATERANGE",M]);break;case"DISCONTINUITY-SEQUENCE":k=parseInt(M);break;case"KEY":var V,W=new u.AttrList(M),Y=W.enumeratedString("METHOD"),q=W.URI,z=W.hexadecimalInteger("IV"),X=W.enumeratedString("KEYFORMATVERSIONS"),Q=W.enumeratedString("KEYID"),$=null!=(V=W.enumeratedString("KEYFORMAT"))?V:"identity";if(["com.apple.streamingkeydelivery","com.microsoft.playready","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed","com.widevine"].indexOf($)>-1){d.logger.warn("Keyformat "+$+" is not supported from the manifest");continue}if("identity"!==$)continue;Y&&(y=l.LevelKey.fromURL(e,q),q&&["AES-128","SAMPLE-AES","SAMPLE-AES-CENC"].indexOf(Y)>=0&&(y.method=Y,y.keyFormat=$,Q&&(y.keyID=Q),X&&(y.keyFormatVersions=X),y.iv=z));break;case"START":var J=new u.AttrList(M).decimalFloatingPoint("TIME-OFFSET");(0,i.isFiniteNumber)(J)&&(E.startTimeOffset=J);break;case"MAP":var Z=new u.AttrList(M);if(_.duration){var tt=new s.Fragment(c,e);S(tt,Z,r,y),D=tt,_.initSegment=D,D.rawProgramDateTime&&!_.rawProgramDateTime&&(_.rawProgramDateTime=D.rawProgramDateTime)}else S(_,Z,r,y),D=_,w=!0;break;case"SERVER-CONTROL":var et=new u.AttrList(M);E.canBlockReload=et.bool("CAN-BLOCK-RELOAD"),E.canSkipUntil=et.optionalFloat("CAN-SKIP-UNTIL",0),E.canSkipDateRanges=E.canSkipUntil>0&&et.bool("CAN-SKIP-DATERANGES"),E.partHoldBack=et.optionalFloat("PART-HOLD-BACK",0),E.holdBack=et.optionalFloat("HOLD-BACK",0);break;case"PART-INF":var rt=new u.AttrList(M);E.partTarget=rt.decimalFloatingPoint("PART-TARGET");break;case"PART":var it=E.partList;it||(it=E.partList=[]);var nt=A>0?it[it.length-1]:void 0,at=A++,st=new s.Part(new u.AttrList(M),_,e,at,nt);it.push(st),_.duration+=st.duration;break;case"PRELOAD-HINT":var ot=new u.AttrList(M);E.preloadHint=ot;break;case"RENDITION-REPORT":var lt=new u.AttrList(M);E.renditionReports=E.renditionReports||[],E.renditionReports.push(lt);break;default:d.logger.warn("line parsed but not handled: "+f)}}}I&&!I.relurl?(b.pop(),R-=I.duration,E.partList&&(E.fragmentHint=I)):E.partList&&(T(_,I),_.cc=k,E.fragmentHint=_);var ut=b.length,dt=b[0],ct=b[ut-1];if((R+=E.skippedSegments*E.targetduration)>0&&ut&&ct){E.averagetargetduration=R/ut;var ht=ct.sn;E.endSN="initSegment"!==ht?ht:0,dt&&(E.startCC=dt.cc,dt.initSegment||E.fragments.every((function(t){return t.relurl&&(e=t.relurl,p.test(null!=(r=null===(i=n.parseURL(e))||void 0===i?void 0:i.path)?r:""));var e,r,i}))&&(d.logger.warn("MP4 fragments found but no init segment (probably no MAP, incomplete M3U8), trying to fetch SIDX"),(_=new s.Fragment(c,e)).relurl=ct.relurl,_.level=r,_.sn="initSegment",dt.initSegment=_,E.needSidxRanges=!0))}else E.endSN=0,E.startCC=0;return E.fragmentHint&&(R+=E.fragmentHint.duration),E.totalduration=R,E.endCC=k,C>0&&function(t,e){for(var r=t[e],i=e;i--;){var n=t[i];if(!n)return;n.programDateTime=r.programDateTime-1e3*n.duration,r=n}}(b,C),E},t}();function y(t,e){["video","audio","text"].forEach((function(r){var i=t.filter((function(t){return(0,c.isCodecType)(t,r)}));if(i.length){var n=i.filter((function(t){return 0===t.lastIndexOf("avc1",0)||0===t.lastIndexOf("mp4a",0)}));e[r+"Codec"]=n.length>0?n[0]:i[0],t=t.filter((function(t){return-1===i.indexOf(t)}))}})),e.unknownCodecs=t}function E(t,e,r){var i=e[r];i&&(t[r]=i)}function T(t,e){t.rawProgramDateTime?t.programDateTime=Date.parse(t.rawProgramDateTime):null!=e&&e.programDateTime&&(t.programDateTime=e.endProgramDateTime),(0,i.isFiniteNumber)(t.programDateTime)||(t.programDateTime=null,t.rawProgramDateTime=null)}function S(t,e,r,i){t.relurl=e.URI,e.BYTERANGE&&t.setByteRange(e.BYTERANGE),t.level=r,t.sn="initSegment",i&&(t.levelkey=i),t.initSegment=null}},"./src/loader/playlist-loader.ts":
/*!***************************************!*\
  !*** ./src/loader/playlist-loader.ts ***!
  \***************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>h});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../events */"./src/events.ts"),a=r(/*! ../errors */"./src/errors.ts"),s=r(/*! ../utils/logger */"./src/utils/logger.ts"),o=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),l=r(/*! ./m3u8-parser */"./src/loader/m3u8-parser.ts"),u=r(/*! ../types/loader */"./src/types/loader.ts"),d=r(/*! ../utils/attr-list */"./src/utils/attr-list.ts");function c(t,e){var r=t.url;return void 0!==r&&0!==r.indexOf("data:")||(r=e.url),r}const h=function(){function t(t){this.hls=void 0,this.loaders=Object.create(null),this.hls=t,this.registerListeners()}var e=t.prototype;return e.startLoad=function(t){},e.stopLoad=function(){this.destroyInternalLoaders()},e.registerListeners=function(){var t=this.hls;t.on(n.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.on(n.Events.LEVEL_LOADING,this.onLevelLoading,this),t.on(n.Events.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),t.on(n.Events.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},e.unregisterListeners=function(){var t=this.hls;t.off(n.Events.MANIFEST_LOADING,this.onManifestLoading,this),t.off(n.Events.LEVEL_LOADING,this.onLevelLoading,this),t.off(n.Events.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),t.off(n.Events.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},e.createInternalLoader=function(t){var e=this.hls.config,r=e.pLoader,i=e.loader,n=new(r||i)(e);return t.loader=n,this.loaders[t.type]=n,n},e.getInternalLoader=function(t){return this.loaders[t.type]},e.resetInternalLoader=function(t){this.loaders[t]&&delete this.loaders[t]},e.destroyInternalLoaders=function(){for(var t in this.loaders){var e=this.loaders[t];e&&e.destroy(),this.resetInternalLoader(t)}},e.destroy=function(){this.unregisterListeners(),this.destroyInternalLoaders()},e.onManifestLoading=function(t,e){var r=e.url;this.load({id:null,groupId:null,level:0,responseType:"text",type:u.PlaylistContextType.MANIFEST,url:r,deliveryDirectives:null})},e.onLevelLoading=function(t,e){var r=e.id,i=e.level,n=e.url,a=e.deliveryDirectives;this.load({id:r,groupId:null,level:i,responseType:"text",type:u.PlaylistContextType.LEVEL,url:n,deliveryDirectives:a})},e.onAudioTrackLoading=function(t,e){var r=e.id,i=e.groupId,n=e.url,a=e.deliveryDirectives;this.load({id:r,groupId:i,level:null,responseType:"text",type:u.PlaylistContextType.AUDIO_TRACK,url:n,deliveryDirectives:a})},e.onSubtitleTrackLoading=function(t,e){var r=e.id,i=e.groupId,n=e.url,a=e.deliveryDirectives;this.load({id:r,groupId:i,level:null,responseType:"text",type:u.PlaylistContextType.SUBTITLE_TRACK,url:n,deliveryDirectives:a})},e.load=function(t){var e,r,i,n,a,o,l=this.hls.config,d=this.getInternalLoader(t);if(d){var c=d.context;if(c&&c.url===t.url)return void s.logger.trace("[playlist-loader]: playlist request ongoing");s.logger.log("[playlist-loader]: aborting previous loader for type: "+t.type),d.abort()}switch(t.type){case u.PlaylistContextType.MANIFEST:r=l.manifestLoadingMaxRetry,i=l.manifestLoadingTimeOut,n=l.manifestLoadingRetryDelay,a=l.manifestLoadingMaxRetryTimeout;break;case u.PlaylistContextType.LEVEL:case u.PlaylistContextType.AUDIO_TRACK:case u.PlaylistContextType.SUBTITLE_TRACK:r=0,i=l.levelLoadingTimeOut;break;default:r=l.levelLoadingMaxRetry,i=l.levelLoadingTimeOut,n=l.levelLoadingRetryDelay,a=l.levelLoadingMaxRetryTimeout}if(d=this.createInternalLoader(t),null!==(e=t.deliveryDirectives)&&void 0!==e&&e.part&&(t.type===u.PlaylistContextType.LEVEL&&null!==t.level?o=this.hls.levels[t.level].details:t.type===u.PlaylistContextType.AUDIO_TRACK&&null!==t.id?o=this.hls.audioTracks[t.id].details:t.type===u.PlaylistContextType.SUBTITLE_TRACK&&null!==t.id&&(o=this.hls.subtitleTracks[t.id].details),o)){var h=o.partTarget,f=o.targetduration;h&&f&&(i=Math.min(1e3*Math.max(3*h,.8*f),i))}var g={timeout:i,maxRetry:r,retryDelay:n,maxRetryDelay:a,highWaterMark:0},v={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)};d.load(t,g,v)},e.loadsuccess=function(t,e,r,i){if(void 0===i&&(i=null),r.isSidxRequest)return this.handleSidxRequest(t,r),void this.handlePlaylistLoaded(t,e,r,i);this.resetInternalLoader(r.type);var n=t.data;0===n.indexOf("#EXTM3U")?(e.parsing.start=performance.now(),n.indexOf("#EXTINF:")>0||n.indexOf("#EXT-X-TARGETDURATION:")>0?this.handleTrackOrLevelPlaylist(t,e,r,i):this.handleMasterPlaylist(t,e,r,i)):this.handleManifestParsingError(t,r,"no EXTM3U delimiter",i)},e.loaderror=function(t,e,r){void 0===r&&(r=null),this.handleNetworkError(e,r,!1,t)},e.loadtimeout=function(t,e,r){void 0===r&&(r=null),this.handleNetworkError(e,r,!0)},e.handleMasterPlaylist=function(t,e,r,i){var a=this.hls,o=t.data,u=c(t,r),h=l.default.parseMasterPlaylist(o,u),f=h.levels,g=h.sessionData;if(f.length){var v=f.map((function(t){return{id:t.attrs.AUDIO,audioCodec:t.audioCodec}})),p=f.map((function(t){return{id:t.attrs.SUBTITLES,textCodec:t.textCodec}})),m=l.default.parseMasterPlaylistMedia(o,u,"AUDIO",v),y=l.default.parseMasterPlaylistMedia(o,u,"SUBTITLES",p),E=l.default.parseMasterPlaylistMedia(o,u,"CLOSED-CAPTIONS");m.length&&(m.some((function(t){return!t.url}))||!f[0].audioCodec||f[0].attrs.AUDIO||(s.logger.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),m.unshift({type:"main",name:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new d.AttrList({}),bitrate:0,url:""}))),a.trigger(n.Events.MANIFEST_LOADED,{levels:f,audioTracks:m,subtitles:y,captions:E,url:u,stats:e,networkDetails:i,sessionData:g})}else this.handleManifestParsingError(t,r,"no level found in manifest",i)},e.handleTrackOrLevelPlaylist=function(t,e,r,s){var o=this.hls,h=r.id,f=r.level,g=r.type,v=c(t,r),p=(0,i.isFiniteNumber)(h)?h:0,m=(0,i.isFiniteNumber)(f)?f:p,y=function(t){switch(t.type){case u.PlaylistContextType.AUDIO_TRACK:return u.PlaylistLevelType.AUDIO;case u.PlaylistContextType.SUBTITLE_TRACK:return u.PlaylistLevelType.SUBTITLE;default:return u.PlaylistLevelType.MAIN}}(r),E=l.default.parseLevelPlaylist(t.data,v,m,y,p);if(E.fragments.length){if(g===u.PlaylistContextType.MANIFEST){var T={attrs:new d.AttrList({}),bitrate:0,details:E,name:"",url:v};o.trigger(n.Events.MANIFEST_LOADED,{levels:[T],audioTracks:[],url:v,stats:e,networkDetails:s,sessionData:null})}if(e.parsing.end=performance.now(),E.needSidxRanges){var S,b=null===(S=E.fragments[0].initSegment)||void 0===S?void 0:S.url;this.load({url:b,isSidxRequest:!0,type:g,level:f,levelDetails:E,id:h,groupId:null,rangeStart:0,rangeEnd:2048,responseType:"arraybuffer",deliveryDirectives:null})}else r.levelDetails=E,this.handlePlaylistLoaded(t,e,r,s)}else o.trigger(n.Events.ERROR,{type:a.ErrorTypes.NETWORK_ERROR,details:a.ErrorDetails.LEVEL_EMPTY_ERROR,fatal:!1,url:v,reason:"no fragments found in level",level:"number"==typeof r.level?r.level:void 0})},e.handleSidxRequest=function(t,e){var r=new Uint8Array(t.data),i=(0,o.findBox)(r,["sidx"])[0];if(i){var n=(0,o.parseSegmentIndex)(i);if(n){var a=n.references,s=e.levelDetails;a.forEach((function(t,e){var i=t.info,n=s.fragments[e];if(0===n.byteRange.length&&n.setByteRange(String(1+i.end-i.start)+"@"+String(i.start)),n.initSegment){var a=(0,o.findBox)(r,["moov"])[0],l=a?a.length:null;n.initSegment.setByteRange(String(l)+"@0")}}))}}},e.handleManifestParsingError=function(t,e,r,i){this.hls.trigger(n.Events.ERROR,{type:a.ErrorTypes.NETWORK_ERROR,details:a.ErrorDetails.MANIFEST_PARSING_ERROR,fatal:e.type===u.PlaylistContextType.MANIFEST,url:t.url,reason:r,response:t,context:e,networkDetails:i})},e.handleNetworkError=function(t,e,r,i){void 0===r&&(r=!1),s.logger.warn("[playlist-loader]: A network "+(r?"timeout":"error")+" occurred while loading "+t.type+" level: "+t.level+" id: "+t.id+' group-id: "'+t.groupId+'"');var o=a.ErrorDetails.UNKNOWN,l=!1,d=this.getInternalLoader(t);switch(t.type){case u.PlaylistContextType.MANIFEST:o=r?a.ErrorDetails.MANIFEST_LOAD_TIMEOUT:a.ErrorDetails.MANIFEST_LOAD_ERROR,l=!0;break;case u.PlaylistContextType.LEVEL:o=r?a.ErrorDetails.LEVEL_LOAD_TIMEOUT:a.ErrorDetails.LEVEL_LOAD_ERROR,l=!1;break;case u.PlaylistContextType.AUDIO_TRACK:o=r?a.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT:a.ErrorDetails.AUDIO_TRACK_LOAD_ERROR,l=!1;break;case u.PlaylistContextType.SUBTITLE_TRACK:o=r?a.ErrorDetails.SUBTITLE_TRACK_LOAD_TIMEOUT:a.ErrorDetails.SUBTITLE_LOAD_ERROR,l=!1}d&&this.resetInternalLoader(t.type);var c={type:a.ErrorTypes.NETWORK_ERROR,details:o,fatal:l,url:t.url,loader:d,context:t,networkDetails:e};i&&(c.response=i),this.hls.trigger(n.Events.ERROR,c)},e.handlePlaylistLoaded=function(t,e,r,i){var a=r.type,s=r.level,o=r.id,l=r.groupId,d=r.loader,c=r.levelDetails,h=r.deliveryDirectives;if(null!=c&&c.targetduration){if(d)switch(c.live&&(d.getCacheAge&&(c.ageHeader=d.getCacheAge()||0),d.getCacheAge&&!isNaN(c.ageHeader)||(c.ageHeader=0)),a){case u.PlaylistContextType.MANIFEST:case u.PlaylistContextType.LEVEL:this.hls.trigger(n.Events.LEVEL_LOADED,{details:c,level:s||0,id:o||0,stats:e,networkDetails:i,deliveryDirectives:h});break;case u.PlaylistContextType.AUDIO_TRACK:this.hls.trigger(n.Events.AUDIO_TRACK_LOADED,{details:c,id:o||0,groupId:l||"",stats:e,networkDetails:i,deliveryDirectives:h});break;case u.PlaylistContextType.SUBTITLE_TRACK:this.hls.trigger(n.Events.SUBTITLE_TRACK_LOADED,{details:c,id:o||0,groupId:l||"",stats:e,networkDetails:i,deliveryDirectives:h})}}else this.handleManifestParsingError(t,r,"invalid target duration",i)},t}()},"./src/polyfills/number.ts":
/*!*********************************!*\
  !*** ./src/polyfills/number.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{MAX_SAFE_INTEGER:()=>n,isFiniteNumber:()=>i});var i=Number.isFinite||function(t){return"number"==typeof t&&isFinite(t)},n=Number.MAX_SAFE_INTEGER||9007199254740991},"./src/remux/aac-helper.ts":
/*!*********************************!*\
  !*** ./src/remux/aac-helper.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});const i=function(){function t(){}return t.getSilentFrame=function(t,e){switch(t){case"mp4a.40.2":if(1===e)return new Uint8Array([0,200,0,128,35,128]);if(2===e)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===e)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(1===e)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===e)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===e)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}},t}()},"./src/remux/mp4-generator.ts":
/*!************************************!*\
  !*** ./src/remux/mp4-generator.ts ***!
  \************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>a});var i=Math.pow(2,32)-1,n=function(){function t(){}return t.init=function(){var e;for(e in t.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]},t.types)t.types.hasOwnProperty(e)&&(t.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);var r=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);t.HDLR_TYPES={video:r,audio:i};var n=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),a=new Uint8Array([0,0,0,0,0,0,0,0]);t.STTS=t.STSC=t.STCO=a,t.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),t.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),t.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),t.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var s=new Uint8Array([105,115,111,109]),o=new Uint8Array([97,118,99,49]),l=new Uint8Array([0,0,0,1]);t.FTYP=t.box(t.types.ftyp,s,l,s,o),t.DINF=t.box(t.types.dinf,t.box(t.types.dref,n))},t.box=function(t){for(var e=8,r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];for(var a=i.length,s=a;a--;)e+=i[a].byteLength;var o=new Uint8Array(e);for(o[0]=e>>24&255,o[1]=e>>16&255,o[2]=e>>8&255,o[3]=255&e,o.set(t,4),a=0,e=8;a<s;a++)o.set(i[a],e),e+=i[a].byteLength;return o},t.hdlr=function(e){return t.box(t.types.hdlr,t.HDLR_TYPES[e])},t.mdat=function(e){return t.box(t.types.mdat,e)},t.mdhd=function(e,r){r*=e;var n=Math.floor(r/(i+1)),a=Math.floor(r%(i+1));return t.box(t.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,n>>24,n>>16&255,n>>8&255,255&n,a>>24,a>>16&255,a>>8&255,255&a,85,196,0,0]))},t.mdia=function(e){return t.box(t.types.mdia,t.mdhd(e.timescale,e.duration),t.hdlr(e.type),t.minf(e))},t.mfhd=function(e){return t.box(t.types.mfhd,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e]))},t.minf=function(e){return"audio"===e.type?t.box(t.types.minf,t.box(t.types.smhd,t.SMHD),t.DINF,t.stbl(e)):t.box(t.types.minf,t.box(t.types.vmhd,t.VMHD),t.DINF,t.stbl(e))},t.moof=function(e,r,i){return t.box(t.types.moof,t.mfhd(e),t.traf(i,r))},t.moov=function(e){for(var r=e.length,i=[];r--;)i[r]=t.trak(e[r]);return t.box.apply(null,[t.types.moov,t.mvhd(e[0].timescale,e[0].duration)].concat(i).concat(t.mvex(e)))},t.mvex=function(e){for(var r=e.length,i=[];r--;)i[r]=t.trex(e[r]);return t.box.apply(null,[t.types.mvex].concat(i))},t.mvhd=function(e,r){r*=e;var n=Math.floor(r/(i+1)),a=Math.floor(r%(i+1)),s=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,n>>24,n>>16&255,n>>8&255,255&n,a>>24,a>>16&255,a>>8&255,255&a,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return t.box(t.types.mvhd,s)},t.sdtp=function(e){var r,i,n=e.samples||[],a=new Uint8Array(4+n.length);for(r=0;r<n.length;r++)i=n[r].flags,a[r+4]=i.dependsOn<<4|i.isDependedOn<<2|i.hasRedundancy;return t.box(t.types.sdtp,a)},t.stbl=function(e){return t.box(t.types.stbl,t.stsd(e),t.box(t.types.stts,t.STTS),t.box(t.types.stsc,t.STSC),t.box(t.types.stsz,t.STSZ),t.box(t.types.stco,t.STCO))},t.avc1=function(e){var r,i,n,a=[],s=[];for(r=0;r<e.sps.length;r++)n=(i=e.sps[r]).byteLength,a.push(n>>>8&255),a.push(255&n),a=a.concat(Array.prototype.slice.call(i));for(r=0;r<e.pps.length;r++)n=(i=e.pps[r]).byteLength,s.push(n>>>8&255),s.push(255&n),s=s.concat(Array.prototype.slice.call(i));var o=t.box(t.types.avcC,new Uint8Array([1,a[3],a[4],a[5],255,224|e.sps.length].concat(a).concat([e.pps.length]).concat(s))),l=e.width,u=e.height,d=e.pixelRatio[0],c=e.pixelRatio[1];return t.box(t.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,l>>8&255,255&l,u>>8&255,255&u,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o,t.box(t.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),t.box(t.types.pasp,new Uint8Array([d>>24,d>>16&255,d>>8&255,255&d,c>>24,c>>16&255,c>>8&255,255&c])))},t.esds=function(t){var e=t.config.length;return new Uint8Array([0,0,0,0,3,23+e,0,1,0,4,15+e,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([e]).concat(t.config).concat([6,1,2]))},t.mp4a=function(e){var r=e.samplerate;return t.box(t.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]),t.box(t.types.esds,t.esds(e)))},t.mp3=function(e){var r=e.samplerate;return t.box(t.types[".mp3"],new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]))},t.stsd=function(e){return"audio"===e.type?"mp3"===e.segmentCodec&&"mp3"===e.codec?t.box(t.types.stsd,t.STSD,t.mp3(e)):t.box(t.types.stsd,t.STSD,t.mp4a(e)):t.box(t.types.stsd,t.STSD,t.avc1(e))},t.tkhd=function(e){var r=e.id,n=e.duration*e.timescale,a=e.width,s=e.height,o=Math.floor(n/(i+1)),l=Math.floor(n%(i+1));return t.box(t.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,o>>24,o>>16&255,o>>8&255,255&o,l>>24,l>>16&255,l>>8&255,255&l,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,a>>8&255,255&a,0,0,s>>8&255,255&s,0,0]))},t.traf=function(e,r){var n=t.sdtp(e),a=e.id,s=Math.floor(r/(i+1)),o=Math.floor(r%(i+1));return t.box(t.types.traf,t.box(t.types.tfhd,new Uint8Array([0,0,0,0,a>>24,a>>16&255,a>>8&255,255&a])),t.box(t.types.tfdt,new Uint8Array([1,0,0,0,s>>24,s>>16&255,s>>8&255,255&s,o>>24,o>>16&255,o>>8&255,255&o])),t.trun(e,n.length+16+20+8+16+8+8),n)},t.trak=function(e){return e.duration=e.duration||4294967295,t.box(t.types.trak,t.tkhd(e),t.mdia(e))},t.trex=function(e){var r=e.id;return t.box(t.types.trex,new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},t.trun=function(e,r){var i,n,a,s,o,l,u=e.samples||[],d=u.length,c=12+16*d,h=new Uint8Array(c);for(r+=8+c,h.set(["video"===e.type?1:0,0,15,1,d>>>24&255,d>>>16&255,d>>>8&255,255&d,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0),i=0;i<d;i++)a=(n=u[i]).duration,s=n.size,o=n.flags,l=n.cts,h.set([a>>>24&255,a>>>16&255,a>>>8&255,255&a,s>>>24&255,s>>>16&255,s>>>8&255,255&s,o.isLeading<<2|o.dependsOn,o.isDependedOn<<6|o.hasRedundancy<<4|o.paddingValue<<1|o.isNonSync,61440&o.degradPrio,15&o.degradPrio,l>>>24&255,l>>>16&255,l>>>8&255,255&l],12+16*i);return t.box(t.types.trun,h)},t.initSegment=function(e){t.types||t.init();var r=t.moov(e),i=new Uint8Array(t.FTYP.byteLength+r.byteLength);return i.set(t.FTYP),i.set(r,t.FTYP.byteLength),i},t}();n.types=void 0,n.HDLR_TYPES=void 0,n.STTS=void 0,n.STSC=void 0,n.STCO=void 0,n.STSZ=void 0,n.VMHD=void 0,n.SMHD=void 0,n.STSD=void 0,n.FTYP=void 0,n.DINF=void 0;const a=n},"./src/remux/mp4-remuxer.ts":
/*!**********************************!*\
  !*** ./src/remux/mp4-remuxer.ts ***!
  \**********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>g,flushTextTrackMetadataCueSamples:()=>p,flushTextTrackUserdataCueSamples:()=>m,normalizePts:()=>v});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ./aac-helper */"./src/remux/aac-helper.ts"),a=r(/*! ./mp4-generator */"./src/remux/mp4-generator.ts"),s=r(/*! ../events */"./src/events.ts"),o=r(/*! ../errors */"./src/errors.ts"),l=r(/*! ../utils/logger */"./src/utils/logger.ts"),u=r(/*! ../types/loader */"./src/types/loader.ts"),d=r(/*! ../utils/timescale-conversion */"./src/utils/timescale-conversion.ts");function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}var h=null,f=null,g=function(){function t(t,e,r,i){if(void 0===i&&(i=""),this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.ISGenerated=!1,this._initPTS=void 0,this._initDTS=void 0,this.nextAvcDts=null,this.nextAudioPts=null,this.videoSampleDuration=null,this.isAudioContiguous=!1,this.isVideoContiguous=!1,this.observer=t,this.config=e,this.typeSupported=r,this.ISGenerated=!1,null===h){var n=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);h=n?parseInt(n[1]):0}if(null===f){var a=navigator.userAgent.match(/Safari\/(\d+)/i);f=a?parseInt(a[1]):0}}var e=t.prototype;return e.destroy=function(){},e.resetTimeStamp=function(t){l.logger.log("[mp4-remuxer]: initPTS & initDTS reset"),this._initPTS=this._initDTS=t},e.resetNextTimestamp=function(){l.logger.log("[mp4-remuxer]: reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1},e.resetInitSegment=function(){l.logger.log("[mp4-remuxer]: ISGenerated flag reset"),this.ISGenerated=!1},e.getVideoStartPts=function(t){var e=!1,r=t.reduce((function(t,r){var i=r.pts-t;return i<-4294967296?(e=!0,v(t,r.pts)):i>0?t:r.pts}),t[0].pts);return e&&l.logger.debug("PTS rollover detected"),r},e.remux=function(t,e,r,i,n,a,s,o){var d,c,h,f,g,y,E=n,T=n,S=t.pid>-1,b=e.pid>-1,D=e.samples.length,L=t.samples.length>0,A=s&&D>0||D>1;if((!S||L)&&(!b||A)||this.ISGenerated||s){this.ISGenerated||(h=this.generateIS(t,e,n));var R,k=this.isVideoContiguous,I=-1;if(A&&(I=function(t){for(var e=0;e<t.length;e++)if(t[e].key)return e;return-1}(e.samples),!k&&this.config.forceKeyFrameOnDiscontinuity))if(y=!0,I>0){l.logger.warn("[mp4-remuxer]: Dropped "+I+" out of "+D+" video samples due to a missing keyframe");var _=this.getVideoStartPts(e.samples);e.samples=e.samples.slice(I),e.dropped+=I,R=T+=(e.samples[0].pts-_)/e.inputTimeScale}else-1===I&&(l.logger.warn("[mp4-remuxer]: No keyframe found out of "+D+" video samples"),y=!1);if(this.ISGenerated){if(L&&A){var C=this.getVideoStartPts(e.samples),w=(v(t.samples[0].pts,C)-C)/e.inputTimeScale;E+=Math.max(0,w),T+=Math.max(0,-w)}if(L){if(t.samplerate||(l.logger.warn("[mp4-remuxer]: regenerate InitSegment as audio detected"),h=this.generateIS(t,e,n)),c=this.remuxAudio(t,E,this.isAudioContiguous,a,b||A||o===u.PlaylistLevelType.AUDIO?T:void 0),A){var O=c?c.endPTS-c.startPTS:0;e.inputTimeScale||(l.logger.warn("[mp4-remuxer]: regenerate InitSegment as video detected"),h=this.generateIS(t,e,n)),d=this.remuxVideo(e,T,k,O)}}else A&&(d=this.remuxVideo(e,T,k,0));d&&(d.firstKeyFrame=I,d.independent=-1!==I,d.firstKeyFramePTS=R)}}return this.ISGenerated&&(r.samples.length&&(g=p(r,n,this._initPTS,this._initDTS)),i.samples.length&&(f=m(i,n,this._initPTS))),{audio:c,video:d,initSegment:h,independent:y,text:f,id3:g}},e.generateIS=function(t,e,r){var n,s,o,l=t.samples,u=e.samples,d=this.typeSupported,c={},h=!(0,i.isFiniteNumber)(this._initPTS),f="audio/mp4";if(h&&(n=s=1/0),t.config&&l.length){switch(t.timescale=t.samplerate,t.segmentCodec){case"mp3":d.mpeg?(f="audio/mpeg",t.codec=""):d.mp3&&(t.codec="mp3")}c.audio={id:"audio",container:f,codec:t.codec,initSegment:"mp3"===t.segmentCodec&&d.mpeg?new Uint8Array(0):a.default.initSegment([t]),metadata:{channelCount:t.channelCount}},h&&(o=t.inputTimeScale,n=s=l[0].pts-Math.round(o*r))}if(e.sps&&e.pps&&u.length&&(e.timescale=e.inputTimeScale,c.video={id:"main",container:"video/mp4",codec:e.codec,initSegment:a.default.initSegment([e]),metadata:{width:e.width,height:e.height}},h)){o=e.inputTimeScale;var g=this.getVideoStartPts(u),p=Math.round(o*r);s=Math.min(s,v(u[0].dts,g)-p),n=Math.min(n,g-p)}if(Object.keys(c).length)return this.ISGenerated=!0,h&&(this._initPTS=n,this._initDTS=s),{tracks:c,initPTS:n,timescale:o}},e.remuxVideo=function(t,e,r,i){var n,u,g=t.inputTimeScale,p=t.samples,m=[],E=p.length,T=this._initPTS,S=this.nextAvcDts,b=8,D=this.videoSampleDuration,L=Number.POSITIVE_INFINITY,A=Number.NEGATIVE_INFINITY,R=!1;r&&null!==S||(S=e*g-(p[0].pts-v(p[0].dts,p[0].pts)));for(var k=0;k<E;k++){var I=p[k];I.pts=v(I.pts-T,S),I.dts=v(I.dts-T,S),I.dts<p[k>0?k-1:k].dts&&(R=!0)}R&&p.sort((function(t,e){var r=t.dts-e.dts,i=t.pts-e.pts;return r||i})),n=p[0].dts;var _=(u=p[p.length-1].dts)-n,C=_?Math.round(_/(E-1)):D||t.inputTimeScale/30;if(r){var w=n-S,O=w>C,x=w<-1;if((O||x)&&(O?l.logger.warn("AVC: "+(0,d.toMsFromMpegTsClock)(w,!0)+" ms ("+w+"dts) hole between fragments detected, filling it"):l.logger.warn("AVC: "+(0,d.toMsFromMpegTsClock)(-w,!0)+" ms ("+w+"dts) overlapping between fragments detected"),!x||S>p[0].pts)){n=S;var P=p[0].pts-w;p[0].dts=n,p[0].pts=P,l.logger.log("Video: First PTS/DTS adjusted: "+(0,d.toMsFromMpegTsClock)(P,!0)+"/"+(0,d.toMsFromMpegTsClock)(n,!0)+", delta: "+(0,d.toMsFromMpegTsClock)(w,!0)+" ms")}}n=Math.max(0,n);for(var F=0,M=0,N=0;N<E;N++){for(var U=p[N],B=U.units,G=B.length,K=0,H=0;H<G;H++)K+=B[H].data.length;M+=K,F+=G,U.length=K,U.dts=Math.max(U.dts,n),L=Math.min(U.pts,L),A=Math.max(U.pts,A)}u=p[E-1].dts;var j,V=M+4*F+8;try{j=new Uint8Array(V)}catch(t){return void this.observer.emit(s.Events.ERROR,s.Events.ERROR,{type:o.ErrorTypes.MUX_ERROR,details:o.ErrorDetails.REMUX_ALLOC_ERROR,fatal:!1,bytes:V,reason:"fail allocating video mdat "+V})}var W=new DataView(j.buffer);W.setUint32(0,V),j.set(a.default.types.mdat,4);for(var Y=!1,q=Number.POSITIVE_INFINITY,z=Number.POSITIVE_INFINITY,X=Number.NEGATIVE_INFINITY,Q=Number.NEGATIVE_INFINITY,$=0;$<E;$++){for(var J=p[$],Z=J.units,tt=0,et=0,rt=Z.length;et<rt;et++){var it=Z[et],nt=it.data,at=it.data.byteLength;W.setUint32(b,at),b+=4,j.set(nt,b),b+=at,tt+=4+at}var st=void 0;if($<E-1)D=p[$+1].dts-J.dts,st=p[$+1].pts-J.pts;else{var ot=this.config,lt=$>0?J.dts-p[$-1].dts:C;if(st=$>0?J.pts-p[$-1].pts:C,ot.stretchShortVideoTrack&&null!==this.nextAudioPts){var ut=Math.floor(ot.maxBufferHole*g),dt=(i?L+i*g:this.nextAudioPts)-J.pts;dt>ut?((D=dt-lt)<0?D=lt:Y=!0,l.logger.log("[mp4-remuxer]: It is approximately "+dt/90+" ms to the next segment; using duration "+D/90+" ms for the last video frame.")):D=lt}else D=lt}var ct=Math.round(J.pts-J.dts);q=Math.min(q,D),X=Math.max(X,D),z=Math.min(z,st),Q=Math.max(Q,st),m.push(new y(J.key,D,tt,ct))}if(m.length)if(h){if(h<70){var ht=m[0].flags;ht.dependsOn=2,ht.isNonSync=0}}else if(f&&Q-z<X-q&&C/X<.025&&0===m[0].cts){l.logger.warn("Found irregular gaps in sample duration. Using PTS instead of DTS to determine MP4 sample duration.");for(var ft=n,gt=0,vt=m.length;gt<vt;gt++){var pt=ft+m[gt].duration,mt=ft+m[gt].cts;if(gt<vt-1){var yt=pt+m[gt+1].cts;m[gt].duration=yt-mt}else m[gt].duration=gt?m[gt-1].duration:C;m[gt].cts=0,ft=pt}}console.assert(null!==D,"mp4SampleDuration must be computed"),D=Y||!D?C:D,this.nextAvcDts=S=u+D,this.videoSampleDuration=D,this.isVideoContiguous=!0;var Et={data1:a.default.moof(t.sequenceNumber++,n,c({},t,{samples:m})),data2:j,startPTS:L/g,endPTS:(A+D)/g,startDTS:n/g,endDTS:S/g,type:"video",hasAudio:!1,hasVideo:!0,nb:m.length,dropped:t.dropped};return t.samples=[],t.dropped=0,console.assert(j.length,"MDAT length must not be zero"),Et},e.remuxAudio=function(t,e,r,i,u){var d=t.inputTimeScale,h=d/(t.samplerate?t.samplerate:d),f="aac"===t.segmentCodec?1024:1152,g=f*h,p=this._initPTS,m="mp3"===t.segmentCodec&&this.typeSupported.mpeg,E=[],T=void 0!==u,S=t.samples,b=m?0:8,D=this.nextAudioPts||-1,L=e*d;if(this.isAudioContiguous=r=r||S.length&&D>0&&(i&&Math.abs(L-D)<9e3||Math.abs(v(S[0].pts-p,L)-D)<20*g),S.forEach((function(t){t.pts=v(t.pts-p,L)})),!r||D<0){if(!(S=S.filter((function(t){return t.pts>=0}))).length)return;D=0===u?0:i&&!T?Math.max(0,L):S[0].pts}if("aac"===t.segmentCodec)for(var A=this.config.maxAudioFramesDrift,R=0,k=D;R<S.length;R++){var I=S[R],_=I.pts,C=_-k,w=Math.abs(1e3*C/d);if(C<=-A*g&&T)0===R&&(l.logger.warn("Audio frame @ "+(_/d).toFixed(3)+"s overlaps nextAudioPts by "+Math.round(1e3*C/d)+" ms."),this.nextAudioPts=D=k=_);else if(C>=A*g&&w<1e4&&T){var O=Math.round(C/g);(k=_-O*g)<0&&(O--,k+=g),0===R&&(this.nextAudioPts=D=k),l.logger.warn("[mp4-remuxer]: Injecting "+O+" audio frame @ "+(k/d).toFixed(3)+"s due to "+Math.round(1e3*C/d)+" ms gap.");for(var x=0;x<O;x++){var P=Math.max(k,0),F=n.default.getSilentFrame(t.manifestCodec||t.codec,t.channelCount);F||(l.logger.log("[mp4-remuxer]: Unable to get silent frame for given audio codec; duplicating last frame instead."),F=I.unit.subarray()),S.splice(R,0,{unit:F,pts:P}),k+=g,R++}}I.pts=k,k+=g}for(var M,N=null,U=null,B=0,G=S.length;G--;)B+=S[G].unit.byteLength;for(var K=0,H=S.length;K<H;K++){var j=S[K],V=j.unit,W=j.pts;if(null!==U)E[K-1].duration=Math.round((W-U)/h);else{if(r&&"aac"===t.segmentCodec&&(W=D),N=W,!(B>0))return;B+=b;try{M=new Uint8Array(B)}catch(t){return void this.observer.emit(s.Events.ERROR,s.Events.ERROR,{type:o.ErrorTypes.MUX_ERROR,details:o.ErrorDetails.REMUX_ALLOC_ERROR,fatal:!1,bytes:B,reason:"fail allocating audio mdat "+B})}m||(new DataView(M.buffer).setUint32(0,B),M.set(a.default.types.mdat,4))}M.set(V,b);var Y=V.byteLength;b+=Y,E.push(new y(!0,f,Y,0)),U=W}var q=E.length;if(q){var z=E[E.length-1];this.nextAudioPts=D=U+h*z.duration;var X=m?new Uint8Array(0):a.default.moof(t.sequenceNumber++,N/h,c({},t,{samples:E}));t.samples=[];var Q=N/d,$=D/d,J={data1:X,data2:M,startPTS:Q,endPTS:$,startDTS:Q,endDTS:$,type:"audio",hasAudio:!0,hasVideo:!1,nb:q};return this.isAudioContiguous=!0,console.assert(M.length,"MDAT length must not be zero"),J}},e.remuxEmptyAudio=function(t,e,r,i){var a=t.inputTimeScale,s=a/(t.samplerate?t.samplerate:a),o=this.nextAudioPts,u=(null!==o?o:i.startDTS*a)+this._initDTS,d=i.endDTS*a+this._initDTS,c=1024*s,h=Math.ceil((d-u)/c),f=n.default.getSilentFrame(t.manifestCodec||t.codec,t.channelCount);if(l.logger.warn("[mp4-remuxer]: remux empty Audio"),f){for(var g=[],v=0;v<h;v++){var p=u+v*c;g.push({unit:f,pts:p,dts:p})}return t.samples=g,this.remuxAudio(t,e,r,!1)}l.logger.trace("[mp4-remuxer]: Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec")},t}();function v(t,e){var r;if(null===e)return t;for(r=e<t?-8589934592:8589934592;Math.abs(t-e)>4294967296;)t+=r;return t}function p(t,e,r,i){var n=t.samples.length;if(n){for(var a=t.inputTimeScale,s=0;s<n;s++){var o=t.samples[s];o.pts=v(o.pts-r,e*a)/a,o.dts=v(o.dts-i,e*a)/a}var l=t.samples;return t.samples=[],{samples:l}}}function m(t,e,r){var i=t.samples.length;if(i){for(var n=t.inputTimeScale,a=0;a<i;a++){var s=t.samples[a];s.pts=v(s.pts-r,e*n)/n}t.samples.sort((function(t,e){return t.pts-e.pts}));var o=t.samples;return t.samples=[],{samples:o}}}var y=function(t,e,r,i){this.size=void 0,this.duration=void 0,this.cts=void 0,this.flags=void 0,this.duration=e,this.size=r,this.cts=i,this.flags=new E(t)},E=function(t){this.isLeading=0,this.isDependedOn=0,this.hasRedundancy=0,this.degradPrio=0,this.dependsOn=1,this.isNonSync=1,this.dependsOn=t?2:1,this.isNonSync=t?0:1}},"./src/remux/passthrough-remuxer.ts":
/*!******************************************!*\
  !*** ./src/remux/passthrough-remuxer.ts ***!
  \******************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>u});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ./mp4-remuxer */"./src/remux/mp4-remuxer.ts"),a=r(/*! ../utils/mp4-tools */"./src/utils/mp4-tools.ts"),s=r(/*! ../loader/fragment */"./src/loader/fragment.ts"),o=r(/*! ../utils/logger */"./src/utils/logger.ts");function l(t,e){var r=null==t?void 0:t.codec;return r&&r.length>4?r:"hvc1"===r||"hev1"===r?"hvc1.1.c.L120.90":"av01"===r?"av01.0.04M.08":"avc1"===r||e===s.ElementaryStreamTypes.VIDEO?"avc1.42e01e":"mp4a.40.5"}const u=function(){function t(){this.emitInitSegment=!1,this.audioCodec=void 0,this.videoCodec=void 0,this.initData=void 0,this.initPTS=void 0,this.initTracks=void 0,this.lastEndTime=null}var e=t.prototype;return e.destroy=function(){},e.resetTimeStamp=function(t){this.initPTS=t,this.lastEndTime=null},e.resetNextTimestamp=function(){this.lastEndTime=null},e.resetInitSegment=function(t,e,r){this.audioCodec=e,this.videoCodec=r,this.generateInitSegment(t),this.emitInitSegment=!0},e.generateInitSegment=function(t){var e=this.audioCodec,r=this.videoCodec;if(!t||!t.byteLength)return this.initTracks=void 0,void(this.initData=void 0);var i=this.initData=(0,a.parseInitSegment)(t);e||(e=l(i.audio,s.ElementaryStreamTypes.AUDIO)),r||(r=l(i.video,s.ElementaryStreamTypes.VIDEO));var n={};i.audio&&i.video?n.audiovideo={container:"video/mp4",codec:e+","+r,initSegment:t,id:"main"}:i.audio?n.audio={container:"audio/mp4",codec:e,initSegment:t,id:"audio"}:i.video?n.video={container:"video/mp4",codec:r,initSegment:t,id:"main"}:o.logger.warn("[passthrough-remuxer.ts]: initSegment does not contain moov or trak boxes."),this.initTracks=n},e.remux=function(t,e,r,s,l){var u,d=this.initPTS,c=this.lastEndTime,h={audio:void 0,video:void 0,text:s,id3:r,initSegment:void 0};(0,i.isFiniteNumber)(c)||(c=this.lastEndTime=l||0);var f=e.samples;if(!f||!f.length)return h;var g={initPTS:void 0,timescale:1},v=this.initData;if(v&&v.length||(this.generateInitSegment(f),v=this.initData),!v||!v.length)return o.logger.warn("[passthrough-remuxer.ts]: Failed to generate initSegment."),h;this.emitInitSegment&&(g.tracks=this.initTracks,this.emitInitSegment=!1);var p=(0,a.getStartDTS)(v,f);(0,i.isFiniteNumber)(d)||(this.initPTS=g.initPTS=d=p-l);var m=(0,a.getDuration)(f,v),y=t?p-d:c,E=y+m;(0,a.offsetStartDTS)(v,f,d),m>0?this.lastEndTime=E:(o.logger.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());var T=!!v.audio,S=!!v.video,b="";T&&(b+="audio"),S&&(b+="video");var D={data1:f,startPTS:y,startDTS:y,endPTS:E,endDTS:E,type:b,hasAudio:T,hasVideo:S,nb:1,dropped:0};h.audio="audio"===D.type?D:void 0,h.video="audio"!==D.type?D:void 0,h.initSegment=g;var L=null!=(u=this.initPTS)?u:0;return h.id3=(0,n.flushTextTrackMetadataCueSamples)(r,l,L,L),s.samples.length&&(h.text=(0,n.flushTextTrackUserdataCueSamples)(s,l,L)),h},t}()},"./src/task-loop.ts":
/*!**************************!*\
  !*** ./src/task-loop.ts ***!
  \**************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var i=function(){function t(){this._boundTick=void 0,this._tickTimer=null,this._tickInterval=null,this._tickCallCount=0,this._boundTick=this.tick.bind(this)}var e=t.prototype;return e.destroy=function(){this.onHandlerDestroying(),this.onHandlerDestroyed()},e.onHandlerDestroying=function(){this.clearNextTick(),this.clearInterval()},e.onHandlerDestroyed=function(){},e.hasInterval=function(){return!!this._tickInterval},e.hasNextTick=function(){return!!this._tickTimer},e.setInterval=function(t){return!this._tickInterval&&(this._tickInterval=self.setInterval(this._boundTick,t),!0)},e.clearInterval=function(){return!!this._tickInterval&&(self.clearInterval(this._tickInterval),this._tickInterval=null,!0)},e.clearNextTick=function(){return!!this._tickTimer&&(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0)},e.tick=function(){this._tickCallCount++,1===this._tickCallCount&&(this.doTick(),this._tickCallCount>1&&this.tickImmediate(),this._tickCallCount=0)},e.tickImmediate=function(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)},e.doTick=function(){},t}()},"./src/types/cmcd.ts":
/*!***************************!*\
  !*** ./src/types/cmcd.ts ***!
  \***************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{CMCDObjectType:()=>i,CMCDStreamType:()=>a,CMCDStreamingFormat:()=>n,CMCDVersion:()=>s});var i,n,a,s=1;!function(t){t.MANIFEST="m",t.AUDIO="a",t.VIDEO="v",t.MUXED="av",t.INIT="i",t.CAPTION="c",t.TIMED_TEXT="tt",t.KEY="k",t.OTHER="o"}(i||(i={})),function(t){t.DASH="d",t.HLS="h",t.SMOOTH="s",t.OTHER="o"}(n||(n={})),function(t){t.VOD="v",t.LIVE="l"}(a||(a={}))},"./src/types/demuxer.ts":
/*!******************************!*\
  !*** ./src/types/demuxer.ts ***!
  \******************************/(t,e,r)=>{"use strict";var i;r.r(e),r.d(e,{MetadataSchema:()=>i}),function(t){t.audioId3="org.id3",t.dateRange="com.apple.quicktime.HLS",t.emsg="https://aomedia.org/emsg/ID3"}(i||(i={}))},"./src/types/level.ts":
/*!****************************!*\
  !*** ./src/types/level.ts ***!
  \****************************/(t,e,r)=>{"use strict";function i(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}var n;function a(t,e){var r=t.canSkipUntil,i=t.canSkipDateRanges,a=t.endSN;return r&&(void 0!==e?e-a:0)<r?i?n.v2:n.Yes:n.No}r.r(e),r.d(e,{HlsSkip:()=>n,HlsUrlParameters:()=>s,Level:()=>o,getSkipValue:()=>a}),function(t){t.No="",t.Yes="YES",t.v2="v2"}(n||(n={}));var s=function(){function t(t,e,r){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=t,this.part=e,this.skip=r}return t.prototype.addDirectives=function(t){var e=new self.URL(t);return void 0!==this.msn&&e.searchParams.set("_HLS_msn",this.msn.toString()),void 0!==this.part&&e.searchParams.set("_HLS_part",this.part.toString()),this.skip&&e.searchParams.set("_HLS_skip",this.skip),e.toString()},t}(),o=function(){function t(t){this.attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.videoCodec=void 0,this.width=void 0,this.unknownCodecs=void 0,this.audioGroupIds=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.textGroupIds=void 0,this.url=void 0,this._urlId=0,this.url=[t.url],this.attrs=t.attrs,this.bitrate=t.bitrate,t.details&&(this.details=t.details),this.id=t.id||0,this.name=t.name,this.width=t.width||0,this.height=t.height||0,this.audioCodec=t.audioCodec,this.videoCodec=t.videoCodec,this.unknownCodecs=t.unknownCodecs,this.codecSet=[t.videoCodec,t.audioCodec].filter((function(t){return t})).join(",").replace(/\.[^.,]+/g,"")}var e,r,n;return e=t,(r=[{key:"maxBitrate",get:function(){return Math.max(this.realBitrate,this.bitrate)}},{key:"uri",get:function(){return this.url[this._urlId]||""}},{key:"urlId",get:function(){return this._urlId},set:function(t){var e=t%this.url.length;this._urlId!==e&&(this.details=void 0,this._urlId=e)}}])&&i(e.prototype,r),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}()},"./src/types/loader.ts":
/*!*****************************!*\
  !*** ./src/types/loader.ts ***!
  \*****************************/(t,e,r)=>{"use strict";var i,n;r.r(e),r.d(e,{PlaylistContextType:()=>i,PlaylistLevelType:()=>n}),function(t){t.MANIFEST="manifest",t.LEVEL="level",t.AUDIO_TRACK="audioTrack",t.SUBTITLE_TRACK="subtitleTrack"}(i||(i={})),function(t){t.MAIN="main",t.AUDIO="audio",t.SUBTITLE="subtitle"}(n||(n={}))},"./src/types/transmuxer.ts":
/*!*********************************!*\
  !*** ./src/types/transmuxer.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{ChunkMetadata:()=>i});var i=function(t,e,r,i,n,a){void 0===i&&(i=0),void 0===n&&(n=-1),void 0===a&&(a=!1),this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing={start:0,executeStart:0,executeEnd:0,end:0},this.buffering={audio:{start:0,executeStart:0,executeEnd:0,end:0},video:{start:0,executeStart:0,executeEnd:0,end:0},audiovideo:{start:0,executeStart:0,executeEnd:0,end:0}},this.level=t,this.sn=e,this.id=r,this.size=i,this.part=n,this.partial=a}},"./src/utils/attr-list.ts":
/*!********************************!*\
  !*** ./src/utils/attr-list.ts ***!
  \********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{AttrList:()=>a});var i=/^(\d+)x(\d+)$/,n=/\s*(.+?)\s*=((?:\".*?\")|.*?)(?:,|$)/g,a=function(){function t(e){for(var r in"string"==typeof e&&(e=t.parseAttrList(e)),e)e.hasOwnProperty(r)&&(this[r]=e[r])}var e=t.prototype;return e.decimalInteger=function(t){var e=parseInt(this[t],10);return e>Number.MAX_SAFE_INTEGER?1/0:e},e.hexadecimalInteger=function(t){if(this[t]){var e=(this[t]||"0x").slice(2);e=(1&e.length?"0":"")+e;for(var r=new Uint8Array(e.length/2),i=0;i<e.length/2;i++)r[i]=parseInt(e.slice(2*i,2*i+2),16);return r}return null},e.hexadecimalIntegerAsNumber=function(t){var e=parseInt(this[t],16);return e>Number.MAX_SAFE_INTEGER?1/0:e},e.decimalFloatingPoint=function(t){return parseFloat(this[t])},e.optionalFloat=function(t,e){var r=this[t];return r?parseFloat(r):e},e.enumeratedString=function(t){return this[t]},e.bool=function(t){return"YES"===this[t]},e.decimalResolution=function(t){var e=i.exec(this[t]);if(null!==e)return{width:parseInt(e[1],10),height:parseInt(e[2],10)}},t.parseAttrList=function(t){var e,r={};for(n.lastIndex=0;null!==(e=n.exec(t));){var i=e[2];0===i.indexOf('"')&&i.lastIndexOf('"')===i.length-1&&(i=i.slice(1,-1)),r[e[1]]=i}return r},t}()},"./src/utils/binary-search.ts":
/*!************************************!*\
  !*** ./src/utils/binary-search.ts ***!
  \************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});const i={search:function(t,e){for(var r=0,i=t.length-1,n=null,a=null;r<=i;){var s=e(a=t[n=(r+i)/2|0]);if(s>0)r=n+1;else{if(!(s<0))return a;i=n-1}}return null}}},"./src/utils/buffer-helper.ts":
/*!************************************!*\
  !*** ./src/utils/buffer-helper.ts ***!
  \************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{BufferHelper:()=>a});var i=r(/*! ./logger */"./src/utils/logger.ts"),n={length:0,start:function(){return 0},end:function(){return 0}},a=function(){function t(){}return t.isBuffered=function(e,r){try{if(e)for(var i=t.getBuffered(e),n=0;n<i.length;n++)if(r>=i.start(n)&&r<=i.end(n))return!0}catch(t){}return!1},t.bufferInfo=function(e,r,i){try{if(e){var n,a=t.getBuffered(e),s=[];for(n=0;n<a.length;n++)s.push({start:a.start(n),end:a.end(n)});return this.bufferedInfo(s,r,i)}}catch(t){}return{len:0,start:r,end:r,nextStart:void 0}},t.bufferedInfo=function(t,e,r){e=Math.max(0,e),t.sort((function(t,e){var r=t.start-e.start;return r||e.end-t.end}));var i=[];if(r)for(var n=0;n<t.length;n++){var a=i.length;if(a){var s=i[a-1].end;t[n].start-s<r?t[n].end>s&&(i[a-1].end=t[n].end):i.push(t[n])}else i.push(t[n])}else i=t;for(var o,l=0,u=e,d=e,c=0;c<i.length;c++){var h=i[c].start,f=i[c].end;if(e+r>=h&&e<f)u=h,l=(d=f)-e;else if(e+r<h){o=h;break}}return{len:l,start:u||0,end:d||0,nextStart:o}},t.getBuffered=function(t){try{return t.buffered}catch(t){return i.logger.log("failed to get media.buffered",t),n}},t}()},"./src/utils/cea-608-parser.ts":
/*!*************************************!*\
  !*** ./src/utils/cea-608-parser.ts ***!
  \*************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{CaptionScreen:()=>m,Row:()=>p,default:()=>S});var i,n=r(/*! ../utils/logger */"./src/utils/logger.ts"),a={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},s=function(t){var e=t;return a.hasOwnProperty(t)&&(e=a[t]),String.fromCharCode(e)},o={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},l={17:2,18:4,21:6,22:8,23:10,19:13,20:15},u={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},d={25:2,26:4,29:6,30:8,31:10,27:13,28:15},c=["white","green","blue","cyan","red","yellow","magenta","black","transparent"];!function(t){t[t.ERROR=0]="ERROR",t[t.TEXT=1]="TEXT",t[t.WARNING=2]="WARNING",t[t.INFO=2]="INFO",t[t.DEBUG=3]="DEBUG",t[t.DATA=3]="DATA"}(i||(i={}));var h=function(){function t(){this.time=null,this.verboseLevel=i.ERROR}return t.prototype.log=function(t,e){this.verboseLevel>=t&&n.logger.log(this.time+" ["+t+"] "+e)},t}(),f=function(t){for(var e=[],r=0;r<t.length;r++)e.push(t[r].toString(16));return e},g=function(){function t(t,e,r,i,n){this.foreground=void 0,this.underline=void 0,this.italics=void 0,this.background=void 0,this.flash=void 0,this.foreground=t||"white",this.underline=e||!1,this.italics=r||!1,this.background=i||"black",this.flash=n||!1}var e=t.prototype;return e.reset=function(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1},e.setStyles=function(t){for(var e=["foreground","underline","italics","background","flash"],r=0;r<e.length;r++){var i=e[r];t.hasOwnProperty(i)&&(this[i]=t[i])}},e.isDefault=function(){return"white"===this.foreground&&!this.underline&&!this.italics&&"black"===this.background&&!this.flash},e.equals=function(t){return this.foreground===t.foreground&&this.underline===t.underline&&this.italics===t.italics&&this.background===t.background&&this.flash===t.flash},e.copy=function(t){this.foreground=t.foreground,this.underline=t.underline,this.italics=t.italics,this.background=t.background,this.flash=t.flash},e.toString=function(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash},t}(),v=function(){function t(t,e,r,i,n,a){this.uchar=void 0,this.penState=void 0,this.uchar=t||" ",this.penState=new g(e,r,i,n,a)}var e=t.prototype;return e.reset=function(){this.uchar=" ",this.penState.reset()},e.setChar=function(t,e){this.uchar=t,this.penState.copy(e)},e.setPenState=function(t){this.penState.copy(t)},e.equals=function(t){return this.uchar===t.uchar&&this.penState.equals(t.penState)},e.copy=function(t){this.uchar=t.uchar,this.penState.copy(t.penState)},e.isEmpty=function(){return" "===this.uchar&&this.penState.isDefault()},t}(),p=function(){function t(t){this.chars=void 0,this.pos=void 0,this.currPenState=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chars=[];for(var e=0;e<100;e++)this.chars.push(new v);this.logger=t,this.pos=0,this.currPenState=new g}var e=t.prototype;return e.equals=function(t){for(var e=!0,r=0;r<100;r++)if(!this.chars[r].equals(t.chars[r])){e=!1;break}return e},e.copy=function(t){for(var e=0;e<100;e++)this.chars[e].copy(t.chars[e])},e.isEmpty=function(){for(var t=!0,e=0;e<100;e++)if(!this.chars[e].isEmpty()){t=!1;break}return t},e.setCursor=function(t){this.pos!==t&&(this.pos=t),this.pos<0?(this.logger.log(i.DEBUG,"Negative cursor position "+this.pos),this.pos=0):this.pos>100&&(this.logger.log(i.DEBUG,"Too large cursor position "+this.pos),this.pos=100)},e.moveCursor=function(t){var e=this.pos+t;if(t>1)for(var r=this.pos+1;r<e+1;r++)this.chars[r].setPenState(this.currPenState);this.setCursor(e)},e.backSpace=function(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)},e.insertChar=function(t){t>=144&&this.backSpace();var e=s(t);this.pos>=100?this.logger.log(i.ERROR,"Cannot insert "+t.toString(16)+" ("+e+") at position "+this.pos+". Skipping it!"):(this.chars[this.pos].setChar(e,this.currPenState),this.moveCursor(1))},e.clearFromPos=function(t){var e;for(e=t;e<100;e++)this.chars[e].reset()},e.clear=function(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()},e.clearToEndOfRow=function(){this.clearFromPos(this.pos)},e.getTextString=function(){for(var t=[],e=!0,r=0;r<100;r++){var i=this.chars[r].uchar;" "!==i&&(e=!1),t.push(i)}return e?"":t.join("")},e.setPenStyles=function(t){this.currPenState.setStyles(t),this.chars[this.pos].setPenState(this.currPenState)},t}(),m=function(){function t(t){this.rows=void 0,this.currRow=void 0,this.nrRollUpRows=void 0,this.lastOutputScreen=void 0,this.logger=void 0,this.rows=[];for(var e=0;e<15;e++)this.rows.push(new p(t));this.logger=t,this.currRow=14,this.nrRollUpRows=null,this.lastOutputScreen=null,this.reset()}var e=t.prototype;return e.reset=function(){for(var t=0;t<15;t++)this.rows[t].clear();this.currRow=14},e.equals=function(t){for(var e=!0,r=0;r<15;r++)if(!this.rows[r].equals(t.rows[r])){e=!1;break}return e},e.copy=function(t){for(var e=0;e<15;e++)this.rows[e].copy(t.rows[e])},e.isEmpty=function(){for(var t=!0,e=0;e<15;e++)if(!this.rows[e].isEmpty()){t=!1;break}return t},e.backSpace=function(){this.rows[this.currRow].backSpace()},e.clearToEndOfRow=function(){this.rows[this.currRow].clearToEndOfRow()},e.insertChar=function(t){this.rows[this.currRow].insertChar(t)},e.setPen=function(t){this.rows[this.currRow].setPenStyles(t)},e.moveCursor=function(t){this.rows[this.currRow].moveCursor(t)},e.setCursor=function(t){this.logger.log(i.INFO,"setCursor: "+t),this.rows[this.currRow].setCursor(t)},e.setPAC=function(t){this.logger.log(i.INFO,"pacData = "+JSON.stringify(t));var e=t.row-1;if(this.nrRollUpRows&&e<this.nrRollUpRows-1&&(e=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==e){for(var r=0;r<15;r++)this.rows[r].clear();var n=this.currRow+1-this.nrRollUpRows,a=this.lastOutputScreen;if(a){var s=a.rows[n].cueStartTime,o=this.logger.time;if(s&&null!==o&&s<o)for(var l=0;l<this.nrRollUpRows;l++)this.rows[e-this.nrRollUpRows+l+1].copy(a.rows[n+l])}}this.currRow=e;var u=this.rows[this.currRow];if(null!==t.indent){var d=t.indent,c=Math.max(d-1,0);u.setCursor(t.indent),t.color=u.chars[c].penState.foreground}var h={foreground:t.color,underline:t.underline,italics:t.italics,background:"black",flash:!1};this.setPen(h)},e.setBkgData=function(t){this.logger.log(i.INFO,"bkgData = "+JSON.stringify(t)),this.backSpace(),this.setPen(t),this.insertChar(32)},e.setRollUpRows=function(t){this.nrRollUpRows=t},e.rollUp=function(){if(null!==this.nrRollUpRows){this.logger.log(i.TEXT,this.getDisplayText());var t=this.currRow+1-this.nrRollUpRows,e=this.rows.splice(t,1)[0];e.clear(),this.rows.splice(this.currRow,0,e),this.logger.log(i.INFO,"Rolling up")}else this.logger.log(i.DEBUG,"roll_up but nrRollUpRows not set yet")},e.getDisplayText=function(t){t=t||!1;for(var e=[],r="",i=-1,n=0;n<15;n++){var a=this.rows[n].getTextString();a&&(i=n+1,t?e.push("Row "+i+": '"+a+"'"):e.push(a.trim()))}return e.length>0&&(r=t?"["+e.join(" | ")+"]":e.join("\n")),r},e.getTextAndFormat=function(){return this.rows},t}(),y=function(){function t(t,e,r){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chNr=t,this.outputFilter=e,this.mode=null,this.verbose=0,this.displayedMemory=new m(r),this.nonDisplayedMemory=new m(r),this.lastOutputScreen=new m(r),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.logger=r}var e=t.prototype;return e.reset=function(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.outputFilter.reset(),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null},e.getHandler=function(){return this.outputFilter},e.setHandler=function(t){this.outputFilter=t},e.setPAC=function(t){this.writeScreen.setPAC(t)},e.setBkgData=function(t){this.writeScreen.setBkgData(t)},e.setMode=function(t){t!==this.mode&&(this.mode=t,this.logger.log(i.INFO,"MODE="+t),"MODE_POP-ON"===this.mode?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),"MODE_ROLL-UP"!==this.mode&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=t)},e.insertChars=function(t){for(var e=0;e<t.length;e++)this.writeScreen.insertChar(t[e]);var r=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";this.logger.log(i.INFO,r+": "+this.writeScreen.getDisplayText(!0)),"MODE_PAINT-ON"!==this.mode&&"MODE_ROLL-UP"!==this.mode||(this.logger.log(i.TEXT,"DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())},e.ccRCL=function(){this.logger.log(i.INFO,"RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")},e.ccBS=function(){this.logger.log(i.INFO,"BS - BackSpace"),"MODE_TEXT"!==this.mode&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())},e.ccAOF=function(){},e.ccAON=function(){},e.ccDER=function(){this.logger.log(i.INFO,"DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()},e.ccRU=function(t){this.logger.log(i.INFO,"RU("+t+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(t)},e.ccFON=function(){this.logger.log(i.INFO,"FON - Flash On"),this.writeScreen.setPen({flash:!0})},e.ccRDC=function(){this.logger.log(i.INFO,"RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")},e.ccTR=function(){this.logger.log(i.INFO,"TR"),this.setMode("MODE_TEXT")},e.ccRTD=function(){this.logger.log(i.INFO,"RTD"),this.setMode("MODE_TEXT")},e.ccEDM=function(){this.logger.log(i.INFO,"EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)},e.ccCR=function(){this.logger.log(i.INFO,"CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)},e.ccENM=function(){this.logger.log(i.INFO,"ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()},e.ccEOC=function(){if(this.logger.log(i.INFO,"EOC - End Of Caption"),"MODE_POP-ON"===this.mode){var t=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=t,this.writeScreen=this.nonDisplayedMemory,this.logger.log(i.TEXT,"DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate(!0)},e.ccTO=function(t){this.logger.log(i.INFO,"TO("+t+") - Tab Offset"),this.writeScreen.moveCursor(t)},e.ccMIDROW=function(t){var e={flash:!1};if(e.underline=t%2==1,e.italics=t>=46,e.italics)e.foreground="white";else{var r=Math.floor(t/2)-16;e.foreground=["white","green","blue","cyan","red","yellow","magenta"][r]}this.logger.log(i.INFO,"MIDROW: "+JSON.stringify(e)),this.writeScreen.setPen(e)},e.outputDataUpdate=function(t){void 0===t&&(t=!1);var e=this.logger.time;null!==e&&this.outputFilter&&(null!==this.cueStartTime||this.displayedMemory.isEmpty()?this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,e,this.lastOutputScreen),t&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:e):this.cueStartTime=e,this.lastOutputScreen.copy(this.displayedMemory))},e.cueSplitAtTime=function(t){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,t,this.displayedMemory),this.cueStartTime=t))},t}();function E(t,e,r){r.a=t,r.b=e}function T(t,e,r){return r.a===t&&r.b===e}const S=function(){function t(t,e,r){this.channels=void 0,this.currentChannel=0,this.cmdHistory=void 0,this.logger=void 0;var i=new h;this.channels=[null,new y(t,e,i),new y(t+1,r,i)],this.cmdHistory={a:null,b:null},this.logger=i}var e=t.prototype;return e.getHandler=function(t){return this.channels[t].getHandler()},e.setHandler=function(t,e){this.channels[t].setHandler(e)},e.addData=function(t,e){var r,n,a,s=!1;this.logger.time=t;for(var o=0;o<e.length;o+=2)if(n=127&e[o],a=127&e[o+1],0!==n||0!==a){if(this.logger.log(i.DATA,"["+f([e[o],e[o+1]])+"] -> ("+f([n,a])+")"),(r=this.parseCmd(n,a))||(r=this.parseMidrow(n,a)),r||(r=this.parsePAC(n,a)),r||(r=this.parseBackgroundAttributes(n,a)),!r&&(s=this.parseChars(n,a))){var l=this.currentChannel;l&&l>0?this.channels[l].insertChars(s):this.logger.log(i.WARNING,"No channel found yet. TEXT-MODE?")}r||s||this.logger.log(i.WARNING,"Couldn't parse cleaned data "+f([n,a])+" orig: "+f([e[o],e[o+1]]))}},e.parseCmd=function(t,e){var r=this.cmdHistory;if(!((20===t||28===t||21===t||29===t)&&e>=32&&e<=47||(23===t||31===t)&&e>=33&&e<=35))return!1;if(T(t,e,r))return E(null,null,r),this.logger.log(i.DEBUG,"Repeated command ("+f([t,e])+") is dropped"),!0;var n=20===t||21===t||23===t?1:2,a=this.channels[n];return 20===t||21===t||28===t||29===t?32===e?a.ccRCL():33===e?a.ccBS():34===e?a.ccAOF():35===e?a.ccAON():36===e?a.ccDER():37===e?a.ccRU(2):38===e?a.ccRU(3):39===e?a.ccRU(4):40===e?a.ccFON():41===e?a.ccRDC():42===e?a.ccTR():43===e?a.ccRTD():44===e?a.ccEDM():45===e?a.ccCR():46===e?a.ccENM():47===e&&a.ccEOC():a.ccTO(e-32),E(t,e,r),this.currentChannel=n,!0},e.parseMidrow=function(t,e){var r=0;if((17===t||25===t)&&e>=32&&e<=47){if((r=17===t?1:2)!==this.currentChannel)return this.logger.log(i.ERROR,"Mismatch channel in midrow parsing"),!1;var n=this.channels[r];return!!n&&(n.ccMIDROW(e),this.logger.log(i.DEBUG,"MIDROW ("+f([t,e])+")"),!0)}return!1},e.parsePAC=function(t,e){var r,i=this.cmdHistory;if(!((t>=17&&t<=23||t>=25&&t<=31)&&e>=64&&e<=127||(16===t||24===t)&&e>=64&&e<=95))return!1;if(T(t,e,i))return E(null,null,i),!0;var n=t<=23?1:2;r=e>=64&&e<=95?1===n?o[t]:u[t]:1===n?l[t]:d[t];var a=this.channels[n];return!!a&&(a.setPAC(this.interpretPAC(r,e)),E(t,e,i),this.currentChannel=n,!0)},e.interpretPAC=function(t,e){var r,i={color:null,italics:!1,indent:null,underline:!1,row:t};return r=e>95?e-96:e-64,i.underline=1==(1&r),r<=13?i.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(r/2)]:r<=15?(i.italics=!0,i.color="white"):i.indent=4*Math.floor((r-16)/2),i},e.parseChars=function(t,e){var r,n,a=null,o=null;if(t>=25?(r=2,o=t-8):(r=1,o=t),o>=17&&o<=19?(n=17===o?e+80:18===o?e+112:e+144,this.logger.log(i.INFO,"Special char '"+s(n)+"' in channel "+r),a=[n]):t>=32&&t<=127&&(a=0===e?[t]:[t,e]),a){var l=f(a);this.logger.log(i.DEBUG,"Char codes =  "+l.join(",")),E(t,e,this.cmdHistory)}return a},e.parseBackgroundAttributes=function(t,e){var r;if(!((16===t||24===t)&&e>=32&&e<=47||(23===t||31===t)&&e>=45&&e<=47))return!1;var i={};16===t||24===t?(r=Math.floor((e-32)/2),i.background=c[r],e%2==1&&(i.background=i.background+"_semi")):45===e?i.background="transparent":(i.foreground="black",47===e&&(i.underline=!0));var n=t<=23?1:2;return this.channels[n].setBkgData(i),E(t,e,this.cmdHistory),!0},e.reset=function(){for(var t=0;t<Object.keys(this.channels).length;t++){var e=this.channels[t];e&&e.reset()}this.cmdHistory={a:null,b:null}},e.cueSplitAtTime=function(t){for(var e=0;e<this.channels.length;e++){var r=this.channels[e];r&&r.cueSplitAtTime(t)}},t}()},"./src/utils/codecs.ts":
/*!*****************************!*\
  !*** ./src/utils/codecs.ts ***!
  \*****************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{isCodecSupportedInMp4:()=>a,isCodecType:()=>n});var i={audio:{a3ds:!0,"ac-3":!0,"ac-4":!0,alac:!0,alaw:!0,dra1:!0,"dts+":!0,"dts-":!0,dtsc:!0,dtse:!0,dtsh:!0,"ec-3":!0,enca:!0,g719:!0,g726:!0,m4ae:!0,mha1:!0,mha2:!0,mhm1:!0,mhm2:!0,mlpa:!0,mp4a:!0,"raw ":!0,Opus:!0,opus:!0,samr:!0,sawb:!0,sawp:!0,sevc:!0,sqcp:!0,ssmv:!0,twos:!0,ulaw:!0},video:{avc1:!0,avc2:!0,avc3:!0,avc4:!0,avcp:!0,av01:!0,drac:!0,dva1:!0,dvav:!0,dvh1:!0,dvhe:!0,encv:!0,hev1:!0,hvc1:!0,mjp2:!0,mp4v:!0,mvc1:!0,mvc2:!0,mvc3:!0,mvc4:!0,resv:!0,rv60:!0,s263:!0,svc1:!0,svc2:!0,"vc-1":!0,vp08:!0,vp09:!0},text:{stpp:!0,wvtt:!0}};function n(t,e){var r=i[e];return!!r&&!0===r[t.slice(0,4)]}function a(t,e){return MediaSource.isTypeSupported((e||"video")+'/mp4;codecs="'+t+'"')}},"./src/utils/cues.ts":
/*!***************************!*\
  !*** ./src/utils/cues.ts ***!
  \***************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>o});var i=r(/*! ./vttparser */"./src/utils/vttparser.ts"),n=r(/*! ./webvtt-parser */"./src/utils/webvtt-parser.ts"),a=r(/*! ./texttrack-utils */"./src/utils/texttrack-utils.ts"),s=/\s/;const o={newCue:function(t,e,r,o){for(var l,u,d,c,h,f=[],g=self.VTTCue||self.TextTrackCue,v=0;v<o.rows.length;v++)if(d=!0,c=0,h="",!(l=o.rows[v]).isEmpty()){for(var p=0;p<l.chars.length;p++)s.test(l.chars[p].uchar)&&d?c++:(h+=l.chars[p].uchar,d=!1);l.cueStartTime=e,e===r&&(r+=1e-4),c>=16?c--:c++;var m=(0,i.fixLineBreaks)(h.trim()),y=(0,n.generateCueId)(e,r,m);t&&t.cues&&t.cues.getCueById(y)||((u=new g(e,r,m)).id=y,u.line=v+1,u.align="left",u.position=10+Math.min(80,10*Math.floor(8*c/32)),f.push(u))}return t&&f.length&&(f.sort((function(t,e){return"auto"===t.line||"auto"===e.line?0:t.line>8&&e.line>8?e.line-t.line:t.line-e.line})),f.forEach((function(e){return(0,a.addCueToTrack)(t,e)}))),f}}},"./src/utils/discontinuities.ts":
/*!**************************************!*\
  !*** ./src/utils/discontinuities.ts ***!
  \**************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{adjustSlidingStart:()=>d,alignMediaPlaylistByPDT:()=>f,alignPDT:()=>h,alignStream:()=>c,findDiscontinuousReferenceFrag:()=>l,findFirstFragWithCC:()=>s,shouldAlignOnDiscontinuities:()=>o});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ./logger */"./src/utils/logger.ts"),a=r(/*! ../controller/level-helper */"./src/controller/level-helper.ts");function s(t,e){for(var r=null,i=0,n=t.length;i<n;i++){var a=t[i];if(a&&a.cc===e){r=a;break}}return r}function o(t,e,r){return!(!e.details||!(r.endCC>r.startCC||t&&t.cc<r.startCC))}function l(t,e,r){void 0===r&&(r=0);var i=t.fragments,a=e.fragments;if(a.length&&i.length){var o=s(i,a[0].cc);if(o&&(!o||o.startPTS))return o;n.logger.log("No frag in previous level to align on")}else n.logger.log("No fragments to align")}function u(t,e){if(t){var r=t.start+e;t.start=t.startPTS=r,t.endPTS=r+t.duration}}function d(t,e){for(var r=e.fragments,i=0,n=r.length;i<n;i++)u(r[i],t);e.fragmentHint&&u(e.fragmentHint,t),e.alignedSliding=!0}function c(t,e,r){e&&(function(t,e,r){if(o(t,r,e)){var a=l(r.details,e);a&&(0,i.isFiniteNumber)(a.start)&&(n.logger.log("Adjusting PTS using last level due to CC increase within current level "+e.url),d(a.start,e))}}(t,r,e),!r.alignedSliding&&e.details&&h(r,e.details),r.alignedSliding||!e.details||r.skippedSegments||(0,a.adjustSliding)(e.details,r))}function h(t,e){if(e.fragments.length&&t.hasProgramDateTime&&e.hasProgramDateTime){var r=e.fragments[0].programDateTime,a=t.fragments[0].programDateTime,s=(a-r)/1e3+e.fragments[0].start;s&&(0,i.isFiniteNumber)(s)&&(n.logger.log("Adjusting PTS using programDateTime delta "+(a-r)+"ms, sliding:"+s.toFixed(3)+" "+t.url+" "),d(s,t))}}function f(t,e){if(t.hasProgramDateTime&&e.hasProgramDateTime){var r=t.fragments,i=e.fragments;if(r.length&&i.length){var n=i[Math.round(i.length/2)-1],a=s(r,n.cc)||r[Math.round(r.length/2)-1],o=n.programDateTime,l=a.programDateTime;null!==o&&null!==l&&d((l-o)/1e3-(a.start-n.start),t)}}}},"./src/utils/ewma-bandwidth-estimator.ts":
/*!***********************************************!*\
  !*** ./src/utils/ewma-bandwidth-estimator.ts ***!
  \***********************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});var i=r(/*! ../utils/ewma */"./src/utils/ewma.ts");const n=function(){function t(t,e,r){this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultEstimate_=r,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new i.default(t),this.fast_=new i.default(e)}var e=t.prototype;return e.update=function(t,e){var r=this.slow_,n=this.fast_;this.slow_.halfLife!==t&&(this.slow_=new i.default(t,r.getEstimate(),r.getTotalWeight())),this.fast_.halfLife!==e&&(this.fast_=new i.default(e,n.getEstimate(),n.getTotalWeight()))},e.sample=function(t,e){var r=(t=Math.max(t,this.minDelayMs_))/1e3,i=8*e/r;this.fast_.sample(r,i),this.slow_.sample(r,i)},e.canEstimate=function(){var t=this.fast_;return t&&t.getTotalWeight()>=this.minWeight_},e.getEstimate=function(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_},e.destroy=function(){},t}()},"./src/utils/ewma.ts":
/*!***************************!*\
  !*** ./src/utils/ewma.ts ***!
  \***************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});const i=function(){function t(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=t,this.alpha_=t?Math.exp(Math.log(.5)/t):0,this.estimate_=e,this.totalWeight_=r}var e=t.prototype;return e.sample=function(t,e){var r=Math.pow(this.alpha_,t);this.estimate_=e*(1-r)+r*this.estimate_,this.totalWeight_+=t},e.getTotalWeight=function(){return this.totalWeight_},e.getEstimate=function(){if(this.alpha_){var t=1-Math.pow(this.alpha_,this.totalWeight_);if(t)return this.estimate_/t}return this.estimate_},t}()},"./src/utils/fetch-loader.ts":
/*!***********************************!*\
  !*** ./src/utils/fetch-loader.ts ***!
  \***********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>p,fetchSupported:()=>h});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ../loader/load-stats */"./src/loader/load-stats.ts"),a=r(/*! ../demux/chunk-cache */"./src/demux/chunk-cache.ts");function s(t){var e="function"==typeof Map?new Map:void 0;return(s=function(t){if(null===t||(r=t,-1===Function.toString.call(r).indexOf("[native code]")))return t;var r;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,i)}function i(){return o(t,arguments,d(this).constructor)}return i.prototype=Object.create(t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),u(i,t)})(t)}function o(t,e,r){return(o=l()?Reflect.construct.bind():function(t,e,r){var i=[null];i.push.apply(i,e);var n=new(Function.bind.apply(t,i));return r&&u(n,r.prototype),n}).apply(null,arguments)}function l(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function u(t,e){return(u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d(t){return(d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function h(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),!0}catch(t){}return!1}var f=function(){function t(t){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=void 0,this.response=void 0,this.controller=void 0,this.context=void 0,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=t.fetchSetup||g,this.controller=new self.AbortController,this.stats=new n.LoadStats}var e=t.prototype;return e.destroy=function(){this.loader=this.callbacks=null,this.abortInternal()},e.abortInternal=function(){var t=this.response;t&&t.ok||(this.stats.aborted=!0,this.controller.abort())},e.abort=function(){var t;this.abortInternal(),null!==(t=this.callbacks)&&void 0!==t&&t.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)},e.load=function(t,e,r){var n=this,a=this.stats;if(a.loading.start)throw new Error("Loader can only be used once.");a.loading.start=self.performance.now();var s=function(t,e){var r={method:"GET",mode:"cors",credentials:"same-origin",signal:e,headers:new self.Headers(c({},t.headers))};return t.rangeEnd&&r.headers.set("Range","bytes="+t.rangeStart+"-"+String(t.rangeEnd-1)),r}(t,this.controller.signal),o=r.onProgress,l="arraybuffer"===t.responseType,u=l?"byteLength":"length";this.context=t,this.config=e,this.callbacks=r,this.request=this.fetchSetup(t,s),self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout((function(){n.abortInternal(),r.onTimeout(a,t,n.response)}),e.timeout),self.fetch(this.request).then((function(r){if(n.response=n.loader=r,!r.ok){var s=r.status,u=r.statusText;throw new v(u||"fetch, bad network response",s,r)}return a.loading.first=Math.max(self.performance.now(),a.loading.start),a.total=parseInt(r.headers.get("Content-Length")||"0"),o&&(0,i.isFiniteNumber)(e.highWaterMark)?n.loadProgressively(r,a,t,e.highWaterMark,o):l?r.arrayBuffer():r.text()})).then((function(s){var l=n.response;self.clearTimeout(n.requestTimeout),a.loading.end=Math.max(self.performance.now(),a.loading.first);var d=s[u];d&&(a.loaded=a.total=d);var c={url:l.url,data:s};o&&!(0,i.isFiniteNumber)(e.highWaterMark)&&o(a,t,s,l),r.onSuccess(c,a,t,l)})).catch((function(e){if(self.clearTimeout(n.requestTimeout),!a.aborted){var i=e&&e.code||0,s=e?e.message:null;r.onError({code:i,text:s},t,e?e.details:null)}}))},e.getCacheAge=function(){var t=null;if(this.response){var e=this.response.headers.get("age");t=e?parseFloat(e):null}return t},e.loadProgressively=function(t,e,r,i,n){void 0===i&&(i=0);var s=new a.default,o=t.body.getReader();return function a(){return o.read().then((function(o){if(o.done)return s.dataLength&&n(e,r,s.flush(),t),Promise.resolve(new ArrayBuffer(0));var l=o.value,u=l.length;return e.loaded+=u,u<i||s.dataLength?(s.push(l),s.dataLength>=i&&n(e,r,s.flush(),t)):n(e,r,l,t),a()})).catch((function(){return Promise.reject()}))}()},t}();function g(t,e){return new self.Request(t.url,e)}var v=function(t){var e,r;function i(e,r,i){var n;return(n=t.call(this,e)||this).code=void 0,n.details=void 0,n.code=r,n.details=i,n}return r=t,(e=i).prototype=Object.create(r.prototype),e.prototype.constructor=e,u(e,r),i}(s(Error));const p=f},"./src/utils/imsc1-ttml-parser.ts":
/*!****************************************!*\
  !*** ./src/utils/imsc1-ttml-parser.ts ***!
  \****************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{IMSC1_CODEC:()=>d,parseIMSC1:()=>g});var i=r(/*! ./mp4-tools */"./src/utils/mp4-tools.ts"),n=r(/*! ./vttparser */"./src/utils/vttparser.ts"),a=r(/*! ./vttcue */"./src/utils/vttcue.ts"),s=r(/*! ../demux/id3 */"./src/demux/id3.ts"),o=r(/*! ./timescale-conversion */"./src/utils/timescale-conversion.ts"),l=r(/*! ./webvtt-parser */"./src/utils/webvtt-parser.ts");function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}var d="stpp.ttml.im1t",c=/^(\d{2,}):(\d{2}):(\d{2}):(\d{2})\.?(\d+)?$/,h=/^(\d*(?:\.\d*)?)(h|m|s|ms|f|t)$/,f={left:"start",center:"center",right:"end",start:"start",end:"end"};function g(t,e,r,n,d){var c=(0,i.findBox)(new Uint8Array(t),["mdat"]);if(0!==c.length){var h=c.map((function(t){return(0,s.utf8ArrayToStr)(t)})),g=(0,o.toTimescaleFromScale)(e,1,r);try{h.forEach((function(t){return n(function(t,e){var r=(new DOMParser).parseFromString(t,"text/xml").getElementsByTagName("tt")[0];if(!r)throw new Error("Invalid ttml");var i={frameRate:30,subFrameRate:1,frameRateMultiplier:0,tickRate:0},n=Object.keys(i).reduce((function(t,e){return t[e]=r.getAttribute("ttp:"+e)||i[e],t}),{}),s="preserve"!==r.getAttribute("xml:space"),o=p(v(r,"styling","style")),d=p(v(r,"layout","region")),c=v(r,"body","[begin]");return[].map.call(c,(function(t){var r=function t(e,r){return[].slice.call(e.childNodes).reduce((function(e,i,n){var a;return"br"===i.nodeName&&n?e+"\n":null!==(a=i.childNodes)&&void 0!==a&&a.length?t(i,r):r?e+i.textContent.trim().replace(/\s+/g," "):e+i.textContent}),"")}(t,s);if(!r||!t.hasAttribute("begin"))return null;var i=E(t.getAttribute("begin"),n),c=E(t.getAttribute("dur"),n),h=E(t.getAttribute("end"),n);if(null===i)throw y(t);if(null===h){if(null===c)throw y(t);h=i+c}var g=new a.default(i-e,h-e,r);g.id=(0,l.generateCueId)(g.startTime,g.endTime,g.text);var v=d[t.getAttribute("region")],p=o[t.getAttribute("style")];g.position=10,g.size=80;var T=function(t,e,r){var i="http://www.w3.org/ns/ttml#styling",n=null,a=null!=t&&t.hasAttribute("style")?t.getAttribute("style"):null;return a&&r.hasOwnProperty(a)&&(n=r[a]),["displayAlign","textAlign","color","backgroundColor","fontSize","fontFamily"].reduce((function(r,a){var s=m(e,i,a)||m(t,i,a)||m(n,i,a);return s&&(r[a]=s),r}),{})}(v,p,o),S=T.textAlign;if(S){var b=f[S];b&&(g.lineAlign=b),g.align=S}return u(g,T),g})).filter((function(t){return null!==t}))}(t,g))}))}catch(t){d(t)}}else d(new Error("Could not parse IMSC1 mdat"))}function v(t,e,r){var i=t.getElementsByTagName(e)[0];return i?[].slice.call(i.querySelectorAll(r)):[]}function p(t){return t.reduce((function(t,e){var r=e.getAttribute("xml:id");return r&&(t[r]=e),t}),{})}function m(t,e,r){return t&&t.hasAttributeNS(e,r)?t.getAttributeNS(e,r):null}function y(t){return new Error("Could not parse ttml timestamp "+t)}function E(t,e){if(!t)return null;var r=(0,n.parseTimeStamp)(t);return null===r&&(c.test(t)?r=function(t,e){var r=c.exec(t),i=(0|r[4])+(0|r[5])/e.subFrameRate;return 3600*(0|r[1])+60*(0|r[2])+(0|r[3])+i/e.frameRate}(t,e):h.test(t)&&(r=function(t,e){var r=h.exec(t),i=Number(r[1]);switch(r[2]){case"h":return 3600*i;case"m":return 60*i;case"ms":return 1e3*i;case"f":return i/e.frameRate;case"t":return i/e.tickRate}return i}(t,e))),r}},"./src/utils/logger.ts":
/*!*****************************!*\
  !*** ./src/utils/logger.ts ***!
  \*****************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{enableLogs:()=>o,logger:()=>l});var i=function(){},n={trace:i,debug:i,log:i,warn:i,info:i,error:i},a=n;function s(t){var e=self.console[t];return e?e.bind(self.console,"["+t+"] >"):i}function o(t,e){if(self.console&&!0===t||"object"==typeof t){!function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];r.forEach((function(e){a[e]=t[e]?t[e].bind(t):s(e)}))}(t,"debug","log","info","warn","error");try{a.log('Debug logs enabled for "'+e+'"')}catch(t){a=n}}else a=n}var l=n},"./src/utils/mediakeys-helper.ts":
/*!***************************************!*\
  !*** ./src/utils/mediakeys-helper.ts ***!
  \***************************************/(t,e,r)=>{"use strict";var i;r.r(e),r.d(e,{KeySystems:()=>i,requestMediaKeySystemAccess:()=>n}),function(t){t.WIDEVINE="com.widevine.alpha",t.PLAYREADY="com.microsoft.playready"}(i||(i={}));var n="undefined"!=typeof self&&self.navigator&&self.navigator.requestMediaKeySystemAccess?self.navigator.requestMediaKeySystemAccess.bind(self.navigator):null},"./src/utils/mediasource-helper.ts":
/*!*****************************************!*\
  !*** ./src/utils/mediasource-helper.ts ***!
  \*****************************************/(t,e,r)=>{"use strict";function i(){return self.MediaSource||self.WebKitMediaSource}r.r(e),r.d(e,{getMediaSource:()=>i})},"./src/utils/mp4-tools.ts":
/*!********************************!*\
  !*** ./src/utils/mp4-tools.ts ***!
  \********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{RemuxerTrackIdConfig:()=>l,appendUint8Array:()=>b,bin2str:()=>u,computeRawDurationFromSamples:()=>E,discardEPB:()=>R,findBox:()=>g,getDuration:()=>y,getStartDTS:()=>m,offsetStartDTS:()=>T,parseEmsg:()=>k,parseInitSegment:()=>p,parseSEIMessageFromNALu:()=>A,parseSamples:()=>D,parseSegmentIndex:()=>v,readSint32:()=>h,readUint16:()=>d,readUint32:()=>c,segmentValidRange:()=>S,writeUint32:()=>f});var i=r(/*! ./typed-array */"./src/utils/typed-array.ts"),n=r(/*! ../loader/fragment */"./src/loader/fragment.ts"),a=r(/*! ../demux/id3 */"./src/demux/id3.ts"),s=Math.pow(2,32)-1,o=[].push,l={video:1,audio:2,id3:3,text:4};function u(t){return String.fromCharCode.apply(null,t)}function d(t,e){var r=t[e]<<8|t[e+1];return r<0?65536+r:r}function c(t,e){var r=h(t,e);return r<0?4294967296+r:r}function h(t,e){return t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}function f(t,e,r){t[e]=r>>24,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=255&r}function g(t,e){var r=[];if(!e.length)return r;for(var i=t.byteLength,n=0;n<i;){var a=c(t,n),s=a>1?n+a:i;if(u(t.subarray(n+4,n+8))===e[0])if(1===e.length)r.push(t.subarray(n+8,s));else{var l=g(t.subarray(n+8,s),e.slice(1));l.length&&o.apply(r,l)}n=s}return r}function v(t){var e=[],r=t[0],i=8,n=c(t,i);i+=4,i+=0===r?8:16,i+=2;var a=t.length+0,s=d(t,i);i+=2;for(var o=0;o<s;o++){var l=i,u=c(t,l);l+=4;var h=2147483647&u;if(1==(2147483648&u)>>>31)return console.warn("SIDX has hierarchical references (not supported)"),null;var f=c(t,l);l+=4,e.push({referenceSize:h,subsegmentDuration:f,info:{duration:f/n,start:a,end:a+h-1}}),a+=h,i=l+=4}return{earliestPresentationTime:0,timescale:n,version:r,referencesCount:s,references:e}}function p(t){for(var e=[],r=g(t,["moov","trak"]),i=0;i<r.length;i++){var a=r[i],s=g(a,["tkhd"])[0];if(s){var o=s[0],l=0===o?12:20,d=c(s,l),h=g(a,["mdia","mdhd"])[0];if(h){var f=c(h,l=0===(o=h[0])?12:20),v=g(a,["mdia","hdlr"])[0];if(v){var p=u(v.subarray(8,12)),m={soun:n.ElementaryStreamTypes.AUDIO,vide:n.ElementaryStreamTypes.VIDEO}[p];if(m){var y=g(a,["mdia","minf","stbl","stsd"])[0],E=void 0;y&&(E=u(y.subarray(12,16))),e[d]={timescale:f,type:m},e[m]={timescale:f,id:d,codec:E}}}}}}return g(t,["moov","mvex","trex"]).forEach((function(t){var r=c(t,4),i=e[r];i&&(i.default={duration:c(t,12),flags:c(t,20)})})),e}function m(t,e){return g(e,["moof","traf"]).reduce((function(e,r){var i=g(r,["tfdt"])[0],n=i[0],a=g(r,["tfhd"]).reduce((function(e,r){var a=c(r,4),s=t[a];if(s){var o=c(i,4);1===n&&(o*=Math.pow(2,32),o+=c(i,8));var l=o/(s.timescale||9e4);if(isFinite(l)&&(null===e||l<e))return l}return e}),null);return null!==a&&isFinite(a)&&(null===e||a<e)?a:e}),null)||0}function y(t,e){for(var r=0,i=0,a=0,s=g(t,["moof","traf"]),o=0;o<s.length;o++){var l=s[o],u=g(l,["tfhd"])[0],d=e[c(u,4)];if(d){var h=d.default,f=c(u,0)|(null==h?void 0:h.flags),p=null==h?void 0:h.duration;8&f&&(p=c(u,2&f?12:8));for(var m=d.timescale||9e4,y=g(l,["trun"]),T=0;T<y.length;T++)!(r=E(y[T]))&&p&&(r=p*c(y[T],4)),d.type===n.ElementaryStreamTypes.VIDEO?i+=r/m:d.type===n.ElementaryStreamTypes.AUDIO&&(a+=r/m)}}if(0===i&&0===a){for(var S=0,b=g(t,["sidx"]),D=0;D<b.length;D++){var L=v(b[D]);null!=L&&L.references&&(S+=L.references.reduce((function(t,e){return t+e.info.duration||0}),0))}return S}return i||a}function E(t){var e=c(t,0),r=8;1&e&&(r+=4),4&e&&(r+=4);for(var i=0,n=c(t,4),a=0;a<n;a++)256&e&&(i+=c(t,r),r+=4),512&e&&(r+=4),1024&e&&(r+=4),2048&e&&(r+=4);return i}function T(t,e,r){g(e,["moof","traf"]).forEach((function(e){g(e,["tfhd"]).forEach((function(i){var n=c(i,4),a=t[n];if(a){var o=a.timescale||9e4;g(e,["tfdt"]).forEach((function(t){var e=t[0],i=c(t,4);if(0===e)i-=r*o,f(t,4,i=Math.max(i,0));else{i*=Math.pow(2,32),i+=c(t,8),i-=r*o,i=Math.max(i,0);var n=Math.floor(i/(s+1)),a=Math.floor(i%(s+1));f(t,4,n),f(t,8,a)}}))}}))}))}function S(t){var e={valid:null,remainder:null},r=g(t,["moof"]);if(!r)return e;if(r.length<2)return e.remainder=t,e;var n=r[r.length-1];return e.valid=(0,i.sliceUint8)(t,0,n.byteOffset-8),e.remainder=(0,i.sliceUint8)(t,n.byteOffset-8),e}function b(t,e){var r=new Uint8Array(t.length+e.length);return r.set(t),r.set(e,t.length),r}function D(t,e){var r=[],i=e.samples,a=e.timescale,s=e.id,o=!1;return g(i,["moof"]).map((function(l){var u=l.byteOffset-8;g(l,["traf"]).map((function(l){var d=g(l,["tfdt"]).map((function(t){var e=t[0],r=c(t,4);return 1===e&&(r*=Math.pow(2,32),r+=c(t,8)),r/a}))[0];return void 0!==d&&(t=d),g(l,["tfhd"]).map((function(d){var f=c(d,4),v=16777215&c(d,0),p=0,m=0!=(16&v),y=0,E=0!=(32&v),T=8;f===s&&(0!=(1&v)&&(T+=8),0!=(2&v)&&(T+=4),0!=(8&v)&&(p=c(d,T),T+=4),m&&(y=c(d,T),T+=4),E&&(T+=4),"video"===e.type&&(o=function(t){if(!t)return!1;var e=t.indexOf("."),r=e<0?t:t.substring(0,e);return"hvc1"===r||"hev1"===r||"dvh1"===r||"dvhe"===r}(e.codec)),g(l,["trun"]).map((function(s){var l=s[0],d=16777215&c(s,0),f=0!=(1&d),g=0,v=0!=(4&d),m=0!=(256&d),E=0,T=0!=(512&d),S=0,b=0!=(1024&d),D=0!=(2048&d),R=0,k=c(s,4),I=8;f&&(g=c(s,I),I+=4),v&&(I+=4);for(var _=g+u,C=0;C<k;C++){if(m?(E=c(s,I),I+=4):E=p,T?(S=c(s,I),I+=4):S=y,b&&(I+=4),D&&(R=0===l?c(s,I):h(s,I),I+=4),e.type===n.ElementaryStreamTypes.VIDEO)for(var w=0;w<S;){var O=c(i,_);L(o,i[_+=4])&&A(i.subarray(_,_+O),o?2:1,t+R/a,r),_+=O,w+=O+4}t+=E/a}})))}))}))})),r}function L(t,e){if(t){var r=e>>1&63;return 39===r||40===r}return 6==(31&e)}function A(t,e,r,i){var n=R(t),s=0;s+=e;for(var o=0,l=0,u=!1,h=0;s<n.length;){o=0;do{if(s>=n.length)break;o+=h=n[s++]}while(255===h);l=0;do{if(s>=n.length)break;l+=h=n[s++]}while(255===h);var f=n.length-s;if(!u&&4===o&&s<n.length){if(u=!0,181===n[s++]){var g=d(n,s);if(s+=2,49===g){var v=c(n,s);if(s+=4,1195456820===v){var p=n[s++];if(3===p){var m=n[s++],y=64&m,E=y?2+3*(31&m):0,T=new Uint8Array(E);if(y){T[0]=m;for(var S=1;S<E;S++)T[S]=n[s++]}i.push({type:p,payloadType:o,pts:r,bytes:T})}}}}}else if(5===o&&l<f){if(u=!0,l>16){for(var b=[],D=0;D<16;D++){var L=n[s++].toString(16);b.push(1==L.length?"0"+L:L),3!==D&&5!==D&&7!==D&&9!==D||b.push("-")}for(var A=l-16,k=new Uint8Array(A),I=0;I<A;I++)k[I]=n[s++];i.push({payloadType:o,pts:r,uuid:b.join(""),userData:(0,a.utf8ArrayToStr)(k),userDataBytes:k})}}else if(l<f)s+=l;else if(l>f)break}}function R(t){for(var e=t.byteLength,r=[],i=1;i<e-2;)0===t[i]&&0===t[i+1]&&3===t[i+2]?(r.push(i+2),i+=2):i++;if(0===r.length)return t;var n=e-r.length,a=new Uint8Array(n),s=0;for(i=0;i<n;s++,i++)s===r[0]&&(s++,r.shift()),a[i]=t[s];return a}function k(t){var e=t[0],r="",i="",n=0,a=0,s=0,o=0,l=0,d=0;if(0===e){for(;"\0"!==u(t.subarray(d,d+1));)r+=u(t.subarray(d,d+1)),d+=1;for(r+=u(t.subarray(d,d+1)),d+=1;"\0"!==u(t.subarray(d,d+1));)i+=u(t.subarray(d,d+1)),d+=1;i+=u(t.subarray(d,d+1)),d+=1,n=c(t,12),a=c(t,16),o=c(t,20),l=c(t,24),d=28}else if(1===e){n=c(t,d+=4);var h=c(t,d+=4),f=c(t,d+=4);for(d+=4,s=Math.pow(2,32)*h+f,Number.isSafeInteger(s)||(s=Number.MAX_SAFE_INTEGER,console.warn("Presentation time exceeds safe integer limit and wrapped to max safe integer in parsing emsg box")),o=c(t,d),l=c(t,d+=4),d+=4;"\0"!==u(t.subarray(d,d+1));)r+=u(t.subarray(d,d+1)),d+=1;for(r+=u(t.subarray(d,d+1)),d+=1;"\0"!==u(t.subarray(d,d+1));)i+=u(t.subarray(d,d+1)),d+=1;i+=u(t.subarray(d,d+1)),d+=1}return{schemeIdUri:r,value:i,timeScale:n,presentationTime:s,presentationTimeDelta:a,eventDuration:o,id:l,payload:t.subarray(d,t.byteLength)}}},"./src/utils/output-filter.ts":
/*!************************************!*\
  !*** ./src/utils/output-filter.ts ***!
  \************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var i=function(){function t(t,e){this.timelineController=void 0,this.cueRanges=[],this.trackName=void 0,this.startTime=null,this.endTime=null,this.screen=null,this.timelineController=t,this.trackName=e}var e=t.prototype;return e.dispatchCue=function(){null!==this.startTime&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen,this.cueRanges),this.startTime=null)},e.newCue=function(t,e,r){(null===this.startTime||this.startTime>t)&&(this.startTime=t),this.endTime=e,this.screen=r,this.timelineController.createCaptionsTrack(this.trackName)},e.reset=function(){this.cueRanges=[],this.startTime=null},t}()},"./src/utils/texttrack-utils.ts":
/*!**************************************!*\
  !*** ./src/utils/texttrack-utils.ts ***!
  \**************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{addCueToTrack:()=>a,clearCurrentCues:()=>s,getCuesInRange:()=>l,removeCuesInRange:()=>o,sendAddTrackEvent:()=>n});var i=r(/*! ./logger */"./src/utils/logger.ts");function n(t,e){var r;try{r=new Event("addtrack")}catch(t){(r=document.createEvent("Event")).initEvent("addtrack",!1,!1)}r.track=t,e.dispatchEvent(r)}function a(t,e){var r=t.mode;if("disabled"===r&&(t.mode="hidden"),t.cues&&!t.cues.getCueById(e.id))try{if(t.addCue(e),!t.cues.getCueById(e.id))throw new Error("addCue is failed for: "+e)}catch(r){i.logger.debug("[texttrack-utils]: "+r);var n=new self.TextTrackCue(e.startTime,e.endTime,e.text);n.id=e.id,t.addCue(n)}"disabled"===r&&(t.mode=r)}function s(t){var e=t.mode;if("disabled"===e&&(t.mode="hidden"),t.cues)for(var r=t.cues.length;r--;)t.removeCue(t.cues[r]);"disabled"===e&&(t.mode=e)}function o(t,e,r,i){var n=t.mode;if("disabled"===n&&(t.mode="hidden"),t.cues&&t.cues.length>0)for(var a=l(t.cues,e,r),s=0;s<a.length;s++)i&&!i(a[s])||t.removeCue(a[s]);"disabled"===n&&(t.mode=n)}function l(t,e,r){var i=[],n=function(t,e){if(e<t[0].startTime)return 0;var r=t.length-1;if(e>t[r].endTime)return-1;for(var i=0,n=r;i<=n;){var a=Math.floor((n+i)/2);if(e<t[a].startTime)n=a-1;else{if(!(e>t[a].startTime&&i<r))return a;i=a+1}}return t[i].startTime-e<e-t[n].startTime?i:n}(t,e);if(n>-1)for(var a=n,s=t.length;a<s;a++){var o=t[a];if(o.startTime>=e&&o.endTime<=r)i.push(o);else if(o.startTime>r)return i}return i}},"./src/utils/time-ranges.ts":
/*!**********************************!*\
  !*** ./src/utils/time-ranges.ts ***!
  \**********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});const i={toString:function(t){for(var e="",r=t.length,i=0;i<r;i++)e+="["+t.start(i).toFixed(3)+","+t.end(i).toFixed(3)+"]";return e}}},"./src/utils/timescale-conversion.ts":
/*!*******************************************!*\
  !*** ./src/utils/timescale-conversion.ts ***!
  \*******************************************/(t,e,r)=>{"use strict";function i(t,e,r,i){void 0===r&&(r=1),void 0===i&&(i=!1);var n=t*e*r;return i?Math.round(n):n}function n(t,e,r,n){return void 0===r&&(r=1),void 0===n&&(n=!1),i(t,e,1/r,n)}function a(t,e){return void 0===e&&(e=!1),i(t,1e3,1/9e4,e)}function s(t,e){return void 0===e&&(e=1),i(t,9e4,1/e)}r.r(e),r.d(e,{toMpegTsClockFromTimescale:()=>s,toMsFromMpegTsClock:()=>a,toTimescaleFromBase:()=>i,toTimescaleFromScale:()=>n})},"./src/utils/typed-array.ts":
/*!**********************************!*\
  !*** ./src/utils/typed-array.ts ***!
  \**********************************/(t,e,r)=>{"use strict";function i(t,e,r){return Uint8Array.prototype.slice?t.slice(e,r):new Uint8Array(Array.prototype.slice.call(t,e,r))}r.r(e),r.d(e,{sliceUint8:()=>i})},"./src/utils/vttcue.ts":
/*!*****************************!*\
  !*** ./src/utils/vttcue.ts ***!
  \*****************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});const i=function(){if("undefined"!=typeof self&&self.VTTCue)return self.VTTCue;var t=["","lr","rl"],e=["start","middle","end","left","right"];function r(t,e){if("string"!=typeof e)return!1;if(!Array.isArray(t))return!1;var r=e.toLowerCase();return!!~t.indexOf(r)&&r}function i(t){return r(e,t)}function n(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];for(var n=1;n<arguments.length;n++){var a=arguments[n];for(var s in a)t[s]=a[s]}return t}function a(e,a,s){var o=this,l={enumerable:!0};o.hasBeenReset=!1;var u="",d=!1,c=e,h=a,f=s,g=null,v="",p=!0,m="auto",y="start",E=50,T="middle",S=50,b="middle";Object.defineProperty(o,"id",n({},l,{get:function(){return u},set:function(t){u=""+t}})),Object.defineProperty(o,"pauseOnExit",n({},l,{get:function(){return d},set:function(t){d=!!t}})),Object.defineProperty(o,"startTime",n({},l,{get:function(){return c},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");c=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"endTime",n({},l,{get:function(){return h},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");h=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"text",n({},l,{get:function(){return f},set:function(t){f=""+t,this.hasBeenReset=!0}})),Object.defineProperty(o,"region",n({},l,{get:function(){return g},set:function(t){g=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"vertical",n({},l,{get:function(){return v},set:function(e){var i=function(e){return r(t,e)}(e);if(!1===i)throw new SyntaxError("An invalid or illegal string was specified.");v=i,this.hasBeenReset=!0}})),Object.defineProperty(o,"snapToLines",n({},l,{get:function(){return p},set:function(t){p=!!t,this.hasBeenReset=!0}})),Object.defineProperty(o,"line",n({},l,{get:function(){return m},set:function(t){if("number"!=typeof t&&"auto"!==t)throw new SyntaxError("An invalid number or illegal string was specified.");m=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"lineAlign",n({},l,{get:function(){return y},set:function(t){var e=i(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");y=e,this.hasBeenReset=!0}})),Object.defineProperty(o,"position",n({},l,{get:function(){return E},set:function(t){if(t<0||t>100)throw new Error("Position must be between 0 and 100.");E=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"positionAlign",n({},l,{get:function(){return T},set:function(t){var e=i(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");T=e,this.hasBeenReset=!0}})),Object.defineProperty(o,"size",n({},l,{get:function(){return S},set:function(t){if(t<0||t>100)throw new Error("Size must be between 0 and 100.");S=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"align",n({},l,{get:function(){return b},set:function(t){var e=i(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");b=e,this.hasBeenReset=!0}})),o.displayState=void 0}return a.prototype.getCueAsHTML=function(){return self.WebVTT.convertCueToDOMTree(self,this.text)},a}()},"./src/utils/vttparser.ts":
/*!********************************!*\
  !*** ./src/utils/vttparser.ts ***!
  \********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{VTTParser:()=>h,fixLineBreaks:()=>c,parseTimeStamp:()=>a});var i=r(/*! ./vttcue */"./src/utils/vttcue.ts"),n=function(){function t(){}return t.prototype.decode=function(t,e){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))},t}();function a(t){function e(t,e,r,i){return 3600*(0|t)+60*(0|e)+(0|r)+parseFloat(i||0)}var r=t.match(/^(?:(\d+):)?(\d{2}):(\d{2})(\.\d+)?/);return r?parseFloat(r[2])>59?e(r[2],r[3],0,r[4]):e(r[1],r[2],r[3],r[4]):null}var s=function(){function t(){this.values=Object.create(null)}var e=t.prototype;return e.set=function(t,e){this.get(t)||""===e||(this.values[t]=e)},e.get=function(t,e,r){return r?this.has(t)?this.values[t]:e[r]:this.has(t)?this.values[t]:e},e.has=function(t){return t in this.values},e.alt=function(t,e,r){for(var i=0;i<r.length;++i)if(e===r[i]){this.set(t,e);break}},e.integer=function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},e.percent=function(t,e){if(/^([\d]{1,3})(\.[\d]*)?%$/.test(e)){var r=parseFloat(e);if(r>=0&&r<=100)return this.set(t,r),!0}return!1},t}();function o(t,e,r,i){var n=i?t.split(i):[t];for(var a in n)if("string"==typeof n[a]){var s=n[a].split(r);2===s.length&&e(s[0],s[1])}}var l=new i.default(0,0,""),u="middle"===l.align?"middle":"center";function d(t,e,r){var i=t;function n(){var e=a(t);if(null===e)throw new Error("Malformed timestamp: "+i);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function d(){t=t.replace(/^\s+/,"")}if(d(),e.startTime=n(),d(),"--\x3e"!==t.slice(0,3))throw new Error("Malformed time stamp (time stamps must be separated by '--\x3e'): "+i);t=t.slice(3),d(),e.endTime=n(),d(),function(t,e){var i=new s;o(t,(function(t,e){var n;switch(t){case"region":for(var a=r.length-1;a>=0;a--)if(r[a].id===e){i.set(t,r[a].region);break}break;case"vertical":i.alt(t,e,["rl","lr"]);break;case"line":n=e.split(","),i.integer(t,n[0]),i.percent(t,n[0])&&i.set("snapToLines",!1),i.alt(t,n[0],["auto"]),2===n.length&&i.alt("lineAlign",n[1],["start",u,"end"]);break;case"position":n=e.split(","),i.percent(t,n[0]),2===n.length&&i.alt("positionAlign",n[1],["start",u,"end","line-left","line-right","auto"]);break;case"size":i.percent(t,e);break;case"align":i.alt(t,e,["start",u,"end","left","right"])}}),/:/,/\s/),e.region=i.get("region",null),e.vertical=i.get("vertical","");var n=i.get("line","auto");"auto"===n&&-1===l.line&&(n=-1),e.line=n,e.lineAlign=i.get("lineAlign","start"),e.snapToLines=i.get("snapToLines",!0),e.size=i.get("size",100),e.align=i.get("align",u);var a=i.get("position","auto");"auto"===a&&50===l.position&&(a="start"===e.align||"left"===e.align?0:"end"===e.align||"right"===e.align?100:50),e.position=a}(t,e)}function c(t){return t.replace(/<br(?: \/)?>/gi,"\n")}var h=function(){function t(){this.state="INITIAL",this.buffer="",this.decoder=new n,this.regionList=[],this.cue=null,this.oncue=void 0,this.onparsingerror=void 0,this.onflush=void 0}var e=t.prototype;return e.parse=function(t){var e=this;function r(){var t=e.buffer,r=0;for(t=c(t);r<t.length&&"\r"!==t[r]&&"\n"!==t[r];)++r;var i=t.slice(0,r);return"\r"===t[r]&&++r,"\n"===t[r]&&++r,e.buffer=t.slice(r),i}t&&(e.buffer+=e.decoder.decode(t,{stream:!0}));try{var n="";if("INITIAL"===e.state){if(!/\r\n|\n/.test(e.buffer))return this;var a=(n=r()).match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(!a||!a[0])throw new Error("Malformed WebVTT signature.");e.state="HEADER"}for(var s=!1;e.buffer;){if(!/\r\n|\n/.test(e.buffer))return this;switch(s?s=!1:n=r(),e.state){case"HEADER":/:/.test(n)?o(n,(function(t,e){}),/:/):n||(e.state="ID");continue;case"NOTE":n||(e.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(n)){e.state="NOTE";break}if(!n)continue;if(e.cue=new i.default(0,0,""),e.state="CUE",-1===n.indexOf("--\x3e")){e.cue.id=n;continue}case"CUE":if(!e.cue){e.state="BADCUE";continue}try{d(n,e.cue,e.regionList)}catch(t){e.cue=null,e.state="BADCUE";continue}e.state="CUETEXT";continue;case"CUETEXT":var l=-1!==n.indexOf("--\x3e");if(!n||l&&(s=!0)){e.oncue&&e.cue&&e.oncue(e.cue),e.cue=null,e.state="ID";continue}if(null===e.cue)continue;e.cue.text&&(e.cue.text+="\n"),e.cue.text+=n;continue;case"BADCUE":n||(e.state="ID")}}}catch(t){"CUETEXT"===e.state&&e.cue&&e.oncue&&e.oncue(e.cue),e.cue=null,e.state="INITIAL"===e.state?"BADWEBVTT":"BADCUE"}return this},e.flush=function(){try{if((this.cue||"HEADER"===this.state)&&(this.buffer+="\n\n",this.parse()),"INITIAL"===this.state||"BADWEBVTT"===this.state)throw new Error("Malformed WebVTT signature.")}catch(t){this.onparsingerror&&this.onparsingerror(t)}return this.onflush&&this.onflush(),this},t}()},"./src/utils/webvtt-parser.ts":
/*!************************************!*\
  !*** ./src/utils/webvtt-parser.ts ***!
  \************************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{generateCueId:()=>c,parseWebVTT:()=>h});var i=r(/*! ./src/polyfills/number */"./src/polyfills/number.ts"),n=r(/*! ./vttparser */"./src/utils/vttparser.ts"),a=r(/*! ../demux/id3 */"./src/demux/id3.ts"),s=r(/*! ./timescale-conversion */"./src/utils/timescale-conversion.ts"),o=r(/*! ../remux/mp4-remuxer */"./src/remux/mp4-remuxer.ts"),l=/\r\n|\n\r|\n|\r/g,u=function(t,e,r){return void 0===r&&(r=0),t.slice(r,r+e.length)===e},d=function(t){for(var e=5381,r=t.length;r;)e=33*e^t.charCodeAt(--r);return(e>>>0).toString()};function c(t,e,r){return d(t.toString())+d(e.toString())+d(r)}function h(t,e,r,d,h,f,g,v){var p,m=new n.VTTParser,y=(0,a.utf8ArrayToStr)(new Uint8Array(t)).trim().replace(l,"\n").split("\n"),E=[],T=(0,s.toMpegTsClockFromTimescale)(e,r),S="00:00.000",b=0,D=0,L=!0;m.oncue=function(t){var e=d[h],r=d.ccOffset,i=(b-T)/9e4;null!=e&&e.new&&(void 0!==D?r=d.ccOffset=e.start:function(t,e,r){var i=t[e],n=t[i.prevCC];if(!n||!n.new&&i.new)return t.ccOffset=t.presentationOffset=i.start,void(i.new=!1);for(;null!==(a=n)&&void 0!==a&&a.new;){var a;t.ccOffset+=i.start-n.start,i.new=!1,n=t[(i=n).prevCC]}t.presentationOffset=r}(d,h,i)),i&&(r=i-d.presentationOffset);var n=t.endTime-t.startTime,a=(0,o.normalizePts)(9e4*(t.startTime+r-D),9e4*f)/9e4;t.startTime=Math.max(a,0),t.endTime=Math.max(a+n,0);var s=t.text.trim();t.text=decodeURIComponent(encodeURIComponent(s)),t.id||(t.id=c(t.startTime,t.endTime,s)),t.endTime>0&&E.push(t)},m.onparsingerror=function(t){p=t},m.onflush=function(){p?v(p):g(E)},y.forEach((function(t){if(L){if(u(t,"X-TIMESTAMP-MAP=")){L=!1,t.slice(16).split(",").forEach((function(t){u(t,"LOCAL:")?S=t.slice(6):u(t,"MPEGTS:")&&(b=parseInt(t.slice(7)))}));try{D=function(t){var e=parseInt(t.slice(-3)),r=parseInt(t.slice(-6,-4)),n=parseInt(t.slice(-9,-7)),a=t.length>9?parseInt(t.substring(0,t.indexOf(":"))):0;if(!((0,i.isFiniteNumber)(e)&&(0,i.isFiniteNumber)(r)&&(0,i.isFiniteNumber)(n)&&(0,i.isFiniteNumber)(a)))throw Error("Malformed X-TIMESTAMP-MAP: Local:"+t);return e+=1e3*r,e+=6e4*n,e+=36e5*a}(S)/1e3}catch(t){p=t}return}""===t&&(L=!1)}m.parse(t+"\n")})),m.flush()}},"./src/utils/xhr-loader.ts":
/*!*********************************!*\
  !*** ./src/utils/xhr-loader.ts ***!
  \*********************************/(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>s});var i=r(/*! ../utils/logger */"./src/utils/logger.ts"),n=r(/*! ../loader/load-stats */"./src/loader/load-stats.ts"),a=/^age:\s*[\d.]+\s*$/m;const s=function(){function t(t){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=void 0,this.loader=null,this.stats=void 0,this.xhrSetup=t?t.xhrSetup:null,this.stats=new n.LoadStats,this.retryDelay=0}var e=t.prototype;return e.destroy=function(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null},e.abortInternal=function(){var t=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),t&&(t.onreadystatechange=null,t.onprogress=null,4!==t.readyState&&(this.stats.aborted=!0,t.abort()))},e.abort=function(){var t;this.abortInternal(),null!==(t=this.callbacks)&&void 0!==t&&t.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)},e.load=function(t,e,r){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=t,this.config=e,this.callbacks=r,this.retryDelay=e.retryDelay,this.loadInternal()},e.loadInternal=function(){var t=this.config,e=this.context;if(t){var r=this.loader=new self.XMLHttpRequest,i=this.stats;i.loading.first=0,i.loaded=0;var n=this.xhrSetup;try{if(n)try{n(r,e.url)}catch(t){r.open("GET",e.url,!0),n(r,e.url)}r.readyState||r.open("GET",e.url,!0);var a=this.context.headers;if(a)for(var s in a)r.setRequestHeader(s,a[s])}catch(t){return void this.callbacks.onError({code:r.status,text:t.message},e,r)}e.rangeEnd&&r.setRequestHeader("Range","bytes="+e.rangeStart+"-"+(e.rangeEnd-1)),r.onreadystatechange=this.readystatechange.bind(this),r.onprogress=this.loadprogress.bind(this),r.responseType=e.responseType,self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),t.timeout),r.send()}},e.readystatechange=function(){var t=this.context,e=this.loader,r=this.stats;if(t&&e){var n=e.readyState,a=this.config;if(!r.aborted&&n>=2)if(self.clearTimeout(this.requestTimeout),0===r.loading.first&&(r.loading.first=Math.max(self.performance.now(),r.loading.start)),4===n){e.onreadystatechange=null,e.onprogress=null;var s=e.status,o="arraybuffer"===e.responseType;if(s>=200&&s<300&&(o&&e.response||null!==e.responseText)){var l,u;if(r.loading.end=Math.max(self.performance.now(),r.loading.first),u=o?(l=e.response).byteLength:(l=e.responseText).length,r.loaded=r.total=u,!this.callbacks)return;var d=this.callbacks.onProgress;if(d&&d(r,t,l,e),!this.callbacks)return;var c={url:e.responseURL,data:l};this.callbacks.onSuccess(c,r,t,e)}else r.retry>=a.maxRetry||s>=400&&s<499?(i.logger.error(s+" while loading "+t.url),this.callbacks.onError({code:s,text:e.statusText},t,e)):(i.logger.warn(s+" while loading "+t.url+", retrying in "+this.retryDelay+"..."),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay),this.retryDelay=Math.min(2*this.retryDelay,a.maxRetryDelay),r.retry++)}else self.clearTimeout(this.requestTimeout),this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),a.timeout)}},e.loadtimeout=function(){i.logger.warn("timeout while loading "+this.context.url);var t=this.callbacks;t&&(this.abortInternal(),t.onTimeout(this.stats,this.context,this.loader))},e.loadprogress=function(t){var e=this.stats;e.loaded=t.loaded,t.lengthComputable&&(e.total=t.total)},e.getCacheAge=function(){var t=null;if(this.loader&&a.test(this.loader.getAllResponseHeaders())){var e=this.loader.getResponseHeader("age");t=e?parseFloat(e):null}return t},t}()},"./node_modules/eventemitter3/index.js":
/*!*********************************************!*\
  !*** ./node_modules/eventemitter3/index.js ***!
  \*********************************************/t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function i(){}function n(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function a(t,e,i,a,s){if("function"!=typeof i)throw new TypeError("The listener must be a function");var o=new n(i,a||t,s),l=r?r+e:e;return t._events[l]?t._events[l].fn?t._events[l]=[t._events[l],o]:t._events[l].push(o):(t._events[l]=o,t._eventsCount++),t}function s(t,e){0==--t._eventsCount?t._events=new i:delete t._events[e]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(r=!1)),o.prototype.eventNames=function(){var t,i,n=[];if(0===this._eventsCount)return n;for(i in t=this._events)e.call(t,i)&&n.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},o.prototype.listeners=function(t){var e=r?r+t:t,i=this._events[e];if(!i)return[];if(i.fn)return[i.fn];for(var n=0,a=i.length,s=new Array(a);n<a;n++)s[n]=i[n].fn;return s},o.prototype.listenerCount=function(t){var e=r?r+t:t,i=this._events[e];return i?i.fn?1:i.length:0},o.prototype.emit=function(t,e,i,n,a,s){var o=r?r+t:t;if(!this._events[o])return!1;var l,u,d=this._events[o],c=arguments.length;if(d.fn){switch(d.once&&this.removeListener(t,d.fn,void 0,!0),c){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,e),!0;case 3:return d.fn.call(d.context,e,i),!0;case 4:return d.fn.call(d.context,e,i,n),!0;case 5:return d.fn.call(d.context,e,i,n,a),!0;case 6:return d.fn.call(d.context,e,i,n,a,s),!0}for(u=1,l=new Array(c-1);u<c;u++)l[u-1]=arguments[u];d.fn.apply(d.context,l)}else{var h,f=d.length;for(u=0;u<f;u++)switch(d[u].once&&this.removeListener(t,d[u].fn,void 0,!0),c){case 1:d[u].fn.call(d[u].context);break;case 2:d[u].fn.call(d[u].context,e);break;case 3:d[u].fn.call(d[u].context,e,i);break;case 4:d[u].fn.call(d[u].context,e,i,n);break;default:if(!l)for(h=1,l=new Array(c-1);h<c;h++)l[h-1]=arguments[h];d[u].fn.apply(d[u].context,l)}}return!0},o.prototype.on=function(t,e,r){return a(this,t,e,r,!1)},o.prototype.once=function(t,e,r){return a(this,t,e,r,!0)},o.prototype.removeListener=function(t,e,i,n){var a=r?r+t:t;if(!this._events[a])return this;if(!e)return s(this,a),this;var o=this._events[a];if(o.fn)o.fn!==e||n&&!o.once||i&&o.context!==i||s(this,a);else{for(var l=0,u=[],d=o.length;l<d;l++)(o[l].fn!==e||n&&!o[l].once||i&&o[l].context!==i)&&u.push(o[l]);u.length?this._events[a]=1===u.length?u[0]:u:s(this,a)}return this},o.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&s(this,e)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,t.exports=o},"./node_modules/url-toolkit/src/url-toolkit.js":
/*!*****************************************************!*\
  !*** ./node_modules/url-toolkit/src/url-toolkit.js ***!
  \*****************************************************/function(t){var e,r,i,n,a;e=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,r=/^(?=([^\/?#]*))\1([^]*)$/,i=/(?:\/|^)\.(?=\/)/g,n=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,a={buildAbsoluteURL:function(t,e,i){if(i=i||{},t=t.trim(),!(e=e.trim())){if(!i.alwaysNormalize)return t;var n=a.parseURL(t);if(!n)throw new Error("Error trying to parse base URL.");return n.path=a.normalizePath(n.path),a.buildURLFromParts(n)}var s=a.parseURL(e);if(!s)throw new Error("Error trying to parse relative URL.");if(s.scheme)return i.alwaysNormalize?(s.path=a.normalizePath(s.path),a.buildURLFromParts(s)):e;var o=a.parseURL(t);if(!o)throw new Error("Error trying to parse base URL.");if(!o.netLoc&&o.path&&"/"!==o.path[0]){var l=r.exec(o.path);o.netLoc=l[1],o.path=l[2]}o.netLoc&&!o.path&&(o.path="/");var u={scheme:o.scheme,netLoc:s.netLoc,path:null,params:s.params,query:s.query,fragment:s.fragment};if(!s.netLoc&&(u.netLoc=o.netLoc,"/"!==s.path[0]))if(s.path){var d=o.path,c=d.substring(0,d.lastIndexOf("/")+1)+s.path;u.path=a.normalizePath(c)}else u.path=o.path,s.params||(u.params=o.params,s.query||(u.query=o.query));return null===u.path&&(u.path=i.alwaysNormalize?a.normalizePath(s.path):s.path),a.buildURLFromParts(u)},parseURL:function(t){var r=e.exec(t);return r?{scheme:r[1]||"",netLoc:r[2]||"",path:r[3]||"",params:r[4]||"",query:r[5]||"",fragment:r[6]||""}:null},normalizePath:function(t){for(t=t.split("").reverse().join("").replace(i,"");t.length!==(t=t.replace(n,"")).length;);return t.split("").reverse().join("")},buildURLFromParts:function(t){return t.scheme+t.netLoc+t.path+t.params+t.query+t.fragment}},t.exports=a}},e={};function r(i){var n=e[i];if(void 0!==n)return n.exports;var a=e[i]={exports:{}};return t[i].call(a.exports,a,a.exports,r),a.exports}r.m=t,r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i=r("./src/hls.ts");return i=i.default})(),t.exports=i())},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={getBrowserVersion:function(){var t=navigator.userAgent;if(t.toLowerCase().indexOf("mobile")>-1)return"Unknown";if(t.indexOf("Firefox")>-1)return"Firefox "+t.match(/firefox\/[\d.]+/gi)[0].match(/[\d]+/)[0];if(t.indexOf("Edge")>-1)return"Edge "+t.match(/edge\/[\d.]+/gi)[0].match(/[\d]+/)[0];if(t.indexOf("rv:11")>-1)return"IE 11";if(t.indexOf("Opera")>-1||t.indexOf("OPR")>-1){if(t.indexOf("Opera")>-1)return"Opera "+t.match(/opera\/[\d.]+/gi)[0].match(/[\d]+/)[0];if(t.indexOf("OPR")>-1)return"Opera "+t.match(/opr\/[\d.]+/gi)[0].match(/[\d]+/)[0]}else{if(t.indexOf("Chrome")>-1)return"Chrome "+t.match(/chrome\/[\d.]+/gi)[0].match(/[\d]+/)[0];if(t.indexOf("Safari")>-1)return"Safari "+t.match(/safari\/[\d.]+/gi)[0].match(/[\d]+/)[0];if(!(t.indexOf("MSIE")>-1||t.indexOf("Trident")>-1))return"Unknown";if(t.indexOf("MSIE")>-1)return"IE "+t.match(/msie [\d.]+/gi)[0].match(/[\d]+/)[0];if(t.indexOf("Trident")>-1){var e=t.match(/trident\/[\d.]+/gi)[0].match(/[\d]+/)[0];return"IE "+(parseInt(e)+4)}}}};e.default=i,t.exports=e.default}]);