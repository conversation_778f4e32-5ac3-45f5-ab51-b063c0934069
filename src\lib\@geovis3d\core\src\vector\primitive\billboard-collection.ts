import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.BillboardCollection} 构造函数参数
 */
export type BillboardCollectionConstructorOptions = ConstructorParameters<
  typeof Cesium.BillboardCollection
>[0];

/**
 * {@link Cesium.BillboardCollection} 拓展用法与 {@link Cesium.BillboardCollection} 基本一致。
 *
 * `GcBillboardCollection.event`鼠标事件监听
 */
export class GcBillboardCollection extends Cesium.BillboardCollection {
  constructor(options?: BillboardCollectionConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
