<!-- NumberAttribute -->
<script lang="ts" setup>
import { useVModel } from '@vueuse/core';

defineOptions({ name: 'NumberAttribute' });

const props = withDefaults(
  defineProps<{
    modelValue?: number;
    label?: string;
    controlsPosition?: any;
    min?: number;
    max?: number;
    tail?: string;
    precision?: number;
  }>(),
  {
    controlsPosition: 'right',
  },
);

const emit = defineEmits<{
  (event: 'update:modelValue', value?: number): void;
}>();

const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-form-item v-if="label" :label="label">
    <el-input-number
      v-model.number="model"
      :min="min"
      :max="max"
      :precision="precision"
      :controls-position="controlsPosition"
    />
  </el-form-item>
  <el-input-number
    v-else
    v-model.number="model"
    :min="min"
    :max="max"
    :precision="precision"
    :controls-position="controlsPosition"
  />
</template>
