import type { ChannelInstance, VideoDetect, VideoTreeNode } from './abstract';

/** 林业局视频方案 */
export class FBVideoDetect implements VideoDetect {
  constructor(private readonly baseURL: string) {}

  private _request(params: RequsetParams) {
    return requset({ ...params, baseURL: this.baseURL } as any);
  }

  private _orgListCache: any = null;

  /** 组织树 */
  private async _getOrgList() {
    if (!this._orgListCache) {
      const { data } = await this._request({
        url: '/admin/API/tree/deviceOrg',
        method: 'get',
      });

      const list = JSON.parse(data).data.departments;

      list.forEach((item) => {
        item.children = list.filter(child => child.parentCode == item.code);
      });
      this._orgListCache = list;
    }
    return this._orgListCache;
  }

  private _cameraListCache: any = null;

  /** 摄像头树 */
  private async _cameraList() {
    if (!this._cameraListCache) {
      const orgs = await this._getOrgList();
      const { data } = await this._request({
        url: '/admin/API/tree/devices',
        method: 'post',
      });
      const cameras = JSON.parse(data).data.devices;
      cameras.forEach((camera) => {
        const channels = camera.units.map(unit => unit.channels)?.flat() ?? [];
        const org = orgs.find(org => org.code == camera.orgCode);
        org?.children?.push({
          ...camera,
          children: channels,
        });
      });
      this._cameraListCache = orgs.find(e => !e.parentCode)?.children ?? [];
    }
    return this._cameraListCache;
  }

  /** 获取摄像头总数量 */
  async getCount(): Promise<number> {
    const devices = await this._cameraList();
    let count = 0;
    devices?.forEach((item) => {
      item.units.forEach((unit) => {
        count += unit?.channels?.length || 0;
      });
    });
    return count;
  }

  /**
   * 获取树列表
   * @type {params} 不传则返回根列表  传入上一次`getNodes` 的列表项则可获取下级
   */
  async getNodes(params?: VideoTreeNode): Promise<VideoTreeNode[]> {
    const children = !params ? await this._cameraList() : params?.ext?.children;
    return children?.map((item) => {
      const onLine = item.status == '1' || item?.children?.find(e => e.status == '1');
      return {
        name: item.name || item.channelName,
        code: item.code || item.channelCode,
        isLeaf: !item.children?.length,
        onLine: !!onLine,
        isChannel: !!item.channelCode,
        ext: item,
      };
    });
  }

  getInstance(data: VideoTreeNode) {
    return new FBChannelInstance(data, this.baseURL);
  }
}

/** 摄像头实例 */
export class FBChannelInstance implements ChannelInstance {
  constructor(public data: VideoTreeNode, private baseURL: string) {}
  private _request(params: RequsetParams) {
    return requset({ ...params, baseURL: this.baseURL } as any);
  }

  /** 获取实时流RTSP地址 */
  async realTimeRTSP(): Promise<string> {
    const { data: res = {} } = await this._request({
      url: '/admin/API/MTS/Video/StartVideo',
      method: 'POST',
      data: {
        data: {
          channelId: this.data.ext.channelCode,
          dataType: '1',
          streamType: '1',
        },
      },
    });
    const url = res.data?.url?.split('|').find(e => e.includes('59.212.180.8:9100'));
    return `${url}?token=${res.data?.token}`;
  }
}

interface RequsetParams {
  url: string;
  method: string;
  data?: any;
  params?: any;
}

const fetch = axios.create().request;

function requset({ baseURL, url, method, data, params }) {
  return fetch({
    url: baseURL,
    method,
    headers: {
      'Ph-Url': url,
      'Ph-Accept': 'application/json;charset=UTF-8',
      'Ph-Content-Type': 'application/json;charset=UTF-8',
    },
    data: data || {},
    params: params || {},
  });
}
