import type { BD09, Coordinate, GCJ02, WGS84 } from './types';

import * as Cesium from 'cesium';

import { cartographicToBd09, cartographicToGcj02, cartographicToWgs84 } from './cartographic';

/**
 * Cartesian3 转 Cartographic
 * @param position Cartesian3
 * @returns Cesium.Cartographic
 */
export function cartesianToCartographic(position: Cesium.Cartesian3): Cesium.Cartographic {
  return Cesium.Ellipsoid.WGS84.cartesianToCartographic(position);
}

/**
 * Cartesian3 转 Coordinate
 * @param position Cartesian3
 * @returns Coordinate
 */
export function cartesianToCoordinate(
  position: Cesium.Cartesian3,
  scene: Cesium.Scene,
): Coordinate {
  return scene.cartesianToCanvasCoordinates(position);
}

/**
 * Cartesian3 转 BD09
 * @param position Cartesian3
 * @returns BD09
 */
export function cartesianToBd09(position: Cesium.Cartesian3): BD09 {
  const cartographic = cartesianToCartographic(position);
  return cartographicToBd09(cartographic);
}

/**
 * Cartesian3 转 WGS84
 * @param position Cartesian3
 * @returns WGS84
 */
export function cartesianToWgs84(position: Cesium.Cartesian3): WGS84 {
  const cartographic = cartesianToCartographic(position);
  return cartographicToWgs84(cartographic);
}

/**
 * Cartesian3 转 GCJ02
 * @param position Cartesian3
 * @returns GCJ02
 */
export function cartesianToGcj02(position: Cesium.Cartesian3): GCJ02 {
  const wgs84 = cartesianToCartographic(position);
  return cartographicToGcj02(wgs84);
}
