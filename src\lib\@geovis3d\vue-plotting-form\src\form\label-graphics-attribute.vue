<!-- LabelGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { LabelGraphicsKey, LabelGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { LabelGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian2Attribute from './cartesian2-attribute.vue';
import Cartesian3Attribute from './cartesian3-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import HorizontalOriginAttribute from './horizontal-origin-attribute.vue';
import LabelStyleAttribute from './label-style-attribute.vue';
import NearFarScalarAttribute from './near-far-scalar-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import StringAttribute from './string-attribute.vue';
import VerticalOriginAttribute from './vertical-origin-attribute.vue';

defineOptions({ name: 'LabelGraphicsGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: LabelGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.LabelGraphics, LabelGraphicsSerializateJSON>({
  graphic: () => props.entity?.label,
  omit: props.omit,
  toJSON: (graphics, omit) => LabelGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => LabelGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="label"
    graphics-field="show"
    label="可见"
  />
  <StringAttribute
    v-if="!hide?.includes('text')"
    v-model="model.text"
    graphics="label"
    graphics-field="text"
    label="文本"
  />
  <StringAttribute
    v-if="!hide?.includes('font')"
    v-model="model.font"
    graphics="label"
    graphics-field="font"
    label="字体"
  />
  <LabelStyleAttribute
    v-if="!hide?.includes('style')"
    v-model="model.style"
    graphics="label"
    graphics-field="style"
    label="字体样式"
  />
  <NumberAttribute
    v-if="!hide?.includes('scale')"
    v-model="model.scale"
    graphics="label"
    graphics-field="scale"
    :precision="2"
    label="缩放"
  />
  <BooleanAttribute
    v-if="!hide?.includes('showBackground')"
    v-model="model.showBackground"
    graphics="label"
    graphics-field="showBackground"
    label="背景"
  />
  <ColorAttribute
    v-if="!hide?.includes('backgroundColor')"
    v-model="model.backgroundColor"
    graphics="label"
    graphics-field="backgroundColor"
    label="背景颜色"
  />
  <Cartesian2Attribute
    v-if="!hide?.includes('backgroundPadding')"
    v-model="model.backgroundPadding"
    graphics="label"
    graphics-field="backgroundPadding"
    label="背景内间距"
  />
  <Cartesian2Attribute
    v-if="!hide?.includes('pixelOffset')"
    v-model="model.pixelOffset"
    graphics="label"
    graphics-field="pixelOffset"
    label="像素偏移"
  />
  <Cartesian3Attribute
    v-if="!hide?.includes('eyeOffset')"
    v-model="model.eyeOffset"
    graphics="label"
    graphics-field="eyeOffset"
    label="视角偏移"
  />
  <HorizontalOriginAttribute
    v-if="!hide?.includes('horizontalOrigin')"
    v-model="model.horizontalOrigin"
    graphics="label"
    graphics-field="horizontalOrigin"
    label="水平参照"
  />
  <VerticalOriginAttribute
    v-if="!hide?.includes('verticalOrigin')"
    v-model="model.verticalOrigin"
    graphics="label"
    graphics-field="verticalOrigin"
    label="垂直参照"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="label"
    graphics-field="heightReference"
    label="高度参照"
  />
  <ColorAttribute
    v-if="!hide?.includes('fillColor')"
    v-model="model.fillColor"
    graphics="label"
    graphics-field="fillColor"
    label="填充颜色"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="label"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="label"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('translucencyByDistance')"
    v-model="model.translucencyByDistance"
    graphics="label"
    graphics-field="translucencyByDistance"
    label="按距离透明"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('pixelOffsetScaleByDistance')"
    v-model="model.pixelOffsetScaleByDistance"
    graphics="label"
    graphics-field="pixelOffsetScaleByDistance"
    label="像素偏移比例"
  />
  <NearFarScalarAttribute
    v-if="!hide?.includes('scaleByDistance')"
    v-model="model.scaleByDistance"
    graphics="label"
    graphics-field="scaleByDistance"
    label="按距离缩放"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="label"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <NumberAttribute
    v-if="!hide?.includes('disableDepthTestDistance')"
    v-model="model.disableDepthTestDistance"
    graphics="label"
    graphics-field="disableDepthTestDistance"
    label="禁用深度检测距离"
    :precision="2"
  />
</template>
