import type { AnyFn } from '@/lib/@geovis3d/shared';

import * as Cesium from 'cesium';

export type EventDestroyCallBack = () => boolean;

type Modifier = Cesium.KeyboardEventModifier;

export type CesiumEventCallbackMapper<
  Key extends string | number | symbol,
  Args extends unknown[],
> = {
  [key in Key]: (...args: Args) => void;
};

/**
 * 键盘按键数组
 * undefined 为不按下键盘
 */
export const KEYBOARD_EVENT_MODIFIERS = [
  undefined,
  Cesium.KeyboardEventModifier.SHIFT,
  Cesium.KeyboardEventModifier.CTRL,
  Cesium.KeyboardEventModifier.ALT,
];

export class CesiumEventCollection<
  Key extends string,
  Event<PERSON>allbackMapper extends CesiumEventCallbackMapper<Key, any>,
> {
  /**
   * @internal
   */
  private _collection = new Map<
    keyof EventCallbackMapper,
    Map<Modifier | undefined, Map<boolean, Set<AnyFn>>>
  >();

  /**
   * 监听手势事件
   * @param type 事件类型
   * @param callback 事件回调
   * @param modifier 修饰键
   * @param once 触发后是否自动移除（一次性事件）
   */
  on<T extends keyof EventCallbackMapper>(
    type: T,
    callback: EventCallbackMapper[T],
    modifier?: Modifier,
    once?: boolean,
  ): EventDestroyCallBack {
    modifier ??= undefined;
    once = !!once;
    !this._collection.get(type) && this._collection.set(type, new Map());
    const typeMap = this._collection.get(type)!;
    !typeMap.get(modifier) && typeMap.set(modifier, new Map());
    const modifierMap = typeMap.get(modifier)!;
    !modifierMap.get(once) && modifierMap.set(once, new Set());
    const functionSet = modifierMap.get(once)!;
    functionSet.add(callback);
    return () => this.off(type, callback);
  }

  off<T extends keyof EventCallbackMapper>(
    type: T,
    callback: EventCallbackMapper[T],
    modifier?: Modifier,
  ): boolean {
    modifier ??= undefined;
    const onces = this._collection.get(type)?.get(modifier)?.get(true);
    const continuous = this._collection.get(type)?.get(modifier)?.get(false);
    const onceSuccess = !!onces?.delete(callback);
    const continuousSuccess = !!continuous?.delete(callback);
    return onceSuccess || continuousSuccess;
  }

  emit<T extends keyof EventCallbackMapper>(
    type: T,
    args: Parameters<EventCallbackMapper[T]>,
    modifier?: Modifier,
  ): boolean {
    modifier ??= undefined;
    const onces = this._collection.get(type)?.get(modifier)?.get(true);
    const continuous = this._collection.get(type)?.get(modifier)?.get(false);
    let success = false;
    onces?.forEach((fn) => {
      try {
        fn(...(args as any));
        success = true;
      }
      catch (error) {
        console.error(error);
      }
      onces.delete(fn); // 移除一次性事件
    });

    continuous?.forEach((fn) => {
      try {
        fn(...(args as any));
        success = true;
      }
      catch (error) {
        console.error(error);
      }
    });
    return success;
  }
}
