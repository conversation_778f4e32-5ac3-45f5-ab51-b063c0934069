import type { App } from 'vue';
import { useUserStore } from '@/stores/user';
import { createRouter, createWebHashHistory } from 'vue-router/auto';
import { routes } from 'vue-router/auto-routes';
import { setupCommonSSO } from './common-sso';
import { setupSSOToken } from './setupSSOToken';

export const router = createRouter({
  history: createWebHashHistory(import.meta.env.VITE_PUBLIC_PATH),
  routes: [

    ...routes.filter(e => e.path === '/login'),
    {
      path: '/',
      name: 'Layout',
      redirect: '/home',
      component: () => import('@/layout/layout.vue'),
      children: routes.filter(e => e.path !== '/login'),
    },

    // 虚拟路由
    {
      name: 'SSO',
      path: '/sso',
      component: () => undefined,
    },
    { path: '/:pathMatch(.*)*', name: 'NotFound', redirect: '/home' },

  ],
});

export function setupRouter(app: App) {
  app.use(router);
}

if (import.meta.env.VITE_ENV !== 'HUBEI') {
  setupCommonSSO(router);
}
else {
// 单点登录
  setupSSOToken(router);
}

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore();
  if (!userStore.isLogined && to.name !== '/login/' && to.name !== 'SSO' as any) {
    return next({ name: '/login/' });
  }
  next();
});
