<script lang="ts" setup>
import type { VNodeChild } from 'vue';
import type { BasicOrComponentOpt } from '../basic-or-component/basic-or-component.vue';

const props = defineProps<HeaderTitle2Props>();

const slots = defineSlots<HeaderTitle2Slots>();

export interface HeaderTitle2Props {
  content?: BasicOrComponentOpt;
  extra?: BasicOrComponentOpt;
}

export interface HeaderTitle2Slots {
  default?: VNodeChild;
  extra?: VNodeChild;
}
</script>

<template>
  <div class="header-title2">
    <div class="header-title2__content">
      <basic-or-component :is="slots.default ?? props.content" />
    </div>
    <div class="header-title2__extra">
      <basic-or-component :is="slots.extra ?? props.extra" />
    </div>
  </div>
</template>

<style lang="scss">
.header-title2 {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 36px;
  padding: 16px 24px;
  background-color: #1c1d1e;
}

.header-title2__content {
  flex: 1;
  font-size: 18px;
  line-height: 28px;
  color: #f6fcff;
}
</style>
