import { computedLoading } from '@/hooks/computed-loading';
import { cartesianToWgs84 } from '@x3d/all';
import { useCzScreenSpaceAction, useCzViewer } from '@x3d/vue-hooks';
import { intersects } from 'ol/format/filter';
import { Point } from 'ol/geom';
import { WfsService } from '../utils/wfs-service';
import { useLayerEffectState } from './layer-effect-state';

// WFS全局变量
export const useWfsState = createGlobalState(() => {
  const { layerInfoMap } = useLayerEffectState()!;
  const viewer = useCzViewer();

  const position = shallowRef<Cesium.Cartesian3>();

  const pickImageryLayers = shallowRef<Cesium.ImageryLayer[]>([]);

  // 点选
  useCzScreenSpaceAction(Cesium.ScreenSpaceEventType.LEFT_CLICK, async (context) => {
    const ray = viewer.value.camera.getPickRay(context.position);
    const cartesian = viewer.value.scene.pickPosition(context.position);
    if (!ray || !cartesian) {
      return;
    }
    pickImageryLayers.value = viewer.value.imageryLayers.pickImageryLayers(ray, viewer.value.scene) ?? [];
    position.value = cartesian;
  });

  const wfsServices = computed(() => {
    // 过滤出配置了WFS的图层信息
    const feasibleLayers = pickImageryLayers.value.filter(layer => layerInfoMap.get(layer)?.config?.wfs?.url);
    return feasibleLayers.map((layer) => {
      const config = layerInfoMap.get(layer)!.config!;
      return new WfsService({
        typeName: config.wfs?.typeName ?? '',
        featurePrefix: config.wfs?.featurePrefix,
        featureNS: config.wfs?.featureNS,
        url: config.wfs?.url ?? '',
      });
    });
  });

  // 点选会拾取到多个图层，界面中通过tabs去切换当前选中图层，这里记录当前选中的图层名称
  const currentWFSLayerName = ref<string>();

  watchEffect(() => {
    currentWFSLayerName.value = wfsServices.value?.[0]?.typeName;
  });

  const currentWFSService = computed(() => {
    return wfsServices.value.find(wfsService => wfsService.typeName === currentWFSLayerName.value);
  });

  const [currentPropertyKeys] = computedLoading(async () => {
    return currentWFSService.value?.getPropertyKeys();
  });

  const [currentFeature] = computedLoading(async () => {
    if (!currentWFSService.value || !position.value) {
      return;
    }
    const [longitude, latitude] = cartesianToWgs84(position.value);
    const geomKey = await currentWFSService.value.getGeomKey();
    const featureCollection = await currentWFSService.value.getFeature(intersects(geomKey!.name!, new Point([latitude, longitude]), ''));
    return featureCollection?.features?.[0];
  });

  const editting = ref(false);

  return {
    position,
    wfsServices,
    currentWFSLayerName,
    currentWFSService,
    currentFeature,
    currentPropertyKeys,
    editting,
  };
});
