<script lang="ts" setup>
import type { VNodeChild } from 'vue';
import type { BasicOrComponentOpt } from '../basic-or-component/basic-or-component.vue';

export interface HeaderTitle1Props {
  content?: BasicOrComponentOpt;
  extra?: BasicOrComponentOpt;
  contentClass?: any;
}

export interface HeaderTitle1Slots {
  default?: VNodeChild;
  extra?: VNodeChild;
}

const props = defineProps<HeaderTitle1Props>();
const slots = defineSlots<HeaderTitle1Slots>();
</script>

<template>
  <div class="header-title1">
    <div class="header-title1__content" :class="contentClass">
      <basic-or-component :is="slots.default ?? props.content" />
    </div>
    <div class="header-title1__extra">
      <basic-or-component :is="slots.extra ?? props.extra" />
    </div>
  </div>
</template>

<style lang="scss">
.header-title1 {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 36px;
  padding: 22px 24px;

  &::before {
    width: 4px;
    height: 16px;
    margin-right: 8px;
    content: '';
    background: #4176ff;
    border-radius: 307px;
  }
}

.header-title1__content {
  flex: 1;
  font-size: 20px;
  font-weight: bold;
  line-height: 28px;
  color: #f6fcff;
}
</style>
