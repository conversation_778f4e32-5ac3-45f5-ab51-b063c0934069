import type { Dayjs } from 'dayjs';
import * as Cesium from 'cesium';

import dayjs from 'dayjs';

/**
 * dayjs对象 转 Cesium.JulianDate
 */
export function julianFromDayjs(dayjs: Dayjs): Cesium.JulianDate {
  return Cesium.JulianDate.fromDate(dayjs.toDate());
}

/**
 * Cesium.JulianDate 转 dayjs对象
 */
export function julianToDayjs(julian: Cesium.JulianDate): Dayjs {
  return dayjs(Cesium.JulianDate.toDate(julian));
}
