<!-- ColorMaterialProperty 属性编辑 -->
<script lang="ts" setup>
import type { ColorMaterialPropertySerializateJSON } from '../@geovis3d/plotting';
import ColorAttribute from '../color-attribute.vue';

import { useShallowBinding } from '../hooks';

defineOptions({ name: 'ColorMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: ColorMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: ColorMaterialPropertySerializateJSON): void;
}>();

const model = ref<ColorMaterialPropertySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: (value) => {
    model.value = { ...value };
  },
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="色彩" /> -->
  <ColorAttribute v-model="model.color" label="颜色" />
</template>
