import type { Cartesian3SerializateJSON } from './cartesian3';

import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type {
  ClassificationTypeSerializateJSON,
  CornerTypeSerializateJSON,
  HeightReferenceSerializateJSON,
  ShadowModeSerializateJSON,
} from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { Cartesian3Serializate } from './cartesian3';

import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface CorridorGraphicsSerializateJSON {
  show?: boolean;
  positions?: Cartesian3SerializateJSON[];
  width?: number;
  height?: number;
  heightReference?: HeightReferenceSerializateJSON;
  extrudedHeight?: number;
  extrudedHeightReference?: HeightReferenceSerializateJSON;
  cornerType?: CornerTypeSerializateJSON;
  granularity?: number;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  classificationType?: ClassificationTypeSerializateJSON;
  zIndex?: number;
}

export type CorridorGraphicsKey = keyof CorridorGraphicsSerializateJSON;

export class CorridorGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.CorridorGraphics,
    omit?: CorridorGraphicsKey[],
    time?: Cesium.JulianDate,
  ): CorridorGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map(
        (item: Cesium.Cartesian3) => Cartesian3Serializate.toJSON(item)!,
      ),
      width: getValue('width'),
      height: getValue('height'),
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      extrudedHeight: getValue('extrudedHeight'),
      extrudedHeightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('extrudedHeightReference'))
        ?? 'NONE',
      cornerType: EnumSerializate.toJSON(Cesium.CornerType, getValue('cornerType')),
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType:
        EnumSerializate.toJSON(Cesium.ClassificationType, getValue('classificationType')) ?? 'BOTH',
      zIndex: getValue('zIndex'),
    };
  }

  static fromJSON(
    json?: CorridorGraphicsSerializateJSON,
    omit?: CorridorGraphicsKey[],
  ): Cesium.CorridorGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.CorridorGraphics({
      show: getValue('show') ?? true,
      positions: getValue('positions')?.map(item => Cartesian3Serializate.fromJSON(item)!),
      width: getValue('width'),
      height: getValue('height'),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      extrudedHeight: getValue('extrudedHeight'),
      extrudedHeightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('extrudedHeightReference'),
      ),
      cornerType: EnumSerializate.fromJSON(Cesium.CornerType, getValue('cornerType')),
      granularity: getValue('granularity'),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      classificationType: EnumSerializate.fromJSON(
        Cesium.ClassificationType,
        getValue('classificationType'),
      ),
      zIndex: getValue('zIndex'),
    });
  }
}
