import type { MaterialPropertySerializateController } from './material-property';

import { GreentideMaterialProperty } from '@/lib/@geovis3d/material';

export interface GreentideMaterialPropertySerializateJSON {
  // color?: string;
}

export default <
  MaterialPropertySerializateController<
    'GreentideMaterialProperty',
    GreentideMaterialProperty,
    GreentideMaterialPropertySerializateJSON
  >
>{
  type: 'GreentideMaterialProperty',
  hit: (property) => {
    return property instanceof GreentideMaterialProperty;
  },
  toJSON(_property, _time) {
    // time ??= Cesium.JulianDate.now();
    // const color = property?.getValue(time)?.color;
    return {
      type: 'GreentideMaterialProperty',
      params: {
        // color: ColorSerializate.toJSON(color),
      },
    };
  },
  fromJSON(_json) {
    // const color = json?.params?.color;
    // return new GreentideMaterialProperty(ColorSerializate.fromJSON(color));
    return new GreentideMaterialProperty();
  },
};
