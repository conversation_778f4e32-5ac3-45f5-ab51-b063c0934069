<!-- 业务组件 --应急资源树 -->
<script lang="ts" setup>
import type { components } from '@/genapi/elasticsearch';
import { elasticsearchResourcesGetListUsingPost, elasticsearchResourcesGetTreeListUsingPost } from '@/genapi/elasticsearch';

import { CzEntity } from '@x3d/all';
import { useCzEntityCollection } from '@x3d/vue-hooks';
import { getIconPointPlainPNG, getIconPointPNG } from './assets/export';
import CollapseTree from './collapse-tree.vue';
import DataAttributePopper from './data-attribute-popper.vue';
import { createEmergencyResource } from './hook';

defineOptions({ name: 'EmergencyResource' });

const { filterable, position, distance } = createEmergencyResource()!;

const year = ref('2022');

type TreeModel = Record<string, any> & { children?: TreeModel[] };

const params = computed(() => {
  const [longitude, latitude] = position.value ?? [];
  if (!filterable.value || !longitude || !latitude || !distance.value) {
    return undefined;
  }
  return {
    longitude,
    latitude,
    distance: distance.value,
  };
});

// 已经组合成树图的列表
const { state: treeList, execute: getTreeList, isLoading } = useAsyncState(
  async () => {
    const res = await elasticsearchResourcesGetTreeListUsingPost({
      data: {
        ...params.value,
        year: year.value,
        readCount: '1',
      },
    });
    const data = res.data?.filter(e => e.count !== 0) ?? [];
    data.forEach((item) => {
      if (item.parentId !== 0) {
        const parent = data.find(e => e.id === item.parentId);
        if (parent) {
          parent.children ??= [];
          parent.children.push(item);
        }
      }
    });
    return (data ?? []) as TreeModel[];
  },
  undefined,
  {
    immediate: true,
  },
);

const collapseTreeRef = shallowRef();
const entityEffect = useCzEntityCollection();
// 树图当前选中的资源类型ID列表
const checkedResIds = reactive(new Set<string>());
// 状态恢复
watch(
  [params, year],
  async () => {
    await nextTick();
    const keys = [...checkedResIds];
    await getTreeList();
    entityEffect.removeScope();

    await nextTick();
    collapseTreeRef.value?.treeRef.value?.forEach((e) => {
      e.setCheckedKeys([], true);
    });
    await nextTick();
    collapseTreeRef.value?.treeRef.value?.forEach((e) => {
      e.setCheckedKeys(keys, true);
    });
  },
  { deep: true },
);

const collapseValue = ref(['风险隐患']);

const datas = computed(() => {
  return [
    {
      item: {
        name: '防护目标',
      },
      icon: 'fanghu',
      tree: treeList.value?.find(e => e.name === '防护目标')?.children,
    },
    {
      item: {
        name: '风险隐患',
      },
      icon: 'fengxian',
      tree: treeList.value?.find(e => e.name === '风险隐患')?.children,
    },
    {
      item: {
        name: '应急资源',
      },
      icon: 'yingjiziyuan1',
      tree: treeList.value?.find(e => e.name === '应急资源')?.children,
    },
  ];
});

const currentAttr = ref<components['schemas']['GetOneDTODuiXiang']>();

// 上球
async function pointEffect(data: TreeModel, check: boolean) {
  const id = data.id;
  if (!check) {
    entityEffect.removeWhere(entity => entity.pointId === id);
  }
  else {
    const { data: resList } = await elasticsearchResourcesGetListUsingPost({
      data: {
        typeCode: id,
        year: year.value,
        ...params.value,
      },
    });
    resList?.forEach((item) => {
      if (!item.longitude || !item.latitude)
        return;
      const entity = new CzEntity({
        position: Cesium.Cartesian3.fromDegrees(+item.longitude, +item.latitude, 0),
        billboard: {
          image: getIconPointPNG(item.typeName!),
          width: 20,
          height: 20,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        },
      });
      entity.pointId = id;

      entity.event.on('LEFT_CLICK', async () => {
        currentAttr.value = item;
      });
      entityEffect.add(entity);
    });
  }
}

// 选中触发的事件
async function onCheckChange(data: TreeModel, check: boolean) {
  check ? checkedResIds.add(data.id!) : checkedResIds.delete(data.id!);
  // 每次选中或者取消的时候 就关闭资源弹窗
  currentAttr.value = undefined;
  pointEffect(data, check);
}

const collapseAttr = {
  'accordion': true,
  'modelValue': collapseValue.value,
  'onUpdate:modelValue': (e: any) => (collapseValue.value = e),
};
const treeAttr = computed(() => ({
  defaultExpandedKeys: [...checkedResIds],
  showCheckbox: true,
  filterNodeMethod: (value, item) => {
    return item.name !== '应急专家' && item.name !== '应急物资仓库' && item.name !== '应急物资';
  },
  nodeKey: 'id',
  checkStrictly: true,
  props: { label: 'name' },
  onCheckChange,
}));
</script>

<template>
  <drag-card title="应急一张图" class="emergency-resource" :initial-right="$vh(40)" :initial-top="$vh(100)">
    <el-radio-group v-model="year" class="ml-4">
      <el-radio value="2023">
        2024年
      </el-radio>
      <el-radio value="2022">
        2023年
      </el-radio>
      <el-radio value="2021">
        2022年
      </el-radio>
    </el-radio-group>
    <el-scrollbar v-loading="isLoading" class="right-resource h-700px!" wrap-class="p-10px">
      <CollapseTree
        ref="collapseTreeRef"
        :collapse="collapseAttr"
        :data="datas"
        :tree="treeAttr"
      >
        >
        <template #title="data">
          <span text="16px" p="x-20px"> {{ data?.item?.name }}</span>
        </template>

        <template #node="item">
          <div
            class="tree-item"
            @click="() => !item.data.children?.length && (item.node.checked = !item.node.checked)"
          >
            <img
              v-if="!item.data.children?.length"
              :src="getIconPointPlainPNG(item.data.name) "
            >
            <span> {{ item.data.name }}({{ item.data.count }}) </span>
            <el-checkbox
              v-if="!item.data.children?.length"
              :model-value="item.node.checked"
              @click.stop=""
              @update:model-value="(checked) => (item.node.checked = !!checked)"
            />
          </div>
        </template>
      </CollapseTree>
    </el-scrollbar>
    <DataAttributePopper v-model="currentAttr" />
  </drag-card>
</template>

<style scoped lang="scss">
  :deep().el-tree {
  .el-tree-node__content {
    display: flex;
    align-items: center;
    height: 36px;

    .el-tree-node__label {
      flex: 1;
    }

    > .el-checkbox {
      display: none;
    }
  }

  .tree-item {
    display: flex;
    flex: 1;
    align-items: center;
    padding-right: 30px;
    cursor: pointer;

    > img {
      width: 16px;
      height: 16px;
      margin-right: 12px;
      filter: grayscale(100%) brightness(200%);
    }

    > span {
      flex: 1;
    }
  }
}

:deep().el-collapse {
  --el-collapse-border-color: transparent;
  --el-collapse-header-height: auto;

  .el-collapse-item__header {
    margin-top: 8px;

    > .el-icon {
      display: none;
    }
  }
}
</style>
