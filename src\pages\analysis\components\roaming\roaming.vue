<!-- 漫游 -->
<script lang="ts" setup>
import { cartesianToCartographic } from '@x3d/all';
import { useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'Roaming' });

const viewer = useCzViewer();

// 监听键盘事件
useEventListener('keydown', (e) => {
  const position = viewer.value.camera.position;
  const detail = viewer.value.scene.clampToHeight(position);
  const clampHeight = detail ? cartesianToCartographic(detail).height : 0;
  const diff = cartesianToCartographic(position).height - clampHeight;
  const distance = diff / 50;
  if (['ArrowUp', 'w'].includes(e.key)) {
    viewer.value.camera.moveForward(distance);
  }
  if (['ArrowDown', 's'].includes(e.key)) {
    viewer.value.camera.moveBackward(distance);
  }
  if (['ArrowLeft', 'a'].includes(e.key)) {
    viewer.value.camera.moveLeft(distance);
  }
  if (['ArrowRight', 'd'].includes(e.key)) {
    viewer.value.camera.moveRight(distance);
  }
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="漫游"
    class="h-188px w-400px"
  >
    <div p="20px">
      输入↑(W),→(D),↓(S),←(A)控制相机平移
    </div>
  </drag-card>
</template>
