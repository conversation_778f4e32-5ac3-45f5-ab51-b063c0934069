<script lang="ts" setup>
import LayerManager from '@/widget/layer/layer-manager.vue';
import { createCesiumViewer } from './createCesiumViewer';

import LayoutContextmenu from './layout-contextmenu.vue';
import UserSetting from './user-setting.vue';

defineOptions({ name: 'Layout' });
const elRef = shallowRef<HTMLDivElement>();
const viewer = createCesiumViewer(elRef);

const windowsize = useWindowSize();

const isMobile = computed(() => windowsize.width.value < 768);
</script>

<template>
  <div id="cesium-container" ref="elRef" class="absolute inset-0 z-0 of-hidden" flex="~" />
  <div v-if="viewer" id="layout-container">
    <UserSetting />
    <layout-header v-if="!isMobile" />
    <div id="layout-main">
      <div id="layout-left" />
      <div id="layout-content">
        <search-tool />
        <LayerManager v-if="viewer?.scene" v-slot="{ onClick }">
          <el-button h="60px!" px="21px!" position="absolute bottom-60px left-20px" class="layer-button" @click.stop="onClick">
            <el-icon class="i-custom:layer" mr="10px" text="25px!" />
            <span text="20px">
              图层
            </span>
          </el-button>
        </LayerManager>
        <bottom-nav v-if="!isMobile" />
        <div id="layout-content-right" />
      </div>
      <div id="layout-right" />
    </div>
    <router-view />

    <layout-footer v-if="!isMobile" />
    <LayoutContextmenu />
  </div>
</template>

<style lang="scss">
.cesium-viewer-bottom {
  display: none !important;
}

#layout-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
}

#layout-main {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#layout-left,
#layout-left-sub,
#layout-content,
#layout-right-sub,
#layout-right {
  position: relative;
  pointer-events: none;

  > * {
    pointer-events: initial;
  }
}

#layout-content {
  position: relative;
  display: flex;
  flex: 1;
  padding-top: 84px;

  &-right {
    position: absolute;
    right: 0;
    margin-right: 15px;
  }
}

/* stylelint-disable-next-line no-duplicate-selectors */
#layout-content-right {
  display: flex;
  flex-direction: column;
  align-items: end;
}

.layer-button {
  height: 40px;
  padding: 6px 12px;
  font-size: 24px;
  color: #fff;
  pointer-events: auto;
  background: var(--el-bg-color);
  border-radius: 8px;

  --el-button-hover-bg-color: var(--el-bg-color) !important;

  &.active {
    background-color: rgb(29 30 32 / 100%);
    border-color: var(--el-button-active-border-color);
    box-shadow: 0 0 2px var(--el-button-active-border-color);
  }
}
</style>
