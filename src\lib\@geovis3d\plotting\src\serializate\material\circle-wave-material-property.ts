import type { MaterialPropertySerializateController } from './material-property';

import { CircleWaveMaterialProperty } from '@/lib/@geovis3d/material';

import { ColorSerializate } from '../color';

export interface CircleWaveMaterialPropertySerializateJSON {
  color?: string;
  duration?: number;
  count?: number;
  gradient?: number;
}

export default <
  MaterialPropertySerializateController<
    'CircleWaveMaterialProperty',
    CircleWaveMaterialProperty,
    CircleWaveMaterialPropertySerializateJSON
  >
>{
  type: 'CircleWaveMaterialProperty',
  hit: (property) => {
    return property instanceof CircleWaveMaterialProperty;
  },
  toJSON: (property, time) => {
    const { color, duration, count, gradient } = property?.getValue(time) ?? {};
    return {
      type: 'CircleWaveMaterialProperty',
      params: {
        color: ColorSerializate.toJSON(color),
        duration,
        count: count || 1,
        gradient,
      },
    };
  },
  fromJSON(json) {
    const { color, duration, count, gradient } = json?.params ?? {};
    return new CircleWaveMaterialProperty({
      color: ColorSerializate.fromJSO<PERSON>(color),
      duration,
      count: count || 1,
      gradient,
    });
  },
};
