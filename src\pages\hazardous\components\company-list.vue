<!-- 左侧列表视图 -->
<script lang="ts" setup>
import type {
  components,
} from '@/genapi/production';
import {
  productionHazardousChemicalsCompanyListPageUsingPost,
} from '@/genapi/production';
import { usePage } from '@/hooks/use-page';
import $3DTiles from '../3dtiles';

import {
  COMPANY_RISK_RANK_ENUM,
  COMPANY_RISK_RANK_ENUM_COLORS,
  COMPANY_RISK_RANK_ENUM_OPTION,
  COMPANY_TYPE_ENUM,
} from '../enum';
import { useCompanyActiveInject } from '../hooks';
import LeftListModal from './left-list-modal.vue';

defineOptions({ name: 'CompanyList' });

const companyActive = useCompanyActiveInject();

function isActiveComputer(companyCode: string) {
  return [companyActive.twoCompanyCode, companyActive.threeCompanyCode].includes(companyCode);
}

const isMounted = useMounted();

const [expand, toggle] = useToggle();

// 企业类型数据
const companyType = Object.keys(COMPANY_TYPE_ENUM).map(value => ({
  label: COMPANY_TYPE_ENUM[value],
  value,
}));

// 查询参数
const query = reactive<components['schemas']['QiYeFenYeChaXunCanShu']>({
  companyName: undefined,
  companyTypeList: [],
  hazardRankList: [],
});

// 具有三维倾斜摄影的的企业的企业编码
const hasTilseCompanyCodes = $3DTiles.filter(e => e.tilesets?.length).map(e => e.companyCode);

// 分页
const { records, execute, pageSize, currentPage, isLoading, total } = usePage({
  immediate: true,
  initPageSize: 100,
  async fetch({ currentPage, pageSize }) {
    const { data } = await productionHazardousChemicalsCompanyListPageUsingPost({
      data: {
        current: currentPage,
        size: pageSize,
        query,
      },
    });

    // 排序  具有三维倾斜摄影的公司排在最前面
    // 海南福山油田勘探开发有限责任公司 排在第一位！
    const records = data?.records?.sort((a, b) => {
      if (a.companyName === '海南福山油田勘探开发有限责任公司') {
        return -3;
      }
      else if (a.companyName === '海南国盛石油有限公司') {
        return -2;
      }
      return hasTilseCompanyCodes.includes(b.companyCode!) ? 1 : -1;
    });
    return {
      records: records ?? [],
      total: data?.total ?? 0,
    };
  },
});

watch(
  () => query,
  () => {
    execute({ currentPage: 1 });
  },
  { deep: true },
);
</script>

<template>
  <basic-card v-if="isMounted" class="left-list" title="危化品企业">
    <el-form p="10px">
      <el-form-item>
        <el-input
          v-model="query.companyName"
          clearable
          class="flex-1"
          placeholder="请输入关键字"
          @change="execute({ currentPage: 1 })"
        />
      </el-form-item>
      <template v-if="expand">
        <el-form-item>
          <el-select-v2
            v-model="query.companyTypeList"
            class="w-100%"
            placeholder="类型"
            :options="companyType"
            clearable
            multiple
            @change="execute({ currentPage: 1 })"
          />
        </el-form-item>
        <el-form-item>
          <el-select-v2
            v-model="query.hazardRankList"
            class="w-100%"
            placeholder="危险源等级"
            :options="COMPANY_RISK_RANK_ENUM_OPTION"
            clearable
            multiple
            @change="execute({ currentPage: 1 })"
          />
        </el-form-item>
      </template>
      <div class="flex cursor-pointer justify-center text-12px" @click="toggle()">
        {{ expand ? '收起' : '展开' }}更多筛选 <el-icon v-if="expand" class="i-material-symbols:keyboard-arrow-up-rounded" />
        <el-icon v-else class="i-material-symbols:keyboard-arrow-down-rounded" />
      </div>
    </el-form>
    <el-divider class="mb-10px !mt-4px" />
    <ListScrollView
      wrap-class="px-10px"
      class="flex-1"
      node-key="id"
      :empty="!records.length"
      :list="records"
      :loading="isLoading"
    >
      <template #default="{ data }">
        <div class="list-item" :class="{ active: isActiveComputer(data?.companyCode) }">
          <div class="header">
            <span class="name">
              {{ data?.companyName }}
            </span>
            <div
              class="level"
              :style="{ color: COMPANY_RISK_RANK_ENUM_COLORS[data.companyRiskRank!] }"
            >
              {{ COMPANY_RISK_RANK_ENUM[data.companyRiskRank!] || '未知' }}
            </div>
          </div>
          <div class="address">
            <el-icon class="i-tabler:map-pin-filled" />
            <el-text truncated>
              {{ data.addressRegistry || '暂无' }}
            </el-text>
          </div>
          <el-divider m="y-10px!" />
          <div class="functional-row">
            <el-button
              type="primary"
              size="small"
              @click="companyActive.detailCompanyCode = data.companyCode"
            >
              <el-icon class="i-tabler:file-text" />
              <span>详情</span>
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="companyActive.twoCompanyCode = data.companyCode"
            >
              <el-icon class="i-tabler:hexagon-2-filled" />
              <span>二维</span>
            </el-button>
            <el-button
              v-if="hasTilseCompanyCodes.includes(data.companyCode)"
              size="small"
              type="primary"
              @click="companyActive.threeCompanyCode = data.companyCode"
            >
              <el-icon class="i-tabler:hexagon-3-filled" />
              <span>三维</span>
            </el-button>
          </div>
        </div>
      </template>
    </ListScrollView>
    <el-pagination v-model:page-size="pageSize" v-model:current-page="currentPage" flex="~ justify-center items-center" :total="total" />
  </basic-card>
  <LeftListModal />
</template>

<style scoped lang="scss">
  .left-list {
  position: absolute;
  top: 50%;
  left: 60px;
  display: flex;
  flex-direction: column;
  width: 350px;
  height: 800px;
  overflow: hidden;
  pointer-events: initial;
  transform: translateY(-50%);
}

.el-form-item {
  margin-bottom: 10px;
}

.list-item {
  display: flex;
  flex-direction: column;
  padding: 12px 20px 6px;
  margin-bottom: 12px;
  overflow: hidden;
  font-size: 16px;
  background: rgb(#292b2e, 60%);
  border: 2px solid #f7f7f738;
  border-radius: 2px;
  box-shadow: inset 0 1px 20px 0 rgb(#292b2e, 34%);

  &.active {
    background: rgb(1 17 62 / 0%);
    border: 2px solid rgb(247 247 247 / 43.8%);
  }

  .header {
    display: flex;

    .name {
      flex: 1;
      padding-right: 10px;
      line-height: 20px;
    }

    .level {
      font-size: 12px;
      line-height: 22px;
    }
  }

  .address {
    display: flex;
    align-items: center;
    padding: 6px 0;
    font-size: 12px;

    .el-icon {
      margin-right: 4px;
      font-size: 14px;
    }
  }

  .functional-row {
    display: flex;
    justify-content: center;
  }
}
</style>
