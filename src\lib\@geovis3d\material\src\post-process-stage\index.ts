/**
 * Cesium屏幕后处理
 */
import * as Cesium from 'cesium';

import fogGLSL from './glsl/fog.glsl?raw';
import rainGLSL from './glsl/rain.glsl?raw';
import snowGLSL from './glsl/snow.glsl?raw';

/**
 * 屏幕后处理-下雪场景
 *
 * @example
 * ```ts
 * var snow = createSnowStage();
 *
 * //add
 * viewer.scene.postProcessStages.add(snow);
 *
 * //remove
 * viewer.scene.postProcessStages.remove(snow);
 * ```
 */
export function createSnowStage() {
  const snow = new Cesium.PostProcessStage({
    name: 'czm_snow',
    fragmentShader: snowGLSL,
  });
  return snow;
}

export interface CreateRainStageOptions {
  /**
   * 粒子速度
   * @default 70
   */
  speed?: number;
}

/**
 * 屏幕后处理-下雨场景
 *
 * @example
 * ```ts
 * var snow = createRainStage();
 *
 * //add
 * viewer.scene.postProcessStages.add(snow);
 *
 * //remove
 * viewer.scene.postProcessStages.remove(snow);
 * ```
 */
export function createRainStage(options?: CreateRainStageOptions) {
  const rain = new Cesium.PostProcessStage({
    name: 'czm_rain',
    fragmentShader: rainGLSL,
    uniforms: {
      speed: options?.speed ?? 70,
    },
  });
  return rain;
}

/**
 * 雾场景参数
 */
export interface CreateFogStageOptions {
  /**
   * 雾强度 0-1
   * @default 0.5
   */
  potency?: number;
}

/**
 * 屏幕后处理-雾场景
 *
 * @example
 * ```ts
 * var snow = createRainStage();
 *
 * //add
 * viewer.scene.postProcessStages.add(snow);
 *
 * //remove
 * viewer.scene.postProcessStages.remove(snow);
 * ```
 */
export function createFogStage(options?: CreateFogStageOptions) {
  const rain = new Cesium.PostProcessStage({
    name: 'czm_fog',
    fragmentShader: fogGLSL,
    uniforms: {
      potency: options?.potency ?? 0.8,
    },
  });
  return rain;
}
