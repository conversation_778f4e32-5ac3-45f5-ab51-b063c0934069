{"name": "3d-data-system", "type": "module", "version": "0.1.0", "private": true, "engines": {"node": ">=18.16.x"}, "scripts": {"dev": "vite", "dev:hainan": "vite --host --mode=hainan", "dev:henan": "vite --host --mode=henan", "dev:heilongjiang: ": "vite --host --mode=heilongjiang", "build": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build && npm run dist-zip", "build:hainan": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build --mode=hainan && npm run dist-zip", "build:hubei": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build --mode=hubei && npm run dist-zip", "build:heilongjiang": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build --mode=heilongjiang && npm run dist-zip", "build:henan": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build --mode=henan && npm run dist-zip", "build:liaoning": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build --mode=liaoning && npm run dist-zip", "build:sichuan": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build --mode=sichuan && npm run dist-zip", "build:fujian": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vite build --mode=fujian && npm run dist-zip", "preview": "vite preview", "openapi": "tsx ./internals/openapi.ts && eslint ./src/genapi --fix", "lint": "pnpm run lint:eslint && pnpm run lint:stylelint", "lint:eslint": "eslint . --fix", "lint:stylelint": "stylelint . --fix", "lint:tsc": "vue-tsc --noEmit --emitDeclarationOnly false", "taze": "taze -w -r major minor patch", "set:spacesize": "cross-env NODE_OPTIONS=--max_old_space_size=18192", "dist-zip": "tsx ./internals/dist-zip.ts"}, "dependencies": {"@codemirror/lang-json": "^6.0.1", "@codemirror/lang-xml": "^6.1.0", "@codemirror/theme-one-dark": "^6.1.2", "@ctrl/tinycolor": "^4.1.0", "@turf/turf": "^7.2.0", "@types/bezier-js": "^4.1.3", "@types/geojson": "^7946.0.16", "@types/wellknown": "^0.5.8", "@vueuse/components": "^13.0.0", "@vueuse/core": "^13.0.0", "@vueuse/shared": "^13.0.0", "@x3d/all": "1.0.0-beta.44", "@x3d/vue-base": "1.0.0-beta.44", "@x3d/vue-form": "1.0.0-beta.44", "@x3d/vue-hooks": "1.0.0-beta.44", "animation-timeline-js": "^2.3.5", "axios": "^1.8.3", "bezier-js": "^6.1.4", "cesium": "^1.127.0", "cesium-navigation-es6": "^3.0.9", "codemirror": "^6.0.1", "color": "^5.0.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.6", "fast-xml-parser": "^5.0.9", "flv.js": "^1.6.2", "gcoord": "^1.0.7", "geojson": "^0.5.0", "geoserver-helper": "^0.0.37", "html2canvas-pro": "^1.5.8", "jsencrypt": "^3.3.2", "jspdf": "^3.0.0", "mvt-imagery-provider": "^1.0.3", "ol": "^10.4.0", "pinia": "2.3.0", "qs": "^6.14.0", "vue": "^3.5.13", "vue-codemirror": "^6.1.1", "vue-cookies": "^1.8.6", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0", "vue3-seamless-scroll": "^3.0.2", "wellknown": "^0.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@iconify-json/material-symbols": "^1.2.16", "@iconify-json/tabler": "^1.2.17", "@tsconfig/node20": "^20.1.4", "@types/archiver": "^6.0.3", "@types/qs": "^6.9.18", "@unocss/eslint-plugin": "^66.0.0", "@vitejs/plugin-legacy": "^6.0.2", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "@xiankq/openapi-typescript-expand": "^1.0.8", "archiver": "^7.0.1", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "eslint": "^9.22.0", "eslint-plugin-format": "^1.0.1", "https-proxy-agent": "^7.0.6", "minimist": "^1.2.8", "pnpm": "^10.6.4", "postcss-html": "^1.8.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sass": "1.83.1", "socks-proxy-agent": "^8.0.5", "stylelint": "^16.16.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^37.0.0", "stylelint-config-standard-scss": "^14.0.0", "svgo": "^3.3.2", "taze": "^19.0.2", "tsx": "^4.19.3", "typescript": "^5.8.2", "unocss": "^66.0.0", "unplugin-auto-import": "^19.1.1", "unplugin-cesium": "^2.1.1", "unplugin-element-plus": "^0.9.1", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.4.1", "unplugin-vue-router": "^0.12.0", "vite": "6.0.7", "vite-plugin-commonjs": "^0.10.4", "vue-tsc": "^2.2.8"}}