import type { AllGeoJSON } from '@turf/turf';
import type { IAreaItem } from './types';
import { layerInfoGetSystemSettingsByUserCodeUsingGet } from '@/genapi/cimapi';
import { computedLoading } from '@/hooks/computed-loading';
import { toStaticFilePath } from '@/utils/resolve-path';
import { useUserStore } from './user';

// 系统设置
export const useSettingStore = defineStore('setting', () => {
  const userStore = useUserStore();
  const { state: setting, execute: refreshSetting, isLoading: settingLoadinng } = useAsyncState(async () => {
    if (!userStore.userName) {
      return {};
    }
    const { data = {} } = await layerInfoGetSystemSettingsByUserCodeUsingGet({
      params: {
        userCode: userStore.userName,
      },
    });
    if (data?.resolution) {
      data.resolution = +data.resolution;
    }
    return data;
  }, {}, {
    resetOnExecute: false,
    immediate: false,
  });

  const activeMenus = ref<string[]>([]);
  watchImmediate(() => setting.value?.menus, (menus) => {
    // 固定首页的标题
    const homeTitle = '首页';
    if (menus) {
      const menuList = menus.split(',');
      if (!menuList.includes(homeTitle)) {
        activeMenus.value = [homeTitle, ...menuList];
      } else {
        activeMenus.value = menuList;
      }
    } else {
      // 如果没有菜单设置，至少包含首页
      activeMenus.value = [homeTitle];
    }
  });

  watchImmediate(() => userStore.userName, () => {
    refreshSetting();
  });


  // 获取全国所有省、市、县、区列表数据
  const [areaList, areaListLoading] = computedLoading(async () => {
    const { data } = await axios.get(toStaticFilePath(`/bound/all.json`));
    data.forEach((item: any) => {
      item.label = item.name;
      item.value = item.adcode;
      item.children = data.filter((e: any) => e.parent === item.adcode);
    });
    return [data.find((e: any) => e.parent === null)] as IAreaItem[];
  });

  const geojson = computedAsync(async () => {
    if (!setting.value?.defaultUnit) {
      return {} as AllGeoJSON;
    }
    else {
      const { data = {} } = await axios.get(toStaticFilePath(`/bound/${setting.value.defaultUnit}.json`));
      return data as AllGeoJSON;
    }
  }, {} as AllGeoJSON);
  return {
    /**
     * 系统设置数据
     */
    setting,
    refreshSetting,
    settingLoadinng,
    areaList,
    areaListLoading,
    geojson,
    activeMenus,
  };
});
