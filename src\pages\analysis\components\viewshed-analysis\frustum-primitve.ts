import type { Primitive, ShadowMap } from 'cesium';
import * as Cesium from 'cesium';
import getPostStageFragmentShader from './shader';

/**
 * 用于定义 FrustumPrimitive 的配置选项
 */
export interface FrustumPrimitiveOptions {
  /**
   * Cesium 场景实例
   */
  scene: Cesium.Scene;
  /**
   * 视锥体起始位置
   */
  position: Cesium.Cartesian3;
  /**
   * 视锥体结束位置
   */
  endPosition: Cesium.Cartesian3;
  /**
   * 垂直视场角（可选，默认为 60 度）
   */
  verticalFov?: number;
  /**
   * 水平视场角（可选，默认为 120 度）
   */
  horizontalFov?: number;
  /**
   * 视锥体轮廓颜色（可选，默认为黄色）
   */
  outlineColor?: Cesium.Color;
  /**
   * 可见区域颜色（可选，默认为透明白色）
   */
  visibleColor?: Cesium.Color;
  /**
   * 不可见区域颜色（可选，默认为半透明黑色）
   */
  invisibilityColor?: Cesium.Color;
}

/**
 * 表示一个视锥体图元，用于在 Cesium 场景中绘制视锥体
 */
export class FrustumPrimitive extends Cesium.Primitive {
  /**
   * 创建一个新的 FrustumPrimitive 实例
   * @param options - 配置选项
   */
  constructor(options: FrustumPrimitiveOptions) {
    const {
      verticalFov = 60,
      horizontalFov = 120,
      outlineColor = Cesium.Color.YELLOW,
      visibleColor = Cesium.Color.WHITE.withAlpha(0),
      invisibilityColor = Cesium.Color.BLACK.withAlpha(0.5),
    } = options;
    super();
    this.scene = options.scene;
    this.camera = new Cesium.Camera(this.scene);
    this.position = options.position;
    this.endPosition = options.endPosition;
    this.verticalFov = verticalFov;
    this.horizontalFov = horizontalFov;
    this.outlineColor = outlineColor;
    this.visibleColor = visibleColor;
    this.invisibilityColor = invisibilityColor;
    // @ts-expect-error ignore
    this.shadowMap = new Cesium.ShadowMap({
      context: (this.scene as any).context,
      lightCamera: this.camera,
      enabled: true,
      isPointLight: false,
      pointLightRadius: 10000.0,
      cascadesEnabled: false,
      size: 2048,
      softShadows: false,
    });
  }

  /**
   * Cesium 场景实例
   */
  scene: Cesium.Scene;

  /**
   * 相机实例
   */
  camera: Cesium.Camera;

  /**
   * 视锥体起始位置
   */
  position: Cesium.Cartesian3;

  /**
   * 视锥体结束位置
   */
  endPosition: Cesium.Cartesian3;

  /**
   * 垂直视场角
   */
  verticalFov: number;

  /**
   * 水平视场角
   */
  horizontalFov: number;

  /**
   * 视锥体轮廓颜色
   */
  outlineColor: Cesium.Color;

  /**
   * 可见区域颜色
   */
  visibleColor: Cesium.Color;

  /**
   * 不可见区域颜色
   */
  invisibilityColor: Cesium.Color;

  /**
   * 阴影贴图实例
   */
  shadowMap: ShadowMap;

  /**
   * 计算视锥体的方向向量
   */
  get direction(): Cesium.Cartesian3 {
    const direction = Cesium.Cartesian3.subtract(this.endPosition, this.position, new Cesium.Cartesian3());
    return Cesium.Cartesian3.normalize(direction, direction);
  };

  /**
   * 计算视锥体的长度
   */
  get distance() {
    return Cesium.Cartesian3.distance(this.position, this.endPosition);
  }

  /**
   * 轮廓图元实例
   */
  outlinePrimitive: Primitive | undefined;

  /**
   * 更新相机设置
   */
  updateCamera() {
    const frustum: any = this.camera.frustum;
    // 视锥体近平面
    frustum.near = 0.001 * this.distance;
    // 视锥体远平面
    frustum.far = this.distance;
    // 视锥体张角
    frustum.fov = Cesium.Math.toRadians(this.verticalFov);
    // 视锥体宽高比
    frustum.aspectRatio = Math.tan(Cesium.Math.toRadians(this.horizontalFov) * 0.5) / Math.tan(Cesium.Math.toRadians(this.verticalFov) * 0.5);

    // 如果水平视角大于垂直视角，则使用水平视角作为视场角度
    if (this.horizontalFov > this.verticalFov) {
      frustum.fov = Cesium.Math.toRadians(this.horizontalFov);
    }
    this.camera.setView({
      destination: this.position.clone(),
      orientation: {
        up: this.direction.clone(),
        direction: this.direction.clone(),
      },
    });
  }

  /**
   * 更新轮廓图元
   */
  updatePrimitive() {
    const outlineGeometry = createOutLineGeometry({
      distance: this.distance,
      horizontalFov: this.horizontalFov,
      verticalFov: this.verticalFov,
    });
    if (this.outlinePrimitive) {
      this.scene.primitives.remove(this.outlinePrimitive);
    }
    this.outlinePrimitive = new Cesium.Primitive({
      allowPicking: false,
      modelMatrix: this.camera.inverseViewMatrix,
      asynchronous: false,
      shadows: Cesium.ShadowMode.DISABLED,
      geometryInstances: new Cesium.GeometryInstance({
        geometry: outlineGeometry,
        attributes: {
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(this.outlineColor),
        },
      }),
      appearance: new Cesium.PerInstanceColorAppearance({
        translucent: false,
        flat: false,
      }),
    });

    this.scene.primitives.add(this.outlinePrimitive);
  }

  /**
   * 后处理阶段实例
   * @internal
   */
  private postProcessStage: Cesium.PostProcessStage | undefined;

  /**
   * 更新后处理阶段
   */
  updateStage() {
    if (this.postProcessStage) {
      return;
    }

    const scratchTexelStepSize = new Cesium.Cartesian2();
    const shadowMap: any = this.shadowMap;
    const bias = shadowMap._primitiveBias;
    const uniformMap = {
      shadowMap_texture: () => {
        return shadowMap._shadowMapTexture;
      },
      shadowMap_matrix: () => {
        return shadowMap._shadowMapMatrix;
      },
      shadowMap_frontColor: () => {
        const vColor = this.visibleColor;
        return Cesium.Cartesian4.fromColor(vColor);
      },
      shadowMap_backColor: () => {
        const hColor = this.invisibilityColor;
        return Cesium.Cartesian4.fromColor(hColor);
      },
      shadowMap_Far: () => {
        return shadowMap._lightCamera.frustum.far;
      },
      shadowMap_lightheadingEC: () => {
        return shadowMap._lightheadingEC;
      },
      shadowMap_lightPositionEC: () => {
        return shadowMap._lightPositionEC;
      },
      shadowMap_texelSizeDepthBiasAndNormalShadingSmooth: () => {
        const texelStepSize = scratchTexelStepSize;
        texelStepSize.x = 1.0 / shadowMap._textureSize.x;
        texelStepSize.y = 1.0 / shadowMap._textureSize.y;

        return Cesium.Cartesian4.fromElements(
          texelStepSize.x,
          texelStepSize.y,
          bias.depthBias,
          bias.normalShadingSmooth,
          uniformMap.combinedUniforms1,
        );
      },
      shadowMap_normalOffsetScaleDistanceMaxDistanceAndDarkness() {
        return Cesium.Cartesian4.fromElements(
          bias.normalOffsetScale,
          shadowMap._distance,
          shadowMap.maximumDistance,
          shadowMap._darkness,
          this.combinedUniforms2,
        );
      },

      combinedUniforms1: new Cesium.Cartesian4(),
      combinedUniforms2: new Cesium.Cartesian4(),
    };
    const fshader = getPostStageFragmentShader(shadowMap, false);
    this.postProcessStage = new Cesium.PostProcessStage({
      fragmentShader: fshader,
      uniforms: uniformMap,
    });
    this.scene.postProcessStages.add(this.postProcessStage);
  }

  /**
   * 更新视锥体图元
   */
  update() {
    if (this._isDestroyed || this.distance === 0) {
      return;
    }
    // eslint-disable-next-line prefer-rest-params
    const frameState = arguments[0];
    frameState.shadowMaps.push(this.shadowMap);

    this.updateCamera();
    this.updateStage();
    this.updatePrimitive();
  }

  /**
   * 是否已销毁
   * @internal
   */
  private _isDestroyed = false;

  /**
   * 检查是否已销毁
   */
  isDestroyed(): boolean {
    return this._isDestroyed;
  }

  /**
   * 销毁视锥体图元
   */
  destroy() {
    this._isDestroyed = true;
    this.scene.primitives.contains(this.outlinePrimitive) && this.scene.primitives.remove(this.outlinePrimitive);
    this.postProcessStage && this.scene.postProcessStages.contains(this.postProcessStage) && this.scene.postProcessStages.remove(this.postProcessStage);
  }
}

/**
 * 创建视锥体轮廓几何体
 */
function createOutLineGeometry(
  options: {
    /**
     * 水平视场角
     */
    horizontalFov: number;
    /**
     * 垂直视场角
     */
    verticalFov: number;
    /**
     * 视锥体长度
     */
    distance: number;
  },
) {
  const positions = new Float32Array(633);

  const horizontalAngle = Cesium.Math.toRadians(options.horizontalFov);
  const verticalAngle = Cesium.Math.toRadians(options.verticalFov);
  const halfVerticalTan = Math.tan(0.5 * verticalAngle);
  const halfVerticalDistance = options.distance * halfVerticalTan;
  let positionIndex = 0;
  const p = positions;

  p[positionIndex++] = 0;
  p[positionIndex++] = 0;
  p[positionIndex++] = 0;

  const startAngle = Math.PI - 0.5 * horizontalAngle;
  const angleStep1 = horizontalAngle / 4;
  const angleStep2 = horizontalAngle / 20;

  for (let segment = 0; segment < 5; ++segment) {
    const currentAngle = startAngle + segment * angleStep1;
    const adjustedVerticalDistance = halfVerticalDistance / (options.distance / Math.cos(currentAngle));
    const angleAdjustment = Math.atan(adjustedVerticalDistance);
    const startInnerAngle = -angleAdjustment;
    const innerAngleStep = angleAdjustment / 10;

    for (let innerSegment = 0; innerSegment < 21; ++innerSegment) {
      const innerAngle = startInnerAngle + innerSegment * innerAngleStep;
      p[positionIndex++] = options.distance * Math.cos(innerAngle) * Math.sin(currentAngle);
      p[positionIndex++] = options.distance * Math.sin(innerAngle);
      p[positionIndex++] = options.distance * Math.cos(innerAngle) * Math.cos(currentAngle);
    }
  }

  for (let segment = 0; segment < 21; ++segment) {
    const currentAngle = startAngle + segment * angleStep2;
    const adjustedVerticalDistance = halfVerticalDistance / (options.distance / Math.cos(currentAngle));
    const angleAdjustment = Math.atan(adjustedVerticalDistance);
    const startInnerAngle = -angleAdjustment;
    const innerAngleStep = angleAdjustment / 2;

    for (let innerSegment = 0; innerSegment < 5; ++innerSegment) {
      const innerAngle = startInnerAngle + innerSegment * innerAngleStep;
      p[positionIndex++] = options.distance * Math.cos(innerAngle) * Math.sin(currentAngle);
      p[positionIndex++] = options.distance * Math.sin(innerAngle);
      p[positionIndex++] = options.distance * Math.cos(innerAngle) * Math.cos(currentAngle);
    }
  }

  const attributes = new Cesium.GeometryAttributes();
  attributes.position = new Cesium.GeometryAttribute({
    componentDatatype: Cesium.ComponentDatatype.DOUBLE,
    componentsPerAttribute: 3,
    values: positions,
  });

  const indices = new Uint16Array(408);
  const t = indices;
  let index = 0;
  t[index++] = 0;
  t[index++] = 1;
  t[index++] = 0;
  t[index++] = 21;
  t[index++] = 0;
  t[index++] = 85;
  t[index++] = 0;
  t[index++] = 105;

  let outerIndex = 1;
  for (let n = 0; n < 5; ++n) {
    for (let a = 0; a < 20; ++a) {
      t[index++] = outerIndex++;
      t[index++] = outerIndex;
    }
    outerIndex++;
  }

  for (let s = 0; s < 20; ++s) {
    for (let l = 0; l < 5; ++l) {
      t[index++] = outerIndex;
      t[index++] = outerIndex++ + 5;
    }
  }

  return new Cesium.Geometry({
    attributes,
    indices,
    primitiveType: Cesium.PrimitiveType.LINES,
    boundingSphere: Cesium.BoundingSphere.fromVertices(positions as unknown as number[]),
  });
}
