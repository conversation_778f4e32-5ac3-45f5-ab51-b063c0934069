<!-- VerticalOriginAttribute -->
<script lang="ts" setup>
import type { VerticalOriginSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'VerticalOriginAttribute' });

const props = defineProps<{
  modelValue?: VerticalOriginSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: VerticalOriginSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-form-item :label="label">
    <div class="vertical-origin-attribute">
      <div
        class="basic-vertical-origin-item"
        :class="{
          'basic-vertical-origin-item--active': model == 'BASELINE',
        }"
        title="垂直参照-基线"
        @click="model = 'BASELINE'"
      >
        <IconParkTextUnderline />
      </div>
      <div
        class="basic-vertical-origin-item"
        :class="{
          'basic-vertical-origin-item--active': model === 'BOTTOM',
        }"
        title="垂直参照-下"
        @click="model = 'BOTTOM'"
      >
        <IconParkAlignBottom />
      </div>
      <div
        class="basic-vertical-origin-item"
        :class="{
          'basic-vertical-origin-item--active': model === 'CENTER',
        }"
        title="垂直参照-中"
        @click="model = 'CENTER'"
      >
        <IconParkAlignVertically />
      </div>
      <div
        class="basic-vertical-origin-item"
        :class="{
          'basic-vertical-origin-item--active': model === 'TOP',
        }"
        title="垂直参照-上"
        @click="model = 'TOP'"
      >
        <IconParkAlignTop />
      </div>
    </div>
  </el-form-item>
</template>

<style scoped lang="scss">
.vertical-origin-attribute {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-around;
  height: var(--el-component-size);
  font-size: 1.2rem;
  background-color: var(--el-fill-color-blank);
  border-radius: var(--el-border-radius-base);

  .basic-vertical-origin-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25%;
    height: var(--el-component-size);
    cursor: pointer;
    border-radius: var(--el-border-radius-base);

    &.basic-vertical-origin-item--active {
      background: var(--el-fill-color-light);
    }
  }
}
</style>
