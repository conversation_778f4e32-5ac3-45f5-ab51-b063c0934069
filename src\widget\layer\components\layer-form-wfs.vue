<script lang="ts" setup>
import type { WFSConfig } from '../assets/types';
import { computedLoading } from '@/hooks/computed-loading';
import { WfsService } from '../utils/wfs-service';

export interface LayerFormWfsProps {
  modelValue?: WFSConfig;
}

export interface LayerFormWfsEmits {
  (event: 'update:modelValue', data?: WFSConfig): void;
}

defineOptions({ name: 'LayerFormWfs' });
const props = defineProps<LayerFormWfsProps>();
const emit = defineEmits<LayerFormWfsEmits>();

const model = useVModel(props, 'modelValue', emit, { deep: true, defaultValue: {}, clone: true, passive: true });

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 控制显影
const isShowAttributes = ref(false);

// 创建WFS服务实例
const wfsService = computed(() => {
  if (!model.value?.url || !model.value?.typeName) {
    return null;
  }

  return new WfsService({
    url: model.value.url,
    typeName: model.value.typeName,
    featurePrefix: model.value.featurePrefix,
    featureNS: model.value.featureNS,
  });
});

// 获取分页数据（包含总数）
const keyword = ref('');
const [paginatedData, paginatedDataLoading] = computedLoading(async () => {
  if (!wfsService.value) {
    return null;
  }

  const result = await wfsService.value.getPaginatedFeatures({
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
    usePost: false,
  });

  // 更新分页信息
  pagination.total = result.total;

  return result;
});

// 属性表数据 - 从分页数据中提取要素属性
const attributesTable = computed(() => {
  if (!paginatedData.value?.features?.features) {
    return [];
  }

  return paginatedData.value.features.features.map((feature: any, index: number) => ({
    index: (pagination.currentPage - 1) * pagination.pageSize + index + 1,
    id: feature.id, // 要素的外部ID
    geometry_name: feature.geometry_name, // 几何名称
    ...feature.properties, // 所有properties中的属性
  }));
});

// 根据关键词筛选后的表格数据
const filteredAttributesTable = computed(() => {
  if (!keyword.value) {
    return attributesTable.value;
  }

  return attributesTable.value.filter(item =>
    item.id && item.id.toString().toLowerCase().includes(keyword.value.toLowerCase()),
  );
});

// 动态列信息 - 从第一个要素中提取所有可能的属性字段
const dynamicColumns = computed(() => {
  if (!paginatedData.value?.features?.features?.length) {
    return [];
  }

  const firstFeature = paginatedData.value.features.features[0];
  const columns: Array<{ prop: string; label: string; sortable: boolean }> = [];

  // 添加properties中的所有字段
  if (firstFeature.properties) {
    Object.keys(firstFeature.properties).forEach((key) => {
      columns.push({
        prop: key,
        label: key,
        sortable: true,
      });
    });
  }

  return columns;
});
</script>

<template>
  <header-title2 my="16px" content="WFS配置">
    <template #extra>
      <el-button size="small" @click="isShowAttributes = !isShowAttributes">
        属性表
      </el-button>
    </template>
  </header-title2>

  <el-form :label-width="$vh(88)" p="x-20px">
    <el-form-item label="URL地址">
      <el-input v-model="model!.url" type="textarea" placeholder="请输入" :rows="5" />
    </el-form-item>
    <el-form-item label="图层名">
      <el-input v-model="model!.typeName" placeholder="请输入" />
    </el-form-item>
    <el-form-item label="WFS前缀">
      <el-input v-model="model!.featurePrefix" placeholder="请输入" />
    </el-form-item>

    <el-form-item label="工作区">
      <el-input v-model="model!.featureNS" placeholder="请输入" />
    </el-form-item>
  </el-form>

  <el-dialog v-model="isShowAttributes" width="1100px" title="属性表" :show-close="true" class="wfs-attributes-dialog">
    <div class="search">
      <span class="text-14px color-#ffffffD9">筛选条件：</span>
      <el-input v-model="keyword" placeholder="请输入要素ID" size="large" style="width: 300px;">
        <template #prefix>
          <el-icon class="i-tabler:search" />
        </template>
      </el-input>
    </div>

    <div class="table-container" style="min-height: 400px;">
      <el-table v-loading="paginatedDataLoading" :data="filteredAttributesTable" height="400" style="width: 100%">
        <el-table-column prop="index" label="序号" width="80" sortable fixed="left" />
        <el-table-column prop="id" label="要素ID" width="150" sortable fixed="left" />
        <el-table-column prop="geometry_name" label="几何名称" width="120" sortable />
        <template v-for="key in dynamicColumns" :key="key.prop">
          <el-table-column :prop="key.prop" :label="key.label" sortable />
        </template>
      </el-table>
    </div>

    <template #footer>
      <div flex="~ items-center" justify="space-between" gap="40px">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.search {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
