<!-- 缓冲区分析 -->
<script lang="ts" setup>
import type { AllGeoJSON, Units } from '@turf/turf';
import type { Cartesian3, PolygonHierarchy } from 'cesium';
import { cartesianToWgs84, CzPlotEntity, CzPlotScheme, PRESET_PLOT_SCHEMES_OPTIONS } from '@x3d/all';
import { useCzDataSource, useCzEntityCollection, useCzViewer } from '@x3d/vue-hooks';
import { Color } from 'cesium';

defineOptions({ name: 'BufferAnalysis' });

const emits = defineEmits<{ (event: 'close'): void }>();

PRESET_PLOT_SCHEMES_OPTIONS.forEach((item) => {
  CzPlotScheme.addCache(item.type!, item);
});

const form = ref({
  radius: 20,
  units: 'meters' as Units,
  steps: 100,
});

const entityCollection = useCzEntityCollection();

function plot(type: 'Point' | 'Polyline' | 'Polygon') {
  entityCollection.add(
    new CzPlotEntity({
      scheme: {
        type,
      },
      point: {
        pixelSize: 20,
        color: Color.RED,
        heightReference: Cesium.HeightReference.CLAMP_TO_TERRAIN,
      },
      polyline: {
        material: Color.RED,
        width: 2,
        clampToGround: true,
        depthFailMaterial: Color.RED,
      },
      polygon: {
        material: Color.RED.withAlpha(0.4),
        heightReference: Cesium.HeightReference.CLAMP_TO_TERRAIN,
      },
    }),
  );
}
const viewer = useCzViewer();

const effectGeojson = shallowRef<AllGeoJSON>();

function trigger() {
  const currentTime = viewer.value?.clock.currentTime;
  const features: any[] = [];
  entityCollection.scope.forEach((entity) => {
    const polyline: Cartesian3[] = entity.polyline?.positions?.getValue(currentTime);
    if (polyline?.length) {
      features.push(
        turf.buffer(
          turf.lineString(polyline.map(item => turf.getCoord(cartesianToWgs84(item) as any))),
          form.value.radius,
          { units: form.value.units, steps: form.value.steps },
        ),
      );
    }
    const polygon: PolygonHierarchy = entity.polygon?.hierarchy?.getValue(currentTime);
    if (polygon) {
      const positions = polygon.positions.map(item => turf.getCoord(cartesianToWgs84(item) as any));

      features.push(turf.buffer(
        turf.polygon([[...positions, positions[0]]]),
        form.value.radius,
        { units: form.value.units, steps: form.value.steps },
      ));
    }
    const point = entity.position?.getValue(currentTime);
    if (point) {
      features.push(
        turf.buffer(
          turf.point(cartesianToWgs84(point) as any),
          form.value.radius,
          { units: form.value.units, steps: form.value.steps },
        ),
      );
    }
  });

  effectGeojson.value = turf.featureCollection(features);
}
useCzDataSource(() => {
  if (effectGeojson.value) {
    return Cesium.GeoJsonDataSource.load(effectGeojson.value, {
      fill: Cesium.Color.AQUA.withAlpha(0.3),
      stroke: Cesium.Color.AQUA.withAlpha(0.5),
      clampToGround: true,
    });
  }
});

function clear() {
  effectGeojson.value = undefined;
  entityCollection.removeScope();
}
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="缓冲区分析"
    class="w-400px"
    @close="emits('close')"
  >
    <el-form my="15px" mr="25px">
      <div flex="~">
        <el-form-item label="缓冲区范围" :label-width="$vh(90)">
          <el-input-number
            v-model="form.radius"
            :min="0"
            controls-position="right"
          />
        </el-form-item>
        <el-form-item flex="1" label="单位" :label-width="$vh(50)">
          <el-select v-model="form.units">
            <el-option label="千米" value="kilometers" />
            <el-option label="米" value="meters" />
          </el-select>
        </el-form-item>
      </div>
      <el-form-item label="精细度" :label-width="$vh(90)">
        <el-slider
          v-model="form.steps"
          :min="100"
          :max="1000"
          mx="10px"
        />
      </el-form-item>
    </el-form>
    <el-divider>标绘类型</el-divider>
    <div p="20px">
      <el-button type="primary" @click="plot('Point')">
        绘制点
      </el-button>
      <el-button type="primary" @click="plot('Polyline')">
        绘制线
      </el-button>
      <el-button type="primary" @click="plot('Polygon')">
        绘制面
      </el-button>
    </div>

    <template #footer>
      <el-button class="primary" @click="trigger()">
        计算
      </el-button>
      <el-button class="plain-#FF6363 px-26px!" @click="clear()">
        清除
      </el-button>
    </template>
  </drag-card>
</template>
