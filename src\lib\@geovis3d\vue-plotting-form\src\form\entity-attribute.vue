<!-- EntityAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import { useCzEventComputed } from '@x3d/vue-hooks';
import BillboardGraphicsAttribute from './billboard-graphics-attribute.vue';
import BoxGraphicsAttribute from './box-graphics-attribute.vue';
import Cesium3DTilesetGraphicsAttribute from './cesium-3d-tileset-graphics-attribute.vue';
import CorridorGraphicsAttribute from './corridor-graphics-attribute.vue';
import CylinderGraphicsAttribute from './cylinder-graphics-attribute.vue';
import EllipseGraphicsAttribute from './ellipse-graphics-attribute.vue';
import EllipsoidGraphicsAttribute from './ellipsoid-graphics-attribute.vue';
import LabelGraphicsAttribute from './label-graphics-attribute.vue';
import ModelGraphicsAttribute from './model-graphics-attribute.vue';
import ParticleGraphicsAttribute from './particle-graphics-attribute.vue';
import PathGraphicsAttribute from './path-graphics-attribute.vue';
import PlaneGraphicsAttribute from './plane-graphics-attribute.vue';
import PointGraphicsAttribute from './point-graphics-attribute.vue';
import PolygonGraphicsAttribute from './polygon-graphics-attribute.vue';
import PolylineGraphicsAttribute from './polyline-graphics-attribute.vue';
import PolylineVolumeGraphicsAttribute from './polyline-volume-graphics-attribute.vue';
import PropertyBagAttribute from './property-bag-attribute.vue';
import QuaternionAttribute from './quaternion-attribute.vue';
import RectangleGraphicsAttribute from './rectangle-graphics-attribute.vue';
import TimeIntervalCollectionAttribute from './time-interval-collection-attribute.vue';
import WallGraphicsAttribute from './wall-graphics-attribute.vue';

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  hide?: Record<string, string[]>;
}>();

const model = useCzEventComputed(
  () => props.entity?.definitionChanged,
  () => {
    return props.entity;
  },
);
</script>

<template>
  <!-- <StringAttribute  v-if="model?.id"  :hide="hide?.['id']" :entity="entity" label="id" /> -->
  <!-- <StringAttribute  v-if="model?.name"  :hide="hide?.['name']" :entity="entity" label="name" /> -->
  <TimeIntervalCollectionAttribute v-if="false" :entity="entity" label="availability" />
  <!-- <BooleanAttribute v-model="model?.show" /> -->
  <!-- <StringAttribute  v-if="model?.description"  :hide="hide?.['description']" :entity="entity" label="description" /> -->
  <!-- position?: Cesium.PositionProperty | Cesium.Cartesian3; -->
  <ParticleGraphicsAttribute
    v-if="model?.particle"
    :hide="hide?.particle"
    :entity="entity"
    label="particle"
  />
  <QuaternionAttribute
    v-if="model?.orientation"
    :hide="hide?.orientation"
    :entity="entity"
    label="orientation"
  />
  <BillboardGraphicsAttribute
    v-if="model?.billboard"
    :hide="hide?.billboard"
    :entity="entity"
    label="billboard"
  />
  <BoxGraphicsAttribute
    v-if="model?.box"
    :hide="hide?.box"
    :entity="entity"
    label="box"
  />
  <CorridorGraphicsAttribute
    v-if="model?.corridor"
    :hide="hide?.corridor"
    :entity="entity"
    label="corridor"
  />
  <CylinderGraphicsAttribute
    v-if="model?.cylinder"
    :hide="hide?.cylinder"
    :entity="entity"
    label="cylinder"
  />
  <EllipseGraphicsAttribute
    v-if="model?.ellipse"
    :hide="hide?.ellipse"
    :entity="entity"
    label="ellipse"
  />
  <EllipsoidGraphicsAttribute
    v-if="model?.ellipsoid"
    :hide="hide?.ellipsoid"
    :entity="entity"
    label="ellipsoid"
  />
  <LabelGraphicsAttribute
    v-if="model?.label"
    :hide="hide?.label"
    :entity="entity"
    label="label"
  />
  <ModelGraphicsAttribute
    v-if="model?.model"
    :hide="hide?.model"
    :entity="entity"
    label="model"
  />
  <Cesium3DTilesetGraphicsAttribute
    v-if="model?.tileset"
    :hide="hide?.tileset"
    :entity="entity"
    label="tileset"
  />
  <PathGraphicsAttribute
    v-if="model?.path"
    :hide="hide?.path"
    :entity="entity"
    label="path"
  />
  <PlaneGraphicsAttribute
    v-if="model?.plane"
    :hide="hide?.plane"
    :entity="entity"
    label="平面"
  />
  <PointGraphicsAttribute
    v-if="model?.point"
    :hide="hide?.point"
    :entity="entity"
    label="point"
  />
  <PolygonGraphicsAttribute
    v-if="model?.polygon"
    :hide="hide?.polygon"
    :entity="entity"
    label="polygon"
  />
  <PolylineGraphicsAttribute
    v-if="model?.polyline"
    :hide="hide?.polyline"
    :entity="entity"
    label="polyline"
  />
  <PropertyBagAttribute
    v-if="model?.properties"
    :hide="hide?.properties"
    :entity="entity"
    label="properties"
  />
  <PolylineVolumeGraphicsAttribute
    v-if="model?.polylineVolume"
    :hide="hide?.polylineVolume"
    :entity="entity"
    label="polylineVolume"
  />
  <RectangleGraphicsAttribute
    v-if="model?.rectangle"
    :hide="hide?.rectangle"
    :entity="entity"
    label="rectangle"
  />
  <WallGraphicsAttribute
    v-if="model?.wall"
    :hide="hide?.wall"
    :entity="entity"
    label="wall"
  />
</template>
