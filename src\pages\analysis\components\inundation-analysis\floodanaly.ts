import * as Cesium from 'cesium';

interface AnalysisOptions {
  maxWaterHeight: number; // 起始高度
  minWaterHeight: number; // 最高高度
  startHeight: number; // 速度
  speed: number; // 多边形坐标点
  points: Cesium.Cartesian3[];
}

// 启动淹没分析
export function startFloodAnaly(analysisOptions: AnalysisOptions) {
  const { minWaterHeight, points = [] } = analysisOptions;
  const alt = Number(minWaterHeight);
  const waterEntendEntity = new Cesium.Entity({
    name: '多边形',
    polygon: {
      // hierarchy: new Cesium.PolygonHierarchy([]),
      material: Cesium.Color.fromBytes(64, 157, 253, 0),
      hierarchy: new Cesium.CallbackProperty(() => new Cesium.PolygonHierarchy(points), false),
    },
  }) as any;
  // floodEntities.push(waterEntendEntity);
  waterEntendEntity.polygon.heightReference = 'CLAMP_TO_GROUND';
  waterEntendEntity.polygon.material = Cesium.Color.fromBytes(64, 157, 253, 150);
  waterEntendEntity.polygon.extrudedHeight = alt; // 需要提前设置 不然会全部出现
  return waterEntendEntity;
}
