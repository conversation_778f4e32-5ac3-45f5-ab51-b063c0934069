import * as Cesium from 'cesium';

export interface ClampToHeightMostDetailedByTilesetOrTerrainOptions {
  /**
   * 待贴地的点位
   */
  positions: Cesium.Cartesian3[];

  scene: Cesium.Scene;

  /**
   * 贴地类型
   * @default Cesium.ClassificationType.BOTH
   */
  classificationType?: Cesium.ClassificationType;

  /**
   * 地形数据
   * @default scene.terrainProvider
   */
  terrainProvider?: Cesium.TerrainProvider;
}

/**
 * 将传入的点位列表进行贴地处理,若某个点位获取高程失败则将此进行克隆返回
 * @param options
 */
export async function clampToHeightMostDetailedByTilesetOrTerrain(
  options: ClampToHeightMostDetailedByTilesetOrTerrainOptions,
): Promise<Cesium.Cartesian3[]> {
  const {
    positions,
    scene,
    classificationType = Cesium.ClassificationType.BOTH,
    terrainProvider = scene.terrainProvider,
  } = options;

  const tileset = [
    Cesium.ClassificationType.BOTH,
    Cesium.ClassificationType.CESIUM_3D_TILE,
  ].includes(classificationType);
  const terrain = [Cesium.ClassificationType.BOTH, Cesium.ClassificationType.TERRAIN].includes(
    classificationType,
  );

  const tilesetPromise = (async () => {
    if (tileset) {
      try {
        const res = await scene.clampToHeightMostDetailed(positions.map(e => e.clone()));
        return res;
      }
      catch (error) {
        console.error(error);
        return [];
      }
    }
    return [];
  })();
  const terrainPromise = (async () => {
    if (terrain && terrainProvider) {
      try {
        const res = await Cesium.sampleTerrainMostDetailed(
          terrainProvider,
          positions.map(e => Cesium.Cartographic.fromCartesian(e)),
        );
        return res;
      }
      catch (error) {
        console.error(error);
        return [];
      }
    }
    return [];
  })();

  const [tilesetPositions, terrainPositions] = await Promise.all([tilesetPromise, terrainPromise]);
  const resluts: Cesium.Cartesian3[] = [];

  positions.forEach((item, index) => {
    const position
      = tilesetPositions[index] || terrainPositions[index]
        ? Cesium.Ellipsoid.WGS84.cartographicToCartesian(terrainPositions[index])
        : item.clone();
    resluts.push(position);
  });

  return resluts;
}
