import * as Cesium from 'cesium';

export type ColorSerializateJSON = string;

export class ColorSerializate {
  private constructor() {}
  static toJSON(data?: Cesium.Color): ColorSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    return data?.toCssColorString();
  }

  static fromJSON(json?: ColorSerializateJSON): Cesium.Color | undefined {
    if (!json) {
      return undefined;
    }
    return Cesium.Color.fromCssColorString(json);
  }
}
