import * as Cesium from 'cesium';

import image from './assets/greentide.jpg';
import { getCesiumMaterialCache, setCesiumMaterialCache } from './material-cache';

/**
 * 绿潮
 */
export class GreentideMaterialProperty implements Cesium.MaterialProperty {
  constructor() {}

  static readonly MaterialType = 'GreentideMaterial';

  getType(_time?: Cesium.JulianDate) {
    return GreentideMaterialProperty.MaterialType;
  }

  readonly isConstant = false;

  readonly definitionChanged = new Cesium.Event();

  getValue(_time?: Cesium.JulianDate, result?: any) {
    result ??= {};

    return result;
  }

  equals(other?: GreentideMaterialProperty) {
    return other instanceof GreentideMaterialProperty;
  }
}

const WaterMaterial = getCesiumMaterialCache('Water');

setCesiumMaterialCache(GreentideMaterialProperty.MaterialType, {
  ...WaterMaterial,
  fabric: {
    ...WaterMaterial.fabric,
    type: 'Greentide',
    uniforms: {
      ...WaterMaterial.fabric.uniforms,
      baseWaterColor: Cesium.Color.fromCssColorString(`rgba(102,147,24,0.8)`),
      blendColor: Cesium.Color.fromCssColorString(`rgba(65,19,29,0.8)`),
      normalMap: image,
      frequency: 500,
      animationSpeed: 0.01,
      amplitude: 1,
    },
  },
  translucent: () => true,
});
