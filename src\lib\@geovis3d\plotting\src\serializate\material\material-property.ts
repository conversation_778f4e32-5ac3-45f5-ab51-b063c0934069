import * as Cesium from 'cesium';

export interface MaterialPropertySerializateJSON<T = string, P extends Record<string, any> = any> {
  type: T;
  params: P;
}

export interface MaterialPropertySerializateController<
  T extends string = any,
  D extends Cesium.MaterialProperty = any,
  P extends Record<string, any> = any,
  J extends MaterialPropertySerializateJSON<T, P> = any,
> {
  type: T;
  hit: (property: Cesium.MaterialProperty) => boolean;
  fromJSON: (json: J) => D | undefined;
  toJSON: (data?: D, time?: Cesium.JulianDate) => J;
}

/**
 * 材质序列化
 */
export class MaterialPropertySerializate {
  private constructor() {}

  /**
   * @internal
   */
  private static _values = new Map<string, MaterialPropertySerializateController>();

  /**
   * 添加标绘控制器配置
   *
   * `options.type`为唯一字段，作为识别使用
   */
  static addSerializate(controller: MaterialPropertySerializateController) {
    if (!controller.type) {
      throw new Error('controller 必须包含`type`字段');
    }
    else {
      this._values.set(controller.type, controller);
    }
  }

  static toJSON(
    data?: Cesium.MaterialProperty,
    time?: Cesium.JulianDate,
  ): MaterialPropertySerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    time ??= Cesium.JulianDate.now();
    const serializate = [...MaterialPropertySerializate._values.values()].find((item) => {
      return item.hit(data);
    });
    return serializate?.toJSON(data, time);
  }

  static fromJSON(json?: MaterialPropertySerializateJSON): Cesium.MaterialProperty | undefined {
    if (!json) {
      return undefined;
    }
    const serializate = [...MaterialPropertySerializate._values.values()].find((item) => {
      return item.type === json.type;
    });
    return serializate?.fromJSON(json);
  }
}
