<!-- 系统设置弹窗 -->
<script lang="ts" setup>
import type { components } from '@/genapi/cimapi';
import type { IAreaItem } from '@/stores/types';
import type { CascaderInstance, CascaderOption } from 'element-plus';
import { fileSettingUploadFileUsingPost, layerInfoSaveOrUpdateSystemSettingsUsingPost } from '@/genapi/cimapi';

import { useAsync } from '@/hooks/use-async';
import { defaultStatic } from '@/lib/geovisearth/defaultStaticConfig';
import { useSettingStore } from '@/stores/setting';
import { useUserStore } from '@/stores/user';
import { Check, Loading, Plus } from '@element-plus/icons-vue';
import IndicatorCharts from './components/indicator-charts.vue';
import { URL_SETTING_LIST } from './url-setting-map';

// 定义表单类型，基于系统设置VO，添加自定义属性
type SystemSettingForm = components['schemas']['XiTongSheZhiVO'] & {
  logoUrl?: string;
  coordinateSystem?: string;
  strokeWidth?: number;
};

export interface SystemSettingDialogProps {
  modelValue?: boolean;
}

export interface SystemSettingDialogEmits {
  (event: 'update:modelValue', data?: boolean): void;
}

defineOptions({ name: 'SystemSettingDialog' });
const props = defineProps<SystemSettingDialogProps>();
const emit = defineEmits<SystemSettingDialogEmits>();

const model = useVModel(props, 'modelValue', emit);
const cascaderRef = shallowRef<CascaderInstance>();
const userStore = useUserStore();
const settingStore = useSettingStore();

// 保存用户打开弹窗时的原始菜单设置
const originalMenus = ref<string[]>([]);

const form = ref<SystemSettingForm>({});
const menuList = computed({
  get: () => settingStore.activeMenus,
  set: (val) => {
    settingStore.activeMenus = val;
    // 同步到表单
    form.value.menus = val.join(',');
  },
});

const router = useRouter();
const menuRoutes = computed(() => {
  const layoutRoute = router.options.routes.find(item => item.name === 'Layout');
  const routes = layoutRoute?.children ?? [];

  // 过滤和排序路由
  return routes
    .filter((route) => {
      const meta = route.children?.[0]?.meta;
      if (meta?.hidden) {
        return false;
      }

      const limitRoles = meta?.roles ?? [];
      if (limitRoles.length) {
        return userStore.roleList.some(role => limitRoles.includes(role));
      }

      return true;
    })
    .sort((a, b) => (a.children?.[0]?.meta?.sort ?? 1) - (b.children?.[0]?.meta?.sort ?? 1));
});

// 监听设置变化并初始化表单
watchImmediate(() => settingStore.setting, () => {
  form.value = structuredClone(settingStore.setting || {});

  // 确保数值类型正确
  if (form.value.strokeWidth !== undefined) {
    form.value.strokeWidth = Number(form.value.strokeWidth);
  }
  else {
    form.value.strokeWidth = 1;
  }

  // 确保logoUrl字段初始化
  form.value.logoUrl = form.value.logoUrl || '';
});

// 监听弹窗打开，保存原始菜单设置
watch(() => model.value, (newVal) => {
  if (newVal) {
    originalMenus.value = [...settingStore.activeMenus];
  }
});

// Logo上传相关状态和方法
const tempLogoUrl = ref<string>('');
const tempLogoPath = ref<string>('');
const fileInputRef = ref<HTMLInputElement | null>(null);
const uploadStatus = ref<'ready' | 'uploading' | 'success' | 'fail'>('ready');

function getFullLogoUrl(path: string): string {
  if (!path)
    return '';
  if (path.startsWith('/3d-data-system/server'))
    return path;
  return `/3d-data-system/server${path}`;
}

const displayLogoUrl = computed((): string => {
  if (tempLogoUrl.value)
    return tempLogoUrl.value;
  if (form.value.logoUrl)
    return getFullLogoUrl(form.value.logoUrl);
  return '';
});

async function handleLogoUpload(file: File): Promise<boolean> {
  // 检查文件类型和大小
  const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
  const validExtensions = ['jpg', 'jpeg', 'png', 'svg', 'webp'];

  const isValidType = validExtensions.includes(fileExtension);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isValidType) {
    ElMessage.error('Logo文件必须是图片格式(JPG/PNG/SVG/WEBP)!');
    console.error(`Invalid file type: extension: ${fileExtension}`);
    return false;
  }

  if (!isLt10M) {
    ElMessage.error('Logo文件大小不能超过10MB!');
    return false;
  }

  // 检查图片尺寸比例
  const img = new Image();
  img.src = URL.createObjectURL(file);
  await new Promise((resolve) => {
    img.onload = resolve;
  });

  if (Math.abs(img.width / img.height - 1) > 0.1) {
    ElMessage.warning('建议上传1:1比例的Logo图片以获得最佳显示效果');
  }

  URL.revokeObjectURL(img.src);

  uploadStatus.value = 'uploading';
  tempLogoUrl.value = URL.createObjectURL(file);

  try {
    const formData = new FormData();
    formData.append('file', file);
    const response = await fileSettingUploadFileUsingPost({
      data: formData as any,
      headers: {
        'Authorization': userStore.authorization,
        'Content-Type': 'multipart/form-data',
      },
    });

    if (response.code === 200 && response.data) {
      tempLogoPath.value = response.data;
      uploadStatus.value = 'success';
      ElMessage.success('Logo上传成功');
      return true;
    }
    else {
      uploadStatus.value = 'fail';
      console.error('Upload failed:', response);
      ElMessage.error('Logo上传失败');
      return false;
    }
  }
  catch (error) {
    console.error('Logo上传错误:', error);
    uploadStatus.value = 'fail';
    ElMessage.error('Logo上传失败');
    return false;
  }
}

function triggerFileInput(): void {
  fileInputRef.value?.click();
}

function handleFileChange(event: Event): void {
  const inputElement = event.target as HTMLInputElement;
  if (inputElement.files && inputElement.files.length > 0) {
    handleLogoUpload(inputElement.files[0]);
    inputElement.value = '';
  }
}

// 保存设置
const { execute: save, isLoading: saveLoading } = useAsync(async () => {
  if (tempLogoPath.value) {
    form.value.logoUrl = tempLogoPath.value;
  }

  const formDataToSave = {
    ...form.value,
    menus: settingStore.activeMenus.join(','),
    creator: userStore.userName,
  };

  await layerInfoSaveOrUpdateSystemSettingsUsingPost({
    data: formDataToSave,
  });

  await settingStore.refreshSetting();
  ElMessage.success('保存成功');
  model.value = false;
});

// 取消设置
function handleCancel(): void {
  settingStore.activeMenus = [...originalMenus.value];
  tempLogoUrl.value = '';
  tempLogoPath.value = '';
  uploadStatus.value = 'ready';
  model.value = false;
}

// 其他状态
const collapseIndex = ref('1');
const currentURLSetting = ref(URL_SETTING_LIST[0].value);

// 提取版本信息的计算属性
const dialogTitle = computed(() => {
  const sysTitle = form.value.sysTitle || '';
  // 使用正则表达式提取版本信息，匹配 v + 数字.数字.数字 格式
  const versionMatch = sysTitle.match(/v\d+\.\d+(?:\.\d+)?/i);
  const version = versionMatch ? versionMatch[0] : '';

  return version ? `系统设置 - ${version}` : '系统设置';
});

// 天空盒选项
const skyBoxList = computed(() => {
  const { distant, near } = defaultStatic.skyBox;
  const list: Record<string, any>[] = [];

  distant.forEach((ele1) => {
    near.forEach((ele2) => {
      list.push({
        distant: ele1,
        near: ele2,
        key: `${ele1.name}-${ele2.name}`,
      });
    });
  });

  return list;
});
</script>

<template>
  <el-dialog v-model="model" title="系统设置" class="max-h-80vh w770px!">
    <el-scrollbar height="calc(80vh - 200px)" mx="-30px!" px="30px!">
      <el-form label-position="top">
        <el-collapse v-model="collapseIndex" accordion>
          <el-collapse-item name="1">
            <template #title>
              <el-icon class="i-custom:system" text="#fff 24px!" mr="10px" />
              <span class="font-blod-18px">系统</span>
            </template>
            <el-form-item label="系统标题" prop="sysTitle">
              <el-input v-model="form.sysTitle" />
            </el-form-item>
            <div class="horizontal">
              <el-form-item label="初始行政区划" class="flex-1 px-14px!" prop="defaultUnit">
                <el-cascader
                  ref="cascaderRef"
                  v-model="form.defaultUnit"
                  :options="settingStore?.areaList as unknown as CascaderOption[]"
                  :props="{ checkStrictly: true, emitPath: false }"
                >
                  <template #default="{ node, data }">
                    <span>{{ (data as IAreaItem).name }}</span>
                    <span v-if="!node.isLeaf"> ({{ (data as IAreaItem).children?.length || 0 }}) </span>
                  </template>
                </el-cascader>
              </el-form-item>
              <el-form-item label="是否显示边界" class="px-14px!" prop="isShowUnitBorder">
                <el-switch v-model="form.isShowUnitBorder" inactive-value="0" active-value="1" size="large" />
              </el-form-item>
              <el-form-item v-if="form.isShowUnitBorder === '1'" label="边界线宽" class="px-14px!" prop="strokeWidth">
                <el-input-number v-model="form.strokeWidth" :min="1" :step="1" :max="10" />
              </el-form-item>
              <el-form-item v-if="form.isShowUnitBorder === '1'" label="边界颜色" prop="stroke" class="px-14px!">
                <el-color-picker v-model="form.stroke" show-alpha />
              </el-form-item>
            </div>
            <div class="horizontal">
              <el-form-item label="光照" px="14px!" label-position="left" prop="light">
                <el-switch v-model="form.light" size="large" inactive-value="0" active-value="1" />
              </el-form-item>
              <el-form-item label="大气" px="14px!" label-position="left" prop="atmosphere">
                <el-switch v-model="form.atmosphere" inactive-value="0" active-value="1" size="large" />
              </el-form-item>
            </div>

            <el-form-item label="系统链接" prop="dataProcessingUrl">
              <template #label>
                <div flex="~ items-center" w="400px">
                  <span flex="shrink-0" pr="15px">系统链接</span>
                  <el-select-v2 v-model="currentURLSetting" :options="URL_SETTING_LIST" />
                </div>
              </template>
              <div flex="~ gap-x-15px 1">
                <el-input
                  v-model="form[URL_SETTING_LIST.find(item => item.value === currentURLSetting)!.name as keyof typeof form]"
                  class="flex-1!"
                />
                <el-input
                  v-model="form[URL_SETTING_LIST.find(item => item.value === currentURLSetting)!.url as keyof typeof form]"
                  class="flex-[2]!"
                />
              </div>
              <el-alert type="info" :closable="false">
                可用变量：{token}、{base64token}
              </el-alert>
            </el-form-item>

            <el-form-item label="BaseUrl" prop="baseUrl">
              <el-input v-model="form.baseUrl" />
            </el-form-item>

            <el-form-item label="菜单管理" prop="menus">
              <el-checkbox-group v-model="menuList" class="px-5px">
                <el-checkbox
                  v-for="item in menuRoutes"
                  :key="item.path"
                  :disabled="item.path === '/home'"
                  :label="item.children?.[0]?.meta?.title"
                  :value="item?.children?.[0]?.meta?.title"
                />
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="logo管理" prop="logoUrl">
              <div class="logo-uploader">
                <div class="el-upload" @click="triggerFileInput">
                  <div class="logo-container">
                    <el-avatar
                      v-if="displayLogoUrl"
                      shape="square"
                      size="large"
                      :src="displayLogoUrl"
                      class="logo-avatar"
                    />
                    <el-icon v-else class="logo-uploader-icon">
                      <Plus />
                    </el-icon>

                    <div v-if="displayLogoUrl" class="upload-hover-indicator">
                      <el-icon>
                        <Plus />
                      </el-icon>
                      <span>更换</span>
                    </div>

                    <div v-if="uploadStatus === 'uploading'" class="upload-loading">
                      <el-icon class="is-loading">
                        <Loading />
                      </el-icon>
                    </div>
                    <div v-else-if="uploadStatus === 'success'" class="upload-success">
                      <el-icon>
                        <Check />
                      </el-icon>
                    </div>
                  </div>
                </div>
                <input
                  ref="fileInputRef"
                  type="file"
                  accept=".jpeg,.jpg,.png,.svg,.webp"
                  style="display: none;"
                  @change="handleFileChange"
                >
              </div>
              <div class="mt-2 text-xs text-gray-400" style="font-size: 12px; line-height: 1.5;">
                1. 点击图片可更换logo<br>
                2. 建议1:1比例<br>
                3. 大小不超过10MB
              </div>
            </el-form-item>
          </el-collapse-item>
          <el-collapse-item name="2">
            <template #title>
              <el-icon class="i-custom:performance" text="#fff 24px!" mr="10px" />
              <span class="font-blod-18px">性能</span>
            </template>
            <div class="horizontal">
              <el-form-item label="分辦率" class="flex-1 px-25px!" prop="resolution">
                <el-slider v-model="form.resolution" :step="0.1" :max="1.5" :min="0.5" />
              </el-form-item>
              <el-form-item label="动画" class="flex-1 px-25px!" prop="animation">
                <el-switch v-model="form.animation" inactive-value="0" active-value="1" size="large" />
              </el-form-item>
            </div>
          </el-collapse-item>
          <el-collapse-item name="3">
            <template #title>
              <el-icon class="i-custom:screen" text="#fff 24px!" mr="10px" />
              <span class="font-blod-18px">显示</span>
            </template>
            <el-form-item label="天空盒" prop="skyBox">
              <el-select v-model="form.skyBox">
                <el-option v-for="item in skyBoxList" :key="item.key" :value="item.key" :label="item.key" />
              </el-select>
            </el-form-item>
            <el-form-item label="坐标系" prop="coordinateSystem">
              <el-select v-model="form.coordinateSystem">
                <el-option value="0" label="WGS84" />
                <el-option value="1" label="CGCS2000" />
              </el-select>
            </el-form-item>
            <el-form-item label="经纬度格式" prop="locationFormat">
              <el-select v-model="form.locationFormat">
                <el-option value="0" label="度分秒" />
                <el-option value="1" label="浮点数" />
              </el-select>
            </el-form-item>
            <el-form-item label="度量单位" prop="measuringUnit">
              <el-select v-model="form.measuringUnit">
                <el-option value="m" label="米（m）" />
                <el-option value="km" label="千米（km）" />
              </el-select>
            </el-form-item>
            <el-form-item label="主题颜色" prop="themeColor">
              <el-color-picker v-model="form.themeColor" show-alpha />
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <IndicatorCharts />
    </el-scrollbar>
    <template #footer>
      <el-button type="default" @click="handleCancel">
        取消
      </el-button>
      <el-button type="primary" :loading="saveLoading" @click="save()">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.horizontal {
  display: flex;
  margin: 15px -14px;

  .el-form-item {
    padding: 0;
    margin: 0;
  }
}

.el-collapse {
  --el-collapse-header-font-size: 18px;
  --el-collapse-border-color: tranparent;
  --el-collapse-header-height: 56px;

  :deep() &-item {
    margin-bottom: 16px;

    &__header {
      padding: 16px 12px;
      background: rgb(255 255 255 / 5%);
      border-radius: 4px;
    }

    &__content {
      padding-top: 16px;
    }
  }
}

.el-form {
  --el-font-size-base: 16px;

  .el-input {
    --el-input-height: 44px;
    --el-text-color-placeholder: #7e8082;

    border-radius: 4px;
  }

  .el-select {
    :deep() &__wrapper {
      height: 44px;
    }
  }

  :deep() .el-cascader {
    width: 100%;

    .el-input__wrapper {
      height: 44px;
    }
  }

  .el-form-item {
    margin: 0;

    :deep() &__label {
      padding: 0 8px;
      margin-bottom: 8px !important;
    }

    .el-slider {
      --el-color-primary: #fff;
      --el-slider-runway-bg-color: #1c1d1e;
      --el-slider-height: 8px;

      :deep() &__button {
        position: relative;

        &::after {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 12px;
          height: 12px;
          content: ' ';
          background: linear-gradient(-50deg, rgb(31 32 33 / 100%) 25%, rgb(112 112 113 / 100%) 85%);
          border-radius: 6px;
          transform: translate(-50%, -50%);
        }
      }
    }
  }

  .el-form-item + .el-form-item,
  .el-form-item + .horizontal,
  .horizontal + .el-form-item {
    margin-top: 12px;
  }
}

.logo-uploader {
  padding: 0 5px;

  :deep(.el-upload) {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    transition: var(--el-transition-duration);

    &:hover {
      border-color: var(--el-color-primary);

      .upload-hover-indicator {
        opacity: 1;
      }
    }
  }

  .logo-uploader-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    font-size: 28px;
    color: #8c8c8c;
    text-align: center;
  }

  .logo-avatar {
    display: block;
    width: 80px;
    height: 80px;
    object-fit: contain;
  }

  .logo-container {
    position: relative;
    width: 80px;
    height: 80px;

    .upload-hover-indicator {
      position: absolute;
      inset: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #fff;
      background-color: rgb(0 0 0 / 60%);
      border-radius: 4px;
      opacity: 0;
      transition: opacity 0.3s ease;

      .el-icon {
        margin-bottom: 2px;
        font-size: 20px;
      }

      span {
        font-size: 12px;
      }
    }

    .upload-loading {
      position: absolute;
      inset: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      background-color: rgb(0 0 0 / 50%);

      .is-loading {
        font-size: 24px;
        animation: rotating 2s linear infinite;
      }

      @keyframes rotating {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }
    }

    .upload-success {
      position: absolute;
      right: 5px;
      bottom: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      color: #fff;
      background: var(--el-color-success);
      border-radius: 50%;

      .el-icon {
        font-size: 14px;
      }
    }
  }
}
</style>
