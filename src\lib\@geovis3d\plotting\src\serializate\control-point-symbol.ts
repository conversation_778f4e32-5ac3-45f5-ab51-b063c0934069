import type { ControlPointSymbol } from '@/lib/@geovis3d/core';
import type { Cartesian3SerializateJSON } from './cartesian3';
import { Cartesian3Serializate } from './cartesian3';

export interface ControlPointSymbolSerializateJSON {
  id: string;
  position: Cartesian3SerializateJSON;
}

export type ControlPointSymbolKey = keyof ControlPointSymbolSerializateJSON;

export class ControlPointSymbolSerializate {
  private constructor() {}

  static toJSON(data?: ControlPointSymbol): ControlPointSymbolSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }

    return {
      id: data.id,
      position: Cartesian3Serializate.toJSON(data.position)!,
    };
  }

  static fromJSON(json?: ControlPointSymbolSerializateJSON): ControlPointSymbol | undefined {
    if (!json) {
      return undefined;
    }

    return {
      id: json.id,
      position: Cartesian3Serializate.fromJSON(json.position)!,
    };
  }
}
