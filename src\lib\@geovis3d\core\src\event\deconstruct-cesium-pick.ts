export interface DeconstructPickReturn<T> {
  primitive?: T;
  id?: T | T[];
  primitiveCollection?: T;
  collection?: T;
  entityCollection?: T;
  dataSource?: T;
}
/**
 * 解构scene.pick返回值
 * @param pick scene.pick返回值
 * @param field 遍历pick返回值上的某个字段
 */
export function deconstructCesiumPick<T = any>(pick: any, field: string): DeconstructPickReturn<T> {
  const { primitive, id, primitiveCollection, collection } = pick ?? {};

  const entityCollection = id?.entityCollection;
  const dataSource = entityCollection?.owner;

  return {
    primitive: primitive?.[field],
    id: Array.isArray(id) ? id.map(e => e[field]) : id?.[field],
    primitiveCollection: primitiveCollection?.[field],
    collection: collection?.[field],
    entityCollection: entityCollection?.[field],
    dataSource: dataSource?.[field],
  };
}
