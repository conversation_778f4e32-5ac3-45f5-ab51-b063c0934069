import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.DebugCameraPrimitive} 构造函数参数
 */
export type DebugCameraPrimitiveConstructorOptions = ConstructorParameters<
  typeof Cesium.DebugCameraPrimitive
>[0];

/**
 * {@link Cesium.DebugCameraPrimitive} 拓展用法与 {@link Cesium.DebugCameraPrimitive} 基本一致。
 *
 * `GcDebugCameraPrimitive.event`鼠标事件监听
 */
export class GcDebugCameraPrimitive extends Cesium.DebugCameraPrimitive {
  constructor(options: DebugCameraPrimitiveConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
