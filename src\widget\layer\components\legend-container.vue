<!-- 图例区域 -->
<script lang="ts" setup>
import { useLayerState } from '../state/layer-state';

defineOptions({ name: '<PERSON><PERSON>ontaine<PERSON>' });

const { checkedItems } = useLayerState();

const legends = computed(() => {
  const list = checkedItems.value.map((item) => {
    return {
      data: item,
      json: item?.config?.legend ?? {},
    };
  });
  return list.filter(item => item.json.visible && item.json.list?.length);
});
</script>

<template>
  <teleport v-if="legends.length" to="body">
    <div position="fixed bottom-150px right-10px z-20" min-w="200px" p="10px" bg="[var(--el-bg-color)]" rd="5px">
      <header-title1 m="y-10px" min-h="40px!" p="0px!">
        图例
      </header-title1>
      <div v-for="(item, index) in legends" :key="item.data.id">
        <div text="16px" font="bold">
          {{ item.data.layerName }}
        </div>
        <div v-for="child in item.json?.list" :key="child.symbol" flex="~ items-center gap-y-5px" m="y-2px">
          <div :style="{ background: child.color }" size="20px" />
          <span text="14px" p="l-10px">{{ child.name }}</span>
        </div>
        <el-divider v-if="index !== legends.length - 1" my="5px!" />
      </div>
    </div>
  </teleport>
</template>
