export interface UseAsyncOptions {
  /**
   * Delay for executing the promise. In milliseconds.
   *
   * @default 0
   */
  delay?: number;
  /**
   * Execute the promise right after the function is invoked.
   * Will apply the delay if any.
   *
   * When set to false, you will need to execute it manually.
   *
   */
  immediate?: boolean;

  /**
   * Callback when error is caught.
   */
  onError?: (e: unknown) => void;
  /**
   * Callback when success is caught.
   * @param {D} data
   */
  onSuccess?: (data: any) => void;
  /**
   * Sets the state to initialState before executing the promise.
   *
   * This can be useful when calling the execute function more than once (for
   * example, to refresh data). When set to false, the current state remains
   * unchanged until the promise resolves.
   *
   * @default true
   */

  /**
   *
   * An error is thrown when executing the execute function
   *
   * @default false
   */
  throwError?: boolean;
}

export interface UseAsyncReturn<Params extends any[]> {
  isReady: Ref<boolean>;
  isLoading: Ref<boolean>;
  error: Ref<unknown>;
  execute: (delay?: number, ...args: Params) => Promise<any>;
}

export function useAsync<Params extends any[]>(
  promise: Promise<any> | ((...args: Params) => Promise<any>),
  options: UseAsyncOptions = {},
): UseAsyncReturn<Params> {
  const { isReady, isLoading, execute, error } = useAsyncState(promise, undefined, {
    ...options,
    immediate: options.immediate ?? false,
  });
  return {
    isReady,
    isLoading,
    execute,
    error,
  };
}
