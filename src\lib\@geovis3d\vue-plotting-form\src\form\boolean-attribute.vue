<!-- BooleanAttribute -->
<script lang="ts" setup>
import { useVModel } from '@vueuse/core';

defineOptions({ name: 'BooleanAttribute' });

const props = defineProps<{
  modelValue?: boolean;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: boolean): void;
}>();

const model = useVModel(props, 'modelValue', emit);
</script>

<template>
  <el-form-item :label="label">
    <el-switch v-model="model" />
  </el-form-item>
</template>
