<!-- 与vue自带的component类似，但输入的值若非函数类型着直接转换成字符串输出 -->
<script lang="ts" setup>
import type { VNodeChild } from 'vue';

export type BasicType = number | string | boolean | symbol | bigint | null | undefined;
export type BasicOrComponentOpt = BasicType | VNodeChild | (() => VNodeChild | BasicType | unknown);
export interface BasicOrComponentProps {
  is?: BasicOrComponentOpt;
}

defineOptions({ name: 'BasicOrComponent' });

const props = defineProps<BasicOrComponentProps>();
</script>

<template>
  <template v-if="typeof props?.is === 'function'">
    <component :is="is" />
  </template>
  <template v-else-if="is">
    {{ is }}
  </template>
</template>
