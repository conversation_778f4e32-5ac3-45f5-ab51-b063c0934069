import * as Cesium from 'cesium';

export const [createEmergencyResource, injectEmergencyResource] = createInjectionState(() => {
  const position = shallowRef<[number, number] | undefined>();
  const distance = ref(5);
  const filterable = ref(false);
  const visible = ref(false);
  const cartesian3 = computed(() => {
    const [lng, lat] = position.value ?? [];
    if (lng && lat) {
      return Cesium.Cartesian3.fromDegrees(lng, lat, 0);
    }
  });
  const route = useRoute();

  // 路由切换时，关闭筛选弹窗
  watch(route, () => {
    visible.value = false;
  });

  /** 关闭资源树时，关闭筛选框 */
  watch(visible, (visible) => {
    !visible && (filterable.value = false);
  });
  /** 出现筛选框时一定会现资源树 , 显示筛选框且半径为空则赋值默认值 */
  watch(filterable, (filterable) => {
    filterable && (visible.value = true);
    if (filterable && !distance.value) {
      distance.value = 5;
    }
  });

  return {
    position,
    distance,
    filterable,
    visible,
    cartesian3,
  };
});
