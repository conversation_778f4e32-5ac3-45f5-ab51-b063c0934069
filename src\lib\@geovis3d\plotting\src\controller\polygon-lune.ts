import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import { lune } from '@/lib/@geovis3d/geometry';
import * as turf from '@turf/turf';

import * as Cesium from 'cesium';

/**
 * polygon-lune 标绘配置 弓形
 */
export default <PlottingControllerOptions>{
  type: 'polygon-lune',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 3,
  location: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    if (positions.length <= 1) {
      entity._cache = undefined;
      return;
    }
    const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);
    if (positions.length === 2) {
      const bearing = turf.bearing(coords[0], coords[1]);
      const distance = turf.distance(coords[0], coords[1]);
      const center = turf.midpoint(coords[0], coords[1]);
      const coord3 = turf.destination(center, distance / 2, bearing - 90);
      coords.push(coord3.geometry.coordinates);
    }
    const coordinates = lune(coords).geometry.coordinates;
    entity._cache = new Cesium.PolygonHierarchy(
      coordinates.map(e => wgs84ToCartesian([...e, 0])),
    );
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
