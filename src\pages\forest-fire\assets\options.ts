import type { EChartsOption } from 'echarts';

export function everyMonthlineOptions(data, typeData) {
  return {
    tooltip: {
      trigger: 'axis',
      textBorderType: 'dashed',
      padding: 12,
      backgroundColor: 'rgba(0, 0, 0, .5)',
      borderWidth: 0,
      textStyle: { color: '#F2FAFF', fontSize: '14' },
      formatter: (params: any) => {
        return `${params[0].axisValue}${typeData.label}：${params[0].value.toString()}${typeData.unit}`;
      },
    },
    grid: {
      show: false,
      shadowColor: 'rgba(0, 0, 0, 0.5)',
      top: '6%',
      left: '1%',
      right: '3%',
      bottom: '6%',
      containLabel: true,
      borderColor: 'rgba(115, 215, 255, 0.14)',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      // axisLabel: { interval: 0 },
      axisLine: {
        show: true,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: ['rgba(115, 215, 255, 0.14)'],
        },
      },
      axisPointer: {
        show: true,
        lineStyle: {
          color: 'rgba(154, 230, 236, 0.4)',
          shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowBlur: 10,
        },
      },
      axisLabel: {
        rotate: 0,
        color: '#ffffff',
        fontSize: 12,
      },
      data: data.map(el => `${el.hour}时`),
    },
    yAxis: {
      axisLine: {
        show: true,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: ['rgba(115, 215, 255, 0.14)'],
        },
      },
      axisLabel: {
        color: '#ffffff',
        fontSize: 12,
      },
    // minInterval: 1,
    },
    series: [
      {
        data: data.map(el => el.value),
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#5D91DE',
        },
        areaStyle: {
        // 区域填充样式
          normal: {
          // 线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(224,251,255,0.04)',
              },
              {
                offset: 0.8,
                color: '#5D91DE',
              },
              {
                offset: 1,
                color: 'rgba(36,130,214,0.92)',
              },
            ], false),
            // shadowColor: "rgba(109, 126, 0, 0.5)", //阴影颜色
            shadowBlur: 20, // shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
          },
        },
      },
    ],
  } as EChartsOption;
}
