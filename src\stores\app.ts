import { useUserStore } from '@/stores/user';

export interface IAreaItem {
  adcode: string;
  value?: string;
  lng: number;
  lat: number;
  name: string;
  label?: string;
  level: string;
  parent: string | null;
  children?: IAreaItem[];
}

export const useAppStore = defineStore('app', () => {
  const userStore = useUserStore();

  const { execute, state, isLoading } = useAsyncState(async () => {
    const { data } = await cimApi.layerInfoGetSystemSettingsByUserCodeUsingGet({ params: {
      userCode: userStore.userInfo?.systemUser?.userName,
    } });
    if (data?.resolution) {
      data.resolution = Number(data.resolution);
    }
    return data;
  }, undefined, { shallow: false });

  return {
    setting: state,
    execute,
    isLoading,
  };
});
;
