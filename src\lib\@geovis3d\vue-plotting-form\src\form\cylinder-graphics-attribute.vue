<!-- CylinderGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { CylinderGraphicsKey, CylinderGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { CylinderGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'CylinderGraphicsGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: CylinderGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.CylinderGraphics, CylinderGraphicsSerializateJSON>({
  graphic: () => props.entity?.cylinder,
  omit: props.omit,
  toJSON: (graphics, omit) => CylinderGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => CylinderGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="cylinder"
    graphics-field="show"
    label="可见"
  />
  <NumberAttribute
    v-if="!hide?.includes('length')"
    v-model="model.length"
    graphics="cylinder"
    graphics-field="length"
    label="长度"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('topRadius')"
    v-model="model.topRadius"
    graphics="cylinder"
    graphics-field="topRadius"
    label="顶部半径"
    :min="0"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('bottomRadius')"
    v-model="model.bottomRadius"
    graphics="cylinder"
    graphics-field="bottomRadius"
    label="底部半径"
    :min="0"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="cylinder"
    graphics-field="heightReference"
    label="高度参照"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="cylinder"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="cylinder"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="cylinder"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="cylinder"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="cylinder"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('numberOfVerticalLines')"
    v-model="model.numberOfVerticalLines"
    graphics="cylinder"
    graphics-field="numberOfVerticalLines"
    label="垂直线数"
  />
  <NumberAttribute
    v-if="!hide?.includes('slices')"
    v-model="model.slices"
    graphics="cylinder"
    graphics-field="slices"
    label="圆环边数"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="cylinder"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="cylinder"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
