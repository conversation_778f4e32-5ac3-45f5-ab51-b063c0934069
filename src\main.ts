import * as AMapPlugin from '@/components/amap/AMapPlugin';

import App from './app.vue';
import { setupRouter } from './router';

import { setupStore } from './stores';
import { createToDayjsPlugin } from './utils/to-dayjs';

import { createViewPortPlugin } from './utils/view-port';

import '@unocss/reset/tailwind.css';
import '@/styles/base.scss';
import '@/styles/theme.scss';
// import 'cesium/Build/Cesium/Widgets/widgets.css';
import 'uno.css';
import './element-plus';
import 'echarts';

export const app = createApp(App);
// dayjs  $toDayjs
app.use(createToDayjsPlugin());

// 屏幕适配 $vh $vw $viewPort
app.use(createViewPortPlugin());

// pinia
setupStore(app);

// 路由
setupRouter(app);

app.mount('#app');
AMapPlugin.Service.WebService.instance.register(import.meta.env.VITE_AMAP_TOKEN, import.meta.env.VITE_AMAP_URL);

// globalThis.Cesium = Cesium;
