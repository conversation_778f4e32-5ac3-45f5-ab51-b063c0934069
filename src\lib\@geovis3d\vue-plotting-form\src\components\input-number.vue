<!-- 与  ElInputNumber 无异，但拓展了unit单位渲染 -->
<script lang="ts" setup>
import type { BasicOrVNode } from '@/lib/@geovis3d/vue-component';
import type { InputNumberInstance } from 'element-plus';
import { useAllAttrs } from '@/lib/@geovis3d/hooks';

import { ResolveVnode } from '@/lib/@geovis3d/vue-component';
import { computed, shallowRef } from 'vue';

type ElInputNumberProps = InputNumberInstance['$props'];

export interface InputNumberProps extends /* @vue-ignore */ ElInputNumberProps {
  unit?: BasicOrVNode;
}

defineOptions({
  name: 'InputNumber',
  extends: ElInputNumber,
  inheritAttrs: false,
});

withDefaults(defineProps<InputNumberProps>(), {
  unit: '',
});

const elRef = shallowRef<InputNumberInstance>();

const to = computed(() => elRef.value?.$el?.querySelector('.el-input__wrapper'));

const allAttrs = useAllAttrs();
</script>

<template>
  <ElInputNumber v-bind="allAttrs" ref="elRef" />
  <teleport v-if="to" :to="to">
    <div class="el-input-number--unit">
      <slot name="unit">
        <ResolveVnode :render="unit" />
      </slot>
    </div>
  </teleport>
</template>

<style scoped lang="scss">
.el-input-number--unit {
  padding: 0 12px;
}
</style>
