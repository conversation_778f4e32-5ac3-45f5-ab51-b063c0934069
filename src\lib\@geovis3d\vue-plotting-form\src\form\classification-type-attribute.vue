<!-- ClassificationTypeAttribute -->
<script lang="ts" setup>
import type { ClassificationTypeSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'ClassificationTypeAttribute' });

const props = defineProps<{
  modelValue?: ClassificationTypeSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: ClassificationTypeSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const options = [
  {
    label: '无',
    value: 'BOTH',
  },
  {
    label: '仅地形',
    value: 'TERRAIN',
  },
  {
    label: '仅三维模型',
    value: 'CESIUM_3D_TILE',
  },
];
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="model" :options="options" :clearable="false" />
  </el-form-item>
</template>
