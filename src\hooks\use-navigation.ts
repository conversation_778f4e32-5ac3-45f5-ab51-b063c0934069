import type { MaybeElementRef } from '@vueuse/core';
import type { NavigationOptions } from 'cesium-navigation-es6';
import { useCzViewer } from '@x3d/vue-hooks';

import { Rectangle } from 'cesium';
import CesiumNavigation from 'cesium-navigation-es6';

interface INavigationDiv {
  compass: {
    node: HTMLDivElement;
    show: Ref<boolean>;
  };
  zoomControls: {
    node: HTMLDivElement;
    show: Ref<boolean>;
  };
  distanceLegend: {
    node: HTMLDivElement;
    show: Ref<boolean>;
  };
}
interface INavigationDom {
  distanceLegendDiv: HTMLDivElement;
  navigationDiv: HTMLDivElement;

}
type IType = 'compass' | 'zoomControls' | 'distanceLegend';
export const useNavigation = createGlobalState(() => {
  function initCesiumNavigation(): INavigationDiv {
    const showCompass = ref(false);
    const showZoomControls = ref(false);
    const showDistanceLegend = ref(false);
    const viewer = useCzViewer();

    const options: NavigationOptions = {
    };
    // 用于在使用重置导航重置地图视图时设置默认视图控制。接受的值是Cesium.Cartographic 和 Cesium.Rectangle.
    options.defaultResetView = new Rectangle(1.8670074555138327, 0.311765925867657, 1.963149547213567, 0.35556907991780545);
    // 用于启用或禁用罗盘。true是启用罗盘，false是禁用罗盘。默认值为true。如果将选项设置为false，则罗盘将不会添加到地图中。
    options.enableCompass = true;
    // 用于启用或禁用缩放控件。true是启用，false是禁用。默认值为true。如果将选项设置为false，则缩放控件将不会添加到地图中。
    options.enableZoomControls = true;
    // 用于启用或禁用距离图例。true是启用，false是禁用。默认值为true。如果将选项设置为false，距离图例将不会添加到地图中。
    options.enableDistanceLegend = true;
    // 用于启用或禁用指南针外环。true是启用，false是禁用。默认值为true。如果将选项设置为false，则该环将可见但无效。
    options.enableCompassOuterRing = true;

    // 修改重置视图的tooltip
    options.resetTooltip = '重置视图';
    // 修改放大按钮的tooltip
    options.zoomInTooltip = '放大';
    // 修改缩小按钮的tooltip
    options.zoomOutTooltip = '缩小';
    // 如需自定义罗盘控件，请看下面的自定义罗盘控件
    const navigation = new CesiumNavigation(viewer.value, options) as INavigationDom;
    const compassDiv = navigation.navigationDiv.childNodes?.[0] as HTMLDivElement;
    const zoomControlsDiv = navigation.navigationDiv.childNodes?.[1] as HTMLDivElement;
    watchImmediate(showCompass, (show) => {
      compassDiv && (compassDiv.style.display = show ? '' : 'none');
    });

    watchImmediate(showZoomControls, (show) => {
      zoomControlsDiv && (zoomControlsDiv.style.display = show ? '' : 'none');
    });
    watchImmediate(showDistanceLegend, (show) => {
      navigation.distanceLegendDiv.style.display = show ? '' : 'none';
    });
    return {
      compass: { node: compassDiv, show: showCompass },
      zoomControls: { node: zoomControlsDiv, show: showZoomControls },
      distanceLegend: { node: navigation.distanceLegendDiv, show: showDistanceLegend },
    };
  };
  const cesiumNavigation = initCesiumNavigation();
  return {
    append(type: IType, dom: MaybeElementRef<HTMLDivElement>) {
      const node = toValue(dom);
      cesiumNavigation[type].show.value = true;
      nextTick(() => {
        node?.append(cesiumNavigation[type].node);
      });
    },
    remove(type: IType) {
      cesiumNavigation[type].show.value = false;
    },
  };
});
