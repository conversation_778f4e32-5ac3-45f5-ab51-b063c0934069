<!-- 预警事件列表 -->
<script lang="ts" setup>
import { productionHazardousChemicalsWaringEventUsingGet } from '@/genapi/production';

import { Vue3SeamlessScroll } from 'vue3-seamless-scroll';

defineOptions({ name: 'RightRiskList' });

const { state: data, isLoading, execute } = useAsyncState(async () => {
  const { data } = await productionHazardousChemicalsWaringEventUsingGet({});
  return data;
}, undefined, {
  immediate: false,
});

/** 15分钟轮询 */
useIntervalFn(() => execute(), 1000 * 60 * 15, { immediateCallback: true });

const stats = computed(() => {
  return [
    {
      text: '总数',
      value: data?.value?.length ?? 0,
    },
    {
      text: '已销警',
      value: data?.value?.filter(e => +e.hasAlarmElimination !== 1)?.length ?? 0,
    },
    {
      text: '未销警',
      value: data?.value?.filter(e => +e.hasAlarmElimination === 1)?.length ?? 0,
    },
  ];
});
</script>

<template>
  <HeaderTitle2>24小时告警事件</HeaderTitle2>
  <div v-loading="isLoading" class="right-risk-list">
    <div class="m-b-10px flex justify-around">
      <span
        v-for="item in stats"
        :key="item.text"
        class="text-16px"
      >{{ item.text }}:{{ item.value }}</span>
    </div>
    <Vue3SeamlessScroll v-if="data?.length" :list="data" class="seamless" hover>
      <div v-for="(item, index) in data" :key="index" class="list-item">
        <span>{{ item.companyName }}</span>
        <span>告警设备：{{ item.equipName }}</span>
        <div>
          <span>{{ item.waringStatus }}</span>
          <span class="ml-10px">{{ +item.hasAlarmElimination === 1 ? '未销警' : '已销警' }}</span>
        </div>
      </div>
    </Vue3SeamlessScroll>
  </div>
</template>

<style scoped lang="scss">
  .right-risk-list {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  font-size: 14px;

  .seamless {
    flex: 1;
    overflow: hidden;
  }

  .list-item {
    display: flex;
    flex-direction: column;
    padding: 10px;
    margin: 5px 0;
    background: rgb(5 74 151 / 20%);
  }
}
</style>
