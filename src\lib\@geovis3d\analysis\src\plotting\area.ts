import type { GcEntityConstructorOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, cartographicToCartesian } from '@/lib/@geovis3d/coordinate';
import { PlottingEntity } from '@/lib/@geovis3d/core';
import * as Cesium from 'cesium';

import { area } from '../arithmetic/area';

export class AnalysisAreaEntity extends PlottingEntity {
  constructor(options?: GcEntityConstructorOptions) {
    super({
      polygon: {
        material: Cesium.Color.RED.withAlpha(0.3),
      },
      ...options,
      plotting: {
        options: {
          manualTerminate: entity => entity.plotting.coordinates.getLength() >= 2,
          location: { visible: false },
          control: { visible: true },
          altitude: { visible: false },
          interval: { visible: 'loop' },
          delete: { visible: true },
          update(entity) {
            if (!entity.polygon) {
              entity.polygon = new Cesium.PolygonGraphics({
                material: Cesium.Color.RED.withAlpha(0.8),
              });
            }
            const positions = entity.plotting.coordinates.getPositions();
            const mousePosition = entity.plotting.mousePosition;
            mousePosition && positions.push(mousePosition.clone());
            const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);

            if (coords.length < 3) {
              entity._cache = null;
              return;
            }
            entity._cache = new Cesium.PolygonHierarchy(positions);
            entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
            entity.position = new Cesium.ConstantPositionProperty(
              cartographicToCartesian(
                Cesium.Rectangle.center(Cesium.Rectangle.fromCartesianArray(positions)),
              ),
            );
            area(positions).then((e) => {
              let text: string = '';
              if (e / 1000 / 1000 > 10) {
                text = `${(e / 1000 / 1000).toFixed(2)}km²`;
              }
              else {
                text = `${(+e).toFixed(2)}m²`;
              }
              entity.label = new Cesium.LabelGraphics({
                text,
                font: '16pt Source Han Sans CN',
                pixelOffset: new Cesium.Cartesian2(0, -20),
              });
            });
          },
        },
      },
    });
  }
}
