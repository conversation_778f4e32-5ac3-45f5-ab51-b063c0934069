/**
 * 列表对象 转el-select option
 */
export function listToOptions<T extends Record<string, any>>(list: T[], label: keyof T, value: keyof T) {
  return list.map(e => ({
    label: e[label],
    value: e[value],
  }));
}

/**
 * 对象 转el-select option
 */
export function recordToOptions<T extends Record<string, any>>(record: T) {
  return Object.keys(record).map((key) => {
    return {
      label: record[key],
      value: key,
    };
  });
}

/**
 * 通过value获取label
 */
export function toRecordLabel(record: Record<string, any>, value: any, nullString = '') {
  return record[value] || nullString;
}
