import { openapiTypescriptExpand } from '@xiankq/openapi-typescript-expand';
import axios from 'axios';

// import { HttpsProxyAgent } from 'https-proxy-agent';

// axios.defaults.httpAgent = new HttpsProxyAgent('http://*************:8888', {
//   keepAlive: true,
// });

(async () => {
  const { data } = await axios.request({
    proxy: false, // 即便设置了http代理这里也要配置false，否则会报错
    url: 'http://************:8087/3d-data-system/server/v3/api-docs',
  });
  openapiTypescriptExpand(data, {
    output: './src/genapi/cimapi.ts',
    requestName: 'cimRequest',
    headerCode: 'import {cimRequest} from "./request";\n',

  });
})();

// (async () => {
//   const { data } = await axios.request({
//     proxy: false, // 即便设置了http代理这里也要配置false，否则会报错
//     url: 'https://cyyj.geovisearth.com/3d-data-system/server/v3/api-docs',
//   });
//   openapiTypescriptExpand(data, {
//     output: './src/genapi/cimapi.ts',
//     requestName: 'cimRequest',
//     headerCode: 'import {cimRequest} from "./request";\n',

//   });
// })();

// // 可视化安全生产专题
// (async () => {
//   const { data } = await axios.request({
//     url: 'http://localhost:3000/3d-data-system/visual-system/bdv/server/production/v3/api-docs',
//   });
//   openapiTypescriptExpand(data, {
//     output: './src/genapi/production.ts',
//     requestName: 'bdvRequest',
//     headerCode: 'import {bdvRequest} from "./request";\n',
//   });
// })();

// // 可视化 elasticsearch
// (async () => {
//   const { data } = await axios.request({
//     url: 'http://localhost:3000/3d-data-system/visual-system/bdv/server/elasticsearch/v3/api-docs',
//   });
//   openapiTypescriptExpand(data, {
//     output: './src/genapi/elasticsearch.ts',
//     requestName: 'bdvRequest',
//     headerCode: 'import {bdvRequest} from "./request";\n',
//   });
// })();

// // 可视化 disaster
// (async () => {
//   const { data } = await axios.request({
//     url: 'http://localhost:3000/3d-data-system/visual-system/bdv/server/disaster/v3/api-docs',
//   });
//   openapiTypescriptExpand(data, {
//     output: './src/genapi/disaster.ts',
//     requestName: 'bdvRequest',
//     headerCode: 'import {bdvRequest} from "./request";\n',
//   });
// })();

// // 态势系统
// (async () => {
//   const { data } = await axios.request({
//     url: 'http://localhost:3000/3d-data-system/sicuation-system/server/analysis/v3/api-docs',
//   });
//   openapiTypescriptExpand(data, {
//     output: './src/genapi/analysis.ts',
//     requestName: 'analysisRequest',
//     headerCode: 'import {analysisRequest} from "./request";\n',
//   });
// })();
