import type { AxiosRequestConfig } from 'axios';
import { useUserStore } from '@/stores/user';

import axios from 'axios';

function createInstance(config: AxiosRequestConfig = {}) {
  const instance = axios.create({
    headers: {
      'Content-Type': 'application/json',
    },
    maxRedirects: 0,
    ...config,
  });
  instance.interceptors.request.use((config) => {
    const { authorization } = useUserStore();
    config.headers!.Authorization = authorization;
    return config;
  });
  instance.interceptors.response.use(
    (response) => {
      const userStore = useUserStore();
      const code = +response.data?.code;
      if (code) {
        if ([4001, 4002, 4003, 4004, 4005].includes(code)) {
          userStore.logout();
          ElMessage.error('登录已失效');
        }
        else if (code >= 400) {
          console.error(response);
          const message = response?.data?.message || '请求异常';
          ElMessage.error(message);
        }

        if (code >= 400) {
          console.error(response);
          return Promise.reject(response);
        }
      }
      return response;
    },
    (error = {}) => {
      const message = error.response?.data?.message || '请求异常';
      const userStore = useUserStore();
      const code = +error.response?.data?.code;
      if ([4001, 4004, 4005, 4003, 4002].includes(code)) {
        userStore.logout();
      }
      else {
        ElMessage.error(message);
        return Promise.reject(error);
      }
    },
  );
  return instance;
}

const instance = createInstance();

/**
 * CIM 请求器
 */
export async function cimRequest<T>(options: AxiosRequestConfig<T> & Record<string, any>) {
  const { path = {}, ...params } = options;
  let url = options.url;
  for (const key in path) {
    url = url?.replaceAll(`{${key}}`, path[key]);
  }
  const { data } = await instance.request({
    baseURL: import.meta.env.VITE_CIM_URL,
    ...params,
    url,
  });
  return data;
}

/**
 * 可视化安全生产专题 请求器
 */
export async function bdvRequest<T>(options: AxiosRequestConfig<T> & Record<string, any>) {
  const { path = {}, ...params } = options;
  let url = options.url;
  for (const key in path) {
    url = url?.replaceAll(`{${key}}`, path[key]);
  }
  const { data } = await instance.request({
    baseURL: `${import.meta.env.VITE_VISUAL_SYSTEM_PATH}/bdv/server`,
    ...params,
    url,
  });
  return data;
}

export async function analysisRequest<T>(options: AxiosRequestConfig<T> & Record<string, any>) {
  const { path = {}, ...params } = options;
  let url = options.url;
  for (const key in path) {
    url = url?.replaceAll(`{${key}}`, path[key]);
  }
  const { data } = await instance.request({
    baseURL: `${import.meta.env.VITE_SICUATION_SYSTEM_PATH}/server`,
    ...params,
    url,
  });
  return data;
}
