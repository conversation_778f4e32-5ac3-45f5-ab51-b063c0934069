window.Mp4Player=function(r){var i={};function __webpack_require__(e){var t;return(i[e]||(t=i[e]={i:e,l:!1,exports:{}},r[e].call(t.exports,t,t.exports,__webpack_require__),t.l=!0,t)).exports}return __webpack_require__.m=r,__webpack_require__.c=i,__webpack_require__.d=function(e,t,r){__webpack_require__.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},__webpack_require__.t=function(t,e){if(1&e&&(t=__webpack_require__(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(__webpack_require__.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)__webpack_require__.d(r,i,function(e){return t[e]}.bind(null,i));return r},__webpack_require__.n=function(e){var t=e&&e.__esModule?function getDefault(){return e.default}:function getModuleExports(){return e};return __webpack_require__.d(t,"a",t),t},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.p="",__webpack_require__(__webpack_require__.s=14)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),n=_interopRequireDefault(r(1)),a=_interopRequireDefault(r(2));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}r=function(){function Box(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Box),this.headSize=8,this.size=0,this.type="",this.subBox=[],this.start=-1}return i(Box,[{key:"readHeader",value:function readHeader(e){if(this.start=e.position,this.size=e.readUint32(),this.type=String.fromCharCode(e.readUint8(),e.readUint8(),e.readUint8(),e.readUint8()),1===this.size)this.size=e.readUint64();else if(0===this.size&&"mdat"!==this.type)throw new a.default("parse","",{line:19,handle:"[Box] readHeader",msg:"parse mp4 mdat box failed"});if("uuid"===this.type)for(var t=[],r=0;r<16;r++)t.push(e.readUint8());"pssh"===this.type&&(this.version=e.readUint8(),this.flags=e.readUint8())}},{key:"readBody",value:function readBody(e){var t=this.size-e.position+this.start,r=this.type,t=(this.data=e.buffer.slice(e.position,e.position+t),e.position+=this.data.byteLength,void 0);(t=Box.containerBox.find(function(e){return e===r})?Box.containerParser:Box[r])&&t instanceof Function&&t.call(this)}},{key:"read",value:function read(e){this.readHeader(e),this.readBody(e)}}],[{key:"containerParser",value:function containerParser(){for(var e=new n.default(this.data),t=e.buffer.byteLength;e.position<t;){var r=new Box;r.readHeader(e),this.subBox.push(r),r.readBody(e)}delete this.data}}]),Box}();r.containerBox=["moov","trak","edts","mdia","minf","dinf","stbl","mvex","moof","traf","mfra","sinf","schi"],t.default=r,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),n=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(2));r=function(){function Stream(e){if(!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Stream),!(e instanceof ArrayBuffer))throw new n.default("parse","",{line:9,handle:"[Stream] constructor",msg:"data is valid"});this.buffer=e,this.dataview=new DataView(e),this.dataview.position=0}return i(Stream,[{key:"skip",value:function skip(e){for(var t=Math.floor(e/4),e=e%4,r=0;r<t;r++)Stream.readByte(this.dataview,4);0<e&&Stream.readByte(this.dataview,e)}},{key:"readUint8",value:function readUint8(){return Stream.readByte(this.dataview,1)}},{key:"readUint16",value:function readUint16(){return Stream.readByte(this.dataview,2)}},{key:"readUint24",value:function readUint24(){return Stream.readByte(this.dataview,3)}},{key:"readUint32",value:function readUint32(){return Stream.readByte(this.dataview,4)}},{key:"readUint64",value:function readUint64(){return Stream.readByte(this.dataview,8)}},{key:"readInt8",value:function readInt8(){return Stream.readByte(this.dataview,1,!0)}},{key:"readInt16",value:function readInt16(){return Stream.readByte(this.dataview,2,!0)}},{key:"readInt32",value:function readInt32(){return Stream.readByte(this.dataview,4,!0)}},{key:"position",set:function set(e){this.dataview.position=e},get:function get(){return this.dataview.position}}],[{key:"readByte",value:function readByte(e,t,r){var i=void 0;switch(t){case 1:i=r?e.getInt8(e.position):e.getUint8(e.position);break;case 2:i=r?e.getInt16(e.position):e.getUint16(e.position);break;case 3:if(r)throw"not supported for readByte 3";i=e.getUint8(e.position)<<16,i=(i|=e.getUint8(e.position+1)<<8)|e.getUint8(e.position+2);break;case 4:i=r?e.getInt32(e.position):e.getUint32(e.position);break;case 8:if(r)throw new n.default("parse","",{line:73,handle:"[Stream] readByte",msg:"not supported for readBody 8"});i=e.getUint32(e.position)<<32,i|=e.getUint32(e.position+4);break;default:i=""}return e.position+=t,i}}]),Stream}();t.default=r,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(3)),n=r(36);r=function(){function _Errors(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:"",e=(!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,_Errors),r.version=n.version,function _possibleConstructorReturn(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(_Errors.__proto__||Object.getPrototypeOf(_Errors)).call(this,e,t,r)));return e.url=i,e}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(_Errors,i.default.Errors),_Errors}();t.default=r,e.exports=t.default},function(e,t){e.exports=window.Player},function(e,t,r){"use strict";var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=r(17),u=r(34),s=Function.prototype.apply,f=Function.prototype.call,n=Object.create,a=Object.defineProperty,d=Object.defineProperties,l=Object.prototype.hasOwnProperty,c={configurable:!0,enumerable:!1,writable:!0},h=function on(e,t){var r;return u(t),l.call(this,"__ee__")?r=this.__ee__:(r=c.value=n(null),a(this,"__ee__",c),c.value=null),r[e]?"object"===o(r[e])?r[e].push(t):r[e]=[r[e],t]:r[e]=t,this},r=function once(e,t){var r,i;return u(t),i=this,h.call(this,e,r=function once(){p.call(i,e,r),s.call(t,this,arguments)}),r.__eeOnceListener__=t,this},p=function off(e,t){var r,i,n,a;if(u(t),l.call(this,"__ee__")&&(r=this.__ee__)[e])if("object"===(void 0===(i=r[e])?"undefined":o(i)))for(a=0;n=i[a];++a)n!==t&&n.__eeOnceListener__!==t||(2===i.length?r[e]=i[a?0:1]:i.splice(a,1));else i!==t&&i.__eeOnceListener__!==t||delete r[e];return this},_=function emit(e){var t,r,i,n,a;if(l.call(this,"__ee__")&&(n=this.__ee__[e]))if("object"===(void 0===n?"undefined":o(n))){for(r=arguments.length,a=new Array(r-1),t=1;t<r;++t)a[t-1]=arguments[t];for(n=n.slice(),t=0;i=n[t];++t)s.call(i,this,a)}else switch(arguments.length){case 1:f.call(n,this);break;case 2:f.call(n,this,arguments[1]);break;case 3:f.call(n,this,arguments[1],arguments[2]);break;default:for(r=arguments.length,a=new Array(r-1),t=1;t<r;++t)a[t-1]=arguments[t];s.call(n,this,a)}},v={on:h,once:r,off:p,emit:_},m={on:i(h),once:i(r),off:i(p),emit:i(_)},y=d({},m);e.exports=t=function exports(e){return null==e?n(y):d(Object(e),m)},t.methods=v},function(e,t,r){"use strict";var i=r(28)();e.exports=function(e){return e!==i&&null!==e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}();var n=function(){function UTC(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,UTC);var e=new Date;e.setFullYear(1904),e.setMonth(0),e.setDate(1),e.setHours(0),e.setMinutes(0),e.setSeconds(0),this.time=e}return i(UTC,[{key:"setTime",value:function setTime(e){return this.time.setTime(this.time.getTime()+ +e),this.time.toLocaleString()}}]),UTC}();t.default=n,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),x=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(11));r=function(){function SPSParser(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,SPSParser)}return i(SPSParser,null,[{key:"_ebsp2rbsp",value:function _ebsp2rbsp(e){for(var t=e,r=t.byteLength,i=new Uint8Array(r),n=0,a=0;a<r;a++)2<=a&&3===t[a]&&0===t[a-1]&&0===t[a-2]||(i[n]=t[a],n++);return new Uint8Array(i.buffer,0,n)}},{key:"parseSPS",value:function parseSPS(e){var e=SPSParser._ebsp2rbsp(e),t=new x.default(e),e=(t.readByte(),t.readByte()),r=(t.readByte(),t.readByte()),i=(t.readUEG(),SPSParser.getProfileString(e)),r=SPSParser.getLevelString(r),n=1,a=420,o=8;if((100===e||110===e||122===e||244===e||44===e||83===e||86===e||118===e||128===e||138===e||144===e)&&(3===(n=t.readUEG())&&t.readBits(1),n<=3&&(a=[0,420,422,444][n]),o=t.readUEG()+8,t.readUEG(),t.readBits(1),t.readBool()))for(var u=3!==n?8:12,s=0;s<u;s++)t.readBool()&&SPSParser._skipScalingList(t,s<6?16:64);t.readUEG();e=t.readUEG();if(0===e)t.readUEG();else if(1===e){t.readBits(1),t.readSEG(),t.readSEG();for(var f=t.readUEG(),d=0;d<f;d++)t.readSEG()}t.readUEG(),t.readBits(1);var e=t.readUEG(),l=t.readUEG(),c=t.readBits(1),h=(0===c&&t.readBits(1),t.readBits(1),0),p=0,_=0,v=0,m=(t.readBool()&&(h=t.readUEG(),p=t.readUEG(),_=t.readUEG(),v=t.readUEG()),1),y=1,g=0,b=!0,w=0,k=0,P=(t.readBool()&&(t.readBool()&&(0<(S=t.readByte())&&S<16?(m=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][S-1],y=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][S-1]):255===S&&(m=t.readByte()<<8|t.readByte(),y=t.readByte()<<8|t.readByte())),t.readBool()&&t.readBool(),t.readBool()&&(t.readBits(4),t.readBool())&&t.readBits(24),t.readBool()&&(t.readUEG(),t.readUEG()),t.readBool())&&(S=t.readBits(32),P=t.readBits(32),b=t.readBool(),g=(w=P)/(k=2*S)),1),S=(1===m&&1===y||(P=m/y),0),U=0,n=(U=0===n?(S=1,2-c):(S=3===n?1:2,(1===n?2:1)*(2-c)),16*(e+1)),e=16*(l+1)*(2-c),l=(n-=(h+p)*S,e-=(_+v)*U,Math.ceil(n*P));return t.destroy(),t=null,{profile_string:i,level_string:r,bit_depth:o,chroma_format:a,chroma_format_string:SPSParser.getChromaFormatString(a),frame_rate:{fixed:b,fps:g,fps_den:k,fps_num:w},par_ratio:{width:m,height:y},codec_size:{width:n,height:e},present_size:{width:l,height:e}}}},{key:"_skipScalingList",value:function _skipScalingList(e,t){for(var r=8,i=8,n=0;n<t;n++)r=0===(i=0!==i?(r+e.readSEG()+256)%256:i)?r:i}},{key:"getProfileString",value:function getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}},{key:"getLevelString",value:function getLevelString(e){return(e/10).toFixed(1)}},{key:"getChromaFormatString",value:function getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}},{key:"toVideoMeta",value:function toVideoMeta(e){var t={},e=(e&&e.codec_size&&(t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height),t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t.parRatio={width:e.par_ratio.width,height:e.par_ratio.height},t.frameRate=e.frame_rate,t.frameRate.fps_den),r=t.frameRate.fps_num;t.refSampleDuration=Math.floor(t.timescale*(e/r))}}]),SPSParser}();t.default=r,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.CUSTOM_EVENTS={MEDIA_EXPIRED:"MEDIA_EXPIRED",INIT_FAIL:"INIT_FAIL",PARSE_ERROR:"PARSE_ERROR",BUFFERED_RESET:"BUFFERED_RESET"},t.TASK_ERROR="TASK_ERROR",t.TASK_ERROR_TYPES={ERROR:"ERROR",CODE_ERROR:"CODE_ERROR",CANCEL:"CANCEL"}},function(e,t,r){"use strict";e.exports=function(e){return null!=e}},function(e,t,r){"use strict";r=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(37));e.exports=r.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}();var n=function(){function Golomb(e){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Golomb),this.TAG="Golomb",this._buffer=e,this._bufferIndex=0,this._totalBytes=e.byteLength,this._totalBits=8*e.byteLength,this._currentWord=0,this._currentWordBitsLeft=0}return i(Golomb,[{key:"destroy",value:function destroy(){this._buffer=null}},{key:"_fillCurrentWord",value:function _fillCurrentWord(){var e=this._totalBytes-this._bufferIndex,e=Math.min(4,e),t=new Uint8Array(4);t.set(this._buffer.subarray(this._bufferIndex,this._bufferIndex+e)),this._currentWord=new DataView(t.buffer).getUint32(0),this._bufferIndex+=e,this._currentWordBitsLeft=8*e}},{key:"readBits",value:function readBits(e){var t=Math.min(this._currentWordBitsLeft,e),r=this._currentWord>>>32-t;if(32<e)throw new Error("Cannot read more than 32 bits at a time");return this._currentWordBitsLeft-=t,0<this._currentWordBitsLeft?this._currentWord<<=t:0<this._totalBytes-this._bufferIndex&&this._fillCurrentWord(),0<(t=e-t)&&this._currentWordBitsLeft?r<<t|this.readBits(t):r}},{key:"readBool",value:function readBool(){return 1===this.readBits(1)}},{key:"readByte",value:function readByte(){return this.readBits(8)}},{key:"_skipLeadingZero",value:function _skipLeadingZero(){for(var e=void 0,e=0;e<this._currentWordBitsLeft;e++)if(0!=(this._currentWord&2147483648>>>e))return this._currentWord<<=e,this._currentWordBitsLeft-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}},{key:"readUEG",value:function readUEG(){var e=this._skipLeadingZero();return this.readBits(e+1)-1}},{key:"readSEG",value:function readSEG(){var e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}]),Golomb}();t.default=n,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),n=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(10));r=function(){function Buffer(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Buffer),this.buffer=new Uint8Array(0)}return i(Buffer,[{key:"write",value:function write(){for(var t=this,e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];r.forEach(function(e){e?t.buffer=(0,n.default)(Uint8Array,t.buffer,e):window.console.error(e)})}}],[{key:"writeUint16",value:function writeUint16(e){return new Uint8Array([e>>8&255,255&e])}},{key:"writeUint32",value:function writeUint32(e){return new Uint8Array([e>>24,e>>16&255,e>>8&255,255&e])}}]),Buffer}();t.default=r,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),a=_interopRequireDefault(r(4)),o=_interopRequireDefault(r(2)),u=r(8),s=_interopRequireDefault(r(84));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}r=function(){function Task(t,r,e,i){var n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{};!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Task),(0,a.default)(this),window.Task=Task,this.url=t,this.range=r,n.start=r[0],n.end=r[1],this.uniqueTag=this.url+"&range="+r[0]+"-"+r[1],this.playerId=n.playerId,Task.queue.some(function(e){return e.url===t&&JSON.stringify(e.range)===JSON.stringify(r)&&e.playerId===n.playerId})||(this.xhrSetup=e,this.id=this.playerId+r.join("-"),this.running=!1,this.canceled=!1,this.initialize(t,r,i))}return i(Task,[{key:"initialize",value:function initialize(t,e,r){var i=new window.XMLHttpRequest,n=(i.target=this,i.responseType="arraybuffer",i.withCredentials=this.withCredentials||!1,i.open("get",t),"function"==typeof this.xhrSetup&&this.xhrSetup(i,t),i.setRequestHeader("Range","bytes="+e[0]+"-"+e[1]),this);i.onreadystatechange=function(e){2===i.readyState&&(n.headers=(0,s.default)(i),n.status=i.status),i.readyState},i.onload=function(){200===i.status||206===i.status?r&&r instanceof Function&&r(i.response):403===i.status?n.emit(u.CUSTOM_EVENTS.MEDIA_EXPIRED):n._emitTaskError(u.TASK_ERROR_TYPES.CODE_ERROR),i.target.remove()},i.onerror=function(e){i.target.emit("error",new o.default("network","",{line:25,handle:"[Task] constructor",msg:e.message,url:t})),i.target.remove()},i.onabort=function(){i.target.remove()},n.canceled||(n.xhr=i,Task.queue.push(n)),n.update()}},{key:"cancel",value:function cancel(){this.xhr.abort(),this._emitTaskError(u.TASK_ERROR_TYPES.CANCEL),this.canceled=!0}},{key:"_emitTaskError",value:function _emitTaskError(e){this.emit(u.TASK_ERROR,{code:e,url:this.uniqueTag,readyState:this.xhr.readyState,status:this.xhr.status})}},{key:"remove",value:function remove(){var r=this;Task.queue.filter(function(e,t){return e.url===r.url&&e.id===r.id&&(Task.queue.splice(t,1),!0)}),this.update()}},{key:"update",value:function update(){var e=Task.queue,t=e.filter(function(e){return e.running}),e=e.filter(function(e){return!e.running}),r=Task.limit-t.length;e.forEach(function(e,t){t<r&&e.run()})}},{key:"run",value:function run(){1===this.xhr.readyState?(this.running=!0,this.xhr.send()):this.remove()}}],[{key:"clear",value:function clear(e){for(var t=Task.queue,r=t.length-1;-1<r;r--){var i=t[r];i.running&&i.playerId===e&&i.cancel(),t.splice(r,1)}}}]),Task}();r.queue=[],r.limit=2,t.default=r,e.exports=t.default},function(e,t,r){e.exports=r(15)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),a=function get(e,t,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,t);return void 0!==i?"value"in i?i.value:void 0!==(i=i.get)?i.call(r):void 0:null!==(i=Object.getPrototypeOf(e))?get(i,t,r):void 0},s=_interopRequireDefault(r(3)),o=_interopRequireDefault(r(16)),f=_interopRequireDefault(r(86)),u=_interopRequireDefault(r(13)),d=r(87),l=_interopRequireDefault(r(88)),c=_interopRequireDefault(r(89)),h=r(8),p=_interopRequireDefault(r(90));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}var _="DESTROYED",v=s.default.sniffer,m=function isEnded(e,t){t.meta.endTime-e.currentTime<.5&&e.duration-e.currentTime<.5&&(e.mse.endOfStream(),e._stopProgress())},r=function(){function Mp4Player(e){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Mp4Player);var t=!0,r=((e.onlyInit||e.videoInit)&&(e.onlyInit=!0,e.autoplay=!1,t=e.videoInit=!1),function _possibleConstructorReturn(e,t){if(e)return!t||"object"!=typeof t&&"function"!=typeof t?e:t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}(this,(Mp4Player.__proto__||Object.getPrototypeOf(Mp4Player)).call(this,e)));return r._pendingPromises=[],r._allInitPromise=new l.default,r._maxBufferLength=e.maxBufferLength||5,r._playerId=Mp4Player.uniqueId++,r._onBufferedResetFunc=r._onBufferedReset.bind(r),r._onSeekingFunc=r._onSeeking.bind(r),r._onMp4InitFunc=r._onMp4Init.bind(r),r._onWaitingFunc=r._onWaiting.bind(r),r._onEndedFunc=r._onEnded.bind(r),r._onDestroyFunc=r._onDestroy.bind(r),r._replay=r._onReplay.bind(r),r._onOnlineHandlerFunc=r._onOnlineHandler.bind(r),r._onOfflineHandlerFunc=r._onOfflineHandler.bind(r),r._tickInSeconds=e.tickInSeconds||.2,r._hasStartProgress=t,r._hasStartProgressBack=t,r.video.addEventListener(h.CUSTOM_EVENTS.BUFFERED_RESET,r._onBufferedResetFunc),r._bindNetworkStateChange(),r._initMp4Kernal(),r.once("ready",function(){r.gapJumpInst=new p.default(r,r.config),s.default.util.on(r,"addVideoBufferEnd",function(){r.gapJumpInst.onSegmentAppend()})}),r}return function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(Mp4Player,s.default),n(Mp4Player,[{key:"_initMp4Kernal",value:function _initMp4Kernal(){var r=this,e=r.config.pluginRule||function(){return!0};if(f.default.isSupported('video/mp4; codecs="avc1.64001E, mp4a.40.5"')){if(!e.call(r))return!1;Object.defineProperty(r,"src",{get:function get(){return r.currentSrc},set:function set(e){r.mse.endOfStream(),r._onDestroy(),r.config.autoplay=r.autoplay=!0,r.config.url=e,r._init(e).then(function(e){var t=e[0],e=e[1];r.proxyOnce("canplay",function(){r.play(),r.proxyOn("waiting",r._onWaitingFunc)}),r._startProgress(),r._onSuperStart(e.url),r.mp4=t,r.mse=e},function(e){r._errorHandle(e)})},configurable:!0}),r.proxyOn("ended",r._onEndedFunc),r.proxyOnce("destroy",r._onDestroyFunc),(r.config.videoInit||r.config.onlyInit)&&r.start(r.config.url)}}},{key:"start",value:function start(t){var r=this,i=(t=t||r.config.url,r.config.autoplay&&"chrome"==s.default.sniffer.browser&&!s.default.util.hasClass(r.root,"xgplayer-is-enter")&&s.default.util.addClass(r.root,"xgplayer-is-enter"),r.proxyOnce("error",function(){s.default.util.hasClass(r.root,"xgplayer-is-enter")&&s.default.util.removeClass(r.root,"xgplayer-is-enter")}),r.proxyOn("timeupdate",function(){s.default.util.hasClass(r.root,"xgplayer-isloading")&&s.default.util.removeClass(r.root,"xgplayer-isloading")}),r.proxyOnce("canplay",function(){var e;s.default.util.hasClass(r.root,"xgplayer-is-enter")&&s.default.util.removeClass(r.root,"xgplayer-is-enter"),"safari"===v.browser&&r.buffered&&r.config.autoplay&&(e=r.buffered.start(0),r.currentTime=e+.1)}),r._init(t));i.id="init",r._addPendingPromise(i),i.then(function(e){r._allInitPromise.resolve(),r._hasInited=!0,r._removePendingPromise(i);var t=e[0],e=e[1];r.config.autoplay&&r.proxyOnce("canplay",function(){r.play()}),r._onSuperStart(e.url),r.mp4=t,r.mse=e,s.default.util.on(t,"error",function(e){r._errorHandle(e)}),r.proxyOn("seeking",r._onSeekingFunc),r.proxyOnce("playing",r._onMp4InitFunc),r.proxyOn("waiting",r._onWaitingFunc),r._startProgress()},function(e){e!==_&&(r._onSuperStart(t),r._errorHandle(e))})}},{key:"switchURL",value:function switchURL(e){var i=this,n=new o.default(e,i.config.xhrSetup,i,i.config.preloadSize,{playerId:i._playerId}),a=i.mp4;s.default.util.on(n,"moovReady",function(){a.timeRage;var t=i.currentTime,e=a.timeRage.find(function(e){return 2<e[0]-t})[0],r=i.getBufferedRange(i.buffered2)[1];0<r-e&&"safari"!==v.browser&&i.mse.removeBuffer(e,r),s.default.util.hasClass(i.root,"xgplayer-ended")||i.emit("urlchange"),i.mp4=n,i.mse.appendBuffer(n.packMeta(a.meta))}),s.default.util.on(n,"error",function(e){i._errorHandle(e)})}},{key:"_onBufferedReset",value:function _onBufferedReset(){this.mp4&&this.mp4.timeRage&&this.mp4.timeRage.forEach(function(e){e.downloaded=!1})}},{key:"_onOnlineHandler",value:function _onOnlineHandler(){this._hasInited?(this._hasStartProgressBack&&this._startProgress(),this._hasStartProgressBack=!1):this.src=this.config.url}},{key:"_onOfflineHandler",value:function _onOfflineHandler(){this._hasStartProgress&&(this._stopProgress(),this._hasStartProgressBack=!0)}},{key:"_bindNetworkStateChange",value:function _bindNetworkStateChange(){window.addEventListener("online",this._onOnlineHandlerFunc),window.addEventListener("offline",this._onOfflineHandlerFunc)}},{key:"_unbindNetworkStateChange",value:function _unbindNetworkStateChange(){window.removeEventListener("online",this._onOnlineHandlerFunc),window.removeEventListener("offline",this._onOfflineHandlerFunc)}},{key:"_onSuperStart",value:function _onSuperStart(e){a(Mp4Player.prototype.__proto__||Object.getPrototypeOf(Mp4Player.prototype),"start",this).call(this,e)}},{key:"_init",value:function _init(e){var t=new l.default;return this._initMp4(e,t),t}},{key:"_initMse",value:function _initMse(t,e,r){var i=this,n=-1<e.videoCodec.indexOf("hvc1")||e.encv&&-1<e.encv.data_format.indexOf("hvc1"),a=!!e.videoCodec,e=!!e.audioCodec,o=void 0,o=a&&e?n?'video/mp4; codecs="hev1.1.6.L93.B0, mp4a.40.5"':'video/mp4; codecs="avc1.64001E, mp4a.40.5"':a?n?'video/mp4; codecs="hev1.1.6.L93.B0"':'video/mp4; codecs="avc1.64001E"':'video/mp4; codecs="mp4a.40.5"',u=new f.default(o,i.config.mediaType);s.default.util.on(u,"sourceopen",function(){var e=t.packMeta(t.meta);u.appendBuffer(e),s.default.util.once(u,"updateend",function(){i._loadData()})}),s.default.util.on(u,"error",function(e){r.reject(e)}),r.resolve([t,u])}},{key:"_initMp4",value:function _initMp4(e,t){var r=this,i=new o.default(e,this.config.xhrSetup,this,this.config.preloadSize,{playerId:this._playerId});s.default.util.once(i,"metaReady",function(e){r._initMse(i,e,t)}),s.default.util.on(i,"error",function(e){t.reject(e)})}},{key:"_loadData",value:function _loadData(){var r,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;navigator.onLine&&(r=this).mp4&&(t=t||r.currentTime,r.mp4.seek(t+.1*e).then(function(e){var t;e&&r.mse&&((t=r.mse).updating=!0,s.default.util.once(t,"updateend",function(){t.updating=!1,r.emit("addVideoBufferEnd")}),t.appendBuffer(e))},function(){e<10&&setTimeout(function(){r._loadData(e+1)},2e3)}))}},{key:"_onTimeUpdate",value:function _onTimeUpdate(){var r,e,i=this,t=i.mse,n=i.mp4;t&&!t.updating&&n&&n.canDownload&&(t=n.timeRage,r=i.getBufferedRange(i.buffered2),e=i.currentTime+i._maxBufferLength,0<r[1]-e||(t.every(function(e,t){return!(!e.downloaded&&(0===r[1]?e[0]<=i.currentTime&&(i.currentTime<e[1]||i.duration-e[1]<.5)&&(i._loadData(0,e[0]),1):(e[0]>=r[1]||e[1]-1>=r[1]||r[1]>=e[0]&&r[1]<=e[0]+1)&&(i._loadData(0,e[0]),1)))}),m(i,n)))}},{key:"_onWaiting",value:function _onWaiting(){var e=this,t=e.currentTime;e._onInnerWait(t),t+2<e.duration&&e._onInnerWait(t+2),0<t-2&&e._onInnerWait(t-2)}},{key:"_onEnded",value:function _onEnded(){this.off("waiting",this._onWaitingFunc),this._stopProgress()}},{key:"_onReplay",value:function _onReplay(){var r=this;r._onDestroy(),r._init(r.config.url).then(function(e){var t=e[0],e=e[1];r._startProgress(),r._onSuperStart(e.url),r.mp4=t,r.mse=e,r.proxyOnce("canplay",function(){r.play(),r.proxyOn("waiting",r._onWaitingFunc)})},function(e){r._errorHandle(e)})}},{key:"_onDestroy",value:function _onDestroy(){var e=this;e._hasInited=!1,e._unbindNetworkStateChange(),u.default.clear(e._playerId),e.mp4&&(e.mp4.destroy(),e.mp4=null),e.mse&&e.mse.destroy(),e.unloadVideo(),e._stopProgress(),e.cancelPendingPromises()}},{key:"_errorHandle",value:function _errorHandle(e){var t;"network"!==e.errt&&"network"!==e.errorType&&"parse"!==e.errt&&"parse"!==e.errorType&&(e.url=(t=this).src,e.errd&&"object"===i(e.errd)&&t.mp4&&(e.errd.url=t.mp4.url,e.url=t.mp4.url,t.mp4.canDownload=!1),t.emit("DATA_REPORT",e),u.default.clear(t._playerId),t.mp4&&t.mp4.bufferCache&&t.mp4.bufferCache.clear(),t.currentTime&&(t._currentTime=t.currentTime),t._start&&(t.start=t._start,t._start=null),t.switchURL=null,t._replay=null,t.off("seeking",t._onSeekingFunc),t.off("playing",t._onMp4InitFunc),t.off("waiting",t._onWaitingFunc),t.off("ended",t._onEndedFunc),t.off("destroy",t._onDestroyFunc),iplayer.emit("error",e),t.src=t.config.url,t.proxyOnce("canplay",function(){t._currentTime&&(t.currentTime=t._currentTime),t.play()}))}},{key:"_onSeeking",value:function _onSeeking(){var e=this.currentTime;this._onCheckLoad(e),m(this,this.mp4)}},{key:"_onInnerWait",value:function _onInnerWait(e){this._onCheckLoad(e)}},{key:"_onCheckLoad",value:function _onCheckLoad(r){var i=this,e=i.buffered2||i.buffered,t=!1,n=i.mp4.timeRage;if(e.length){for(var a=0,o=e.length;a<o;a++)if(r>=e.start(a)&&r<=e.end(a)){t=!0;break}t||n.every(function(e,t){return!(e[0]<=r&&(e[1]>r||i.duration-e[1]<.5)&&(i._loadData(0,e[0]),1))})}else n.every(function(e,t){return!(e[0]<=r&&e[1]>r&&(i._loadData(0,e[0]),1))})}},{key:"_onMp4Init",value:function _onMp4Init(){this.config.mp4Init&&this.pause()}},{key:"_stopProgress",value:function _stopProgress(){this._hasStartProgress=!1,this._requestTimer&&(this._requestTimer.stop(),this._requestTimer=null)}},{key:"_startProgress",value:function _startProgress(){var e=this;e._stopProgress(),e._requestTimer=new c.default(function(){e._requestTimer&&e._onTimeUpdate()}),e._requestTimer.tickEvery(e._tickInSeconds),this._hasStartProgress=!0}},{key:"unloadVideo",value:function unloadVideo(){try{this.video&&this.video.src&&(this.video.removeAttribute("src"),this.video.load())}catch(e){}}},{key:"_addPendingPromise",value:function _addPendingPromise(e){this._pendingPromises.push(e)}},{key:"_removePendingPromise",value:function _removePendingPromise(e){e=this._pendingPromises.indexOf(e);-1<e&&this._pendingPromises.splice(e,1)}},{key:"onMediaExpired",value:function onMediaExpired(){this._stopProgress(),this.emit(h.CUSTOM_EVENTS.MEDIA_EXPIRED)}},{key:"cancelPendingPromises",value:function cancelPendingPromises(){0<this._pendingPromises.length&&this._pendingPromises.forEach(function(e){e.reject(_)}),this._pendingPromises=[]}},{key:"enableAutoBuffer",value:function enableAutoBuffer(e){var t=this;e?this._allInitPromise&&this._allInitPromise.then(function(){t._startProgress()}):this._stopProgress()}},{key:"destroy",value:function destroy(){this.mp4&&this.mp4.destroy(),this.mse&&this.mse.destroy(),this.video&&this.video.removeEventListener(h.CUSTOM_EVENTS.BUFFERED_RESET,this._onBufferedResetFunc),a(Mp4Player.prototype.__proto__||Object.getPrototypeOf(Mp4Player.prototype),"destroy",this).call(this)}},{key:"emitInitFail",value:function emitInitFail(e){this.emit(h.CUSTOM_EVENTS.INIT_FAIL,e)}},{key:"emitParseError",value:function emitParseError(){this.emit(h.CUSTOM_EVENTS.PARSE_ERROR)}},{key:"ready",get:function get(){return this._allInitPromise}}]),Mp4Player}();r.uniqueId=1,r.isSupported=d.isSupported,r.isSupportedWithXgmse=d.isSupportedWithXgmse,r.isMediaSourceSupported=d.isMediaSourceSupported,r.CUSTOM_EVENTS=h.CUSTOM_EVENTS,t.default=r,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),a=_interopRequireDefault(r(4)),o=_interopRequireDefault(r(35)),n=_interopRequireDefault(r(12)),u=_interopRequireDefault(r(83)),s=_interopRequireDefault(r(13)),x=_interopRequireDefault(r(85)),M=_interopRequireDefault(r(2)),f=r(8),d=_interopRequireDefault(r(3));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}r=function(){function MP4(e,t,r){var i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:Math.pow(25,4),n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:{};!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,MP4),(0,a.default)(this),this.url=e,this.xhrSetup=t,this.CHUNK_SIZE=i,this.player=r,this.ext=n,this.timeRage=[],this.canDownload=!0,this.init(),d.default.util.once(this,"moovReady",this.moovParse.bind(this))}return i(MP4,[{key:"getData",value:function getData(){var i=this,n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:n+this.CHUNK_SIZE-1,o=this;return new Promise(function(t,r){var e=new s.default(i.url,[n,a],i.xhrSetup,function(e){o.hasDestroyed||t(e)},o.ext);d.default.util.once(e,f.CUSTOM_EVENTS.MEDIA_EXPIRED,function(){o.player&&o.player.onMediaExpired()}),d.default.util.once(e,f.TASK_ERROR,function(e){r({code:e.code,status:e.status})})})}},{key:"moovParse",value:function moovParse(){var e,t,r,i,n=this,u=this,a=this.moovBox,o=x.default.findBox(a,"mvhd"),a=x.default.findBox(a,"trak"),s=void 0,f=void 0,d=void 0,l=void 0,c=void 0,h=void 0,p=void 0,_=void 0,v=void 0,m=void 0,y=void 0,g=void 0,b=void 0,w=void 0,k=void 0,P=void 0,S=void 0,U=[1,1];(a=Array.isArray(a)?a:[a]).forEach(function(e){var t,r,i,n,a=x.default.findBox(e,"hdlr"),o=x.default.findBox(e,"mdhd");a&&o?(t=x.default.findBox(e,"stsd").subBox[0],"vide"===a.handleType&&(i=void 0,(r=x.default.findBox(e,"avcC"))||(i=x.default.findBox(e,"hvcC")),n=x.default.findBox(e,"tkhd"),s=e,c=o.timescale,r?(d=t.type+"."+x.default.toHex(r.profile,r.profileCompatibility,r.AVCLevelIndication).join(""),p=r.sequence,U=[r.spsInfo.par_ratio.width,r.spsInfo.par_ratio.height],r.spsInfo.codec_size&&(y=r.spsInfo.codec_size.width,g=r.spsInfo.codec_size.height),_=r.pps&&r.pps.map(function(e){return Number("0x"+e)}),m=r.profile):i?(S=i.data,(r=x.default.findBox(e,"hvc1"))&&(P=r.data,y=r.width,g=r.height),d=t.type+"."+x.default.toHex(i.profile,i.profileCompatibility,i.profileCompatibilityIndications).join(""),v=i.vps&&i.vps.map(function(e){return Number("0x"+e)}),p=i.sequence,_=i.pps&&i.pps.map(function(e){return Number("0x"+e)}),m=i.profile):d=""+t.type,n)&&!y&&(y=n.width,g=n.height),"soun"===a.handleType&&(f=e,r=x.default.findBox(e,"esds"),i=x.default.findBox(e,"mp4a"),n=x.default.findBox(e,5),h=o.timescale,l=r?t.type+"."+x.default.toHex(r.subBox[0].subBox[0].typeID)+"."+r.subBox[0].subBox[0].subBox[0].type:""+t.type,n&&n.EScode&&(k=n.EScode.map(function(e){return Number("0x"+e)})),i)&&(b=i.channelCount,w=i.sampleRate)):u.emit("error",new M.default("parse","",{line:101,handle:"[MP4] moovParse",url:u.url}))}),p&&(this.videoTrak=s,this.audioTrak=f||null,a=this._boxes.find(function(e){return"mdat"===e.type}),e=x.default.seekTrakDuration(s,c),t=f?x.default.seekTrakDuration(f,h):e,this.mdatStart=a.start,r=this.videoKeyFrames,i=r.length-1,r.forEach(function(e,t){t<i?n.timeRage.push([e.time.time/c,r[t+1].time.time/c]):n.timeRage.push([e.time.time/c,o.duration/o.timeScale])}),this.meta={videoCodec:d,audioCodec:l,createTime:o.createTime,modifyTime:o.modifyTime,duration:o.duration/o.timeScale,timeScale:o.timeScale,videoDuration:e,videoTimeScale:c,audioDuration:t,audioTimeScale:h,endTime:Math.min(e,t),vps:v,sps:p,pps:_,width:y,height:g,profile:m,pixelRatio:U,channelCount:b,sampleRate:w,audioConfig:k,hvc1Data:P,hvcCData:S,ext:{videoTrak:this.videoTrak,audioTrak:this.audioTrak,mdatStart:this.mdatStart,timeRage:this.timeRage},stss:this._stssObj},this.emit("metaReady",this.meta))}},{key:"init",value:function init(){var a=this;a.getData().then(function(e){var t=void 0,r=0,i=void 0,n=void 0;try{t=new o.default(e)}catch(e){return a.emit("error",e.type?e:new M.default("parse","",{line:176,handle:"[MP4] init",msg:e.message})),!1}a._boxes=n=t.boxes,n.every(function(e){return r+=e.size,"moov"!==e.type||(i=e,a.moovBox=i,a.emit("moovReady",i),!1)}),i||((e=t.nextBox)?"moov"===e.type?a.getData(r,r+e.size+28).then(function(e){e=new o.default(e);a._boxes=a._boxes.concat(e.boxes),(i=e.boxes.filter(function(e){return"moov"===e.type})).length?(a.moovBox=i[0],a.emit("moovReady",i)):a.emit("error",new M.default("parse","",{line:203,handle:"[MP4] init",msg:"not find moov box"}))}):a.emit("error",new M.default("parse","",{line:207,handle:"[MP4] init",msg:"not find moov box"})):a.getData(r,"").then(function(e){e=new o.default(e);e?(a._boxes=a._boxes.concat(e.boxes),e.boxes.every(function(e){return"moov"!==e.type||(i=e,a.moovBox=i,a.emit("moovReady",i),!1)})):a.emit("error",new M.default("parse","",{line:225,handle:"[MP4] init",msg:"not find moov box"}))}))}).catch(function(){a.emit("error",new M.default("network","",{line:231,handle:"[MP4] getData",msg:"getData failed"}))})}},{key:"getSamplesByOrders",value:function getSamplesByOrders(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"video",t=arguments[1],r=arguments[2],i="video"===e?this.videoTrak:this.audioTrak,n=x.default.findBox(i,"stsc"),a=x.default.findBox(i,"stsz"),o=x.default.findBox(i,"stts"),u=x.default.findBox(i,"stco"),s="video"===e?this._cttsObj:null,f="video"===e?this._stscVideoObj:this._stscAudioObj,d=this.mdatStart,l=[],r=void 0!==r?r:a.entries.length;if(t instanceof Array)t.forEach(function(e,t){l.push({idx:e,size:a.entries[e],time:x.default.seekSampleTime(o,s,e),offset:x.default.seekSampleOffset(n,u,a,e,d,f)})});else if(0!==r)for(var c=t;c<r;c++)l.push({idx:c,size:a.entries[c],time:x.default.seekSampleTime(o,s,c),offset:x.default.seekSampleOffset(n,u,a,c,d,f)});else l={idx:t,size:a.entries[t],time:x.default.seekSampleTime(o,s,t),offset:x.default.seekSampleOffset(n,u,a,t,d,f)};return l}},{key:"packMeta",value:function packMeta(){var e;if(this.meta)return(e=new n.default).write(u.default.ftyp()),e.write(u.default.moov(this.meta)),e.buffer}},{key:"getRangeFromTime",value:function getRangeFromTime(e){var e=this.getFragmentIdx(e),t=this.getFragRange(e);return t===[0,0]?null:{range:t,fragIndex:e}}},{key:"getFragmentIdx",value:function getFragmentIdx(e){var i,n=Math.round(e*this.meta.videoTimeScale),a=void 0,o=this.videoKeyFrames;return o.every(function(e,t){var e=e.time.time,r=o[t+1]?o[t+1].time.time:Number.MAX_SAFE_INTEGER;return!(e<=n&&n<r&&(a=t,1))}),this.audioTrak&&(i=this.audioKeyFrames).every(function(e,t){var e=e.startTime,r=i[t+1]?i[t+1].startTime:Number.MAX_SAFE_INTEGER;return!(e<=n&&n<r&&(a=Math.min(t,a),1))}),a}},{key:"seek",value:function seek(e){e=this.getFragmentIdx(e);return this.timeRage[e].downloaded=!0,this.loadFragment(e)}},{key:"getFragRange",value:function getFragRange(e){var t,r=this.videoKeyFrames[e].offset,i=void 0;return this.audioTrak&&(t=this.getSamplesByOrders("audio",this.audioKeyFrames[e].order,0),r=Math.min(r,t.offset)),e<this.videoKeyFrames.length-1&&(i=this.videoKeyFrames[e+1].offset,this.audioTrak)&&(t=this.getSamplesByOrders("audio",this.audioKeyFrames[e+1].order,0),i=Math.max(i,t.offset)),window.isNaN(r)||void 0!==i&&window.isNaN(i)?(this.emit("error",new M.default("parse","",{line:366,handle:"[MP4] loadFragment",url:this.url})),[0,0]):[r+this.mdatStart,i?this.mdatStart+i:""]}},{key:"loadFragment",value:function loadFragment(t){var r=this,i=this,n=this.getFragRange(t);return n!==[0,0]&&this.getData(n[0],n[1]).then(function(e){return i.createFragment(new Uint8Array(e),n[0]-r.mdatStart,t)}).then(function(e){return e})}},{key:"addFragment",value:function addFragment(t){var r=new n.default;return new Promise(function(e){r.write(u.default.moof(t)),r.write(u.default.mdat(t)),e(r.buffer)})}},{key:"getVideoBuffer",value:function getVideoBuffer(r,i,e){var t=this.videoKeyFrames.map(function(e){return e.idx}),t=this.getSamplesByOrders("video",t[e],t[e+1]),n=t.map(function(e,t){return{size:e.size,duration:e.time.duration,offset:e.time.offset,buffer:new Uint8Array(r.slice(e.offset-i,e.offset-i+e.size)),key:0===t,idx:e.idx}});return this.addFragment({id:1,time:t[0].time.time,firstFlags:33554432,flags:3841,samples:n,sampleOffset:t[0].idx,fragIndex:e})}},{key:"getAudioBuffer",value:function getAudioBuffer(r,i,e){var t=this.getSamplesByOrders("audio",this.audioKeyFrames[e].order,this.audioKeyFrames[e+1]?this.audioKeyFrames[e+1].order:void 0),n=t.map(function(e,t){return{size:e.size,duration:e.time.duration,offset:e.time.offset,buffer:new Uint8Array(r.slice(e.offset-i,e.offset-i+e.size)),key:0===t}});return this.addFragment({id:2,time:t[0].time.time,firstFlags:0,flags:1793,samples:n,sampleOffset:t[0].idx,fragIndex:e})}},{key:"createFragment",value:function createFragment(e,t,r){var n=[],i=[this.getVideoBuffer(e,t,r)];return this.audioTrak&&i.push(this.getAudioBuffer(e,t,r)),Promise.all(i).then(function(e){n.push(e[0]),e&&e[1]&&n.push(e[1]);var t=0,r=(n.every(function(e){return t+=e.byteLength,!0}),new Uint8Array(t)),i=0;return n.every(function(e){return r.set(e,i),i+=e.byteLength,!0}),r})}},{key:"update",value:function update(e){this.url=e}},{key:"destroy",value:function destroy(){if(!this.hasDestroyed){for(var e in this)delete this[e];this.hasDestroyed=!0}}},{key:"videoKeyFrames",get:function get(){if(this._videoFrames)return this._videoFrames;var e=this.videoTrak,t=x.default.findBox(e,"stss"),r=x.default.findBox(e,"stsc"),i=x.default.findBox(e,"ctts");if(this._cttsObj=null,i){this._cttsObj={};for(var n=0,a=0;a<i.entry.length;a++)for(var o=i.entry[a],u=0;u<o.count;u++)this._cttsObj[n]=o.offset,n+=1}this._stscVideoObj={};for(var s=0,f=0;f<r.count-1;f++)for(var d=r.entries[f],l=0;l<d.chunk_count*d.samples_per_chunk;l++)this._stscVideoObj[++s]=d;e=this.getSamplesByOrders("video",t.entries.map(function(e){return e-1}));return this._stssObj=t,this._videoFrames=e}},{key:"audioKeyFrames",get:function get(){if(!this._audioFrames){for(var t=x.default.findBox(this.videoTrak,"mdhd").timescale,r=x.default.findBox(this.audioTrak,"mdhd").timescale,i=x.default.findBox(this.audioTrak,"stts").entry,e=x.default.findBox(this.audioTrak,"stsc"),n=(this._stscAudioObj={},0),a=0;a<e.count-1;a++)for(var o=e.entries[a],u=0;u<o.chunk_count*o.samples_per_chunk;u++)this._stscAudioObj[++n]=o;var s=this.videoKeyFrames.map(function(e){return x.default.seekOrderSampleByTime(i,r,e.time.time/t)});this._audioFrames=s}return this._audioFrames}}]),MP4}();t.default=r,e.exports=t.default},function(e,t,r){"use strict";var o=r(9),a=r(18),u=r(22),s=r(30),f=r(31);(e.exports=function(e,t){var r,i,n,a;return arguments.length<2||"string"!=typeof e?(a=t,t=e,e=null):a=arguments[2],o(e)?(r=f.call(e,"c"),i=f.call(e,"e"),n=f.call(e,"w")):i=!(r=n=!0),e={value:t,configurable:r,enumerable:i,writable:n},a?u(s(a),e):e}).gs=function(e,t,r){var i,n;return"string"!=typeof e?(n=r,r=t,t=e,e=null):n=arguments[3],o(t)?a(t)?o(r)?a(r)||(n=r,r=void 0):r=void 0:(n=t,t=r=void 0):t=void 0,e=o(e)?(i=f.call(e,"c"),f.call(e,"e")):!(i=!0),t={get:t,set:r,configurable:i,enumerable:e},n?u(s(n),t):t}},function(e,t,r){"use strict";var i=r(19),n=/^\s*class[\s{/}]/,a=Function.prototype.toString;e.exports=function(e){return!!i(e)&&!n.test(a.call(e))}},function(e,t,r){"use strict";var i=r(20);e.exports=function(e){if("function"!=typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!=typeof e.length)return!1;if("function"!=typeof e.call)return!1;if("function"!=typeof e.apply)return!1}catch(e){return!1}return!i(e)}},function(e,t,r){"use strict";var i=r(21);e.exports=function(e){if(!i(e))return!1;try{return e.constructor?e.constructor.prototype===e:!1}catch(e){return!1}}},function(e,t,r){"use strict";var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=r(9),a={object:!0,function:!0,undefined:!0};e.exports=function(e){return!!n(e)&&hasOwnProperty.call(a,void 0===e?"undefined":i(e))}},function(e,t,r){"use strict";e.exports=r(23)()?Object.assign:r(24)},function(e,t,r){"use strict";e.exports=function(){var e=Object.assign;return"function"==typeof e&&(e(e={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}},function(e,t,r){"use strict";var o=r(25),u=r(29),s=Math.max;e.exports=function(t,r){var i,e,n,a=s(arguments.length,2);for(t=Object(u(t)),n=function assign(e){try{t[e]=r[e]}catch(e){i=i||e}},e=1;e<a;++e)o(r=arguments[e]).forEach(n);if(void 0!==i)throw i;return t}},function(e,t,r){"use strict";e.exports=r(26)()?Object.keys:r(27)},function(e,t,r){"use strict";e.exports=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}},function(e,t,r){"use strict";var i=r(5),n=Object.keys;e.exports=function(e){return n(i(e)?Object(e):e)}},function(e,t,r){"use strict";e.exports=function(){}},function(e,t,r){"use strict";var i=r(5);e.exports=function(e){if(i(e))return e;throw new TypeError("Cannot use null or undefined")}},function(e,t,r){"use strict";var i=r(5),n=Array.prototype.forEach,a=Object.create,o=function process(e,t){for(var r in e)t[r]=e[r]};e.exports=function(e){var t=a(null);return n.call(arguments,function(e){i(e)&&o(Object(e),t)}),t}},function(e,t,r){"use strict";e.exports=r(32)()?String.prototype.contains:r(33)},function(e,t,r){"use strict";var i="razdwatrzy";e.exports=function(){return"function"==typeof i.contains&&!0===i.contains("dwa")&&!1===i.contains("foo")}},function(e,t,r){"use strict";var i=String.prototype.indexOf;e.exports=function(e){return-1<i.call(this,e,arguments[1])}},function(e,t,r){"use strict";e.exports=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=_interopRequireDefault(r(0)),o=_interopRequireDefault(r(10)),u=_interopRequireDefault(r(1)),i=_interopRequireDefault(r(38)),n=_interopRequireDefault(r(39)),s=_interopRequireDefault(r(40)),f=_interopRequireDefault(r(41)),d=_interopRequireDefault(r(42)),l=_interopRequireDefault(r(43)),c=_interopRequireDefault(r(44)),h=_interopRequireDefault(r(45)),p=_interopRequireDefault(r(46)),_=_interopRequireDefault(r(47)),v=_interopRequireDefault(r(48)),m=_interopRequireDefault(r(49)),y=_interopRequireDefault(r(50)),g=_interopRequireDefault(r(51)),b=_interopRequireDefault(r(52)),w=_interopRequireDefault(r(7)),k=_interopRequireDefault(r(53)),P=_interopRequireDefault(r(54)),S=_interopRequireDefault(r(55)),U=_interopRequireDefault(r(56)),x=_interopRequireDefault(r(57)),M=_interopRequireDefault(r(58)),R=_interopRequireDefault(r(59)),D=_interopRequireDefault(r(60)),B=_interopRequireDefault(r(61)),T=_interopRequireDefault(r(62)),E=_interopRequireDefault(r(63)),q=_interopRequireDefault(r(64)),C=_interopRequireDefault(r(65)),O=_interopRequireDefault(r(66)),I=_interopRequireDefault(r(67)),A=_interopRequireDefault(r(68)),L=_interopRequireDefault(r(69)),z=_interopRequireDefault(r(70)),j=_interopRequireDefault(r(71)),H=_interopRequireDefault(r(72)),G=_interopRequireDefault(r(11)),N=_interopRequireDefault(r(73)),W=_interopRequireDefault(r(74)),V=_interopRequireDefault(r(75)),K=_interopRequireDefault(r(76)),J=_interopRequireDefault(r(77)),X=_interopRequireDefault(r(78)),Z=_interopRequireDefault(r(79)),Y=_interopRequireDefault(r(80)),$=_interopRequireDefault(r(81));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}var F={};function _buildTree(e,r,i){var n=e;r.map(function(e,t){n[e]=t==r.length-1?i:n[e]||{},n=n[e]})}_buildTree(F,["box","avc1"],_interopRequireDefault(r(82)).default),_buildTree(F,["box","avcC"],$.default),_buildTree(F,["box","btrt"],Y.default),_buildTree(F,["box","co64"],Z.default),_buildTree(F,["box","ctts"],X.default),_buildTree(F,["box","dref"],J.default),_buildTree(F,["box","elst"],K.default),_buildTree(F,["box","esds"],V.default),_buildTree(F,["box","frma"],W.default),_buildTree(F,["box","ftyp"],N.default),_buildTree(F,["box","golomb"],G.default),_buildTree(F,["box","hdlr"],H.default),_buildTree(F,["box","hmhd"],j.default),_buildTree(F,["box","hvc1"],z.default),_buildTree(F,["box","hvcC"],L.default),_buildTree(F,["box","iods"],A.default),_buildTree(F,["box","mdat"],I.default),_buildTree(F,["box","mdhd"],O.default),_buildTree(F,["box","mfhd"],C.default),_buildTree(F,["box","mp4a"],q.default),_buildTree(F,["box","MP4DecConfigDescrTag"],E.default),_buildTree(F,["box","MP4DecSpecificDescrTag"],T.default),_buildTree(F,["box","MP4ESDescrTag"],B.default),_buildTree(F,["box","mvhd"],D.default),_buildTree(F,["box","nmhd"],R.default),_buildTree(F,["box","pasp"],M.default),_buildTree(F,["box","sbgp"],x.default),_buildTree(F,["box","schm"],U.default),_buildTree(F,["box","sdtp"],S.default),_buildTree(F,["box","SLConfigDescriptor"],P.default),_buildTree(F,["box","smhd"],k.default),_buildTree(F,["box","sps"],w.default),_buildTree(F,["box","stco"],b.default),_buildTree(F,["box","stsc"],g.default),_buildTree(F,["box","stsd"],y.default),_buildTree(F,["box","stsh"],m.default),_buildTree(F,["box","stss"],v.default),_buildTree(F,["box","stsz"],_.default),_buildTree(F,["box","stts"],p.default),_buildTree(F,["box","stz2"],h.default),_buildTree(F,["box","tfhd"],c.default),_buildTree(F,["box","tkhd"],l.default),_buildTree(F,["box","traf"],d.default),_buildTree(F,["box","trun"],f.default),_buildTree(F,["box","udta"],s.default),_buildTree(F,["box","url"],n.default),_buildTree(F,["box","vmhd"],i.default),t.default=function Parse(e){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Parse),this.buffer=null,this.boxes=[],this.nextBox=null,this.start=0;for(var t=this,r=(t.buffer?(0,o.default)(Uint8Array,t.buffer,e):t.buffer=e,e.byteLength),i=(e.position=0,new u.default(e));8<=r-i.position;){var n=new a.default;if(n.readHeader(i),!(n.size-8<=r-i.position)&&"mdat"!==n.type){t.nextBox=n,i.position-=8;break}n.readBody(i),t.boxes.push(n)}t.buffer=new Uint8Array(t.buffer.slice(i.position))},e.exports=t.default},function(e){e.exports=JSON.parse('{"version":"2.0.4"}')},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){for(var t=0,r=arguments.length,i=Array(1<r?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];var a=!0,o=!1,u=void 0;try{for(var s,f=i[Symbol.iterator]();!(a=(s=f.next()).done);a=!0)t+=s.value.length}catch(e){o=!0,u=e}finally{try{!a&&f.return&&f.return()}finally{if(o)throw u}}var d=new e(t),l=0,c=!0,o=!1,u=void 0;try{for(var h,p=i[Symbol.iterator]();!(c=(h=p.next()).done);c=!0){var _=h.value;d.set(_,l),l+=_.length}}catch(e){o=!0,u=e}finally{try{!c&&p.return&&p.return()}finally{if(o)throw u}}return d}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.vmhd=function(){var e=new n.default(this.data);this.version=e.readUint8(),this.flag=[e.readUint8(),e.readUint8(),e.readUint8()],this.graphicsmode=e.readUint16(),this.opcolor=[e.readUint16(),e.readUint16(),e.readUint16()],delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default["url "]=function(){for(var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=[e.readUint8(),e.readUint8(),e.readUint8()],[]),r=e.buffer.byteLength;e.position<r;)t.push(e.readUint8());this.location=t,delete this.subBox,delete this.data}},function(e,t,r){"use strict";(function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}})(r(0)).default.udta=function(){delete this.subBox}},function(e,t,r){},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1)),a=_interopRequireDefault(r(6));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.tkhd=function(){for(var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3,0),1===this.version?(this.create=e.readUint64(),this.modify=e.readUint64(),this.createTime=(new a.default).setTime(1e3*this.create),this.modifyTime=(new a.default).setTime(1e3*this.modify),this.trackID=e.readUint32(),this.reserverd=e.readUint32(),this.duration=e.readUint64()):(this.create=e.readUint32(),this.modify=e.readUint32(),this.createTime=(new a.default).setTime(1e3*this.create),this.modifyTime=(new a.default).setTime(1e3*this.modify),this.trackID=e.readUint32(),this.reserverd=e.readUint32(),this.duration=e.readUint32()),e.readUint64(),this.layer=e.readInt16(),this.alternate_group=e.readInt16(),this.volume=e.readInt16()>>8,e.readUint16(),[]),r=0;r<9;r++)t.push(e.readUint16()+"."+e.readUint16());this.matrix=t,this.width=e.readUint16()+"."+e.readUint16(),this.height=e.readUint16()+"."+e.readUint16(),delete this.data,delete this.subBox}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),o=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.tfhd=function(){var e=new o.default(this.data),t=(this.version=e.readUint8(),this.flag=o.default.readByte(e.dataview,3),this.track_id=e.readUint32(),1&this.flag),r=2&this.flag,i=8&this.flag,n=16&this.flag,a=32&this.flag;t&&(this.baseDataOffset=e.readUint32()),r&&(this.sampleDescriptionIndex=e.readUint32()),i&&(this.defaultSampleDuration=e.readUint32()),n&&(this.defaultSampleSize=e.readUint32()),a&&(this.defaultSampleFlags=e.readUint32()),delete this.subBox,delete this.data}},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.stts=function(){for(var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.count=e.readUint32(),[]),r=0,i=this.count;r<i;r++)t.push({sampleCount:e.readUint32(),sampleDuration:e.readUint32()});this.entry=t,delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.stsz=function(){var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.sampleSize=e.readUint32(),this.count=e.readUint32(),[]);this.entries=t;for(var r=0,i=this.count;r<i;r++)this.sampleSize?t.push(this.sampleSize):t.push(e.readUint32());delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.stss=function(){var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.count=e.readUint32(),[]);this.entries=t;for(var r=0,i=this.count;r<i;r++)t.push(e.readUint32());delete this.subBox,delete this.data}},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.stsd=function(){var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.entryCount=e.readUint32(),new i.default);t.readHeader(e),this.subBox.push(t),t.readBody(e),delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),d=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.stsc=function(){var e=new d.default(this.data),t=(this.version=e.readUint8(),this.flag=d.default.readByte(e.dataview,3),this.count=e.readUint32(),[]);this.entries=t;for(var r=0,i=this.count;r<i;r++)t.push({first_chunk:e.readUint32(),samples_per_chunk:e.readUint32(),sample_desc_index:e.readUint32()});for(var n,a,o,u,s=0,f=this.count;s<f-1;s++)n=t[s],a=t[s-1],n.chunk_count=t[s+1].first_chunk-n.first_chunk,n.first_sample=0===s?1:a.first_sample+a.chunk_count*a.samples_per_chunk;1===this.count?((o=t[0]).first_sample=1,o.chunk_count=0):1<this.count&&(o=t[this.count-1],u=t[this.count-2],o.first_sample=u.first_sample+u.chunk_count*u.samples_per_chunk,o.chunk_count=0),delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.stco=function(){var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.count=e.readUint32(),[]);this.entries=t;for(var r=0,i=this.count;r<i;r++)t.push(e.readUint32());delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.smhd=function(){var e=new n.default(this.data);this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.balance=e.readInt8()+"."+e.readInt8(),delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(0));i.default.SLConfigDescriptor=function(e){var t=new i.default,r=void 0;return t.type=e.readUint8(),128===(r=e.readUint8())?(t.extend=!0,e.skip(2),r=e.readUint8()+5):r+=2,t.size=r,t.SL=e.readUint8(),delete t.subBox,t}},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.schm=function(){var e=new n.default(this.data);this.scheme_version=e.readUint32(),this.scheme_type="";for(var t=0;t<4;t++)this.scheme_type+=String.fromCharCode(e.readUint8());if(1&this.flags){this.scheme_uri="";for(t=0;t<this.size-this.hdr_size-8;t++)this.scheme_uri+=String.fromCharCode(e.readUint8())}}},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.pasp=function(){var e=new n.default(this.data);this.hSpacing=e.readUint32(),this.vSpacing=e.readUint32()}},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1)),a=_interopRequireDefault(r(6));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.mvhd=function(){for(var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.create=e.readUint32(),this.modify=e.readUint32(),this.createTime=(new a.default).setTime(1e3*this.create),this.modifyTime=(new a.default).setTime(1e3*this.modify),this.timeScale=e.readUint32(),this.duration=e.readUint32(),this.rate=e.readUint16()+"."+e.readUint16(),this.volume=e.readUint8()+"."+e.readUint8(),n.default.readByte(e.dataview,8),n.default.readByte(e.dataview,2),[]),r=0;r<9;r++)t.push(e.readUint16()+"."+e.readUint16());this.matrix=t,n.default.readByte(e.dataview,24),this.nextTrackID=e.readUint32(),delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(0));i.default.MP4ESDescrTag=function(e){var t=new i.default,r=void 0;return t.type=e.readUint8(),128===(r=e.readUint8())?(t.extend=!0,e.skip(2),r=e.readUint8()+5):r+=2,t.size=r,t.esID=e.readUint16(),t.priority=e.readUint8(),t.subBox.push(i.default.MP4DecConfigDescrTag(e)),t.subBox.push(i.default.SLConfigDescriptor(e)),t}},function(e,t,r){"use strict";var o=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(0));o.default.MP4DecSpecificDescrTag=function(e){for(var t=new o.default,r=void 0,i=void 0,n=(t.type=e.readUint8(),128===(r=e.readUint8())?(t.extend=!0,e.skip(2),i=(r=e.readUint8()+5)-5):(i=r,r+=2),t.size=r,[]),a=0;a<i;a++)n.push(Number(e.readUint8()).toString(16).padStart(2,"0"));return t.EScode=n,delete t.subBox,t}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.MP4DecConfigDescrTag=function(e){var t=new i.default,r=void 0;return t.type=e.readUint8(),128===(r=e.readUint8())?(t.extend=!0,e.skip(2),r=e.readUint8()+5):r+=2,t.size=r,t.typeID=e.readUint8(),t.streamUint=e.readUint8(),t.bufferSize=n.default.readByte(e.dataview,3),t.maximum=e.readUint32(),t.average=e.readUint32(),t.subBox.push(i.default.MP4DecSpecificDescrTag(e)),t}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.mp4a=function(){var e=new n.default(this.data),t=(e.skip(6),this.dataReferenceIndex=e.readUint16(),e.skip(8),this.channelCount=e.readUint16(),this.sampleSize=e.readUint16(),e.skip(4),this.sampleRate=e.readUint32()>>16,new i.default);t.readHeader(e),this.subBox.push(t),t.readBody(e),delete this.data}},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1)),a=_interopRequireDefault(r(6));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.mdhd=function(){var e=new n.default(this.data);this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),1===this.version?(this.create=e.readUint64(),this.modify=e.readUint64(),this.createTime=(new a.default).setTime(1e3*this.create),this.modifyTime=(new a.default).setTime(1e3*this.modify),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.create=e.readUint32(),this.modify=e.readUint32(),this.createTime=(new a.default).setTime(1e3*this.create),this.modifyTime=(new a.default).setTime(1e3*this.modify),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.language=e.readUint16(),e.readUint16(),delete this.subBox,delete this.data}},function(e,t,r){"use strict";(function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}})(r(0)).default.mdat=function(){delete this.subBox}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.iods=function(){for(var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),[]),r=e.buffer.byteLength;e.position<r;)t.push(e.readUint8());this.content=t,delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),d=_interopRequireDefault(r(1));_interopRequireDefault(r(7));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.hvcC=function(){var e=new d.default(this.data);this.configVersion=e.readUint8(),this.profile=e.readUint8(),this.profileCompatibility=e.readUint32(),this.constraintIndicatorFlags=[];for(var t=0;t<6;t++)this.constraintIndicatorFlags.push(Number(e.readUint8()).toString(16));this.levelIdc=e.readUint8(),this.profileCompatibilityIndications=e.readUint32(),this.bitDepthLumaMinus8=e.readUint8(),this.bitDepthChromaMinus8=e.readUint8(),this.avgFrameRate=e.readUint16(),this.constantFrameRate=e.readUint8(),this.numOfArrays=e.readUint8(),this.vpsHeader=e.readUint24(),this.vpsLength=e.readUint16();for(var r=[],i=0;i<this.vpsLength;i++)r.push(Number(e.readUint8()).toString(16));this.vps=r,this.spsHeader=e.readUint24(),this.spsLength=e.readUint16();for(var n=[],a=0;a<this.spsLength;a++)n.push(Number(e.readUint8()).toString(16));this.sequence=n,this.ppsHeader=e.readUint24(),this.ppsLength=e.readUint16();for(var o=[],u=0;u<this.ppsLength;u++)o.push(Number(e.readUint8()).toString(16));this.pps=o;for(var s=[],f=e.dataview.byteLength;e.position<f;)s.push(e.readUint8());this.last=s,delete this.subBox}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.hvc1=function(){var e=new n.default(this.data);e.skip(6),this.dataReferenceIndex=e.readUint16(),e.skip(16),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.skip(4),this.frameCount=e.readUint16(),e.skip(1);for(var t=0;t<31;t++)String.fromCharCode(e.readUint8());for(this.depth=e.readUint16(),e.skip(2);e.position<e.buffer.byteLength;){var r=new i.default;r.readHeader(e),this.subBox.push(r),r.readBody(e)}}},function(e,t,r){},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.hdlr=function(){for(var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),e.skip(4),this.handleType=""+String.fromCharCode(e.readUint8())+String.fromCharCode(e.readUint8())+String.fromCharCode(e.readUint8())+String.fromCharCode(e.readUint8()),e.skip(12),[]);e.position<this.size-8;)t.push(String.fromCharCode(e.readUint8()));this.name=t.join(""),delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.ftyp=function(){for(var e=new n.default(this.data),t=(this.major_brand=String.fromCharCode(e.readUint8(),e.readUint8(),e.readUint8(),e.readUint8()),this.minor_version=e.readUint32(),[]),r=0,i=Math.floor((e.buffer.byteLength-8)/4);r<i;r++)t.push(String.fromCharCode(e.readUint8(),e.readUint8(),e.readUint8(),e.readUint8()));this.compatible_brands=t,e=null,delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.frma=function(){var e=new n.default(this.data);this.data_format="";for(var t=0;t<4;t++)this.data_format+=String.fromCharCode(e.readUint8())}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.esds=function(){var e=new n.default(this.data),e=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),i.default.MP4ESDescrTag(e));this.subBox.push(e),delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),a=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.elst=function(){var e=new a.default(this.data),t=(this.version=e.readUint8(),this.flag=a.default.readByte(e.dataview,3),[]),r=e.readUint32();this.entries=t;for(var i=0;i<r;i++){var n={};t.push(n),1===this.version?(n.segment_duration=e.readUint64(),n.media_time=e.readUint64()):(n.segment_duration=e.readUint32(),n.media_time=e.readInt32()),n.media_rate_integer=e.readInt16(),n.media_rate_fraction=e.readInt16()}delete this.subBox,delete this.data}},function(e,t,r){"use strict";var n=_interopRequireDefault(r(0)),a=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}n.default.dref=function(){for(var e=new a.default(this.data),t=(this.version=e.readUint8(),this.flag=a.default.readByte(e.dataview,3),e.readUint32()),r=(this.entryCount=t,0);r<t;r++){var i=new n.default;this.subBox.push(i),i.read(e)}delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.ctts=function(){var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.entryCount=e.readUint32(),[]);this.entry=t;for(var r=0,i=this.entryCount;r<i;r++)t.push({count:e.readUint32(),offset:e.readUint32()});delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.co64=function(){var e=new n.default(this.data),t=(this.version=e.readUint8(),this.flag=n.default.readByte(e.dataview,3),this.count=e.readUint32(),[]);this.entries=t;for(var r=0,i=this.count;r<i;r++)t.push(e.readUint64());delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.btrt=function(){var e=new n.default(this.data);this.bufferSizeDB=e.readUint32(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32(),delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),f=_interopRequireDefault(r(1)),d=_interopRequireDefault(r(7));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.avcC=function(){for(var e=new f.default(this.data),t=(this.configVersion=e.readUint8(),this.profile=e.readUint8(),this.profileCompatibility=e.readUint8(),this.AVCLevelIndication=e.readUint8(),this.lengthSizeMinusOne=1+(3&e.readUint8()),this.numOfSequenceParameterSets=31&e.readUint8(),e.readUint16()),r=(this.sequenceLength=t,[]),i=0;i<t;i++)r.push(e.readUint8());this.spsInfo=d.default.parseSPS(new Uint8Array(r)),this.ppsCount=e.readUint8();for(var n=e.readUint16(),a=(this.ppsLength=n,[]),o=0;o<n;o++)a.push(Number(e.readUint8()).toString(16));this.pps=a,this.sequence=r;for(var u=[],s=e.dataview.byteLength;e.position<s;)u.push(e.readUint8());this.last=u,delete this.subBox,delete this.data}},function(e,t,r){"use strict";var i=_interopRequireDefault(r(0)),n=_interopRequireDefault(r(1));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}i.default.avc1=function(){var e=new n.default(this.data);e.skip(6),this.dataReferenceIndex=e.readUint16(),e.skip(16),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.skip(4),this.frameCount=e.readUint16(),e.skip(1);for(var t=0;t<31;t++)String.fromCharCode(e.readUint8());for(this.depth=e.readUint16(),e.skip(2);e.position<e.buffer.byteLength;){var r=new i.default;r.readHeader(e),this.subBox.push(r),r.readBody(e)}delete this.data}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),s=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(12));var n=Math.pow(2,32)-1,r=function(){function FMP4(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,FMP4)}return i(FMP4,null,[{key:"type",value:function type(e){return new Uint8Array([e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)])}},{key:"size",value:function size(e){return s.default.writeUint32(e)}},{key:"extension",value:function extension(e,t){return new Uint8Array([e,t>>16&255,t>>8&255,255&t])}},{key:"ftyp",value:function ftyp(){var e=new s.default;return e.write(FMP4.size(24),FMP4.type("ftyp"),new Uint8Array([105,115,111,54,0,0,0,1,105,115,111,54,100,97,115,104])),e.buffer}},{key:"moov",value:function moov(e){var t=new s.default,r=8,i=FMP4.mvhd(e.duration,e.timeScale),n=FMP4.mvex(e.duration,e.timeScale),a=FMP4.videoTrak(e),o=new Uint8Array([]);return[i,n,a,o=e.channelCount?FMP4.audioTrak(e):o].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("moov"),i,n,a,o),t.buffer}},{key:"mvhd",value:function mvhd(e,t){var r=new s.default,t=(e*=t,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>24&255,t>>16&255,t>>8&255,255&t,e>>24&255,e>>16&255,e>>8&255,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,3]));return r.write(FMP4.size(8+t.length),FMP4.type("mvhd"),new Uint8Array(t)),r.buffer}},{key:"videoTrak",value:function videoTrak(e){var t=new s.default,r=8,i=FMP4.tkhd({id:1,duration:e.videoDuration,timescale:e.videoTimeScale,width:e.width,height:e.height,type:"video"}),e=FMP4.mdia({type:"video",timescale:e.videoTimeScale,duration:e.videoDuration,vps:e.vps,sps:e.sps,pps:e.pps,pixelRatio:e.pixelRatio,width:e.width,height:e.height,videoCodec:e.videoCodec,hvc1Data:e.hvc1Data,hvcCData:e.hvcCData,stss:e.stss});return[i,e].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("trak"),i,e),t.buffer}},{key:"audioTrak",value:function audioTrak(e){var t=new s.default,r=8,i=FMP4.tkhd({id:2,duration:e.audioDuration,timescale:e.audioTimeScale,width:0,height:0,type:"audio"}),n=e.channelCount,a=e.sampleRate,n=FMP4.mdia({type:"audio",timescale:e.audioTimeScale,duration:e.audioDuration,channelCount:n,sampleRate:a,audioConfig:e.audioConfig});return[i,n].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("trak"),i,n),t.buffer}},{key:"tkhd",value:function tkhd(e){var t=new s.default,r=e.id,i=e.duration*e.timeScale,n=e.width,a=e.height,e=e.type,r=new Uint8Array([0,0,0,3,0,0,0,0,217,172,236,86,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,i>>24&255,i>>16&255,i>>8&255,255&i,0,0,0,0,0,0,0,0,0,0,0,"video"===e?1:0,"audio"===e?1:0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>8&255,255&n,0,0,a>>8&255,255&a,0,0]);return t.write(FMP4.size(8+r.byteLength),FMP4.type("tkhd"),r),t.buffer}},{key:"edts",value:function edts(e){var t=new s.default,r=e.duration,e=e.mediaTime;return t.write(FMP4.size(36),FMP4.type("edts")),t.write(FMP4.size(28),FMP4.type("elst")),t.write(new Uint8Array([0,0,0,1,r>>24&255,r>>16&255,r>>8&255,255&r,e>>24&255,e>>16&255,e>>8&255,255&e,0,0,0,1])),t.buffer}},{key:"mdia",value:function mdia(e){var t=new s.default,r=8,i=FMP4.mdhd(e.timescale),n=FMP4.hdlr(e.type),e=FMP4.minf(e);return[i,n,e].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("mdia"),i,n,e),t.buffer}},{key:"mdhd",value:function mdhd(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,r=new s.default,t=(t*=e,Math.floor(t/(1+n)),Math.floor(t%(1+n)),new Uint8Array([0,0,0,0,217,20,37,90,e>>24&255,e>>16&255,e>>8&255,255&e,0,0,0,0,21,199,0,0]));return r.write(FMP4.size(12+t.byteLength),FMP4.type("mdhd"),FMP4.extension(0,0),t),r.buffer}},{key:"hdlr",value:function hdlr(e){var t=new s.default,r=[0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0];return"audio"===e&&(r.splice.apply(r,[8,4].concat([115,111,117,110])),r.splice.apply(r,[24,13].concat([83,111,117,110,100,72,97,110,100,108,101,114,0]))),t.write(FMP4.size(8+r.length),FMP4.type("hdlr"),new Uint8Array(r)),t.buffer}},{key:"minf",value:function minf(e){var t=new s.default,r=8,i="video"===e.type?FMP4.vmhd():FMP4.smhd(),n=FMP4.dinf(),e=FMP4.stbl(e);return[i,n,e].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("minf"),i,n,e),t.buffer}},{key:"vmhd",value:function vmhd(){var e=new s.default;return e.write(FMP4.size(20),FMP4.type("vmhd"),new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])),e.buffer}},{key:"smhd",value:function smhd(){var e=new s.default;return e.write(FMP4.size(16),FMP4.type("smhd"),new Uint8Array([0,0,0,0,0,0,0,0])),e.buffer}},{key:"dinf",value:function dinf(){var e=new s.default;return e.write(FMP4.size(36),FMP4.type("dinf"),FMP4.size(28),FMP4.type("dref"),new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1])),e.buffer}},{key:"stbl",value:function stbl(e){var t=new s.default,r=8,i=FMP4.stsd(e),n=FMP4.stts(),a=FMP4.stsc(),o=FMP4.stsz(),u=FMP4.stco();return"video"===e.type?([i,n,e=FMP4.stss(e.stss),a,o,u].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("stbl"),i,n,e,a,o,u)):([i,n,a,o,u].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("stbl"),i,n,a,o,u)),t.buffer}},{key:"stsd",value:function stsd(e){var t=new s.default,r=void 0,r="audio"===e.type?FMP4.mp4a(e):-1<e.videoCodec.indexOf("hvc1")?FMP4.hvc1(e):FMP4.avc1(e);return t.write(FMP4.size(16+r.byteLength),FMP4.type("stsd"),FMP4.extension(0,0),new Uint8Array([0,0,0,1]),r),t.buffer}},{key:"mp4a",value:function mp4a(e){var t=new s.default,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,e.sampleRate>>8&255,255&e.sampleRate,0,0]),e=FMP4.esds(e.audioConfig);return t.write(FMP4.size(8+r.byteLength+e.byteLength),FMP4.type("mp4a"),r,e),t.buffer}},{key:"esds",value:function esds(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[43,146,8,0],t=e.length,r=new s.default,t=new Uint8Array([0,0,0,0,3,23+t,0,1,0,4,15+t,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([t]).concat(e).concat([6,1,2]));return r.write(FMP4.size(8+t.byteLength),FMP4.type("esds"),t),r.buffer}},{key:"avc1",value:function avc1(e){var t=new s.default,r=e.sps,i=e.pps,n=e.width,a=e.height,o=e.pixelRatio[0],e=e.pixelRatio[1],r=new Uint8Array([1,r[1],r[2],r[3],255,225].concat([r.length>>>8&255,255&r.length]).concat(r).concat(1).concat([i.length>>>8&255,255&i.length]).concat(i)),avc1=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n>>8&255,255&n,a>>8&255,255&a,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),i=new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192]),n=new Uint8Array([o>>24,o>>16&255,o>>8&255,255&o,e>>24,e>>16&255,e>>8&255,255&e]);return t.write(FMP4.size(40+avc1.byteLength+r.byteLength+i.byteLength),FMP4.type("avc1"),avc1,FMP4.size(8+r.byteLength),FMP4.type("avcC"),r,FMP4.size(20),FMP4.type("btrt"),i,FMP4.size(16),FMP4.type("pasp"),n),t.buffer}},{key:"hvc1",value:function hvc1(e){var t=new s.default,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,e.width>>8&255,255&e.width,e.height>>8&255,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return t.write(FMP4.size(8+r.byteLength+8+e.hvcCData.byteLength+10),FMP4.type("hvc1"),r,FMP4.size(8+e.hvcCData.byteLength),FMP4.type("hvcC"),new Uint8Array(e.hvcCData),FMP4.size(10),FMP4.type("fiel"),new Uint8Array([1,0])),t.buffer}},{key:"stts",value:function stts(){var e=new s.default,t=new Uint8Array([0,0,0,0,0,0,0,0]);return e.write(FMP4.size(16),FMP4.type("stts"),t),e.buffer}},{key:"stss",value:function stss(e){var t=new s.default,r=[],i=(e.entries.forEach(function(e){r.push(e>>24),r.push(e>>16&255),r.push(e>>8&255),r.push(255&e)}),new Uint8Array([0,0,0,0,e.count>>24,e.count>>16&255,e.count>>8&255,255&e.count].concat(r)));return t.write(FMP4.size(16+4*e.count),FMP4.type("stss"),i),t.buffer}},{key:"stsc",value:function stsc(){var e=new s.default,t=new Uint8Array([0,0,0,0,0,0,0,0]);return e.write(FMP4.size(16),FMP4.type("stsc"),t),e.buffer}},{key:"stco",value:function stco(){var e=new s.default,t=new Uint8Array([0,0,0,0,0,0,0,0]);return e.write(FMP4.size(16),FMP4.type("stco"),t),e.buffer}},{key:"stsz",value:function stsz(){var e=new s.default,t=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]);return e.write(FMP4.size(20),FMP4.type("stsz"),t),e.buffer}},{key:"mvex",value:function mvex(e,t){var r=new s.default,e=s.default.writeUint32(e*t);return r.write(FMP4.size(88),FMP4.type("mvex"),FMP4.size(16),FMP4.type("mehd"),FMP4.extension(0,0),e,FMP4.trex1(1),FMP4.trex2(2)),r.buffer}},{key:"trex",value:function trex(e){var t=new s.default,e=new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return t.write(FMP4.size(8+e.byteLength),FMP4.type("trex"),e),t.buffer}},{key:"trex1",value:function trex1(e){var t=new s.default,e=new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e,0,0,0,1,0,0,2,0,0,0,0,0,0,1,0,0]);return t.write(FMP4.size(8+e.byteLength),FMP4.type("trex"),e),t.buffer}},{key:"trex2",value:function trex2(e){var t=new s.default,e=new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e,0,0,0,1,0,0,4,0,0,0,0,0,2,0,0,0]);return t.write(FMP4.size(8+e.byteLength),FMP4.type("trex"),e),t.buffer}},{key:"moof",value:function moof(e){var t=new s.default,r=8,i=FMP4.mfhd(e),e=FMP4.traf(e);return[i,e].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("moof"),i,e),t.buffer}},{key:"mfhd",value:function mfhd(e){var t=new s.default,r=null,r=(e.id,s.default.writeUint32((e.fragIndex||0)+1));return t.write(FMP4.size(16),FMP4.type("mfhd"),FMP4.extension(0,0),r),t.buffer}},{key:"traf",value:function traf(e){var t=new s.default,r=8,i=FMP4.tfhd(e.id),n=FMP4.tfdt(e,e.time),a=FMP4.sdtp(e),e=FMP4.trun(e,a.byteLength);return[i,n,a,e].forEach(function(e){r+=e.byteLength}),t.write(FMP4.size(r),FMP4.type("traf"),i,n,a,e),t.buffer}},{key:"tfhd",value:function tfhd(e){var t=new s.default,e=s.default.writeUint32(e);return t.write(FMP4.size(16),FMP4.type("tfhd"),new Uint8Array([0,2,0,0]),e),t.buffer}},{key:"tfdt",value:function tfdt(e,t){var r=new s.default,i=Math.floor(t/(1+n)),t=Math.floor(t%(1+n));return r.write(FMP4.size(20),FMP4.type("tfdt"),FMP4.extension(1,0),s.default.writeUint32(i),s.default.writeUint32(t)),r.buffer}},{key:"trun",value:function trun(e,t){var r=e.id,i=1===r?16:12,n=new s.default,a=s.default.writeUint32(e.samples.length),t=s.default.writeUint32(96+i*e.samples.length+t);return n.write(FMP4.size(20+i*e.samples.length),FMP4.type("trun"),FMP4.extension(0,e.flags),a,t),e.samples.forEach(function(e,t){n.write(s.default.writeUint32(e.duration)),n.write(s.default.writeUint32(e.size)),1===r?(n.write(s.default.writeUint32(e.key?33554432:16842752)),n.write(s.default.writeUint32(e.offset))):n.write(s.default.writeUint32(16777216))}),n.buffer}},{key:"sdtp",value:function sdtp(t){var r=new s.default;return r.write(FMP4.size(12+t.samples.length),FMP4.type("sdtp"),FMP4.extension(0,0)),t.samples.forEach(function(e){r.write(new Uint8Array(1===t.id?[e.key?32:16]:[16]))}),r.buffer}},{key:"mdat",value:function mdat(e){var t=new s.default,r=8;return e.samples.forEach(function(e){r+=e.size}),t.write(FMP4.size(r),FMP4.type("mdat")),e.samples.forEach(function(e){t.write(e.buffer)}),t.buffer}}]),FMP4}();t.default=r,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function getResponseHeaders(e){var r={};if(e instanceof window.XMLHttpRequest)try{e.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach(function(e){var e=e.split(": "),t=e.shift(),e=e.join(": ");r[t]=e})}catch(e){}return r},e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o={findBox:function(e,t){var r,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:[];return e.type!==t?e&&e.subBox&&((r=e.subBox.filter(function(e){return e.type===t})).length?r.forEach(function(e){return i.push(e)}):e.subBox.forEach(function(e){return o.findBox(e,t,i)})):i.push(e),1<(i=[].concat(i)).length?i:i[0]},padStart:function(e,t,r){for(var i=String(r),r=t>>0,n=Math.ceil(r/i.length),a=[],t=String(e);n--;)a.push(i);return a.join("").substring(0,r-t.length)+t},toHex:function(){for(var t=[],e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return r.forEach(function(e){t.push(o.padStart(Number(e).toString(16),2,0))}),t},sum:function(){for(var t=0,e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return r.forEach(function(e){t+=e}),t},stscOffset:function(e,t,r){var i,n,r=r[t];return r?(n=Math.floor((t-r.first_sample)/r.samples_per_chunk),i=r.first_sample+n*r.samples_per_chunk,{chunk_index:r.first_chunk+n,samples_offset:[i,t]}):(r=e.entries.pop(),e.entries.push(r),n=Math.floor((t-r.first_sample)/r.samples_per_chunk),n=Math.min(n,r.chunk_count),{chunk_index:r.first_chunk+n,samples_offset:[r.first_sample+r.samples_per_chunk*n,t]})},seekSampleOffset:function(e,t,r,i,n,a){e=o.stscOffset(e,i+1,a),a=t.entries[e.chunk_index-1]+o.sum.apply(null,r.entries.slice(e.samples_offset[0]-1,e.samples_offset[1]-1))-n;if(a<0)throw"result="+a+",stco.length="+t.entries.length+",sum="+o.sum.apply(null,r.entries.slice(0,i));return a},seekSampleTime:function(e,t,r){var i=void 0,n=void 0,a=0,o=0,u=0;return e.entry.every(function(e){return n=e.sampleDuration,r<a+e.sampleCount?(i=o+(r-a)*e.sampleDuration,!1):(a+=e.sampleCount,o+=e.sampleCount*n,!0)}),t&&t[r]&&(u=t[r]),{time:i=i||o+(r-a)*n,duration:n,offset:u}},seekOrderSampleByTime:function(e,r,i){var n,a=0,o=0,u=0;return e.every(function(e,t){return n=e.sampleCount*e.sampleDuration/r,i<=a+n?(o=u+Math.ceil((i-a)*r/e.sampleDuration),a+=Math.ceil((i-a)*r/e.sampleDuration)*e.sampleDuration/r,!1):(a+=n,u+=e.sampleCount,!0)}),{order:o,startTime:a}},seekTrakDuration:function(e,t){var e=o.findBox(e,"stts"),r=0;return e.entry.forEach(function(e){r+=e.sampleCount*e.sampleDuration}),Number(r/t).toFixed(4)},StringToArrayBuffer:function(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),i=0;i<e.length;i++)r[i]=e.charCodeAt(i);return t},fromHex:function(e){for(var t=new Uint8Array(e.length/2),r=0;r<e.length;r+=2)t[r/2]=window.parseInt(e.substr(r,2),16);return t},fromCharCode:function(e){for(var t="",r=0;r<e.length;r+=16e3){var i=e.subarray(r,r+16e3);t+=String.fromCharCode.apply(null,i)}return t},ArrayBufferToString:function(e){for(var t="",r=new Uint8Array(e),i=0;i<r.length;i++)t+=String.fromCharCode(r[i]);return t},Base64ToHex:function(e){for(var t=window.atob(e.replace(/-/g,"+").replace(/_/g,"/")),r="",i=0;i<t.length;i++)r+=("0"+t.charCodeAt(i).toString(16)).substr(-2);return r},toBase64:function(e,t){e=o.fromCharCode(e),t=void 0===t||t,e=window.btoa(e).replace(/\+/g,"-").replace(/\//g,"_");return t?e:e.replace(/=*$/,"")},toUTF8:function(e){for(var e=encodeURIComponent(e),t=unescape(e),r=new Uint8Array(t.length),i=0;i<t.length;++i)r[i]=t.charCodeAt(i);return r.buffer},bufferToString:function(e){return("0"+Number(e).toString(16)).slice(-2).toUpperCase()},strToBuf:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e[r]+e[r+1],16));return new Uint8Array(t)},str2hex:function(e){if(""===e)return"";for(var t=[],r=0;r<e.length;r++)t.push(e.charCodeAt(r));return t},parse:function(e){if(Array.isArray(e))return e.map(function(e){return parseInt(e,16)});for(var t,r=[],i=0;i<e.length;i++)i%2&&(t=e[i-1]+e[i],r.push(parseInt(t,16)));return r}};t.default=o,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),n=_interopRequireDefault(r(4)),a=_interopRequireDefault(r(2));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}r=function(){function MSE(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:'video/mp4; codecs="avc1.64001E, mp4a.40.5"',t=arguments[1];!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,MSE),(0,n.default)(this),this.codecs=e,this.mediaSource=new window.MediaSource(t),this.url=window.URL.createObjectURL(this.mediaSource),this.queue=[],this.updating=!1,this._hasDestroyed=!1,this._hasEndOfStream=!1,this._hasEndOfStreamSuccess=!1,this._onSourceOpen=this._onSourceOpen.bind(this),this._onMediaSourceClose=this._onMediaSourceClose.bind(this),this._onSourceBufferError=this._onSourceBufferError.bind(this),this._onSourceBufferUpdateEnd=this._onSourceBufferUpdateEnd.bind(this),this.mediaSource.addEventListener("sourceopen",this._onSourceOpen),this.mediaSource.addEventListener("sourceclose",this._onMediaSourceClose)}return i(MSE,[{key:"_onSourceOpen",value:function _onSourceOpen(){var e=this;e.sourceBuffer=e.mediaSource.addSourceBuffer(e.codecs),e.sourceBuffer.addEventListener("error",this._onSourceBufferError),e.sourceBuffer.addEventListener("updateend",this._onSourceBufferUpdateEnd),e.emit("sourceopen")}},{key:"_onSourceBufferError",value:function _onSourceBufferError(e){this.emit("error",new a.default("mse","",{line:16,handle:"[MSE] constructor sourceopen",msg:e.message}))}},{key:"_onSourceBufferUpdateEnd",value:function _onSourceBufferUpdateEnd(){var e,t=this;t.emit("updateend"),this._hasEndOfStream&&!this._hasEndOfStreamSuccess?this._endOfStream():(e=t.queue.shift())&&t.sourceBuffer&&!1===t.sourceBuffer.updating&&"open"===t.state?t.sourceBuffer.appendBuffer(e):e&&t.queue.unshift(e)}},{key:"_onMediaSourceClose",value:function _onMediaSourceClose(){this.emit("sourceclose")}},{key:"appendBuffer",value:function appendBuffer(e){var t;if(e)return(t=this.sourceBuffer)&&!t.updating&&"open"===this.state?(t.appendBuffer(e),!0):(this.queue.push(e),!1)}},{key:"removeBuffer",value:function removeBuffer(e,t){var r=this.sourceBuffer;r&&!1===r.updating&&"open"===this.state&&r.remove(e,t)}},{key:"endOfStream",value:function endOfStream(){this._hasEndOfStream=!0,"open"===this.mediaSource.readyState&&this.sourceBuffer&&!this.sourceBuffer.updating&&(this._hasEndOfStreamSuccess=!0,this._endOfStream())}},{key:"_endOfStream",value:function _endOfStream(){this.queue=[],"open"===this.mediaSource.readyState&&this.mediaSource.endOfStream()}},{key:"destroy",value:function destroy(){this._hasDestroyed||(this._hasDestroyed=!0,window.URL.revokeObjectURL(this.url),this.mediaSource&&(this.mediaSource.removeEventListener("sourceclose",this._onMediaSourceClose),this.mediaSource.removeEventListener("sourceopen",this._onSourceOpen)),this.sourceBuffer&&(this.sourceBuffer.removeEventListener("error",this._onSourceBufferError),this.sourceBuffer.removeEventListener("updateend",this._onSourceBufferUpdateEnd)))}},{key:"state",get:function get(){return this.mediaSource.readyState}},{key:"duration",get:function get(){return this.mediaSource.duration},set:function set(e){this.mediaSource.duration=e}}],[{key:"isSupported",value:function isSupported(e){return window.MediaSource&&window.MediaSource.isTypeSupported(e)}}]),MSE}();t.default=r,e.exports=t.default},function(e,t,r){"use strict";t.isMSBrowser=function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE "),e=e.indexOf("Trident/");return 0<t||0<e},t.isMediaSourceSupported=function(){var e=window.MediaSource=window.MediaSource||window.WebKitMediaSource;return e&&"function"==typeof e.isTypeSupported},t.isSupported=function(){return t.isMediaSourceSupported()&&!t.isMSBrowser()},t.isSupportedWithXgmse=function(){return!t.isMSBrowser()}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}();var n=function(){function ProxyPromise(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,ProxyPromise);var r=void 0,i=void 0,e=new Promise(function(e,t){r=e,i=t});return e.resolve=r,e.reject=i,e}return i(ProxyPromise,[{key:"resolve",value:function resolve(e){}},{key:"reject",value:function reject(e){}}]),ProxyPromise}();t.default=n,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}();var n=function(){function Timer(e){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,Timer),this.onTick_=e,this.cancelPending_=null}return i(Timer,[{key:"tickAfter",value:function tickAfter(e){var t=this,r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,i=(this.stop(),!0),n=null;this.cancelPending_=function(){window.clearTimeout(n),i=!1};n=window.setTimeout(function onTick(){i&&(t.onTick_(),r)&&r()},1e3*e);return this}},{key:"tickEvery",value:function tickEvery(e){var t=this;this.tickAfter(e,function(){t.tickEvery(e)})}},{key:"stop",value:function stop(){this.cancelPending_&&(this.cancelPending_(),this.cancelPending_=null)}}]),Timer}();t.default=n,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}(),n=function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}(r(91));r=function(){function GapJump(e,t){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,GapJump),this.player=e,this.mediaElem=e.video,this.config=t,this.timer=new n.default,this.prevReadyState=this.mediaElem.readyState,this.didFireLargeGap=!1,this.seekingEventReceived=!1,this.segmentAppended=!1,this.onWaitFunc=this._onWaiting.bind(this),this.onPlayFunc=this._onPlay.bind(this),this.isSafari=/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),!1!==this.config.useGapJump&&this._start(),this.hasPlayed=!1}return i(GapJump,[{key:"_onWaiting",value:function _onWaiting(){this.onGapJump("_onWaiting")}},{key:"_onPlay",value:function _onPlay(){this.hasPlayed=!0}},{key:"_start",value:function _start(){var e=this;this.mediaElem.addEventListener("waiting",this.onWaitFunc),this.mediaElem.addEventListener("play",this.onPlayFunc),this.timer.repeat(function(){e.onGapJump("repeat")},250)}},{key:"onSegmentAppend",value:function onSegmentAppend(){this.segmentAppended=!0,this.onGapJump("onSegmentAppend")}},{key:"onSeeking",value:function onSeeking(){this.seekingEventReceived=!0,this.segmentAppended=!1,this.didFireLargeGap=!1}},{key:"onGapJump",value:function onGapJump(e){if(this.mediaElem.readyState!==HTMLMediaElement.HAVE_NOTHING){if(this.mediaElem.seeking){if(!this.seekingEventReceived)return}else this.seekingEventReceived=!1;var t,r,i,n;this.mediaElem.paused&&0!==this.mediaElem.currentTime&&this.hasPlayed||(this.mediaElem.readyState!==this.prevReadyState&&(this.didFireLargeGap=!1,this.prevReadyState=this.mediaElem.readyState),i=this.mediaElem.buffered,t=this.config.smallGapLimit||.5,n=this.config.gapDetectionThreshold||.1,r=this.mediaElem.currentTime,null===(n=this._getIndex(i,r,n)))||0===n&&!this.segmentAppended||(i=i.start(n)+.1,this.mediaElem.duration<i)||(n=i-r)<GapJump.BROWSER_GAP_TOLERANCE||n<=t&&(!0!==this.config.disableGapSetPosition&&(this.mediaElem.currentTime=this.isSafari?i+.1:i),this.player)&&this.player.emit("detectGap")}}},{key:"_getIndex",value:function _getIndex(e,t,r){if(!e||!e.length)return null;if(1===e.length&&e.end(0)-e.start(0)<1e-6)return null;for(var i=this._getBuffered(e),n=null,a=0;a<i.length;a++)if(i[a].start>t&&(0===a||i[a-1].end-t<=r)){n=a;break}return n}},{key:"_getBuffered",value:function _getBuffered(e){if(!e)return[];for(var t=[],r=0;r<e.length;r++)t.push({start:e.start(r),end:e.end(r)});return t}},{key:"destroy",value:function destroy(){this.mediaElem.removeEventListener("waiting",this.onWaitFunc),this.mediaElem.removeEventListener("play",this.onPlayFunc),this.timer.clear(),this.timer=null}}]),GapJump}();(t.default=r).BROWSER_GAP_TOLERANCE=.001,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(){function defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,r){return t&&defineProperties(e.prototype,t),r&&defineProperties(e,r),e}}();var n=function(){function IntervalTimer(){!function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,IntervalTimer),this.timeID=null,this.func=null}return i(IntervalTimer,[{key:"repeat",value:function repeat(e,t){this.timeID=1,this.repeatInterval(e,t)}},{key:"repeatInterval",value:function repeatInterval(e,t){var r=this;this.timeID&&(null===this.func&&(this.func=e),this.func===e)&&(this.timeID&&clearTimeout(this.timeID),this.timeID=null,this.timeID=setTimeout(function(){e(),r.repeatInterval(e,t)},t))}},{key:"clear",value:function clear(){clearTimeout(this.timeID),this.timeID=null}}]),IntervalTimer}();t.default=n,e.exports=t.default}]);