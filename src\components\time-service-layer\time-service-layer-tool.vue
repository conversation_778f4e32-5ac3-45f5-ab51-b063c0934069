<!-- 时序影像 -->
<script lang="ts" setup>
import type { ImageryLayer, Rectangle } from 'cesium';
import type { LayerConfig } from './state';
import { Bd09MercatorTilingScheme, Gcj02MercatorTilingScheme } from '@x3d/all';
import { useCzImageryLayerCollection, useCzViewer } from '@x3d/vue-hooks';
import { computed, ref, shallowRef, watch, watchEffect } from 'vue';
import layerServer from '../../../public/layer-server.json';
import { activeLayerType, currentDateIndex, isTimeAxisPlaying, nextImage, previousImage } from './state';

export interface ImageryLayerOptions {
  label: string;
  layer: ImageryLayer;
}

defineOptions({ name: 'TimeSeriesLayerTool' });

// 从配置文件加载层数据
// 使用导入的layerServer数据
const layerConfigs = ref<LayerConfig[]>(layerServer);

// 根据激活的类型筛选出相关图层
const filteredLayers = computed(() => {
  if (!activeLayerType.value || layerConfigs.value.length === 0) {
    return [];
  }
  return layerConfigs.value.filter(item => item.type === activeLayerType.value);
});

// 可用的日期标签
const dates = computed(() => {
  const uniqueDates = new Set(filteredLayers.value.map(item => item.label));
  return Array.from(uniqueDates);
});

const imageryLayerCollection = useCzImageryLayerCollection(undefined);
const imageryLayers = ref<Cesium.ImageryLayer[]>([]);
// 未使用的viewer变量，但保留以防后续需要
const _ = useCzViewer();

// 使用全局状态中的currentDateIndex作为stepIndex
const stepIndex = computed({
  get: () => currentDateIndex.value,
  set: (value) => {
    currentDateIndex.value = value;
  },
});
const sliderRef = shallowRef<InstanceType<GlobalComponents['ElSlider']>>();

const interval = useIntervalFn(
  () => {
    if (stepIndex.value >= imageryLayers.value.length - 1) {
      interval.pause();
      isTimeAxisPlaying.value = false;
    }
    else {
      stepIndex.value++;
    }
  },
  2000,
  {
    immediate: false,
  },
);

function player() {
  if (imageryLayers.value.length === 0)
    return;

  stepIndex.value === imageryLayers.value.length - 1 && (stepIndex.value = 0);
  interval.resume();
}

// 上一个影像
function handlePrevious() {
  if (imageryLayers.value.length === 0)
    return;
  previousImage(imageryLayers.value.length - 1);
}

// 下一个影像
function handleNext() {
  if (imageryLayers.value.length === 0)
    return;
  nextImage(imageryLayers.value.length - 1);
}

// 监听播放状态
watch(isTimeAxisPlaying, (isPlaying) => {
  if (isPlaying) {
    player();
  }
  else {
    interval.pause();
  }
});

function createTilingScheme(tileMatrixSetID?: string, rectangle?: Rectangle) {
  // 瓦片投影
  const tilingScheme = tileMatrixSetID === 'EPSG:4326'
    ? new Cesium.GeographicTilingScheme({ rectangle })
    : tileMatrixSetID === 'EPSG:4490'
      ? new Cesium.WebMercatorTilingScheme({})
      : tileMatrixSetID === 'BD09'
        ? new Bd09MercatorTilingScheme({})
        : tileMatrixSetID === 'GCJO2'
          ? new Gcj02MercatorTilingScheme({})
          : new Cesium.WebMercatorTilingScheme({});
  return tilingScheme;
}

// 创建图层选项 - 使用UrlTemplateImageryProvider替代WebMapTileServiceImageryProvider
const allImageryLayers = computed<ImageryLayerOptions[]>(() => {
  if (filteredLayers.value.length === 0) {
    return [];
  }
  const rectangle: Cesium.Rectangle = Cesium.Rectangle.MAX_VALUE;
  return filteredLayers.value.map((item) => {
    // 使用可选链和默认值处理tileMatrixSetID
    const tilingScheme = createTilingScheme(item.tileMatrixSetID, rectangle);
    return {
      label: item.label,
      layer: new Cesium.ImageryLayer(
        new Cesium.UrlTemplateImageryProvider({
          url: item.layerUrl,
          minimumLevel: 0,
          maximumLevel: 22,
          tilingScheme,
          rectangle,
          tileWidth: 256,
          tileHeight: 256,
          customTags: {
            z: (imageryProvider: any, x: number, y: number, level: number) => level,
            z1: (imageryProvider: any, x: number, y: number, level: number) => level + 1,
            z2: (imageryProvider: any, x: number, y: number, level: number) => level + 2,
          },
        }),
      ),
    };
  });
});

// 优化图层管理逻辑 - 当前图层索引变化时，只显示当前图层，隐藏其他图层
watchEffect(() => {
  if (imageryLayers.value.length === 0 || stepIndex.value >= imageryLayers.value.length)
    return;

  // 隐藏所有图层
  imageryLayers.value.forEach((layer, index) => {
    layer.show = index === stepIndex.value;
  });
});

// 当过滤后的图层变化时，更新图层集合
watch(allImageryLayers, (newLayers) => {
  // 清除所有当前图层
  imageryLayers.value.forEach((layer) => {
    imageryLayerCollection.remove(layer, true);
  });
  imageryLayers.value = [];

  // 添加新图层
  imageryLayers.value = newLayers.map(item => item.layer);

  // 只添加图层到集合，但默认隐藏
  imageryLayers.value.forEach((layer, index) => {
    layer.show = index === stepIndex.value; // 只显示当前索引的图层
    imageryLayerCollection.add(layer);
  });

  // 重置步进索引
  stepIndex.value = 0;
}, { deep: true });

// 是否显示时间轴工具
const showTimelineTool = computed(() => {
  return activeLayerType.value !== null && filteredLayers.value.length > 0;
});

// 为了解决ElSlider的marks类型问题，创建一个符合类型要求的marks对象
const sliderMarks = computed(() => {
  const result: Record<number, { label: string; style: Record<string, string> }> = {};
  dates.value.forEach((date, index) => {
    result[index] = { label: date, style: {} };
  });
  return result;
});
</script>

<template>
  <div v-if="showTimelineTool" class="time-series-layer-tool">
    <div class="time-series-layer-tool--header">
      <div class="time-series-layer-tool--header-leader">
        <i class="iconfont icon-shijianzhou text-22px" />
        <el-icon class="i-custom:time-line text-22px" />
        当前影像：{{ dates[stepIndex] || '' }}
      </div>
      <div class="time-series-layer-tool--play-group">
        <div class="player-button" @click="handlePrevious">
          <el-icon class="i-tabler:player-skip-back-filled" />
        </div>
        <div class="player-button active" @click="interval.isActive.value ? interval.pause() : player()">
          <el-icon v-if="interval.isActive.value" class="i-icon-park-outline:pause" />
          <el-icon v-else class="i-tabler:player-play-filled" />
        </div>
        <div class="player-button" @click="handleNext">
          <el-icon class="i-tabler:player-skip-forward-filled" />
        </div>
      </div>
    </div>
    <ElSlider
      ref="sliderRef"
      v-model="stepIndex"
      class="timeline-slider"
      :max="imageryLayers.length - 1"
      show-stops
      :marks="sliderMarks"
      :show-tooltip="false"
    />
  </div>
</template>

<style scoped lang="scss">
.time-series-layer-tool {
  --uno: 'bg-#fff';

  position: fixed;
  bottom: 80px;
  left: 50%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 870px;
  height: 124px;
  padding: 16px 30px 4px;
  color: #000;
  pointer-events: initial;
  background: #fff;
  border-radius: 8px;
  transform: translateX(-50%);
}

.time-series-layer-tool--header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-series-layer-tool--header-leader {
  height: 30px;
  font-size: 18px;
  font-weight: 700;
  vertical-align: middle;
  color: #000;
}

.time-series-layer-tool--play-group {
  position: absolute;
  left: 50%;
  display: flex;
  transform: translateX(-50%);

  > .player-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    margin: 0 6px;
    font-size: 24px;
    color: #004189;
    cursor: pointer;
    border-radius: 50%;

    &.active,
    &:hover {
      color: #fff;
      background: rgb(18 61 118 / 80%);
      box-shadow: inset 0 0 6px 2px #2f7eb0;
    }
  }
}

:deep(.timeline-slider) {
  height: 24px;
  padding: 0 20px;
  margin-bottom: 10px;
  background: linear-gradient(270deg, rgb(0 65 137 / 0%) -3%, rgb(0 65 137 / 10%) 50%, rgb(0 65 137 / 0%) 103%);

  --el-slider-main-bg-color: #4e6e9e;
  --el-slider-runway-bg-color: #4e6e9e;
  --el-slider-border-radius: 0;
  --el-slider-height: 2px;

  .el-slider__stop {
    bottom: 0;
    width: 1px;
    height: 15px;
    background-color: var(--el-slider-runway-bg-color);
  }

  .el-slider__marks-text {
    top: -35px;
    margin-top: 0;
    font-size: 18px;
    color: #000;

    &[active] {
      top: -37px;
      font-size: 20px;
      color: #004189;
    }
  }

  .el-slider__button {
    bottom: 0;
    width: 2px;
    margin-top: -8px;
    background: #004189;
    border: 0;
    border-radius: 0;
  }
}
</style>
