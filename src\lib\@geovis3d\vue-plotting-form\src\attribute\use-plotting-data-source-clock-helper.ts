import type { PlottingDataSource } from '@/lib/@geovis3d/core';
import type { MaybeRefOrGetter } from 'vue-demi';
import { throttle } from '@/lib/@geovis3d/core';
import { toValue, useIntervalFn } from '@vueuse/core';
import { useCzEventListener } from '@x3d/vue-hooks';

import * as Cesium from 'cesium';
import { computed, ref, watch, watchEffect } from 'vue';

export function usePlottingDataSourceClockHelper(
  dataSource: MaybeRefOrGetter<PlottingDataSource | undefined>,
) {
  const data = computed(() => toValue(dataSource));

  /**
   * 时钟
   */
  const clock = new Cesium.DataSourceClock();
  const now = Cesium.JulianDate.fromDate(new Date(0));
  clock.startTime = now.clone();
  clock.currentTime = now.clone();
  clock.multiplier = 1;
  clock.stopTime = Cesium.JulianDate.addDays(now.clone(), 2, now.clone());
  clock.clockRange = Cesium.ClockRange.LOOP_STOP;

  const resetClock = () => {
    data.value && (data.value.clock = clock.clone());
  };

  // 时钟参数变化，重置
  useCzEventListener(
    () => clock.definitionChanged,
    throttle(() => {
      const value = clock.clone();
      if (data.value && !data.value.clock?.equals(value)) {
        resetClock();
      }
    }, 10),
  );

  /**
   * 步进倍数
   */
  const multipler = computed({
    get: () => clock.multiplier,
    set: (value: number) => (clock.multiplier = value),
  });

  const playing = ref(false);

  /**
   * 步进器时长
   */
  const duration = ref(0);
  const { resume, pause } = useIntervalFn(
    () => {
      duration.value += (10 / 1000) * multipler.value;
    },
    5,
    { immediate: false },
  );

  // useIntervalFn(() => {
  //   if (!playing.value) {
  //     const start = clock.startTime.clone();
  //     clock.currentTime = Cesium.JulianDate.addSeconds(start, duration.value, start);
  //   }
  // }, 10);

  watch(duration, (duration) => {
    const start = clock.startTime.clone();
    clock.currentTime = Cesium.JulianDate.addSeconds(start, duration, start);
  });

  watchEffect(() => (playing.value ? resume() : pause()));

  watchEffect(() => {
    if (playing.value) {
      resetClock();
    }
  });

  return {
    playing,
    duration,
    multipler,
  };
}
