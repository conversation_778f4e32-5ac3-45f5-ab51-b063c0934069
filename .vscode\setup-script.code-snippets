{
  "tpl-page-setup-script": {
    "scope": "vue",
    "prefix": "tpl-page-setup-script",
    "description": "新建setup-script页面",

    "body": [
      "<!-- ${1:请输入页面名称} -->",
      "<script lang=\"ts\" setup>",
      "  defineOptions({ name: '${RELATIVE_FILEPATH/(.+pages)(.+).vue/${2:/pascalcase}/g}' });",
      "definePage({",
        "meta: {",
          "title: '${1:请输入页面名称}',",
        "},",
      "});",
      "</script>",
      "",
      "<template>",
      "  <div>",
      "   ${RELATIVE_FILEPATH}",
      "  </div>",
      "</template>",
      "",
    ],
  },
  "tpl-setup-script": {
    "scope": "vue",
    "prefix": "tpl-setup-script",
    "description": "新建setup-script组件",

    "body": [
      "<!-- ${1:请输入组件名称或备注} -->",
      "<script lang=\"ts\" setup>",
      "  defineOptions({ name: '${RELATIVE_FILEPATH/(.+?(\\\\|\\/))((-|\\w|(\\.\\w+))+)((\\\\|\\/)index)*.vue/${3:/pascalcase}/g}' });",
      "</script>",
      "",
      "<template>",
      "  <div>",
      "   ${RELATIVE_FILEPATH}",
      "  </div>",
      "</template>",
      "",
    ],
  },
  "tpl-define-props-emits": {
    "scope": "typescript,typescriptreact",
    "prefix": "tpl-define-props-emits",
    "body": [
      "  export interface ${RELATIVE_FILEPATH/(.+?(\\\\|\\/))((-|\\w)+)((\\\\|\\/)index)*.vue/${3:/pascalcase}/g}Props {",
      "  modelValue?:unknown",
      "  }",
      "",
      "  export interface ${RELATIVE_FILEPATH/(.+?(\\\\|\\/))((-|\\w)+)((\\\\|\\/)index)*.vue/${3:/pascalcase}/g}Emits {",
      "  (event: 'update:modelValue', data?: unknown): void",
      "  }",
      "",
      " const props = defineProps<${RELATIVE_FILEPATH/(.+?(\\\\|\\/))((-|\\w)+)((\\\\|\\/)index)*.vue/${3:/pascalcase}/g}Props>()",
      " const emit = defineEmits<${RELATIVE_FILEPATH/(.+?(\\\\|\\/))((-|\\w)+)((\\\\|\\/)index)*.vue/${3:/pascalcase}/g}Emits>()",
      "",
    ],
  },
  "tpl-use-vmodel": {
    "scope": "typescript,typescriptreact",
    "prefix": "tpl-use-vmodel",
    "body": ["const model = useVModel(props,'modelValue',emit)", ""],
  },
}
