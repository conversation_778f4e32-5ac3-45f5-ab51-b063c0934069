<!-- 等高线分析 -->
<script lang="ts" setup>
import { useTerrainCheck } from '@/hooks/useTerrainCheck';
import { useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'ElevationAnalysis' });
const viewer = useCzViewer();

const terrainCheck = useTerrainCheck();

const minHeight = -414.0; // approximate dead sea elevation
const maxHeight = 8777.0; // approximate everest elevation
let contourUniforms = {};
let shadingUniforms = {};
const contourData = ref({
  enableContour: false,
  contourSpacing: 50.0,
  contourWidth: 2.0,
  selectedShading: 'none',
  contourColor: '#ff0000',

});
function getElevationContourMaterial() {
  return new Cesium.Material({
    fabric: {
      type: 'ElevationColorContour',
      materials: {
        contourMaterial: {
          type: 'ElevationContour',
        },
        elevationRampMaterial: {
          type: 'ElevationRamp',
        },
      },
      components: {
        diffuse:
          'contourMaterial.alpha == 0.0 ? elevationRampMaterial.diffuse : contourMaterial.diffuse',
        alpha: 'max(contourMaterial.alpha, elevationRampMaterial.alpha)',
      },
    },
    translucent: false,
  });
}

const elevationRamp = [0.0, 0.045, 0.1, 0.15, 0.37, 0.54, 1.0];
const slopeRamp = [0.0, 0.29, 0.5, Math.sqrt(2) / 2, 0.87, 0.91, 1.0];
const aspectRamp = [0.0, 0.2, 0.4, 0.6, 0.8, 0.9, 1.0];

function getColorRamp(selectedShading: string) {
  const ramp = document.createElement('canvas');
  ramp.width = 100;
  ramp.height = 1;
  const ctx = ramp.getContext('2d');

  let values: number[] = [];
  if (selectedShading === 'elevation') {
    values = elevationRamp;
  }
  else if (selectedShading === 'slope') {
    values = slopeRamp;
  }
  else if (selectedShading === 'aspect') {
    values = aspectRamp;
  }

  const grd = ctx?.createLinearGradient(0, 0, 100, 0);
  grd?.addColorStop(values?.[0], '#000000'); // black
  grd?.addColorStop(values?.[1], '#2747E0'); // blue
  grd?.addColorStop(values?.[2], '#D33B7D'); // pink
  grd?.addColorStop(values?.[3], '#D33038'); // red
  grd?.addColorStop(values?.[4], '#FF9742'); // orange
  grd?.addColorStop(values?.[5], '#ffd700'); // yellow
  grd?.addColorStop(values?.[6], '#ffffff'); // white

  ctx?.fillStyle && (ctx.fillStyle = grd);
  ctx?.fillRect(0, 0, 100, 1);

  return ramp;
}

function updateMaterial() {
  const hasContour = contourData.value.enableContour;
  const selectedShading = contourData.value.selectedShading;
  const globe = viewer.value.scene.globe;
  let material;
  if (hasContour) {
    if (selectedShading === 'elevation') {
      material = getElevationContourMaterial();
      shadingUniforms = material.materials.elevationRampMaterial.uniforms;
      shadingUniforms.minimumHeight = minHeight;
      shadingUniforms.maximumHeight = maxHeight;
      contourUniforms = material.materials.contourMaterial.uniforms;
    }
    else {
      material = Cesium.Material.fromType('ElevationContour');
      contourUniforms = material.uniforms;
    }
    contourUniforms.width = contourData.value.contourWidth;
    contourUniforms.spacing = contourData.value.contourSpacing;
    contourUniforms.color = Cesium.Color.fromCssColorString(
      contourData.value.contourColor,
    );
  }
  else if (selectedShading === 'elevation') {
    material = Cesium.Material.fromType('ElevationRamp');
    shadingUniforms = material.uniforms;
    shadingUniforms.minimumHeight = minHeight;
    shadingUniforms.maximumHeight = maxHeight;
  }
  if (selectedShading !== 'none') {
    shadingUniforms.image = getColorRamp(selectedShading);
  }
  globe.material = material;
}

updateMaterial();
watchDeep(contourData, () => {
  updateMaterial();
});
onUnmounted(() => {
  viewer.value.scene.globe.material = undefined;
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="等高线分析"
    class="w-400px"
  >
    <el-text v-if="!terrainCheck" p="x-24px!" type="danger">
      未检测到存在地形，请打开图层勾选地形
    </el-text>
    <el-form p="24px" :label-width="$vh(70)">
      <el-form-item label="效果">
        <el-radio-group v-model="contourData.selectedShading">
          <el-radio value="none">
            地形原貌
          </el-radio>
          <el-radio value="elevation">
            高度渐变
          </el-radio>
          <el-radio value="slope">
            坡度渐变
          </el-radio>
          <el-radio value="aspect">
            贴图
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="显示等高线">
        <el-switch v-model="contourData.enableContour" />
      </el-form-item>
      <el-form-item label="间距">
        <el-slider v-model="contourData.contourSpacing" />
      </el-form-item>
      <el-form-item label="线宽">
        <el-slider v-model="contourData.contourWidth" />
      </el-form-item>
      <el-form-item label="颜色">
        <el-color-picker v-model="contourData.contourColor" show-alpha />
      </el-form-item>
    </el-form>
    <template #footer>
      <span />
      <el-button type="primary" class="plain-#FF6363 px-26px!">
        清除
      </el-button>
    </template>
  </drag-card>
</template>
