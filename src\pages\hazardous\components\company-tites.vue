<!-- 二维/三维倾斜摄影相关逻辑 -->
<script lang="ts" setup>
import {
  productionHazardousChemicalsCompanyDetailCompanyCodeUsingPost,
  productionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost,
  productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet,
  productionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet,
} from '@/genapi/production';

import { CzClassificationPrimitive, CzEntity } from '@x3d/all';
import { useCzDataSourceCollection, useCzEntityCollection, useCzPrimitiveCollection, useCzViewer } from '@x3d/vue-hooks';

import * as Cesium from 'cesium';
import $3DTiles from '../3dtiles';
import { tankDara } from '../config';
import { useCompanyActiveInject } from '../hooks';

defineOptions({ name: 'CompanyTites' });

const viewer = useCzViewer();

const companyActive = useCompanyActiveInject();

const companyCode = computed(() => {
  return companyActive.twoCompanyCode || companyActive.threeCompanyCode;
});

// 是否是倾斜摄影
const is3D = computed(() => {
  return !!companyActive.threeCompanyCode;
});

const primitiveEffect = useCzPrimitiveCollection();

const dataSoureEffect = useCzDataSourceCollection();

const visible = ref(false);
// 当前点击的设备 数组下标
const currentIndex = ref<number>();

/** threeCompanyCode有值，添加倾斜摄影数据 */
watchEffect(async () => {
  primitiveEffect.removeScope();
  const companyCode = companyActive.threeCompanyCode;
  if (!companyCode)
    return;

  const tilesets = $3DTiles.find(e => e.companyCode === companyCode)?.tilesets;
  if (!tilesets?.length)
    return;

  const tilesetPromises = tilesets!.map(
    url => Cesium.Cesium3DTileset.fromUrl(`${import.meta.env.VITE_VISUAL_SYSTEM_PATH}/bdv/minio${url}`),
  );
  const tilesetList = await Promise.all(tilesetPromises);
  tilesetList.forEach(e => primitiveEffect.add(e));

  // 如果是国盛则需要添加额外的单体化数据
  if (companyCode === '*********') {
    const activeColor = Cesium.ColorGeometryInstanceAttribute.fromColor(
      Cesium.Color.fromCssColorString('#00CED1').withAlpha(0.6),
    );
    const color = Cesium.ColorGeometryInstanceAttribute.fromColor(
      Cesium.Color.ALICEBLUE.withAlpha(0.1),
    );
      // 添加单体化柱体
    tankDara.forEach(async (item) => {
      const Cartesian3 = Cesium.Cartesian3.fromDegrees(item.longitude, item.latitude);
      const modelMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(Cartesian3);

      const primitive = new CzClassificationPrimitive({
        scene: viewer.value.scene,
        geometryInstances: new Cesium.GeometryInstance({
          geometry: new Cesium.CylinderGeometry({
            length: 50,
            topRadius: item.radius, // 圆柱半径
            bottomRadius: item.radius, // 圆柱半径
          }),
          modelMatrix, // 圆柱矩阵
          attributes: {
            color,
            show: new Cesium.ShowGeometryInstanceAttribute(true),
          },
          id: item.deviceCode, // 请求数据
        }),
        classificationType: Cesium.ClassificationType.CESIUM_3D_TILE,
      });
      primitiveEffect.add(primitive);

      // 处理鼠标事件
      primitive.event.on('HOVER', ({ hover }) => {
        const attributes = primitive.getGeometryInstanceAttributes(item.deviceCode);
        attributes.color = hover ? activeColor.value : color.value; // 选中的颜色
      });
      primitive.event.on('LEFT_CLICK', () => {
        visible.value = true;
        currentIndex.value = equipList.value?.findIndex(e => e.equipCode === item.deviceCode);
      });
    });
  }
});

// 当前企业详情数据
const detail = computedAsync(async () => {
  if (!companyCode.value)
    return;
  const { data } = await productionHazardousChemicalsCompanyDetailCompanyCodeUsingPost({
    path: {
      companyCode: companyCode.value!,
    },
  });
  return data;
});
  /**
   * 企业范围polygon上球
   * 二维或三维均跳转到所在企业
   */
watchEffect(async () => {
  dataSoureEffect.removeScope();
  if (detail.value?.rangeGeometryData) {
    const geoJson = JSON.parse(detail.value?.rangeGeometryData ?? '{}');
    const dataSource = await Cesium.GeoJsonDataSource.load(geoJson, {
      fill: Cesium.Color.TRANSPARENT,
      strokeWidth: 4,
      // clampToGround: !is3D.value, //非三维要贴地
    });
    const _dataSource = await dataSoureEffect.add(dataSource);
    viewer.value.zoomTo(_dataSource);
  }
});

// 通过危险源编码获取设备列表
async function getEquipList(hazardCode: string) {
  const { data } = await productionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost({
    path: {
      hazardCode,
    },
  });
  return data ?? [];
}

/** 通过企业编码获取危险源列表，然后根据危险源编码获取设备列表 */
const equipList = computedAsync(async () => {
  if (!companyCode.value)
    return;
  const { data } = await productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet({
    path: {
      companyCode: companyCode.value,
    },
  });
  const equipListsPromises = data?.map(e => getEquipList(e.hazardCode!) ?? []);
  const equipLists = await Promise.all(equipListsPromises ?? []);
  const equipList = equipLists.flat();
  // 只返回有经纬坐标的设备
  return (
    equipList?.filter((equip) => {
      return Number(equip.latitude) && Number(equip.longitude);
    }) ?? []
  );
});

// 通过设备编码获取设备指标列表
async function getTargets(equipCode: string) {
  const { data } = await productionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet({
    path: {
      equipCode,
    },
  });
  return data ?? [];
}

// 通过设备编码获取设备指标列表
const targetsList = computedAsync(async () => {
  if (!equipList.value?.length)
    return;
  const targetspromises = equipList.value.map((e) => {
    return getTargets(e.equipCode!);
  });
  const targetsList = await Promise.all(targetspromises);
  return targetsList;
});

// 设备的经纬列表
const positions = computedAsync(async () => {
  let positions = equipList.value?.map(({ latitude, longitude }) => {
    return Cesium.Cartesian3.fromDegrees(+longitude!, +latitude!, 0);
  });
    // 如果是三维倾斜摄影，则点位需要获取精确高度
  if (is3D.value && positions?.length) {
    positions = await viewer.value.scene.clampToHeightMostDetailed(positions);
  }
  return positions;
});

const entityEffect = useCzEntityCollection();
// 设备监测名称点位上球
watchEffect(async () => {
  entityEffect.removeScope();
  positions.value?.forEach((position, index) => {
    const equip = equipList.value?.[index];

    const entity = new CzEntity({
      position,
      label: {
        font: '10pt Source Han Sans CN',
        fillColor: Cesium.Color.DARKORANGE,
        showBackground: companyCode.value !== '*********',
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 3,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 垂直位置
        text: `${equip?.equipName}\n${equip?.equipDescribe}`,
        pixelOffset: new Cesium.Cartesian2(0, -20),
        heightReference: is3D.value
          ? Cesium.HeightReference.CLAMP_TO_3D_TILE
          : Cesium.HeightReference.CLAMP_TO_GROUND,
        scaleByDistance: new Cesium.NearFarScalar(1000000, 1, 2000000, 0.1),
      },
    });
    entityEffect.add(entity);
    entity.event.on('LEFT_CLICK', () => {
      visible.value = true;
      currentIndex.value = index;
    });
  });
});

// 清空点中的设备
watch(companyCode, () => {
  currentIndex.value = undefined;
});
</script>

<template>
  <located-popper1
    v-if=" positions?.[currentIndex!]"
    show-close
    class="!h-330px !w-440px"
    :position="positions?.[currentIndex!]"
    :header="
      (equipList?.[currentIndex!]?.equipName ?? '')
        + (equipList?.[currentIndex!]?.equipDescribe ?? '')
    "
    content-class="px-10px"
    @close="currentIndex = undefined"
  >
    <el-tabs>
      <el-tab-pane
        v-for="(target, index) in targetsList?.[currentIndex!]"
        :key="index"
        :label="target.targetName"
      >
        <el-scrollbar>
          <el-descriptions size="small" :column="2" border>
            <el-descriptions-item label="实时值">
              {{ target.targetRealInfo?.lastValue }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="量程">
              {{ target?.rangeDown ?? '--' }}~{{ target?.rangeUp ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="上上限">
              {{ target.thresholdUp ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="上限">
              {{ target.thresholdUp2 ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="下限">
              {{ target.thresholdDown2 ?? '--' }}{{ target.unit }}
            </el-descriptions-item>
            <el-descriptions-item label="下下限">
              {{ target.thresholdDown ?? '--' }}{{ target.unit }}
            </el-descriptions-item>

            <el-descriptions-item label="采集时间">
              {{ target.targetRealInfo?.lastCollectTime }}
            </el-descriptions-item>
            <el-descriptions-item label="最后报警时间">
              {{ target.targetRealInfo?.lastAlarmStarted }}
            </el-descriptions-item>
          </el-descriptions>
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
  </located-popper1>
</template>

<style lang="scss" scoped>
  :deep().is-bordered-label.el-descriptions__label {
  text-align: right;
  white-space: nowrap;
}
</style>
