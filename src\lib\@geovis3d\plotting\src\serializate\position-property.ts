import type { WGS84 } from '@/lib/@geovis3d/coordinate';

import type { ReferenceFrameSerializateJSON } from './enum';
import type { JulianDateSerializateJSON } from './julian-date';
import * as Cesium from 'cesium';
import { EnumSerializate } from './enum';
import { TimeIntervalSerializate } from './time-interval';

export interface PositionPropertySerializateJSON {
  referenceFrame?: ReferenceFrameSerializateJSON;
  numberOfDerivatives?: number;
  cartesian?: number[];
  cartographicDegrees?: WGS84;
  epoch?: JulianDateSerializateJSON;
  nextTime?: number;
  previousTime?: number;
}

export type PositionPropertyKey = keyof PositionPropertySerializateJSON;

export class PositionPropertySerializate {
  private constructor() {}

  static toJSON(data?: Cesium.PositionProperty): PositionPropertySerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    if (data instanceof Cesium.SampledPositionProperty) {
      return {
        referenceFrame: EnumSerializate.toJSON(Cesium.ReferenceFrame, data.referenceFrame),
        numberOfDerivatives: data.numberOfDerivatives,
      };
    }
  }

  static fromJSON(json?: PositionPropertySerializateJSON): Cesium.PositionProperty | undefined {
    if (!json) {
      return undefined;
    }
    return new Cesium.ConstantPositionProperty(
      json.intervals.map(item => TimeIntervalSerializate.fromJSON(item)!),
    );
  }
}
