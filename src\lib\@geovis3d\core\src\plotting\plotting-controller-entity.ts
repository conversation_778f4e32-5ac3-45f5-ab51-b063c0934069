import type { GcEntityConstructorOptions } from '../vector';
import { coordinateToCartesian } from '@/lib/@geovis3d/coordinate';

import * as Cesium from 'cesium';

import { GcEntity } from '../vector';
import deleteSVG from './assets/delete.svg';
import heightSVG from './assets/height.svg';
import moveSVG from './assets/move.svg';
import scaleSVG from './assets/scale.svg';
import xSVG from './assets/x.svg';
import ySVG from './assets/y.svg';

import zSVG from './assets/z.svg';

/**
 * 拖拽回调
 */
export type OnPositionChangedCallback = (event: {
  startPosition: Cesium.Cartesian2;
  endPosition: Cesium.Cartesian2;
  position?: Cesium.Cartesian3;
  draging: boolean;
}) => void;

/**
 * @porperty parent  父级标绘entity
 * @porperty [autoSetPosition = true] 拖拽后是否自动设置位置
 * @porperty onPositionChanged 拖拽回调
 */
export interface PlottingControllerEntityConstructorOptions extends GcEntityConstructorOptions {
  parent: Cesium.Entity;
  onPositionChanged?: OnPositionChangedCallback;
  autoSetPosition?: boolean;
}

export class PlottingControllerEntity extends GcEntity {
  constructor(options: PlottingControllerEntityConstructorOptions) {
    const { onPositionChanged, autoSetPosition, ...other } = options;

    super(other);

    this.event.on('DRAG', ({ context, draging }) => {
      const owner = this.entityCollection?.owner as any;
      const scene = owner?.clustering?._scene as Cesium.Scene;
      // 拖拽时锁定屏幕视角
      scene.screenSpaceCameraController.enableRotate = !draging;
      const position = coordinateToCartesian(context.endPosition, scene, 'globePick')!;
      if (autoSetPosition && position) {
        this.position = new Cesium.ConstantPositionProperty(position);
      }
      onPositionChanged?.({
        startPosition: context.startPosition?.clone(),
        endPosition: context.endPosition?.clone(),
        position: position?.clone(),
        draging,
      });
    });
  }

  /**
   * 控制点默认配置
   */
  static CONTROL_STYLE: GcEntityConstructorOptions = {
    point: {
      outlineColor: Cesium.Color.WHITE.withAlpha(0.3),
      color: Cesium.Color.BLUE.withAlpha(0.8),
      pixelSize: 8,
      outlineWidth: 2,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '拖动该点修改位置'),
    prompt: options => (options.element.innerHTML = '释放以完成修改位置'),
  };

  /**
   * 间隔点默认配置
   */
  static INTERVAL_STYLE: GcEntityConstructorOptions = {
    point: {
      outlineColor: Cesium.Color.WHITE.withAlpha(0.3),
      color: Cesium.Color.GREEN.withAlpha(0.8),
      pixelSize: 8,
      outlineWidth: 2,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '拖动该点增加控制点'),
    prompt: options => (options.element.innerHTML = '释放以完成增加控制点'),
  };

  /**
   * 中心点默认配置
   */
  static CENTER_STYLE: GcEntityConstructorOptions = {
    point: {
      outlineColor: Cesium.Color.WHITE.withAlpha(0.3),
      color: Cesium.Color.RED.withAlpha(0.8),
      pixelSize: 8,
      outlineWidth: 2,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '拖动该点整体平移'),
    prompt: options => (options.element.innerHTML = '释放以完成平移'),
  };

  /**
   * 位置点默认配置
   */
  static LOCATION_STYLE: GcEntityConstructorOptions = {
    billboard: {
      image: moveSVG,
      width: 30,
      height: 30,
      pixelOffset: new Cesium.Cartesian2(-30, -60),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '拖动该点整体平移'),
    prompt: options => (options.element.innerHTML = '释放以完成平移'),
  };

  /**
   * 相对地面高度调节点默认配置
   */
  static ALTITUDE_STYLE: GcEntityConstructorOptions = {
    billboard: {
      image: heightSVG,
      width: 30,
      height: 30,
      pixelOffset: new Cesium.Cartesian2(0, -60),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '下上拖拽进行整体高度调节'),
    prompt: options => (options.element.innerHTML = '释放以完成高度调节'),
  };

  /**
   * 缩放点默认配置
   */
  static SCALE_STYLE: GcEntityConstructorOptions = {
    billboard: {
      image: scaleSVG,
      width: 30,
      height: 30,
      pixelOffset: new Cesium.Cartesian2(30, -60),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '拖动该点整体缩放'),
    prompt: options => (options.element.innerHTML = '释放以完成缩放'),
  };

  /**
   * 旋转X默认配置
   */
  static ROTATION_X_STYLE: GcEntityConstructorOptions = {
    billboard: {
      image: xSVG,
      width: 30,
      height: 30,
      pixelOffset: new Cesium.Cartesian2(-30, -90),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = 'X轴顺时针旋转,Shift+点击则逆时针旋转'),
  };

  /**
   * 旋转Y默认配置
   */
  static ROTATION_Y_STYLE: GcEntityConstructorOptions = {
    billboard: {
      image: ySVG,
      width: 30,
      height: 30,
      pixelOffset: new Cesium.Cartesian2(0, -90),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = 'Y轴顺时针旋转,Shift+点击则逆时针旋转'),
  };

  /**
   * 旋转Z默认配置
   */
  static ROTATION_Z_STYLE: GcEntityConstructorOptions = {
    billboard: {
      image: zSVG,
      width: 30,
      height: 30,
      pixelOffset: new Cesium.Cartesian2(30, -90),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = 'Z轴顺时针旋转,Shift+点击则逆时针旋转'),
  };

  /**
   * 删除点默认配置
   */
  static DELETE_STYLE: GcEntityConstructorOptions = {
    billboard: {
      image: deleteSVG,
      width: 30,
      height: 30,
      pixelOffset: new Cesium.Cartesian2(60, -90),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '移除当前绘制'),
  };

  /**
   * 采样控制点默认配置
   */
  static SAMPLED_CONTROL_STYLE: GcEntityConstructorOptions = {
    point: {
      outlineColor: Cesium.Color.WHITE.withAlpha(0.3),
      color: Cesium.Color.BLUE.withAlpha(0.8),
      pixelSize: 8,
      outlineWidth: 2,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '拖动该点修改位置'),
    prompt: options => (options.element.innerHTML = '释放以完成修改'),
  };

  /**
   * 采样间隔点默认配置
   */
  static SAMPLED_INTERVAL_STYLE: GcEntityConstructorOptions = {
    point: {
      outlineColor: Cesium.Color.WHITE.withAlpha(0.3),
      color: Cesium.Color.GREEN.withAlpha(0.8),
      pixelSize: 8,
      outlineWidth: 2,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    tooltip: options => (options.element.innerHTML = '拖动该点增加采样点'),
    prompt: options => (options.element.innerHTML = '释放以完成增加采样点'),
  };

  /**
   * 采样路线默认配置
   */
  static SAMPLED_LINE_STYLE: GcEntityConstructorOptions = {
    polyline: {
      width: 1,
      material: Cesium.Color.YELLOW.withAlpha(0.8),
      // clampToGround: true,
    },
  };
}
