<!-- Cartesian2Attribute -->
<script lang="ts" setup>
import type { Cartesian2SerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

export interface Cartesian2AttributeProps {
  modelValue?: Cartesian2SerializateJSON;
  label?: string;
  xLabel?: string;
  yLabel?: string;
}
defineOptions({ name: 'Cartesian2Attribute' });

const props = withDefaults(defineProps<Cartesian2AttributeProps>(), {
  xLabel: 'X',
  yLabel: 'Y',
});

const emit = defineEmits<{
  (event: 'update:modelValue', value?: Cartesian2SerializateJSON): void;
}>();

const model = ref<Cartesian2SerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div>
    <el-form-item :label="label" />
    <NumberAttribute v-model.number="model.x" :label="xLabel" />
    <NumberAttribute v-model.number="model.y" :label="yLabel" />
  </div>
</template>
