import type { <PERSON>lot<PERSON><PERSON>ontroller } from './plotting-controller';
import type { ControlPointSymbol } from './plotting-coordinates-property';
import type { PlottingEntity } from './plotting-entity';

import {
  cartesianToCartographic,
  cartographicToCartesian,
  coordinateToCartesian,
} from '@/lib/@geovis3d/coordinate';

import { arrayDiff, isFunction, throttle } from '@/lib/@geovis3d/shared';
import * as Cesium from 'cesium';
import { PlottingControllerEntity } from './plotting-controller-entity';

/**
 * 当标绘处于激活态时的相关回调
 * @param controller
 */
export function plottingActiveCallback(controller: PlottingController) {
  /**
   * 可见性判断函数
   * @param visible
   */
  function resolveVisible<T>(visible?: T | ((entity: PlottingEntity) => T)): () => T | undefined {
    return () => {
      if (isFunction(visible)) {
        return visible(controller.entity);
      }
      else {
        return visible;
      }
    };
  }

  /**
   */
  let intervalDraging = false;

  /**
   * 控制点
   */
  const control = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      if (intervalDraging) {
        return;
      }
      const options = controller.options?.control;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates } = controller;

      if (!visible()) {
        cleanup();
        return;
      }
      const positionSymbols = coordinates.getValue();
      const prevIds = entities.map(e => e.id);
      const newIds = positionSymbols.map(e => e.id);
      const { existed, added, missed } = arrayDiff(prevIds, newIds);
      existed.forEach((id) => {
        const entity = entities.find(e => e.id === id);
        const position = coordinates.getById(id);
        entity && (entity.position = new Cesium.ConstantPositionProperty(position));
      });
      added.forEach((id) => {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.CONTROL_STYLE,
          id,
          position: coordinates.getById(id),
          autoSetPosition: true,
          parent,
          onPositionChanged: ({ position }) => {
            position && coordinates.setById(id, position);
          },
        });
        entities.push(entity);
        parent.entityCollection.add(entity);
      });

      missed.forEach((id) => {
        parent.entityCollection.removeById(id);
        const index = entities.findIndex(e => e.id === id);
        index != -1 && entities.splice(index, 1);
      });
    };
    return { cleanup, render };
  })();

  /**
   *
   * 间隔点
   */
  const interval = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      if (intervalDraging) {
        return;
      }
      const options = controller.options?.interval;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates } = controller;
      cleanup();
      if (!visible()) {
        return;
      }
      const loop = visible() === 'loop';
      const positions = coordinates.getPositions();
      positions.forEach((position, index) => {
        if (!loop && index === 0) {
          return;
        }
        const prev = index == 0 ? positions.at(-1) : positions[index - 1];
        const midpoint = Cesium.Cartesian3.midpoint(prev, position, position.clone());
        let item: ControlPointSymbol;
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.INTERVAL_STYLE,
          position: midpoint,
          autoSetPosition: true,
          parent,
          onPositionChanged: ({ position, draging }) => {
            intervalDraging = draging;
            if (position) {
              item
                ? coordinates.setById(item.id, position)
                : (item = coordinates.addPosition(position, index));
            }
          },
        });
        parent.entityCollection.add(entity);
        entities.push(entity);
      });
    };
    return { cleanup, render };
  })();

  /**
   * 中心点
   */
  const center = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.center;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates, scene } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];

      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.CENTER_STYLE,
          position,
          parent,
          onPositionChanged: ({ startPosition, position: end }) => {
            const start = coordinateToCartesian(startPosition, scene!)!;
            if (start && end) {
              const offset = Cesium.Cartesian3.subtract(end, start, start.clone());
              const list = coordinates.getValue();
              list.forEach(({ id, position }) => {
                coordinates.setById(id, Cesium.Cartesian3.add(position, offset, position.clone()));
              });
            }
          },
        });
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * 位置控制
   */
  const location = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.location;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates, scene } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];

      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.LOCATION_STYLE,
          position,
          parent,
          onPositionChanged: ({ startPosition, position: end }) => {
            const start = coordinateToCartesian(startPosition, scene!)!;
            if (start && end) {
              const offset = Cesium.Cartesian3.subtract(end, start, start.clone());
              const list = coordinates.getValue();
              list.forEach(({ id, position }) => {
                coordinates.setById(id, Cesium.Cartesian3.add(position, offset, position.clone()));
              });
            }
          },
        });
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * 海拔控制
   */
  const altitude = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.altitude;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates, scene } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];
      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.ALTITUDE_STYLE,
          position,
          parent,
          autoSetPosition: false,
          onPositionChanged: (ctx) => {
            const offset = ctx.startPosition.y - ctx.endPosition.y;
            const scale = offset > 0 ? 1 : -1; // 上升或下降
            const list = coordinates.getValue();
            const offsetHeight = scale * 0.005 * scene!.camera.positionCartographic.height!;
            list.forEach(({ id, position }) => {
              const prev = cartesianToCartographic(position);
              prev.height ??= 0;
              prev.height += offsetHeight;
              position = cartographicToCartesian(prev);
              coordinates.setById(id, position);
            });
          },
        });
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * 缩放控制
   */
  const scale = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.scale;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];
      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.SCALE_STYLE,
          position,
          parent,
          onPositionChanged: ({ startPosition, endPosition }) => {
            const vector1 = startPosition.x;
            const vector2 = endPosition.x;
            const scale = vector1 > vector2 ? 0.95 : vector1 == vector2 ? 1 : 1.05;
            controller.options?.scaleCallback?.(parent, scale);
          },
        });
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * 删除控制
   */
  const deletes = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.delete;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates, scene } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];

      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.DELETE_STYLE,
          position,
          parent,
          onPositionChanged: ({ startPosition, position: end }) => {
            const start = coordinateToCartesian(startPosition, scene!)!;
            if (start && end) {
              const offset = Cesium.Cartesian3.subtract(end, start, start.clone());
              const list = coordinates.getValue();
              list.forEach(({ id, position }) => {
                coordinates.setById(id, Cesium.Cartesian3.add(position, offset, position.clone()));
              });
            }
          },
        });
        entity.event.on('LEFT_CLICK', () => parent.entityCollection.remove(parent));
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * 旋转控制
   * @param entity
   * @param direction 需要旋转的轴
   * @param reverse 是否反方向
   */
  const rotation = (entity: PlottingEntity, direction: 'x' | 'y' | 'z', reverse?: boolean) => {
    const time = Cesium.JulianDate.now();
    const position = entity.position?.getValue(time);
    const quaternion: Cesium.Quaternion
      = entity.orientation?.getValue(time)
        ?? Cesium.Transforms.headingPitchRollQuaternion(position!, new Cesium.HeadingPitchRoll());
    const mtx3 = Cesium.Matrix3.fromQuaternion(quaternion);
    const mtx4 = Cesium.Matrix4.fromRotationTranslation(mtx3, position!);
    const hpr = Cesium.Transforms.fixedFrameToHeadingPitchRoll(mtx4);
    const num = reverse ? -Cesium.Math.PI / 18 : Cesium.Math.PI / 18;
    if (direction == 'x') {
      hpr.heading += num;
    }
    else if (direction == 'y') {
      hpr.pitch += num;
    }
    else if (direction == 'z') {
      hpr.roll += num;
    }

    if (hpr.pitch >= Cesium.Math.PI / 2) {
      hpr.pitch = Cesium.Math.PI / 2 - 0.1;
    }
    else if (hpr.pitch <= -Cesium.Math.PI / 2) {
      hpr.pitch = -Cesium.Math.PI / 2 + 0.1;
    }

    const _quaternion = Cesium.Transforms.headingPitchRollQuaternion(position!, hpr.clone());
    const orientation = entity.orientation as Cesium.ConstantProperty;
    !orientation?.setValue
      ? (entity.orientation = new Cesium.ConstantProperty(_quaternion))
      : orientation?.setValue(_quaternion);
  };

  /**
   * X轴旋转控制
   */
  const rotationX = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.rotationX;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];
      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.ROTATION_X_STYLE,
          position,
          parent,
        });
        entity.event.on('LEFT_CLICK', () => rotation(parent, 'x'));
        entity.event.on(
          'LEFT_CLICK',
          () => rotation(parent, 'x', true),
          Cesium.KeyboardEventModifier.SHIFT,
        );
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * Y轴旋转控制
   */
  const rotationY = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.rotationY;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];
      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.ROTATION_Y_STYLE,
          position,
          parent,
        });
        entity.event.on('LEFT_CLICK', () => rotation(parent, 'y'));
        entity.event.on(
          'LEFT_CLICK',
          () => rotation(parent, 'y', true),
          Cesium.KeyboardEventModifier.SHIFT,
        );
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * Z轴旋转控制
   */
  const rotationZ = (() => {
    const entities: PlottingControllerEntity[] = [];

    const cleanup = () => {
      const { entity: parent } = controller;
      entities.forEach(entity => parent.entityCollection.remove(entity));
      entities.length = 0;
    };

    const render = () => {
      const options = controller.options?.rotationZ;
      const visible = resolveVisible(options?.visible);
      const { entity: parent, coordinates } = controller;
      if (!visible()) {
        cleanup();
        return;
      }
      const position = coordinates.getCenter();
      if (!position) {
        return;
      }
      const entity = entities?.[0];
      if (entity) {
        entity.position = new Cesium.ConstantPositionProperty(position);
      }
      else {
        const entity = new PlottingControllerEntity({
          ...PlottingControllerEntity.ROTATION_Z_STYLE,
          position,
          parent,
        });
        entity.event.on('LEFT_CLICK', () => rotation(parent, 'z'));
        entity.event.on(
          'LEFT_CLICK',
          () => rotation(parent, 'z', true),
          Cesium.KeyboardEventModifier.SHIFT,
        );
        entities.push(entity);
        parent.entityCollection.add(entity);
      }
    };
    return { cleanup, render };
  })();

  /**
   * 渲染总线
   */
  const render = throttle(() => {
    const { scene, active, defining, entity } = controller;
    if (active && !defining && scene && entity.isMounted) {
      control.render();
      interval.render();
      center.render();
      location.render();
      altitude.render();
      scale.render();
      deletes.render();
      rotationX.render();
      rotationY.render();
      rotationZ.render();
    }
    else {
      control.cleanup();
      interval.cleanup();
      center.cleanup();
      location.cleanup();
      altitude.cleanup();
      scale.cleanup();
      deletes.cleanup();
      rotationX.cleanup();
      rotationY.cleanup();
      rotationZ.cleanup();
    }
  }, 0);

  controller.definitionChanged.addEventListener(render);
  controller.entity.definitionChanged.addEventListener(render);
  controller.coordinates.definitionChanged.addEventListener(render);
}
