import type * as Cesium from 'cesium';

import type { BD09, Coordinate, GCJ02, WGS84 } from './types';
import {
  cartesianToBd09,
  cartesianToCartographic,
  cartesianToGcj02,
  cartesianToWgs84,
} from './cartesian';

/**
 * Coordinate 转 Cartographic
 * @param position Coordinate
 * @returns Cartographic | undefined
 */
export function coordinateToCartographic(
  position: Coordinate,
  scene: Cesium.Scene,
): Cesium.Cartographic | undefined {
  const cartesian = coordinateToCartesian(position, scene);
  return cartesian && cartesianToCartographic(cartesian);
}

/**
 * Coordinate 转 Cartesian3
 * @param position Coordinate
 *
 * @param mode  转换模式
 * @defualt auto
 * pickPosition：使用scene.pickPosition进行转换，可贴模型、倾斜摄影等进行拾取。但如果未开启深度监测(globe.depthTestAgainstTerrain=false)则贴地形拾取或出现不准确的问题
 *
 * globePick：使用camera.getPickRay 进行转换，不可贴模型倾斜摄影拾取，但可以贴地形拾取，如果不存在地形，则拾取的高程为0
 *
 * auto：自动进行判断返回何种拾取内容
 *
 * 计算速度对比  globePick > auto >= pickPosition
 */
export function coordinateToCartesian(
  position: Coordinate,
  scene: Cesium.Scene,
  mode: 'pickPosition' | 'globePick' | 'auto' | 'noHeight' = 'auto',
): Cesium.Cartesian3 | undefined {
  if (mode === 'pickPosition') {
    return scene.pickPosition(position);
  }
  else if (mode === 'globePick') {
    const ray = scene.camera.getPickRay(position);
    return ray && scene.globe.pick(ray, scene);
  }
  else {
    // 如果开启深度监测
    if (scene.globe.depthTestAgainstTerrain) {
      return scene.pickPosition(position);
    }
    const cartesian1 = scene.pickPosition(position);
    const ray = scene.camera.getPickRay(position);
    const cartesian2 = ray && scene.globe.pick(ray, scene);
    if (!cartesian1) {
      return cartesian2;
    }
    const height1 = (cartesian1 && cartesianToCartographic(cartesian1).height) ?? 0;
    const height2 = (cartesian2 && cartesianToCartographic(cartesian2).height) ?? 0;
    return height1 < height2 ? cartesian2 : cartesian1;
  }
}

/**
 * Coordinate 转 BD09
 * @param position Coordinate
 * @returns BD09 | undefined
 */
export function coordinateToBd09(position: Coordinate, scene: Cesium.Scene): BD09 | undefined {
  const cartesian = coordinateToCartesian(position, scene);
  return cartesian && cartesianToBd09(cartesian);
}

/**
 * Coordinate 转 WGS84
 * @param position Coordinate
 * @returns WGS84 | undefined
 */
export function coordinateToWgs84(position: Coordinate, scene: Cesium.Scene): WGS84 | undefined {
  const cartesian = coordinateToCartesian(position, scene);
  return cartesian && cartesianToWgs84(cartesian);
}

/**
 * Coordinate 转 GCJ02
 * @param position Coordinate
 * @returns GCJ02 | undefined
 */
export function coordinateToGcj02(position: Coordinate, scene: Cesium.Scene): GCJ02 | undefined {
  const cartesian = coordinateToCartesian(position, scene);
  return cartesian && cartesianToGcj02(cartesian);
}
