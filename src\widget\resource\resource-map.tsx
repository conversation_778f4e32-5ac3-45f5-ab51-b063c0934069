import { disasterFloodWindPreventionEmergencySuppliesListTableRowIdUsingPost } from '@/genapi/disaster';

import {
  ElDescriptions as Descs,
  ElDescriptionsItem as DescsItem,
  ElTable,
  ElTableColumn,
  ElTabPane,
  ElTabs,
} from 'element-plus';

import 'element-plus/es/components/descriptions/style/css';
import 'element-plus/es/components/tabs/style/css';

export const ResourceMap: Record<any, any> = {
  '其他隐患': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>
        <DescsItem label="隐患来源">{t.yhbsly}</DescsItem>
        <DescsItem label="整改措施">{t.zgcs}</DescsItem>
        <DescsItem label="整改责任单位">{t.zgzrdw}</DescsItem>
        <DescsItem label="整改进展情况">{t.zgjzqk}</DescsItem>
        <DescsItem label="（预计）整改完成时间">{t.zgwcsj}</DescsItem>
        <DescsItem label="确认已开展的整改措施">{t.qrykzdzgcs}</DescsItem>
        <DescsItem label="整改措施存在的问题">{t.zgcsczdwt}</DescsItem>
        <DescsItem label="是否整改到位">{t.sfzgdw}</DescsItem>
        <DescsItem label="是否可以销号">{t.sfkyxh}</DescsItem>
        <DescsItem label="下一步督促指导措施">{t.xybdczdcs}</DescsItem>
        <DescsItem label="所在市县">{t.szsx}</DescsItem>
        <DescsItem label="所在乡镇（区）">{t.szxz}</DescsItem>
        <DescsItem label="所在行政村">{t.szxzc}</DescsItem>
        <DescsItem label="具体地址">{t.jtdd}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '堤防': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="堤防工程名称">{t.name}</DescsItem>
        <DescsItem label="责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="堤防级别">{t.grade}</DescsItem>
        <DescsItem label="防护人口(万人)">{t.protectionpeoplenum}</DescsItem>
        <DescsItem label="防护农田(万亩)">{t.protectioncropland}</DescsItem>
        <DescsItem label="主要存在问题">{t.problem}</DescsItem>
        <DescsItem label="主要应急处理措施">{t.problemprocessing}</DescsItem>
        <DescsItem label="行政编码">{t.addvcd}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '低洼地': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="所属地市">{t.province}</DescsItem>
        <DescsItem label="区/县">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="户主姓名">{t.housename}</DescsItem>
        <DescsItem label="户数">{t.households}</DescsItem>
        <DescsItem label="人数">{t.peoplenum}</DescsItem>
        <DescsItem label="安置地点">{t.temporaryshelter}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="危险等级">{t.hazardlevel}</DescsItem>
        <DescsItem label="行政编码">{t.addvcd}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '易淹易涝小区': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="所在位置">
          {`${t.szsx}`}
          {`${t.szxz}`}
          {`${t.jtdd}`}
        </DescsItem>
        <DescsItem label="类型">{t.lx}</DescsItem>
        <DescsItem label="影响户数">{t.yxhs}</DescsItem>
        <DescsItem label="影响人数">{t.yxrs}</DescsItem>
        <DescsItem label="转移安置地点">{t.zyazdd}</DescsItem>
        <DescsItem label="转移责任人">{t.zrr}</DescsItem>
        <DescsItem label="电话">{t.zrrsjhm}</DescsItem>
        <DescsItem label="主要对策/整改措施">{t.zgcs}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '水闸': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="水闸名称">{t.name}</DescsItem>
        <DescsItem label="责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="水闸详细地址">{t.address}</DescsItem>
        <DescsItem label="工程规模">{t.scale}</DescsItem>
        <DescsItem label="灌溉面积(万亩)">{t.wateringproportion}</DescsItem>
        <DescsItem label="防护人口(万人)">{t.protectionpeoplenum}</DescsItem>
        <DescsItem label="防护农田(万亩)">{t.protectioncropland}</DescsItem>
        <DescsItem label="主要存在问题">{t.problem}</DescsItem>
        <DescsItem label="主要应急处理措施">{t.problemprocessing}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '森林公园': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="所属地市">{t.province}</DescsItem>
        <DescsItem label="区/县">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="面积">{t.totalarea}</DescsItem>
        <DescsItem label="主要树木">{t.vegetation}</DescsItem>
        <DescsItem label="影响周边范围">{t.affectingcrowd}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '崩塌': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="地质灾害名称">{t.name}</DescsItem>
        <DescsItem label="灾害类型">{t.type}</DescsItem>
        <DescsItem label="监测责任人">{t.principalpersonjc}</DescsItem>
        <DescsItem label="监测责任人电话">{t.principalpersonjctel}</DescsItem>
        <DescsItem label="监测人">{t.monitorperson}</DescsItem>
        <DescsItem label="检测人电话">{t.monitorpersontel}</DescsItem>
        <DescsItem label="防灾责任人">{t.principalperson}</DescsItem>
        <DescsItem label="防灾责任人电话">{t.principalpersontel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="威胁人口（人）">{t.peopleNum}</DescsItem>
        <DescsItem label="威胁资产（万元）">{t.threatenedassets}</DescsItem>
        <DescsItem label="灾害规模">{t.scale}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '泥石流': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="地质灾害名称">{t.name}</DescsItem>
        <DescsItem label="灾害类型">{t.type}</DescsItem>
        <DescsItem label="监测责任人">{t.principalpersonjc}</DescsItem>
        <DescsItem label="监测责任人电话">{t.principalpersonjctel}</DescsItem>
        <DescsItem label="监测人">{t.monitorperson}</DescsItem>
        <DescsItem label="检测人电话">{t.monitorpersontel}</DescsItem>
        <DescsItem label="防灾责任人">{t.principalperson}</DescsItem>
        <DescsItem label="防灾责任人电话">{t.principalpersontel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="威胁人口（人）">{t.peopleNum}</DescsItem>
        <DescsItem label="威胁资产（万元）">{t.threatenedassets}</DescsItem>
        <DescsItem label="灾害规模">{t.scale}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '滑坡': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="地质灾害名称">{t.name}</DescsItem>
        <DescsItem label="灾害类型">{t.type}</DescsItem>
        <DescsItem label="监测责任人">{t.principalpersonjc}</DescsItem>
        <DescsItem label="监测责任人电话">{t.principalpersonjctel}</DescsItem>
        <DescsItem label="监测人">{t.monitorperson}</DescsItem>
        <DescsItem label="检测人电话">{t.monitorpersontel}</DescsItem>
        <DescsItem label="防灾责任人">{t.principalperson}</DescsItem>
        <DescsItem label="防灾责任人电话">{t.principalpersontel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="威胁人口（人）">{t.peopleNum}</DescsItem>
        <DescsItem label="威胁资产（万元）">{t.threatenedassets}</DescsItem>
        <DescsItem label="灾害规模">{t.scale}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '防汛物资仓库': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="仓库名称">{t.depotname}</DescsItem>
        <DescsItem label="负责人">{t.leader}</DescsItem>
        <DescsItem label="联系人">{t.linkman}</DescsItem>
        <DescsItem label="联系电话">{t.phone}</DescsItem>
        <DescsItem label="使用面积">{t.area}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="建设时间">{t.buildtime}</DescsItem>
        <DescsItem label="建筑结构">{t.buildstruct}</DescsItem>
        <DescsItem label="所属组织机构">{t.dept}</DescsItem>
        <DescsItem label="创建人">{t.creator}</DescsItem>
        <DescsItem label="创建时间">{t.createtime}</DescsItem>
        <DescsItem label="单位名称">{t.unitname}</DescsItem>
        <DescsItem label="物资视频">{t.depotviedo}</DescsItem>
        <DescsItem label="物资图片">{t.depotimg}</DescsItem>
        <DescsItem label="备注">{t.remark}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '应急物资仓库': ({ data: t, raw, longitude, latitude }) => {
    const { data } = useAsyncState(async () => {
      const { data } = await disasterFloodWindPreventionEmergencySuppliesListTableRowIdUsingPost({
        path: {
          tableRowId: raw.tableRowId,
        },
        data: {
          tableRowId: t.id,
        },
      });
      return data;
    }, []);
    return () => (
      <ElTabs modelValue="基本信息">
        <ElTabPane label="基本信息" name="基本信息">
          <Descs border column={2}>
            <DescsItem label="仓库名称">{t.depotname}</DescsItem>
            <DescsItem label="负责人">{t.leader}</DescsItem>
            <DescsItem label="联系人">{t.linkman}</DescsItem>
            <DescsItem label="联系电话">{t.phone}</DescsItem>
            <DescsItem label="使用面积">{t.area}</DescsItem>
            <DescsItem label="地址">{t.address}</DescsItem>
            <DescsItem label="建设时间">{t.buildtime}</DescsItem>
            <DescsItem label="建筑结构">{t.buildstruct}</DescsItem>
            <DescsItem label="所属组织机构">{t.dept}</DescsItem>
            <DescsItem label="创建人">{t.creator}</DescsItem>
            <DescsItem label="创建时间">{t.createtime}</DescsItem>
            <DescsItem label="单位名称">{t.unitname}</DescsItem>
            <DescsItem label="物资视频">{t.depotviedo}</DescsItem>
            <DescsItem label="物资图片">{t.depotimg}</DescsItem>
            <DescsItem label="备注">{t.remark}</DescsItem>
            <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
            <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
          </Descs>
        </ElTabPane>
        <ElTabPane label="物资详情" name="物资详情" border>
          <ElTable data={data.value} border size="small">
            <ElTableColumn label="物资名称" prop="name" />
            <ElTableColumn label="规格" prop="model" />

            <ElTableColumn label="类型" prop="type" />
            <ElTableColumn label="数量" prop="count" />
            <ElTableColumn label="单位" prop="unit" />
          </ElTable>
        </ElTabPane>
      </ElTabs>
    );
  },
  '救灾物资仓库': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="仓库名称">{t.depotname}</DescsItem>
        <DescsItem label="负责人">{t.leader}</DescsItem>
        <DescsItem label="联系人">{t.linkman}</DescsItem>
        <DescsItem label="联系电话">{t.phone}</DescsItem>
        <DescsItem label="仓库编码">{t.depotcode}</DescsItem>
        <DescsItem label="使用面积">{t.area}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="建设时间">{t.buildtime}</DescsItem>
        <DescsItem label="建筑结构">{t.buildstruct}</DescsItem>
        <DescsItem label="所属组织机构">{t.dept}</DescsItem>
        <DescsItem label="单位名称">{t.unitname}</DescsItem>
        <DescsItem label="物资视频">{t.depotviedo}</DescsItem>
        <DescsItem label="物资图片">{t.depotimg}</DescsItem>
        <DescsItem label="同步时间">{t.syncTime}</DescsItem>
        <DescsItem label="备注">{t.remark}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '避风港': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="渔港名称">{t.name}</DescsItem>
        <DescsItem label="监管单位">{t.regulators}</DescsItem>
        <DescsItem label="监管责任人联系方式">{t.regulatorstel}</DescsItem>
        <DescsItem label="港口容纳船只数">{t.accommodatenum}</DescsItem>
        <DescsItem label="低潮时航道平均水深(米)">{t.averagedepth}</DescsItem>
        <DescsItem label="低潮时港池水深(米)">{t.depth}</DescsItem>
        <DescsItem label="港池面积(万平方米)">{t.acreage}</DescsItem>
        <DescsItem label="护岸堤(米)">{t.bankprotectiondam}</DescsItem>
        <DescsItem label="防波堤(米)">{t.breakwater}</DescsItem>
        <DescsItem label="渔业码头(米)">{t.fishingwharf}</DescsItem>
        <DescsItem label="应急预案">{t.contingencyplan}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '防洪楼': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="防洪楼名称">{t.name}</DescsItem>
        <DescsItem label="防洪楼别名">{t.alias}</DescsItem>
        <DescsItem label="防洪楼管理责任人">{t.principalperson}</DescsItem>
        <DescsItem label="防洪楼管理责任人">{t.principalpersontel}</DescsItem>
        <DescsItem label="可转移人数(人)">{t.mayshiftnum}</DescsItem>
        <DescsItem label="风险点">{t.riskpoint}</DescsItem>
        <DescsItem label="村庄人口数">{t.peoplenum}</DescsItem>
        <DescsItem label="结构">{t.structure}</DescsItem>
        <DescsItem label="防洪楼尺寸（长）(米)">{t.along}</DescsItem>
        <DescsItem label="防洪楼尺寸（宽）(米)">{t.wide}</DescsItem>
        <DescsItem label="防洪楼尺寸（高）(米)">{t.high}</DescsItem>
        <DescsItem label="层数">{t.pliesnum}</DescsItem>
        <DescsItem label="建筑面积(m2)">{t.proportion}</DescsItem>
        <DescsItem label="地面高程">{t.groundelevation}</DescsItem>
        <DescsItem label="二层楼底高程(米)">{t.cellingheightelevation}</DescsItem>
        <DescsItem label="建设时间">{t.constructiontime}</DescsItem>
        <DescsItem label="建设单位">{t.developmentorganization}</DescsItem>
        <DescsItem label="工程投资">{t.engineeringinvestment}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="流域">{t.drainagebasin}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '地震避难场所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="点位名称">{t.name}</DescsItem>
        <DescsItem label="联系人">{t.contactman}</DescsItem>
        <DescsItem label="联系人电话">{t.contacttel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="点位地址">{t.address}</DescsItem>
        <DescsItem label="避难类型">{t.type}</DescsItem>
        <DescsItem label="占用面积">{t.occupyarea}</DescsItem>
        <DescsItem label="楼层面积">{t.floorarea}</DescsItem>
        <DescsItem label="室内面积">{t.indoorarea}</DescsItem>
        <DescsItem label="容纳范围">{t.scoperegion}</DescsItem>
        <DescsItem label="最大容纳人数">{t.maxpeoples}</DescsItem>
        <DescsItem label="备注">{t.remarks}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '台风避难场所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="点位名称">{t.name}</DescsItem>
        <DescsItem label="联系人">{t.contactman}</DescsItem>
        <DescsItem label="联系人电话">{t.contacttel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="点位地址">{t.address}</DescsItem>
        <DescsItem label="避难类型">{t.type}</DescsItem>
        <DescsItem label="占用面积">{t.occupyarea}</DescsItem>
        <DescsItem label="楼层面积">{t.floorarea}</DescsItem>
        <DescsItem label="室内面积">{t.indoorarea}</DescsItem>
        <DescsItem label="容纳范围">{t.scoperegion}</DescsItem>
        <DescsItem label="最大容纳人数">{t.maxpeoples}</DescsItem>
        <DescsItem label="备注">{t.remarks}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '洪涝避难场所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="点位名称">{t.name}</DescsItem>
        <DescsItem label="联系人">{t.contactman}</DescsItem>
        <DescsItem label="联系人电话">{t.contacttel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="点位地址">{t.address}</DescsItem>
        <DescsItem label="避难类型">{t.type}</DescsItem>
        <DescsItem label="占用面积">{t.occupyarea}</DescsItem>
        <DescsItem label="楼层面积">{t.floorarea}</DescsItem>
        <DescsItem label="室内面积">{t.indoorarea}</DescsItem>
        <DescsItem label="容纳范围">{t.scoperegion}</DescsItem>
        <DescsItem label="最大容纳人数">{t.maxpeoples}</DescsItem>
        <DescsItem label="备注">{t.remarks}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '台风洪涝避难场所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="点位名称">{t.name}</DescsItem>
        <DescsItem label="联系人">{t.contactman}</DescsItem>
        <DescsItem label="联系人电话">{t.contacttel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="点位地址">{t.address}</DescsItem>
        <DescsItem label="避难类型">{t.type}</DescsItem>
        <DescsItem label="占用面积">{t.occupyarea}</DescsItem>
        <DescsItem label="楼层面积">{t.floorarea}</DescsItem>
        <DescsItem label="室内面积">{t.indoorarea}</DescsItem>
        <DescsItem label="容纳范围">{t.scoperegion}</DescsItem>
        <DescsItem label="最大容纳人数">{t.maxpeoples}</DescsItem>
        <DescsItem label="备注">{t.remarks}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '消防队伍': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="点位名称">{t.name}</DescsItem>
        <DescsItem label="机构简称">{t.abbreviation}</DescsItem>
        <DescsItem label="机构类型">{t.type}</DescsItem>
        <DescsItem label="联系人">{t.principalperson}</DescsItem>
        <DescsItem label="联系电话">{t.principalpersontel}</DescsItem>
        <DescsItem label="传真号码">{t.faxnum}</DescsItem>
        <DescsItem label="机构代码">{t.institutioncode}</DescsItem>
        <DescsItem label="邮政编码">{t.postalcode}</DescsItem>
        <DescsItem label="其他信息">{t.otherinfo}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="点位地址">{t.address}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '消防救援队站': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="地区">{t.address}</DescsItem>
        <DescsItem label="机构代码">{t.addvcd}</DescsItem>
        <DescsItem label="车辆总数">{t.carAmount}</DescsItem>
        <DescsItem label="人员总数">{t.personAmount}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '扑火队伍': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="点位名称">{t.name}</DescsItem>
        <DescsItem label="点位地址">{t.address}</DescsItem>
        <DescsItem label="负责人">{t.principalperson}</DescsItem>
        <DescsItem label="联系电话">{t.principalpersontel}</DescsItem>
        <DescsItem label="装备人员">{t.equippersonnel}</DescsItem>
        <DescsItem label="人数">{t.populationnum}</DescsItem>
        <DescsItem label="集结地">{t.rendezvous}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="装备情况">{t.equipcondition}</DescsItem>
        <DescsItem label="其他信息">{t.otherinfo}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '消防救援队伍': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="地区">{t.dq}</DescsItem>
        <DescsItem label="队伍名称">{t.dwmc}</DescsItem>
        <DescsItem label="主管部门">{t.zgbm}</DescsItem>
        <DescsItem label="所在地点">{t.szdd}</DescsItem>
        <DescsItem label="队伍人数">{t.dwrs}</DescsItem>
        <DescsItem label="消防员">{t.xfy}</DescsItem>
        <DescsItem label="专职">{t.zz}</DescsItem>
        <DescsItem label="负责人">{t.fzr}</DescsItem>
        <DescsItem label="联系方式">{t.lxfs}</DescsItem>
        <DescsItem label="营房面积（M2）">{t.yfmj}</DescsItem>
        <DescsItem label="核心装备及数量">{t.hxzbjsl}</DescsItem>
        <DescsItem label="批次号">{t.pch}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '专业救援队伍': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="队伍名称">{t.teamName}</DescsItem>
        <DescsItem label="值班员">{t.dutyName}</DescsItem>
        <DescsItem label="类别">{t.type}</DescsItem>
        <DescsItem label="电话">{t.contactDesc}</DescsItem>
        <DescsItem label="人数/力量">{t.scale}</DescsItem>
        <DescsItem label="驻地">{t.address}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '社会救援组织': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="编号">{t.codeOne}</DescsItem>
        <DescsItem label="联系人">{t.contactman}</DescsItem>
        <DescsItem label="联系人电话">{t.contacttel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="点位地址">{t.address}</DescsItem>
        <DescsItem label="救援类型">{t.saveservice}</DescsItem>
        <DescsItem label="备注">{t.remarks}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '医院': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="社会信用代码">{t.shxydm}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="编制职工人数">{t.bzzgrs}</DescsItem>
        <DescsItem label="在职职工人数">{t.zzzgrs}</DescsItem>
        <DescsItem label="编制床位数">{t.bzcws}</DescsItem>
        <DescsItem label="实际床位数">{t.sjcws}</DescsItem>
        <DescsItem label="医疗机构类型">{t.hclass}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '卫生院': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="社会信用代码">{t.shxydm}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="编制职工人数">{t.bzzgrs}</DescsItem>
        <DescsItem label="在职职工人数">{t.zzzgrs}</DescsItem>
        <DescsItem label="编制床位数">{t.bzcws}</DescsItem>
        <DescsItem label="实际床位数">{t.sjcws}</DescsItem>
        <DescsItem label="医疗机构类型">{t.hclass}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '诊所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="社会信用代码">{t.shxydm}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="编制职工人数">{t.bzzgrs}</DescsItem>
        <DescsItem label="在职职工人数">{t.zzzgrs}</DescsItem>
        <DescsItem label="编制床位数">{t.bzcws}</DescsItem>
        <DescsItem label="实际床位数">{t.sjcws}</DescsItem>
        <DescsItem label="医疗机构类型">{t.hclass}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '急救中心': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.type}</DescsItem>
        <DescsItem label="社会信用代码">{t.shxydm}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="编制职工人数">{t.bzzgrs}</DescsItem>
        <DescsItem label="在职职工人数">{t.zzzgrs}</DescsItem>
        <DescsItem label="编制床位数">{t.bzcws}</DescsItem>
        <DescsItem label="实际床位数">{t.sjcws}</DescsItem>
        <DescsItem label="医疗机构类型">{t.hclass}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '其他医疗机构': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem span={1} label="名称">
          {t.name}
        </DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="在职职工人数">{t.zzzgrs}</DescsItem>
        <DescsItem label="编制床位数">{t.bzcws}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '大学': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="在校人数">{t.totalstude}</DescsItem>
        <DescsItem label="教职工人数">{t.totalfacul}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '高中': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="在校人数">{t.totalstude}</DescsItem>
        <DescsItem label="教职工人数">{t.totalfacul}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '小学': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="在校人数">{t.totalstude}</DescsItem>
        <DescsItem label="教职工人数">{t.totalfacul}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '幼儿园': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="在校人数">{t.totalstude}</DescsItem>
        <DescsItem label="教职工人数">{t.totalfacul}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '其他类型学校': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="在校人数">{t.totalstude}</DescsItem>
        <DescsItem label="教职工人数">{t.totalfacul}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '初中': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="在校人数">{t.totalstude}</DescsItem>
        <DescsItem label="教职工人数">{t.totalfacul}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '防火瞭望塔': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="负责人">{t.principalperson}</DescsItem>
        <DescsItem label="联系电话">{t.principalpersontel}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="人员数量">{t.populationnum}</DescsItem>
        <DescsItem label="其他信息">{t.otherinfo}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '地震及火山监测台': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地址">{t.customsaddress}</DescsItem>
        <DescsItem label="高程">{t.elevation}</DescsItem>
        <DescsItem label="省名称">{t.province}</DescsItem>
        <DescsItem label="市名称">{t.city}</DescsItem>
        <DescsItem label="区/县名称">{t.county}</DescsItem>
        <DescsItem label="乡/镇名称">{t.town}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="归属">{t.customs}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '塔吊': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="工地名称">{t.name}</DescsItem>
        <DescsItem label="塔吊公司">{t.installationunit}</DescsItem>
        <DescsItem label="建筑公司">{t.buildinguser}</DescsItem>
        <DescsItem label="责任人">{t.principalpersontel}</DescsItem>
        <DescsItem label="塔吊名称">{t.facilityname}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '通讯基站': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="基站名称">{t.name}</DescsItem>
        <DescsItem label="现场责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="监管单位">{t.regulators}</DescsItem>
        <DescsItem label="监管责任人联系方式">{t.regulatorstel}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="基站详细地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="主要存在问题">{t.problem}</DescsItem>
        <DescsItem label="主要应急处理措施">{t.problemprocessing}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '对讲机中继台': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.name}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="海拔">{t.altitude}</DescsItem>
        <DescsItem label="覆盖面">{t.coverage}</DescsItem>
        <DescsItem label="建成时间">{t.completiontime}</DescsItem>
        <DescsItem label="其他信息">{t.otherinfo}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '变电站': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="变电站名称">{t.name}</DescsItem>
        <DescsItem label="责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="监管单位">{t.regulators}</DescsItem>
        <DescsItem label="监管责任人联系方式">{t.regulatorstel}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="变电站详细地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="供电片区范围">{t.zone}</DescsItem>
        <DescsItem label="主要存在问题">{t.problem}</DescsItem>
        <DescsItem label="主要应急处理措施">{t.problemprocessing}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '船只': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="名称">{t.shipname}</DescsItem>
        <DescsItem label="曾用名">{t.usedname}</DescsItem>
        <DescsItem label="英文名">{t.shipengname}</DescsItem>
        <DescsItem label="船舶名称代码">{t.imo}</DescsItem>
        <DescsItem label="船长">{t.shiplength}</DescsItem>
        <DescsItem label="北斗ID">{t.beidouId}</DescsItem>
        <DescsItem label="九位码MMSI">{t.mmsi}</DescsItem>
        <DescsItem label="船舶联系人">{t.contact}</DescsItem>
        <DescsItem label="电话">{t.phone}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="船东">{t.shipowner}</DescsItem>
        <DescsItem label="建造地">{t.buildplace}</DescsItem>
        <DescsItem label="建造年份">{t.buildyear}</DescsItem>
        <DescsItem label="电台呼号">{t.callsign}</DescsItem>
        <DescsItem label="船检证书号">{t.certificateinspection}</DescsItem>
        <DescsItem label="国籍证书">{t.certificationnation}</DescsItem>
        <DescsItem label="完工日志">{t.finishtime}</DescsItem>
        <DescsItem label="捕捞许可证">{t.fishingpermitnumber}</DescsItem>
        <DescsItem label="标识码">{t.identificationcode}</DescsItem>
        <DescsItem label="小渔船">{t.issmallfishing}</DescsItem>
        <DescsItem label="作业区域">{t.jobregon}</DescsItem>
        <DescsItem label="工作方式">{t.jobway}</DescsItem>
        <DescsItem label="最后报位时间">{t.lastbittime}</DescsItem>
        <DescsItem label="挂靠岸台">{t.mounttheshore}</DescsItem>
        <DescsItem label="净吨位">{t.nettonnage}</DescsItem>
        <DescsItem label="组织机构名称">{t.orgname}</DescsItem>
        <DescsItem label="核载人数">{t.personscapacity}</DescsItem>
        <DescsItem label="归属地">{t.placebelonging}</DescsItem>
        <DescsItem label="检验登记号">{t.registrationmark}</DescsItem>
        <DescsItem label="卫星电话号码">{t.satellitephone}</DescsItem>
        <DescsItem label="船舶类别">{t.shipcategory}</DescsItem>
        <DescsItem label="船型深">{t.shipdepth}</DescsItem>
        <DescsItem label="船体材料">{t.shipmaterial}</DescsItem>
        <DescsItem label="船港籍">{t.shipport}</DescsItem>
        <DescsItem label="船型宽">{t.shipwidth}</DescsItem>
        <DescsItem label="功率">{t.shippower}</DescsItem>
        <DescsItem label="终端类型">{t.terminaltype}</DescsItem>
        <DescsItem label="总吨位">{t.totaltonnage}</DescsItem>
        <DescsItem label="渔船类型">{t.type}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '天然气': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="天然气站名称">{t.name}</DescsItem>
        <DescsItem label="责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="监管单位">{t.regulators}</DescsItem>
        <DescsItem label="监管责任人联系方式">{t.regulatorstel}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="天然气站详细地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="供气范围">{t.zone}</DescsItem>
        <DescsItem label="规模">{t.modle}</DescsItem>
        <DescsItem label="主要存在问题">{t.problem}</DescsItem>
        <DescsItem label="主要应急处理措施">{t.problemprocessing}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '水厂': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="水厂名称">{t.name}</DescsItem>
        <DescsItem label="责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="监管单位">{t.regulators}</DescsItem>
        <DescsItem label="监管责任人联系方式">{t.regulatorstel}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="水厂详细地址">{t.address}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="规模">{t.modle}</DescsItem>
        <DescsItem label="主要存在问题">{t.problem}</DescsItem>
        <DescsItem label="主要应急处理措施">{t.problemprocessing}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '生产场所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="企业名称">{t.unitname}</DescsItem>
        <DescsItem label="CAS号">{t.casnum}</DescsItem>
        <DescsItem label="统一社会信用代码">{t.socialcode}</DescsItem>
        <DescsItem label="经办人">{t.agent}</DescsItem>
        <DescsItem label="经办人身份证号">{t.agentnum}</DescsItem>
        <DescsItem label="负责人">{t.chargeperson}</DescsItem>
        <DescsItem label="企业主要负责人电话">{t.chargepersontel}</DescsItem>
        <DescsItem label="企业安全负责人姓名">{t.securityperson}</DescsItem>
        <DescsItem label="企业安全负责人电话">{t.securitypersontel}</DescsItem>
        <DescsItem label="联系人姓名">{t.contactsman}</DescsItem>
        <DescsItem label="联系人电话">{t.contactsmantel}</DescsItem>
        <DescsItem label="联系人身份证号">{t.contactsmannum}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="化学品名称">{t.name}</DescsItem>
        <DescsItem label="用途">{t.purpose}</DescsItem>
        <DescsItem label="最大储存量">{t.maxstorage}</DescsItem>
        <DescsItem label="年储存量">{t.yearsstorage}</DescsItem>
        <DescsItem label="安全风险">{t.securityrisk}</DescsItem>
        <DescsItem label="法人">{t.legalperson}</DescsItem>
        <DescsItem label="法定代表人身份证号">{t.legalpersonnum}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '高危存储企业': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="企业名称">{t.unitname}</DescsItem>
        <DescsItem label="CAS号">{t.casnum}</DescsItem>
        <DescsItem label="统一社会信用代码">{t.socialcode}</DescsItem>
        <DescsItem label="经办人">{t.agent}</DescsItem>
        <DescsItem label="经办人身份证号">{t.agentnum}</DescsItem>
        <DescsItem label="负责人">{t.chargeperson}</DescsItem>
        <DescsItem label="企业主要负责人电话">{t.chargepersontel}</DescsItem>
        <DescsItem label="企业安全负责人姓名">{t.securityperson}</DescsItem>
        <DescsItem label="企业安全负责人电话">{t.securitypersontel}</DescsItem>
        <DescsItem label="联系人姓名">{t.contactsman}</DescsItem>
        <DescsItem label="联系人电话">{t.contactsmantel}</DescsItem>
        <DescsItem label="联系人身份证号">{t.contactsmannum}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="化学品名称">{t.name}</DescsItem>
        <DescsItem label="用途">{t.purpose}</DescsItem>
        <DescsItem label="最大储存量">{t.maxstorage}</DescsItem>
        <DescsItem label="年储存量">{t.yearsstorage}</DescsItem>
        <DescsItem label="安全风险">{t.securityrisk}</DescsItem>
        <DescsItem label="法人">{t.legalperson}</DescsItem>
        <DescsItem label="法定代表人身份证号">{t.legalpersonnum}</DescsItem>
        <DescsItem label="详细地址">{t.address}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '加油站': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="加油站名称">{t.name}</DescsItem>
        <DescsItem label="类型">{t.type}</DescsItem>
        <DescsItem label="责任人">{t.principalperson}</DescsItem>
        <DescsItem label="责任人联系方式">{t.principalpersontel}</DescsItem>
        <DescsItem label="监管单位">{t.regulators}</DescsItem>
        <DescsItem label="监管责任人联系方式">{t.regulatorstel}</DescsItem>
        <DescsItem label="主要存在问题">{t.problem}</DescsItem>
        <DescsItem label="主要应急处理措施">{t.problemprocessing}</DescsItem>
        <DescsItem label="油站储存量">{t.storage}</DescsItem>
        <DescsItem label="地市级">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="加油站详细地址">{t.address}</DescsItem>
        <DescsItem label="存储类型">{t.storagetype}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '易燃经营场所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="企业名称">{t.unitname}</DescsItem>
        <DescsItem label="CAS号">{t.casnum}</DescsItem>
        <DescsItem label="统一社会信用代码">{t.creditcode}</DescsItem>
        <DescsItem label="经办人">{t.operatorperson}</DescsItem>
        <DescsItem label="经办人证件号">{t.operatorpersonnum}</DescsItem>
        <DescsItem label="企业主要负责人">{t.mainperson}</DescsItem>
        <DescsItem label="企业主要负责人电话">{t.mainpersontel}</DescsItem>
        <DescsItem label="企业主要负责人证件号">{t.mainpersonnum}</DescsItem>
        <DescsItem label="企业安全负责人">{t.securityperson}</DescsItem>
        <DescsItem label="企业安全负责人电话">{t.securitypersontel}</DescsItem>
        <DescsItem label="企业安全负责人证件号">{t.securitypersonnum}</DescsItem>
        <DescsItem label="联系人姓名">{t.personname}</DescsItem>
        <DescsItem label="联系人电话">{t.persontel}</DescsItem>
        <DescsItem label="联系人证件号">{t.personnum}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="企业类型">{t.type}</DescsItem>
        <DescsItem label="法定代表人">{t.legalperson}</DescsItem>
        <DescsItem label="法定代表证件号">{t.legalpersonnum}</DescsItem>
        <DescsItem label="周边单位或小区">{t.surroundings}</DescsItem>
        <DescsItem label="化学品储量">{t.reserves}</DescsItem>
        <DescsItem label="行业大类">{t.trademax}</DescsItem>
        <DescsItem label="行业小类">{t.trademin}</DescsItem>
        <DescsItem label="行业监管部门">{t.regulators}</DescsItem>
        <DescsItem label="成立时间">{t.establishmenttime}</DescsItem>
        <DescsItem label="许可证编号">{t.licensenum}</DescsItem>
        <DescsItem label="许可证有效截至时间">{t.cuttime}</DescsItem>
        <DescsItem label="许可证有效开始时间">{t.starttime}</DescsItem>
        <DescsItem label="用途">{t.purpose}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="职工人数">{t.staffnum}</DescsItem>
        <DescsItem label="罐区总数量">{t.tankfarmnum}</DescsItem>
        <DescsItem label="罐区总面积">{t.tankfarmarea}</DescsItem>
        <DescsItem label="仓库总数量">{t.warehousenum}</DescsItem>
        <DescsItem label="仓库总面积">{t.warehousearea}</DescsItem>
        <DescsItem label="汽油月销售">{t.gasolinemsales}</DescsItem>
        <DescsItem label="汽油年销售">{t.gasolineysales}</DescsItem>
        <DescsItem label="柴油年销售">{t.dieseloilysales}</DescsItem>
        <DescsItem label="柴油月销售">{t.dieseloilmsales}</DescsItem>
        <DescsItem label="运营主体">{t.operatemaster}</DescsItem>
        <DescsItem label="运营主体其性质">{t.operatenature}</DescsItem>
        <DescsItem label="加油站级别">{t.stationlevel}</DescsItem>
        <DescsItem label="是否构成重大危险源">{t.isdangerous}</DescsItem>
        <DescsItem label="生产流程简介">{t.workflow}</DescsItem>
        <DescsItem label="重大危险源">{t.bigsource}</DescsItem>
        <DescsItem label="重大危险源等级">{t.sourcegrade}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '危化品使用场所': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="企业名称">{t.unitname}</DescsItem>
        <DescsItem label="CAS号">{t.casnum}</DescsItem>
        <DescsItem label="统一社会信用代码">{t.socialcode}</DescsItem>
        <DescsItem label="经办人">{t.agent}</DescsItem>
        <DescsItem label="经办人身份证号">{t.agentnum}</DescsItem>
        <DescsItem label="负责人">{t.chargeperson}</DescsItem>
        <DescsItem label="企业主要负责人电话">{t.chargepersontel}</DescsItem>
        <DescsItem label="企业安全负责人姓名">{t.securityperson}</DescsItem>
        <DescsItem label="企业安全负责人电话">{t.securitypersontel}</DescsItem>
        <DescsItem label="联系人姓名">{t.contactsman}</DescsItem>
        <DescsItem label="联系人电话">{t.contactsmantel}</DescsItem>
        <DescsItem label="联系人身份证号">{t.contactsmannum}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="化学品名称">{t.name}</DescsItem>
        <DescsItem label="所在位置">{t.address}</DescsItem>
        <DescsItem label="用途">{t.purpose}</DescsItem>
        <DescsItem label="最大储存量">{t.maxstorage}</DescsItem>
        <DescsItem label="年储存量">{t.yearsstorage}</DescsItem>
        <DescsItem label="安全风险">{t.securityrisk}</DescsItem>
        <DescsItem label="法人">{t.legalperson}</DescsItem>
        <DescsItem label="法定代表人身份证号">{t.legalpersonnum}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '非煤矿山安全生产': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="矿山名称">{t.ksmc}</DescsItem>
        <DescsItem label="联系电话">{t.lxdh}</DescsItem>
        <DescsItem label="联系人">{t.lxr}</DescsItem>
        <DescsItem label="单位负责人">{t.dwfzr}</DescsItem>
        <DescsItem label="单位名称">{t.dwmc}</DescsItem>
        <DescsItem label="单位地址">{t.dwdz}</DescsItem>
        <DescsItem label="开采方式">{t.kcfs}</DescsItem>
        <DescsItem label="类型">{t.lx}</DescsItem>
        <DescsItem label="生产能力">{t.scnl}</DescsItem>
        <DescsItem label="省">{t.province}</DescsItem>
        <DescsItem label="市">{t.city}</DescsItem>
        <DescsItem label="区县">{t.county}</DescsItem>
        <DescsItem label="乡镇">{t.town}</DescsItem>
        <DescsItem label="采矿证号">{t.ckzh}</DescsItem>
        <DescsItem label="采矿证有效期">{t.ckzyxq}</DescsItem>
        <DescsItem label="登记编号">{t.djbh}</DescsItem>
        <DescsItem label="工商营业执照编号">{t.gsyyzzbh}</DescsItem>
        <DescsItem label="工商登记日期">{t.gsdjrq}</DescsItem>
        <DescsItem label="行政许可决定书文号">{t.xzxkjdswh}</DescsItem>
        <DescsItem label="有效期">{t.yxq}</DescsItem>
        <DescsItem label="取证单位类型">{t.qzdwlx}</DescsItem>
        <DescsItem label="受理时间">{t.slsj1}</DescsItem>
        <DescsItem label="经济类型">{t.jjlx}</DescsItem>
        <DescsItem label="许可范围">{t.xkfw}</DescsItem>
        <DescsItem label="备注">{t.bz}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '非煤矿山安全生产1': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="矿山名称">{t.ksmc}</DescsItem>
        <DescsItem label="企业联系人">{t.qylxr}</DescsItem>
        <DescsItem label="企业联系电话">{t.qylxdh}</DescsItem>
        <DescsItem label="行政区划">{t.xzqh}</DescsItem>
        <DescsItem label="开采方式（露天或地下）">{t.kcfs}</DescsItem>
        <DescsItem label="矿种">{t.kz}</DescsItem>
        <DescsItem label="采矿证有效期">{t.ckzyxq}</DescsItem>
        <DescsItem label="安全生产许可证有效期">{t.aqscxkzyxq}</DescsItem>
        <DescsItem label="状态">{t.zt}</DescsItem>
        <DescsItem label="采矿证生产能力或尾矿库等别">{t.ckzscnl}</DescsItem>
        <DescsItem label="规模">{t.gm}</DescsItem>
        <DescsItem label="双重预防机制建设情况">{t.scyfjzjsqk}</DescsItem>
        <DescsItem label="隐患条数">{t.yhts}</DescsItem>
        <DescsItem label="其中重大隐患条数">{t.qzzdyhts}</DescsItem>
        <DescsItem label="备注">{t.bz}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '运输企业': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="企业名称">{t.unitname}</DescsItem>
        <DescsItem label="CAS号">{t.casnum}</DescsItem>
        <DescsItem label="统一社会信用代码">{t.socialcode}</DescsItem>
        <DescsItem label="经办人">{t.agent}</DescsItem>
        <DescsItem label="经办人身份证号">{t.agentnum}</DescsItem>
        <DescsItem label="法人">{t.legalperson}</DescsItem>
        <DescsItem label="法定代表人身份证号">{t.legalpersonnum}</DescsItem>
        <DescsItem label="负责人">{t.chargeperson}</DescsItem>
        <DescsItem label="企业主要负责人电话">{t.chargepersontel}</DescsItem>
        <DescsItem label="企业安全负责人姓名">{t.securityperson}</DescsItem>
        <DescsItem label="企业安全负责人电话">{t.securitypersontel}</DescsItem>
        <DescsItem label="联系人姓名">{t.contactsman}</DescsItem>
        <DescsItem label="联系人电话">{t.contactsmantel}</DescsItem>
        <DescsItem label="联系人身份证号">{t.contactsmannum}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="所在位置">{t.address}</DescsItem>
        <DescsItem label="化学品名称">{t.name}</DescsItem>
        <DescsItem label="用途">{t.purpose}</DescsItem>
        <DescsItem label="最大储存量">{t.maxstorage}</DescsItem>
        <DescsItem label="年储存量">{t.yearsstorage}</DescsItem>
        <DescsItem label="安全风险">{t.securityrisk}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '危险品运输车辆': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="车牌号码">{t.branum}</DescsItem>
        <DescsItem label="经营许可证号">{t.pernum}</DescsItem>
        <DescsItem label="道路运输证字">{t.traword}</DescsItem>
        <DescsItem label="车籍地">{t.code}</DescsItem>
        <DescsItem label="业户名称">{t.clitname}</DescsItem>
        <DescsItem label="运输性质">{t.trancha}</DescsItem>
        <DescsItem label="经营范围">{t.bnscope}</DescsItem>
        <DescsItem label="车辆类型">{t.vectype}</DescsItem>
        <DescsItem label="车辆类型等级">{t.pasgrade}</DescsItem>
        <DescsItem label="客车类型">{t.bustyperating}</DescsItem>
        <DescsItem label="班线类型">{t.lintype}</DescsItem>
        <DescsItem label="车长">{t.veclength}</DescsItem>
        <DescsItem label="车高">{t.vechigh}</DescsItem>
        <DescsItem label="车宽">{t.vecwide}</DescsItem>
        <DescsItem label="车辆颜色">{t.bracolor}</DescsItem>
        <DescsItem label="车身颜色">{t.veccolor}</DescsItem>
        <DescsItem label="核定吨座位">{t.chenum}</DescsItem>
        <DescsItem label="计征吨座位">{t.colnum}</DescsItem>
        <DescsItem label="核定乘员数">{t.chepsnnum}</DescsItem>
        <DescsItem label="核定载质量">{t.chelodmass}</DescsItem>
        <DescsItem label="核定座位">{t.cheseats}</DescsItem>
        <DescsItem label="经营许可证字">{t.perword}</DescsItem>
        <DescsItem label="经营方式">{t.bustype}</DescsItem>
        <DescsItem label="注册（登记）日期">{t.registrationdate}</DescsItem>
        <DescsItem label="发证机构">{t.depcode}</DescsItem>
        <DescsItem label="核发机构">{t.gradepcode}</DescsItem>
        <DescsItem label="核发日期">{t.gradate}</DescsItem>
        <DescsItem label="营运状态">{t.vecbusstatus}</DescsItem>
        <DescsItem label="本次年审日期">{t.chetodate}</DescsItem>
        <DescsItem label="道路运输证号">{t.trano}</DescsItem>
        <DescsItem label="道路运输证有效期起">{t.stadate}</DescsItem>
        <DescsItem label="道路运输证有效期止">{t.enddate}</DescsItem>
        <DescsItem label="车辆类型">{t.vecentype}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '旅游客车运输车辆': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="车牌号码">{t.branum}</DescsItem>
        <DescsItem label="经营许可证号">{t.pernum}</DescsItem>
        <DescsItem label="道路运输证字">{t.traword}</DescsItem>
        <DescsItem label="车籍地">{t.code}</DescsItem>
        <DescsItem label="业户名称">{t.clitname}</DescsItem>
        <DescsItem label="运输性质">{t.trancha}</DescsItem>
        <DescsItem label="经营范围">{t.bnscope}</DescsItem>
        <DescsItem label="车辆类型">{t.vectype}</DescsItem>
        <DescsItem label="车辆类型等级">{t.pasgrade}</DescsItem>
        <DescsItem label="客车类型">{t.bustyperating}</DescsItem>
        <DescsItem label="班线类型">{t.lintype}</DescsItem>
        <DescsItem label="车长">{t.veclength}</DescsItem>
        <DescsItem label="车高">{t.vechigh}</DescsItem>
        <DescsItem label="车宽">{t.vecwide}</DescsItem>
        <DescsItem label="车辆颜色">{t.bracolor}</DescsItem>
        <DescsItem label="车身颜色">{t.veccolor}</DescsItem>
        <DescsItem label="核定吨座位">{t.chenum}</DescsItem>
        <DescsItem label="计征吨座位">{t.colnum}</DescsItem>
        <DescsItem label="核定乘员数">{t.chepsnnum}</DescsItem>
        <DescsItem label="核定载质量">{t.chelodmass}</DescsItem>
        <DescsItem label="核定座位">{t.cheseats}</DescsItem>
        <DescsItem label="经营许可证字">{t.perword}</DescsItem>
        <DescsItem label="经营方式">{t.bustype}</DescsItem>
        <DescsItem label="注册（登记）日期">{t.registrationdate}</DescsItem>
        <DescsItem label="发证机构">{t.depcode}</DescsItem>
        <DescsItem label="核发机构">{t.gradepcode}</DescsItem>
        <DescsItem label="核发日期">{t.gradate}</DescsItem>
        <DescsItem label="营运状态">{t.vecbusstatus}</DescsItem>
        <DescsItem label="本次年审日期">{t.chetodate}</DescsItem>
        <DescsItem label="道路运输证号">{t.trano}</DescsItem>
        <DescsItem label="道路运输证有效期起">{t.stadate}</DescsItem>
        <DescsItem label="道路运输证有效期止">{t.enddate}</DescsItem>
        <DescsItem label="车辆类型">{t.vecentype}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '安全生产企业': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="企业名称">{t.name}</DescsItem>
        <DescsItem label="法定代表人">{t.legalperson}</DescsItem>
        <DescsItem label="法定代表证件号">{t.legalpersonnum}</DescsItem>
        <DescsItem label="经办人">{t.operatorperson}</DescsItem>
        <DescsItem label="经办人证件号">{t.operatorpersonnum}</DescsItem>
        <DescsItem label="企业主要负责人">{t.mainperson}</DescsItem>
        <DescsItem label="企业主要负责人电话">{t.mainpersontel}</DescsItem>
        <DescsItem label="企业主要负责人证件号">{t.mainpersonnum}</DescsItem>
        <DescsItem label="企业安全负责人">{t.securityperson}</DescsItem>
        <DescsItem label="企业安全负责人电话">{t.securitypersontel}</DescsItem>
        <DescsItem label="企业安全负责人证件号">{t.securitypersonnum}</DescsItem>
        <DescsItem label="联系人姓名">{t.personname}</DescsItem>
        <DescsItem label="联系人电话">{t.persontel}</DescsItem>
        <DescsItem label="联系人证件号">{t.personnum}</DescsItem>
        <DescsItem label="企业类型">{t.type}</DescsItem>
        <DescsItem label="地级市">{t.province}</DescsItem>
        <DescsItem label="区/县级">{t.area}</DescsItem>
        <DescsItem label="乡镇/街道">{t.town}</DescsItem>
        <DescsItem label="居委会/行政村">{t.villages}</DescsItem>
        <DescsItem label="行业大类">{t.trademax}</DescsItem>
        <DescsItem label="行业小类">{t.trademin}</DescsItem>
        <DescsItem label="行业监管部门">{t.regulators}</DescsItem>
        <DescsItem label="成立时间">{t.establishmenttime}</DescsItem>
        <DescsItem label="许可证编号">{t.licensenum}</DescsItem>
        <DescsItem label="许可证有效截至时间">{t.cuttime}</DescsItem>
        <DescsItem label="许可证有效开始时间">{t.starttime}</DescsItem>
        <DescsItem label="用途">{t.purpose}</DescsItem>
        <DescsItem label="地址">{t.address}</DescsItem>
        <DescsItem label="职工人数">{t.staffnum}</DescsItem>
        <DescsItem label="罐区总数量">{t.tankfarmnum}</DescsItem>
        <DescsItem label="罐区总面积">{t.tankfarmarea}</DescsItem>
        <DescsItem label="仓库总数量">{t.warehousenum}</DescsItem>
        <DescsItem label="仓库总面积">{t.warehousearea}</DescsItem>
        <DescsItem label="汽油月销售">{t.gasolinemsales}</DescsItem>
        <DescsItem label="汽油年销售">{t.gasolineysales}</DescsItem>
        <DescsItem label="柴油年销售">{t.dieseloilysales}</DescsItem>
        <DescsItem label="柴油月销售">{t.dieseloilmsales}</DescsItem>
        <DescsItem label="运营主体">{t.operatemaster}</DescsItem>
        <DescsItem label="运营主体其性质">{t.operatenature}</DescsItem>
        <DescsItem label="加油站级别">{t.stationlevel}</DescsItem>
        <DescsItem label="是否构成重大危险源">{t.isdangerous}</DescsItem>
        <DescsItem label="生产流程简介">{t.workflow}</DescsItem>
        <DescsItem label="周边单位或小区">{t.surroundings}</DescsItem>
        <DescsItem label="化学品储量">{t.reserves}</DescsItem>
        <DescsItem label="重大危险源">{t.bigsource}</DescsItem>
        <DescsItem label="重大危险源等级">{t.sourcegrade}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '铁路环境安全隐患点': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="高铁线路">{t.railLine}</DescsItem>
        <DescsItem label="铁路单位">{t.railUnit}</DescsItem>
        <DescsItem label="铁路里程">{t.railMileage}</DescsItem>
        <DescsItem label="铁路地界内">{t.railLimits}</DescsItem>
        <DescsItem label="铁路联系人及电话号码">{t.railphone}</DescsItem>
        <DescsItem label="县/区">{t.counties}</DescsItem>
        <DescsItem label="村/社区">{t.community}</DescsItem>
        <DescsItem label="安保区内">{t.inzone}</DescsItem>
        <DescsItem label="安保区外">{t.outzone}</DescsItem>
        <DescsItem label="问题类别">{t.questionType}</DescsItem>
        <DescsItem label="问题概况">{t.problemSituation}</DescsItem>
        <DescsItem label="整治措施">{t.regulationMeasure}</DescsItem>
        <DescsItem label="整治情况">{t.regulationCase}</DescsItem>
        <DescsItem label="整治牵头单位">{t.regulationUnit}</DescsItem>
        <DescsItem label="整治部门">{t.regulationDepartment}</DescsItem>
        <DescsItem label="整治责任人">{t.regulationResponsible}</DescsItem>
        <DescsItem label="区域">{t.area}</DescsItem>
        <DescsItem label="行政区划">{t.code}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '建筑工地': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="所在位置">
          {`${t.szsx}`}
          {`${t.szxz}`}
          {`${t.jtdd}`}
        </DescsItem>
        <DescsItem label="责任人">{t.zrr}</DescsItem>
        <DescsItem label="电话">{t.zrrsjhm}</DescsItem>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>
        <DescsItem label="整改责任单位">{t.zgzrdw}</DescsItem>
        <DescsItem label="整改措施">{t.zgcs}</DescsItem>
        <DescsItem label="整改进展情况">{t.zgjzqk}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '易淹易涝点（区）': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="所在位置">
          {`${t.szsx}`}
          {`${t.szxz}`}
          {`${t.jtdd}`}
        </DescsItem>
        <DescsItem label="类型">{t.lx}</DescsItem>
        <DescsItem label="影响户数">{t.yxhs}</DescsItem>
        <DescsItem label="影响人数">{t.yxrs}</DescsItem>
        <DescsItem label="转移安置地点">{t.zyazdd}</DescsItem>
        <DescsItem label="转移责任人">{t.zrr}</DescsItem>
        <DescsItem label="电话">{t.zrrsjhm}</DescsItem>
        <DescsItem label="主要对策/整改措施">{t.zgcs}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '渔港': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="渔港名称">{t.ygmc}</DescsItem>
        <DescsItem label="所在位置">
          {t.szsx}
          {t.szxz}
          {t.szxzc}
        </DescsItem>
        <DescsItem label="港口管理单位">{t.gkgldw}</DescsItem>
        <DescsItem label="避风船只容量">{t.bfczrl}</DescsItem>
        <DescsItem label="抗风能力">{t.kfnl}</DescsItem>
        <DescsItem label="责任人">{t.zrr}</DescsItem>
        <DescsItem label="电话">{t.zrrsjhm}</DescsItem>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>
        <DescsItem label="执法检查的单位人员电话">{t.zfjcddwmc}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '渔船': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="船号">{t.ycbh}</DescsItem>
        <DescsItem label="船主">{t.czxm}</DescsItem>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>

        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '旅游景区景点': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="景区名称">{t.jqjdmc}</DescsItem>
        <DescsItem label="所在位置">
          {t.szsx}
          {t.szxz}
          {t.szxzc}
        </DescsItem>
        <DescsItem label="管理单位">{t.gldw}</DescsItem>
        <DescsItem label="面积">{t.jqmj}</DescsItem>
        <DescsItem label="责任人">{t.zrr}</DescsItem>
        <DescsItem label="电话">{t.zrrsjhm}</DescsItem>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '危房': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="所在位置">
          {t.szsx}
          {t.szxz}
          {t.szxzc}
          {t.jtdz}
        </DescsItem>
        <DescsItem label="户主姓名">{t.hzxm}</DescsItem>
        <DescsItem label="联系方式">{t.hzlxfs}</DescsItem>

        <DescsItem label="责任人">{t.zrr}</DescsItem>
        <DescsItem label="电话">{t.zrrsjhm}</DescsItem>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '漫水桥（路）': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem label="所在位置">
          {t.szsx}
          {t.szxz}
          {t.szxzc}
          {t.jtdd}
        </DescsItem>
        <DescsItem label="类型">{t.lx}</DescsItem>
        <DescsItem spa={2} label="监管单位">
          {t.jgdw}
        </DescsItem>

        <DescsItem label="责任人">{t.zgzrdw}</DescsItem>
        <DescsItem label="电话">{t.zgzrrsjhm}</DescsItem>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>
        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
  '地质灾害隐患点': ({ data: t, longitude, latitude }) => {
    ('阳江镇题榜村委会封岭村边坡崩塌');
    return () => (
      <ElTabs>
        <ElTabPane label="基础信息">
          <Descs border column={2}>
            <DescsItem label="地灾名称">{t.name}</DescsItem>
            <DescsItem label="灾害规模">{t.zqgm}</DescsItem>
            <DescsItem label="防灾责任人">{t.fzname}</DescsItem>
            <DescsItem label="电话">{t.fzdh}</DescsItem>
            <DescsItem label="险情等级">{t.xqdj}</DescsItem>
            <DescsItem label="巡防员">{t.xfname}</DescsItem>
            <DescsItem label="电话">{t.xfdh}</DescsItem>
            <DescsItem label="威胁人口">{t.wxrk}</DescsItem>
            <DescsItem label="监测员">{t.jcname}</DescsItem>
            <DescsItem label="电话">{t.jcdh}</DescsItem>
            <DescsItem label="威胁财产">{t.zhlx || '-'}</DescsItem>

            <DescsItem label="所在位置">
              {t.szsx}
              {t.szxz}
              {t.szxzc}
              {t.jtdd}
            </DescsItem>
            <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
            <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
          </Descs>
        </ElTabPane>

      </ElTabs>
    );
  },

  '水库': ({ data: t, longitude, latitude }) => {
    return () => (
      <Descs border column={2}>
        <DescsItem span={1} label="水库名称">
          {t.gcmc}
        </DescsItem>

        <DescsItem label="所在位置">
          {t.szsx}
          {t.szxz}
          {t.szxzc}
          {t.jtdz}
        </DescsItem>

        <DescsItem label="责任人">{t.zrr}</DescsItem>
        <DescsItem label="电话">{t.zrrsjhm}</DescsItem>
        <DescsItem label="主要问题及隐患">{t.dqzywtjyh}</DescsItem>

        <DescsItem label="经度">{longitude?.toFixed(5)}</DescsItem>
        <DescsItem label="纬度">{latitude?.toFixed(5)}</DescsItem>
      </Descs>
    );
  },
};
export default ResourceMap;
