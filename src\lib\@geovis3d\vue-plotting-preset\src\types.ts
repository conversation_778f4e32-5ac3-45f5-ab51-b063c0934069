export interface ApiRequest<T = any> {
  code: string;
  data?: T;
  message: string;
}

/**
 * 标绘列表项
 */
export interface PlotAssetModel {
  dir?: string;
  fileName?: string;
  id?: string;
  isDirectory?: boolean;
  children?: PlotAssetModel[];
}

/**
 * 标绘列表项
 */
export interface PlotSimpleLayer {
  appCode?: string;
  createBy?: string;
  createTime?: string;
  fileDir?: string;
  id?: string;
  idx?: number;
  key?: string;
  parentId?: string;
  remark?: string;
  updateBy?: string;
  updateTime?: string;
}

/**
 * 标绘列表项
 */
export interface PlotDetailLayer extends PlotSimpleLayer {
  value?: string;
}
