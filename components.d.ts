/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BasicCard: typeof import('./src/components/basic-card/basic-card.vue')['default']
    BasicOrComponent: typeof import('./src/components/basic-or-component/basic-or-component.vue')['default']
    BottomNav: typeof import('./src/layout/bottom-nav.vue')['default']
    DatetimePicker: typeof import('./src/components/datetime-picker/index.vue')['default']
    DragCard: typeof import('./src/components/drag/drag-card.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FlexCard: typeof import('./src/components/flex-card/index.vue')['default']
    HeaderTitle1: typeof import('./src/components/header-title/header-title1.vue')['default']
    HeaderTitle2: typeof import('./src/components/header-title/header-title2.vue')['default']
    Layout: typeof import('./src/layout/layout.vue')['default']
    LayoutContextmenu: typeof import('./src/layout/layout-contextmenu.vue')['default']
    LayoutFooter: typeof import('./src/layout/layout-footer.vue')['default']
    LayoutHeader: typeof import('./src/layout/layout-header.vue')['default']
    LayoutLeftPanel: typeof import('./src/layout/layout-left-panel.vue')['default']
    LayoutPanelButton: typeof import('./src/layout/layout-panel-button.vue')['default']
    LayoutRightPanel: typeof import('./src/layout/layout-right-panel.vue')['default']
    LayoutScaffold: typeof import('./src/layout/layout-scaffold.vue')['default']
    ListScrollView: typeof import('./src/components/scroll-view/list-scroll-view.vue')['default']
    LocatedPopper: typeof import('./src/components/located-popper/located-popper.vue')['default']
    LocatedPopper1: typeof import('./src/components/located-popper/located-popper1.vue')['default']
    MeasureAngle: typeof import('./src/components/measure-tools/measure-angle.vue')['default']
    MeasureArea: typeof import('./src/components/measure-tools/measure-area.vue')['default']
    MeasureDistance: typeof import('./src/components/measure-tools/measure-distance.vue')['default']
    MeasureDistanceClamp: typeof import('./src/components/measure-tools/measure-distance-clamp.vue')['default']
    MeasureHeight: typeof import('./src/components/measure-tools/measure-height.vue')['default']
    MeasureTools: typeof import('./src/components/measure-tools/index.vue')['default']
    NavigationSearch: typeof import('./src/components/amap/search-tool/navigation-search.vue')['default']
    PoiDetail: typeof import('./src/components/amap/search-tool/poi-detail.vue')['default']
    PoiSearch: typeof import('./src/components/amap/search-tool/poi-search.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchTool: typeof import('./src/components/amap/search-tool/search-tool.vue')['default']
    TimelinePlayer: typeof import('./src/layout/timeline-player.vue')['default']
    TimeServiceLayerTool: typeof import('./src/components/time-service-layer/time-service-layer-tool.vue')['default']
    UserSetting: typeof import('./src/layout/user-setting.vue')['default']
    VueEcharts: typeof import('vue-echarts')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
