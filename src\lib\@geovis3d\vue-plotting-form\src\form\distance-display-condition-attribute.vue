<!-- DistanceDisplayConditionAttribute -->
<script lang="ts" setup>
import type { DistanceDisplayConditionSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'DistanceDisplayConditionAttribute' });

const props = defineProps<{
  modelValue?: DistanceDisplayConditionSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: DistanceDisplayConditionSerializateJSON): void;
}>();

const model = ref<DistanceDisplayConditionSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div>
    <el-form-item :label="label" />
    <NumberAttribute v-model="model.near" label="最小距离" :max="model.far" :precision="2" />
    <NumberAttribute v-model="model.far" label="最大距离" :min="model.near" :precision="2" />
  </div>
</template>
