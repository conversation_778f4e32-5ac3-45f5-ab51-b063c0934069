<!-- CircleWaveMaterialPropertyAttribute 属性编辑 -->
<script lang="ts" setup>
import type { CircleWaveMaterialPropertySerializateJSON } from '../@geovis3d/plotting';
import ColorAttribute from '../color-attribute.vue';
import { useShallowBinding } from '../hooks';

import NumberAttribute from '../number-attribute.vue';

defineOptions({ name: 'CircleWaveMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: CircleWaveMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: CircleWaveMaterialPropertySerializateJSON): void;
}>();

const model = ref<CircleWaveMaterialPropertySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <NumberAttribute v-model="model.count" :min="1" :precision="0" label="圆环数量" />
  <ColorAttribute v-model="model.color" label="圆环颜色" />
</template>
