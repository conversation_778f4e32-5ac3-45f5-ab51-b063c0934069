<!-- 矢量数据属性弹窗 -->
<script lang="ts" setup>
import { useCzScreenSpaceAction, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'PropertiesPopperVector' });

const viewer = useCzViewer();

const properties = shallowRef<any>();
const position = shallowRef<Cesium.Cartesian3>();

const description = shallowRef<any>();

function escape2Html(str: any) {
  const arrEntities = { lt: '<', gt: '>', nbsp: ' ', amp: '&', quot: '"' };

  return str.replace(/&(lt|gt|nbsp|amp|quot);/gi, (all, t) => {
    return arrEntities[t];
  });
}

// 点选
useCzScreenSpaceAction(Cesium.ScreenSpaceEventType.LEFT_CLICK, async (context) => {
  const ray = viewer.value.camera.getPickRay(context.position);
  const cartesian = viewer.value.scene.pickPosition(context.position);
  if (!ray || !cartesian) {
    return;
  }
  const features = await viewer.value.imageryLayers.pickImageryLayerFeatures(ray, viewer.value.scene) ?? [];
  if (features.some((item: any) => item.imageryLayer.imageryProvider.url.includes('/wms'))) {
    return;
  }
  const propertiesList = features.map((item: any) => {
    return item?.properties || item?.data?.properties;
  });
  const _properties = propertiesList.find((properties: any) => {
    return !!Object.values(properties ?? {}).find((e: any) => Cesium.defined(e));
  });

  const _description = features.find((feature: any) => {
    return !!feature?.description;
  })?.description;

  properties.value = _properties;
  description.value = _description;
  position.value = cartesian;
});
</script>

<template>
  <located-popper1
    v-if="position && (properties || description)"
    :position="position"
    show-close
    header="属性信息"
    class="w-500px!"
    @close="position = undefined"
  >
    <el-scrollbar h="250px!" wrap-class="p-10px">
      <el-descriptions v-if="properties" :column="1" border>
        <el-descriptions-item v-for="(value, key) in properties" :key="key" :label="`${key}`">
          <span> {{ value }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <div v-else class="html-table">
        <div v-html="escape2Html(description || '')" />
      </div>
    </el-scrollbar>
  </located-popper1>
</template>

<style lang="scss" scoped>
.html-table {
  :deep() * {
    padding: 0;
    color: #fff;
    background: transparent;
  }

  :deep() .featureInfo {
    width: 100%;
    font-size: 16px;
  }
}
</style>
