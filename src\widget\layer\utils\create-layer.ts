import type { Rectangle } from 'cesium';
import type { LayerConfig, MVTConfig, UrlTemplateConfig, WMSConfig, WMTSConfig } from '../assets/types';
import type { LayerInfo } from './legacy';
import { Bd09MercatorTilingScheme, Gcj02MercatorTilingScheme } from '@x3d/all';
import { Cesium3DTileset, CesiumTerrainProvider, ImageryLayer, TileMapServiceImageryProvider, UrlTemplateImageryProvider, WebMapServiceImageryProvider, WebMapTileServiceImageryProvider } from 'cesium';
import MVTImageryProvider from 'mvt-imagery-provider';

export type LayerType = 'ImageryLayer' | 'TerrainProvider' | 'Cesium3DTileset' | string;

// 传入数据生成图层数据
export function createLayer(data: LayerInfo): { type: LayerType; value: any } | undefined {
  const config: LayerConfig = data?.config ?? {};

  const type = config?.type?.toLowerCase();
  if (!type) {
    return;
  }

  switch (type) {
    case 'urltemp':
      return createURLTemplateLayer(config);
    case 'tms':
      return createTMSLayer(config);
    case 'wmts':
      return createWMTSLayer(config);
    case 'wms':
      return createWMSLayer(config);
    case 'mvt':
      return createMVTLayer(config);
    case '倾斜摄影':
      return createTilesetLayer(config);

    case 'cesiumdem':
      return createTerrainLayer(config);
    default:
  }
}

function createResource(config: LayerConfig) {
  const egis = config.egis ?? {};
  let Authorization: string | undefined;
  // egis授权
  if (egis.username && egis.password) {
    Authorization = `Basic ${btoa(`${egis.username}:${egis.password}`)}`;
  }
  return new Cesium.Resource({
    url: config.layer?.url ?? '',
    headers: {
      ...Authorization ? { Authorization } : {},
    },
  });
}

function createRectangle(config: LayerConfig) {
  return config.rectangle?.length === 4 ? Cesium.Rectangle.fromDegrees(...config.rectangle) : Cesium.Rectangle.MAX_VALUE;
}

function createTilingScheme(tileMatrixSetID?: string, rectangle?: Rectangle) {
  // 瓦片投影
  const tilingScheme = tileMatrixSetID === 'EPSG:4326'
    ? new Cesium.GeographicTilingScheme({ rectangle })
    : tileMatrixSetID === 'EPSG:4490'
      ? new Cesium.WebMercatorTilingScheme({})
      : tileMatrixSetID === 'BD09'
        ? new Bd09MercatorTilingScheme({})
        : tileMatrixSetID === 'GCJO2'
          ? new Gcj02MercatorTilingScheme({})
          : new Cesium.WebMercatorTilingScheme({ });
  return tilingScheme;
}

function createWMSLayer(config: LayerConfig) {
  const layer = config.layer as WMSConfig;
  const resource = createResource(config);
  const rectangle = createRectangle(config);
  const tilingScheme = createTilingScheme(layer.tileMatrixSetID, rectangle);
  return {
    type: 'ImageryLayer',
    value: new ImageryLayer(
      new WebMapServiceImageryProvider({
        url: resource,
        minimumLevel: layer.minimumLevel,
        maximumLevel: layer.maximumLevel,
        tilingScheme,
        rectangle,
        layers: layer.layer!,
        tileWidth: layer.titleSize || 256,
        tileHeight: layer.titleSize || 256,
        parameters: {
          layer: layer.layer,
          format: layer.format,
        },
      }),
    ),
  };
}

function createWMTSLayer(config: LayerConfig) {
  const layer = config.layer as WMTSConfig;
  const resource = createResource(config);
  const rectangle = createRectangle(config);
  const tilingScheme = createTilingScheme(layer.tileMatrixSetID, rectangle);
  return {
    type: 'ImageryLayer',
    value: new ImageryLayer(
      new WebMapTileServiceImageryProvider({
        url: resource,
        minimumLevel: layer.minimumLevel,
        maximumLevel: layer.maximumLevel,
        tilingScheme,
        rectangle,
        style: layer.style || 'default',
        layer: layer.layer!,
        format: layer.format,
        tileMatrixSetID: layer.tileMatrixSetID || 'EPSG:4326',
        tileMatrixLabels: layer.tileMatrixSetID === 'EPSG:4326' ? [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21] : undefined,
        tileWidth: layer.titleSize || 256,
        tileHeight: layer.titleSize || 256,
      }),
    ),
  };
}

function createTMSLayer(config: LayerConfig) {
  const layer = config.layer as WMTSConfig;
  const resource = createResource(config);
  const rectangle = createRectangle(config);
  const tilingScheme = createTilingScheme(layer.tileMatrixSetID, rectangle);
  return {
    type: 'ImageryLayer',
    value: ImageryLayer.fromProviderAsync(
      TileMapServiceImageryProvider.fromUrl(resource, {
        maximumLevel: layer.maximumLevel,
        minimumLevel: layer.minimumLevel,
        tilingScheme,
        rectangle,
        tileWidth: layer.titleSize || 256,
        tileHeight: layer.titleSize || 256,
      }),
      {},
    ),
  };
}

function createURLTemplateLayer(config: LayerConfig) {
  const layer = config.layer as UrlTemplateConfig;
  const resource = createResource(config);
  const rectangle = createRectangle(config);
  const tilingScheme = createTilingScheme(layer.tileMatrixSetID, rectangle);
  return {
    type: 'ImageryLayer',
    value: new ImageryLayer(
      new UrlTemplateImageryProvider({
        url: resource,
        maximumLevel: layer.maximumLevel,
        minimumLevel: layer.minimumLevel,
        tilingScheme,
        rectangle,
        tileWidth: layer.titleSize || 256,
        tileHeight: layer.titleSize || 256,
        customTags: {
          z1: (imageryProvider: any, x: number, y: number, level: number) => {
            return level + 1;
          },
          z2: (imageryProvider: any, x: number, y: number, level: number) => {
            return level + 2;
          },
          z3: (imageryProvider: any, x: number, y: number, level: number) => {
            return level + 3;
          },
        },
      }),
    ),
  };
}

function createMVTLayer(config: LayerConfig) {
  const layer = config.layer as MVTConfig;
  const rectangle = createRectangle(config);
  const tilingScheme = createTilingScheme(layer.tileMatrixSetID, rectangle);
  return {
    type: 'ImageryLayer',
    value: ImageryLayer.fromProviderAsync(
      MVTImageryProvider.fromUrl({
        version: 8,
        name: 'custom-style',
        sources: {
          'vector-source': {
            type: 'vector',
            tiles: [
              layer?.url ?? '',
            ],
            scheme: 'xyz',
          },
        },
        layers: [
          {
            'id': 'point-layer',
            'type': 'line',
            'paint': {
              'line-opacity': layer.lineColor ? 1 : 0,
              'line-color': layer.lineColor || '#000000',
            },
            'source': 'vector-source',
            'source-layer': layer.layer,
            'layout': {
              visibility: 'visible',
            },
          },
          {
            'id': 'fill-layer',
            'type': 'fill',
            'source': 'vector-source',
            'source-layer': layer.layer,
            'layout': {
              visibility: 'visible',
            },
            'paint': {
              'fill-opacity': layer.fillColor ? 1 : 0,
              'fill-color': layer.fillColor || '#000000',
              'fill-outline-color': layer.fillOutlineColor,
            },
          },
        ],

      }, {
        maximumLevel: layer.maximumLevel,
        minimumLevel: layer.minimumLevel,
        tilingScheme,
        tileSize: layer.titleSize || 256,
      }) as any,
      {},
    ),
  };
}

function createTilesetLayer(config: LayerConfig) {
  const resource = createResource(config);
  return {
    type: 'Cesium3DTileset',
    value: Cesium3DTileset.fromUrl(resource, {
      maximumScreenSpaceError: 1, // 数值加大，能让最终成像变模糊
      loadSiblings: false, // 如果为true则不会在已加载完概况房屋后，自动从中心开始超清化房屋
      progressiveResolutionHeightFraction: 1, // 数值偏于0能够让初始加载变得模糊
      maximumCacheOverflowBytes: Number.POSITIVE_INFINITY,
    }),
  };
}

function createTerrainLayer(config: LayerConfig) {
  const resource = createResource(config);
  return {
    type: 'TerrainProvider',
    value: CesiumTerrainProvider.fromUrl(resource),
  };
}
