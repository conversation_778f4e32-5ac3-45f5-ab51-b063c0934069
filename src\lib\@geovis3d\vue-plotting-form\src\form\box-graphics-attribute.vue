<!-- BoxGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { BoxGraphicsKey, BoxGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { BoxGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import Cartesian3Attribute from './cartesian3-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'BoxGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: BoxGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.BoxGraphics, BoxGraphicsSerializateJSON>({
  graphic: () => props.entity?.box,
  omit: props.omit,
  toJSON: (graphics, omit) => BoxGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => BoxGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="box"
    graphics-field="show"
    label="可见"
  />
  <Cartesian3Attribute
    v-if="!hide?.includes('dimensions')"
    v-model="model.dimensions"
    graphics="box"
    graphics-field="dimensions"
    label="尺寸"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="box"
    graphics-field="heightReference"
    label="高度参照"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="box"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="box"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="box"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="box"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="box"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="box"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="box"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
</template>
