{"version": 1, "themeName": "macarons", "theme": {"seriesCnt": "10", "backgroundColor": "rgba(0,0,0,0)", "titleColor": "#ffffff", "subtitleColor": "#ffffff", "textColorShow": false, "textColor": "#333", "markTextColor": "#ffffff", "color": ["#7abfff", "#bac94a", "#e2d36b", "#6c8cbf", "#b0988e", "#4d8581", "#abded7", "#8474a1", "#6ec6ca", "#f9e2ae", "#fbc78d", "#a7d676", "#ccabdb", "#8474a1", "#205072", "#cde0c9", "#2c6975", "#329D9c", "#117C6F", "#289CBE", "#235D3A", "#397D54", "#EE9c6c"], "borderColor": "#ccc", "borderWidth": 0, "visualMapColor": ["#5ab1ef", "#e0ffff"], "legendTextColor": "#71bcff", "kColor": "#d87a80", "kColor0": "#2ec7c9", "kBorderColor": "#d87a80", "kBorderColor0": "#2ec7c9", "kBorderWidth": 1, "lineWidth": "2", "symbolSize": "2", "symbol": "circle", "symbolBorderWidth": 1, "lineSmooth": true, "graphLineWidth": 1, "graphLineColor": "#aaaaaa", "mapLabelColor": "#d87a80", "mapLabelColorE": "rgb(100,0,0)", "mapBorderColor": "#eeeeee", "mapBorderColorE": "#444", "mapBorderWidth": 0.5, "mapBorderWidthE": 1, "mapAreaColor": "#dddddd", "mapAreaColorE": "rgba(254,153,78,1)", "axes": [{"type": "all", "name": "通用坐标轴", "axisLineShow": true, "axisLineColor": "#eeeeee", "axisTickShow": true, "axisTickColor": "#eeeeee", "axisLabelShow": true, "axisLabelColor": "#eeeeee", "splitLineShow": true, "splitLineColor": ["#aaaaaa"], "splitAreaShow": false, "splitAreaColor": ["#eeeeee"]}, {"type": "category", "name": "类目坐标轴", "axisLineShow": true, "axisLineColor": "#71bcff", "axisTickShow": false, "axisTickColor": "#ffffff", "axisLabelShow": true, "axisLabelColor": "#71bcff", "splitLineShow": false, "splitLineColor": ["#eee"], "splitAreaShow": false, "splitAreaColor": ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]}, {"type": "value", "name": "数值坐标轴", "axisLineShow": true, "axisLineColor": "#71bcff", "axisTickShow": false, "axisTickColor": "#ffffff", "axisLabelShow": true, "axisLabelColor": "#71bcff", "splitLineShow": false, "splitLineColor": ["#eee"], "splitAreaShow": false, "splitAreaColor": ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]}, {"type": "log", "name": "对数坐标轴", "axisLineShow": true, "axisLineColor": "#71bcff", "axisTickShow": false, "axisTickColor": "#ffffff", "axisLabelShow": true, "axisLabelColor": "#71bcff", "splitLineShow": false, "splitLineColor": ["#eee"], "splitAreaShow": false, "splitAreaColor": ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]}, {"type": "time", "name": "时间坐标轴", "axisLineShow": true, "axisLineColor": "#71bcff", "axisTickShow": true, "axisTickColor": "#71bcff", "axisLabelShow": true, "axisLabelColor": "#71bcff", "splitLineShow": false, "splitLineColor": ["#eee"], "splitAreaShow": false, "splitAreaColor": ["rgba(250,250,250,0.3)", "rgba(200,200,200,0.3)"]}], "axisSeperateSetting": true, "toolboxColor": "#ffffff", "toolboxEmphasisColor": "#ffffff", "tooltipAxisColor": "#ffffff", "tooltipAxisWidth": "1", "timelineLineColor": "#008acd", "timelineLineWidth": 1, "timelineItemColor": "#008acd", "timelineItemColorE": "#a9334c", "timelineCheckColor": "#2ec7c9", "timelineCheckBorderColor": "#2ec7c9", "timelineItemBorderWidth": 1, "timelineControlColor": "#008acd", "timelineControlBorderColor": "#008acd", "timelineControlBorderWidth": 0.5, "timelineLabelColor": "#008acd", "datazoomBackgroundColor": "rgba(47,69,84,0)", "datazoomDataColor": "#efefff", "datazoomFillColor": "rgba(182,162,222,0.2)", "datazoomHandleColor": "#008acd", "datazoomHandleWidth": "100", "datazoomLabelColor": "#333333"}}