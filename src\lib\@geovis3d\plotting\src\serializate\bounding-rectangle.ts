import * as Cesium from 'cesium';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface BoundingRectangleSerializateJSON {
  x?: number;
  y?: number;
  width?: number;
  height?: number;
}

export type BoundingRectangleKey = keyof BoundingRectangleSerializateJSON;

export class BoundingRectangleSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.BoundingRectangle,
    time?: Cesium.JulianDate,
  ): BoundingRectangleSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      x: getValue('x'),
      y: getValue('y'),
      width: getValue('width'),
      height: getValue('height'),
    };
  }

  static fromJSON(json?: BoundingRectangleSerializateJSON): Cesium.BoundingRectangle | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json);

    return new Cesium.BoundingRectangle(
      getValue('x'),
      getValue('y'),
      getValue('width'),
      getValue('height'),
    );
  }
}
