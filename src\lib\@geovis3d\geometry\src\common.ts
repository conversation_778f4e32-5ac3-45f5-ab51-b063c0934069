import type { Coord, Feature, LineString, Point, Position } from '@turf/turf';

import { getCoord, lineString, point } from '@turf/turf';

export const FITTING_COUNT = 100;
export const HALF_PI = Math.PI / 2;
export const ZERO_TOLERANCE = 0.0001;

/**
 * 传入Coord数组 范围经纬数组列表。
 * {@link turf.getCoord}的批量版本
 * @param points
 */
export function getBatchCoords(points: Coord[]): Position[] {
  return points.map(e => getCoord(e));
}

/**
 * 计算两个坐标之间的距离
 * @param point1
 * @param point2

 * @constructor
 */

export function mathDistance(point1: Coord, point2: Coord): number {
  const coord1 = getCoord(point1);
  const coord2 = getCoord(point2);
  return Math.hypot(coord1[0] - coord2[0], coord1[1] - coord2[1]);
}

/**
 * 计算点集合的总距离
 * @param points

 */
export function wholeDistance(points: Coord[]): number {
  let distance = 0;
  if (points.length > 0) {
    points.forEach((item, index) => {
      if (index < points.length - 1) {
        distance += mathDistance(item, points[index + 1]);
      }
    });
  }
  return distance;
}
/**
 * 获取基础长度
 * @param points

 */
export function getBaseLength(points: Coord[]): number {
  return wholeDistance(points) ** 0.99;
}

/**
 * 获取交集的点
 */
export function getIntersectPoint(
  pointA: Coord,
  pointB: Coord,
  pointC: Coord,
  pointD: Coord,
): Position {
  const coordA = getCoord(pointA);
  const coordB = getCoord(pointB);
  const coordC = getCoord(pointC);
  const coordD = getCoord(pointD);

  if (coordA[1] === coordB[1]) {
    const f = (coordD[0] - coordC[0]) / (coordD[1] - coordC[1]);
    const x = f * (coordA[1] - coordC[1]) + coordC[0];
    const y = coordA[1];
    return [x, y];
  }
  if (coordC[1] === coordD[1]) {
    const e = (coordB[0] - coordA[0]) / (coordB[1] - coordA[1]);
    const x = e * (coordC[1] - coordA[1]) + coordA[0];
    const y = coordC[1];
    return [x, y];
  }
  const e = (coordB[0] - coordA[0]) / (coordB[1] - coordA[1]);
  const f = (coordD[0] - coordC[0]) / (coordD[1] - coordC[1]);
  const y = (e * coordA[1] - coordA[0] - f * coordC[1] + coordC[0]) / (e - f);
  const x = e * y - e * coordA[1] + coordA[0];
  return [x, y];
}

/**
 * 通过三个点确定一个圆的中心点
 * @param point1
 * @param point2
 * @param point3
 */
export function getCircleCenterOfThreePoints(
  point1: Coord,
  point2: Coord,
  point3: Coord,
): Position {
  const coord1 = getCoord(point1);
  const coord2 = getCoord(point2);
  const coord3 = getCoord(point3);
  const coordA = [(coord1[0] + coord2[0]) / 2, (coord1[1] + coord2[1]) / 2];
  const coordB = [coordA[0] - coord1[1] + coord2[1], coordA[1] + coord1[0] - coord2[0]];
  const coordC = [(coord1[0] + coord3[0]) / 2, (coord1[1] + coord3[1]) / 2];
  const coordD = [coordC[0] - coord1[1] + coord3[1], coordC[1] + coord1[0] - coord3[0]];
  return getIntersectPoint(coordA, coordB, coordC, coordD);
}

/**
 * 获取方位角（地平经度）
 * @param startPoint
 * @param endPoint

 */
export function getAzimuth(startPoint: Coord, endPoint: Coord): number {
  let azimuth: number;
  const startCoord = getCoord(startPoint);
  const endCoord = getCoord(endPoint);
  const angle = Math.asin(
    Math.abs(endCoord[1] - startCoord[1]) / mathDistance(startCoord, endCoord),
  );
  if (endCoord[1] >= startCoord[1] && endCoord[0] >= startCoord[0]) {
    azimuth = angle + Math.PI;
  }
  else if (endCoord[1] >= startCoord[1] && endCoord[0] < startCoord[0]) {
    azimuth = Math.PI * 2 - angle;
  }
  else if (endCoord[1] < startCoord[1] && endCoord[0] < startCoord[0]) {
    azimuth = angle;
  }
  else if (endCoord[1] < startCoord[1] && endCoord[0] >= startCoord[0]) {
    azimuth = Math.PI - angle;
  }
  return azimuth!;
}

/**
 * 通过三个点获取方位角
 * @param coordA
 * @param coordB
 * @param coordC

 */
export function getAngleOfThreePoints(coordA: Coord, coordB: Coord, coordC: Coord): number {
  const angle = getAzimuth(coordB, coordA) - getAzimuth(coordB, coordC);
  return angle < 0 ? angle + Math.PI * 2 : angle;
}

/**
 * 判断是否是顺时针
 * @param point1
 * @param point2
 * @param point3

 */
export function isClockWise(point1: Coord, point2: Coord, point3: Coord): boolean {
  const coord1 = getCoord(point1);
  const coord2 = getCoord(point2);
  const coord3 = getCoord(point3);
  return !!(
    (coord3[1] - coord1[1]) * (coord2[0] - coord1[0])
    > (coord2[1] - coord1[1]) * (coord3[0] - coord1[0])
  );
}

/**
 * 获取立方值
 * @param t
 * @param startPoint
 * @param cPoint1
 * @param cPoint2
 * @param endPoint

 */
export function getCubicValue(
  t: number,
  startPoint: Coord,
  cPoint1: Coord,
  cPoint2: Coord,
  endPoint: Coord,
): [number, number] {
  t = Math.max(Math.min(t, 1), 0);
  const tp = 1 - t;
  const t2 = t * t;
  const startCoord = getCoord(startPoint);
  const cCoord1 = getCoord(cPoint1);
  const cCoord2 = getCoord(cPoint2);
  const endCoord = getCoord(endPoint);
  const t3 = t2 * t;
  const tp2 = tp * tp;
  const tp3 = tp2 * tp;
  const x
    = tp3 * startCoord[0] + 3 * tp2 * t * cCoord1[0] + 3 * tp * t2 * cCoord2[0] + t3 * endCoord[0];
  const y
    = tp3 * startCoord[1] + 3 * tp2 * t * cCoord1[1] + 3 * tp * t2 * cCoord2[1] + t3 * endCoord[1];
  return [x, y];
}

/**
 * 根据起止点和旋转方向求取第三个点
 * @param startPoint
 * @param endPoint
 * @param angle
 * @param distance
 * @param clockWise

 */
export function getThirdPoint(
  startPoint: Coord,
  endPoint: Coord,
  angle: number,
  distance: number,
  clockWise = false,
): Feature<Point> {
  const azimuth = getAzimuth(startPoint, endPoint);
  const alpha = clockWise ? azimuth + angle : azimuth - angle;
  const dx = distance * Math.cos(alpha);
  const dy = distance * Math.sin(alpha);
  const endCoord = getCoord(endPoint);
  return point([endCoord[0] + dx, endCoord[1] + dy]);
}

/**
 * 插值弓形线段点
 * @param center
 * @param radius
 * @param startAngle
 * @param endAngle

 */
export function getArcPoints(
  center: Coord,
  radius: number,
  startAngle: number,
  endAngle: number,
): Feature<Point>[] {
  let x: number;
  let y: number;
  const coords = getCoord(center);
  const points: Feature<Point>[] = [];
  let angleDiff = endAngle - startAngle;
  angleDiff = angleDiff < 0 ? angleDiff + Math.PI * 2 : angleDiff;
  for (let i = 0; i <= 100; i++) {
    const angle = startAngle + (angleDiff * i) / 100;
    x = coords[0] + radius * Math.cos(angle);
    y = coords[1] + radius * Math.sin(angle);

    points.push(point([x, y]));
  }
  return points;
}

/**
 * 获取默认三点的内切圆
 * @param point1
 * @param point2
 * @param point3

 */
function getNormal(point1: Coord, point2: Coord, point3: Coord): number[] {
  const coord1 = getCoord(point1);
  const coord2 = getCoord(point2);
  const coord3 = getCoord(point3);

  let dX1 = coord1[0] - coord2[0];
  let dY1 = coord1[1] - coord2[1];
  const d1 = Math.hypot(dX1, dY1);
  dX1 /= d1;
  dY1 /= d1;
  let dX2 = coord3[0] - coord2[0];
  let dY2 = coord3[1] - coord2[1];
  const d2 = Math.hypot(dX2, dY2);
  dX2 /= d2;
  dY2 /= d2;
  const uX = dX1 + dX2;
  const uY = dY1 + dY2;
  return [uX, uY];
}

/**
 * getBisectorNormals
 * @param t
 * @param point1
 * @param point2
 * @param point3
 */
export function getBisectorNormals(
  t: number,
  point1: Coord,
  point2: Coord,
  point3: Coord,
): number[][] {
  const coord1 = getCoord(point1);
  const coord2 = getCoord(point2);
  const coord3 = getCoord(point3);
  const normal = getNormal(coord1, coord2, coord3);
  let bisectorNormalRight, bisectorNormalLeft, dt, x, y;
  const dist = Math.hypot(normal[0], normal[1]);
  const uX = normal[0] / dist;
  const uY = normal[1] / dist;
  const d1 = mathDistance(coord1, coord2);
  const d2 = mathDistance(coord2, coord3);
  if (dist > ZERO_TOLERANCE) {
    if (isClockWise(coord1, coord2, coord3)) {
      dt = t * d1;
      x = coord2[0] - dt * uY;
      y = coord2[1] + dt * uX;
      bisectorNormalRight = [x, y];
      dt = t * d2;
      x = coord2[0] + dt * uY;
      y = coord2[1] - dt * uX;
      bisectorNormalLeft = [x, y];
    }
    else {
      dt = t * d1;
      x = coord2[0] + dt * uY;
      y = coord2[1] - dt * uX;
      bisectorNormalRight = [x, y];
      dt = t * d2;
      x = coord2[0] - dt * uY;
      y = coord2[1] + dt * uX;
      bisectorNormalLeft = [x, y];
    }
  }
  else {
    x = coord2[0] + t * (coord1[0] - coord2[0]);
    y = coord2[1] + t * (coord1[1] - coord2[1]);
    bisectorNormalRight = [x, y];
    x = coord2[0] + t * (coord3[0] - coord2[0]);
    y = coord2[1] + t * (coord3[1] - coord2[1]);
    bisectorNormalLeft = [x, y];
  }
  return [bisectorNormalRight, bisectorNormalLeft];
}

/**
 * 获取阶乘数据
 * @param n

 */
function getFactorial(n: number): number {
  let result = 1;
  if (n <= 1) {
    result = 1;
  }
  else if (n === 2) {
    result = 2;
  }
  else if (n === 3) {
    result = 6;
  }
  else if (n === 24) {
    result = 24;
  }
  else if (n === 5) {
    result = 120;
  }
  else {
    for (let i = 1; i <= n; i++) {
      result *= i;
    }
  }
  return result;
}

/**
 * 获取二项分布
 * @param n
 * @param index
 */
export function getBinomialFactor(n: number, index: number): number {
  return getFactorial(n) / (getFactorial(index) * getFactorial(n - index));
}

/**
 * 贝塞尔曲线
 * @param points

 */
export function getBezierPoints(line: Feature<LineString>): Feature<LineString> {
  const coords = line.geometry.coordinates;
  if (coords.length <= 2) {
    return lineString(coords);
  }
  else {
    const bezierPoints: Position[] = [];
    const n = coords.length - 1;
    for (let t = 0; t <= 1; t += 0.01) {
      let x = 0;
      let y = 0;
      for (let index = 0; index <= n; index++) {
        const factor = getBinomialFactor(n, index);
        const a = t ** index;
        const b = (1 - t) ** (n - index);
        x += factor * a * b * coords[index][0];
        y += factor * a * b * coords[index][1];
      }
      bezierPoints.push([x, y]);
    }
    bezierPoints.push(coords[n]);
    return lineString(bezierPoints);
  }
}

/**
 * 得到二次线性因子
 * @param k
 * @param t

 */
function getQuadricBSplineFactor(k: number, t: number): number {
  let res = 0;
  if (k === 0) {
    res = (t - 1) ** 2 / 2;
  }
  else if (k === 1) {
    res = (-2 * t ** 2 + 2 * t + 1) / 2;
  }
  else if (k === 2) {
    res = t ** 2 / 2;
  }
  return res;
}

/**
 * 插值线性点
 * @param points

 */
export function getQBSplinePoints(points: Coord[]): Feature<Point>[] {
  const coords = points.map(e => getCoord(e));
  if (coords.length <= 2) {
    return coords.map(e => point(e));
  }
  else {
    const n = 2;
    const bSplinePoints: Position[] = [];
    const m = coords.length - n - 1;
    bSplinePoints.push(coords[0]);
    for (let i = 0; i <= m; i++) {
      for (let t = 0; t <= 1; t += 0.05) {
        let x = 0;
        let y = 0;
        for (let k = 0; k <= n; k++) {
          const factor = getQuadricBSplineFactor(k, t);
          x += factor * coords[i + k][0];
          y += factor * coords[i + k][1];
        }
        bSplinePoints.push([x, y]);
      }
    }
    bSplinePoints.push(coords.at(-1));
    return bSplinePoints.map(e => point(e));
  }
}
