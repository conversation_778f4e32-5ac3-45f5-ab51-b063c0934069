<!-- 可视域分析 -->
<script lang="ts" setup>
import { CzPlotEntity, CzPlotScheme, POLYLINE_PLOT_SCHEME_OPTIONS } from '@x3d/all';
import { useCzEntity, useCzPrimitive, useCzViewer } from '@x3d/vue-hooks';
import { FrustumPrimitive } from './frustum-primitve';

defineOptions({ name: 'ViewshedAnalysis' });
CzPlotScheme.addCache('Polyline', POLYLINE_PLOT_SCHEME_OPTIONS);

const viewer = useCzViewer();

watchEffect((onCleanup) => {
  const defaultShadowMode = viewer.value.scene.globe.shadows;
  viewer.value.scene.globe.shadows = Cesium.ShadowMode.DISABLED;
  onCleanup(() => {
    try {
      viewer.value.scene.globe.shadows = defaultShadowMode;
    }
    catch (error) {
      console.error(error);
    }
  });
});

const form = ref({
  horizontalFov: 120,
  verticxalFov: 30,
});

const plotEntity = shallowRef<CzPlotEntity>();
useCzEntity(plotEntity);

const position = shallowRef<Cesium.Cartesian3>();
const endPosition = shallowRef<Cesium.Cartesian3>();

function execute() {
  position.value = undefined;
  endPosition.value = undefined;
  plotEntity.value = new CzPlotEntity({
    scheme: {
      forceTerminate(entity) {
        return entity.record.positions.getLength() === 2;
      },
      control: {
        visible: true,
      },

      effect(entity) {
        const positions = [...entity.record.positions.getValue()].map(item => item.position);
        if (positions.length < 2 && entity.controller.mouse) {
          positions.push(entity.controller.mouse);
        }
        if (positions.length >= 2) {
          position.value = positions[0].clone();
          endPosition.value = positions[1].clone();
        }
      },
    },
  });
}
useCzPrimitive(() => {
  if (position.value && endPosition.value) {
    return new FrustumPrimitive({
      scene: viewer.value.scene,
      position: position.value.clone(),
      endPosition: endPosition.value.clone(),
      visibleColor: Cesium.Color.GREEN,
      invisibilityColor: Cesium.Color.RED,
      horizontalFov: form.value.horizontalFov,
      verticalFov: form.value.verticxalFov,
    });
  }
});

function clear() {
  plotEntity.value = undefined;
  position.value = undefined;
  endPosition.value = undefined;
}

watchEffect((onCleanup) => {
  if (viewer.value) {
    viewer.value.scene.globe.depthTestAgainstTerrain = true;
  }
  onCleanup(() => {
    if (viewer.value) {
      viewer.value.scene.globe.depthTestAgainstTerrain = false;
    }
  });
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="可视域分析"
    class="w-400px"
  >
    <el-form m="x-20px y-10px">
      <el-form-item label="水平角度">
        <el-input-number v-model="form.horizontalFov" :min="1" :max="80" :precision="0" />
      </el-form-item>
      <el-form-item label="垂直角度">
        <el-input-number v-model="form.verticxalFov" :min="1" :max="180" :precision="0" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button class="bg-#4176FF! color-white!" px="26px!" @click="execute()">
        绘制
      </el-button>
      <el-button class="plain-#FF6363" px="26px!" @click="clear()">
        清空
      </el-button>
    </template>
  </drag-card>
</template>
