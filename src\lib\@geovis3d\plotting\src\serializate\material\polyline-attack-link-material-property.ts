import type { MaterialPropertySerializateController } from './material-property';
import { PolylineAttackLinkMaterialProperty } from '@/lib/@geovis3d/material';

import * as Cesium from 'cesium';

import { ColorSerializate } from '../color';

export interface PolylineAttackLinkMaterialPropertySerializateJSON {
  color?: string;
  time?: number;
}

/**
 * PolylineAttackLinkMaterialProperty序列化加入缓存
 * @internal
 */
export default <
  MaterialPropertySerializateController<
    'PolylineAttackLinkMaterialProperty',
    PolylineAttackLinkMaterialProperty,
    PolylineAttackLinkMaterialPropertySerializateJSON
  >
>{
  type: 'PolylineAttackLinkMaterialProperty',
  hit: (property) => {
    return property instanceof PolylineAttackLinkMaterialProperty;
  },
  toJSON(property, time) {
    time ??= Cesium.JulianDate.now();

    const data = property?.getValue(time) ?? {};
    return {
      type: 'PolylineAttackLinkMaterialProperty',
      params: {
        color: ColorSerializate.toJSON(data.color),
        time: data.time,
      },
    };
  },
  fromJSON(json) {
    const { color, time } = json?.params ?? {};
    return new PolylineAttackLinkMaterialProperty({
      color: ColorSerializate.fromJSON(color),
      time,
    });
  },
};
