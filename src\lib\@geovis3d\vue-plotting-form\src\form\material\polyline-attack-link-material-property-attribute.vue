<!-- PolylineAttackLinkMaterialPropertyAttribute 属性编辑 -->
<script lang="ts" setup>
import type { PolylineAttackLinkMaterialPropertySerializateJSON } from '@/lib/@geovis3d/plotting';

import ColorAttribute from '../color-attribute.vue';
import { useShallowBinding } from '../hooks';
import NumberAttribute from '../number-attribute.vue';

defineOptions({ name: 'PolylineAttackLinkMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: PolylineAttackLinkMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: PolylineAttackLinkMaterialPropertySerializateJSON): void;
}>();

const model = ref<PolylineAttackLinkMaterialPropertySerializateJSON>({});
useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="攻击特效线" /> -->
  <ColorAttribute v-model="model.color" label="颜色" />
  <NumberAttribute v-model="model.time" :min="1" :precision="0" label="持续时间" />
</template>
