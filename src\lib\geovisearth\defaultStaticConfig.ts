/**
 * 该文件配置所以默认的静态资源路径
 * 如：图片，模型等
 * 这些配置是在创建实例是没有给于iamgeUrl时使用
 * 例：
 *  craeteDynamicCricleGraphics:(options){
 *    new Cesium.ImageMaterialProperty({
 *     image: options.img || `static/data/images/Textures/circle_bg.pn`'
 *   }
 * }
 * 优先看options 是否传入img,如没有使用默认配置
 */
export const defaultStatic = {
  skyBox: {
    distant: [{
      // 远景深邃
      name: '远景深邃',
      positiveX: `static/data/images/SkyBox/00h+00.jpg`,
      negativeX: `static/data/images/SkyBox/12h+00.jpg`,
      positiveY: `static/data/images/SkyBox/06h+00.jpg`,
      negativeY: `static/data/images/SkyBox/18h+00.jpg`,
      positiveZ: `static/data/images/SkyBox/06h+90.jpg`,
      negativeZ: `static/data/images/SkyBox/06h-90.jpg`,
    }, {
      // 远景暗黑
      name: '远景暗黑',
      positiveX: `static/data/images/SkyBox/Version2_dark_px.jpg`,
      negativeX: `static/data/images/SkyBox/Version2_dark_mx.jpg`,
      positiveY: `static/data/images/SkyBox/Version2_dark_py.jpg`,
      negativeY: `static/data/images/SkyBox/Version2_dark_my.jpg`,
      positiveZ: `static/data/images/SkyBox/Version2_dark_pz.jpg`,
      negativeZ: `static/data/images/SkyBox/Version2_dark_mz.jpg`,
    }, {
      // 远景暗紫
      name: '远景暗紫',
      positiveX: `static/data/images/SkyBox/tycho2t3_80_pxs.jpg`,
      negativeX: `static/data/images/SkyBox/tycho2t3_80_mxs.jpg`,
      positiveY: `static/data/images/SkyBox/tycho2t3_80_pys.jpg`,
      negativeY: `static/data/images/SkyBox/tycho2t3_80_mys.jpg`,
      positiveZ: `static/data/images/SkyBox/tycho2t3_80_pzs.jpg`,
      negativeZ: `static/data/images/SkyBox/tycho2t3_80_mzs.jpg`,
    }],
    near: [{
      // 近景晴天
      name: '近景晴天',
      positiveX: `static/data/images/SkyBox/rightav9.jpg`,
      negativeX: `static/data/images/SkyBox/leftav9.jpg`,
      positiveY: `static/data/images/SkyBox/frontav9.jpg`,
      negativeY: `static/data/images/SkyBox/backav9.jpg`,
      positiveZ: `static/data/images/SkyBox/topav9.jpg`,
      negativeZ: `static/data/images/SkyBox/bottomav9.jpg`,
    }, {
      // 近景晚霞
      name: '近景晚霞',
      positiveX: `static/data/images/SkyBox/SunSetRight.png`,
      negativeX: `static/data/images/SkyBox/SunSetLeft.png`,
      positiveY: `static/data/images/SkyBox/SunSetFront.png`,
      negativeY: `static/data/images/SkyBox/SunSetBack.png`,
      positiveZ: `static/data/images/SkyBox/SunSetUp.png`,
      negativeZ: `static/data/images/SkyBox/SunSetDown.png`,
    }, {
      // 近景蓝天
      name: '近景蓝天',
      positiveX: `static/data/images/SkyBox/Right.jpg`,
      negativeX: `static/data/images/SkyBox/Left.jpg`,
      positiveY: `static/data/images/SkyBox/Front.jpg`,
      negativeY: `static/data/images/SkyBox/Back.jpg`,
      positiveZ: `static/data/images/SkyBox/Up.jpg`,
      negativeZ: `static/data/images/SkyBox/Down.jpg`,
    }],
  },
};
