<!-- 可拖拽卡片 -->
<script lang="ts" setup>
import type { VNodeChild } from 'vue';
import type { BasicOrComponentOpt } from '../basic-or-component/basic-or-component.vue';
import type HeaderTitle1 from '../header-title/header-title1.vue';

export interface DragCardProps {

  initialLeft?: number;
  initialRight?: number;
  initialTop?: number;
  initialBottom?: number;
  /**
   * teleport中的to属性，默认body
   */
  appendTo?: any;
  /**
   * 是否显示关闭按钮，默认true
   */
  showClose?: boolean;
  /**
   * 是否显示菜单按钮，默认false
   */
  showMenu?: boolean;

  extra?: BasicOrComponentOpt;
  title?: BasicOrComponentOpt;
  content?: BasicOrComponentOpt;
  footer?: BasicOrComponentOpt;
  contentClass?: any;
  footerClass?: any;
}

export interface DragCardSlots {
  title?: VNodeChild;
  extra?: VNodeChild;
  default?: VNodeChild;
  footer?: VNodeChild;
}

export interface DragCardEmits {
  (event: 'clickMenu'): void;
  (event: 'close'): void;
}

defineOptions({ name: 'DragCard', inheritAttrs: false });
const props = withDefaults(defineProps<DragCardProps>(), {
  appendTo: 'body',
  showClose: true,
});
const emit = defineEmits<DragCardEmits>();
const slots = defineSlots<DragCardSlots>();

const elRef = shallowRef<HTMLElement>();
const headerRef = shallowRef<InstanceType<typeof HeaderTitle1>>();

const { x, y, style } = useDraggable(elRef, {
  stopPropagation: true,
  handle: () => headerRef.value?.$el,
  containerElement: document.body,
});

const windowSize = useWindowSize();
const boxSize = useElementSize(elRef);

// 初始化设置
const { stop: initialcomplete } = watch(
  [boxSize.height, boxSize.width, windowSize.width, windowSize.height],
  () => {
    if (boxSize.width.value && boxSize.height.value && windowSize.width.value && windowSize.height.value) {
      let _x = windowSize.width.value / 2 - boxSize.width.value / 2;
      let _y = windowSize.height.value / 2 - boxSize.height.value / 2;
      if (props.initialLeft || props?.initialLeft === 0) {
        _x = props.initialLeft;
      }
      else if (props.initialRight || props.initialRight === 0) {
        _x = (windowSize.width.value - props.initialRight) - boxSize.width.value;
      }

      if (props.initialTop || props?.initialTop === 0) {
        _y = props.initialTop;
      }
      else if (props.initialBottom || props.initialBottom === 0) {
        _y = (windowSize.height.value - props.initialBottom) - boxSize.height.value;
      }
      x.value = _x;
      y.value = _y;
      initialcomplete();
    }
  },
);
</script>

<template>
  <teleport :to="props.appendTo">
    <div
      ref="elRef"
      class="drag-card"
      w="400px"
      flex="~ col"
      bg="[var(--el-bg-color)]"
      b="1px #fff/10"
      rd="8px"
      position="fixed"
      z="10"
      v-bind="$attrs"
      :style="style"
    >
      <header-title1 ref="headerRef" cursor="move" b-b="1px! solid #fff/10%" select="none" flex="shrink-0">
        <basic-or-component :is="slots.title ?? props.title" />
        <template #extra>
          <basic-or-component :is="slots.extra ?? props.extra" />
          <el-button v-if="showMenu" link class="h-25px! p-0!" @click.stop="emit('clickMenu')">
            <el-icon class="i-material-symbols:more-vert" text="20px!" color="#fff" />
          </el-button>
          <el-button v-if="showClose" link class="h-25px! p-0!" @click.stop="emit('close')">
            <el-icon class="i-material-symbols:close" text="20px! #fff!" />
          </el-button>
        </template>
      </header-title1>
      <div class="drag-card__content" :class="props.contentClass">
        <basic-or-component :is="slots.default ?? props.content" />
      </div>

      <div
        v-if="slots.footer ?? props.footer"
        class="drag-card__footer"
        :class="props.footerClass"
        b-t="1px #fff/10"
        shadow="[0_-6px_16px_0_#000/40]"
        flex="~ justify-between"
        p="24px"
      >
        <basic-or-component :is="slots.footer ?? props.footer" />
      </div>
    </div>
  </teleport>
</template>
