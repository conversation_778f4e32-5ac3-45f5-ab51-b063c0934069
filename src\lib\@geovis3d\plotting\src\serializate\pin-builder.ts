import type { ColorSerializateJSON } from './color';

import * as Cesium from 'cesium';

import { ColorSerializate } from './color';

export type PinBuilderSerializateJSON = [text: string, color: ColorSerializateJSON, size: number];

export interface PinBuilderHTMLCanvasElement extends HTMLCanvasElement {
  pinBuilder: PinBuilderSerializateJSON;
}

export type PinBuilderKey = keyof PinBuilderSerializateJSON;

export class PinBuilderSerializate {
  private constructor() {}
  static toJSON(data?: PinBuilderHTMLCanvasElement): PinBuilderSerializateJSON | undefined {
    if (data?.pinBuilder) {
      return data?.pinBuilder;
    }
  }

  static fromJSON(json?: PinBuilderSerializateJSON): PinBuilderHTMLCanvasElement | undefined {
    if (json) {
      const canvas = new Cesium.PinBuilder().fromText(
        json[0],
        ColorSerializate.fromJSON(json[1]) || Cesium.Color.GREEN.withAlpha(0.8),
        json[2],
      ) as PinBuilderHTMLCanvasElement;

      canvas.pinBuilder = [
        json[0],
        json[1] || Cesium.Color.GREEN.withAlpha(0.8).toCssColorString(),
        json[2],
      ];
      return canvas;
    }
  }
}
