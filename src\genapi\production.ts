/* eslint-disable eslint-comments/no-unlimited-disable */
/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by @xiankq/openapi-typescript-expand
// Power by openapi-typescript

import {bdvRequest} from "./request";



/**
 * @tag 安全生产-安全生产事故专题
 * @summary 按事件类型统计: 0-收缩 1-展示所有
 * @url /production/accident/accidentTypeStatistic/{isShowAll}
 * @method get
 * @description 按事件类型统计
 */

export module ProductionAccidentAccidentTypeStatisticIsShowAllUsingGet {
  export type Operation = paths['/production/accident/accidentTypeStatistic/{isShowAll}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 按事件类型统计: 0-收缩 1-展示所有
 * @url /production/accident/accidentTypeStatistic/{isShowAll}
 * @method get
 * @description 按事件类型统计
 */

export function productionAccidentAccidentTypeStatisticIsShowAllUsingGet(options:ProductionAccidentAccidentTypeStatisticIsShowAllUsingGet.Options):Promise<ProductionAccidentAccidentTypeStatisticIsShowAllUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/accidentTypeStatistic/{isShowAll}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 特大事故年度统计
 * @url /production/accident/bestAccidentYearStatistic
 * @method get
 * @description 特大事故年度统计
 */

export module ProductionAccidentBestAccidentYearStatisticUsingGet {
  export type Operation = paths['/production/accident/bestAccidentYearStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 特大事故年度统计
 * @url /production/accident/bestAccidentYearStatistic
 * @method get
 * @description 特大事故年度统计
 */

export function productionAccidentBestAccidentYearStatisticUsingGet(options:ProductionAccidentBestAccidentYearStatisticUsingGet.Options):Promise<ProductionAccidentBestAccidentYearStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/bestAccidentYearStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 日报数据查询:yyyy-MM-dd
 * @url /production/accident/dayReport/{day}
 * @method get
 * @description 日报数据查询
 */

export module ProductionAccidentDayReportDayUsingGet {
  export type Operation = paths['/production/accident/dayReport/{day}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 日报数据查询:yyyy-MM-dd
 * @url /production/accident/dayReport/{day}
 * @method get
 * @description 日报数据查询
 */

export function productionAccidentDayReportDayUsingGet(options:ProductionAccidentDayReportDayUsingGet.Options):Promise<ProductionAccidentDayReportDayUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/dayReport/{day}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 日报数据下载:yyyy-MM-dd
 * @url /production/accident/dayReportDownload/{day}
 * @method get
 * @description 
 */

export module ProductionAccidentDayReportDownloadDayUsingGet {
  export type Operation = paths['/production/accident/dayReportDownload/{day}']['get'];
  export type Result = any;
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 日报数据下载:yyyy-MM-dd
 * @url /production/accident/dayReportDownload/{day}
 * @method get
 * @description 
 */

export function productionAccidentDayReportDownloadDayUsingGet(options:ProductionAccidentDayReportDownloadDayUsingGet.Options):Promise<ProductionAccidentDayReportDownloadDayUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/dayReportDownload/{day}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 死亡人数年度统计
 * @url /production/accident/dieNumYearStatistic
 * @method get
 * @description 死亡人数年度统计
 */

export module ProductionAccidentDieNumYearStatisticUsingGet {
  export type Operation = paths['/production/accident/dieNumYearStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 死亡人数年度统计
 * @url /production/accident/dieNumYearStatistic
 * @method get
 * @description 死亡人数年度统计
 */

export function productionAccidentDieNumYearStatisticUsingGet(options:ProductionAccidentDieNumYearStatisticUsingGet.Options):Promise<ProductionAccidentDieNumYearStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/dieNumYearStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 属性统计：1-市县统计 2-等级统计 3-月份统计
 * @url /production/accident/fieldStatistic/{fieldKey}
 * @method get
 * @description 属性统计：1-市县统计 2-等级统计 3-月份统计
 */

export module ProductionAccidentFieldStatisticFieldKeyUsingGet {
  export type Operation = paths['/production/accident/fieldStatistic/{fieldKey}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 属性统计：1-市县统计 2-等级统计 3-月份统计
 * @url /production/accident/fieldStatistic/{fieldKey}
 * @method get
 * @description 属性统计：1-市县统计 2-等级统计 3-月份统计
 */

export function productionAccidentFieldStatisticFieldKeyUsingGet(options:ProductionAccidentFieldStatisticFieldKeyUsingGet.Options):Promise<ProductionAccidentFieldStatisticFieldKeyUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/fieldStatistic/{fieldKey}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary geoHash事故网格点位明细
 * @url /production/accident/gridPointList/{geoHash}
 * @method get
 * @description geoHash事故网格点位明细
 */

export module ProductionAccidentGridPointListGeoHashUsingGet {
  export type Operation = paths['/production/accident/gridPointList/{geoHash}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary geoHash事故网格点位明细
 * @url /production/accident/gridPointList/{geoHash}
 * @method get
 * @description geoHash事故网格点位明细
 */

export function productionAccidentGridPointListGeoHashUsingGet(options:ProductionAccidentGridPointListGeoHashUsingGet.Options):Promise<ProductionAccidentGridPointListGeoHashUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/gridPointList/{geoHash}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary geoHash事故网格点位聚合统计
 * @url /production/accident/gridPointStatistic
 * @method post
 * @description geoHash事故网格点位聚合统计
 */

export module ProductionAccidentGridPointStatisticUsingPost {
  export type Operation = paths['/production/accident/gridPointStatistic']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary geoHash事故网格点位聚合统计
 * @url /production/accident/gridPointStatistic
 * @method post
 * @description geoHash事故网格点位聚合统计
 */

export function productionAccidentGridPointStatisticUsingPost(options:ProductionAccidentGridPointStatisticUsingPost.Options):Promise<ProductionAccidentGridPointStatisticUsingPost.Result> {
  return bdvRequest({
    url:'/production/accident/gridPointStatistic',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 列表查询
 * @url /production/accident/list
 * @method post
 * @description 列表查询
 */

export module ProductionAccidentListUsingPost {
  export type Operation = paths['/production/accident/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 列表查询
 * @url /production/accident/list
 * @method post
 * @description 列表查询
 */

export function productionAccidentListUsingPost(options:ProductionAccidentListUsingPost.Options):Promise<ProductionAccidentListUsingPost.Result> {
  return bdvRequest({
    url:'/production/accident/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 分页查询
 * @url /production/accident/listPage
 * @method post
 * @description 分页查询
 */

export module ProductionAccidentListPageUsingPost {
  export type Operation = paths['/production/accident/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 分页查询
 * @url /production/accident/listPage
 * @method post
 * @description 分页查询
 */

export function productionAccidentListPageUsingPost(options:ProductionAccidentListPageUsingPost.Options):Promise<ProductionAccidentListPageUsingPost.Result> {
  return bdvRequest({
    url:'/production/accident/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 点位列表查询
 * @url /production/accident/listPoints
 * @method post
 * @description 点位列表查询
 */

export module ProductionAccidentListPointsUsingPost {
  export type Operation = paths['/production/accident/listPoints']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 点位列表查询
 * @url /production/accident/listPoints
 * @method post
 * @description 点位列表查询
 */

export function productionAccidentListPointsUsingPost(options:ProductionAccidentListPointsUsingPost.Options):Promise<ProductionAccidentListPointsUsingPost.Result> {
  return bdvRequest({
    url:'/production/accident/listPoints',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 事故相关人员列表
 * @url /production/accident/listRelatedUser/{id}
 * @method get
 * @description 事故相关人员列表
 */

export module ProductionAccidentListRelatedUserIdUsingGet {
  export type Operation = paths['/production/accident/listRelatedUser/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 事故相关人员列表
 * @url /production/accident/listRelatedUser/{id}
 * @method get
 * @description 事故相关人员列表
 */

export function productionAccidentListRelatedUserIdUsingGet(options:ProductionAccidentListRelatedUserIdUsingGet.Options):Promise<ProductionAccidentListRelatedUserIdUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/listRelatedUser/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 续报列表
 * @url /production/accident/listResubmit/{id}
 * @method get
 * @description 续报列表
 */

export module ProductionAccidentListResubmitIdUsingGet {
  export type Operation = paths['/production/accident/listResubmit/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 续报列表
 * @url /production/accident/listResubmit/{id}
 * @method get
 * @description 续报列表
 */

export function productionAccidentListResubmitIdUsingGet(options:ProductionAccidentListResubmitIdUsingGet.Options):Promise<ProductionAccidentListResubmitIdUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/listResubmit/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 减少损失统计
 * @url /production/accident/reduceLossesStatistic
 * @method get
 * @description 减少损失统计
 */

export module ProductionAccidentReduceLossesStatisticUsingGet {
  export type Operation = paths['/production/accident/reduceLossesStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 减少损失统计
 * @url /production/accident/reduceLossesStatistic
 * @method get
 * @description 减少损失统计
 */

export function productionAccidentReduceLossesStatisticUsingGet(options:ProductionAccidentReduceLossesStatisticUsingGet.Options):Promise<ProductionAccidentReduceLossesStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/accident/reduceLossesStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 简报数据查询
 * @url /production/accident/simpleReport
 * @method post
 * @description 简报数据查询
 */

export module ProductionAccidentSimpleReportUsingPost {
  export type Operation = paths['/production/accident/simpleReport']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 简报数据查询
 * @url /production/accident/simpleReport
 * @method post
 * @description 简报数据查询
 */

export function productionAccidentSimpleReportUsingPost(options:ProductionAccidentSimpleReportUsingPost.Options):Promise<ProductionAccidentSimpleReportUsingPost.Result> {
  return bdvRequest({
    url:'/production/accident/simpleReport',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 简报数据下载
 * @url /production/accident/simpleReportDownload
 * @method post
 * @description 简报数据下载
 */

export module ProductionAccidentSimpleReportDownloadUsingPost {
  export type Operation = paths['/production/accident/simpleReportDownload']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-安全生产事故专题
 * @summary 简报数据下载
 * @url /production/accident/simpleReportDownload
 * @method post
 * @description 简报数据下载
 */

export function productionAccidentSimpleReportDownloadUsingPost(options:ProductionAccidentSimpleReportDownloadUsingPost.Options):Promise<ProductionAccidentSimpleReportDownloadUsingPost.Result> {
  return bdvRequest({
    url:'/production/accident/simpleReportDownload',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺分页查询
 * @url /production/hazardousChemicals/commitmentListPage
 * @method post
 * @description 企业分页查询
 */

export module ProductionHazardousChemicalsCommitmentListPageUsingPost {
  export type Operation = paths['/production/hazardousChemicals/commitmentListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺分页查询
 * @url /production/hazardousChemicals/commitmentListPage
 * @method post
 * @description 企业分页查询
 */

export function productionHazardousChemicalsCommitmentListPageUsingPost(options:ProductionHazardousChemicalsCommitmentListPageUsingPost.Options):Promise<ProductionHazardousChemicalsCommitmentListPageUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/commitmentListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 企业详情查询
 * @url /production/hazardousChemicals/companyDetail/{companyCode}
 * @method post
 * @description 企业详情查询
 */

export module ProductionHazardousChemicalsCompanyDetailCompanyCodeUsingPost {
  export type Operation = paths['/production/hazardousChemicals/companyDetail/{companyCode}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 企业详情查询
 * @url /production/hazardousChemicals/companyDetail/{companyCode}
 * @method post
 * @description 企业详情查询
 */

export function productionHazardousChemicalsCompanyDetailCompanyCodeUsingPost(options:ProductionHazardousChemicalsCompanyDetailCompanyCodeUsingPost.Options):Promise<ProductionHazardousChemicalsCompanyDetailCompanyCodeUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/companyDetail/{companyCode}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 企业列表查询
 * @url /production/hazardousChemicals/companyList
 * @method post
 * @description 企业列表查询
 */

export module ProductionHazardousChemicalsCompanyListUsingPost {
  export type Operation = paths['/production/hazardousChemicals/companyList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 企业列表查询
 * @url /production/hazardousChemicals/companyList
 * @method post
 * @description 企业列表查询
 */

export function productionHazardousChemicalsCompanyListUsingPost(options:ProductionHazardousChemicalsCompanyListUsingPost.Options):Promise<ProductionHazardousChemicalsCompanyListUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/companyList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 企业分页查询
 * @url /production/hazardousChemicals/companyListPage
 * @method post
 * @description 企业分页查询
 */

export module ProductionHazardousChemicalsCompanyListPageUsingPost {
  export type Operation = paths['/production/hazardousChemicals/companyListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 企业分页查询
 * @url /production/hazardousChemicals/companyListPage
 * @method post
 * @description 企业分页查询
 */

export function productionHazardousChemicalsCompanyListPageUsingPost(options:ProductionHazardousChemicalsCompanyListPageUsingPost.Options):Promise<ProductionHazardousChemicalsCompanyListPageUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/companyListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据危险源编码查询设备列表
 * @url /production/hazardousChemicals/getEquipListByHazardCode/{hazardCode}
 * @method post
 * @description 根据危险源编码查询设备列表
 */

export module ProductionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost {
  export type Operation = paths['/production/hazardousChemicals/getEquipListByHazardCode/{hazardCode}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据危险源编码查询设备列表
 * @url /production/hazardousChemicals/getEquipListByHazardCode/{hazardCode}
 * @method post
 * @description 根据危险源编码查询设备列表
 */

export function productionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost(options:ProductionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost.Options):Promise<ProductionHazardousChemicalsGetEquipListByHazardCodeHazardCodeUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/getEquipListByHazardCode/{hazardCode}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据企业编码查询危险源列表
 * @url /production/hazardousChemicals/getHazardListByCompanyCode/{companyCode}
 * @method get
 * @description 根据企业编码查询危险源列表
 */

export module ProductionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet {
  export type Operation = paths['/production/hazardousChemicals/getHazardListByCompanyCode/{companyCode}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据企业编码查询危险源列表
 * @url /production/hazardousChemicals/getHazardListByCompanyCode/{companyCode}
 * @method get
 * @description 根据企业编码查询危险源列表
 */

export function productionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet(options:ProductionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet.Options):Promise<ProductionHazardousChemicalsGetHazardListByCompanyCodeCompanyCodeUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/getHazardListByCompanyCode/{companyCode}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺明细
 * @url /production/hazardousChemicals/getSafetyCommitByCompanyCode/{companyCode}
 * @method get
 * @description 安全承诺明细
 */

export module ProductionHazardousChemicalsGetSafetyCommitByCompanyCodeCompanyCodeUsingGet {
  export type Operation = paths['/production/hazardousChemicals/getSafetyCommitByCompanyCode/{companyCode}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺明细
 * @url /production/hazardousChemicals/getSafetyCommitByCompanyCode/{companyCode}
 * @method get
 * @description 安全承诺明细
 */

export function productionHazardousChemicalsGetSafetyCommitByCompanyCodeCompanyCodeUsingGet(options:ProductionHazardousChemicalsGetSafetyCommitByCompanyCodeCompanyCodeUsingGet.Options):Promise<ProductionHazardousChemicalsGetSafetyCommitByCompanyCodeCompanyCodeUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/getSafetyCommitByCompanyCode/{companyCode}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据设备编码查询储罐设备信息
 * @url /production/hazardousChemicals/getTankEquipByCode/{equipCode}
 * @method post
 * @description 根据设备编码查询储罐设备信息
 */

export module ProductionHazardousChemicalsGetTankEquipByCodeEquipCodeUsingPost {
  export type Operation = paths['/production/hazardousChemicals/getTankEquipByCode/{equipCode}']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据设备编码查询储罐设备信息
 * @url /production/hazardousChemicals/getTankEquipByCode/{equipCode}
 * @method post
 * @description 根据设备编码查询储罐设备信息
 */

export function productionHazardousChemicalsGetTankEquipByCodeEquipCodeUsingPost(options:ProductionHazardousChemicalsGetTankEquipByCodeEquipCodeUsingPost.Options):Promise<ProductionHazardousChemicalsGetTankEquipByCodeEquipCodeUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/getTankEquipByCode/{equipCode}',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据设备编码查询设备指标列表
 * @url /production/hazardousChemicals/getTargetByEquipCode/{equipCode}
 * @method get
 * @description 根据设备编码查询设备指标列表
 */

export module ProductionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet {
  export type Operation = paths['/production/hazardousChemicals/getTargetByEquipCode/{equipCode}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据设备编码查询设备指标列表
 * @url /production/hazardousChemicals/getTargetByEquipCode/{equipCode}
 * @method get
 * @description 根据设备编码查询设备指标列表
 */

export function productionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet(options:ProductionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet.Options):Promise<ProductionHazardousChemicalsGetTargetByEquipCodeEquipCodeUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/getTargetByEquipCode/{equipCode}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 政府巡查
 * @url /production/hazardousChemicals/governmentPatrol
 * @method get
 * @description 政府巡查
 */

export module ProductionHazardousChemicalsGovernmentPatrolUsingGet {
  export type Operation = paths['/production/hazardousChemicals/governmentPatrol']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 政府巡查
 * @url /production/hazardousChemicals/governmentPatrol
 * @method get
 * @description 政府巡查
 */

export function productionHazardousChemicalsGovernmentPatrolUsingGet(options:ProductionHazardousChemicalsGovernmentPatrolUsingGet.Options):Promise<ProductionHazardousChemicalsGovernmentPatrolUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/governmentPatrol',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 政府巡查反馈
 * @url /production/hazardousChemicals/governmentPatrolFeedbackPage
 * @method post
 * @description 政府巡查反馈
 */

export module ProductionHazardousChemicalsGovernmentPatrolFeedbackPageUsingPost {
  export type Operation = paths['/production/hazardousChemicals/governmentPatrolFeedbackPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 政府巡查反馈
 * @url /production/hazardousChemicals/governmentPatrolFeedbackPage
 * @method post
 * @description 政府巡查反馈
 */

export function productionHazardousChemicalsGovernmentPatrolFeedbackPageUsingPost(options:ProductionHazardousChemicalsGovernmentPatrolFeedbackPageUsingPost.Options):Promise<ProductionHazardousChemicalsGovernmentPatrolFeedbackPageUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/governmentPatrolFeedbackPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 政府巡查反馈报告
 * @url /production/hazardousChemicals/governmentPatrolFeedbackReport/{id}
 * @method get
 * @description 政府巡查反馈报告
 */

export module ProductionHazardousChemicalsGovernmentPatrolFeedbackReportIdUsingGet {
  export type Operation = paths['/production/hazardousChemicals/governmentPatrolFeedbackReport/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 政府巡查反馈报告
 * @url /production/hazardousChemicals/governmentPatrolFeedbackReport/{id}
 * @method get
 * @description 政府巡查反馈报告
 */

export function productionHazardousChemicalsGovernmentPatrolFeedbackReportIdUsingGet(options:ProductionHazardousChemicalsGovernmentPatrolFeedbackReportIdUsingGet.Options):Promise<ProductionHazardousChemicalsGovernmentPatrolFeedbackReportIdUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/governmentPatrolFeedbackReport/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺市县等级统计
 * @url /production/hazardousChemicals/hazardCityCommitmentStatistic/{commitDate}
 * @method get
 * @description 安全承诺市县等级统计
 */

export module ProductionHazardousChemicalsHazardCityCommitmentStatisticCommitDateUsingGet {
  export type Operation = paths['/production/hazardousChemicals/hazardCityCommitmentStatistic/{commitDate}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺市县等级统计
 * @url /production/hazardousChemicals/hazardCityCommitmentStatistic/{commitDate}
 * @method get
 * @description 安全承诺市县等级统计
 */

export function productionHazardousChemicalsHazardCityCommitmentStatisticCommitDateUsingGet(options:ProductionHazardousChemicalsHazardCityCommitmentStatisticCommitDateUsingGet.Options):Promise<ProductionHazardousChemicalsHazardCityCommitmentStatisticCommitDateUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/hazardCityCommitmentStatistic/{commitDate}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 危险源市县等级统计
 * @url /production/hazardousChemicals/hazardCityRankStatistic
 * @method get
 * @description 危险源市县等级统计
 */

export module ProductionHazardousChemicalsHazardCityRankStatisticUsingGet {
  export type Operation = paths['/production/hazardousChemicals/hazardCityRankStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 危险源市县等级统计
 * @url /production/hazardousChemicals/hazardCityRankStatistic
 * @method get
 * @description 危险源市县等级统计
 */

export function productionHazardousChemicalsHazardCityRankStatisticUsingGet(options:ProductionHazardousChemicalsHazardCityRankStatisticUsingGet.Options):Promise<ProductionHazardousChemicalsHazardCityRankStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/hazardCityRankStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 风险构成
 * @url /production/hazardousChemicals/riskProfile/{companyCode}
 * @method get
 * @description 风险构成
 */

export module ProductionHazardousChemicalsRiskProfileCompanyCodeUsingGet {
  export type Operation = paths['/production/hazardousChemicals/riskProfile/{companyCode}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 风险构成
 * @url /production/hazardousChemicals/riskProfile/{companyCode}
 * @method get
 * @description 风险构成
 */

export function productionHazardousChemicalsRiskProfileCompanyCodeUsingGet(options:ProductionHazardousChemicalsRiskProfileCompanyCodeUsingGet.Options):Promise<ProductionHazardousChemicalsRiskProfileCompanyCodeUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/riskProfile/{companyCode}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺排名
 * @url /production/hazardousChemicals/statisticByCommitment
 * @method get
 * @description 安全承诺排名
 */

export module ProductionHazardousChemicalsStatisticByCommitmentUsingGet {
  export type Operation = paths['/production/hazardousChemicals/statisticByCommitment']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 安全承诺排名
 * @url /production/hazardousChemicals/statisticByCommitment
 * @method get
 * @description 安全承诺排名
 */

export function productionHazardousChemicalsStatisticByCommitmentUsingGet(options:ProductionHazardousChemicalsStatisticByCommitmentUsingGet.Options):Promise<ProductionHazardousChemicalsStatisticByCommitmentUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/statisticByCommitment',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据字段属性维度统计企业数量 1-企业规模统计 2-企业类型统计 3-经济类型 4-所属行业统计 5-风险等级 6-市县统计
 * @url /production/hazardousChemicals/statisticByKey
 * @method post
 * @description 根据字段属性维度统计企业数量
 */

export module ProductionHazardousChemicalsStatisticByKeyUsingPost {
  export type Operation = paths['/production/hazardousChemicals/statisticByKey']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 根据字段属性维度统计企业数量 1-企业规模统计 2-企业类型统计 3-经济类型 4-所属行业统计 5-风险等级 6-市县统计
 * @url /production/hazardousChemicals/statisticByKey
 * @method post
 * @description 根据字段属性维度统计企业数量
 */

export function productionHazardousChemicalsStatisticByKeyUsingPost(options:ProductionHazardousChemicalsStatisticByKeyUsingPost.Options):Promise<ProductionHazardousChemicalsStatisticByKeyUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/statisticByKey',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 当日承诺数量统计
 * @url /production/hazardousChemicals/statisticNowCommitmentNum
 * @method get
 * @description 当日承诺数量统计
 */

export module ProductionHazardousChemicalsStatisticNowCommitmentNumUsingGet {
  export type Operation = paths['/production/hazardousChemicals/statisticNowCommitmentNum']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 当日承诺数量统计
 * @url /production/hazardousChemicals/statisticNowCommitmentNum
 * @method get
 * @description 当日承诺数量统计
 */

export function productionHazardousChemicalsStatisticNowCommitmentNumUsingGet(options:ProductionHazardousChemicalsStatisticNowCommitmentNumUsingGet.Options):Promise<ProductionHazardousChemicalsStatisticNowCommitmentNumUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/statisticNowCommitmentNum',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 查询企业视频关联信息
 * @url /production/hazardousChemicals/videoList
 * @method post
 * @description 查询企业视频关联信息
 */

export module ProductionHazardousChemicalsVideoListUsingPost {
  export type Operation = paths['/production/hazardousChemicals/videoList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 查询企业视频关联信息
 * @url /production/hazardousChemicals/videoList
 * @method post
 * @description 查询企业视频关联信息
 */

export function productionHazardousChemicalsVideoListUsingPost(options:ProductionHazardousChemicalsVideoListUsingPost.Options):Promise<ProductionHazardousChemicalsVideoListUsingPost.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/videoList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 预警事件(近24小时内的数据)
 * @url /production/hazardousChemicals/waringEvent
 * @method get
 * @description 预警事件(近24小时内的数据)
 */

export module ProductionHazardousChemicalsWaringEventUsingGet {
  export type Operation = paths['/production/hazardousChemicals/waringEvent']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-危险化学品专题
 * @summary 预警事件(近24小时内的数据)
 * @url /production/hazardousChemicals/waringEvent
 * @method get
 * @description 预警事件(近24小时内的数据)
 */

export function productionHazardousChemicalsWaringEventUsingGet(options:ProductionHazardousChemicalsWaringEventUsingGet.Options):Promise<ProductionHazardousChemicalsWaringEventUsingGet.Result> {
  return bdvRequest({
    url:'/production/hazardousChemicals/waringEvent',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-全省安全生产重大隐患排查专题
 * @summary 属性统计：1-市县统计 2-行业统计
 * @url /production/hiddenHazardousPatrol/fieldStatistic/{fieldKey}
 * @method get
 * @description 属性统计：1-市县统计 2-行业统计
 */

export module ProductionHiddenHazardousPatrolFieldStatisticFieldKeyUsingGet {
  export type Operation = paths['/production/hiddenHazardousPatrol/fieldStatistic/{fieldKey}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-全省安全生产重大隐患排查专题
 * @summary 属性统计：1-市县统计 2-行业统计
 * @url /production/hiddenHazardousPatrol/fieldStatistic/{fieldKey}
 * @method get
 * @description 属性统计：1-市县统计 2-行业统计
 */

export function productionHiddenHazardousPatrolFieldStatisticFieldKeyUsingGet(options:ProductionHiddenHazardousPatrolFieldStatisticFieldKeyUsingGet.Options):Promise<ProductionHiddenHazardousPatrolFieldStatisticFieldKeyUsingGet.Result> {
  return bdvRequest({
    url:'/production/hiddenHazardousPatrol/fieldStatistic/{fieldKey}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-全省安全生产重大隐患排查专题
 * @summary 查询详情
 * @url /production/hiddenHazardousPatrol/getById/{id}
 * @method get
 * @description 查询详情
 */

export module ProductionHiddenHazardousPatrolGetByIdIdUsingGet {
  export type Operation = paths['/production/hiddenHazardousPatrol/getById/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-全省安全生产重大隐患排查专题
 * @summary 查询详情
 * @url /production/hiddenHazardousPatrol/getById/{id}
 * @method get
 * @description 查询详情
 */

export function productionHiddenHazardousPatrolGetByIdIdUsingGet(options:ProductionHiddenHazardousPatrolGetByIdIdUsingGet.Options):Promise<ProductionHiddenHazardousPatrolGetByIdIdUsingGet.Result> {
  return bdvRequest({
    url:'/production/hiddenHazardousPatrol/getById/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-全省安全生产重大隐患排查专题
 * @summary 点位列表查询
 * @url /production/hiddenHazardousPatrol/listPoints
 * @method post
 * @description 点位列表查询
 */

export module ProductionHiddenHazardousPatrolListPointsUsingPost {
  export type Operation = paths['/production/hiddenHazardousPatrol/listPoints']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-全省安全生产重大隐患排查专题
 * @summary 点位列表查询
 * @url /production/hiddenHazardousPatrol/listPoints
 * @method post
 * @description 点位列表查询
 */

export function productionHiddenHazardousPatrolListPointsUsingPost(options:ProductionHiddenHazardousPatrolListPointsUsingPost.Options):Promise<ProductionHiddenHazardousPatrolListPointsUsingPost.Result> {
  return bdvRequest({
    url:'/production/hiddenHazardousPatrol/listPoints',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 下载信息
 * @url /production/industrialTrade/download
 * @method post
 * @description 下载信息
 */

export module ProductionIndustrialTradeDownloadUsingPost {
  export type Operation = paths['/production/industrialTrade/download']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 下载信息
 * @url /production/industrialTrade/download
 * @method post
 * @description 下载信息
 */

export function productionIndustrialTradeDownloadUsingPost(options:ProductionIndustrialTradeDownloadUsingPost.Options):Promise<ProductionIndustrialTradeDownloadUsingPost.Result> {
  return bdvRequest({
    url:'/production/industrialTrade/download',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 属性统计：1-市县统计 2-规模统计 3-类型统计
 * @url /production/industrialTrade/fieldStatistic/{fieldKey}
 * @method get
 * @description 属性统计
 */

export module ProductionIndustrialTradeFieldStatisticFieldKeyUsingGet {
  export type Operation = paths['/production/industrialTrade/fieldStatistic/{fieldKey}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 属性统计：1-市县统计 2-规模统计 3-类型统计
 * @url /production/industrialTrade/fieldStatistic/{fieldKey}
 * @method get
 * @description 属性统计
 */

export function productionIndustrialTradeFieldStatisticFieldKeyUsingGet(options:ProductionIndustrialTradeFieldStatisticFieldKeyUsingGet.Options):Promise<ProductionIndustrialTradeFieldStatisticFieldKeyUsingGet.Result> {
  return bdvRequest({
    url:'/production/industrialTrade/fieldStatistic/{fieldKey}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 查询明细
 * @url /production/industrialTrade/getBySerialNo/{serialNo}
 * @method get
 * @description 查询明细
 */

export module ProductionIndustrialTradeGetBySerialNoSerialNoUsingGet {
  export type Operation = paths['/production/industrialTrade/getBySerialNo/{serialNo}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 查询明细
 * @url /production/industrialTrade/getBySerialNo/{serialNo}
 * @method get
 * @description 查询明细
 */

export function productionIndustrialTradeGetBySerialNoSerialNoUsingGet(options:ProductionIndustrialTradeGetBySerialNoSerialNoUsingGet.Options):Promise<ProductionIndustrialTradeGetBySerialNoSerialNoUsingGet.Result> {
  return bdvRequest({
    url:'/production/industrialTrade/getBySerialNo/{serialNo}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 列表查询
 * @url /production/industrialTrade/list
 * @method post
 * @description 列表查询
 */

export module ProductionIndustrialTradeListUsingPost {
  export type Operation = paths['/production/industrialTrade/list']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 列表查询
 * @url /production/industrialTrade/list
 * @method post
 * @description 列表查询
 */

export function productionIndustrialTradeListUsingPost(options:ProductionIndustrialTradeListUsingPost.Options):Promise<ProductionIndustrialTradeListUsingPost.Result> {
  return bdvRequest({
    url:'/production/industrialTrade/list',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 分页查询
 * @url /production/industrialTrade/listPage
 * @method post
 * @description 分页查询
 */

export module ProductionIndustrialTradeListPageUsingPost {
  export type Operation = paths['/production/industrialTrade/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 分页查询
 * @url /production/industrialTrade/listPage
 * @method post
 * @description 分页查询
 */

export function productionIndustrialTradeListPageUsingPost(options:ProductionIndustrialTradeListPageUsingPost.Options):Promise<ProductionIndustrialTradeListPageUsingPost.Result> {
  return bdvRequest({
    url:'/production/industrialTrade/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 点位列表查询
 * @url /production/industrialTrade/listPoints
 * @method post
 * @description 点位列表查询
 */

export module ProductionIndustrialTradeListPointsUsingPost {
  export type Operation = paths['/production/industrialTrade/listPoints']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-2022版海南省工贸企业基本数据
 * @summary 点位列表查询
 * @url /production/industrialTrade/listPoints
 * @method post
 * @description 点位列表查询
 */

export function productionIndustrialTradeListPointsUsingPost(options:ProductionIndustrialTradeListPointsUsingPost.Options):Promise<ProductionIndustrialTradeListPointsUsingPost.Result> {
  return bdvRequest({
    url:'/production/industrialTrade/listPoints',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 报警趋势分析:1-今天 2-本周 3-本月
 * @url /production/nonCoalMine/alarmTrendStatistic/{key}
 * @method get
 * @description 报警趋势分析
 */

export module ProductionNonCoalMineAlarmTrendStatisticKeyUsingGet {
  export type Operation = paths['/production/nonCoalMine/alarmTrendStatistic/{key}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 报警趋势分析:1-今天 2-本周 3-本月
 * @url /production/nonCoalMine/alarmTrendStatistic/{key}
 * @method get
 * @description 报警趋势分析
 */

export function productionNonCoalMineAlarmTrendStatisticKeyUsingGet(options:ProductionNonCoalMineAlarmTrendStatisticKeyUsingGet.Options):Promise<ProductionNonCoalMineAlarmTrendStatisticKeyUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/alarmTrendStatistic/{key}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 证书统计
 * @url /production/nonCoalMine/certificateStatistic
 * @method get
 * @description 证书统计
 */

export module ProductionNonCoalMineCertificateStatisticUsingGet {
  export type Operation = paths['/production/nonCoalMine/certificateStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 证书统计
 * @url /production/nonCoalMine/certificateStatistic
 * @method get
 * @description 证书统计
 */

export function productionNonCoalMineCertificateStatisticUsingGet(options:ProductionNonCoalMineCertificateStatisticUsingGet.Options):Promise<ProductionNonCoalMineCertificateStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/certificateStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 企业规模统计
 * @url /production/nonCoalMine/companyScaleStatistic
 * @method get
 * @description 企业规模统计
 */

export module ProductionNonCoalMineCompanyScaleStatisticUsingGet {
  export type Operation = paths['/production/nonCoalMine/companyScaleStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 企业规模统计
 * @url /production/nonCoalMine/companyScaleStatistic
 * @method get
 * @description 企业规模统计
 */

export function productionNonCoalMineCompanyScaleStatisticUsingGet(options:ProductionNonCoalMineCompanyScaleStatisticUsingGet.Options):Promise<ProductionNonCoalMineCompanyScaleStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/companyScaleStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 下井人员定位预警
 * @url /production/nonCoalMine/downHolePersonnelList
 * @method get
 * @description 下井人员定位预警
 */

export module ProductionNonCoalMineDownHolePersonnelListUsingGet {
  export type Operation = paths['/production/nonCoalMine/downHolePersonnelList']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 下井人员定位预警
 * @url /production/nonCoalMine/downHolePersonnelList
 * @method get
 * @description 下井人员定位预警
 */

export function productionNonCoalMineDownHolePersonnelListUsingGet(options:ProductionNonCoalMineDownHolePersonnelListUsingGet.Options):Promise<ProductionNonCoalMineDownHolePersonnelListUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/downHolePersonnelList',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 下载信息
 * @url /production/nonCoalMine/download
 * @method post
 * @description 下载信息
 */

export module ProductionNonCoalMineDownloadUsingPost {
  export type Operation = paths['/production/nonCoalMine/download']['post'];
  export type Result = any;
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 下载信息
 * @url /production/nonCoalMine/download
 * @method post
 * @description 下载信息
 */

export function productionNonCoalMineDownloadUsingPost(options:ProductionNonCoalMineDownloadUsingPost.Options):Promise<ProductionNonCoalMineDownloadUsingPost.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/download',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 属性统计：1-市县统计 2-规模统计 3-类型统计
 * @url /production/nonCoalMine/fieldStatistic/{field}
 * @method get
 * @description 属性统计
 */

export module ProductionNonCoalMineFieldStatisticFieldUsingGet {
  export type Operation = paths['/production/nonCoalMine/fieldStatistic/{field}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 属性统计：1-市县统计 2-规模统计 3-类型统计
 * @url /production/nonCoalMine/fieldStatistic/{field}
 * @method get
 * @description 属性统计
 */

export function productionNonCoalMineFieldStatisticFieldUsingGet(options:ProductionNonCoalMineFieldStatisticFieldUsingGet.Options):Promise<ProductionNonCoalMineFieldStatisticFieldUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/fieldStatistic/{field}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 查询明细
 * @url /production/nonCoalMine/getBySerialNo/{serialNo}
 * @method get
 * @description 查询明细
 */

export module ProductionNonCoalMineGetBySerialNoSerialNoUsingGet {
  export type Operation = paths['/production/nonCoalMine/getBySerialNo/{serialNo}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 查询明细
 * @url /production/nonCoalMine/getBySerialNo/{serialNo}
 * @method get
 * @description 查询明细
 */

export function productionNonCoalMineGetBySerialNoSerialNoUsingGet(options:ProductionNonCoalMineGetBySerialNoSerialNoUsingGet.Options):Promise<ProductionNonCoalMineGetBySerialNoSerialNoUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/getBySerialNo/{serialNo}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 分页查询
 * @url /production/nonCoalMine/listPage
 * @method post
 * @description 分页查询
 */

export module ProductionNonCoalMineListPageUsingPost {
  export type Operation = paths['/production/nonCoalMine/listPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 分页查询
 * @url /production/nonCoalMine/listPage
 * @method post
 * @description 分页查询
 */

export function productionNonCoalMineListPageUsingPost(options:ProductionNonCoalMineListPageUsingPost.Options):Promise<ProductionNonCoalMineListPageUsingPost.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/listPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 矿山个数统计
 * @url /production/nonCoalMine/mineCountStatistic
 * @method get
 * @description 矿山个数统计
 */

export module ProductionNonCoalMineMineCountStatisticUsingGet {
  export type Operation = paths['/production/nonCoalMine/mineCountStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 矿山个数统计
 * @url /production/nonCoalMine/mineCountStatistic
 * @method get
 * @description 矿山个数统计
 */

export function productionNonCoalMineMineCountStatisticUsingGet(options:ProductionNonCoalMineMineCountStatisticUsingGet.Options):Promise<ProductionNonCoalMineMineCountStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/mineCountStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 矿山详情
 * @url /production/nonCoalMine/mineDetail/{id}
 * @method get
 * @description 矿山详情
 */

export module ProductionNonCoalMineMineDetailIdUsingGet {
  export type Operation = paths['/production/nonCoalMine/mineDetail/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 矿山详情
 * @url /production/nonCoalMine/mineDetail/{id}
 * @method get
 * @description 矿山详情
 */

export function productionNonCoalMineMineDetailIdUsingGet(options:ProductionNonCoalMineMineDetailIdUsingGet.Options):Promise<ProductionNonCoalMineMineDetailIdUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/mineDetail/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 矿山预警统计
 * @url /production/nonCoalMine/mineWaringStatistic
 * @method get
 * @description 矿山预警统计
 */

export module ProductionNonCoalMineMineWaringStatisticUsingGet {
  export type Operation = paths['/production/nonCoalMine/mineWaringStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 矿山预警统计
 * @url /production/nonCoalMine/mineWaringStatistic
 * @method get
 * @description 矿山预警统计
 */

export function productionNonCoalMineMineWaringStatisticUsingGet(options:ProductionNonCoalMineMineWaringStatisticUsingGet.Options):Promise<ProductionNonCoalMineMineWaringStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/mineWaringStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 上球列表
 * @url /production/nonCoalMine/pointsList
 * @method post
 * @description 上球列表
 */

export module ProductionNonCoalMinePointsListUsingPost {
  export type Operation = paths['/production/nonCoalMine/pointsList']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 上球列表
 * @url /production/nonCoalMine/pointsList
 * @method post
 * @description 上球列表
 */

export function productionNonCoalMinePointsListUsingPost(options:ProductionNonCoalMinePointsListUsingPost.Options):Promise<ProductionNonCoalMinePointsListUsingPost.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/pointsList',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿库详情
 * @url /production/nonCoalMine/tailingDetail/{id}
 * @method get
 * @description 尾矿库详情
 */

export module ProductionNonCoalMineTailingDetailIdUsingGet {
  export type Operation = paths['/production/nonCoalMine/tailingDetail/{id}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿库详情
 * @url /production/nonCoalMine/tailingDetail/{id}
 * @method get
 * @description 尾矿库详情
 */

export function productionNonCoalMineTailingDetailIdUsingGet(options:ProductionNonCoalMineTailingDetailIdUsingGet.Options):Promise<ProductionNonCoalMineTailingDetailIdUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/tailingDetail/{id}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿库等级统计
 * @url /production/nonCoalMine/tailingsLevelStatistic
 * @method get
 * @description 尾矿库等级统计
 */

export module ProductionNonCoalMineTailingsLevelStatisticUsingGet {
  export type Operation = paths['/production/nonCoalMine/tailingsLevelStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿库等级统计
 * @url /production/nonCoalMine/tailingsLevelStatistic
 * @method get
 * @description 尾矿库等级统计
 */

export function productionNonCoalMineTailingsLevelStatisticUsingGet(options:ProductionNonCoalMineTailingsLevelStatisticUsingGet.Options):Promise<ProductionNonCoalMineTailingsLevelStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/tailingsLevelStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿库安全度统计
 * @url /production/nonCoalMine/tailingsSafeStatistic
 * @method get
 * @description 尾矿库安全度统计
 */

export module ProductionNonCoalMineTailingsSafeStatisticUsingGet {
  export type Operation = paths['/production/nonCoalMine/tailingsSafeStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿库安全度统计
 * @url /production/nonCoalMine/tailingsSafeStatistic
 * @method get
 * @description 尾矿库安全度统计
 */

export function productionNonCoalMineTailingsSafeStatisticUsingGet(options:ProductionNonCoalMineTailingsSafeStatisticUsingGet.Options):Promise<ProductionNonCoalMineTailingsSafeStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/tailingsSafeStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿预警统计
 * @url /production/nonCoalMine/tailingsWaringStatistic
 * @method get
 * @description 尾矿预警统计
 */

export module ProductionNonCoalMineTailingsWaringStatisticUsingGet {
  export type Operation = paths['/production/nonCoalMine/tailingsWaringStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 尾矿预警统计
 * @url /production/nonCoalMine/tailingsWaringStatistic
 * @method get
 * @description 尾矿预警统计
 */

export function productionNonCoalMineTailingsWaringStatisticUsingGet(options:ProductionNonCoalMineTailingsWaringStatisticUsingGet.Options):Promise<ProductionNonCoalMineTailingsWaringStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/tailingsWaringStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 视屏分页查询
 * @url /production/nonCoalMine/videoListPage
 * @method post
 * @description 视屏分页查询
 */

export module ProductionNonCoalMineVideoListPageUsingPost {
  export type Operation = paths['/production/nonCoalMine/videoListPage']['post'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Body = Required<Operation>['requestBody']['content']['application/json'];
  export interface Options {
    [key: string]: unknown;
    data: Body;
  };
}

/**
 * @tag 安全生产-非煤矿山专题
 * @summary 视屏分页查询
 * @url /production/nonCoalMine/videoListPage
 * @method post
 * @description 视屏分页查询
 */

export function productionNonCoalMineVideoListPageUsingPost(options:ProductionNonCoalMineVideoListPageUsingPost.Options):Promise<ProductionNonCoalMineVideoListPageUsingPost.Result> {
  return bdvRequest({
    url:'/production/nonCoalMine/videoListPage',
    method:'post',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 实时监测预警
 * @url /production/subjectResponsibility/actualTimeWaring
 * @method get
 * @description 实时监测预警
 */

export module ProductionSubjectResponsibilityActualTimeWaringUsingGet {
  export type Operation = paths['/production/subjectResponsibility/actualTimeWaring']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 实时监测预警
 * @url /production/subjectResponsibility/actualTimeWaring
 * @method get
 * @description 实时监测预警
 */

export function productionSubjectResponsibilityActualTimeWaringUsingGet(options:ProductionSubjectResponsibilityActualTimeWaringUsingGet.Options):Promise<ProductionSubjectResponsibilityActualTimeWaringUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/actualTimeWaring',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 危化企业/非煤矿山企业市县统计
 * @url /production/subjectResponsibility/chemicalsMineCityStatistic
 * @method get
 * @description 危化企业/非煤矿山企业市县统计
 */

export module ProductionSubjectResponsibilityChemicalsMineCityStatisticUsingGet {
  export type Operation = paths['/production/subjectResponsibility/chemicalsMineCityStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 危化企业/非煤矿山企业市县统计
 * @url /production/subjectResponsibility/chemicalsMineCityStatistic
 * @method get
 * @description 危化企业/非煤矿山企业市县统计
 */

export function productionSubjectResponsibilityChemicalsMineCityStatisticUsingGet(options:ProductionSubjectResponsibilityChemicalsMineCityStatisticUsingGet.Options):Promise<ProductionSubjectResponsibilityChemicalsMineCityStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/chemicalsMineCityStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 危化企业/非煤矿山企业统计
 * @url /production/subjectResponsibility/chemicalsMineStatistic
 * @method get
 * @description 危化企业/非煤矿山企业统计
 */

export module ProductionSubjectResponsibilityChemicalsMineStatisticUsingGet {
  export type Operation = paths['/production/subjectResponsibility/chemicalsMineStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 危化企业/非煤矿山企业统计
 * @url /production/subjectResponsibility/chemicalsMineStatistic
 * @method get
 * @description 危化企业/非煤矿山企业统计
 */

export function productionSubjectResponsibilityChemicalsMineStatisticUsingGet(options:ProductionSubjectResponsibilityChemicalsMineStatisticUsingGet.Options):Promise<ProductionSubjectResponsibilityChemicalsMineStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/chemicalsMineStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 危化企业统计
 * @url /production/subjectResponsibility/chemicalsStatistic
 * @method get
 * @description 危化企业统计
 */

export module ProductionSubjectResponsibilityChemicalsStatisticUsingGet {
  export type Operation = paths['/production/subjectResponsibility/chemicalsStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 危化企业统计
 * @url /production/subjectResponsibility/chemicalsStatistic
 * @method get
 * @description 危化企业统计
 */

export function productionSubjectResponsibilityChemicalsStatisticUsingGet(options:ProductionSubjectResponsibilityChemicalsStatisticUsingGet.Options):Promise<ProductionSubjectResponsibilityChemicalsStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/chemicalsStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 动态监管数据分析：1-物联设备 2-视频设备
 * @url /production/subjectResponsibility/dynamicDataAnalysis/{key}
 * @method get
 * @description 动态监管数据分析
 */

export module ProductionSubjectResponsibilityDynamicDataAnalysisKeyUsingGet {
  export type Operation = paths['/production/subjectResponsibility/dynamicDataAnalysis/{key}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 动态监管数据分析：1-物联设备 2-视频设备
 * @url /production/subjectResponsibility/dynamicDataAnalysis/{key}
 * @method get
 * @description 动态监管数据分析
 */

export function productionSubjectResponsibilityDynamicDataAnalysisKeyUsingGet(options:ProductionSubjectResponsibilityDynamicDataAnalysisKeyUsingGet.Options):Promise<ProductionSubjectResponsibilityDynamicDataAnalysisKeyUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/dynamicDataAnalysis/{key}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 本月责任落实预警
 * @url /production/subjectResponsibility/entResponsibilityStatistic
 * @method get
 * @description 本月责任落实预警
 */

export module ProductionSubjectResponsibilityEntResponsibilityStatisticUsingGet {
  export type Operation = paths['/production/subjectResponsibility/entResponsibilityStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 本月责任落实预警
 * @url /production/subjectResponsibility/entResponsibilityStatistic
 * @method get
 * @description 本月责任落实预警
 */

export function productionSubjectResponsibilityEntResponsibilityStatisticUsingGet(options:ProductionSubjectResponsibilityEntResponsibilityStatisticUsingGet.Options):Promise<ProductionSubjectResponsibilityEntResponsibilityStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/entResponsibilityStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 隐患排查统计
 * @url /production/subjectResponsibility/hiddenDangerCheckStatistic
 * @method get
 * @description 隐患排查统计
 */

export module ProductionSubjectResponsibilityHiddenDangerCheckStatisticUsingGet {
  export type Operation = paths['/production/subjectResponsibility/hiddenDangerCheckStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 隐患排查统计
 * @url /production/subjectResponsibility/hiddenDangerCheckStatistic
 * @method get
 * @description 隐患排查统计
 */

export function productionSubjectResponsibilityHiddenDangerCheckStatisticUsingGet(options:ProductionSubjectResponsibilityHiddenDangerCheckStatisticUsingGet.Options):Promise<ProductionSubjectResponsibilityHiddenDangerCheckStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/hiddenDangerCheckStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 隐患排查整改率
 * @url /production/subjectResponsibility/hiddenDangerRate
 * @method get
 * @description 隐患排查整改率
 */

export module ProductionSubjectResponsibilityHiddenDangerRateUsingGet {
  export type Operation = paths['/production/subjectResponsibility/hiddenDangerRate']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 隐患排查整改率
 * @url /production/subjectResponsibility/hiddenDangerRate
 * @method get
 * @description 隐患排查整改率
 */

export function productionSubjectResponsibilityHiddenDangerRateUsingGet(options:ProductionSubjectResponsibilityHiddenDangerRateUsingGet.Options):Promise<ProductionSubjectResponsibilityHiddenDangerRateUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/hiddenDangerRate',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 非煤矿山统计
 * @url /production/subjectResponsibility/mineStatistic
 * @method get
 * @description 非煤矿山统计
 */

export module ProductionSubjectResponsibilityMineStatisticUsingGet {
  export type Operation = paths['/production/subjectResponsibility/mineStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 非煤矿山统计
 * @url /production/subjectResponsibility/mineStatistic
 * @method get
 * @description 非煤矿山统计
 */

export function productionSubjectResponsibilityMineStatisticUsingGet(options:ProductionSubjectResponsibilityMineStatisticUsingGet.Options):Promise<ProductionSubjectResponsibilityMineStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/mineStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 月度企业报警排行
 * @url /production/subjectResponsibility/monthlyEnterpriseAlarmRanking
 * @method get
 * @description 月度企业报警排行
 */

export module ProductionSubjectResponsibilityMonthlyEnterpriseAlarmRankingUsingGet {
  export type Operation = paths['/production/subjectResponsibility/monthlyEnterpriseAlarmRanking']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 月度企业报警排行
 * @url /production/subjectResponsibility/monthlyEnterpriseAlarmRanking
 * @method get
 * @description 月度企业报警排行
 */

export function productionSubjectResponsibilityMonthlyEnterpriseAlarmRankingUsingGet(options:ProductionSubjectResponsibilityMonthlyEnterpriseAlarmRankingUsingGet.Options):Promise<ProductionSubjectResponsibilityMonthlyEnterpriseAlarmRankingUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/monthlyEnterpriseAlarmRanking',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 红黑榜 1-红榜 2-黑榜
 * @url /production/subjectResponsibility/redBlackSortList/{key}
 * @method get
 * @description 红黑榜
 */

export module ProductionSubjectResponsibilityRedBlackSortListKeyUsingGet {
  export type Operation = paths['/production/subjectResponsibility/redBlackSortList/{key}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 红黑榜 1-红榜 2-黑榜
 * @url /production/subjectResponsibility/redBlackSortList/{key}
 * @method get
 * @description 红黑榜
 */

export function productionSubjectResponsibilityRedBlackSortListKeyUsingGet(options:ProductionSubjectResponsibilityRedBlackSortListKeyUsingGet.Options):Promise<ProductionSubjectResponsibilityRedBlackSortListKeyUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/redBlackSortList/{key}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 责任落实区域分析
 * @url /production/subjectResponsibility/regionResponsibilityStatistic
 * @method get
 * @description 责任落实区域分析
 */

export module ProductionSubjectResponsibilityRegionResponsibilityStatisticUsingGet {
  export type Operation = paths['/production/subjectResponsibility/regionResponsibilityStatistic']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 责任落实区域分析
 * @url /production/subjectResponsibility/regionResponsibilityStatistic
 * @method get
 * @description 责任落实区域分析
 */

export function productionSubjectResponsibilityRegionResponsibilityStatisticUsingGet(options:ProductionSubjectResponsibilityRegionResponsibilityStatisticUsingGet.Options):Promise<ProductionSubjectResponsibilityRegionResponsibilityStatisticUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/regionResponsibilityStatistic',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 风险趋势分析: 1-危化 2-矿山
 * @url /production/subjectResponsibility/riskTrendAnalysis/{key}
 * @method get
 * @description 风险趋势分析
 */

export module ProductionSubjectResponsibilityRiskTrendAnalysisKeyUsingGet {
  export type Operation = paths['/production/subjectResponsibility/riskTrendAnalysis/{key}']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export type Path = Operation['parameters']['path'];
  export interface Options {
    [key: string]: unknown;
    path: Path;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 风险趋势分析: 1-危化 2-矿山
 * @url /production/subjectResponsibility/riskTrendAnalysis/{key}
 * @method get
 * @description 风险趋势分析
 */

export function productionSubjectResponsibilityRiskTrendAnalysisKeyUsingGet(options:ProductionSubjectResponsibilityRiskTrendAnalysisKeyUsingGet.Options):Promise<ProductionSubjectResponsibilityRiskTrendAnalysisKeyUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/riskTrendAnalysis/{key}',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 当日预警
 * @url /production/subjectResponsibility/waringToday
 * @method get
 * @description 当日预警
 */

export module ProductionSubjectResponsibilityWaringTodayUsingGet {
  export type Operation = paths['/production/subjectResponsibility/waringToday']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 当日预警
 * @url /production/subjectResponsibility/waringToday
 * @method get
 * @description 当日预警
 */

export function productionSubjectResponsibilityWaringTodayUsingGet(options:ProductionSubjectResponsibilityWaringTodayUsingGet.Options):Promise<ProductionSubjectResponsibilityWaringTodayUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/waringToday',
    method:'get',
    ...options,
  });
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 当日预警类型占比
 * @url /production/subjectResponsibility/waringTypeRateToday
 * @method get
 * @description 当日预警类型占比
 */

export module ProductionSubjectResponsibilityWaringTypeRateTodayUsingGet {
  export type Operation = paths['/production/subjectResponsibility/waringTypeRateToday']['get'];
  export type Result = Required<Operation>['responses']['200']['content']['*/*'];
  export interface Options {
    [key: string]: unknown;
  };
}

/**
 * @tag 安全生产-主体责任专题
 * @summary 当日预警类型占比
 * @url /production/subjectResponsibility/waringTypeRateToday
 * @method get
 * @description 当日预警类型占比
 */

export function productionSubjectResponsibilityWaringTypeRateTodayUsingGet(options:ProductionSubjectResponsibilityWaringTypeRateTodayUsingGet.Options):Promise<ProductionSubjectResponsibilityWaringTypeRateTodayUsingGet.Result> {
  return bdvRequest({
    url:'/production/subjectResponsibility/waringTypeRateToday',
    method:'get',
    ...options,
  });
}
export interface paths {
    "/production/accident/accidentTypeStatistic/{isShowAll}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 按事件类型统计: 0-收缩 1-展示所有
         * @description 按事件类型统计
         */
        get: operations["accidentTypeStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/bestAccidentYearStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 特大事故年度统计
         * @description 特大事故年度统计
         */
        get: operations["bestAccidentYearStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/dayReport/{day}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 日报数据查询:yyyy-MM-dd
         * @description 日报数据查询
         */
        get: operations["dayReportUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/dayReportDownload/{day}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** 日报数据下载:yyyy-MM-dd */
        get: operations["dayReportDownloadUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/dieNumYearStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 死亡人数年度统计
         * @description 死亡人数年度统计
         */
        get: operations["dieNumYearStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/fieldStatistic/{fieldKey}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 属性统计：1-市县统计 2-等级统计 3-月份统计
         * @description 属性统计：1-市县统计 2-等级统计 3-月份统计
         */
        get: operations["fieldStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/gridPointList/{geoHash}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * geoHash事故网格点位明细
         * @description geoHash事故网格点位明细
         */
        get: operations["gridPointListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/gridPointStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * geoHash事故网格点位聚合统计
         * @description geoHash事故网格点位聚合统计
         */
        post: operations["gridPointStatisticUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 列表查询
         * @description 列表查询
         */
        post: operations["getListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询
         * @description 分页查询
         */
        post: operations["listPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/listPoints": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 点位列表查询
         * @description 点位列表查询
         */
        post: operations["listPointsUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/listRelatedUser/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 事故相关人员列表
         * @description 事故相关人员列表
         */
        get: operations["listRelatedUserUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/listResubmit/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 续报列表
         * @description 续报列表
         */
        get: operations["listResubmitUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/reduceLossesStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 减少损失统计
         * @description 减少损失统计
         */
        get: operations["reduceLossesStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/simpleReport": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 简报数据查询
         * @description 简报数据查询
         */
        post: operations["simpleReportUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/accident/simpleReportDownload": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 简报数据下载
         * @description 简报数据下载
         */
        post: operations["simpleReportDownloadUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/commitmentListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 安全承诺分页查询
         * @description 企业分页查询
         */
        post: operations["commitmentListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/companyDetail/{companyCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 企业详情查询
         * @description 企业详情查询
         */
        post: operations["companyDetailUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/companyList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 企业列表查询
         * @description 企业列表查询
         */
        post: operations["companyListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/companyListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 企业分页查询
         * @description 企业分页查询
         */
        post: operations["companyListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/getEquipListByHazardCode/{hazardCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 根据危险源编码查询设备列表
         * @description 根据危险源编码查询设备列表
         */
        post: operations["getEquipListByHazardCodeUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/getHazardListByCompanyCode/{companyCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据企业编码查询危险源列表
         * @description 根据企业编码查询危险源列表
         */
        get: operations["getHazardListByCompanyCodeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/getSafetyCommitByCompanyCode/{companyCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 安全承诺明细
         * @description 安全承诺明细
         */
        get: operations["getSafetyCommitByCompanyCodeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/getTankEquipByCode/{equipCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 根据设备编码查询储罐设备信息
         * @description 根据设备编码查询储罐设备信息
         */
        post: operations["getTankEquipByCodeUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/getTargetByEquipCode/{equipCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 根据设备编码查询设备指标列表
         * @description 根据设备编码查询设备指标列表
         */
        get: operations["getTargetByEquipCodeUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/governmentPatrol": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 政府巡查
         * @description 政府巡查
         */
        get: operations["governmentPatrolUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/governmentPatrolFeedbackPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 政府巡查反馈
         * @description 政府巡查反馈
         */
        post: operations["governmentPatrolFeedbackPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/governmentPatrolFeedbackReport/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 政府巡查反馈报告
         * @description 政府巡查反馈报告
         */
        get: operations["governmentPatrolFeedbackReportUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/hazardCityCommitmentStatistic/{commitDate}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 安全承诺市县等级统计
         * @description 安全承诺市县等级统计
         */
        get: operations["hazardCityCommitmentStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/hazardCityRankStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 危险源市县等级统计
         * @description 危险源市县等级统计
         */
        get: operations["hazardCityRankStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/riskProfile/{companyCode}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 风险构成
         * @description 风险构成
         */
        get: operations["riskProfileUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/statisticByCommitment": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 安全承诺排名
         * @description 安全承诺排名
         */
        get: operations["statisticByCommitmentUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/statisticByKey": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 根据字段属性维度统计企业数量 1-企业规模统计 2-企业类型统计 3-经济类型 4-所属行业统计 5-风险等级 6-市县统计
         * @description 根据字段属性维度统计企业数量
         */
        post: operations["statisticByKeyUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/statisticNowCommitmentNum": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 当日承诺数量统计
         * @description 当日承诺数量统计
         */
        get: operations["statisticNowCommitmentNumUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/videoList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 查询企业视频关联信息
         * @description 查询企业视频关联信息
         */
        post: operations["getVideoListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hazardousChemicals/waringEvent": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 预警事件(近24小时内的数据)
         * @description 预警事件(近24小时内的数据)
         */
        get: operations["waringEventUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hiddenHazardousPatrol/fieldStatistic/{fieldKey}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 属性统计：1-市县统计 2-行业统计
         * @description 属性统计：1-市县统计 2-行业统计
         */
        get: operations["fieldStatisticUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hiddenHazardousPatrol/getById/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 查询详情
         * @description 查询详情
         */
        get: operations["getByIdUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/hiddenHazardousPatrol/listPoints": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 点位列表查询
         * @description 点位列表查询
         */
        post: operations["listPointsUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/industrialTrade/download": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 下载信息
         * @description 下载信息
         */
        post: operations["downloadUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/industrialTrade/fieldStatistic/{fieldKey}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 属性统计：1-市县统计 2-规模统计 3-类型统计
         * @description 属性统计
         */
        get: operations["fieldStatisticUsingGET_2"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/industrialTrade/getBySerialNo/{serialNo}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 查询明细
         * @description 查询明细
         */
        get: operations["getBySerialNoUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/industrialTrade/list": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 列表查询
         * @description 列表查询
         */
        post: operations["getListUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/industrialTrade/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询
         * @description 分页查询
         */
        post: operations["listPageUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/industrialTrade/listPoints": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 点位列表查询
         * @description 点位列表查询
         */
        post: operations["listPointsUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/alarmTrendStatistic/{key}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 报警趋势分析:1-今天 2-本周 3-本月
         * @description 报警趋势分析
         */
        get: operations["alarmTrendStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/certificateStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 证书统计
         * @description 证书统计
         */
        get: operations["certificateStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/companyScaleStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 企业规模统计
         * @description 企业规模统计
         */
        get: operations["companyScaleStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/downHolePersonnelList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 下井人员定位预警
         * @description 下井人员定位预警
         */
        get: operations["downHolePersonnelListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/download": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 下载信息
         * @description 下载信息
         */
        post: operations["downloadUsingPOST_1"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/fieldStatistic/{field}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 属性统计：1-市县统计 2-规模统计 3-类型统计
         * @description 属性统计
         */
        get: operations["fieldStatisticUsingGET_3"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/getBySerialNo/{serialNo}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 查询明细
         * @description 查询明细
         */
        get: operations["getBySerialNoUsingGET_1"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/listPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 分页查询
         * @description 分页查询
         */
        post: operations["listPageUsingPOST_2"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/mineCountStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 矿山个数统计
         * @description 矿山个数统计
         */
        get: operations["mineCountStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/mineDetail/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 矿山详情
         * @description 矿山详情
         */
        get: operations["mineDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/mineWaringStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 矿山预警统计
         * @description 矿山预警统计
         */
        get: operations["mineWaringStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/pointsList": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 上球列表
         * @description 上球列表
         */
        post: operations["pointsListUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/tailingDetail/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 尾矿库详情
         * @description 尾矿库详情
         */
        get: operations["tailingDetailUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/tailingsLevelStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 尾矿库等级统计
         * @description 尾矿库等级统计
         */
        get: operations["tailingsLevelStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/tailingsSafeStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 尾矿库安全度统计
         * @description 尾矿库安全度统计
         */
        get: operations["tailingsSafeStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/tailingsWaringStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 尾矿预警统计
         * @description 尾矿预警统计
         */
        get: operations["tailingsWaringStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/nonCoalMine/videoListPage": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * 视屏分页查询
         * @description 视屏分页查询
         */
        post: operations["videoListPageUsingPOST"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/actualTimeWaring": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 实时监测预警
         * @description 实时监测预警
         */
        get: operations["actualTimeWaringUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/chemicalsMineCityStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 危化企业/非煤矿山企业市县统计
         * @description 危化企业/非煤矿山企业市县统计
         */
        get: operations["chemicalsMineCityStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/chemicalsMineStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 危化企业/非煤矿山企业统计
         * @description 危化企业/非煤矿山企业统计
         */
        get: operations["chemicalsMineStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/chemicalsStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 危化企业统计
         * @description 危化企业统计
         */
        get: operations["chemicalsStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/dynamicDataAnalysis/{key}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 动态监管数据分析：1-物联设备 2-视频设备
         * @description 动态监管数据分析
         */
        get: operations["dynamicDataAnalysisUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/entResponsibilityStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 本月责任落实预警
         * @description 本月责任落实预警
         */
        get: operations["entResponsibilityUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/hiddenDangerCheckStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 隐患排查统计
         * @description 隐患排查统计
         */
        get: operations["hiddenDangerCheckStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/hiddenDangerRate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 隐患排查整改率
         * @description 隐患排查整改率
         */
        get: operations["hiddenDangerRateUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/mineStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 非煤矿山统计
         * @description 非煤矿山统计
         */
        get: operations["mineStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/monthlyEnterpriseAlarmRanking": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 月度企业报警排行
         * @description 月度企业报警排行
         */
        get: operations["monthlyEnterpriseAlarmRankingUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/redBlackSortList/{key}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 红黑榜 1-红榜 2-黑榜
         * @description 红黑榜
         */
        get: operations["redBlackSortListUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/regionResponsibilityStatistic": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 责任落实区域分析
         * @description 责任落实区域分析
         */
        get: operations["regionResponsibilityStatisticUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/riskTrendAnalysis/{key}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 风险趋势分析: 1-危化 2-矿山
         * @description 风险趋势分析
         */
        get: operations["riskTrendAnalysisUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/waringToday": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 当日预警
         * @description 当日预警
         */
        get: operations["waringTodayUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/production/subjectResponsibility/waringTypeRateToday": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * 当日预警类型占比
         * @description 当日预警类型占比
         */
        get: operations["waringTypeRateTodayUsingGET"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        /**
         * AccidentDayReportDTO
         * @description 安全生产事故日报
         */
        AccidentDayReportDTO: {
            /** @description 行业图形统计 */
            industryChartStatisticDTOList?: components["schemas"]["AccidentIndustryChartStatisticDTO"][];
            /** @description 行业列表统计 */
            industryListStatisticDTOList?: components["schemas"]["IndustryListStatisticDTO"][];
            /** @description 去年 */
            lastYear?: string;
            /** @description 较大事故 */
            majorAccident?: components["schemas"]["IndustryListStatisticDTO"];
            /** @description 较大事故简要情况 */
            majorAccidentsDesc?: string;
            /** @description 较大事故年度图形统计 */
            majorAccidentsStatistic?: components["schemas"]["YearChartStatisticDTO"][];
            /** @description 前年 */
            preLastYear?: string;
            /** @description 统计日期区间描述 */
            statisticDateDesc?: string;
            /** @description 今年 */
            thisYear?: string;
            /** @description 总标题 */
            title?: string;
            /** @description 全省事故总体情况 */
            wholeProvinceDesc?: string;
        };
        /**
         * AccidentDTO
         * @description 安全生产事故信息
         */
        AccidentDTO: {
            /** @description 事故标识 */
            accidentFlag?: string;
            /** @description 事故简介 */
            accidentGeneral?: string;
            /** @description 事故等级 01- 一般事故 02-较大事故 03-重大事故 04-特别重大事故 */
            accidentLevel?: string;
            /** @description 事故类型 */
            accidentType?: string;
            /** @description 发生地址 */
            address?: string;
            /** @description 重伤人数 */
            bestInjuredNum?: string;
            /** @description 死亡人数 */
            dieNum?: string;
            /**
             * Format: date-time
             * @description 发生时间
             */
            happenTime?: string;
            /** @description 发生单位 */
            happenUnit?: string;
            /** @description 主键 */
            id?: string;
            /** @description 受伤人数 */
            injuredNum?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /**
             * Format: int32
             * @description 损失金额（万元）
             */
            loss?: number;
            /** @description 失踪人数 */
            missNum?: string;
            /** @description 事故原因 */
            reason?: string;
            /** @description 填报单位 */
            reportUnit?: string;
            /** @description 死亡人数合计 */
            totalDieNum?: string;
        };
        /**
         * AccidentIndustryChartStatisticDTO
         * @description 安全生产事故行业统计
         */
        AccidentIndustryChartStatisticDTO: {
            /**
             * Format: int64
             * @description 死亡
             */
            deathNum?: number;
            /**
             * Format: int64
             * @description 起数
             */
            eventNum?: number;
            /** @description 描述 */
            text?: string;
        };
        /**
         * AccidentQueryDTO
         * @description 安全生产事故查询对象
         */
        AccidentQueryDTO: {
            /**
             * @description 较大事故
             * @example false
             */
            accidentBastLevel?: boolean;
            /** @description 事故等级 */
            accidentLevel?: string;
            /** @description 事故类型 */
            accidentType?: string;
            /**
             * Format: double
             * @description 右下角纬度
             */
            bottomRightLatitude?: number;
            /**
             * Format: double
             * @description 右下角经度
             */
            bottomRightLongitude?: number;
            /** @description 发生时间-结束:yyyy-MM-dd */
            happenTimeEnd?: string;
            /** @description 发生时间-开始:yyyy-MM-dd */
            happenTimeStart?: string;
            /** @description 主键 */
            id?: string;
            /** @description 行业 */
            industry?: string;
            /** @description 父类ID */
            parentId?: string;
            /** @description 行政区划（市县） */
            regionCode?: string;
            /**
             * Format: double
             * @description 左上角纬度
             */
            topLeftLatitude?: number;
            /**
             * Format: double
             * @description 左上角经度
             */
            topLeftLongitude?: number;
            /**
             * Format: int64
             * @description 死亡超出人数
             */
            upDeathNum?: number;
            /**
             * Format: float
             * @description 损失超出金额
             */
            upLossNum?: number;
        };
        /**
         * AccidentRelatedUserDTO
         * @description 安全生产事故关联人员信息信息
         */
        AccidentRelatedUserDTO: {
            /** @description 年龄 */
            age?: string;
            /** @description 文化程度 */
            education?: string;
            /** @description 主键 */
            id?: string;
            /** @description 名称 */
            name?: string;
            /** @description 性别 */
            sex?: string;
            /** @description 状态 */
            status?: string;
        };
        /**
         * AccidentSimpleReportDTO
         * @description 安全生产事故简报
         */
        AccidentSimpleReportDTO: {
            /** @description 二、生产安全事故分项统计情况 */
            itemDesc?: string;
            /** @description 报告时间 */
            reportDate?: string;
            /** @description 序号 */
            reportNumber?: string;
            /** @description 标题 */
            reportTitle?: string;
            /** @description 报送单位 */
            reportUnit?: string;
            /** @description 一、生产安全事故总体情况 */
            totalDesc?: string;
        };
        /** AlarmRisk */
        AlarmRisk: {
            /** @description 报警点位数 */
            alarmPoints?: string;
            /** @description 重复报警点位数 */
            alarmPointsRepeat?: string;
            /** @description 平均报警时率 */
            avgAlarmRate?: string;
            /** @description 最大报警时长 */
            maxAlarmTime?: string;
            /** @description 监测点位数 */
            monitorPoints?: string;
            /** @description 评分 */
            score?: string;
        };
        /**
         * CertificateStatisticDTO
         * @description 证书统计
         */
        CertificateStatisticDTO: {
            /** @description 到期 */
            expire?: string;
            /** @description 2月后到期 */
            expireTwoMonthLater?: string;
            /** @description 正常 */
            normal?: string;
            /** @description 类型：1-安全生产管理人员 2-注册安全工程师 3-安全生产责任人 4-特种作业人员 */
            type?: string;
        };
        /**
         * 查询排序配置项
         * @description 查询排序配置项
         */
        ChaXunPaiXuPeiZhiXiang: {
            /**
             * @description 排序方式
             * @example ASC/asc;DESC/desc
             */
            orderType?: string;
            /** @description 属性名称 */
            propertyName?: string;
        };
        /**
         * ChemicalsMineCityStatisticDTO
         * @description 危化企业/非煤矿山企业市县统计
         */
        ChemicalsMineCityStatisticDTO: {
            /**
             * Format: int32
             * @description 危化企业
             */
            chemicalsNum?: number;
            /** @description 市县编码 */
            cityCode?: string;
            /** @description 市县名称 */
            cityName?: string;
            /**
             * Format: int32
             * @description 矿山企业
             */
            mineNum?: number;
        };
        /**
         * CommitmentDetailDTO
         * @description 安全承诺书明细
         */
        CommitmentDetailDTO: {
            /** @description 盲板作业 作业数量 */
            blindplateNumber?: string;
            /** @description 承诺时间 */
            commitDate?: string;
            /** @description 承诺人 */
            commitment?: string;
            /** @description 企业编码 查询主键，对应表1 v_company中的企业编码 */
            companyCode?: string;
            /** @description 是否有承包商作业 0否;1是 */
            contractor?: string;
            /** @description 临时用电作业 作业数量 */
            electricityworkNumber?: string;
            /** @description 一级动火作业 作业数量 */
            fire1Number?: string;
            /** @description 二级动火作业 作业数量 */
            fire2Number?: string;
            /** @description 特级动火作业 作业数量 */
            firesNumber?: string;
            /** @description 高处作业 作业数量 */
            highworkNumber?: string;
            /** @description 编号 */
            id?: string;
            /** @description 检维修作业 作业数量 */
            inspectionNumber?: string;
            /** @description 吊装作业 作业数量 */
            liftingworkNumber?: string;
            mhazards?: string;
            /** @description 是否处于开停车状态 0否;1是 */
            openParking?: string;
            /** @description 停车套数 停车套数 */
            parkNumber?: string;
            /** @description 风险级别 1高风险；2较大风险；3一般风险；4低风险 */
            riskGrade?: string;
            /** @description 断路作业 作业数量 */
            roadworkNumber?: string;
            /** @description 运行套数 运行套数 */
            runNumber?: string;
            /** @description 动土作业 作业数量 */
            soilworkNumber?: string;
            /** @description 受限空间作业 作业数量 */
            spaceworkNumber?: string;
            /** @description 是否开展中（扩）试 0否;1是 */
            test?: string;
            /** @description 是否处于试生产期 0否;1是 */
            trialProduction?: string;
            /** @description 生产装置套数 生产装置套数 */
            unitsNumber?: string;
        };
        /**
         * CommitmentPageDTO
         * @description 安全承诺书分页DTO
         */
        CommitmentPageDTO: {
            /** @description 承诺时间 */
            commitDate?: string;
            /** @description 企业编码 */
            companyCode?: string;
            /** @description 企业名称 */
            companyName?: string;
            /** @description 编号 */
            id?: string;
            /** @description 行政区划名称 */
            regionName?: string;
            /** @description 风险级别 1高风险；2较大风险；3一般风险；4低风险 */
            riskGrade?: string;
        };
        /**
         * CommitmentQueryDTO
         * @description 安全承诺书分页查询参数
         */
        CommitmentQueryDTO: {
            /** @description 承诺时间开始 */
            commitBeginTime?: string;
            /** @description 承诺时间结束 */
            commitEndTime?: string;
            /** @description 企业名称 */
            companyName?: string;
            /** @description 行政区划 */
            regionCode?: string;
            /** @description 风险级别 1高风险；2较大风险；3一般风险；4低风险 */
            riskGrade?: string;
        };
        /**
         * CompanyCheckDTO
         * @description 企业自查
         */
        CompanyCheckDTO: {
            /**
             * Format: int32
             * @description 已整改
             */
            handlerCount?: number;
            /**
             * Format: int32
             * @description 已自查
             */
            hasCheckCount?: number;
            /**
             * Format: int32
             * @description 未自查
             */
            notCheckCount?: number;
            /**
             * Format: int32
             * @description 未整改
             */
            notHandlerCount?: number;
            /**
             * Format: int32
             * @description 自查总次数
             */
            totalCount?: number;
        };
        /**
         * CompanyDTO
         * @description 企业信息
         */
        CompanyDTO: {
            /** @description 工商注册地址 企业注册所在地地址 */
            addressRegistry?: string;
            /** @description 生产场所地址 */
            addressWorksite?: string;
            /** @description 行政区域编码 企业注册地行政区域，区县级（6位） */
            areaCode?: string;
            /** @description 营业执照经营范围 */
            businessScope?: string;
            /** @description 企业编码 */
            companyCode?: string;
            /** @description 企业名称 企业名称 */
            companyName?: string;
            /** @description 企业风险等级 1:重大风险;2:较大风险;3：一般风险 4：低风险 9：离线  */
            companyRiskRank?: string;
            /** @description 企业规模 1:大;2:中;3:小;4:微 */
            companyScale?: string;
            /** @description 企业简称 企业简称 */
            companyShortName?: string;
            /** @description 企业状态 企业状态类型：0正常，1停产，2涉密 */
            companyStatus?: string;
            /** @description 企业类型 01:生产;02:经营;03:使用 */
            companyType?: string;
            /** @description 安全值班电话 */
            dutyPhone?: string;
            /** @description 经济类型 参照国标 GB/T12402-2000,保留两级 */
            economicType?: string;
            /** @description 成立日期 */
            establishDate?: string;
            /** @description 厂区面积 默认为0 */
            factoryArea?: string;
            /** @description 编号 主键，36位UUID */
            id?: string;
            /** @description 所属化工园区名称 */
            industrialParkName?: string;
            /** @description 所属行业门类 参照国标 GB/T 4754—2017 */
            industryCategory?: string;
            /** @description 所属行业大类 参照国标 GB/T 4754—2017 */
            industryClass?: string;
            /** @description 是否在化工园区内 0否;1是 */
            inIndustrialPark?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 接入网关状态:1-已接入 0-未接入 */
            linkedStatus?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 化学品登记系统编码 危化品登记系统中企业的唯一编码 */
            nrccCode?: string;
            /** @description 在线状态:1-在线 0-离线 */
            onlineStatus?: string;
            /** @description 职工人数 */
            peopleEmployee?: string;
            /** @description 危险化学品作业人员人数 */
            peopleHazard?: string;
            /** @description 特种作业人员人数 */
            peopleOperation?: string;
            /** @description 从业人员人数 */
            peoplePractitioner?: string;
            /** @description 剧毒化学品作业人员人数 */
            peopleToxic?: string;
            /** @description 邮政编码 */
            postCode?: string;
            /** @description 边界地理信息 */
            rangeGeometryData?: string;
            /** @description 法定代表人 */
            representativePerson?: string;
            /** @description 企业负责人手机 */
            responsibleMobile?: string;
            /** @description 企业负责人 */
            responsiblePerson?: string;
            /** @description 企业负责人电话 */
            responsiblePhone?: string;
            /** @description 安全生产许可证有效期结束日期 */
            safetyLicenseEnd?: string;
            /** @description 安全生产许可证编号 */
            safetyLicenseNo?: string;
            /** @description 安全生产许可证有效期开始日期 */
            safetyLicenseStart?: string;
            /** @description 安全负责人手机 */
            safetyResponsibleMobile?: string;
            /** @description 安全负责人 */
            safetyResponsiblePerson?: string;
            /** @description 安全负责人电话 */
            safetyResponsiblePhone?: string;
            /** @description 安全生产标准化等级 1:一级;2:二级;3三级 */
            safetyStandardGrad?: string;
            /** @description 统一社会信用代码 */
            socialCreditCode?: string;
            /** @description 企业网址 */
            webSite?: string;
        };
        /**
         * CompanyEquipDTO
         * @description 企业设备
         */
        CompanyEquipDTO: {
            /** @description 设备编码 */
            equipCode?: string;
            /** @description 设备描述 */
            equipDescribe?: string;
            /** @description 设备名称 */
            equipName?: string;
            /** @description 设备运行状态 0停用，1在用 */
            equipStatus?: string;
            /** @description 设备类型 罐：G0;气体检测仪：Q0;生产装置：P0；仓库或库区：K0 油罐：Z0 */
            equipType?: string;
            /** @description 危险源编码 所属危险源编码（对应表2中的危险源编码） */
            hazardCode?: string;
            /** @description 编号 主键，36位UUID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 罐类型 1:固定罐；2：外浮顶；3：内浮顶；4：球罐；5：卧罐；6：储槽 */
            tankType?: string;
        };
        /**
         * CompanyEquipTankDTO
         * @description 企业罐体设备详情
         */
        CompanyEquipTankDTO: {
            /** @description 建造日期 */
            bulidDate?: string;
            /** @description 设备编码 */
            equipCode?: string;
            /** @description 存储介质 */
            medium?: string;
            /** @description 介质形态 */
            mediumStatus?: string;
            /** @description 设计压力 */
            pressureDesing?: string;
            /** @description 最高工作压力 */
            pressureMax?: string;
            /** @description 压力类型 */
            pressureType?: string;
            /** @description 储量 液态单位为吨，气态为立方米 */
            reserves?: string;
            /** @description 罐类型 */
            tankType?: string;
            /** @description 温度类型 */
            temperatureType?: string;
            /** @description 设计温度高限 */
            tempratureMax?: string;
            /** @description 设计温度低限 */
            tempratureMin?: string;
            /** @description 投用日期 */
            useDate?: string;
        };
        /**
         * DownHolePersonnelInfoDTO
         * @description 下井人员定位预警
         */
        DownHolePersonnelInfoDTO: {
            /** @description 公司名称 */
            companyName?: string;
            /**
             * Format: int32
             * @description 超时报警
             */
            exceedingPersonNum?: number;
            /**
             * Format: int32
             * @description 超员报警
             */
            overPersonNum?: number;
            /**
             * Format: int32
             * @description 本月报警次数
             */
            recordNum?: number;
            /**
             * Format: int32
             * @description 定员人数
             */
            verificationPersonNum?: number;
        };
        /**
         * DynamicDataAnalysisDTO
         * @description 动态监管数据分析
         */
        DynamicDataAnalysisDTO: {
            /** @description 危化 */
            chemicals?: components["schemas"]["StatisticDTO"];
            /** @description 矿山 */
            mine?: components["schemas"]["StatisticDTO"];
            /** @description 尾库 */
            tailings?: components["schemas"]["StatisticDTO"];
        };
        /**
         * EquipTargetCurrentDTO
         * @description 最新指标信息
         */
        EquipTargetCurrentDTO: {
            id?: string;
            /** @description 最近报警起始时间 */
            lastAlarmStarted?: string;
            /** @description 最近一次的报警状态，不考虑销警。 */
            lastAlarmStatus?: string;
            /** @description 最近报警或销警时间 */
            lastAlarmTime?: string;
            /** @description 最近报警或销警数值 */
            lastAlarmValue?: string;
            /** @description 指标最新采集时间，为企业前端采集的时间 */
            lastCollectTime?: string;
            /** @description 指标最新采集的实时数值 */
            lastValue?: string;
            /** @description 指标是否在线 */
            online?: string;
            /** @description 对应表7中的指标编码 */
            targetCode?: string;
            /** @description 指标当前状态：高高报（hh）高报（hi）低报（lo）低低报（ll）信号量报警（si）正常（no） */
            targetStatus?: string;
            /** @description 指标数据质量状态（是否超量程） */
            valueQualityStatus?: string;
        };
        /**
         * GovernmentCheckDTO
         * @description 政府自查
         */
        GovernmentCheckDTO: {
            /**
             * Format: int32
             * @description 企业数
             */
            companyCount?: number;
            /**
             * Format: int32
             * @description 发现隐患数
             */
            dangerCount?: number;
            /**
             * Format: int32
             * @description 隐患整改数
             */
            dangerHandlerCount?: number;
            /**
             * Format: bigdecimal
             * @description 整改率
             */
            dangerHandlerRate?: number;
            /**
             * Format: int32
             * @description 总次数
             */
            totalCount?: number;
        };
        /**
         * GridPointStatisticDTO
         * @description 网格点位统计DTO
         */
        GridPointStatisticDTO: {
            /**
             * Format: int64
             * @description 总数
             */
            count?: number;
            /** @description geoHash值 */
            geoHash?: string;
            /** @description ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
        };
        /**
         * GridPointStatisticQueryDTO
         * @description 网格点位统计入参DTO
         */
        GridPointStatisticQueryDTO: {
            /**
             * Format: double
             * @description 右下角纬度
             */
            bottomRightLatitude?: number;
            /**
             * Format: double
             * @description 右下角经度
             */
            bottomRightLongitude?: number;
            /**
             * Format: int32
             * @description 精度: 1(5,004km x 5,004km) 2(1,251km x 625km) 3(156km x 156km) 4(39km x 19.5km) 5(4.9km x 4.9km) 6(1.2km x 0.61km) 7(152.8m x 152.8m)
             */
            precision?: number;
            /**
             * Format: double
             * @description 左上角纬度
             */
            topLeftLatitude?: number;
            /**
             * Format: double
             * @description 左上角经度
             */
            topLeftLongitude?: number;
        };
        /**
         * HazardCityCommitmentStatisticDTO
         * @description 安全承诺市县等级统计
         */
        HazardCityCommitmentStatisticDTO: {
            /** @description 风险等级及数量 */
            hazardRankDTO?: components["schemas"]["HazardCommitmentDTO"];
            /** @description 市县编码 */
            regionCode?: string;
            /** @description 市县名称 */
            regionName?: string;
        };
        /**
         * HazardCityRankStatisticDTO
         * @description 危险源市县等级统计
         */
        HazardCityRankStatisticDTO: {
            /** @description 风险等级及数量 */
            hazardRankDTO?: components["schemas"]["HazardRankDTO"];
            /** @description 市县编码 */
            regionCode?: string;
            /** @description 市县名称 */
            regionName?: string;
        };
        /**
         * HazardCommitmentDTO
         * @description 安全承诺等级统计
         */
        HazardCommitmentDTO: {
            /**
             * Format: int32
             * @description 一般风险企业
             */
            generalRiskNum?: number;
            /**
             * Format: int32
             * @description 高风险企业
             */
            highRiskNum?: number;
            /** Format: int32 */
            largeRiskNum?: number;
            /**
             * Format: int32
             * @description 低风险企业
             */
            lowRiskNum?: number;
            /**
             * Format: int32
             * @description 未承诺企业
             */
            uncommittedNum?: number;
        };
        /**
         * HazardRankDTO
         * @description 危险源等级统计
         */
        HazardRankDTO: {
            /**
             * Format: int32
             * @description 一级
             */
            level1Num?: number;
            /**
             * Format: int32
             * @description 二级
             */
            level2Num?: number;
            /**
             * Format: int32
             * @description 三级
             */
            level3Num?: number;
            /**
             * Format: int32
             * @description 四级
             */
            level4Num?: number;
            /**
             * Format: int32
             * @description 非重大危险源
             */
            notHazNum?: number;
        };
        /**
         * HdcGovernmentPatrolDTO
         * @description 政府巡查
         */
        HdcGovernmentPatrolDTO: {
            /**
             * Format: int32
             * @description 超24小时未销警指标数
             */
            over24NotHandler?: number;
            /**
             * Format: int32
             * @description 安全承诺情况-已承诺
             */
            promisedNum?: number;
            /**
             * Format: int32
             * @description 安全承诺情况-未承诺
             */
            promiseNotNum?: number;
            /** @description 安全承诺情况-承诺率 */
            promiseRate?: string;
            /**
             * Format: int32
             * @description 安全承诺情况-应承诺
             */
            promiseShouldNum?: number;
            /** @description 行政编码 */
            regionCode?: string;
            /** @description 行政名称 */
            regionName?: string;
            /**
             * Format: int32
             * @description 系统在线情况-接入数
             */
            sysAccessNum?: number;
            /**
             * Format: int32
             * @description 系统在线情况-离线已报备
             */
            sysOfflineNum?: number;
            /**
             * Format: int32
             * @description 系统在线情况-在线数
             */
            sysOnlineNum?: number;
            /** @description 系统在线情况-在线率 */
            sysOnlineRate?: string;
            /**
             * Format: int32
             * @description 系统在线情况-停产已报备
             */
            sysStopNum?: number;
            /**
             * Format: int32
             * @description 系统在线情况-未报备
             */
            sysUnreported?: number;
            /**
             * Format: int32
             * @description 视频监控在线情况-接入企业
             */
            videoAccessCompanyNum?: number;
            /**
             * Format: int32
             * @description 视频监控在线情况-离线已报备
             */
            videoOfflineReported?: number;
            /**
             * Format: int32
             * @description 视频监控在线情况-离线未报备
             */
            videoOfflineUnreported?: number;
            /**
             * Format: int32
             * @description 视频监控在线情况-在线企业
             */
            videoOnlineCompanyNum?: number;
            /** @description 视频监控在线情况-在线率 */
            videoOnlineRate?: string;
            /**
             * Format: int32
             * @description 预警及通报处置情况-预警未反馈
             */
            warnNotFeedback?: number;
            /**
             * Format: int32
             * @description 预警及通报处置情况-预警未销警
             */
            warnNotHandler?: number;
            /**
             * Format: int32
             * @description 预警及通报处置情况-通报未反馈
             */
            warnNotificationNotFeedback?: number;
        };
        /**
         * HdcGovernmentPatrolFbQueryDTO
         * @description 政府巡查反馈查询
         */
        HdcGovernmentPatrolFbQueryDTO: {
            /** @description 巡查时间结算 yyyy-MM-dd */
            createTimeEnd?: string;
            /** @description 巡查时间开始 yyyy-MM-dd */
            createTimeStart?: string;
        };
        /**
         * HdcGovernmentPatrolFeedbackDTO
         * @description 政府巡查反馈
         */
        HdcGovernmentPatrolFeedbackDTO: {
            /** @description 巡查人 */
            contactor?: string;
            /** @description 巡查时间 */
            createTime?: string;
            /** @description 反馈时间 */
            feedbackTime?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 反馈状态 */
            inspectStatusName?: string;
            /** @description 超24小时未销警指标数-数量 */
            latestAlarmTargetCount?: string;
            /** @description 超24小时未销警指标数-持续时长 */
            latestAlarmTargetInterval?: string;
            /** @description 系统在线情况-在线率 */
            onlineRate?: string;
            /** @description 系统在线情况-排名 */
            onlineSort?: string;
            /** @description 安全承诺情况-在线率 */
            promiseRate?: string;
            /** @description 安全承诺情况--排名 */
            promiseSort?: string;
            /** @description 视频监控在线情况-在线率 */
            videoOnlineRateName?: string;
            /** @description 视频监控在线情况-排名 */
            videoOnlineSort?: string;
            /** @description 预警处置及督办情况-预警未销警 */
            warnCount?: string;
            /** @description 预警处置及督办情况-预警未反馈 */
            warnNoRespCount?: string;
            /** @description 预警处置及督办情况-通报未反馈 */
            warnNoticeNoRespCount?: string;
        };
        /**
         * HdcVideoDTO
         * @description 视频信息表
         */
        HdcVideoDTO: {
            /** @description 公司编码 */
            companyCode?: string;
            /** @description 描述 */
            description?: string;
            /** @description id */
            id?: string;
            /** @description 在线 */
            online?: string;
            /** @description 本地记录 */
            recordLocation?: string;
            /** @description 状态 */
            status?: string;
            /** @description 流模式 */
            streamMode?: string;
            /** @description 转换模式 */
            transMode?: string;
            /** @description url */
            url?: string;
            /** @description 视频编码 */
            videoCode?: string;
            /** @description 视频名称 */
            videoName?: string;
            /** @description 视频排序 */
            videoSort?: string;
        };
        /**
         * HdcVideoQueryDTO
         * @description 视频查询DTO
         */
        HdcVideoQueryDTO: {
            /** @description 公司编码 */
            companyCode?: string;
        };
        /**
         * HiddenDangerCheckDTO
         * @description 隐患排查
         */
        HiddenDangerCheckDTO: {
            /** @description 企业自查 */
            companyCheckDTO?: components["schemas"]["CompanyCheckDTO"];
            /** @description 政府检查 */
            governmentCheckDTO?: components["schemas"]["GovernmentCheckDTO"];
            /** @description 隐患趋势统计 */
            hiddenDangerStatistic?: components["schemas"]["StatisticDTO"][];
        };
        /**
         * HiddenHazardousPatrolDTO
         * @description 全省安全生产重大隐患排查清单
         */
        HiddenHazardousPatrolDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 描述 */
            description?: string;
            /** @description 责任单位 */
            dutyUnit?: string;
            /** @description 整改措施 */
            handleMethod?: string;
            /** @description 整改状态 */
            handleStatus?: string;
            /** @description 整改时间 */
            handleTime?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 行业 */
            industry?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 城市编码 */
            regionCode?: string;
            /** @description 城市名称 */
            regionName?: string;
            /** @description 备注 */
            remark?: string;
        };
        /**
         * HiddenHazardousPatrolListPointsDTO
         * @description 全省安全生产重大隐患排查清单
         */
        HiddenHazardousPatrolListPointsDTO: {
            /** @description 整改状态 */
            handleStatus?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
        };
        /**
         * HiddenHazardousPatrolStatisticDTO
         * @description 全省安全生产重大隐患排查清单统计
         */
        HiddenHazardousPatrolStatisticDTO: {
            /** @description 编码 */
            code?: string;
            /**
             * Format: int32
             * @description 已完成
             */
            completeNum?: number;
            /**
             * Format: int32
             * @description 整改中
             */
            handlingNum?: number;
            /** @description 描述 */
            text?: string;
            /** @description 数量/百分比 */
            value?: string;
        };
        /**
         * IndustrialTradeDTO
         * @description 海南省工贸企业基本数据对象
         */
        IndustrialTradeDTO: {
            /** @description 产　能 */
            capacity?: string;
            /** @description 企业地址 */
            enpAddress?: string;
            /** @description 企业名称 */
            enpName?: string;
            industryType?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 公司法人名称 */
            legalPersonName?: string;
            /** @description 公司法人电话 */
            legalPersonPhone?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 主要产品 */
            mainProducts?: string;
            /** @description 认定年度 */
            recognitionYear?: string;
            /** @description 行政区划 */
            region?: string;
            /** @description 行政区划编码 */
            regionCode?: string;
            /** @description 安全管理人名称 */
            safetyManagerName?: string;
            /** @description 安全管理人电话 */
            safetyManagerPhone?: string;
            /** @description 企业规模 */
            scale?: string;
            /** @description 序列号 */
            serialNo?: string;
            /** @description 标准等级 */
            standardGrade?: string;
            /** @description 类型 */
            type?: string;
            /** @description 类型编码 */
            typeCode?: string;
        };
        /**
         * IndustrialTradePointListDTO
         * @description 海南省工贸企业基本数据上球对象
         */
        IndustrialTradePointListDTO: {
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 类型 */
            type?: string;
        };
        /**
         * IndustrialTradeQueryDTO
         * @description 海南省工贸企业基本数据查询对象
         */
        IndustrialTradeQueryDTO: {
            /** @description 是否有坐标 1-有 0-没有  */
            hasPoint?: string;
            /** @description 关键字 */
            keyword?: string;
            /** @description 行政区划编码 */
            regionCode?: string;
            /** @description 规模:大型企业、中型企业、小型企业、微型企业 */
            scale?: string;
            /** @description 类型 */
            type?: string;
        };
        /**
         * IndustryListStatisticDTO
         * @description 行业列表统计
         */
        IndustryListStatisticDTO: {
            /**
             * Format: int64
             * @description 去年-死亡(人)
             */
            lastDeathNum?: number;
            /**
             * Format: int64
             * @description 去年-死亡(人)-比对个数
             */
            lastDeathNumDiff?: number;
            /** @description 去年-死亡(人)-比对比例% */
            lastDeathNumRateDiff?: string;
            /**
             * Format: int64
             * @description 去年-起数(起)
             */
            lastEventNum?: number;
            /**
             * Format: int64
             * @description 去年-起数(起)-比对个数
             */
            lastEventNumDiff?: number;
            /** @description 去年-起数(起)-比对比例% */
            lastEventNumRateDiff?: string;
            /**
             * Format: int64
             * @description 前年-死亡(人)
             */
            preLastDeathNum?: number;
            /**
             * Format: int64
             * @description 前年-死亡(人)-比对个数
             */
            preLastDeathNumDiff?: number;
            /** @description 前年-死亡(人)-比对比例% */
            preLastDeathNumRateDiff?: string;
            /**
             * Format: int64
             * @description 前年-起数(起)
             */
            preLastEventNum?: number;
            /**
             * Format: int64
             * @description 前年-起数(起)-比对个数
             */
            preLastEventNumDiff?: number;
            /** @description 前年-起数(起)-比对比例% */
            preLastEventNumRateDiff?: string;
            /** @description 描述 */
            text?: string;
            /**
             * Format: int64
             * @description 当年-死亡(人)
             */
            thisDeathNum?: number;
            /**
             * Format: int64
             * @description 当年-起数(起)
             */
            thisEventNum?: number;
        };
        /** LeakageRisk */
        LeakageRisk: {
            /** @description 报警点位数-可燃气体 */
            alarmPointCombustibleGas?: string;
            /** @description 报警点位数-有毒气体 */
            alarmPointToxicGas?: string;
            /** @description 监测点位数-可燃气体 */
            monitorPointCombustibleGas?: string;
            /** @description 监测点位数-有毒气体 */
            monitorPointToxicGas?: string;
            /** @description 评分 */
            score?: string;
        };
        /**
         * MineDetailDTO
         * @description 非煤矿山详情对象
         */
        MineDetailDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 公司名称 */
            companyName?: string;
            /** @description 成立日期 */
            dateOfIncorporation?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 企业负责人 */
            masterName?: string;
            /** @description 负责人联系方式 */
            masterPhoneNo?: string;
            /** @description 开采方式 */
            miningMethod?: string;
            /** @description 从业人数 */
            peopleNum?: string;
            /** @description 安全生产标准化等级 */
            safeLevel?: string;
            /** @description 状态 */
            status?: string;
        };
        /**
         * MineTailingPointsDTO
         * @description 矿山上球对象
         */
        MineTailingPointsDTO: {
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 类型: 1-矿山 2-尾矿 */
            type?: string;
        };
        /**
         * MonthlyEnterpriseAlarmRankingDTO
         * @description 月度企业报警排行
         */
        MonthlyEnterpriseAlarmRankingDTO: {
            /** @description 公司名称 */
            companyName?: string;
            /**
             * Format: int64
             * @description 已处理总数
             */
            handlerNum?: number;
            /** @description 已处理率 */
            handlerRate?: string;
            /**
             * Format: int64
             * @description 报警总数
             */
            recordNum?: number;
        };
        /**
         * NonCoalMineDTO
         * @description 非煤矿山详情对象
         */
        NonCoalMineDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 工商营业执照编号 */
            businessLicense?: string;
            /** @description 经济类型 */
            economicType?: string;
            /** @description 企业联系电话 */
            enpContactNumber?: string;
            /** @description 企业联系人 */
            enterpriseContact?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 安全生产许可证有效期(结束时间) */
            licenseInPlaceEnd?: string;
            /** @description 安全生产许可证有效期(开始时间) */
            licenseInPlaceStart?: string;
            /** @description 安全生产许可范围 */
            limitedLicense?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 矿种 */
            mineralSpecies?: string;
            /** @description 采矿证生产能力或尾矿库等别 */
            miningCapacity?: string;
            /** @description 采矿证有效期(结束时间) */
            miningLicensePeriodEnd?: string;
            /** @description 采矿证有效期(开始时间) */
            miningLicensePeriodStart?: string;
            /** @description 开采方式（露天或地下） */
            miningMethod?: string;
            /** @description 矿山单位名称 */
            nameOfMiningUnit?: string;
            /** @description 单位负责人 */
            personInCharge?: string;
            /** @description 行政区划 */
            region?: string;
            /** @description 行政区划编码 */
            regionCode?: string;
            /** @description 备注 */
            remark?: string;
            /** @description 风险等级 */
            riskLevel?: string;
            /** @description 规模 */
            scale?: string;
            /** @description 企业规模 */
            serialNo?: string;
            /** @description 状态（填生产、停产、闭库） */
            workStatus?: string;
        };
        /**
         * NonCoalMineQueryDTO
         * @description 非煤矿山查询对象
         */
        NonCoalMineQueryDTO: {
            /** @description 是否有坐标 1-有 0-没有  */
            hasPoint?: string;
            /** @description 关键字 */
            keyword?: string;
            /** @description 开采方式：露天矿山、地下矿山、尾矿库、类型未知 */
            miningMethod?: string;
            /** @description 行政区划编码 */
            regionCode?: string;
            /** @description 规模:大型企业、中型企业、小型企业、其他 */
            scale?: string;
        };
        /**
         * PageParam«AccidentDTO»
         * @description 分页参数
         */
        PageParamAccidentDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["AccidentDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["AccidentDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«AccidentQueryDTO»
         * @description 分页参数
         */
        PageParamAccidentQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["AccidentQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«CommitmentPageDTO»
         * @description 分页参数
         */
        PageParamCommitmentPageDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["CommitmentPageDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["CommitmentPageDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«CommitmentQueryDTO»
         * @description 分页参数
         */
        PageParamCommitmentQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["CommitmentQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«CompanyDTO»
         * @description 分页参数
         */
        PageParamCompanyDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["CompanyDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["CompanyDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«HdcGovernmentPatrolFbQueryDTO»
         * @description 分页参数
         */
        PageParamHdcGovernmentPatrolFbQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["HdcGovernmentPatrolFbQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«HdcGovernmentPatrolFeedbackDTO»
         * @description 分页参数
         */
        PageParamHdcGovernmentPatrolFeedbackDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["HdcGovernmentPatrolFeedbackDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["HdcGovernmentPatrolFeedbackDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«IndustrialTradeDTO»
         * @description 分页参数
         */
        PageParamIndustrialTradeDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["IndustrialTradeDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["IndustrialTradeDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«IndustrialTradeQueryDTO»
         * @description 分页参数
         */
        PageParamIndustrialTradeQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["IndustrialTradeQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«NonCoalMineDTO»
         * @description 分页参数
         */
        PageParamNonCoalMineDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["NonCoalMineDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["NonCoalMineDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«NonCoalMineQueryDTO»
         * @description 分页参数
         */
        PageParamNonCoalMineQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["NonCoalMineQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«企业分页查询参数»
         * @description 分页参数
         */
        PageParamQiYeFenYeChaXunCanShu: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["QiYeFenYeChaXunCanShu"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PageParam«VideoDTO»
         * @description 分页参数
         */
        PageParamVideoDTO: {
            /** @description 查询返回数据 */
            records?: components["schemas"]["VideoDTO"][];
            /** @description 分页查询合计对象 */
            summary?: components["schemas"]["VideoDTO"];
            /**
             * Format: int64
             * @description 总条数
             */
            total?: number;
        };
        /**
         * PageParam«VideoQueryDTO»
         * @description 分页参数
         */
        PageParamVideoQueryDTO: {
            /**
             * Format: int64
             * @description 当前页
             * @example 1
             */
            current: number;
            /** @description 扩展参数 */
            extra?: Record<string, never>;
            /**
             * @description 排序信息
             * @example [{propertyName:'',orderType:''}]
             */
            orderConfigList?: components["schemas"]["ChaXunPaiXuPeiZhiXiang"][];
            /** @description 查询参数 */
            query?: components["schemas"]["VideoQueryDTO"];
            /**
             * Format: int64
             * @description 页面大小
             * @example 10
             */
            size: number;
        };
        /**
         * PointListDTO
         * @description 上球点列表对象DTO
         */
        PointListDTO: {
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
        };
        /** 企业分页查询参数 */
        QiYeFenYeChaXunCanShu: {
            /** @description 企业名称 */
            companyName?: string;
            /** @description 企业类型 01:生产;02:经营;03:使用 */
            companyTypeList?: string[];
            /** @description 风险等级 1:一级;2:二级;3:三级;4:四级;9非重大危险源（一般危险源） */
            hazardRankList?: string[];
            /** @description 行政区划 */
            regionCode?: string;
        };
        /**
         * RedBlackSortDTO
         * @description 红黑榜企业排名
         */
        RedBlackSortDTO: {
            /** @description 公司名称 */
            companyName?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 行政区域 */
            regionName?: string;
            /**
             * Format: double
             * @description 分值
             */
            score?: number;
        };
        /**
         * ReduceLossesStatisticDTO
         * @description 减少损失统计对象
         */
        ReduceLossesStatisticDTO: {
            /** @description 环比当月减少率 */
            monthRate?: string;
            /** @description 同比年度减少率 */
            yearRate?: string;
            /** @description 年度统计列表 */
            yearStatisticList?: components["schemas"]["TextValueDTO"][];
        };
        /**
         * ResponsibilityWarningDTO
         * @description 责任落实预警
         */
        ResponsibilityWarningDTO: {
            /** @description 企业名称 */
            companyName?: string;
            /** @description 预警内容 */
            waringDetail?: string;
            /** @description 预警类型 */
            waringType?: string;
        };
        /** Result«AccidentDayReportDTO» */
        ResultAccidentDayReportDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["AccidentDayReportDTO"];
            message?: string;
        };
        /** Result«AccidentSimpleReportDTO» */
        ResultAccidentSimpleReportDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["AccidentSimpleReportDTO"];
            message?: string;
        };
        /** Result«CommitmentDetailDTO» */
        ResultCommitmentDetailDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["CommitmentDetailDTO"];
            message?: string;
        };
        /** Result«CompanyDTO» */
        ResultCompanyDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["CompanyDTO"];
            message?: string;
        };
        /** Result«CompanyEquipTankDTO» */
        ResultCompanyEquipTankDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["CompanyEquipTankDTO"];
            message?: string;
        };
        /** Result«DynamicDataAnalysisDTO» */
        ResultDynamicDataAnalysisDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["DynamicDataAnalysisDTO"];
            message?: string;
        };
        /** Result«HiddenDangerCheckDTO» */
        ResultHiddenDangerCheckDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HiddenDangerCheckDTO"];
            message?: string;
        };
        /** Result«HiddenHazardousPatrolDTO» */
        ResultHiddenHazardousPatrolDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HiddenHazardousPatrolDTO"];
            message?: string;
        };
        /** Result«IndustrialTradeDTO» */
        ResultIndustrialTradeDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["IndustrialTradeDTO"];
            message?: string;
        };
        /** Result«List«AccidentDTO»» */
        ResultListAccidentDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["AccidentDTO"][];
            message?: string;
        };
        /** Result«List«AccidentRelatedUserDTO»» */
        ResultListAccidentRelatedUserDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["AccidentRelatedUserDTO"][];
            message?: string;
        };
        /** Result«List«CertificateStatisticDTO»» */
        ResultListCertificateStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["CertificateStatisticDTO"][];
            message?: string;
        };
        /** Result«List«ChemicalsMineCityStatisticDTO»» */
        ResultListChemicalsMineCityStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ChemicalsMineCityStatisticDTO"][];
            message?: string;
        };
        /** Result«List«CompanyDTO»» */
        ResultListCompanyDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["CompanyDTO"][];
            message?: string;
        };
        /** Result«List«CompanyEquipDTO»» */
        ResultListCompanyEquipDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["CompanyEquipDTO"][];
            message?: string;
        };
        /** Result«List«DownHolePersonnelInfoDTO»» */
        ResultListDownHolePersonnelInfoDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["DownHolePersonnelInfoDTO"][];
            message?: string;
        };
        /** Result«List«GridPointStatisticDTO»» */
        ResultListGridPointStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["GridPointStatisticDTO"][];
            message?: string;
        };
        /** Result«List«HazardCityCommitmentStatisticDTO»» */
        ResultListHazardCityCommitmentStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HazardCityCommitmentStatisticDTO"][];
            message?: string;
        };
        /** Result«List«HazardCityRankStatisticDTO»» */
        ResultListHazardCityRankStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HazardCityRankStatisticDTO"][];
            message?: string;
        };
        /** Result«List«HdcGovernmentPatrolDTO»» */
        ResultListHdcGovernmentPatrolDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HdcGovernmentPatrolDTO"][];
            message?: string;
        };
        /** Result«List«HdcVideoDTO»» */
        ResultListHdcVideoDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HdcVideoDTO"][];
            message?: string;
        };
        /** Result«List«HiddenHazardousPatrolListPointsDTO»» */
        ResultListHiddenHazardousPatrolListPointsDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HiddenHazardousPatrolListPointsDTO"][];
            message?: string;
        };
        /** Result«List«HiddenHazardousPatrolStatisticDTO»» */
        ResultListHiddenHazardousPatrolStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["HiddenHazardousPatrolStatisticDTO"][];
            message?: string;
        };
        /** Result«List«IndustrialTradeDTO»» */
        ResultListIndustrialTradeDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["IndustrialTradeDTO"][];
            message?: string;
        };
        /** Result«List«IndustrialTradePointListDTO»» */
        ResultListIndustrialTradePointListDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["IndustrialTradePointListDTO"][];
            message?: string;
        };
        /** Result«List«MineTailingPointsDTO»» */
        ResultListMineTailingPointsDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["MineTailingPointsDTO"][];
            message?: string;
        };
        /** Result«List«MonthlyEnterpriseAlarmRankingDTO»» */
        ResultListMonthlyEnterpriseAlarmRankingDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["MonthlyEnterpriseAlarmRankingDTO"][];
            message?: string;
        };
        /** Result«List«PointListDTO»» */
        ResultListPointListDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PointListDTO"][];
            message?: string;
        };
        /** Result«List«RedBlackSortDTO»» */
        ResultListRedBlackSortDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["RedBlackSortDTO"][];
            message?: string;
        };
        /** Result«List«设备指标信息DTO»» */
        ResultListSheBeiZhiBiaoXinXiDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["SheBeiZhiBiaoXinXiDTO"][];
            message?: string;
        };
        /** Result«List«StatisticDTO»» */
        ResultListStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["StatisticDTO"][];
            message?: string;
        };
        /** Result«List«TextValueDTO»» */
        ResultListTextValueDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TextValueDTO"][];
            message?: string;
        };
        /** Result«List«WaringEventDTO»» */
        ResultListWaringEventDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WaringEventDTO"][];
            message?: string;
        };
        /** Result«List«危险源信息DTO»» */
        ResultListWeiXianYuanXinXiDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WeiXianYuanXinXiDTO"][];
            message?: string;
        };
        /** Result«MineDetailDTO» */
        ResultMineDetailDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["MineDetailDTO"];
            message?: string;
        };
        /** Result«NonCoalMineDTO» */
        ResultNonCoalMineDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["NonCoalMineDTO"];
            message?: string;
        };
        /** Result«PageParam«AccidentDTO»» */
        ResultPageParamAccidentDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamAccidentDTO"];
            message?: string;
        };
        /** Result«PageParam«CommitmentPageDTO»» */
        ResultPageParamCommitmentPageDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamCommitmentPageDTO"];
            message?: string;
        };
        /** Result«PageParam«CompanyDTO»» */
        ResultPageParamCompanyDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamCompanyDTO"];
            message?: string;
        };
        /** Result«PageParam«HdcGovernmentPatrolFeedbackDTO»» */
        ResultPageParamHdcGovernmentPatrolFeedbackDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamHdcGovernmentPatrolFeedbackDTO"];
            message?: string;
        };
        /** Result«PageParam«IndustrialTradeDTO»» */
        ResultPageParamIndustrialTradeDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamIndustrialTradeDTO"];
            message?: string;
        };
        /** Result«PageParam«NonCoalMineDTO»» */
        ResultPageParamNonCoalMineDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamNonCoalMineDTO"];
            message?: string;
        };
        /** Result«PageParam«VideoDTO»» */
        ResultPageParamVideoDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["PageParamVideoDTO"];
            message?: string;
        };
        /** Result«ReduceLossesStatisticDTO» */
        ResultReduceLossesStatisticDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["ReduceLossesStatisticDTO"];
            message?: string;
        };
        /** Result«RiskProfileDTO» */
        ResultRiskProfileDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["RiskProfileDTO"];
            message?: string;
        };
        /** Result«StatisticNowCommitmentNumDTO» */
        ResultStatisticNowCommitmentNumDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["StatisticNowCommitmentNumDTO"];
            message?: string;
        };
        /** Result«TailingDetailDTO» */
        ResultTailingDetailDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TailingDetailDTO"];
            message?: string;
        };
        /** Result«TextValueDTO» */
        ResultTextValueDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["TextValueDTO"];
            message?: string;
        };
        /** Result«WaringDTO» */
        ResultWaringDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WaringDTO"];
            message?: string;
        };
        /** Result«WaringTodayDTO» */
        ResultWaringTodayDTO: {
            /** Format: int32 */
            code?: number;
            data?: components["schemas"]["WaringTodayDTO"];
            message?: string;
        };
        /**
         * RiskProfileDTO
         * @description 风险构成
         */
        RiskProfileDTO: {
            /** @description 报警管理指数 */
            alarmRisk?: components["schemas"]["AlarmRisk"];
            /** @description 管理状态 */
            controllerStatus?: string;
            /** @description 泄露风险指数 */
            leakageRisk?: components["schemas"]["LeakageRisk"];
            /** @description 存储风险指数 */
            stockRiskFlag?: components["schemas"]["StockRiskFlag"];
            /** @description 装置区风险指数 */
            unitAreaRisk?: components["schemas"]["UnitAreaRisk"];
        };
        /** 设备指标信息DTO */
        SheBeiZhiBiaoXinXiDTO: {
            /** @description 所属设备编码 对应表3中的所属设备编码 */
            equipCode?: string;
            /** @description 编号 主键，36位UUID */
            id?: string;
            /** @description 指标描述 指标描述 */
            parameterDesc?: string;
            /** @description 量程下限 量程下限 */
            rangeDown?: string;
            /** @description 量程上限 量程上限 */
            rangeUp?: string;
            /** @description 删除标记 0未删除，1已删除 */
            status?: string;
            /** @description 指标编码 唯一项 */
            targetCode?: string;
            /** @description 指标名称 */
            targetName?: string;
            /** @description 指标实际信息 */
            targetRealInfo?: components["schemas"]["EquipTargetCurrentDTO"];
            /** @description 指标类型 指标类型，见附录B指标类型附录表 */
            targetType?: string;
            /** @description 一级阈值下限 一级阈值下限 */
            thresholdDown?: string;
            /** @description 二级阈值下限 二级阈值下限 */
            thresholdDown2?: string;
            /** @description 一级阈值上限 一级阈值上限 */
            thresholdUp?: string;
            /** @description 二级阈值上限 二级阈值上限 */
            thresholdUp2?: string;
            /** @description 计量单位 计量单位 */
            unit?: string;
        };
        /**
         * StatisticByKeyDTO
         * @description 根据字段属性维度统计企业数量
         */
        StatisticByKeyDTO: {
            /** @description key */
            key?: string;
            /** @description 行政区划 */
            regionCode?: string;
        };
        /**
         * StatisticDTO
         * @description 统计对象
         */
        StatisticDTO: {
            /** @description 编码 */
            code?: string;
            /** @description 描述 */
            text?: string;
            /** @description 数量/百分比 */
            value?: string;
        };
        /**
         * StatisticNowCommitmentNumDTO
         * @description 当日承诺数量统计
         */
        StatisticNowCommitmentNumDTO: {
            /**
             * Format: int32
             * @description 已承诺
             */
            hasCommittedNum?: number;
            /**
             * Format: int32
             * @description 未承诺
             */
            notCommittedNum?: number;
        };
        /** StockRiskFlag */
        StockRiskFlag: {
            /** @description 液位报警 */
            liquidLevelAlarm?: string;
            /**
             * Format: int64
             * @description 重大危险源
             */
            majorHazard?: number;
            /**
             * Format: int64
             * @description 监测点位
             */
            monitoringPoints?: number;
            /** @description 压力报警 */
            pressureAlarm?: string;
            /** @description 温度报警 */
            temperatureAlarm?: string;
        };
        /**
         * TailingDetailDTO
         * @description 尾矿库详情对象
         */
        TailingDetailDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 公司名称 */
            companyName?: string;
            /** @description 尾矿库形式 */
            damConstruction?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 等级 */
            level?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 企业负责人 */
            masterName?: string;
            /** @description 负责人联系方式 */
            masterPhoneNo?: string;
            /** @description 投入使用日期 */
            putUseDate?: string;
            /** @description 安全度 */
            safe?: string;
            /** @description 状态 */
            status?: string;
            /** @description 尾矿库名称 */
            tailingName?: string;
        };
        /**
         * TextValueDTO
         * @description TextValueDTO
         */
        TextValueDTO: {
            /** @description text */
            text?: string;
            /** @description value */
            value?: string;
        };
        /** UnitAreaRisk */
        UnitAreaRisk: {
            /** @description 低报点位 */
            lowerBestPoints?: string;
            /** @description 低低报点位 */
            lowerPoints?: string;
            /** @description 评分 */
            score?: string;
            /** @description 高高报点位 */
            topBestPoints?: string;
            /** @description 高报点位 */
            topPoints?: string;
        };
        /**
         * VideoDTO
         * @description 视屏对象
         */
        VideoDTO: {
            /** @description 设备名称 */
            deviceName?: string;
            /** @description 主键ID */
            id?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 访问地址 */
            url?: string;
            /** @description 视屏编码 */
            videoCode?: string;
        };
        /**
         * VideoQueryDTO
         * @description 视屏查询对象
         */
        VideoQueryDTO: {
            /** @description 视屏编码 */
            videoCode?: string;
        };
        /**
         * WaringDataDTO
         * @description 实时监测预警数据
         */
        WaringDataDTO: {
            /**
             * Format: bigdecimal
             * @description 环比
             */
            rate?: number;
            /**
             * Format: int64
             * @description 报警企业数量
             */
            waringCompanyTotal?: number;
            /**
             * Format: int64
             * @description 报警总数
             */
            waringTotal?: number;
        };
        /**
         * WaringDTO
         * @description 实时监测预警
         */
        WaringDTO: {
            /** @description 危化 */
            chemicals?: components["schemas"]["WaringDataDTO"];
            /** @description 矿山 */
            mine?: components["schemas"]["WaringDataDTO"];
            /** @description 尾库 */
            tailings?: components["schemas"]["WaringDataDTO"];
        };
        /**
         * WaringEventDTO
         * @description 预警事件
         */
        WaringEventDTO: {
            /** @description 公司名称 */
            companyName?: string;
            /** @description 设备名称 */
            equipName?: string;
            /** @description 设备类型 */
            equipType?: string;
            /** @description 是否已消警：1-是 0-否 */
            hasAlarmElimination?: string;
            /** @description 危险源名称 */
            hazardName?: string;
            /** @description 报警类型 */
            waringStatus?: string;
            /** @description 报警时间/消警时间 */
            waringTime?: string;
        };
        /**
         * WaringTodayDTO
         * @description 当日预警
         */
        WaringTodayDTO: {
            /** @description 预警列表 */
            list?: components["schemas"]["ResponsibilityWarningDTO"][];
            /**
             * Format: int32
             * @description 当日预警企业条数
             */
            waringCompanyCount?: number;
            /**
             * Format: int32
             * @description 当日预警条数
             */
            waringCount?: number;
        };
        /** 危险源信息DTO */
        WeiXianYuanXinXiDTO: {
            /** @description 地址 */
            address?: string;
            /** @description 行政区域编码 危险源所在地行政区域，到区县级（6位） */
            areaCode?: string;
            /** @description 企业编码 对应表1中的企业编码，表示所属企业。 */
            companyCode?: string;
            /** @description 投用日期 */
            establishDate?: string;
            /** @description 重大危险源编码 */
            hazardCode?: string;
            /** @description 重大危险源分类 0：罐区 1：装置2：库区。 */
            hazardFacility?: string;
            /** @description 重大危险源名称 */
            hazardName?: string;
            /** @description 危险源等级 1:一级;2:二级;3:三级;4:四级;9非重大危险源（一般危险源） */
            hazardRank?: string;
            /** @description 危险源简称 */
            hazardShortName?: string;
            /** @description 编号 主键，36位UUID */
            id?: string;
            /** @description 所属化工园区名称 */
            industrialParkName?: string;
            /** @description 是否在化工园区内 0否;1是 */
            inIndustrialPark?: string;
            /** @description 纬度 */
            latitude?: string;
            /** @description 经度 */
            longitude?: string;
            /** @description 外边界500米范围人数估算 */
            people500m?: string;
            /** @description 周边防护目标最近距离（米） 重大危险源与周边重点防护目标最近距离为重大危险源的设备、装置、设施的边缘到周边重点防护目标边缘的最近距离。 */
            protectionTargetDistance?: string;
            /** @description R值 */
            rvalue?: string;
            /** @description 删除标记 0未删除，1已删除 */
            status?: string;
        };
        /**
         * YearChartStatisticDTO
         * @description 较大事故年度图形统计
         */
        YearChartStatisticDTO: {
            /**
             * Format: int64
             * @description 今年数量
             */
            lastYearNum?: number;
            /**
             * Format: int64
             * @description 今年数量
             */
            preLastYearNum?: number;
            /** @description 描述 */
            text?: string;
            /**
             * Format: int64
             * @description 今年数量
             */
            thisYearNum?: number;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    accidentTypeStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description isShowAll */
                isShowAll: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    bestAccidentYearStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dayReportUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description day */
                day: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultAccidentDayReportDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dayReportDownloadUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description day */
                day: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dieNumYearStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fieldStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fieldKey */
                fieldKey: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    gridPointListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description geoHash */
                geoHash: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListAccidentDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    gridPointStatisticUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["GridPointStatisticQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListGridPointStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["AccidentQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListAccidentDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamAccidentQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamAccidentDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPointsUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListPointListDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listRelatedUserUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListAccidentRelatedUserDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listResubmitUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListAccidentDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    reduceLossesStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultReduceLossesStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    simpleReportUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["AccidentQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultAccidentSimpleReportDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    simpleReportDownloadUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["AccidentQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    commitmentListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamCommitmentQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamCommitmentPageDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    companyDetailUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description companyCode */
                companyCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultCompanyDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    companyListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["QiYeFenYeChaXunCanShu"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListCompanyDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    companyListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamQiYeFenYeChaXunCanShu"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamCompanyDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getEquipListByHazardCodeUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description hazardCode */
                hazardCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListCompanyEquipDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getHazardListByCompanyCodeUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description companyCode */
                companyCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListWeiXianYuanXinXiDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getSafetyCommitByCompanyCodeUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description companyCode */
                companyCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultCommitmentDetailDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getTankEquipByCodeUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description equipCode */
                equipCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultCompanyEquipTankDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getTargetByEquipCodeUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description equipCode */
                equipCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListSheBeiZhiBiaoXinXiDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    governmentPatrolUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHdcGovernmentPatrolDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    governmentPatrolFeedbackPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamHdcGovernmentPatrolFbQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamHdcGovernmentPatrolFeedbackDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    governmentPatrolFeedbackReportUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTextValueDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    hazardCityCommitmentStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description commitDate */
                commitDate: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHazardCityCommitmentStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    hazardCityRankStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHazardCityRankStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    riskProfileUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description companyCode */
                companyCode: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultRiskProfileDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    statisticByCommitmentUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    statisticByKeyUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["StatisticByKeyDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    statisticNowCommitmentNumUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultStatisticNowCommitmentNumDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getVideoListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["HdcVideoQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHdcVideoDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    waringEventUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListWaringEventDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fieldStatisticUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fieldKey */
                fieldKey: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHiddenHazardousPatrolStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getByIdUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultHiddenHazardousPatrolDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPointsUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListHiddenHazardousPatrolListPointsDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["IndustrialTradeQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fieldStatisticUsingGET_2: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description fieldKey */
                fieldKey: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getBySerialNoUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description serialNo */
                serialNo: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultIndustrialTradeDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getListUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["IndustrialTradeQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListIndustrialTradeDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamIndustrialTradeQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamIndustrialTradeDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPointsUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["IndustrialTradeQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListIndustrialTradePointListDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    alarmTrendStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description key */
                key: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    certificateStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListCertificateStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    companyScaleStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downHolePersonnelListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListDownHolePersonnelInfoDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    downloadUsingPOST_1: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["NonCoalMineQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    fieldStatisticUsingGET_3: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description field */
                field: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    getBySerialNoUsingGET_1: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description serialNo */
                serialNo: number;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultNonCoalMineDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    listPageUsingPOST_2: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamNonCoalMineQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamNonCoalMineDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    mineCountStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    mineDetailUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultMineDetailDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    mineWaringStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    pointsListUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["NonCoalMineQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListMineTailingPointsDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tailingDetailUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description id */
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultTailingDetailDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tailingsLevelStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tailingsSafeStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    tailingsWaringStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    videoListPageUsingPOST: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["PageParamVideoQueryDTO"];
            };
        };
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultPageParamVideoDTO"];
                };
            };
            /** @description Created */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    actualTimeWaringUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultWaringDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    chemicalsMineCityStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListChemicalsMineCityStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    chemicalsMineStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    chemicalsStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    dynamicDataAnalysisUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description key */
                key: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultDynamicDataAnalysisDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    entResponsibilityUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    hiddenDangerCheckStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultHiddenDangerCheckDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    hiddenDangerRateUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    mineStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    monthlyEnterpriseAlarmRankingUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListMonthlyEnterpriseAlarmRankingDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    redBlackSortListUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description key */
                key: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListRedBlackSortDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    regionResponsibilityStatisticUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    riskTrendAnalysisUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description key */
                key: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    waringTodayUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultWaringTodayDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    waringTypeRateTodayUsingGET: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description OK */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "*/*": components["schemas"]["ResultListStatisticDTO"];
                };
            };
            /** @description Unauthorized */
            401: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Forbidden */
            403: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Not Found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
}
