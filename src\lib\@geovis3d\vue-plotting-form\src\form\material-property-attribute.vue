<!-- MaterialPropertyAttribute -->
<script lang="ts" setup>
import type { MaterialPropertySerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import { useShallowBinding } from './hooks';
import { MaterialPropertyAttributeCollection } from './material';

defineOptions({ name: 'MaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: MaterialPropertySerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: MaterialPropertySerializateJSON): void;
}>();

const model = ref<MaterialPropertySerializateJSON>({
  type: '',
  params: {},
});
useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: (value: any) => (model.value = { ...value }),
  rtl: (value: any) => emit('update:modelValue', { ...value }),
});

const type = ref<string>();

watchEffect(() => {
  type.value = props.modelValue?.type;
});

watch(type, (type) => {
  const data = type ? { type, params: undefined } : undefined;
  emit('update:modelValue', data);
});

const options = computed(() => {
  return [...MaterialPropertyAttributeCollection].map((item) => {
    return {
      label: item.label || item.type,
      value: item.type,
    };
  });
});

const AttributeComponent = computed(() => {
  return [...MaterialPropertyAttributeCollection].find(item => item.type === type.value)?.component;
});
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="type" :options="options" placeholder="请选择" />
  </el-form-item>
  <component :is="AttributeComponent" v-model="model.params" />
</template>
