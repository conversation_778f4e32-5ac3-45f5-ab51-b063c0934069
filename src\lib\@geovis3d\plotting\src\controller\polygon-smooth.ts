import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import * as turf from '@turf/turf';
import * as Cesium from 'cesium';

/**
 * polygon-smooth 标绘配置 平滑闭合面
 */
export default <PlottingControllerOptions>{
  type: 'polygon-smooth',
  manualTerminate: entity => entity.plotting.coordinates.getLength() >= 3,
  location: { visible: true },
  control: { visible: true },
  interval: { visible: true },
  // altitude: { visible: true },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    if (positions.length <= 2) {
      entity._cache = undefined;
      return;
    }
    const wgs84s = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);
    wgs84s.push(wgs84s[0]);
    const smooth = turf.polygonSmooth(turf.polygon([wgs84s]), {
      iterations: 3,
    });
    const cartesians = smooth.features[0].geometry.coordinates[0].map(e =>
      wgs84ToCartesian([e[0], e[1]]),
    );
    entity._cache = new Cesium.PolygonHierarchy(cartesians);
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
