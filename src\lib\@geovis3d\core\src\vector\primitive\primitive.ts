import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.Primitive} 构造函数参数
 */
export type PrimitiveConstructorOptions = ConstructorParameters<typeof Cesium.Primitive>[0];

/**
 * {@link Cesium.Primitive} 拓展用法与 {@link Cesium.Primitive} 基本一致。
 *
 * `GcPrimitive.event`鼠标事件监听
 */
export class GcPrimitive extends Cesium.Primitive {
  constructor(options?: PrimitiveConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
