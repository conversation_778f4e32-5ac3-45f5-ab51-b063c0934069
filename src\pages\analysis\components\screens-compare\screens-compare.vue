<!-- 多屏对比 -->
<script lang="ts" setup>
import LayerManager from '@/widget/layer/layer-manager.vue';
import { X3d } from '@x3d/all';
import { createCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'ScreensCompare' });

const emit = defineEmits<ScreensCompareEmits>();

export interface ScreensCompareEmits {
  (event: 'close', data?: unknown): void;
}

const splitNum = ref(1);

function clickItem(num: any) {
  splitNum.value = num;
}

const elRef = shallowRef<HTMLDivElement>();
const viewer = createCzViewer(elRef, {
  baseLayer: false,
  baseLayerPicker: false, // 图层选择器
  fullscreenButton: false, // 全屏按钮
  geocoder: false, // 右上角查询搜索
  infoBox: false, // 信息框
  homeButton: false, // home按钮
  sceneModePicker: false, // 3d 2d选择器
  selectionIndicator: false, //
  animation: false, // 左下角仪表
  timeline: false, // 时间轴
  navigationHelpButton: false, // 右上角帮助按钮
  shouldAnimate: true, // 允许动画
});

watch(viewer, (viewer) => {
  if (viewer) {
    X3d.register(viewer);
    viewer.value = viewer;

    // viewer.scene.globe.depthTestAgainstTerrain = false;

    // viewer.scene.postProcessStages.fxaa.enabled = true; // 抗锯齿
    // 取消双击事件 ，双击的话，会视角直接切到该实体，且无法拖拽
    viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK,
    );
    // 关闭地面大气层
    viewer.scene.globe.showGroundAtmosphere = false;
    // 关闭天空大气层
    viewer.scene.skyAtmosphere.show = false;
  }
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="多屏对比"
    class="w-400px h-200px!"
    @close="emit('close')"
  >
    <div class="inner-win-content" flex="~">
      <div
        class="inner-earth-1 item"
        :class="{ 'b-#fff!': splitNum === 1 }"
        @click="clickItem(1)"
      >
        <span class="txt">1</span>
        <img src="~@/assets/images/earth-bg.png">
      </div>
      <div
        class="item inner-earth-2"
        :class="{ 'b-#fff!': splitNum === 2 }"
        @click="clickItem(2)"
      >
        <div class="earth-2-child">
          <span class="txt">1</span>
          <img src="~@/assets/images/earth-bg.png">
        </div>
        <div class="earth-2-child">
          <span class="txt">2</span>
          <img src="~@/assets/images/earth-bg.png">
        </div>
      </div>
    </div>
  </drag-card>
  <teleport v-if="splitNum === 2" to="#cesium-container">
    <div ref="elRef" flex="1 shrink-0" />
    <LayerManager v-if="viewer" v-slot="{ onClick }">
      <el-button
        h="60px!"
        px="21px!"
        position="absolute left-[calc(50%+30px)] z-100 bottom-60px"
        rd="5px!"
        class="bg-[var(--el-bg-color)]!"
        @click.stop="onClick"
      >
        <el-icon class="i-custom:layer" mr="10px" text="25px!" /><span text="20px">
          图层
        </span>
      </el-button>
    </LayerManager>
  </teleport>
</template>

<style lang="scss" scoped>
.inner-win-content {
  width: 100%;
  padding: 10px;
  color: white;

  .el-switch__label {
    color: white;
  }

  .item {
    position: relative;
    display: inline-block;
    width: 200px;
    height: 100px;
    overflow: hidden;
    font-size: 0;
    cursor: pointer;
    background: rgb(0 0 0 / 30%);
    border: 2px solid #717e8f;

    .txt {
      position: absolute;
      top: 20px;
      left: 20px;
      width: 8px;
      height: 16px;
      font-family: Arial, Arial-Bold, sans-serif;
      font-size: 22px;
      font-weight: 700;
      color: #3865a0;
      text-align: left;
      letter-spacing: 2px;
    }
  }

  img {
    display: inline;
  }

  .inner-earth-1 {
    margin: 0 10px 0 0;
    text-align: center;

    img {
      width: 144px;
      height: 134px;
      margin: 40px 0;
    }
  }

  .inner-earth-2 {
    overflow: hidden;

    .earth-2-child {
      position: relative;
      display: inline-block;
      width: 50%;
      height: 100%;
      text-align: center;

      img {
        width: 88px;
        height: 82px;
        margin: 50px 0;
      }

      .btns {
        position: absolute;
        bottom: 10px;
        left: 0;
        display: inline-block;
        width: 100%;

        :deep() .el-switch {
          width: 92px;

          .el-switch__label {
            color: #fff;
          }

          .el-switch__label * {
            font-size: 12px;
          }

          .el-switch__core {
            width: 34px;
            height: 14px;
          }

          .el-switch__core::after {
            top: 0;
            height: 11px;
          }
        }
      }
    }

    .earth-2-child:nth-child(1) {
      width: calc(50% - 2px);
      border-right: 2px solid #717e8f;
    }
  }

  .inner-earth-3 {
    margin: 0 10px 0 0;

    .earth-3-child {
      position: relative;
      box-sizing: border-box;
      display: inline-block;
      width: 50%;
      height: 50%;
      overflow: hidden;
      text-align: center;

      img {
        width: 50px;
        height: 50px;
        margin: 28px 0;
      }
    }

    .earth-3-child:nth-child(1) {
      border-right: 2px solid #717e8f;
      border-bottom: 2px solid #717e8f;
    }

    .earth-3-child:nth-child(2) {
      border-bottom: 2px solid #717e8f;
    }

    .earth-3-child:nth-child(3) {
      border-right: 2px solid #717e8f;
    }
  }

  .inner-earth-4 {
    margin: auto;

    .earth-4-child {
      position: relative;
      box-sizing: border-box;
      display: inline-block;
      width: 130px;
      height: 50%;
      overflow: hidden;
      text-align: center;

      img {
        width: 50px;
        height: 50px;
        margin: 28px 0;
      }
    }

    .earth-4-child:nth-child(1),
    .earth-4-child:nth-child(2) {
      border-right: 2px solid #717e8f;
      border-bottom: 2px solid #717e8f;
    }

    .earth-4-child:nth-child(3) {
      width: 121px;
      border-bottom: 2px solid #717e8f;
    }

    .earth-4-child:nth-child(4),
    .earth-4-child:nth-child(5) {
      border-right: 2px solid #717e8f;
    }

    .earth-4-child:nth-child(6) {
      width: 121px;
    }
  }

  .inner-multiple {
    float: left;
    box-sizing: border-box;
    border: 2px solid #0093e3;

    .inner-multiple-span {
      display: inline-block;
      margin-top: 30%;
      margin-left: 50%;
    }
  }

  .inner-multiple-1 {
    height: 360px;
    margin-left: 33%;

    .inner-multiple-span {
      margin-top: 50%;
    }
  }

  .inner-multiple-2 {
    width: 50%;
    height: 360px;

    .inner-multiple-span {
      margin-top: 30%;
    }
  }

  .inner-multiple-num-3 {
    width: 33.3%;
    height: 360px;

    .inner-multiple-span {
      margin-top: 50%;
    }
  }

  .inner-multiple-num-4 {
    width: 50%;
    height: 180px;

    .inner-multiple-span {
      margin-top: 4%;
    }
  }

  .inner-multiple-num-6 {
    width: 33.3%;
    height: 180px;

    .inner-multiple-span {
      margin-top: 4%;
    }
  }

  .inner-multiple-num-9 {
    width: 33.3%;
    height: 140px;

    .inner-multiple-span {
      margin-top: 4%;
    }
  }
}
</style>
