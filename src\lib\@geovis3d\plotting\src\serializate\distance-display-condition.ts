import * as Cesium from 'cesium';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface DistanceDisplayConditionSerializateJSON {
  near?: number;
  far?: number;
}

export type DistanceDisplayConditionKey = keyof DistanceDisplayConditionSerializateJSON;

export class DistanceDisplayConditionSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.DistanceDisplayCondition,
    time?: Cesium.JulianDate,
  ): DistanceDisplayConditionSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      near: getValue('near'),
      far: getValue('far'),
    };
  }

  static fromJSON(
    json?: DistanceDisplayConditionSerializateJSON,
  ): Cesium.DistanceDisplayCondition | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json);
    return new Cesium.DistanceDisplayCondition(getValue('near'), getValue('far'));
  }
}
