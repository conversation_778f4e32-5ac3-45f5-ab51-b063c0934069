import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.Cesium3DTileset} 构造函数参数
 */
export type Cesium3DTilesetConstructorOptions = ConstructorParameters<
  typeof Cesium.Cesium3DTileset
>[0];

/**
 * {@link Cesium.Cesium3DTileset} 拓展用法与 {@link Cesium.Cesium3DTileset} 基本一致。
 * `GcCesium3DTileset.event`鼠标事件监听
 */
export class GcCesium3DTileset extends Cesium.Cesium3DTileset {
  constructor(options: Cesium3DTilesetConstructorOptions) {
    super(options);
    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
