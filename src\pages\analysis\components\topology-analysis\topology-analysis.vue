<!-- 拓扑分析 -->
<script lang="ts" setup>
import type {
  Feature,
  FeatureCollection,
  LineString,
  MultiPolygon,
  Point,
  Polygon,
} from 'geojson';
import { cartesianToWgs84, CzEntity, CzPlotEntity } from '@x3d/all';
import { useCzEntities } from '@x3d/vue-hooks';

import {
  CallbackProperty,
  Cartesian3,
  Color,
  PointGraphics,
  PolygonGraphics,
  PolygonHierarchy,
} from 'cesium';

defineOptions({ name: 'TopologyAnalysis' });
const emits = defineEmits<{
  (event: 'close'): void;
}>();
type FeatureType = Feature<Polygon | MultiPolygon>;
const plotEntities1 = shallowRef<CzPlotEntity>();
const plotEntities2 = shallowRef<CzPlotEntity>();
const geojsons = ref<Feature[]>([]);
const resultGeojson = ref<
  FeatureCollection<Point | Polygon | MultiPolygon> | Feature<Point> | FeatureType
>();
const { isActive } = useCzEntities(() =>
  [plotEntities1.value, plotEntities2.value].filter(e => e),
);
const geojsonTypes = computed(() => geojsons.value.map(g => turf.getType(g)));
function drawPoint() {
  plotEntities1.value && plotEntities2.value && handleClear();
  const plotEntities = geojsons.value?.length ? plotEntities2 : plotEntities1;
  plotEntities.value = new CzPlotEntity({
    scheme: {
      forceTerminate: (entity) => {
        const bool = entity.record.positions.getLength() === 1;
        if (bool) {
          const car3 = entity.record.positions.getPositions()[0];
          const [lon, lat] = cartesianToWgs84(car3);
          geojsons.value.push(turf.point([lon, lat]));
        }
        return bool;
      },
      effect(entity) {
        entity.point ??= new PointGraphics({
          pixelSize: 10.0,
          color: Color.fromCssColorString('#0099FF'),
          outlineColor: Color.WHITE,
          outlineWidth: 2,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        });
        const positions = entity.record.positions.getPositions();
        if (positions.length) {
          entity.position = new Cesium.ConstantPositionProperty(positions[0]);
        }
      },
    },
  });
}
function drawPolyline() {
  plotEntities1.value && plotEntities2.value && handleClear();
  const plotEntities = geojsons.value?.length ? plotEntities2 : plotEntities1;
  plotEntities.value = new CzPlotEntity({
    scheme: {
      manualTerminate: (entity) => {
        const bool = entity.record.positions.getLength() > 2;
        if (bool) {
          const cars3 = entity.record.positions.getPositions();
          const points = cars3.map((car3: Cartesian3) => {
            const [lon, lat] = cartesianToWgs84(car3);
            return [lon, lat];
          });
          geojsons.value.push(turf.lineString(points));
        }
        return bool;
      },
      effect(entity) {
        entity.polyline ??= new Cesium.PolylineGraphics({
          material: Cesium.Color.fromRandom().withAlpha(1),
          width: 4,
          clampToGround: true,
          depthFailMaterial: Cesium.Color.WHITE,
        });
        const { record, controller } = entity;

        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const cache = positions.length >= 2 ? positions : [];
        entity.polyline.positions = new Cesium.CallbackProperty(() => cache, false);
        entity.position = new Cesium.ConstantPositionProperty(
          record.positions.getCenter()!,
        );
      },
    },
  });
}
function drawPolygon() {
  plotEntities1.value && plotEntities2.value && handleClear();
  const plotEntities = geojsons.value?.length ? plotEntities2 : plotEntities1;
  plotEntities.value = new CzPlotEntity({
    scheme: {
      manualTerminate: (entity) => {
        const bool = entity.record.positions.getLength() > 2;
        if (bool) {
          const cars3 = entity.record.positions.getPositions();
          const points = cars3.map((car3: Cartesian3) => {
            const [lon, lat] = cartesianToWgs84(car3);
            return [lon, lat];
          });
          points.push(points?.[0]);
          geojsons.value.push(turf.polygon([points]));
        }
        return bool;
      },
      effect(entity) {
        entity.polygon ??= new PolygonGraphics({
          material: Color.fromRandom().withAlpha(0.4),
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
        });
        entity.polyline ??= new Cesium.PolylineGraphics({
          material: Color.WHITE,
          width: 2,
          clampToGround: true,
          depthFailMaterial: Color.WHITE,
        });
        const { record, controller } = entity;
        const positions = record.positions.getPositions();
        const mouse = controller.mouse;
        mouse && positions.push(mouse.clone());
        const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);
        if (coords.length >= 3) {
          const hierarchy = new PolygonHierarchy(positions);
          entity.polygon.hierarchy = new CallbackProperty(() => hierarchy, false);
          entity.polyline.positions = new Cesium.CallbackProperty(
            () => [...positions, positions[0]],
            false,
          );
        }
      },
    },
  });
}
const { isActive: showAnalysis } = useCzEntities(() => {
  if (resultGeojson.value && geojsonTypes.value?.every(e => e === 'LineString')) {
    const geojson = resultGeojson.value as FeatureCollection<Point>;
    const points = turf.coordAll(geojson);
    return points?.map(
      e =>
        new CzEntity({
          position: Cesium.Cartesian3.fromDegrees(e[0], e[1]),
          point: {
            pixelSize: 5,
            color: Cesium.Color.RED,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
          },
        }),
    );
  }
  if (resultGeojson.value && geojsonTypes.value?.every(e => e === 'Polygon')) {
    let pointsArr: number[][][] = [];
    const geojson = resultGeojson.value as FeatureCollection<Polygon>;
    turf.featureEach(geojson, (currentFeature: Feature<Polygon>) => {
      if (turf.getType(currentFeature).includes('Multi')) {
        turf.featureEach(turf.flatten(currentFeature), (feature) => {
          pointsArr.push(turf.getCoords(feature)[0]);
        });
      }
      else {
        pointsArr = turf.getCoords(currentFeature);
      }
    });
    console.log(pointsArr);
    return pointsArr?.map(
      points =>
        new CzEntity({
          polygon: {
            hierarchy: new CallbackProperty(
              () =>
                new PolygonHierarchy(
                  points.map(e => Cartesian3.fromDegrees(e[0], e[1])),
                ),
              false,
            ),
            material: Color.RED.withAlpha(0.4),
            extrudedHeight: 1,
            outline: true,
            outlineColor: Cesium.Color.WHITE,
            // heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
          },
        }),
    );
  }
  if (
    resultGeojson.value
    && geojsonTypes.value?.some(e => e === 'Point')
    && (geojsonTypes.value?.some(e => e === 'LineString')
      || geojsonTypes.value?.some(e => e === 'Polygon'))
  ) {
    // resultGeojson.value
    const points = turf.coordAll(resultGeojson.value);
    return points.map(
      point =>
        new CzEntity({
          position: Cesium.Cartesian3.fromDegrees(point[0], point[1]),
          point: {
            pixelSize: 10,
            color: Cesium.Color.RED,
            outlineColor: Cesium.Color.WHITE,
            outlineWidth: 2,
          },
        }),
    );
  }
});
function handleClear() {
  plotEntities1.value = undefined;
  plotEntities2.value = undefined;
  resultGeojson.value = undefined;
  geojsons.value = [];
  showAnalysis.value = true;
  isActive.value = true;
}
function lineIntersect() {
  const [geo1, geo2] = geojsons.value as Feature<LineString>[];
  resultGeojson.value = turf.lineIntersect(geo1, geo2, {
    ignoreSelfIntersections: true,
  });
  showAnalysis.value = true;
}
function polygonIntersect() {
  const [geo1, geo2] = geojsons.value;
  const featureCollection = turf.featureCollection([
    geo1,
    geo2,
  ]) as FeatureCollection<Polygon>;
  resultGeojson.value = turf.intersect(featureCollection) as Feature<Polygon>;
  showAnalysis.value = true;
  isActive.value = false;
}
function polygonUnion() {
  const [geo1, geo2] = geojsons.value;
  const featureCollection = turf.featureCollection([
    geo1,
    geo2,
  ]) as FeatureCollection<Polygon>;
  resultGeojson.value = turf.union(featureCollection) as FeatureType;
  showAnalysis.value = true;
  isActive.value = false;
}
function polygonDifference() {
  const [geo1, geo2] = geojsons.value;
  const featureCollection = turf.featureCollection([
    geo1,
    geo2,
  ]) as FeatureCollection<Polygon>;
  resultGeojson.value = turf.difference(featureCollection) as FeatureType;
  showAnalysis.value = true;
  isActive.value = false;
}
function polygonSymmetricDiff() {
  const [geo1, geo2] = geojsons.value as Feature<Polygon>[];
  const diff1 = turf.difference(turf.featureCollection([geo1, geo2])) as FeatureType;
  const diff2 = turf.difference(turf.featureCollection([geo2, geo1])) as FeatureType;
  resultGeojson.value = turf.union(turf.featureCollection([diff1, diff2])) as FeatureType;
  showAnalysis.value = true;
  isActive.value = false;
}
function nearestPointOnLine() {
  const [geo1, geo2] = geojsons.value as Feature<Point | LineString>[];
  const point = (turf.getType(geo1) === 'Point' ? geo1 : geo2) as Feature<Point>;
  const line = (turf.getType(geo1) === 'LineString' ? geo1 : geo2) as Feature<LineString>;
  resultGeojson.value = turf.nearestPointOnLine(line, point, {
    units: 'miles',
  });
  showAnalysis.value = true;
}
function polygonTangents() {
  const [geo1, geo2] = geojsons.value as Feature<Point | Polygon>[];
  const point = (turf.getType(geo1) === 'Point' ? geo1 : geo2) as Feature<Point>;
  const polygon = (turf.getType(geo1) === 'Polygon' ? geo1 : geo2) as Feature<Polygon>;
  resultGeojson.value = turf.polygonTangents(point, polygon);
  showAnalysis.value = true;
}
onUnmounted(() => {
  handleClear();
});
</script>

<template>
  <drag-card
    select="none"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    title="拓扑分析"
    class="w-400px"

    @close="emits('close')"
  >
    <div p="20px">
      <el-button type="primary" @click="drawPoint">
        绘制点
      </el-button>
      <el-button type="primary" @click="drawPolyline">
        绘制线
      </el-button>
      <el-button type="primary" @click="drawPolygon">
        绘制面
      </el-button>
      <el-divider mt="40px!">
        分析类型
      </el-divider>
      <div flex="~ justify-start items-center" my="16px" text="30px">
        <div v-if="geojsonTypes?.every((e) => e === 'Polygon')">
          <el-button
            link
            mr="10px"
            title="并集"
            @click="polygonUnion"
          >
            <el-icon class="i-custom:union" text="20px!" />
          </el-button>
          <el-button
            link
            mr="10px"
            title="交集"
            @click="polygonIntersect"
          >
            <el-icon class="i-custom:intersection" text="20px!" />
          </el-button>
          <el-button
            link
            mr="10px"
            title="差集"
            @click="polygonDifference"
          >
            <el-icon class="i-custom:difference" text="20px!" />
          </el-button>
          <el-button
            link
            mr="10px"
            title="对称差集"
            @click="polygonSymmetricDiff"
          >
            <el-icon class="i-custom:symmetric-diff" text="20px!" />
          </el-button>
        </div>
        <div v-if="geojsonTypes?.every((e) => e === 'LineString')">
          <el-button
            link
            mx="10px!"
            title="计算交点"
            @click="lineIntersect"
          >
            <el-icon class="i-custom:intersection-point" text="20px!" />
          </el-button>
        </div>
        <div
          v-if="
            !geojsonTypes
              || (geojsonTypes?.filter((e) => e === 'Polygon')
                && geojsonTypes?.some((e) => e === 'Point'))
          "
        >
          <el-button
            link
            mx="10px!"
            title="计算切点"
            @click="polygonTangents"
          >
            <el-icon class="i-custom:tangent-point" text="25px!" />
          </el-button>
        </div>
        <div
          v-if="
            !geojsonTypes
              || (geojsonTypes?.some((e) => e === 'LineString')
                && geojsonTypes?.some((e) => e === 'Point'))
          "
        >
          <el-button
            link
            mx="10px!"
            title="计算最近点"
            @click="nearestPointOnLine"
          >
            <el-icon class="i-custom:distance-line" text="25px!" />
          </el-button>
        </div>
        <div class="flex-1 text-right">
          <el-button link @click="(isActive = true), (showAnalysis = false)">
            <el-icon class="i-material-symbols:delete-outline" text="25px! #999!" />
          </el-button>
        </div>
      </div>
      <div text="#FCC650 14px" mt="16px">
        提示：最多只可绘制两个元素。
      </div>
    </div>
    <template #footer>
      <el-button class="plain-#FF6363 px-26px!" @click="handleClear">
        清除
      </el-button>
    </template>
  </drag-card>
</template>
