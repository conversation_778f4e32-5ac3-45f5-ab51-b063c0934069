<script lang="ts" setup>
import type { EgisConfig } from '../assets/types';

export interface LayerFormEgisProps {
  modelValue?: EgisConfig;

}

export interface LayerFormEgisEmits {
  (event: 'update:modelValue', data?: EgisConfig): void;
}

defineOptions({ name: 'LayerFormEgis' });
const props = defineProps<LayerFormEgisProps>();
const emit = defineEmits<LayerFormEgisEmits>();

const model = useVModel(props, 'modelValue', emit, { deep: true, defaultValue: {}, clone: true, passive: true });
</script>

<template>
  <div flex="~ col">
    <header-title2 my="16px" content="EGIS配置" />

    <el-form>
      <el-form-item :label-width="$vh(88)" p="x-20px" label="用户名">
        <el-input
          v-model="model!.username"
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item :label-width="$vh(88)" p="x-20px" label="密码">
        <el-input
          v-model="model!.password"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
