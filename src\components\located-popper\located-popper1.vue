<!-- 弹窗 -->
<script lang="ts" setup>
import type { Cartesian3 } from 'cesium';
import type { VNodeChild } from 'vue';

import type { BasicOrComponentOpt } from '../basic-or-component/basic-or-component.vue';
import { cartesianToCanvascoord, cartesianToCartographic, cartographicToCartesian } from '@x3d/all';
import { useCzEventListener, useCzViewer } from '@x3d/vue-hooks';

defineOptions({ name: 'LocatedPopper1', inheritAttrs: false });

const props = withDefaults(
  defineProps<LocatedPopper1Props>(),
  {
    clampToGround: true,
    to: '.cesium-widget',
  },
);

const emit = defineEmits<LocatedPopper1Emits>();
const slots = defineSlots<LocatedPopper1Slots>();

export interface LocatedPopper1Props {
  position?: Cartesian3;
  to?: any;
  /** 是否贴地 */
  clampToGround?: boolean;
  showClose?: boolean;
  header?: BasicOrComponentOpt;
  extra?: BasicOrComponentOpt;
  content?: BasicOrComponentOpt;
  headerClass?: any;
  extraClass?: any;
  contentClass?: any;
  closeClass?: any;

}
export interface LocatedPopper1Emits {
  (event: 'close',): void;
}

export interface LocatedPopper1Slots {
  header?: VNodeChild;
  extra?: BasicOrComponentOpt;
  default?: VNodeChild;
}

const viewer = useCzViewer();
const popperRef = shallowRef<HTMLElement>();

function computer() {
  if (!popperRef.value) {
    return;
  }
  const style = popperRef.value!.style!;
  // 不传入点位  则将窗口居中
  if (!props.position) {
    style.top = `${(viewer.value?.canvas.offsetHeight / 2).toFixed(2)}px`;
    style.left = `${(viewer.value?.canvas.offsetWidth / 2).toFixed(2)}px`;
    return;
  }

  let position = props.position.clone();

  if (props.clampToGround) {
    // 如果贴地 则每次渲染都要去获取高度
    // 高度保留两位小数防窗口抖动
    const cartographic = cartesianToCartographic(position);
    const height = viewer.value?.scene.globe.getHeight(cartographic) || 0;

    if (height > 0) {
      cartographic.height = +height.toFixed(2);
    }
    position = cartographicToCartesian(cartographic);
  }

  const { x, y } = cartesianToCanvascoord(props.position, viewer.value.scene) ?? {};
  if (x && y) {
    style.top = `${y.toFixed(2)}px`;
    style.left = `${x.toFixed(2)}px`;
  }
}
useCzEventListener(
  () => viewer.value.scene.postRender,
  () => computer(),
);
</script>

<template>
  <teleport :to="to">
    <div
      ref="popperRef"
      v-bind="$attrs"
      class="located-popper1"
    >
      <div
        class="located-popper1__header"
        :class="headerClass"
      >
        <div class="located-popper1__header-content">
          <basic-or-component :is="slots.header ?? props.header" />
        </div>
        <div class="located-popper1__header-extra" :class="extraClass">
          <basic-or-component :is="slots.extra ?? props.extra" />
        </div>
        <el-button
          v-if="showClose"
          text
          class="absolute right-0 text-20px!"
          :class="closeClass"
          @click="emit('close')"
        >
          <el-icon class="i-material-symbols:close-rounded" />
        </el-button>
      </div>
      <div
        class="located-popper1__content"
        :class="contentClass"
      >
        <basic-or-component :is="slots.default ?? props.content" />
      </div>
    </div>
  </teleport>
</template>

<style lang="scss">
.located-popper1 {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 920px;

  // height: 416px;
  padding-bottom: 10px;
  pointer-events: none;
  background-image: url('./assets/located-popper1.svg');
  background-size: 100% 100%;
  transform: translate(-50%, calc(-100% - 40px));

  > * {
    pointer-events: initial;
  }
}

.located-popper1__header {
  position: relative;
  display: flex;
  align-items: center;
  height: 54px;
  padding-right: 54px;
  padding-left: 30px;
  font-size: 20px;
  font-weight: 600;
  background: #292b2e;
  border: 1px solid #292b2e;
  backdrop-filter: blur(10px);
}

.located-popper1__header-content {
  flex: 1;
  overflow: hidden;
}

// .located-popper1__header-extra {
// }

.located-popper1__content {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}
</style>
