<!-- ImageMaterialPropertyAttribute 属性编辑 -->
<script lang="ts" setup>
import type { ImageMaterialPropertySerializateJSON } from '../@geovis3d/plotting';
import { useShallowBinding } from '../hooks';

import StringAttribute from '../string-attribute.vue';

defineOptions({ name: 'ImageMaterialPropertyAttribute' });

const props = defineProps<{
  modelValue?: ImageMaterialPropertySerializateJSON;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', data?: ImageMaterialPropertySerializateJSON): void;
}>();

const model = ref<ImageMaterialPropertySerializateJSON>({});
useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <!-- <el-form-item label="图片" /> -->
  <StringAttribute v-model="model.image" label="图片地址" />
</template>
