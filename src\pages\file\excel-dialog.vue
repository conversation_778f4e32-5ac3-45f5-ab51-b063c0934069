<!-- 表格组件 -->
<script lang="ts" setup>
export interface ExcelDialogProps {
  list?: any[];
}

export interface ExcelDialogEmits {
  (event: 'update:list', data?: any[]): void;
}
defineOptions({ name: 'ExcelDialog' });
const props = defineProps<ExcelDialogProps>();
const emit = defineEmits<ExcelDialogEmits>();
const model = shallowRef(false);

watchEffect(() => {
  model.value = !!props.list;
});

const propNames = computed(() => {
  return Object.keys(props.list?.[0] ?? {});
});

const data = [
  {
    title: '柱状图',
    type: 'bar',
    x_data: ['卫星', '航拍', '地形', '传感器', '监控'],
    y_data: ['300', '400', '270', '600', '500'],
  },
  {
    title: '饼图',
    type: 'pie',
    data: [
      { value: 1048, name: '原始影像' },
      { value: 735, name: '成果影像' },
      { value: 580, name: '矢量数据' },
      { value: 484, name: '矢量瓦片' },
      { value: 300, name: '影像瓦片' },
    ],
  },
  {
    title: '折线图',
    type: 'line',
    x_data: ['7月', '8月', '9月', '10月', '11月'],
    y_data: ['300', '400', '270', '600', '500'],
  },
];

const barOption = computed(() => {
  const d = data.find(e => e.title === '柱状图');
  return {

    title: {
      text: '柱状图',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
    },
    textStyle: {
      color: '#fff',
    },
    xAxis: {
      type: 'category',
      data: d?.x_data,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: d?.y_data,
        type: 'bar',
      },
    ],
  };
});

const pieOption = computed(() => {
  const d = data.find(e => e.title === '饼图');
  return {
    title: {
      text: '饼图',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
    },
    textStyle: {
      color: '#fff',
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: '50%',
        data: d?.data ?? [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };
});

const lineOption = computed(() => {
  const d = data.find(e => e.title === '折线图');
  return {

    title: {
      text: '折线图',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
    },
    textStyle: {
      color: '#fff',
    },
    xAxis: {
      type: 'category',
      data: d?.x_data,
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: d?.y_data,
        type: 'line',
      },
    ],
  };
});

const scatterOption = computed(() => {
  return {
    title: {
      text: '散点图',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
    },
    textStyle: {
      color: '#fff',
    },
    xAxis: {},
    yAxis: {},
    series: [
      {
        symbolSize: 20,
        data: [
          [10.0, 8.04],
          [8.07, 6.95],
          [13.0, 7.58],
          [9.05, 8.81],
          [11.0, 8.33],
          [14.0, 7.66],
          [13.4, 6.81],
          [10.0, 6.33],
          [14.0, 8.96],
          [12.5, 6.82],
          [9.15, 7.2],
          [11.5, 7.2],
          [3.03, 4.23],
          [12.2, 7.83],
          [2.02, 4.47],
          [1.05, 3.33],
          [4.05, 4.96],
          [6.03, 7.24],
          [12.0, 6.26],
          [12.0, 8.84],
          [7.08, 5.82],
          [5.02, 5.68],
        ],
        type: 'scatter',
      },
    ],
  };
});

const heapMapOption = computed(() => {
  const hours = [
    '12a',
    '1a',
    '2a',
    '3a',
    '4a',
    '5a',
    '6a',
    '7a',
    '8a',
    '9a',
    '10a',
    '11a',
    '12p',
    '1p',
    '2p',
    '3p',
    '4p',
    '5p',
    '6p',
    '7p',
    '8p',
    '9p',
    '10p',
    '11p',
  ];
  // prettier-ignore
  const days = [
    'Saturday',
    'Friday',
    'Thursday',
    'Wednesday',
    'Tuesday',
    'Monday',
    'Sunday',
  ];
  const data = [[0, 0, 5], [0, 1, 1], [0, 2, 0], [0, 3, 0], [0, 4, 0], [0, 5, 0], [0, 6, 0], [0, 7, 0], [0, 8, 0], [0, 9, 0], [0, 10, 0], [0, 11, 2], [0, 12, 4], [0, 13, 1], [0, 14, 1], [0, 15, 3], [0, 16, 4], [0, 17, 6], [0, 18, 4], [0, 19, 4], [0, 20, 3], [0, 21, 3], [0, 22, 2], [0, 23, 5], [1, 0, 7], [1, 1, 0], [1, 2, 0], [1, 3, 0], [1, 4, 0], [1, 5, 0], [1, 6, 0], [1, 7, 0], [1, 8, 0], [1, 9, 0], [1, 10, 5], [1, 11, 2], [1, 12, 2], [1, 13, 6], [1, 14, 9], [1, 15, 11], [1, 16, 6], [1, 17, 7], [1, 18, 8], [1, 19, 12], [1, 20, 5], [1, 21, 5], [1, 22, 7], [1, 23, 2], [2, 0, 1], [2, 1, 1], [2, 2, 0], [2, 3, 0], [2, 4, 0], [2, 5, 0], [2, 6, 0], [2, 7, 0], [2, 8, 0], [2, 9, 0], [2, 10, 3], [2, 11, 2], [2, 12, 1], [2, 13, 9], [2, 14, 8], [2, 15, 10], [2, 16, 6], [2, 17, 5], [2, 18, 5], [2, 19, 5], [2, 20, 7], [2, 21, 4], [2, 22, 2], [2, 23, 4], [3, 0, 7], [3, 1, 3], [3, 2, 0], [3, 3, 0], [3, 4, 0], [3, 5, 0], [3, 6, 0], [3, 7, 0], [3, 8, 1], [3, 9, 0], [3, 10, 5], [3, 11, 4], [3, 12, 7], [3, 13, 14], [3, 14, 13], [3, 15, 12], [3, 16, 9], [3, 17, 5], [3, 18, 5], [3, 19, 10], [3, 20, 6], [3, 21, 4], [3, 22, 4], [3, 23, 1], [4, 0, 1], [4, 1, 3], [4, 2, 0], [4, 3, 0], [4, 4, 0], [4, 5, 1], [4, 6, 0], [4, 7, 0], [4, 8, 0], [4, 9, 2], [4, 10, 4], [4, 11, 4], [4, 12, 2], [4, 13, 4], [4, 14, 4], [4, 15, 14], [4, 16, 12], [4, 17, 1], [4, 18, 8], [4, 19, 5], [4, 20, 3], [4, 21, 7], [4, 22, 3], [4, 23, 0], [5, 0, 2], [5, 1, 1], [5, 2, 0], [5, 3, 3], [5, 4, 0], [5, 5, 0], [5, 6, 0], [5, 7, 0], [5, 8, 2], [5, 9, 0], [5, 10, 4], [5, 11, 1], [5, 12, 5], [5, 13, 10], [5, 14, 5], [5, 15, 7], [5, 16, 11], [5, 17, 6], [5, 18, 0], [5, 19, 5], [5, 20, 3], [5, 21, 4], [5, 22, 2], [5, 23, 0], [6, 0, 1], [6, 1, 0], [6, 2, 0], [6, 3, 0], [6, 4, 0], [6, 5, 0], [6, 6, 0], [6, 7, 0], [6, 8, 0], [6, 9, 0], [6, 10, 1], [6, 11, 0], [6, 12, 2], [6, 13, 1], [6, 14, 3], [6, 15, 4], [6, 16, 0], [6, 17, 0], [6, 18, 0], [6, 19, 0], [6, 20, 1], [6, 21, 2], [6, 22, 2], [6, 23, 6]]
    .map((item) => {
      return [item[1], item[0], item[2] || '-'];
    });
  return {
    title: {
      text: '热力图',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
    },
    textStyle: {
      color: '#fff',
    },
    tooltip: {
      position: 'top',
    },
    grid: {
      height: '50%',
      top: '10%',
    },
    xAxis: {
      type: 'category',
      data: hours,
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: 'category',
      data: days,
      splitArea: {
        show: true,
      },
    },
    visualMap: {
      min: 0,
      max: 10,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%',
    },
    series: [
      {
        name: 'Punch Card',
        type: 'heatmap',
        data,
        label: {
          show: true,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };
});
</script>

<template>
  <el-dialog :model-value="model" title="图表解析" destroy-on-close append-to-body @close="emit('update:list', undefined)">
    <el-scrollbar h="50vh!">
      <el-table :data="list ?? []">
        <template v-for="item in propNames" :key="item">
          <el-table-column :prop="item" :label="item" />
        </template>
      </el-table>
      <div grid="~ cols-2 gap-20px" mt="40px">
        <vue-echarts :option="barOption" autoresize class="h-400px! w-100%!" />
        <vue-echarts :option="pieOption" autoresize class="h-400px! w-100%!" />
        <vue-echarts :option="lineOption" autoresize class="h-400px! w-100%!" />
        <vue-echarts :option="scatterOption" autoresize class="h-400px! w-100%!" />
        <vue-echarts :option="heapMapOption" autoresize class="h-400px! w-100%!" grid="col-span-2" />
      </div>
    </el-scrollbar>
  </el-dialog>
</template>
