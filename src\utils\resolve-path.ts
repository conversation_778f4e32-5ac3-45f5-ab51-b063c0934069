/**
 * 将路径正确解析成访问地址的绝对路径
 *
 * @param path 预期的绝对路径
 * @returns 转换后的公共路径
 */
export function toPublicPath(path: string) {
  if (path.startsWith('http')) {
    return path;
  }
  path[0] !== '/' && (path += '/');
  return import.meta.env.VITE_PUBLIC_PATH + path?.replace(/^(\.*)\//, '');
}

/**
 * 将路径转换为服务器中的静态资源地址
 *
 * @param path 预期的绝对路径
 * @returns 返回转换后的文件路径
 */
export function toStaticFilePath(path: string) {
  if (path.startsWith('http')) {
    return path;
  }
  if (!path) {
    return '';
  }
  path[0] !== '/' && (path += '/');

  return import.meta.env.VITE_STATIC_FILE_URL + path;
}

/**
 * 将路径转换为文件上传后的访问地址
 *
 * @param path 预期的绝对路径
 * @returns 返回转换后的文件路径
 */
export function toFilePath(path: string) {
  if (path.startsWith('http')) {
    return path;
  }
  if (!path) {
    return '';
  }
  path[0] !== '/' && (path += '/');
  return import.meta.env.VITE_FILE_URL + path;
}
