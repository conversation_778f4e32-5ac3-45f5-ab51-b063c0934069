import { promiseTimeout } from './promise-timeout';

export type ThrottleCallback<T extends any[]> = (...rest: T) => void;

/**
 * 节流函数
 * @param callback
 * @param ms = 100 节流延迟，单位毫秒
 * @param trailing = true 是否执行最后一次触发
 * @param leading = false 是否执行第一次触发
 */
export function throttle<T extends any[]>(
  callback: ThrottleCallback<T>,
  ms = 100,
  trailing = true,
  leading = false,
): ThrottleCallback<T> {
  const restList: T[] = [];
  let tracked = false;
  const trigger = async () => {
    await promiseTimeout(ms);
    tracked = false;
    if (leading) {
      try {
        callback(...restList[0]);
      }
      catch (error) {
        console.error(error);
      }
    }
    if (trailing && (!leading || restList.length > 1)) {
      try {
        callback(...(restList.at(-1) ?? []));
      }
      catch (error) {
        console.error(error);
      }
    }
    restList.length = 0;
  };

  return (...rest: T) => {
    if (restList.length < 2) {
      restList.push(rest);
    }
    else {
      restList[1] = rest;
    }

    if (!tracked) {
      tracked = true;
      trigger();
    }
  };
}
