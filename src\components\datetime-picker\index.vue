<!-- DatetimePicker -->
<script lang="ts" setup>
type dateFunction = () => string | [string, string];
type disabledDateProp = (t: Date) => boolean;
interface shortcutsProp {
  text: string;
  value: string | [string, string] | dateFunction;
}
type modelValueProp = string | [string, string];
const props = withDefaults(
  defineProps<{
    modelValue: modelValueProp;
    label?: string;
    placeholder?: string;
    startPlaceholder?: string;
    endPlaceholder?: string;
    type?:
      | 'year'
      | 'month'
      | 'date'
      | 'datetime'
      | 'week'
      | 'datetimerange'
      | 'daterange';
    format?: string;
    defaultValue?: Date | [Date, Date];
    shortcuts?: shortcutsProp[];
    rangeSeparator?: string;
    max?: string | Date;
    min?: string | Date;
    disabledDate?: disabledDateProp;
    clearable?: boolean;
  }>(),
  {
    format: 'YYYY-MM-DD HH:mm:ss',
    rangeSeparator: '至',
    placeholder: '请选择时间',
    startPlaceholder: '开始时间',
    endPlaceholder: '结束时间',
    clearable: false,
  },
);

const emits = defineEmits<{
  (event: 'update:modelValue', data: modelValueProp): void;
  (event: 'change', data: modelValueProp): void;
}>();
defineComponent({ name: 'DatetimePicker' });
const value = ref(props.modelValue);
function disabledFunc(date: any) {
  if (props.disabledDate && typeof props.disabledDate === 'function') {
    return props.disabledDate(date);
  }
  else if (props.min || props.max) {
    return (
      dayjs(date).isSameOrAfter(dayjs(props.min))
      && dayjs(date).isSameOrBefore(dayjs(props.max))
    );
  }
}
function changeDateTime() {
  emits('update:modelValue', value.value);
  emits('change', value.value);
}
watch(
  () => props.modelValue,
  () => {
    value.value = [props.modelValue[0], props.modelValue[1]];
  },
);
</script>

<template>
  <div class="datetime-picker">
    <div v-if="label" class="datetime-picker-label">
      {{ label }}
    </div>
    <el-date-picker
      v-model="value"
      :placeholder="placeholder"
      :start-placeholder="startPlaceholder"
      :end-placeholder="endPlaceholder"
      :type="type"
      :format="format"
      :value-format="format"
      :default-value="defaultValue"
      :shortcuts="shortcuts"
      :range-separator="rangeSeparator"
      :picker-options="{ selectableRange: '18:30:00 - 20:30:00' }"
      :disabled-date="disabledFunc"
      :clearable="clearable"
      @change="changeDateTime"
    />
    <el-button text>
      <el-icon class="i-tabler:search" />
    </el-button>
  </div>
</template>

<style scoped lang="scss">
.datetime-picker {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: 44px;
  background: #0a2156;
  border: 1px solid #2049a8;
  border-radius: 2px;

  .datetime-picker-label {
    padding: 10px 20px;
    margin-right: 15px;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px;
    color: #bcdef3;
    text-align: left;
    background: rgb(21 67 118 / 50%);
  }

  :deep() .el-date-editor {
    --el-input-border-color: rgb(0 0 0 / 0%);
    --el-input-hover-border-color: rgb(0 0 0 / 0%);
    --el-border-color: rgb(0 0 0 / 0%);
    --el-input-focus-border-color: rgb(0 0 0 / 0%);
    --el-text-color-primary: #a3bdcb;

    .el-range__icon {
      display: none;
    }
  }

  .after-icon {
    width: 58px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    background: #153174;
  }
}

:deep() .el-button {
  --el-button-disabled-text-color: white;
}
</style>

<style lang="scss">
.el-date-range-picker {
  --el-disabled-border-color: rgb(0 0 0 / 0%);
  --el-disabled-bg-color: rgb(0 0 0 / 0%);

  .el-button--small {
    --el-button-disabled-text-color: #999;
  }
}
</style>
