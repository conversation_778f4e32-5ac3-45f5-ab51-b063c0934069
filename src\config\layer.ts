import * as Cesium from 'cesium';

export function createImageryProviders() {
  return [
    {
      label: '基础影像',
      cover: new URL('../assets/layer-cover/基础影像.png', import.meta.url).href,
      provider: new Cesium.WebMapTileServiceImageryProvider({
        url: `https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer`,
        layer: 'World_Imagery',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'default028mm',
        tileMatrixLabels: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18'],
        maximumLevel: 18,
        tilingScheme: new Cesium.WebMercatorTilingScheme(),
      }),
    },
  ];
}
