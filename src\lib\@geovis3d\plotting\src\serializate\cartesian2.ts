import * as Cesium from 'cesium';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface Cartesian2SerializateJSON {
  x?: number;
  y?: number;
}

export type Cartesian2Key = keyof Cartesian2SerializateJSON;

export class Cartesian2Serializate {
  private constructor() {}
  static toJSON(
    data?: Cesium.Cartesian2,
    time?: Cesium.JulianDate,
  ): Cartesian2SerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      x: getValue('x'),
      y: getValue('y'),
    };
  }

  static fromJSON(json?: Cartesian2SerializateJSON): Cesium.Cartesian2 | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json);
    return new Cesium.Cartesian2(getValue('x'), getValue('y'));
  }
}
