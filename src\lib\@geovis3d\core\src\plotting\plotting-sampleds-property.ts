import type { PlottingController } from './plotting-controller';

import * as Cesium from 'cesium';

/**
 * 采集点位配置
 */
export interface SampledPointSymbol {
  /**
   * 唯一ID
   */
  id: string;

  /**
   * 等待多久后才开始执行 秒
   */
  delay?: number;

  /**
   * 经过该点时距离上一点的耗时 秒
   */
  duration: number;

  /**
   * 笛卡尔点位
   */
  position: Cesium.Cartesian3;
}

/**
 * 采集路径点位储存器
 */
export class PlottingSampledProperty extends Cesium.ConstantProperty {
  constructor(controller: PlottingController, value?: SampledPointSymbol[]) {
    super(value ?? []);
    this._controller = controller;
    controller.coordinates.definitionChanged.addEventListener(() => {
      if (this.getLength() === 0) {
        this.addPosition(controller.coordinates.getCenter()!, 0, 0);
      }
      else {
        this.setByIndex(0, controller.coordinates.getCenter()!);
      }
    });
  }

  /**
   * 采样点默认配速，添加采样点时，会根据这个默认的速度算出当前采样点所经过的时间 km/h
   */
  static defaultSpeed = 40;

  declare getValue: (
    duration?: Cesium.JulianDate,
    result?: SampledPointSymbol[]
  ) => SampledPointSymbol[];

  declare setValue: (value: SampledPointSymbol[]) => void;

  declare equals: (other?: Cesium.Property) => boolean;

  /**
   * @internal
   */
  private _controller: PlottingController;

  cloneValue() {
    return [...this.getValue()];
  }

  getLength() {
    return this.getValue().length;
  }

  containsId(id: string): SampledPointSymbol | undefined {
    return this.getValue().find(e => e.id === id);
  }

  getById(id: string): Cesium.Cartesian3 | undefined {
    const item = this.getValue().find(e => e.id === id);
    return item?.position;
  }

  getPositions(): Cesium.Cartesian3[] {
    return this.getValue().map(e => e.position);
  }

  getByIndex(index: number): Cesium.Cartesian3 | undefined {
    return this.getValue()[index]?.position;
  }

  /**
   * 根据默认的配速计算默认耗时
   * @param position
   * @param index
   */
  computeDuration(position: Cesium.Cartesian3, index: number): number {
    const data = this.getValue();
    const reference
      = index === 0 ? this._controller.coordinates.getCenter()! : data[index - 1].position;
    const distance = Cesium.Cartesian3.distance(reference, position);

    return distance / ((PlottingSampledProperty.defaultSpeed * 1000) / 3600);
  }

  setByIndex(index: number, position?: Cesium.Cartesian3, duration?: number): boolean {
    const value = this.cloneValue();
    const item = value[index];
    if (item) {
      duration ??= item.duration;
      position ??= item.position;
      const id = item.id;
      value.splice(index, 1, { id, duration, position });
      this.setValue(value);
      return true;
    }
    return false;
  }

  addPosition(position: Cesium.Cartesian3, index?: number, duration?: number): SampledPointSymbol {
    const value = this.cloneValue();
    index ??= value.length;
    duration ??= this.computeDuration(position, index);
    const item = { id: Cesium.createGuid(), duration, position };
    value.splice(+index!, 0, item);
    this.setValue(value);
    return item;
  }

  setById(id: string, position: Cesium.Cartesian3, duration?: number): boolean {
    const index = this.getValue().findIndex(e => e.id === id);
    return this.setByIndex(index, position, duration);
  }

  removeById(id: string): boolean {
    const value = this.cloneValue();
    const index = value.findIndex(e => e.id === id);
    if (index != -1) {
      return this.removeIndex(index);
    }
    return false;
  }

  removeIndex(index: number): boolean {
    const value = this.cloneValue();
    if (index <= value.length) {
      value.splice(index, 1);
      this.setValue(value);
      return true;
    }
    return false;
  }

  computeSampledPositionProperty(startTime: Cesium.JulianDate): Cesium.SampledPositionProperty {
    startTime = startTime.clone();
    const property = new Cesium.SampledPositionProperty();
    const origin = this._controller.coordinates.getCenter()!;
    const data = this.getValue();
    let durationCount = 0;
    property.addSample(startTime, origin);
    data.forEach((item) => {
      const duration = item.duration;
      durationCount += duration;
      const time = Cesium.JulianDate.addSeconds(startTime, durationCount, new Cesium.JulianDate());
      property.addSample(time, item.position);
    });
    return property;
  }
}
