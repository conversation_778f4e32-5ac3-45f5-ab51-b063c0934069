<!-- NearFarScalarAttribute -->
<script lang="ts" setup>
import type { NearFarScalarSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'NearFarScalarAttribute' });

const props = defineProps<{
  modelValue?: NearFarScalarSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: NearFarScalarSerializateJSON): void;
}>();

const model = ref<NearFarScalarSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div>
    <el-form-item :label="label" />
    <NumberAttribute v-model="model.near" label="下限距离" :max="model.far" :precision="2" />
    <NumberAttribute
      v-model="model.nearValue"
      label="下限比例"
      :min="0"
      :max="model.farValue"
      :precision="2"
    />
    <NumberAttribute v-model="model.far" label="上限距离" :min="model.nearValue" :precision="2" />
    <NumberAttribute
      v-model="model.farValue"
      label="上限比例"
      :min="model.nearValue"
      :precision="2"
    />
  </div>
</template>
