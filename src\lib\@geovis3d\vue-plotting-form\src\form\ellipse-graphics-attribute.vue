<!-- EllipseGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { EllipseGraphicsKey, EllipseGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { EllipseGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import BooleanAttribute from './boolean-attribute.vue';
import ClassificationTypeAttribute from './classification-type-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'EllipseGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: EllipseGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.EllipseGraphics, EllipseGraphicsSerializateJSON>({
  graphic: () => props.entity?.ellipse,
  omit: props.omit,
  toJSON: (graphics, omit) => EllipseGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => EllipseGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="ellipse"
    graphics-field="show"
    label="可见"
  />
  <NumberAttribute
    v-if="!hide?.includes('semiMajorAxis')"
    v-model="model.semiMajorAxis"
    graphics="ellipse"
    graphics-field="semiMajorAxis"
    label="半长轴"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('semiMinorAxis')"
    v-model="model.semiMinorAxis"
    graphics="ellipse"
    graphics-field="semiMinorAxis"
    label="半短轴"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('height')"
    v-model="model.height"
    graphics="ellipse"
    graphics-field="height"
    label="高"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="ellipse"
    graphics-field="heightReference"
    label="高度参照"
  />
  <NumberAttribute
    v-if="!hide?.includes('extrudedHeight')"
    v-model="model.extrudedHeight"
    graphics="ellipse"
    graphics-field="extrudedHeight"
    label="拉伸高度"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('extrudedHeightReference')"
    v-model="model.extrudedHeightReference"
    graphics="ellipse"
    graphics-field="extrudedHeightReference"
    label="拉伸参照"
  />
  <NumberAttribute
    v-if="!hide?.includes('rotation')"
    v-model="model.rotation"
    graphics="ellipse"
    graphics-field="rotation"
    label="旋转"
  />
  <NumberAttribute
    v-if="!hide?.includes('stRotation')"
    v-model="model.stRotation"
    graphics="ellipse"
    graphics-field="stRotation"
    label="纹理旋转"
  />
  <NumberAttribute
    v-if="!hide?.includes('granularity')"
    v-model="model.granularity"
    graphics="ellipse"
    graphics-field="granularity"
    label="渲染粒度"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="ellipse"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="ellipse"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="ellipse"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="ellipse"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="ellipse"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('numberOfVerticalLines')"
    v-model="model.numberOfVerticalLines"
    graphics="ellipse"
    graphics-field="numberOfVerticalLines"
    label="垂直线数"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="ellipse"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="ellipse"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <ClassificationTypeAttribute
    v-if="!hide?.includes('classificationType')"
    v-model="model.classificationType"
    graphics="ellipse"
    graphics-field="classificationType"
    label="贴地类型"
  />
  <NumberAttribute
    v-if="!hide?.includes('zIndex')"
    v-model="model.zIndex"
    graphics="ellipse"
    graphics-field="zIndex"
    label="层级"
    :precision="0"
  />
</template>
