<script lang="ts" setup>
import type { DataSource } from 'cesium';
import {
  fileSettingDeleteIdUsingDelete,
  fileSettingListUsingPost,
  fileSettingUpdateUsingPut,
} from '@/genapi/cimapi';
import { cimRequest } from '@/genapi/request';
import { useCzDataSourceCollection, useCzEntityCollection, useCzViewer } from '@x3d/vue-hooks';
import ExcelDialog from './excel-dialog.vue';
import StyleDialog from './style-dialog.vue';
import UploadView from './upload-view.vue';
import XmlDialog from './xml-dialog.vue';

// 定义文件记录的接口
interface FileRecord {
  id: string;
  fileName: string;
  fileUrl: string;
  [key: string]: any;
}

defineOptions({ name: 'Left' });
const viewer = useCzViewer();
const show = ref(true);
const showMoreMenu = ref(false);
const dialogVisible = ref(false);
const newfileName = ref('');

const { state: records, execute: refreshFileList, isLoading: recordsLoading } = useAsyncState(async () => {
  const { data } = await fileSettingListUsingPost({
    data: {
      size: 30,
      current: 1,
    },
  });
  return (data?.records ?? []) as unknown as FileRecord[];
}, []);

const sheetJson = shallowRef<any>();
const xmlContent = shallowRef<string>();

const previewIds = ref(new Set<any>());

const dataSourceScope = useCzDataSourceCollection();

const modelScope = useCzEntityCollection();

watchArray(() => [...previewIds.value], (value, oldValue, added, removed) => {
  removed.forEach((id) => {
    dataSourceScope.removeWhere(item => Reflect.get(item, 'file-id') === id);
    modelScope.removeWhere(item => Reflect.get(item, 'id') === id);
  });

  added.forEach(async (id) => {
    const data = records.value.find(item => item.id === id);
    if (!data) {
      return;
    }
    const fileType = data.fileName.split('.').pop()!.toLowerCase();
    try {
      if (fileType === 'glb' || fileType === 'gltf') {
        const entity = new Cesium.Entity({
          id: data.id,
          position: Cesium.Cartesian3.fromDegrees(114.5529, 30.5965, 50),
          model: {
            uri: `${import.meta.env.VITE_CIM_URL}${data.fileUrl}`,
            minimumPixelSize: 64,
          },
        });
        modelScope.add(entity);
        viewer.value.flyTo(entity, {
          duration: 1,
        });
      }
      else if (fileType === 'geojson' || fileType === 'json') {
        const geojson = cimRequest({
          url: decodeURIComponent(data.fileUrl),
          method: 'get',
        });
        const dataSource = await Cesium.GeoJsonDataSource.load(geojson, {
          stroke: Cesium.Color.AQUA,
          fill: Cesium.Color.AQUA.withAlpha(0.1),
        });

        Reflect.set(dataSource, 'file-id', data.id);
        await dataSourceScope.add(dataSource);
        viewer.value.flyTo(dataSource, {
          duration: 1,
        });
      }
      else if (fileType === 'kml') {
        const dataSource = await Cesium.KmlDataSource.load(`${import.meta.env.VITE_CIM_URL}${data.fileUrl}`);
        Reflect.set(dataSource, 'file-id', data.id);
        await dataSourceScope.add(dataSource);
        viewer.value.flyTo(dataSource, {
          duration: 1,
        });
      }
    }
    catch (error) {
      console.error(error);
    }
  });
});

async function deleteFile(item: FileRecord) {
  await ElMessageBox.confirm('确定删除该文件吗？');
  await fileSettingDeleteIdUsingDelete({
    path: { id: item.id },
  });
  ElMessage.success('删除成功！');
  refreshFileList();
  previewIds.value.delete(item.id);
}

const fileId = ref('');
function editFile(val: FileRecord) {
  dialogVisible.value = true;
  newfileName.value = val.fileName;
  fileId.value = val.id;
}

async function confrm() {
  const res = await fileSettingUpdateUsingPut({
    data: {
      id: fileId.value,
      fileName: newfileName.value,
    },
  });
  if (res) {
    ElMessage.success('更新成功！');
    dialogVisible.value = false;
    refreshFileList();
  }
  else {
    ElMessage.warning('更新失败');
  }
}

const currentDataSource = shallowRef<DataSource>();

function setCurrentDataSource(item?: FileRecord) {
  currentDataSource.value = [...dataSourceScope.scope].find(e => Reflect.get(e, 'file-id') === item?.id);
}
</script>

<template>
  <layout-left-panel>
    <div p="b-46px t-64px" class="content">
      <div flex="~ col" h="100%" :w="show ? '400px' : '0'" transition="all 300" of="hidden">
        <UploadView
          @sheet-preview="sheetJson = $event"
          @xml-preview="xmlContent = $event"
          @uploaded="refreshFileList()"
        />

        <header-title1 b-b="1px! solid #fff/10%!">
          文件列表
        </header-title1>
        <el-scrollbar v-loading="recordsLoading" style="max-height: 600px">
          <div
            v-for="(item, index) in records"
            :key="item.id"
            class="scrollbar-item"
            flex="~ items-center"
            cursor="pointer"
            b="1px"
            :border="previewIds.has(item.id) ? '#4176ff' : 'transparent'"
            @click="previewIds.has(item.id) ? previewIds.delete(item.id) : previewIds.add(item.id)"
          >
            <el-text un-text="16px! #fff!" flex="shrink-0" m="r-10px!">
              {{ index + 1 }}.
            </el-text>
            <el-text un-text="16px! #fff!" flex="1" truncated>
              {{ item.fileName }}
            </el-text>
            <el-popover
              placement="bottom"
              trigger="hover"
              popper-style="padding:8px"
              :width="$vh(120)"
              :show-arrow="false"
              :teleported="true"
            >
              <template #reference>
                <el-button link circle class="item-menu h-20px!" @click="showMoreMenu = true">
                  <el-icon class="i-material-symbols:more-vert" text="20px! #fff!" />
                </el-button>
              </template>
              <div flex="~ col" class="menu-btn">
                <el-button text m="x-0!" un-hover="font-blod-14px!" @click.stop="editFile(item)">
                  编辑
                </el-button>
                <el-button
                  v-if="['json', 'geojson'].includes(item.fileName.split('.').pop()!.toLowerCase()) && previewIds.has(item.id)"
                  text
                  m="x-0!"
                  un-hover="font-blod-14px!"
                  @click="setCurrentDataSource(item)"
                >
                  样式
                </el-button>
                <el-button text m="x-0!" un-hover="font-blod-14px!" @click.stop="deleteFile(item)">
                  删除
                </el-button>
              </div>
            </el-popover>
          </div>
        </el-scrollbar>
      </div>
      <el-button link class="toggle-btn" @click="show = !show">
        <el-icon
          class="i-material-symbols:chevron-left"
          text="20px! #FFF!"
          transition="all 300"
          :rotate="show ? '0' : '180'"
          inline-block
        />
      </el-button>
    </div>
  </layout-left-panel>
  <el-dialog v-model="dialogVisible" title="编辑" width="20%" draggable>
    <div style="display: flex">
      <el-text style="width: 80px">
        文件名称
      </el-text>
      <el-input v-model="newfileName" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confrm">确定</el-button>
      </span>
    </template>
  </el-dialog>

  <ExcelDialog v-model:list="sheetJson" />
  <XmlDialog v-model:content="xmlContent" />
  <StyleDialog v-model:data-source="currentDataSource" />
</template>

<style lang="scss" scoped>
.content {
  position: relative;
  height: 100vh;
  pointer-events: auto;
  background: var(--el-bg-color);
}

.toggle-btn {
  position: absolute;
  top: 521px;
  left: 100%;
  z-index: 99;
  width: 24px;
  height: 56px;
  background: var(--el-bg-color) !important;
  border-radius: 0 4px 4px 0 !important;
}

.upload-btn {
  margin: 24px 34px;
  background-color: #4176ff;
  border-radius: 6px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  width: 29.3518vh;
  height: 11.8333vh;
  padding: 5px;
}

.scrollbar-item {
  padding: 12px;
  margin: 10px;
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  border-radius: 4px;
}

.scrollbar-item:hover {
  background: rgb(137 169 255 / 20%);
}
</style>
