import * as Cesium from 'cesium';

import { CesiumVectorEventCollection } from '../../event';

/**
 * {@link Cesium.CloudCollection} 构造函数参数
 */
export type CloudCollectionConstructorOptions = ConstructorParameters<
  typeof Cesium.CloudCollection
>[0];

/**
 * {@link Cesium.CloudCollection} 拓展用法与 {@link Cesium.CloudCollection} 基本一致。
 *
 * `GcCloudCollection.event`鼠标事件监听
 */
export class GcCloudCollection extends Cesium.CloudCollection {
  constructor(options?: CloudCollectionConstructorOptions) {
    super(options);

    this._event = new CesiumVectorEventCollection();
  }

  /**
   * @internal
   */
  private _event: CesiumVectorEventCollection;

  /**
   * 矢量元素事件储存器
   */
  get event(): CesiumVectorEventCollection {
    return this._event;
  }
}
