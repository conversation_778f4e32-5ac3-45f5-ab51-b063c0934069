<!-- PlaneAttribute -->
<script lang="ts" setup>
import type { PlaneSerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import Cartesian3Attribute from './cartesian3-attribute.vue';
import { useShallowBinding } from './hooks';
import NumberAttribute from './number-attribute.vue';

defineOptions({ name: 'PlaneAttribute' });

const props = defineProps<{
  modelValue?: PlaneSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: PlaneSerializateJSON): void;
}>();

const model = ref<PlaneSerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <Cartesian3Attribute v-model="model.normal" label="normal" />
  <NumberAttribute v-model="model.distance" label="distance" />
</template>
