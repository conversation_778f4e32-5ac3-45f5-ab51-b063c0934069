import type { PlottingDataSource } from '@/lib/@geovis3d/core';
import type { EntitySerializateJSON } from '@/lib/@geovis3d/plotting';
import type { MaybeRefOrGetter } from 'vue-demi';

import { PlottingEntity } from '@/lib/@geovis3d/core';
import { PlottingEntitySerializate } from '@/lib/@geovis3d/plotting';
import { toValue } from '@vueuse/core';

import { useCzEventComputed } from '@x3d/vue-hooks';
import * as Cesium from 'cesium';
import { computed } from 'vue';

/**
 * PlottingDataSource业务工具Hook
 * @param dataSource
 */
export function usePlottingDataSourceHelper(
  dataSource?: MaybeRefOrGetter<PlottingDataSource | undefined>,
) {
  const data = computed(() => toValue(dataSource));

  /**
   * 当前激活的entity
   */
  const active = useCzEventComputed(
    () => data.value?.activeChanged,
    () => data.value?.active,
  );

  /**
   * 当前标绘entity列表
   */
  const plottings = useCzEventComputed(
    () => data.value?.entities.collectionChanged,
    () => data.value?.getPlottings() ?? [],
  );

  const sampledPlottings = computed(() =>
    plottings.value.filter(e => e.plotting.sampleds.getLength() > 1),
  );

  const setActive = (entity?: PlottingEntity) => {
    if (data.value) {
      data.value.active = entity;
    }
    else {
      throw new Error('datasource is undefined.');
    }
  };

  const add = (entity: PlottingEntity) => data.value?.entities.add(entity);

  const remove = (entity: PlottingEntity) => data.value?.entities.remove(entity);

  const flyTo = (entity: PlottingEntity) => {
    data.value?.scene?.geovis3d.viewer.flyTo(entity, {
      maximumHeight: 100000,
      duration: 1.5,
      offset: new Cesium.HeadingPitchRange(0, -90, 0),
    });
  };

  const loadJSON = (json?: EntitySerializateJSON[]) => {
    Array.isArray(json) && json?.forEach((entityJSON) => {
      const entity = new PlottingEntity(PlottingEntitySerializate.fromJSON(entityJSON));
      entity && add(entity);
    });
  };

  const toJSON = () => {
    return plottings.value?.map(entity => PlottingEntitySerializate.toJSON(entity));
  };

  return {
    dataSource: data,
    active,
    loadJSON,
    toJSON,
    plottings,
    setActive,
    add,
    remove,
    flyTo,
    sampledPlottings,
  };
}
