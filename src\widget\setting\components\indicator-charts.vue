<!-- 请输入组件名称或备注 -->
<script lang="ts" setup>
import type { components } from '@/genapi/cimapi';
import type { EChartsOption } from 'echarts';
import { visitLogGetLogUsingGet } from '@/genapi/cimapi';

defineOptions({ name: 'IndicatorCharts' });
const list = reactive<(components['schemas']['VisitLog'] & { time: string })[]>([]);
async function getData() {
  const { data } = await visitLogGetLogUsingGet({});
  if (data) {
    list.push({
      ...data,

      time: dayjs().format('HH:mm:ss'),
    });
  }
}

useIntervalFn(
  () => getData(),
  3000,
  {
    immediate: true,
  },
);

const option = computed(() => {
  return {
    title: {
      text: '系统日志',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
    },
    grid: {
      left: '5%',
      right: '0%',
      top: '5%',
    },
    legend: {
      orient: 'vertical',
      top: '5%',
      left: 'right',
      textStyle: {
        color: '#fff',
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    textStyle: {
      color: '#fff',
    },
    xAxis: {
      type: 'category',
      data: list.map(item => item.time),

    },
    yAxis: {
      alignTicks: true,
      type: 'value',
      boundaryGap: [0, '100%'],
      splitLine: {
        show: false,
      },
    },

    series: [
      {
        name: '下载次数',
        type: 'line',
        showSymbol: false,
        data: list.map(item => item.downloadCount),
      },
      {
        name: '登录次数',
        type: 'line',
        showSymbol: false,
        data: list.map(item => item.loginCount),
      },
      {
        name: '其他次数',
        type: 'line',
        showSymbol: false,
        data: list.map(item => item.otherCount),
      },
    ],
  } as EChartsOption;
});
</script>

<template>
  <vue-echarts :option="option" class="h-500px! w-100%!" autoresize />
</template>
