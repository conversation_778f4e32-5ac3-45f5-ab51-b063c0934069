import type { MaterialPropertySerializateController } from './material-property';

import * as Cesium from 'cesium';

export interface ImageMaterialPropertySerializateJSON {
  image?: string;
}

export default <
  MaterialPropertySerializateController<
    'ImageMaterialProperty',
    Cesium.ImageMaterialProperty,
    ImageMaterialPropertySerializateJSON
  >
>{
  type: 'ImageMaterialProperty',
  hit: (property) => {
    return property instanceof Cesium.ImageMaterialProperty;
  },
  toJSO<PERSON>(property, time) {
    time ??= Cesium.JulianDate.now();
    const { image } = property?.getValue(time) ?? {};
    return {
      type: 'ImageMaterialProperty',
      params: {
        image,
        transparent: true,
      },
    };
  },
  fromJSON(json) {
    const { image } = json?.params ?? {};
    return new Cesium.ImageMaterialProperty({
      image,
      transparent: true,
    });
  },
};
