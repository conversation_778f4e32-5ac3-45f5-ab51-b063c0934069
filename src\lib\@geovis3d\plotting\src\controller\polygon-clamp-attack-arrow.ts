import type { PlottingControllerOptions } from '@/lib/@geovis3d/core';
import { cartesianToWgs84, wgs84ToCartesian } from '@/lib/@geovis3d/coordinate';
import { clampAttackArrow } from '@/lib/@geovis3d/geometry';
import * as turf from '@turf/turf';

import * as Cesium from 'cesium';

/**
 * polygon-clamp-attack-arrow 标绘配置 钳击箭头
 */
export default <PlottingControllerOptions>{
  type: 'polygon-clamp-attack-arrow',
  forceTerminate: entity => entity.plotting.coordinates.getLength() >= 5,
  location: { visible: true },
  control: { visible: true },
  // altitude: { visible: true },
  update(entity) {
    if (!entity.polygon) {
      entity.polygon = new Cesium.PolygonGraphics({
        material: Cesium.Color.RED.withAlpha(0.8),
      });
    }
    const positions = entity.plotting.coordinates.getPositions();
    const mousePosition = entity.plotting.mousePosition;
    mousePosition && positions.push(mousePosition.clone());
    const coords = positions.map(e => cartesianToWgs84(e)).map(e => [e[0], e[1]]);

    if (positions.length < 3) {
      entity._cache = null;
      return;
    }
    // 只有3个控制点 ,通过角度算出第四个临时点，第五个点位为点1和点2的中心点
    if (positions.length === 3) {
      const bearing = turf.bearing(turf.point(coords[1]), turf.point(coords[2]));
      const distance = turf.distance(turf.point(coords[1]), turf.point(coords[2]));
      const coord4 = turf.destination(turf.point(coords[0]), distance, bearing).geometry.coordinates;
      coords.push(coord4);
      const coord5 = turf.midpoint(turf.point(coords[0]), turf.point(coords[1])).geometry.coordinates;
      coords.push(coord5);
    }
    // 只有4个控制点 ，第五个点位为点1和点2的中心点
    if (positions.length === 4) {
      const coord5 = turf.midpoint(turf.point(coords[0]), turf.point(coords[1])).geometry.coordinates;
      coords.push(coord5);
    }
    const coordinates = clampAttackArrow(coords).geometry.coordinates;
    entity._cache = new Cesium.PolygonHierarchy(
      coordinates.map(e => wgs84ToCartesian([...e, 0])),
    );
    entity.polygon!.hierarchy = new Cesium.CallbackProperty(() => entity._cache, false);
  },
};
