import type { ConfigEnv, ProxyOptions } from 'vite';

import process from 'node:process';
import { HttpsProxyAgent } from 'https-proxy-agent';

import { loadEnv } from 'vite';

export function createProxy(
  config: ConfigEnv,
): Record<string, string | ProxyOptions> {
  const env = loadEnv(config.mode, process.cwd());

  const agent = env.VITE_SERVER_AGENT ? new HttpsProxyAgent(env.VITE_SERVER_AGENT) : undefined;

  return {
    [env.VITE_CIM_URL]: {
      secure: false,
      changeOrigin: true,
      target: env.VITE_SERVER_TARGET,
      agent,
    },

    [env.VITE_STATIC_FILE_URL]: {
      secure: false,
      changeOrigin: true,
      target: env.VITE_SERVER_TARGET,
      agent,
    },

    [env.VITE_AMAP_URL]: {
      secure: false,
      changeOrigin: true,
      target: env.VITE_SERVER_TARGET,
      agent,
    },
    '/xtdqym': {
      secure: false,
      changeOrigin: true,
      target: env.VITE_SERVER_TARGET,
      agent,
    },
    // 海南应急可视化系统
    [env.VITE_VISUAL_SYSTEM_PATH]: {
      target: 'http://10.131.66.31:8083',
      agent: new HttpsProxyAgent('http://100.100.1.7:8888'),
    },
    // 海南应急可视化系统-视频流代理-websocket
    [env.VITE_VISUAL_SYSTEM_LIVE_PATH]: {
      target: 'ws://172.25.110.40:8888/live',
      ws: true,
      agent: new HttpsProxyAgent('http://100.100.1.7:8888'),
      rewrite: path => path.replace(new RegExp(env.VITE_VISUAL_SYSTEM_LIVE_PATH), ''),
    },

    // 海南态势指挥系统
    [env.VITE_SICUATION_SYSTEM_PATH]: {
      target: 'http://10.131.66.31:8083',
      agent: new HttpsProxyAgent('http://100.100.1.7:8888'),
    },

    // 海南态势指挥系统-森火视频流代理-websocket
    [env.VITE_SICUATION_SYSTEM_FOREST_PATH]: {
      target: 'ws://172.25.110.172:8080/forest',
      ws: true,
      agent: new HttpsProxyAgent('http://100.100.1.7:8888'),
      rewrite: path => path.replace(new RegExp(env.VITE_SICUATION_SYSTEM_FOREST_PATH), ''),
    },
  };
}
