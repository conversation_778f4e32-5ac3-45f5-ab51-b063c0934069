<!-- PolygonGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { PolygonGraphicsKey, PolygonGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { PolygonGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import ArcTypeAttribute from './arc-type-attribute.vue';
import BooleanAttribute from './boolean-attribute.vue';
import ClassificationTypeAttribute from './classification-type-attribute.vue';
import ColorAttribute from './color-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import HeightReferenceAttribute from './height-reference-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import PolygonHierarchyAttribute from './polygon-hierarchy-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'PolygonGraphicsGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: PolygonGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.PolygonGraphics, PolygonGraphicsSerializateJSON>({
  graphic: () => props.entity?.polygon,
  omit: props.omit ?? ['hierarchy'],
  toJSON: (graphics, omit) => PolygonGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => PolygonGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="polygon"
    graphics-field="show"
    label="可见"
  />
  <!-- <PolygonHierarchyAttribute v-if="!hide?.includes('hierarchy')"
    v-model="model.hierarchy"
graphics="polygon"
    graphics-field="hierarchy" label="hierarchy" /> -->
  <NumberAttribute
    v-if="!hide?.includes('height')"
    v-model="model.height"
    graphics="polygon"
    graphics-field="height"
    label="高"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('heightReference')"
    v-model="model.heightReference"
    graphics="polygon"
    graphics-field="heightReference"
    label="高度参照"
  />
  <NumberAttribute
    v-if="!hide?.includes('extrudedHeight')"
    v-model="model.extrudedHeight"
    graphics="polygon"
    graphics-field="extrudedHeight"
    label="拉伸高度"
    :precision="2"
  />
  <HeightReferenceAttribute
    v-if="!hide?.includes('extrudedHeightReference')"
    v-model="model.extrudedHeightReference"
    graphics="polygon"
    graphics-field="extrudedHeightReference"
    label="拉伸参照"
  />
  <NumberAttribute
    v-if="!hide?.includes('stRotation')"
    v-model="model.stRotation"
    graphics="polygon"
    graphics-field="stRotation"
    label="纹理旋转"
  />
  <NumberAttribute
    v-if="!hide?.includes('granularity')"
    v-model="model.granularity"
    graphics="polygon"
    graphics-field="granularity"
    label="渲染粒度"
  />
  <BooleanAttribute
    v-if="!hide?.includes('fill')"
    v-model="model.fill"
    graphics="polygon"
    graphics-field="fill"
    label="是否填充"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="polygon"
    graphics-field="material"
    label="材质"
  />
  <BooleanAttribute
    v-if="!hide?.includes('outline')"
    v-model="model.outline"
    graphics="polygon"
    graphics-field="outline"
    label="是否描边"
  />
  <ColorAttribute
    v-if="!hide?.includes('outlineColor')"
    v-model="model.outlineColor"
    graphics="polygon"
    graphics-field="outlineColor"
    label="描边颜色"
  />
  <NumberAttribute
    v-if="!hide?.includes('outlineWidth')"
    v-model="model.outlineWidth"
    graphics="polygon"
    graphics-field="outlineWidth"
    label="描边线宽"
    :precision="2"
  />
  <BooleanAttribute
    v-if="!hide?.includes('perPositionHeight')"
    v-model="model.perPositionHeight"
    graphics="polygon"
    graphics-field="perPositionHeight"
    label="采用预设高度"
  />
  <BooleanAttribute
    v-if="!hide?.includes('closeTop')"
    v-model="model.closeTop"
    graphics="polygon"
    graphics-field="closeTop"
    label="顶部闭合"
  />
  <BooleanAttribute
    v-if="!hide?.includes('closeBottom')"
    v-model="model.closeBottom"
    graphics="polygon"
    graphics-field="closeBottom"
    label="底部闭合"
  />
  <ArcTypeAttribute
    v-if="!hide?.includes('arcType')"
    v-model="model.arcType"
    graphics="polygon"
    graphics-field="arcType"
    label="转角类型"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="polygon"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="polygon"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <ClassificationTypeAttribute
    v-if="!hide?.includes('classificationType')"
    v-model="model.classificationType"
    graphics="polygon"
    graphics-field="classificationType"
    label="贴地类型"
  />
  <NumberAttribute
    v-if="!hide?.includes('zIndex')"
    v-model="model.zIndex"
    graphics="polygon"
    graphics-field="zIndex"
    label="层级"
    :precision="0"
  />
  <PolygonHierarchyAttribute
    v-if="!hide?.includes('textureCoordinates')"
    v-model="model.textureCoordinates"
    graphics="polygon"
    graphics-field="textureCoordinates"
    label="纹理坐标"
  />
</template>
