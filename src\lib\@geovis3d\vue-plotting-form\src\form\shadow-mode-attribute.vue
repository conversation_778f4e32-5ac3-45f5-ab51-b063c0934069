<!-- ShadowModeAttribute -->
<script lang="ts" setup>
import type { ShadowModeSerializateJSON } from '@/lib/@geovis3d/plotting';

import { useVModel } from '@vueuse/core';

defineOptions({ name: 'ShadowModeAttribute' });

const props = defineProps<{
  modelValue?: ShadowModeSerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: ShadowModeSerializateJSON): void;
}>();

const model = useVModel(props, 'modelValue', emit);

const options = [
  {
    label: '禁用',
    value: 'DISABLED',
  },
  {
    label: '启用',
    value: 'ENABLED',
  },
  {
    label: '仅投射阴影',
    value: 'CAST_ONLY',
  },
  {
    label: '仅接收阴影',
    value: 'RECEIVE_ONLY',
  },
];
</script>

<template>
  <el-form-item :label="label">
    <el-select-v2 v-model="model" :options="options" :clearable="false" />
  </el-form-item>
</template>
