<!-- PolylineGraphicsAttribute -->
<script lang="ts" setup>
import type { GcEntity } from '@/lib/@geovis3d/core';

import type { PolylineGraphicsKey, PolylineGraphicsSerializateJSON } from '@/lib/@geovis3d/plotting';
import type * as Cesium from 'cesium';
import { PolylineGraphicsSerializate } from '@/lib/@geovis3d/plotting';
import ArcTypeAttribute from './arc-type-attribute.vue';
import BooleanAttribute from './boolean-attribute.vue';
import ClassificationTypeAttribute from './classification-type-attribute.vue';
import DistanceDisplayConditionAttribute from './distance-display-condition-attribute.vue';
import { useGraphicsBinding } from './hooks';
import MaterialPropertyAttribute from './material-property-attribute.vue';
import NumberAttribute from './number-attribute.vue';
import ShadowModeAttribute from './shadow-mode-attribute.vue';

defineOptions({ name: 'PolylineGraphicsGraphicsAttribute' });

const props = defineProps<{
  entity?: GcEntity;
  label?: string;
  omit?: PolylineGraphicsKey[];
  hide?: string[];
}>();

const model = useGraphicsBinding<Cesium.PolylineGraphics, PolylineGraphicsSerializateJSON>({
  graphic: () => props.entity?.polyline,
  omit: props.omit || ['positions'],
  toJSON: (graphics, omit) => PolylineGraphicsSerializate.toJSON(graphics, omit),
  fromJSON: (json, omit) => PolylineGraphicsSerializate.fromJSON(json, omit),
});
</script>

<template>
  <BooleanAttribute
    v-if="!hide?.includes('show')"
    v-model="model.show"
    graphics="polyline"
    graphics-field="show"
    label="可见"
  />
  <!-- <Cartesian3ArrayAttribute v-if="!hide?.includes('positions')"
    v-model="model.positions"
graphics="polyline"
    graphics-field="positions" label="positions" /> -->
  <NumberAttribute
    v-if="!hide?.includes('width')"
    v-model="model.width"
    graphics="polyline"
    graphics-field="width"
    label="宽"
    :precision="2"
  />
  <NumberAttribute
    v-if="!hide?.includes('granularity')"
    v-model="model.granularity"
    graphics="polyline"
    graphics-field="granularity"
    label="渲染粒度"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('material')"
    v-model="model.material"
    graphics="polyline"
    graphics-field="material"
    label="材质"
  />
  <MaterialPropertyAttribute
    v-if="!hide?.includes('depthFailMaterial')"
    v-model="model.depthFailMaterial"
    graphics="polyline"
    graphics-field="depthFailMaterial"
    label="地下材质"
  />
  <ArcTypeAttribute
    v-if="!hide?.includes('arcType')"
    v-model="model.arcType"
    graphics="polyline"
    graphics-field="arcType"
    label="转角类型"
  />
  <BooleanAttribute
    v-if="!hide?.includes('clampToGround')"
    v-model="model.clampToGround"
    graphics="polyline"
    graphics-field="clampToGround"
    label="是否贴地"
  />
  <ShadowModeAttribute
    v-if="!hide?.includes('shadows')"
    v-model="model.shadows"
    graphics="polyline"
    graphics-field="shadows"
    label="阴影"
  />
  <DistanceDisplayConditionAttribute
    v-if="!hide?.includes('distanceDisplayCondition')"
    v-model="model.distanceDisplayCondition"
    graphics="polyline"
    graphics-field="distanceDisplayCondition"
    label="距离显示条件"
  />
  <ClassificationTypeAttribute
    v-if="!hide?.includes('classificationType')"
    v-model="model.classificationType"
    graphics="polyline"
    graphics-field="classificationType"
    label="贴地类型"
  />
  <NumberAttribute
    v-if="!hide?.includes('zIndex')"
    v-model="model.zIndex"
    graphics="polyline"
    graphics-field="zIndex"
    label="层级"
    :precision="0"
  />
</template>
