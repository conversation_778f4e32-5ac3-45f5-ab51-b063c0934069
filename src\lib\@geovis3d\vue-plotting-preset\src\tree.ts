import type { PlottingEntityConstructorOptions } from '@/lib/@geovis3d/core';
import {
  PolylineArrowLinkMaterialProperty,
  PolylineAttackLinkMaterialProperty,
  PolylinePulseLinkMaterialProperty,
  PolylineTrailLinkMaterialProperty,
} from '@/lib/@geovis3d/material';

import * as Cesium from 'cesium';

export interface PlottingTreeConstructors {
  uuid?: string;
  label: string;
  options: PlottingEntityConstructorOptions;
}

export interface PlottingTreeModel {
  uuid?: string;
  label: string;
  cover?: string;
  isDirectory?: boolean;
  children?: PlottingTreeModel[];
  constructors?: PlottingTreeConstructors[];
}

const treeList = <PlottingTreeModel[]>[
  {
    label: '常用',
    children: [
      {
        label: '点',
        cover: new URL('./assets/banner/点.svg', import.meta.url).href,
        constructors: [
          {
            label: '点',
            options: {
              plotting: { type: 'point' },
            },
          },
        ],
      },
      {
        label: '文字',
        cover: new URL('./assets/banner/文字.svg', import.meta.url).href,
        constructors: [
          {
            label: '文字',
            options: {
              plotting: { type: 'label' },
            },
          },
        ],
      },
      {
        label: '多线段',
        cover: new URL('./assets/banner/多线段.svg', import.meta.url).href,
        constructors: [
          {
            label: '多线段',
            options: {
              plotting: { type: 'polyline' },
            },
          },
        ],
      },
      {
        label: '多边形',
        cover: new URL('./assets/banner/多边形.svg', import.meta.url).href,
        constructors: [
          {
            label: '多边形',
            options: {
              plotting: { type: 'polygon' },
            },
          },
        ],
      },
      {
        label: '矩形',
        cover: new URL('./assets/banner/矩形.svg', import.meta.url).href,
        constructors: [
          {
            label: '矩形',
            options: {
              plotting: { type: 'rectangle' },
            },
          },
        ],
      },
      {
        label: '扇形',
        cover: new URL('./assets/banner/扇形.svg', import.meta.url).href,
        constructors: [
          {
            label: '扇形',
            options: {
              plotting: { type: 'polygon-sector' },
            },
          },
        ],
      },
      {
        label: '扩散波',
        cover: new URL('./assets/banner/扩散波.svg', import.meta.url).href,
        constructors: [
          {
            label: '扩散波',
            options: {
              plotting: { type: 'ellipse-circle-wave' },
            },
          },
        ],
      },
    ],
  },

  {
    label: '基础',
    children: [
      {
        label: '屏幕文字',
        cover: new URL('./assets/banner/文字.svg', import.meta.url).href,
        constructors: [
          {
            label: '屏幕文字',
            options: {
              plotting: { type: 'screen' },
            },
          },
        ],
      },
      {
        label: '平滑多线段',
        cover: new URL('./assets/banner/文字.svg', import.meta.url).href,
        constructors: [
          {
            label: '平滑多线段',
            options: {
              plotting: { type: 'polyline-smooth' },
            },
          },
        ],
      },
      {
        label: '箭头线',
        cover: new URL('./assets/banner/箭头线.svg', import.meta.url).href,
        constructors: [
          {
            label: '箭头线',
            options: {
              plotting: { type: 'polyline' },
              polyline: {
                width: 4,
                material: new PolylineArrowLinkMaterialProperty({
                  color: Cesium.Color.YELLOW,
                }),
              },
            },
          },
        ],
      },
      {
        label: '滋滋线',
        cover: new URL('./assets/banner/滋滋线.svg', import.meta.url).href,
        constructors: [
          {
            label: '滋滋线',
            options: {
              plotting: { type: 'polyline' },
              polyline: {
                width: 4,
                material: new PolylinePulseLinkMaterialProperty({
                  color: Cesium.Color.AQUA,
                }),
              },
            },
          },
        ],
      },
      {
        label: '攻击特效线',
        cover: new URL('./assets/banner/攻击特效线.svg', import.meta.url).href,
        constructors: [
          {
            label: '攻击特效线',
            options: {
              plotting: { type: 'polyline' },
              polyline: {
                width: 4,
                material: new PolylineAttackLinkMaterialProperty({
                  color: Cesium.Color.RED,
                }),
              },
            },
          },
        ],
      },
      {
        label: '追踪线',
        cover: new URL('./assets/banner/线.svg', import.meta.url).href,
        constructors: [
          {
            label: '追踪线',
            options: {
              plotting: { type: 'polyline' },
              polyline: {
                width: 4,
                material: new PolylineTrailLinkMaterialProperty({
                  color: Cesium.Color.PURPLE,
                }),
              },
            },
          },
        ],
      },
      {
        label: '平滑闭合面',
        cover: new URL('./assets/banner/平滑闭合面.svg', import.meta.url).href,
        constructors: [
          {
            label: '平滑闭合面',
            options: {
              plotting: { type: 'polygon-smooth' },
            },
          },
        ],
      },
      {
        label: '弓形',
        cover: new URL('./assets/banner/弓形.svg', import.meta.url).href,
        constructors: [
          {
            label: '弓形',
            options: {
              plotting: { type: 'polygon-lune' },
            },
          },
        ],
      },
      {
        label: '扇形',
        cover: new URL('./assets/banner/扇形.svg', import.meta.url).href,
        constructors: [
          {
            label: '扇形',
            options: {
              plotting: { type: 'polygon-sector' },
            },
          },
        ],
      },
      {
        label: '矩形',
        cover: new URL('./assets/banner/矩形.svg', import.meta.url).href,
        constructors: [
          {
            label: '矩形',
            options: {
              plotting: { type: 'rectangle' },
            },
          },
        ],
      },
      {
        label: '锥体',
        cover: new URL('./assets/banner/锥体.svg', import.meta.url).href,
        constructors: [
          {
            label: '锥体',
            options: {
              plotting: { type: 'cylinder' },
            },
          },
        ],
      },
      {
        label: '钳击箭头',
        cover: new URL('./assets/banner/钳击箭头.svg', import.meta.url).href,
        constructors: [
          {
            label: '钳击箭头',
            options: {
              plotting: { type: 'polygon-clamp-attack-arrow' },
            },
          },
        ],
      },
      {
        label: '攻击箭头',
        cover: new URL('./assets/banner/攻击箭头.svg', import.meta.url).href,
        constructors: [
          {
            label: '攻击箭头',
            options: {
              plotting: { type: 'polygon-attack-arrow' },
            },
          },
        ],
      },
      {
        label: '燕尾攻击箭头',
        cover: new URL('./assets/banner/燕尾攻击箭头.svg', import.meta.url).href,
        constructors: [
          {
            label: '燕尾攻击箭头',
            options: {
              plotting: { type: 'polygon-tail-attack-arrow' },
            },
          },
        ],
      },
      {
        label: '尖直箭头',
        cover: new URL('./assets/banner/尖直箭头.svg', import.meta.url).href,
        constructors: [
          {
            label: '尖直箭头',
            options: {
              plotting: { type: 'polygon-sharp-straight-arrow' },
            },
          },
        ],
      },
      {
        label: '燕尾直箭头',
        cover: new URL('./assets/banner/燕尾直箭头.svg', import.meta.url).href,
        constructors: [
          {
            label: '燕尾直箭头',
            options: {
              plotting: { type: 'polygon-tail-straight-arrow' },
            },
          },
        ],
      },
      {
        label: '集结地',
        cover: new URL('./assets/banner/集结地.svg', import.meta.url).href,
        constructors: [
          {
            label: '集结地',
            options: {
              plotting: {
                type: 'polygon-staging-ground',
              },
            },
          },
        ],
      },
    ],
  },

  {
    label: '粒子特效',
    children: [
      {
        label: '爆炸',
        cover: new URL('./assets/fire.png', import.meta.url).href,
        constructors: [
          {
            label: '爆炸',
            options: {
              plotting: { type: 'particle' },
              particle: {
                image: new URL('./assets/fire.png', import.meta.url).href,
                show: false,
                startColor: Cesium.Color.ORANGE.withAlpha(0.7),
                endColor: Cesium.Color.BLACK.withAlpha(0.3),
                startScale: 5,
                endScale: 10,
                minimumParticleLife: 3,
                maximumParticleLife: 6,
                minimumSpeed: 1,
                maximumSpeed: 4,
                imageSize: new Cesium.Cartesian2(250, 250),
                emissionRate: 20,
                lifetime: 16,
                loop: true,
                sizeInMeters: true,
                emitter: new Cesium.CircleEmitter(5),
              },
            },
          },
        ],
      },
      {
        label: '火焰',
        cover: new URL('./assets/fire.png', import.meta.url).href,
        constructors: [
          {
            label: '火焰',
            options: {
              plotting: { type: 'particle' },
              particle: {
                image: new URL('./assets/fire.png', import.meta.url).href,
                show: false,
                startColor: Cesium.Color.YELLOW,
                endColor: Cesium.Color.RED,
                startScale: 3,
                endScale: 1.5,
                minimumParticleLife: 1.5,
                maximumParticleLife: 1.8,
                minimumSpeed: 7,
                maximumSpeed: 9,
                imageSize: new Cesium.Cartesian2(25, 25),
                emissionRate: 40,
                lifetime: 10,
                loop: true,
                sizeInMeters: true,
                emitter: new Cesium.ConeEmitter(Cesium.Math.toRadians(25)),
              },
            },
          },
        ],
      },
      {
        label: '水枪',
        cover: new URL('./assets/water.png', import.meta.url).href,
        constructors: [
          {
            label: '水枪',
            options: {
              plotting: { type: 'particle-water-gun' },
              particle: {
                image: new URL('./assets/water.png', import.meta.url).href,
                show: false,
                startColor: new Cesium.Color(1, 1, 1, 0.6),
                endColor: new Cesium.Color(0.8, 0.86, 1, 0.4),
                startScale: 2,
                endScale: 4,
                minimumParticleLife: 1.8,
                maximumParticleLife: 1.8,
                minimumSpeed: 5,
                maximumSpeed: 5,
                imageSize: new Cesium.Cartesian2(2500, 2500),
                emissionRate: 250,
                lifetime: 20,
                loop: true,
                sizeInMeters: true,
                emitter: new Cesium.ConeEmitter(Cesium.Math.toRadians(10)),
              },
            },
          },
        ],
      },
      {
        label: '烟雾',
        cover: new URL('./assets/smoke.png', import.meta.url).href,
        constructors: [
          {
            label: '烟雾',
            options: {
              plotting: { type: 'particle' },
              particle: {
                image: new URL('./assets/smoke.png', import.meta.url).href,
                startColor: Cesium.Color.WHITE.withAlpha(0.7),
                endColor: Cesium.Color.WHITE.withAlpha(0.3),
                startScale: 0,
                endScale: 10,
                minimumParticleLife: 1,
                maximumParticleLife: 6,
                minimumSpeed: 1,
                maximumSpeed: 4,
                imageSize: new Cesium.Cartesian2(25, 25),
                emissionRate: 5,
                lifetime: 16,
                sizeInMeters: true,
                emitter: new Cesium.CircleEmitter(2),
              },
            },
          },
        ],
      },
    ],
  },
];
function createGuid(tree: PlottingTreeModel[]) {
  tree.forEach((e) => {
    e.uuid = Cesium.createGuid();
    e.constructors?.forEach((e) => {
      e.uuid = Cesium.createGuid();
    });
    e.children?.length && createGuid(e.children);
  });
}
createGuid(treeList);

export default treeList;
