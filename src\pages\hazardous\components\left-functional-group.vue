<!-- 左侧功能按钮组 -->
<script lang="ts" setup>
import CompanyDetailDialog from './company-detail-dialog.vue';
import CompanyList from './company-list.vue';
import CompanyTites from './company-tites.vue';
import SafePromiseListModal from './safe-promise-list-modal.vue';
import SpotCheckModal from './spot-check-modal.vue';
import VideoMonitorModal from './video-monitor-modal.vue';

defineOptions({ name: 'LeftFunctionalGroup' });

const buttons = [
  {
    name: '风险研判',
    component: () => h(CompanyList),
  },
  {
    name: '安全承诺',
    component: () => h(SafePromiseListModal),
  },
  {
    name: '监控视频',
    component: () => h(VideoMonitorModal),
  },
  {
    name: '巡查抽查',
    component: () => h(SpotCheckModal),
  },

];
const activeIndex = ref(0);
function handle(index: number) {
  activeIndex.value = index;
}
</script>

<template>
  <div class="left-functional-group">
    <div
      v-for="(item, index) in buttons"
      :key="item.name"
      class="functional"
      :class="{ active: activeIndex === index }"
      @click="handle(index)"
    >
      {{ item.name }}
    </div>
  </div>
  <component :is="buttons[activeIndex]?.component" />

  <!-- 详情弹窗 -->
  <CompanyDetailDialog />
  <!-- 2d/3d -->
  <CompanyTites />
</template>

<style scoped lang="scss">
  .left-functional-group {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 21;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .functional {
    padding: 12px 10px;
    margin-top: 25px;
    font-size: 18px;
    pointer-events: initial;
    cursor: pointer;
    background: rgb(9 49 81 / 60%);
    border: 1px solid rgb(9 49 81);
    border-radius: 4px;
    box-shadow: inset 0 0 10px 4px rgb(20 76 171 / 80%);
    writing-mode: vertical-lr;

    &.active {
      background: rgb(35 154 212 / 60%);
      box-shadow: inset 0 0 10px 4px rgb(84 216 255 / 50%);
    }
  }
}
</style>
