import type { Cartesian3SerializateJSON } from './cartesian3';

import * as Cesium from 'cesium';
import { Cartesian3Serializate } from './cartesian3';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface PolygonHierarchySerializateJSON {
  positions?: Cartesian3SerializateJSON[];
  holes?: PolygonHierarchySerializateJSON[];
}

export type PolygonHierarchyKey = keyof PolygonHierarchySerializateJSON;

export class PolygonHierarchySerializate {
  private constructor() {}
  static toJSON(
    data?: Cesium.PolygonHierarchy,
    time?: Cesium.JulianDate,
  ): PolygonHierarchySerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      positions: getValue('positions')?.map((item: Cesium.Cartesian3) =>
        Cartesian3Serializate.toJSON(item),
      ),
      holes: getValue('holes')?.map((item: Cesium.PolygonHierarchy) =>
        PolygonHierarchySerializate.toJSON(item),
      ),
    };
  }

  static fromJSON(json?: PolygonHierarchySerializateJSON): Cesium.PolygonHierarchy | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json);

    return new Cesium.PolygonHierarchy(
      getValue('positions')?.map(
        (item: Cartesian3SerializateJSON) => Cartesian3Serializate.fromJSON(item)!,
      ) ?? [],
      getValue('holes')?.map(item => PolygonHierarchySerializate.fromJSON(item)!),
    );
  }
}
