import type { ArgsFunction } from '../types';

export type EffectHelperEffect = (onCleanup: ArgsFunction<[VoidFunction]>) => void;

/**
 * 副作用函数工具
 *
 * 类似vue中的 watchEffect
 *
 * @param execute 立即执行函数 只能执行一次
 * @param destroy 销毁函数
 *
 *
 * ```typescript
 *  const [execute, destroy] = effectHelper((onCleanup) => {
 *   const stop = entity.definitionChanged.addEventListener(() => {
 *    // ...
 *   });
 *    onCleanup(() => stop());
 *  });
 *  execute();
 *
 *  setTimeout(() => {
 *    destroy();
 *  }, 1000);
 *
 * ```
 */
export function effectHelper(
  effect: EffectHelperEffect,
): [execute: VoidFunction, destroy: VoidFunction] {
  let cleanup: VoidFunction;
  const onCleanup = (fn: VoidFunction) => (cleanup = fn);
  const destroy = () => cleanup?.();
  const execute = () => {
    destroy();
    effect(onCleanup);
  };
  return [execute, destroy];
}
