<!-- WFS编辑框 -->
<script lang="ts" setup>
import type Feature from 'ol/Feature';
import { json } from '@codemirror/lang-json';
import { oneDark } from '@codemirror/theme-one-dark';
import { useCzDataSource, useCzViewer } from '@x3d/vue-hooks';
import { ElMessage } from 'element-plus';
import GeoJSON from 'ol/format/GeoJSON';
import { Codemirror } from 'vue-codemirror';
import { useWfsState } from '../state/wfs-state';
import { flipGeojson } from '../utils/flip-geojson';

defineOptions({ name: 'WfsEditDialog' });

const { currentPropertyKeys, currentFeature, editting, currentWFSService } = useWfsState();

const featureId = ref<any>();

const geojson = ref('');

const properties = ref<Record<string, any>>({});

function formatJSON() {
  geojson.value = JSON.stringify(JSON.parse(geojson.value), null, 2);
}

watch(currentFeature, () => {
  geojson.value = currentFeature.value ? JSON.stringify(currentFeature.value || {}) : '{}';
  formatJSON();
});

watch(geojson, (geojson) => {
  let json: any = {};
  try {
    json = JSON.parse(geojson);
  }
  catch (error) {
    console.error(error);
  }
  properties.value = JSON.parse(JSON.stringify(json.properties || {}));
});

watchDeep(properties, () => {
  let json: any = {};
  try {
    json = JSON.parse(geojson.value);
  }
  catch (error) {
    console.error(error);
  }
  json.properties = properties.value || {};

  geojson.value = JSON.stringify(json);
  formatJSON();
});

useCzDataSource(() => {
  if (editting.value) {
    return Cesium.GeoJsonDataSource.load(JSON.parse(geojson.value), {
      fill: Cesium.Color.AQUA.withAlpha(0.5),
      stroke: Cesium.Color.AQUA,
    });
  }
});

watchEffect(() => {
  if (editting.value) {
    featureId.value = currentFeature.value?.id;
  }
});

const viewer = useCzViewer();

const { execute, isLoading } = useAsyncState(async () => {
  try {
    const geojsonFormat = new GeoJSON();
    // 吐血了，这里的坐标是【经，纬】，但geoserver接收坐标是【纬，经】，颠倒一下
    const json = flipGeojson(JSON.parse(geojson.value));
    const feature = geojsonFormat.readFeature(json) as Feature;
    const geomKey = (await currentWFSService.value?.getGeomKey())?.name ?? 'the_geom';
    feature.setGeometryName(geomKey);
    const id = feature.getId();

    // 常规更新模式
    await currentWFSService.value?.transaction({
      updates: id ? [feature] : [],
      inserts: id ? [] : [feature],
      options: {
        nativeElements: [],
      },
    });

    // TODO 具体清除哪个缓存？
    for (let index = 0; index < viewer.value.imageryLayers.length; index++) {
      // @ts-expect-error 此处是Cesium的私有属性
      viewer.value.imageryLayers.get(index)._imageryCache = {};
    }
  }
  catch (error) {
    console.error(error);
    ElMessage.error('操作失败，请检查服务器连接和权限');
  }
}, undefined, {
  immediate: false,
});

async function _insertFeatureWithXML() {
  try {
    // 从模拟数据中提取属性和几何
    const mockData = {
      properties: {
        OBJECTID: Math.floor(Math.random() * 100000),
        Id: 123,
        Floor: 3,
      },
      geometry: {
        type: 'MultiPolygon',
        coordinates: [[[
          [114.36001748, 30.59371615], // 注意：XML中是 [经度, 纬度]
          [114.3600282, 30.59374383],
          [114.36003891, 30.59376228],
          [114.36004963, 30.59378996],
          [114.36007106, 30.59380839],
          [114.3600925, 30.59381759],
          [114.36012465, 30.59382678],
          [114.3601568, 30.59383597],
          [114.36021039, 30.5938359],
          [114.36024254, 30.59382662],
          [114.36026398, 30.59380813],
          [114.36028541, 30.59378963],
          [114.36030685, 30.59377114],
          [114.36031757, 30.59374343],
          [114.36032829, 30.59371572],
          [114.36032829, 30.59368802],
          [114.36032829, 30.59366956],
          [114.36031758, 30.59364188],
          [114.36029615, 30.59362344],
          [114.36027471, 30.59359578],
          [114.36025328, 30.59358657],
          [114.36023185, 30.59357737],
          [114.36019969, 30.59356818],
          [114.36016754, 30.59356823],
          [114.36013539, 30.5935775],
          [114.36011395, 30.59358676],
          [114.3600818, 30.59359604],
          [114.36006036, 30.59361453],
          [114.36003892, 30.59364226],
          [114.36002821, 30.59366074],
          [114.36001748, 30.59368845],
          [114.36001748, 30.59371615], // 闭合点
        ]]],
      },
    };

    // 构造坐标字符串
    const coordinates = mockData.geometry.coordinates[0][0];
    const coordinateString = coordinates.map(coord => `${coord[0]} ${coord[1]}`).join(' ');

    // 构造完整的 WFS Transaction XML
    const transactionXML = `
    <?xml version="1.0" encoding="UTF-8"?>
      <wfs:Transaction
        xmlns:wfs="http://www.opengis.net/wfs"
        xmlns:geovis="geovis"
        xmlns:gml="http://www.opengis.net/gml"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        service="WFS"
        version="1.1.0"
        xsi:schemaLocation="http://www.opengis.net/wfs http://schemas.opengis.net/wfs/1.1.0/wfs.xsd">
        <wfs:Insert>
          <geovis:武汉-tiles xmlns:geovis="geovis">
            <geovis:the_geom>
              <gml:MultiPolygon xmlns:gml="http://www.opengis.net/gml">
                <gml:polygonMember>
                  <gml:Polygon>
                    <gml:exterior>
                      <gml:LinearRing>
                        <gml:posList srsDimension="2">${coordinateString}</gml:posList>
                      </gml:LinearRing>
                    </gml:exterior>
                  </gml:Polygon>
                </gml:polygonMember>
              </gml:MultiPolygon>
            </geovis:the_geom>
            <geovis:OBJECTID>${mockData.properties.OBJECTID}</geovis:OBJECTID>
            <geovis:Id>${mockData.properties.Id}</geovis:Id>
            <geovis:Floor>${mockData.properties.Floor}</geovis:Floor>
          </geovis:武汉-tiles>
        </wfs:Insert>
      </wfs:Transaction>`;

    console.log('📝 发送的XML:', transactionXML);

    // 使用 axios 发送请求
    const response = await axios.request({
      url: 'https://cyyj.geovisearth.com/geoserver/geovis/ows',
      method: 'post',
      headers: {
        'Content-Type': 'text/xml; charset=UTF-8',
      },
      data: transactionXML,
    });

    console.log('✅ 服务器响应:', response.data);

    // 解析响应检查是否成功
    if (typeof response.data === 'string' && response.data.includes('SUCCESS')) {
      ElMessage.success('🎉 要素插入成功！');

      // 清除图层缓存
      for (let index = 0; index < viewer.value.imageryLayers.length; index++) {
        // @ts-expect-error 此处是Cesium的私有属性
        viewer.value.imageryLayers.get(index)._imageryCache = {};
      }
    }
    else {
      ElMessage.warning('⚠️ 插入完成，请检查结果');
    }
  }
  catch (error: any) {
    console.error('❌ 插入失败:', error);

    if (error.response) {
      console.log('错误响应:', error.response.data);
      ElMessage.error(`插入失败：${error.response.status} - ${error.response.statusText}`);
    }
    else {
      ElMessage.error('❌ 网络错误，请检查连接');
    }
  }
}

const tab = ref<'code' | 'form'>('form');
</script>

<template>
  <drag-card
    v-if="editting"
    title="属性编辑"
    :initial-right="$vh(40)"
    :initial-top="$vh(100)"
    content-class="flex flex-col overflow-hidden h-70vh!"
    @close="editting = false"
  >
    <el-tabs v-model="tab" p="x-20px" flex="shrink-0">
      <el-tab-pane label="表单视图" name="form" />
      <el-tab-pane label="代码视图" name="code" />
    </el-tabs>

    <!--  -->
    <el-scrollbar v-if="tab === 'form'" flex="1" of="hidden" p="20px">
      <el-form :label-width="$vh(100)">
        <el-form-item
          v-for="item in currentPropertyKeys?.filter(item => !item.type?.startsWith('gml:'))"
          :key="item.name"
          :label="item.name"
        >
          <div class="w-full flex items-center gap-2">
            <el-input v-model="properties[item.name]" class="flex-1" />
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <!-- 代码视图 -->
    <div v-if="tab === 'code'" flex="1" of="hidden" position="relative">
      <Codemirror
        v-model="geojson"
        placeholder=""
        :style="{ height: '100%' }"
        :autofocus="true"
        :indent-with-tab="true"
        :tab-size="2"
        :extensions="[json(), oneDark]"
      />
      <el-button position="absolute" right="20px" bottom="20px" @click="formatJSON()">
        <el-icon class="i-tabler:code text-18px!" />
      </el-button>
    </div>
    <el-button m="20px" type="primary" :loading="isLoading" @click="execute()">
      保存
    </el-button>
  </drag-card>
</template>
