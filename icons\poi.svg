<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="18px" viewBox="0 0 16 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>形状</title>
    <defs>
        <path d="M20,11 C15.5813901,11 12,14.2233947 12,18.1998553 C12,24.4994833 20,29 20,29 C20,29 28,24.6125413 28,18.1998553 C28,14.2233947 24.418156,11 20,11 Z M20,21.2856995 C17.9205674,21.2856995 16.235234,19.7505282 16.235234,17.8570641 C16.235234,15.9633933 17.9205674,14.4284287 20,14.4284287 C22.0790922,14.4284287 23.7646525,15.9633933 23.7646525,17.8570641 C23.7646525,19.7504249 22.0790922,21.2856995 20,21.2856995 Z" id="path-1"></path>
        <filter x="-37.5%" y="-33.3%" width="175.0%" height="166.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="4" operator="erode" in="SourceAlpha" result="shadowSpreadInner1"></feMorphology>
            <feGaussianBlur stdDeviation="4" in="shadowSpreadInner1" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.327412093   0 0 0 0 0.513936128   0 0 0 0 0.667544158  0 0 0 0.1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="应急一张图" transform="translate(-1262.000000, -295.000000)">
            <g id="编组-25" transform="translate(1230.000000, 186.000000)">
                <g id="形状" transform="translate(20.000000, 98.000000)">
                    <use fill="#89D2FA" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                </g>
            </g>
        </g>
    </g>
</svg>