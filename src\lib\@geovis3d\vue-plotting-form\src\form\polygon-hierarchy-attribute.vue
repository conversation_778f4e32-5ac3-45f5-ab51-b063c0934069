<!-- PolygonHierarchyAttribute -->
<script lang="ts" setup>
import type { PolygonHierarchySerializateJSON } from '@/lib/@geovis3d/plotting';

import { ref } from 'vue';
import Cartesian3ArrayAttribute from './cartesian3-array-attribute.vue';
import { useShallowBinding } from './hooks';

defineOptions({ name: 'PolygonHierarchyAttribute' });

const props = defineProps<{
  modelValue?: PolygonHierarchySerializateJSON;
  label?: string;
}>();

const emit = defineEmits<{
  (event: 'update:modelValue', value?: PolygonHierarchySerializateJSON): void;
}>();

const model = ref<PolygonHierarchySerializateJSON>({});

useShallowBinding({
  left: () => props.modelValue,
  right: model,
  ltr: value => (model.value = { ...value }),
  rtl: value => emit('update:modelValue', { ...value }),
});
</script>

<template>
  <div>
    <el-form-item :label="label" />
    <Cartesian3ArrayAttribute v-model="model.positions" label="当前选择" />
    <template v-for="(_item, index) in model.holes?.length" :key="index">
      <PolygonHierarchyAttribute
        v-if="model.holes?.[index]"
        v-model="model.holes[index]"
        :label="`洞${index + 1}`"
      />
    </template>
  </div>
</template>
