import { effectHelper } from '@/lib/@geovis3d/shared';
import * as Cesium from 'cesium';

import { PlottingEntity } from './plotting-entity';

export interface PlottingDataSourceConstructorOptions {
  disabled?: boolean;
}

/**
 * 标绘专用的DataSource,与CustomDataSource几乎无异
 *
 * 提供了部分实用函数
 *
 * @property {getPlottings} 获取当前datasource中的标绘entity列表
 *
 * @property {active} 当前datasource中激活的entity
 * @property {activeChanged} 监听active的变化
 */
export class PlottingDataSource extends Cesium.CustomDataSource {
  constructor(name?: string, options: PlottingDataSourceConstructorOptions = {}) {
    super(name);
    this.disabled = !!options.disabled;

    const [execute, destroy] = effectHelper((onCleanup) => {
      const plotting = this.scene!.geovis3d.plotting;
      const stop = plotting.activeChanged.addEventListener((value, prev) => {
        const thisValue = value && this.entities.contains(value) ? value : undefined;
        const thisPrev = prev && this.entities.contains(prev) ? prev : undefined;
        if (thisValue || thisPrev) {
          this.activeChanged.raiseEvent(thisValue, thisPrev);
        }
      });
      onCleanup(() => stop());
    });

    this.changedEvent.addEventListener((_, field, value) => {
      if (field === 'isMounted') {
        value ? execute() : destroy();
      }
    });
  }

  disabled: boolean;

  get scene(): Cesium.Scene | undefined {
    const clustering = this.clustering as any;
    return clustering._scene;
  }

  /**
   * 获取所有标绘的entity
   */
  getPlottings(): PlottingEntity[] {
    return this.entities.values.filter(
      item => item instanceof PlottingEntity,
    ) as PlottingEntity[];
  }

  /**
   * 此datasource中处于激活态的Entity
   */
  get active(): PlottingEntity | undefined {
    const active = this.scene?.geovis3d.plotting.active;
    if (active) {
      this.entities.contains(active);
      return active;
    }
    return undefined;
  }

  /**
   * 此datasource中处于激活态的Entity
   */
  set active(entity: PlottingEntity | undefined) {
    if (this.active !== entity) {
      this.scene!.geovis3d.plotting.active = entity;
    }
  }

  /**
   * @internal
   */
  private _activeChanged = new Cesium.Event<
    (value?: PlottingEntity, prev?: PlottingEntity) => void
  >();

  /**
   * 当此DataSource中的PlottingEntity的激活态发生变化时触发
   */
  get activeChanged(): Cesium.Event<(value?: PlottingEntity, prev?: PlottingEntity) => void> {
    return this._activeChanged;
  }
}
