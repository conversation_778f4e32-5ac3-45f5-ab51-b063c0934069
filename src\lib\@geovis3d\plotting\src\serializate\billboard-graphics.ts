import type { BoundingRectangleSerializateJSON } from './bounding-rectangle';

import type { Cartesian2SerializateJSON } from './cartesian2';
import type { Cartesian3SerializateJSON } from './cartesian3';
import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type {
  HeightReferenceSerializateJSON,
  HorizontalOriginSerializateJSON,
  VerticalOriginSerializateJSON,
} from './enum';
import type { NearFarScalarSerializateJSON } from './near-far-scalar';
import type { PinBuilderSerializateJSON } from './pin-builder';
import * as Cesium from 'cesium';
import { BoundingRectangleSerializate } from './bounding-rectangle';

import { Cartesian2Serializate } from './cartesian2';
import { Cartesian3Serializate } from './cartesian3';
import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { NearFarScalarSerializate } from './near-far-scalar';
import { PinBuilderSerializate } from './pin-builder';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface BillboardGraphicsSerializateJSON {
  show?: boolean;
  image?: string | PinBuilderSerializateJSON;
  scale?: number;
  pixelOffset?: Cartesian2SerializateJSON;
  eyeOffset?: Cartesian3SerializateJSON;
  horizontalOrigin?: HorizontalOriginSerializateJSON;
  verticalOrigin?: VerticalOriginSerializateJSON;
  heightReference?: HeightReferenceSerializateJSON;
  color?: ColorSerializateJSON;
  rotation?: number;
  alignedAxis?: Cartesian3SerializateJSON;
  sizeInMeters?: boolean;
  width?: number;
  height?: number;
  scaleByDistance?: NearFarScalarSerializateJSON;
  translucencyByDistance?: NearFarScalarSerializateJSON;
  pixelOffsetScaleByDistance?: NearFarScalarSerializateJSON;
  imageSubRegion?: BoundingRectangleSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
  disableDepthTestDistance?: number;
}

export type BillboardGraphicsKey = keyof BillboardGraphicsSerializateJSON;

export class BillboardGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.BillboardGraphics,
    omit?: BillboardGraphicsKey[],
    time?: Cesium.JulianDate,
  ): BillboardGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);
    const getImage = () => {
      const image = getValue('image');
      if (image?.pinBuilder) {
        return image?.pinBuilder;
      }
      else {
        return image;
      }
    };

    return {
      show: getValue('show') ?? true,
      image: getImage(),
      scale: getValue('scale'),
      pixelOffset: Cartesian2Serializate.toJSON(getValue('pixelOffset')),
      eyeOffset: Cartesian3Serializate.toJSON(getValue('eyeOffset')),
      horizontalOrigin:
        EnumSerializate.toJSON(Cesium.HorizontalOrigin, getValue('horizontalOrigin')) ?? 'CENTER',
      verticalOrigin:
        EnumSerializate.toJSON(Cesium.VerticalOrigin, getValue('verticalOrigin')) ?? 'BOTTOM',
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      color: ColorSerializate.toJSON(getValue('color')),
      rotation: getValue('rotation'),
      alignedAxis: Cartesian3Serializate.toJSON(getValue('alignedAxis')),
      sizeInMeters: getValue('sizeInMeters'),
      width: getValue('width'),
      height: getValue('height'),
      scaleByDistance: NearFarScalarSerializate.toJSON(getValue('scaleByDistance')),
      translucencyByDistance: NearFarScalarSerializate.toJSON(getValue('translucencyByDistance')),
      pixelOffsetScaleByDistance: NearFarScalarSerializate.toJSON(
        getValue('pixelOffsetScaleByDistance'),
      ),
      imageSubRegion: BoundingRectangleSerializate.toJSON(getValue('imageSubRegion')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
      disableDepthTestDistance: getValue('disableDepthTestDistance'),
    };
  }

  static fromJSON(
    json?: BillboardGraphicsSerializateJSON,
    omit?: BillboardGraphicsKey[],
  ): Cesium.BillboardGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);
    const getImage = () => {
      const image = getValue('image');
      if (Array.isArray(image)) {
        return PinBuilderSerializate.fromJSON(image);
      }
      else {
        return image;
      }
    };
    return new Cesium.BillboardGraphics({
      show: getValue('show') ?? true,
      image: getImage(),
      scale: getValue('scale'),
      pixelOffset: Cartesian2Serializate.fromJSON(getValue('pixelOffset')),
      eyeOffset: Cartesian3Serializate.fromJSON(getValue('eyeOffset')),
      horizontalOrigin: EnumSerializate.fromJSON(
        Cesium.HorizontalOrigin,
        getValue('horizontalOrigin'),
      ),
      verticalOrigin: EnumSerializate.fromJSON(Cesium.VerticalOrigin, getValue('verticalOrigin')),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      color: ColorSerializate.fromJSON(getValue('color')),
      rotation: getValue('rotation'),
      alignedAxis: Cartesian3Serializate.fromJSON(getValue('alignedAxis')),
      sizeInMeters: getValue('sizeInMeters'),
      width: getValue('width'),
      height: getValue('height'),
      scaleByDistance: NearFarScalarSerializate.fromJSON(getValue('scaleByDistance')),
      translucencyByDistance: NearFarScalarSerializate.fromJSON(getValue('translucencyByDistance')),
      pixelOffsetScaleByDistance: NearFarScalarSerializate.fromJSON(
        getValue('pixelOffsetScaleByDistance'),
      ),
      imageSubRegion: BoundingRectangleSerializate.fromJSON(getValue('imageSubRegion')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
      disableDepthTestDistance: getValue('disableDepthTestDistance'),
    });
  }
}
