import { cimRequest } from './request';

export function apiLogin(data: { username: string; password: string }) {
  return cimRequest({
    url: '/login',
    method: 'post',
    data,
  });
}

export function apiLogout() {
  return cimRequest({
    url: '/logout',
  });
}
export function apiSsoLogin(params: { userName: string }) {
  return cimRequest({
    url: '/sso/in',
    method: 'get',
    params,
  });
}
