/// <reference types="vite/client" />
/// <reference types="element-plus/global" />
/// <reference types="unplugin-vue-router/client" />

/// <reference types="./auto-imports" />
/// <reference types="./components" />
/// <reference types="./typed-router" />
/// <reference types="olcs/lib/types/olcs" />

import type { AttributifyAttributes } from '@unocss/preset-attributify';

declare module '@vue/runtime-dom' {
  interface HTMLAttributes extends AttributifyAttributes {}
}

declare global {
  export type GlobalComponents = import('vue').GlobalComponents;
  export * as Cesium from 'cesium';
  export { default as dayjs } from 'dayjs';
}

// 路由额外配置
declare module 'vue-router' {

  export interface ExtraMata {
    /**
     * 排序
     */
    sort?: number;
    /**
     * 系统标题
     */
    title?: string;
    /**
     * 配置拥有特定权限才可访问
     */
    roles?: string[];
  }

  interface RouteMeta extends ExtraMata {

  }
}

export {};
