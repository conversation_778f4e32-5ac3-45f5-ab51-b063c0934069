import * as Cesium from 'cesium';

import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface RectangleSerializateJSON {
  west?: number;
  south?: number;
  east?: number;
  north?: number;
}

export type RectangleKey = keyof RectangleSerializateJSON;

export class RectangleSerializate {
  private constructor() {}
  static toJSON(
    data?: Cesium.Rectangle,
    time?: Cesium.JulianDate,
  ): RectangleSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now());

    return {
      west: Cesium.Math.toDegrees(getValue('west')),
      south: Cesium.Math.toDegrees(getValue('south')),
      east: Cesium.Math.toDegrees(getValue('east')),
      north: Cesium.Math.toDegrees(getValue('north')),
    };
  }

  static fromJSON(json?: RectangleSerializateJSON): Cesium.Rectangle | undefined {
    if (!json) {
      return undefined;
    }
    if (json.east && json.north && json.south && json.west) {
      const getValue = getSerializateJsonValue(json);
      return Cesium.Rectangle.fromDegrees(
        getValue('west'),
        getValue('south'),
        getValue('east'),
        getValue('north'),
      );
    }
  }
}
