import type { PlottingTreeModel } from './tree';

import type { PlotAssetModel } from './types';
import * as Cesium from 'cesium';

export function resolveSource(data: PlotAssetModel[], assetURL: string): PlottingTreeModel[] {
  const format = (models: PlotAssetModel[]) => {
    const ret: PlottingTreeModel[] = [];

    for (const item of models) {
      const label = item?.fileName?.replace(/\.(.+)/g, '') ?? '';
      // 剔除模型封面
      if (!/(\.\w+\.png$)|(\.bin$)/.test(item?.fileName ?? '')) {
        const isDirectory = !!item.isDirectory;
        const isModel = isDirectory ? undefined : /\.((glb)|(gltf))/.test(item?.fileName ?? '');
        const url = assetURL + item.dir;
        ret.push({
          uuid: item.id,
          label,
          isDirectory,
          children: isDirectory ? format(item.children ?? []) : undefined,
          cover: isDirectory ? undefined : assetURL + item.dir + (isModel ? '.png' : ''),
          constructors: isDirectory
            ? undefined
            : isModel
              ? [
                  {
                    label: '模型',
                    options: {
                      plotting: {
                        type: 'model',
                      },
                      model: {
                        uri: url,
                        minimumPixelSize: 100,
                      },
                    },
                  },
                ]
              : [
                  {
                    label: '立标',
                    options: {
                      plotting: {
                        type: 'billboard',
                      },
                      billboard: {
                        image: url,
                        width: 50,
                        height: 50,
                        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                      },
                    },
                  },
                  {
                    label: '平面',
                    options: {
                      plotting: {
                        type: 'rectangle',
                      },
                      rectangle: {
                        material: new Cesium.ImageMaterialProperty({
                          image: url,
                        }),
                      },
                    },
                  },
                ],
        });
      }
    }

    return ret;
  };
  return format(data);
}
