<!-- 森林火情-弹窗面板功能 -->
<script lang="ts" setup>
import type { components } from '@/genapi/analysis';
import {
  analysisForestGetFireDataByTimeUsingGet,
  analysisForestGetWeatherDataUsingGet,

} from '@/genapi/analysis';

import { toPublicPath } from '@/utils/resolve-path';
import { everyMonthlineOptions } from './assets/options';

export interface PrewarningFirePopperProps {
  modelValue?: components['schemas']['ForestFireGroupByStationVoDuiXiang'];
}

export interface PrewarningFirePopperEmits {
  (event: 'update:modelValue', data?: components['schemas']['ForestFireGroupByStationVoDuiXiang']): void;
}

defineOptions({ name: 'PrewarningFirePopper' });
const props = defineProps<PrewarningFirePopperProps>();
// const props = defineProps<{
//   modelValue: components['schemas']['ForestFireGroupByStationVoDuiXiang'];
//   modelValue: {
//     id?: string;
//     stationId: number;
//     longitude: string;
//     latitude: string;
//     deviceNo: string;
//     stationName: string;
//     devices: Record<string, string>[];
//   } & Record<string, any>;
//   startTime?: string;
//   endTime?: string;
//   shortcuts?: Record<string, any>[];
// }>();
const emit = defineEmits<PrewarningFirePopperEmits>();

const VITE_SICUATION_SYSTEM_PATH = import.meta.env.VITE_SICUATION_SYSTEM_PATH;
const VITE_SICUATION_SYSTEM_FOREST_PATH = import.meta.env.VITE_SICUATION_SYSTEM_FOREST_PATH;

const selectedWarning = ref();
const showPanel = ref<boolean>(true);
const activeCollapse = ref(['视频', '气象数据', '预警信息']);
const curView = ref<'camera' | 'weather' | 'warning'>('warning');
const dateRange = ref<[string, string]>([props.startTime || '', props.endTime || '']);
const selectedCamera = ref();
const weatherDataTypeList = [
  { label: '温度', key: 'TEMP', unit: '℃' },
  { label: '湿度', key: 'HUMIDITY', unit: '%' },
  { label: '雨量', key: 'RAINFALL', unit: 'mm' },
  { label: '大气压', key: 'ATMOS', unit: 'MPa' },
  { label: '风向', key: 'WIND_DIRECTION', unit: '' },
  { label: '风速', key: 'WIND_SPEED', unit: 'm/s' },
];
const weatherDataType = ref(weatherDataTypeList[0].key);
const weathercData: any = computedAsync(() => {
  if (!props.modelValue) {
    return;
  }
  return analysisForestGetWeatherDataUsingGet({
    params: {
      deviceNo: props.modelValue.deviceNo,
      weatherDataType: weatherDataType.value,
    },
  }).then((res: any) => {
    return {
      weather24HourData: res?.data?.weather24HourData || [],
      weatherDescData: res?.data?.weatherData || {},
    };
  });
},

);

const weatherDataOptions = computed(
  () =>
    weathercData?.value?.weather24HourData
    && everyMonthlineOptions(
      weathercData?.value?.weather24HourData,
      weatherDataTypeList.find(el => el.key === weatherDataType.value),
    ),
);
const wraningData = computedAsync(async () => {
  const [startDateTime, endDateTime] = dateRange.value;
  const { data } = await analysisForestGetFireDataByTimeUsingGet({
    params: {
      startDateTime,
      endDateTime,
      stationId: props.modelValue.id,
    },
  });
  if (data.listData?.length) {
    curView.value = 'warning';
    selectedWarning.value = data?.listData?.[0];
  }

  else {
    // curView.value = 'camera';
    selectedWarning.value = undefined;
    handleCamera(props?.modelValue?.devices?.[0]);
  }
  return data.listData;
}, []);
function handleCamera(data) {
  curView.value = 'camera';

  selectedCamera.value = data;
}

const shortcuts = ref();

watch([() => props.startTime, () => props.endTime], () => {
  dateRange.value = [props.startTime || '', props.endTime || ''];
});

const position = computed(() => {
  const { longitude, latitude } = props.modelValue ?? {};
  if (!latitude || !longitude) {
    return;
  }
  return Cesium.Cartesian3.fromDegrees(+longitude, +latitude);
});
</script>

<template>
  <located-popper1
    v-if="modelValue && position"
    v-model:show="showPanel"
    :position="position"
    :header="modelValue.stationName"
    show-close
    @close="emit('update:modelValue', undefined)"
  >
    <div class="forest-fire-panel">
      <el-collapse v-model="activeCollapse" w="200px!" flex="shrink-0">
        <el-collapse-item title="视频" name="视频">
          <ul class="list">
            <li
              v-for="item in modelValue.devices"
              :key="item?.url"
              :class="
                curView === 'camera' && selectedCamera?.url === item.url ? 'active' : ''
              "
              @click="handleCamera(item)"
            >
              {{ item.name }}
              <span v-if="item.status === 'on'" class="online">在线</span>
              <span v-else class="unline">离线</span>
            </li>
          </ul>
        </el-collapse-item>
        <el-collapse-item title="气象数据" name="气象数据">
          <ul class="list">
            <li
              :class="curView === 'weather' ? 'active' : ''"
              @click="curView = 'weather'"
            >
              气象数据
            </li>
          </ul>
        </el-collapse-item>
        <el-collapse-item title="预警信息" name="预警信息" @click="curView = 'warning'">
          <el-scrollbar h="260px!">
            <ul class="list">
              <li
                v-for="item in wraningData"
                :key="item.id"
                :class="
                  curView === 'warning' && selectedWarning?.id === item.id ? 'active' : ''
                "
                @click="(curView = 'warning'), (selectedWarning = item)"
              >
                {{ item.createTime }}
              </li>
            </ul>
          </el-scrollbar>
        </el-collapse-item>
      </el-collapse>
      <div v-if="curView === 'camera'" class="camera-container" flex="1">
        <iframe
          :src="toPublicPath(`/video/flv.html?url=${VITE_SICUATION_SYSTEM_FOREST_PATH}${selectedCamera?.url}`)"
          frameborder="0"
        />
      </div>
      <div v-else-if="curView === 'weather'" class="weather-container" flex="1">
        <el-descriptions
          v-if="weathercData?.weatherDescData"
          direction="vertical"
          :column="7"
          border
        >
          <el-descriptions-item label="设备编号">
            {{ weathercData?.weatherDescData?.deviceNo }}
          </el-descriptions-item>
          <el-descriptions-item
            v-for="item in weatherDataTypeList"
            :key="item.key"
            :label="item.label"
          >
            {{
              weathercData?.weatherDescData[
                item.key
                  .toLocaleLowerCase()
                  .replace(/_./g, (k) => k.substr(1).toLocaleUpperCase())
              ]
            }}{{ item.unit }}
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="weathercData?.weather24HourData?.length">
          <div class="title-container">
            <span>近24小时趋势图</span>
            <el-select v-model="weatherDataType" :teleported="false">
              <el-option
                v-for="item in weatherDataTypeList"
                :key="item.key"
                :value="item.key"
                :label="item.label"
              />
            </el-select>
          </div>
          <vue-echarts :option="weatherDataOptions" autoresize w="720px" h="360px" />
        </div>
      </div>
      <div v-else-if="curView === 'warning'" class="warning-container">
        <div w="668px">
          <datetime-picker
            v-model="dateRange"
            label="时间"
            type="datetimerange"
            :shortcuts="shortcuts"
          />
        </div>
        <el-carousel v-if="selectedWarning" height="450px" :autoplay="false">
          <el-carousel-item>
            <el-image
              lazy
              h="100%"
              b="1px solid #415CA6"
              :src=" VITE_SICUATION_SYSTEM_PATH + selectedWarning?.kjgFilePath"
              fit="fill"
              hide-on-click-modal
              preview-teleported
              :preview-src-list="[
                VITE_SICUATION_SYSTEM_PATH + selectedWarning?.kjgFilePath,
                VITE_SICUATION_SYSTEM_PATH + selectedWarning?.rcxFilePath,
              ]"
            />
          </el-carousel-item>
          <el-carousel-item>
            <el-image
              lazy
              h="100%"
              b="1px solid #415CA6"
              :src="VITE_SICUATION_SYSTEM_PATH + selectedWarning?.rcxFilePath"
              fit="fill"
              hide-on-click-modal
              preview-teleported
              :preview-src-list="[
                VITE_SICUATION_SYSTEM_PATH + selectedWarning?.rcxFilePath,
                VITE_SICUATION_SYSTEM_PATH + selectedWarning?.kjgFilePath,
              ]"
            />
          </el-carousel-item>
          <el-carousel-item>
            <video
              :src=" VITE_SICUATION_SYSTEM_PATH + selectedWarning?.videoFilePath"
              :poster=" VITE_SICUATION_SYSTEM_PATH + selectedWarning?.kjgFilePath"
              h="100%"
              w="100%"
              style="object-fit: fill"
              controls
              autoplay
              muted
            />
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </located-popper1>
</template>

<style scoped lang="scss">
.el-carousel {
  :deep() .el-carousel__arrow {
    background-image: url('./assets/arrow.png');

    .el-icon {
      display: none;
    }
  }

  :deep() .el-carousel__arrow--right {
    margin-top: -12px;
    transform: scaleX(-100%);
  }
}

.forest-fire-panel {
  --el-font-size-base: 14px;

  display: flex;
  justify-content: space-between;

  :deep(.el-collapse) {
    --el-collapse-border-color: transparent;
    --el-collapse-header-text-color: #b8e5ff;
    --el-collapse-content-text-color: #e2f4ff;
    --el-collapse-header-font-size: 14px;
    --el-collapse-content-font-size: 14px;

    .el-collapse-item {
      .el-collapse-item__header {
        .el-icon {
          display: none;
        }

        &::before {
          display: inline-block;
          width: 12px;
          height: 12px;
          margin: 10px;
          content: ' ';
          background: radial-gradient(#17cafe 0%, #1f74de 100%);
          border-radius: 50%;
          box-shadow: 0 0 4px 0 rgb(97 197 249 / 50%);
        }
      }

      .el-collapse-item__content {
        padding-bottom: 0;

        .list {
          li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px 0 52px;
            line-height: 50px;
            cursor: pointer;

            &:hover,
            &.active {
              background: rgb(6 57 132 / 50%);
            }

            span {
              display: flex;
              align-items: center;

              &.online::before {
                width: 8px;
                height: 8px;
                margin: 10px;
                line-height: 50px;
                content: ' ';
                background: #08ee7c;
                border-radius: 50%;
                box-shadow: 0 0 4px 0 rgb(97 197 249 / 50%);
              }

              &.unline::before {
                display: inline-block;
                width: 8px;
                height: 8px;
                margin: 10px;
                content: ' ';
                background: #e70000;
                border-radius: 50%;
                box-shadow: 0 0 4px 0 rgb(97 197 249 / 50%);
              }
            }
          }
        }
      }
    }
  }

  .camera-container {
    display: flex;
    flex: 1;
    align-items: stretch;
    padding: 15px 25px;
    overflow: hidden;
    background: rgb(6 57 132 / 50%);

    iframe {
      width: 100%;
    }
  }

  .weather-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    padding: 15px 25px;
    background: rgb(6 57 132 / 50%);

    :deep(.el-descriptions) {
      --el-text-color-primary: #fff;

      width: 100%;

      .el-descriptions__cell {
        padding-bottom: 0;
      }

      .el-descriptions__label {
        font-size: 14px;
        line-height: 26px;
        text-align: center;
        background: #12469b;
      }

      .el-descriptions__content {
        font-size: 14px;
        line-height: 26px;
        text-align: center;
      }
    }

    .title-container {
      display: flex;
      justify-content: space-between;
      height: 50px;
      padding-left: 20px;
      margin-top: 30px;
      font-size: 16px;
      font-weight: 500;
      color: #e2f4ff;

      :deep(.el-select) {
        width: 120px;
        text-align: center;

        .el-select-dropdown__item {
          padding: 0;
          line-height: 2;
        }
      }
    }
  }

  .warning-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: calc(100% - 30px);
    padding: 15px 25px;
    background: rgb(6 57 132 / 50%);

    .detail {
      display: flex;
      flex-direction: column;
      padding: 12px 0;

      .title-container {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .name {
          font-size: 14px;
          font-weight: 500;
          color: #ff3f10;

          i {
            padding-right: 10px;
          }
        }

        .divider {
          flex: 1;
          margin: 0 10px;
          border-bottom: 1px dashed #377eb1;
        }

        .time {
          font-size: 14px;
          font-weight: 500;
          color: #cdd2d7;
        }
      }

      .desc-container {
        font-size: 14px;
        font-weight: 400;
        color: #cdd2d7;
      }

      :deep(.el-descriptions) {
        .el-descriptions__cell {
          font-size: 14px;
          font-weight: 500;
        }

        .el-descriptions__label {
          display: inline-block;
          width: 154px;
          margin: 0;
          color: #c8eaff;
          text-align: right;
        }

        .el-descriptions__content {
          color: #e2f4ff;
          text-align: center;
        }
      }
    }

    :deep(.el-form) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 80px;

      --el-form-label-font-size: 14px;

      .el-form-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        margin-bottom: 0;

        .el-input {
          .el-input__wrapper {
            width: 297px;
            height: 44px;
            background: #0a2156;
            border: 1px solid #2049a8;
            border-radius: 2px;
            box-shadow: none;
          }
        }
      }
    }

    :deep(.el-divider) {
      --el-border-color: rgb(144 187 218 / 60%);
      --el-text-color-primary: #a1bcc6;

      .el-divider__text {
        font-size: 14px;
        font-weight: 600;
        color: #a1bcc6;
      }
    }

    :deep(.el-scrollbar) {
      flex: 1;
      height: 100%;
    }
  }
}
</style>

<style>
.el-date-range-picker {
  --el-disabled-text-color: rgb(0 0 0 / 20%);
}
</style>
