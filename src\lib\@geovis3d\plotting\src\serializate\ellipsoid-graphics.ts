import type { Cartesian3SerializateJSON } from './cartesian3';

import type { ColorSerializateJSON } from './color';
import type { DistanceDisplayConditionSerializateJSON } from './distance-display-condition';
import type { HeightReferenceSerializateJSON, ShadowModeSerializateJSON } from './enum';
import type { MaterialPropertySerializateJSON } from './material/material-property';
import * as Cesium from 'cesium';
import { Cartesian3Serializate } from './cartesian3';

import { ColorSerializate } from './color';
import { DistanceDisplayConditionSerializate } from './distance-display-condition';
import { EnumSerializate } from './enum';
import { MaterialPropertySerializate } from './material/material-property';
import { getMaybePropertyValue, getSerializateJsonValue } from './utils/serializate-value';

export interface EllipsoidGraphicsSerializateJSON {
  show?: boolean;
  radii?: Cartesian3SerializateJSON;
  innerRadii?: Cartesian3SerializateJSON;
  minimumClock?: number;
  maximumClock?: number;
  minimumCone?: number;
  maximumCone?: number;
  heightReference?: HeightReferenceSerializateJSON;
  fill?: boolean;
  material?: MaterialPropertySerializateJSON;
  outline?: boolean;
  outlineColor?: ColorSerializateJSON;
  outlineWidth?: number;
  stackPartitions?: number;
  slicePartitions?: number;
  subdivisions?: number;
  shadows?: ShadowModeSerializateJSON;
  distanceDisplayCondition?: DistanceDisplayConditionSerializateJSON;
}

export type EllipsoidGraphicsKey = keyof EllipsoidGraphicsSerializateJSON;

export class EllipsoidGraphicsSerializate {
  private constructor() {}

  static toJSON(
    data?: Cesium.EllipsoidGraphics,
    omit?: EllipsoidGraphicsKey[],
    time?: Cesium.JulianDate,
  ): EllipsoidGraphicsSerializateJSON | undefined {
    if (!data) {
      return undefined;
    }
    const getValue = getMaybePropertyValue(data, time ?? Cesium.JulianDate.now(), omit);

    return {
      show: getValue('show') ?? true,
      radii: Cartesian3Serializate.toJSON(getValue('radii')),
      innerRadii: Cartesian3Serializate.toJSON(getValue('innerRadii')),
      minimumClock: getValue('minimumClock'),
      maximumClock: getValue('maximumClock'),
      minimumCone: getValue('minimumCone'),
      maximumCone: getValue('maximumCone'),
      heightReference:
        EnumSerializate.toJSON(Cesium.HeightReference, getValue('heightReference')) ?? 'NONE',
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.toJSON(data.material),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.toJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      stackPartitions: getValue('stackPartitions'),
      slicePartitions: getValue('slicePartitions'),
      subdivisions: getValue('subdivisions'),
      shadows: EnumSerializate.toJSON(Cesium.ShadowMode, getValue('shadows')) ?? 'DISABLED',
      distanceDisplayCondition: DistanceDisplayConditionSerializate.toJSON(
        getValue('distanceDisplayCondition'),
      ),
    };
  }

  static fromJSON(
    json?: EllipsoidGraphicsSerializateJSON,
    omit?: EllipsoidGraphicsKey[],
  ): Cesium.EllipsoidGraphics | undefined {
    if (!json) {
      return undefined;
    }
    const getValue = getSerializateJsonValue(json, omit);

    return new Cesium.EllipsoidGraphics({
      show: getValue('show') ?? true,
      radii: Cartesian3Serializate.fromJSON(getValue('radii')),
      innerRadii: Cartesian3Serializate.fromJSON(getValue('innerRadii')),
      minimumClock: getValue('minimumClock'),
      maximumClock: getValue('maximumClock'),
      minimumCone: getValue('minimumCone'),
      maximumCone: getValue('maximumCone'),
      heightReference: EnumSerializate.fromJSON(
        Cesium.HeightReference,
        getValue('heightReference'),
      ),
      fill: getValue('fill') ?? true,
      material: MaterialPropertySerializate.fromJSON(getValue('material')),
      outline: getValue('outline') ?? false,
      outlineColor: ColorSerializate.fromJSON(getValue('outlineColor')),
      outlineWidth: getValue('outlineWidth') ?? 0,
      stackPartitions: getValue('stackPartitions'),
      slicePartitions: getValue('slicePartitions'),
      subdivisions: getValue('subdivisions'),
      shadows: EnumSerializate.fromJSON(Cesium.ShadowMode, getValue('shadows')),
      distanceDisplayCondition: DistanceDisplayConditionSerializate.fromJSON(
        getValue('distanceDisplayCondition'),
      ),
    });
  }
}
