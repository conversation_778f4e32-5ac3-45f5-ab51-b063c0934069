import type { Dayjs } from 'dayjs';
import type { App } from 'vue';
import dayjs, { isDayjs } from 'dayjs';

import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import 'dayjs/locale/zh-cn';

dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.locale('zh-cn');

/**
 * 将传入的参数转换为 Dayjs 对象
 *
 * @param value 可选参数，待转换的值，支持 Dayjs 对象、Date 对象、时间戳和字符串
 * @returns 转换后的 Dayjs 对象
 */
export function toDayjs(value?: any): Dayjs {
  if (!value) {
    return dayjs();
  }
  let date: Dayjs;
  if (isDayjs(value)) {
    date = value;
  }
  else if (value instanceof Date) {
    date = dayjs(value);
  }
  else if (+value) {
    const timestamp = Number.parseInt(value);
    date = dayjs(timestamp);
  }
  else {
    date = dayjs(value);
  }
  return date;
}

/**
 * 创建一个将日期转换为Day.js对象的插件,创建后,vue实例上会挂上一个$any2darjs
 *
 * @returns 返回一个包含安装函数的对象
 */
export function createToDayjsPlugin() {
  return {
    install(app: App) {
      Object.defineProperty(app.config.globalProperties, '$toDayjs', {
        enumerable: true,
        get: () => toDayjs,
      });
    },
  };
}

declare module '@vue/runtime-core' {
  export interface ComponentCustomProperties {
    $toDayjs: typeof toDayjs;
  }
}
