import { ref } from 'vue';

export interface LayerConfig {
  label: string;
  name: string;
  type: string;
  layerUrl: string;
  tileMatrixSetID?: string;
}

// 创建响应式状态
export const activeLayerType = ref<string | null>(null);
export const isTimeAxisPlaying = ref(false);
export const currentDateIndex = ref(0);

// 激活指定类型的图层
export function activateLayerType(type: string) {
  activeLayerType.value = type;
  isTimeAxisPlaying.value = true;
  currentDateIndex.value = 0;
}

// 停止播放
export function stopTimeAxis() {
  isTimeAxisPlaying.value = false;
}

// 重置状态
export function resetLayerState() {
  activeLayerType.value = null;
  isTimeAxisPlaying.value = false;
  currentDateIndex.value = 0;
}

// 切换到上一个影像
export function previousImage(maxIndex: number) {
  if (currentDateIndex.value > 0) {
    currentDateIndex.value--;
  }
  else {
    // 循环到最后一个
    currentDateIndex.value = maxIndex;
  }
  // 如果正在播放，停止播放
  if (isTimeAxisPlaying.value) {
    isTimeAxisPlaying.value = false;
  }
}

// 切换到下一个影像
export function nextImage(maxIndex: number) {
  if (currentDateIndex.value < maxIndex) {
    currentDateIndex.value++;
  }
  else {
    // 循环到第一个
    currentDateIndex.value = 0;
  }
  // 如果正在播放，停止播放
  if (isTimeAxisPlaying.value) {
    isTimeAxisPlaying.value = false;
  }
}
