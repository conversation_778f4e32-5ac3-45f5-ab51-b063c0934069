<script lang="ts" setup>
defineOptions({ name: 'LayoutLeftPanel' });
</script>

<template>
  <div class="layout-left-panel">
    <div class="absolute left-0 top-88px translate-x--100%" flex="~ col">
      <slot name="function" />
    </div>
    <div flex="~ 1 col" of="y-hidden">
      <slot />
    </div>
  </div>
</template>

<style lang="scss">
.layout-left-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: hidden;
  pointer-events: none;
  background: var(--el-bg-color);
}
</style>
