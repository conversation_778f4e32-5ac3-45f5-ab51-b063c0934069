import type { VNode } from 'vue';

import { h, useAttrs, useSlots } from 'vue';

export type Basic = number | string | boolean | symbol | bigint | null | undefined;

export type BasicOrVNode = Basic | VNode | (() => Basic | VNode);

export interface ResolveVnodeProps extends Record<string, any> {
  render: BasicOrVNode;
}

export function ResolveVnode({ render }: ResolveVnodeProps): VNode | undefined {
  const slots = useSlots();
  const attrs = useAttrs();
  if (typeof render === 'function') {
    return h(render, attrs, slots);
  }
  else {
    return h(() => render, attrs, slots);
  }
}
