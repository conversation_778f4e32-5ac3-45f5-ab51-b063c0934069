import type { AxiosRequestConfig } from 'axios';
import axios from 'axios';
// import { fireGetTokenUsingGet } from '@/genapi/cimapi';  //报错 先关掉

export const FIRE_FACTOR_BASE_URL = '/xtdqym/slfh';

// 更新token
// http://10.131.66.31:8083/3d-data-system/server/fire/updateToken?token=Bearer%20AT-58-l9XTfNH3B8zY5xAAa8jqf8KDm7Gv27m-
const token = '';

const instance = axios.create({
  baseURL: FIRE_FACTOR_BASE_URL,

});

/**
 * 火点因子请求器
 * @param config
 */
export async function request<D>(config: AxiosRequestConfig<D>) {
  if (!token) {
    // const { data } = await fireGetTokenUsingGet({});
    // token = data?.token ?? '';
  }
  const { data } = await instance.request({
    ...config,
    headers: {
      ...config.headers,
      Authorization: token || '',
      RefreshToken: `RT-15-XpMuBUJf0nT-Qi6HcaMxEr-Fx2wYDJ2U`,
    },
  });
  return data;
}

export function levelevaluationControllerGetPage(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/levelevaluation/levelevaluationController/getPage',
    method: 'post',
    data,
  });
}

export function levelevaluationControllerGetTj(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/levelevaluation/levelevaluationController/getTj',
    method: 'post',
    data,
  });
}

export function oneMapCtrGetStationList(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/onemap/oneMapCtr/getStationList',
    method: 'post',
    data,
  });
}

export function oneMapCtrGetAlarmStationSort(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/onemap/oneMapCtr/getAlarmStationSort',
    method: 'post',
    data,
  });
}

export function oneMapCtrGetRightLayer(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/onemap/oneMapCtr/getRightLayer',
    method: 'post',
    data,
  });
}

export function oneMapCtrLoadLayersDetail(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/onemap/oneMapCtr/loadLayersDetail',
    method: 'post',
    data,
  });
}

export function commonControllerGetAreaBoundary(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/common/commonController/getAreaBoundary',
    method: 'post',
    data,
  });
}

export function senseAlarmCtrGetAlarmLevelCnt(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/sensealarm/senseAlarmCtr/getAlarmLevelCnt',
    method: 'get',
    params: data,
  });
}

export function senseAlarmCtrGetAlarmList(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/sensealarm/senseAlarmCtr/getAlarmList',
    method: 'get',
    params: data,
  });
}

export function oneMapCtrInitInfo(data: any) {
  return request({
    url: '/kong/kong-gateway/ffmw-ms/bigScreen/onemap/oneMapCtr/initInfo',
    method: 'post',
    data,
  });
}
